using System;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Diagnostics;
using VolvoFlashWR.Core.ErrorHandling;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Utilities;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Service for monitoring flash operations
    /// </summary>
    public class FlashOperationMonitorService : IFlashOperationMonitorService
    {
        private readonly ILoggingService _logger;
        private FlashOperationMonitor _monitor;
        private FlashOperationErrorHandler _errorHandler;

        /// <summary>
        /// Gets the flash operation monitor
        /// </summary>
        public FlashOperationMonitor Monitor => _monitor;

        /// <summary>
        /// Gets the flash operation error handler
        /// </summary>
        public FlashOperationErrorHandler ErrorHandler => _errorHandler;

        /// <summary>
        /// Initializes a new instance of the FlashOperationMonitorService class
        /// </summary>
        /// <param name="logger">The logger to use</param>
        public FlashOperationMonitorService(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _monitor = new FlashOperationMonitor(_logger);
        }

        /// <summary>
        /// Initializes the service
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing FlashOperationMonitorService", "FlashOperationMonitorService");

                // Start monitoring
                _monitor.StartMonitoring();

                // Create the error handler
                _errorHandler = new FlashOperationErrorHandler(_logger, _monitor);

                _logger.LogInformation("FlashOperationMonitorService initialized successfully", "FlashOperationMonitorService");
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error initializing FlashOperationMonitorService: {ex.Message}", "FlashOperationMonitorService", ex);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Handles an error in a flash operation
        /// </summary>
        /// <param name="operationId">The operation ID</param>
        /// <param name="errorType">The type of error</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="errorData">Additional error data</param>
        /// <returns>A recovery result indicating the outcome of the recovery attempt</returns>
        public Task<FlashErrorRecoveryResult> HandleErrorAsync(Guid operationId, FlashErrorType errorType, string errorMessage, object? errorData = null)
        {
            if (_errorHandler == null)
            {
                _logger.LogError("Error handler is not initialized", "FlashOperationMonitorService");
                return Task.FromResult(new FlashErrorRecoveryResult(false, "Error handler is not initialized"));
            }

            return _errorHandler.HandleErrorAsync(operationId, errorType, errorMessage, errorData);
        }

        /// <summary>
        /// Disposes the service
        /// </summary>
        public void Dispose()
        {
            _errorHandler?.Dispose();
            _monitor?.Dispose();
            _logger.LogInformation("FlashOperationMonitorService disposed", "FlashOperationMonitorService");
        }
    }
}
