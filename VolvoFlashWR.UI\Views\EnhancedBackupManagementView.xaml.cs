using System.Windows;
using System.Windows.Controls;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.UI.ViewModels;

namespace VolvoFlashWR.UI.Views
{
    /// <summary>
    /// Interaction logic for EnhancedBackupManagementView.xaml
    /// </summary>
    public partial class EnhancedBackupManagementView : UserControl
    {
        public EnhancedBackupManagementView()
        {
            InitializeComponent();
        }

        private void VersionTree_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (DataContext is EnhancedBackupManagementViewModel viewModel && e.NewValue is BackupVersionNode node)
            {
                viewModel.SelectedVersion = node.Backup;
            }
        }
    }
}
