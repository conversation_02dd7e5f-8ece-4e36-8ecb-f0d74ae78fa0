using System;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.ECU;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Communication.Tests.ECU
{
    [TestFixture]
    public class ECUCommunicationServiceFactoryTests
    {
        private ECUCommunicationServiceFactory _factory;
        private Mock<ILoggingService> _loggerMock;
        private Mock<IVocomService> _vocomServiceMock;

        [SetUp]
        public void Setup()
        {
            _loggerMock = new Mock<ILoggingService>();
            _vocomServiceMock = new Mock<IVocomService>();
            _factory = new ECUCommunicationServiceFactory(_loggerMock.Object, _vocomServiceMock.Object);
        }

        [Test]
        public async Task CreateServiceAsync_WhenRealServiceInitializes_ReturnsRealService()
        {
            // Arrange
            _vocomServiceMock.Setup(v => v.InitializeAsync()).ReturnsAsync(true);

            // Act
            var service = await _factory.CreateServiceAsync();

            // Assert
            ClassicAssert.That(service, Is.Not.Null);
            ClassicAssert.IsInstanceOf<ECUCommunicationService>(service, "Service should be an instance of ECUCommunicationService");
        }

        [Test]
        public async Task CreateServiceAsync_WhenRealServiceFailsToInitialize_ReturnsDummyService()
        {
            // Arrange - Setup Vocom service to fail initialization for real service but succeed for dummy service
            int callCount = 0;
            _vocomServiceMock.Setup(v => v.InitializeAsync()).Returns(() =>
            {
                callCount++;
                // First 3 calls (for real service) fail, subsequent calls (for dummy service) succeed
                return Task.FromResult(callCount > 3);
            });

            // Act
            var service = await _factory.CreateServiceAsync();

            // Assert
            ClassicAssert.That(service, Is.Not.Null);
            ClassicAssert.IsInstanceOf<DummyECUCommunicationService>(service, "Service should be an instance of DummyECUCommunicationService");
        }

        [Test]
        public async Task CreateServiceAsync_WhenExceptionOccurs_ReturnsDummyService()
        {
            // Arrange - Setup Vocom service to throw exception for real service but succeed for dummy service
            int callCount = 0;
            _vocomServiceMock.Setup(v => v.InitializeAsync()).Returns(() =>
            {
                callCount++;
                if (callCount <= 3)
                {
                    throw new Exception("Test exception");
                }
                return Task.FromResult(true);
            });

            // Act
            var service = await _factory.CreateServiceAsync();

            // Assert
            ClassicAssert.That(service, Is.Not.Null);
            ClassicAssert.IsInstanceOf<DummyECUCommunicationService>(service, "Service should be an instance of DummyECUCommunicationService");
        }
    }
}

