﻿Chapter 10
XGATE (S12XGATEV3)

Table 10-1. Revision History

Revision Sections
Revision Date Description of Changes

Number Affected
V03.22 06 Oct 2005 - Internal updates
V03.23 14 Dec 2005 10.9.2/10-463 - Updated code example
V03.24 17 Jan 2006 - Internal updates

10.1 Introduction
The XGATE module is a peripheral co-processor that allows autonomous data transfers between the
MCU’s peripherals and the internal memories. It has a built in RISC core that is able to pre-process the
transferred data and perform complex communication protocols.

The XGATE module is intended to increase the MCU’s data throughput by lowering the S12X_CPU’s
interrupt load.

Figure 10-1 gives an overview on the XGATE architecture.

This document describes the functionality of the XGATE module, including:
• XGATE registers (Section 10.3, “Memory Map and Register Definition”)
• XGATE RISC core (Section 10.4.1, “XGATE RISC Core”)
• Hardware semaphores (Section 10.4.4, “Semaphores”)
• Interrupt handling (Section 10.5, “Interrupts”)
• Debug features (Section 10.6, “Debug Mode”)
• Security (Section 10.7, “Security”)
• Instruction set (Section 10.8, “Instruction Set”)

10.1.1 Glossary of Terms
XGATE Request

A service request from a peripheral module which is directed to the XGATE by the S12X_INT
module (see Figure 10-1). Each XGATE request attempts to activate a XGATE channel at a certain
priority level.

XGATE Channel
The resources in the XGATE module (i.e. Channel ID number, Priority level, Service Request
Vector, Interrupt Flag) which are associated with a particular XGATE Request.

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 353



Chapter 10 XGATE (S12XGATEV3)

XGATE Channel ID
A 7-bit identifier associated with an XGATE channel. In S12XE designs valid Channel IDs range
from $0D to $78.

XGATE Priority Level
A priority ranging from 1 to 7 which is associated with an XGATE channel. The priority level of
an XGATE channel is selected in the S12X_INT module.

XGATE Register Bank
A register bank consists of registers R1-R7, CCR and the PC. Each interrupt level is associated with
one register bank.

XGATE Channel Interrupt
An S12X_CPU interrupt that is triggered by a code sequence running on the XGATE module.

XGATE Software Channel
Special XGATE channel that is not associated with any peripheral service request. A Software
Channel is triggered by its Software Trigger Bit which is implemented in the XGATE module.

XGATE Semaphore
A set of hardware flip-flops that can be exclusively set by either the S12X_CPU or the XGATE.
(see Section 10.4.4, “Semaphores”)

XGATE Thread
A code sequence which is executed by the XGATE’s RISC core after receiving an XGATE request.

XGATE Debug Mode
A special mode in which the XGATE’s RISC core is halted for debug purposes. This mode enables
the XGATE’s debug features (see Section 10.6, “Debug Mode”).

XGATE Software Error
The XGATE is able to detect a number of error conditions caused by erratic software (see
Section 10.4.5, “Software Error Detection”). These error conditions will cause the XGATE to seize
program execution and flag an Interrupt to the S12X_CPU.

Word
A 16 bit entity.

Byte
An 8 bit entity.

10.1.2 Features
The XGATE module includes these features:

• Data movement between various targets (i.e. Flash, RAM, and peripheral modules)
• Data manipulation through built in RISC core

MC9S12XE-Family Reference Manual  Rev. 1.25
354 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

• Provides up to 108 XGATE channels, including 8 software triggered channels
• Interruptible thread execution
• Two register banks to support fast context switching between threads
• Hardware semaphores which are shared between the S12X_CPU and the XGATE module
• Able to trigger S12X_CPU interrupts upon completion of an XGATE transfer
• Software error detection to catch erratic application code

10.1.3 Modes of Operation
There are four run modes on S12XE devices.

• Run mode, wait mode, stop mode
The XGATE is able to operate in all of these three system modes. Clock activity will be
automatically stopped when the XGATE module is idle.

• Freeze mode (BDM active)
In freeze mode all clocks of the XGATE module may be stopped, depending on the module
configuration (see Section ********, “XGATE Control Register (XGMCTL)”).

10.1.4 Block Diagram
Figure 10-1 shows a block diagram of the XGATE.

Peripheral Interrupts
S12X_INT

XGATE

Interrupt Flags

Semaphores RISC Core
Software
Triggers

Software Triggers
SWE

Interrupt
Software Error Logic

S12X_DBG

Peripherals

S12X_MMC

Figure 10-1. XGATE Block Diagram

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 355

XGATE
Interrupts

(XGIF)

Data/Code XGATE
Requests



Chapter 10 XGATE (S12XGATEV3)

10.2 External Signal Description
The XGATE module has no external pins.

10.3 Memory Map and Register Definition
This section provides a detailed description of address space and registers used by the XGATE module.

The memory map for the XGATE module is given below in Figure 10-2.The address listed for each register
is the sum of a base address and an address offset. The base address is defined at the SoC level and the
address offset is defined at the module level. Reserved registers read zero. Write accesses to the reserved
registers have no effect.

10.3.1 Register Descriptions
This section consists of register descriptions in address order. Each description includes a standard register
diagram with an associated figure number. Details of register bits and field functions follow the register
diagrams, in bit order.

Register
15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0

Name

0x0000 R 0 0 0 0 0 0 0 0 0
XGMCTL XG XG

XG XG XG XGE XGFRZ XGDBG XGSS XGIE
XGEM XGSSM XG XGIEM FACT SWEF

W FRZM DBGM FACTM SWEFM

0x0002 R 0 XGCHID[6:0]
XGCHID W

0x0003 R 0 0 0 0 0 XGCHPL[2:0]
XGCHPL W

0x0004 R
Reserved W

0x0005 R 0 0 0 0 0 0
XGISPSEL XGISPSEL[1:0]

W

0x0006 R 0
XGISP74 XGISP74[15:1]

W
0x0006 R 0

XGISP31 XGISP31[15:1]
W

0x0006 R 0
XGVBR XGVBR[15:1]

W

= Unimplemented or Reserved

Figure 10-2. XGATE Register Summary (Sheet 1 of 3)

MC9S12XE-Family Reference Manual  Rev. 1.25
356 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

127 126 125 124 123 122 121 120 119 118 117 116 115 114 113 112

0x0008 R 0 0 0 0 0 0 0
XGIF XGIF_78 XGF_77 XGIF_76 XGIF_75 XGIF_74 XGIF_73 XGIF_72 XGIF_71 XGIF_70

W

111 110 109 108 107 106 105 104 103 102 101 100 99 98 97 96

0x000A R
XGIF XGIF_6F XGIF_6E XGIF_6D XGIF_6C XGIF_6B XGIF_6A XGIF_69 XGIF_68 XGF_67 XGIF_66 XGIF_65 XGIF_64 XGIF_63 XGIF_62 XGIF_61 XGIF_60

W

95 94 93 92 91 90 89 88 87 86 85 84 83 82 81 80

0x000C R
XGIF XGIF_5F XGIF_5E XGIF_5D XGIF_5C XGIF_5B XGIF_5A XGIF_59 XGIF_58 XGF_57 XGIF_56 XGIF_55 XGIF_54 XGIF_53 XGIF_52 XGIF_51 XGIF_50

W

79 78 77 76 75 74 73 72 71 70 69 68 67 66 65 64
0x000E R
XGIF XGIF_4F XGIF_4E XGIF_4D XGIF_4C XGIF_4B XGIF_4A XGIF_49 XGIF_48 XGF _47 XGIF_46 XGIF_45 XGIF_44 XGIF_43 XGIF_42 XGIF_41 XGIF_40

W

63 62 61 60 59 58 57 56 55 54 53 52 51 50 49 48
0x0010 R
XGIF XGIF_3F XGIF_3E XGIF_3D XGIF_3C XGIF_3B XGIF_3A XGIF_39 XGIF_38 XGF _37 XGIF_36 XGIF_35 XGIF_34 XGIF_33 XGIF_32 XGIF_31 XGIF_30

W

47 46 45 44 43 42 41 40 39 38 37 36 35 34 33 32
0x0012 R
XGIF XGIF_2F XGIF_2E XGIF_2D XGIF_2C XGIF_2B XGIF_2A XGIF_29 XGIF_28 XGF _27 XGIF_26 XGIF_25 XGIF_24 XGIF_23 XGIF_22 XGIF_21 XGIF_20

W

31 30 29 28 27 26 25 24 23 22 21 20 19 18 17 16
0x0014 R
XGIF XGIF_1F XGIF_1E XGIF_1D XGIF_1C XGIF_1B XGIF_1A XGIF_19 XGIF_18 XGF _17 XGIF_16 XGIF_15 XGIF_14 XGIF_13 XGIF_12 XGIF_11 XGIF_10

W

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
0x0016 R 0 0 0 0 0 0 0 0 0 0 0 0 0
XGIF XGIF_0F XGIF_0E XGIF_0D

W

= Unimplemented or Reserved

Figure 10-2. XGATE Register Summary (Sheet 2 of 3)

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 357



Chapter 10 XGATE (S12XGATEV3)

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0

0x0018 R 0 0 0 0 0 0 0 0
XGSWTM XGSWT[7:0]

W XGSWTM[7:0]

0x001A R 0 0 0 0 0 0 0 0
XGSEMM XGSEM[7:0]

W XGSEMM[7:0]

0x001C R
Reserved W

0x001D R 0 0 0 0
XGCCR XGN XGZ XGV XGC

W

0x001E R
XGPC XGPC

W

0x0020 R
Reserved W

0x0021 R
Reserved W

0x0022 R
XGR1 XGR1

W

0x0024 R
XGR2 XGR2

W

0x0026 R
XGR3 XGR3

W

0x0028 R
XGR4 XGR4

W

0x002A R
XGR5 XGR5

W

0x002C R
XGR6 XGR6

W

0x002E R
XGR7 XGR7

W

= Unimplemented or Reserved

Figure 10-2. XGATE Register Summary (Sheet 3 of 3)

MC9S12XE-Family Reference Manual  Rev. 1.25
358 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

******** XGATE Control Register (XGMCTL)
All module level switches and flags are located in the XGATE Module Control Register Figure 10-3.

Module Base +0x00000

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R 0 0 0 0 0 0 0 0 0

XG
W XG XG XG XG XG XGE XGFRZ XGDBG XGSS XGFACT XGIE

XGEM XGIEM SWEF
FRZM DBGM SSM FACTM SWEFM

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 10-3. XGATE Control Register (XGMCTL)

Read: Anytime

Write: Anytime

Table 10-2. XGMCTL Field Descriptions (Sheet 1 of 3)

Field Description

15 XGE Mask — This bit controls the write access to the XGE bit. The XGE bit can only be set or cleared if a "1" is
XGEM written to the XGEM bit in the same register access.

Read:
This bit will always read "0".

Write:
0 Disable write access to the XGE in the same bus cycle
1 Enable write access to the XGE in the same bus cycle

14 XGFRZ Mask — This bit controls the write access to the XGFRZ bit. The XGFRZ bit can only be set or cleared
XGFRZM if a "1" is written to the XGFRZM bit in the same register access.

Read:
This bit will always read "0".

Write:
0 Disable write access to the XGFRZ in the same bus cycle
1 Enable write access to the XGFRZ in the same bus cycle

13 XGDBG Mask — This bit controls the write access to the XGDBG bit. The XGDBG bit can only be set or cleared
XGDBGM if a "1" is written to the XGDBGM bit in the same register access.

Read:
This bit will always read "0".

Write:
0 Disable write access to the XGDBG in the same bus cycle
1 Enable write access to the XGDBG in the same bus cycle

12 XGSS Mask — This bit controls the write access to the XGSS bit. The XGSS bit can only be set or cleared if a
XGSSM "1" is written to the XGSSM bit in the same register access.

Read:
This bit will always read "0".

Write:
0 Disable write access to the XGSS in the same bus cycle
1 Enable write access to the XGSS in the same bus cycle

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 359



Chapter 10 XGATE (S12XGATEV3)

Table 10-2. XGMCTL Field Descriptions (Sheet 2 of 3)

Field Description

11 XGFACT Mask — This bit controls the write access to the XGFACT bit. The XGFACT bit can only be set or
XGFACTM cleared if a "1" is written to the XGFACTM bit in the same register access.

Read:
This bit will always read "0".

Write:
0 Disable write access to the XGFACT in the same bus cycle
1 Enable write access to the XGFACT in the same bus cycle

9 XGSWEF Mask — This bit controls the write access to the XGSWEF bit. The XGSWEF bit can only be cleared
XGSWEFM if a "1" is written to the XGSWEFM bit in the same register access.

Read:
This bit will always read "0".

Write:
0 Disable write access to the XGSWEF in the same bus cycle
1 Enable write access to the XGSWEF in the same bus cycle

8 XGIE Mask — This bit controls the write access to the XGIE bit. The XGIE bit can only be set or cleared if a "1"
XGIEM is written to the XGIEM bit in the same register access.

Read:
This bit will always read "0".

Write:
0 Disable write access to the XGIE in the same bus cycle
1 Enable write access to the XGIE in the same bus cycle

7 XGATE Module Enable (Request Enable)— This bit enables incoming XGATE requests from the S12X_INT
XGE module. If the XGE bit is cleared, pending XGATE requests will be ignored. The thread that is executed by the

RISC core while the XGE bit is cleared will continue to run.
Read:
0 Incoming requests are disabled
1 Incoming requests are enabled
Write:
0 Disable incoming requests
1 Enable incoming requests

6 Halt XGATE in Freeze Mode — The XGFRZ bit controls the XGATE operation in Freeze Mode (BDM active).
XGFRZ Read:

0 RISC core operates normally in Freeze (BDM active)
1 RISC core stops in Freeze Mode (BDM active)
Write:
0 Don’t stop RISC core in Freeze Mode (BDM active)
1 Stop RISC core in Freeze Mode (BDM active)

5 XGATE Debug Mode — This bit indicates that the XGATE is in Debug Mode (see Section 10.6, “Debug Mode”).
XGDBG Debug Mode can be entered by Software Breakpoints (BRK instruction), Tagged or Forced Breakpoints (see

S12X_DBG Section), or by writing a "1" to this bit.
Read:
0 RISC core is not in Debug Mode
1 RISC core is in Debug Mode
Write:
0 Leave Debug Mode
1 Enter Debug Mode
Note: Freeze Mode and Software Error Interrupts have no effect on the XGDBG bit.

MC9S12XE-Family Reference Manual  Rev. 1.25
360 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

Table 10-2. XGMCTL Field Descriptions (Sheet 3 of 3)

Field Description

4 XGATE Single Step — This bit forces the execution of a single instruction.(1)

XGSS Read:
0 No single step in progress
1 Single step in progress
Write
0 No effect
1 Execute a single RISC instruction
Note: Invoking a Single Step will cause the XGATE to temporarily leave Debug Mode until the instruction has

been executed.

3 Fake XGATE Activity — This bit forces the XGATE to flag activity to the MCU even when it is idle. When it is set
XGFACT the MCU will never enter system stop mode which assures that peripheral modules will be clocked during XGATE

idle periods
Read:
0 XGATE will only flag activity if it is not idle or in debug mode.
1 XGATE will always signal activity to the MCU.
Write:
0 Only flag activity if not idle or in debug mode.
1 Always signal XGATE activity.

1 XGATE Software Error Flag — This bit signals a software error. It is set whenever the RISC core detects an
XGSWEF error condition(2). The RISC core is stopped while this bit is set. Clearing this bit will terminate the current thread

and cause the XGATE to become idle.
Read:
0 No software error detected
1 Software error detected
Write:
0 No effect
1 Clears the XGSWEF bit

0 XGATE Interrupt Enable — This bit acts as a global interrupt enable for the XGATE module
XGIE Read:

0 All outgoing XGATE interrupts disabled (except software error interrupts)
1 All outgoing XGATE interrupts enabled
Write:
0 Disable all outgoing XGATE interrupts (except software error interrupts)
1 Enable all outgoing XGATE interrupts

1. Refer to Section 10.6.1, “Debug Features”
2. Refer to Section 10.4.5, “Software Error Detection”

10.3.1.2 XGATE Channel ID Register (XGCHID)
The XGATE Channel ID Register (Figure 10-4) shows the identifier of the XGATE channel that is
currently active. This register will read “$00” if the XGATE module is idle. In debug mode this register
can be used to start and terminate threads. Refer to Section 10.6.1, “Debug Features” for further
information.

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 361



Chapter 10 XGATE (S12XGATEV3)

Module Base +0x0002

7 6 5 4 3 2 1 0
R 0 XGCHID[6:0]
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 10-4. XGATE Channel ID Register (XGCHID)

Read: Anytime

Write: In Debug Mode1

Table 10-3. XGCHID Field Descriptions

Field Description

6–0 Request Identifier — ID of the currently active channel
XGCHID[6:0]

******** XGATE Channel Priority Level (XGCHPL)
The XGATE Channel Priority Level Register (Figure 10-5) shows the priority level of the current thread.
In debug mode this register can be used to select a priority level when launching a thread (see
Section 10.6.1, “Debug Features”).

Module Base +0x0003

7 6 5 4 3 2 1 0
R 0 0 0 0 0 XGCHPL[2:0]
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 10-5. XGATE Channel Priority Level Register (XGCHPL)

Read: Anytime

Write: In Debug Mode1

Table 10-4. XGCHPL Field Descriptions

Field Description

2-0 Priority Level— Priority level of the currently active channel
XGCHPL[2:0]

******** XGATE Initial Stack Pointer Select Register (XGISPSEL)
The XGATE Initial Stack Pointer Select Register (Figure 10-6) determines the register which is mapped
to address “Module Base +0x0006”. A value of zero selects the Vector Base Register (XGVBR). Setting

1. Refer to Section 10.6.1, “Debug Features”

MC9S12XE-Family Reference Manual  Rev. 1.25
362 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

this register to a channel priority level (non-zero value) selects the corresponding Initial Stack Pointer
Registers XGISP74 or XGISP31 (see Table 10-6).

Module Base +0x0005

7 6 5 4 3 2 1 0
R 0 0 0 0 0 0

XGISPSEL[1:0]
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 10-6. XGATE Initial Stack Pointer Select Register (XGISPSEL)

Read: Anytime

Write: Anytime

Table 10-5. XGISPSEL Field Descriptions

Field Description

1-0 Register select— Determines whether XGISP74, XGISP31, or XGVBR is mapped to “Module Base +0x0006”.
XGISPSEL[1:0] See Table 10-6.

Table 10-6. XGISP74, XGISP31, XGVBR Mapping

XGISPSEL[1:0] Register Mapped to “Module Base +0x0006“
3 Reserved
2 XGISP74
1 XGISP31
0 XGVBR

******** XGATE Initial Stack Pointer for Interrupt Priorities 7 to 4 (XGISP74)
The XGISP74 register is intended to point to the stack region that is used by XGATE channels of priority
7 to 4. Every time a thread of such priority is started, RISC core register R7 will be initialized with the
content of XGISP74.

Module Base +0x0006

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R 0

XGISP74[15:1]
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 10-7. XGATE Initial Stack Pointer for Interrupt Priorities 7 to 4 (XGISP74)

Read: Anytime

Write: Only if XGATE requests are disabled (XGE = 0) and idle (XGCHID = $00))

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 363



Chapter 10 XGATE (S12XGATEV3)

Table 10-7. XGISP74 Field Descriptions

Field Description

15–1 Initial Stack Pointer— The XGISP74 register holds the initial value of RISC core register R7, for threads of
XBISP74[15:1] priority 7 to 4.

******** XGATE Initial Stack Pointer for Interrupt Priorities 3 to 1 (XGISP31)
The XGISP31 register is intended to point to the stack region that is used by XGATE channels of priority
3 to 1. Every time a thread of such priority is started, RISC core register R7 will be initialized with the
content of XGISP31.

Module Base +0x0006

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R 0

XGISP31[15:1]
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 10-8. XGATE Initial Stack Pointer for Interrupt Priorities 3 to 1 (XGISP31)

Read: Anytime

Write: Only if XGATE requests are disabled (XGE = 0) and idle (XGCHID = $00))

Table 10-8. XGISP31 Field Descriptions

Field Description

15–1 Initial Stack Pointer— The XGISP31 register holds the initial value of RISC core register R7, for threads of
XBISP31[15:1] priority 3 to 1.

******** XGATE Vector Base Address Register (XGVBR)
The Vector Base Address Register (Figure 10-9) determines the location of the XGATE vector block (see
Section Figure 10-23., “XGATE Vector Block).

Module Base +0x0006

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R 0

XGVBR[15:1]
W

Reset 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 10-9. XGATE Vector Base Address Register (XGVBR)

Read: Anytime

Write: Only if XGATE requests are disabled (XGE = 0) and idle (XGCHID = $00))

MC9S12XE-Family Reference Manual  Rev. 1.25
364 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

Table 10-9. XGVBR Field Descriptions

Field Description

15–1 Vector Base Address — The XGVBR register holds the start address of the vector block in the XGATE
XBVBR[15:1] memory map.

******** XGATE Channel Interrupt Flag Vector (XGIF)
The XGATE Channel Interrupt Flag Vector (Figure 10-10) provides access to the interrupt flags of all
channels. Each flag may be cleared by writing a "1" to its bit location. Refer to Section 10.5.2, “Outgoing
Interrupt Requests” for further information.

Module Base +0x0008

127 126 125 124 123 122 121 120 119 118 117 116 115 114 113 112
R 0 0 0 0 0 0 0

XGIF_78 XGF_77 XGIF_76 XGIF_75 XGIF_74 XGIF_73 XGIF_72 XGIF_71 XGIF_70
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

111 110 109 108 107 106 105 104 103 102 101 100 99 98 97 96
R

XGIF_6F XGIF_6E XGIF_6D XGIF_6C XGIF_6B XGIF_6A XGIF_69 XGIF_68 XGF_67 XGIF_66 XGIF_65 XGIF_64 XGIF_63 XGIF_62 XGIF_61 XGIF_60
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

95 94 93 92 91 90 89 88 87 86 85 84 83 82 81 80
R

XGIF_5F XGIF_5E XGIF_5D XGIF_5C XGIF_5B XGIF_5A XGIF_59 XGIF_58 XGF_57 XGIF_56 XGIF_55 XGIF_54 XGIF_53 XGIF_52 XGIF_51 XGIF_50
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

79 78 77 76 75 74 73 72 71 70 69 68 67 66 65 64
R

XGIF_4F XGIF_4E XGIF_4D XGIF_4C XGIF_4B XGIF_4A XGIF_49 XGIF_48 XGF _47 XGIF_46 XGIF_45 XGIF_44 XGIF_43 XGIF_42 XGIF_41 XGIF_40
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 10-10. XGATE Channel Interrupt Flag Vector (XGIF)

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 365



Chapter 10 XGATE (S12XGATEV3)

63 62 61 60 59 58 57 56 55 54 53 52 51 50 49 48
R

XGIF_3F XGIF_3E XGIF_3D XGIF_3C XGIF_3B XGIF_3A XGIF_39 XGIF_38 XGF _37 XGIF_36 XGIF_35 XGIF_34 XGIF_33 XGIF_32 XGIF_31 XGIF_30
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

47 46 45 44 43 42 41 40 39 38 37 36 35 34 33 32
R

XGIF_2F XGIF_2E XGIF_2D XGIF_2C XGIF_2B XGIF_2A XGIF_29 XGIF_28 XGF _27 XGIF_26 XGIF_25 XGIF_24 XGIF_23 XGIF_22 XGIF_21 XGIF_20
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

31 30 29 28 27 26 25 24 23 22 21 20 19 18 17 16
R

XGIF_1F XGIF_1E XGIF_1D XGIF_1C XGIF_1B XGIF_1A XGIF_19 XGIF_18 XGF _17 XGIF_16 XGIF_15 XGIF_14 XGIF_13 XGIF_12 XGIF_11 XGIF_10
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R 0 0 0 0 0 0 0 0 0 0 0 0 0

XGIF_0F XGIF_0E XGIF_0D
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 10-10. XGATE Channel Interrupt Flag Vector (XGIF) (continued)

Read: Anytime

Write: Anytime

Table 10-10. XGIV Field Descriptions

Field Description

127–9 Channel Interrupt Flags — These bits signal pending channel interrupts. They can only be set by the RISC
XGIF[78:9] core (see SIF instruction on page 10-449). Each flag can be cleared by writing a "1" to its bit location.

Unimplemented interrupt flags will always read "0". Section “Interrupts” of the device overview for a list of
implemented Interrupts.
Read:
0 Channel interrupt is not pending
1 Channel interrupt is pending if XGIE is set
Write:
0 No effect
1 Clears the interrupt flag

MC9S12XE-Family Reference Manual  Rev. 1.25
366 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

NOTE
Suggested Mnemonics for accessing the interrupt flag vector on a word
basis are:

XGIF_7F_70 (XGIF[127:112]),
XGIF_6F_60 (XGIF[111:96]),
XGIF_5F_50 (XGIF[95:80]),
XGIF_4F_40 (XGIF[79:64]),
XGIF_3F_30 (XGIF[63:48]),
XGIF_2F_20 (XGIF[47:32]),
XGIF_1F_10 (XGIF[31:16]),
XGIF_0F_00 (XGIF[15:0])

******** XGATE Software Trigger Register (XGSWT)
The eight software triggers of the XGATE module can be set and cleared through the XGATE Software
Trigger Register (Figure 10-11). The upper byte of this register, the software trigger mask, controls the
write access to the lower byte, the software trigger bits. These bits can be set or cleared if a "1" is written
to the associated mask in the same bus cycle. Refer to Section 10.5.2, “Outgoing Interrupt Requests” for
further information.

Module Base +0x00018

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R 0 0 0 0 0 0 0 0

XGSWT[7:0]
W XGSWTM[7:0]

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 10-11. XGATE Software Trigger Register (XGSWT)

Read: Anytime

Write: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 367



Chapter 10 XGATE (S12XGATEV3)

Table 10-11. XGSWT Field Descriptions

Field Description

15–8 Software Trigger Mask — These bits control the write access to the XGSWT bits. Each XGSWT bit can only
XGSWTM[7:0] be written if a "1" is written to the corresponding XGSWTM bit in the same access.

Read:
These bits will always read "0".
Write:
0 Disable write access to the XGSWT in the same bus cycle
1 Enable write access to the corresponding XGSWT bit in the same bus cycle

7–0 Software Trigger Bits — These bits act as interrupt flags that are able to trigger XGATE software channels.
XGSWT[7:0] They can only be set and cleared by software.

Read:
0 No software trigger pending
1 Software trigger pending if the XGIE bit is set
Write:
0 Clear Software Trigger
1 Set Software Trigger

NOTE
The XGATE channel IDs that are associated with the eight software triggers
are determined on chip integration level. (see Section “Interrupts“ of the
device overview)

XGATE software triggers work like any peripheral interrupt. They can be
used as XGATE requests as well as S12X_CPU interrupts. The target of the
software trigger must be selected in the S12X_INT module.

********0 XGATE Semaphore Register (XGSEM)
The XGATE provides a set of eight hardware semaphores that can be shared between the S12X_CPU and
the XGATE RISC core. Each semaphore can either be unlocked, locked by the S12X_CPU or locked by
the RISC core. The RISC core is able to lock and unlock a semaphore through its SSEM and CSEM
instructions. The S12X_CPU has access to the semaphores through the XGATE Semaphore Register
(Figure 10-12). Refer to section Section 10.4.4, “Semaphores” for details.

Module Base +0x0001A

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R 0 0 0 0 0 0 0 0

XGSEM[7:0]
W XGSEMM[7:0]

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 10-12. XGATE Semaphore Register (XGSEM)

Read: Anytime

Write: Anytime (see Section 10.4.4, “Semaphores”)

MC9S12XE-Family Reference Manual  Rev. 1.25
368 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

Table 10-12. XGSEM Field Descriptions

Field Description

15–8 Semaphore Mask — These bits control the write access to the XGSEM bits.
XGSEMM[7:0] Read:

These bits will always read "0".
Write:
0 Disable write access to the XGSEM in the same bus cycle
1 Enable write access to the XGSEM in the same bus cycle

7–0 Semaphore Bits — These bits indicate whether a semaphore is locked by the S12X_CPU. A semaphore can
XGSEM[7:0] be attempted to be set by writing a "1" to the XGSEM bit and to the corresponding XGSEMM bit in the same

write access. Only unlocked semaphores can be set. A semaphore can be cleared by writing a "0" to the
XGSEM bit and a "1" to the corresponding XGSEMM bit in the same write access.
Read:
0 Semaphore is unlocked or locked by the RISC core
1 Semaphore is locked by the S12X_CPU
Write:
0 Clear semaphore if it was locked by the S12X_CPU
1 Attempt to lock semaphore by the S12X_CPU

********1 XGATE Condition Code Register (XGCCR)
The XGCCR register (Figure 10-13) provides access to the RISC core’s condition code register.

Module Base +0x001D

7 6 5 4 3 2 1 0
R 0 0 0 0

XGN XGZ XGV XGC
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 10-13. XGATE Condition Code Register (XGCCR)

Read: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Write: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Table 10-13. XGCCR Field Descriptions

Field Description

3 Sign Flag — The RISC core’s Sign flag
XGN

2 Zero Flag — The RISC core’s Zero flag
XGZ

1 Overflow Flag — The RISC core’s Overflow flag
XGV

0 Carry Flag — The RISC core’s Carry flag
XGC

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 369



Chapter 10 XGATE (S12XGATEV3)

********2 XGATE Program Counter Register (XGPC)
The XGPC register (Figure 10-14) provides access to the RISC core’s program counter.

Module Base +0x0001E

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

XGPC
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 10-14. XGATE Program Counter Register (XGPC)

Read: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Write: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Table 10-14. XGPC Field Descriptions

Field Description

15–0 Program Counter — The RISC core’s program counter
XGPC[15:0]

********3 XGATE Register 1 (XGR1)
The XGR1 register (Figure 10-15) provides access to the RISC core’s register 1.

Module Base +0x00022

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

XGR1
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 10-15. XGATE Register 1 (XGR1)

Read: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Write: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Table 10-15. XGR1 Field Descriptions

Field Description

15–0 XGATE Register 1 — The RISC core’s register 1
XGR1[15:0]

MC9S12XE-Family Reference Manual  Rev. 1.25
370 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

********4 XGATE Register 2 (XGR2)
The XGR2 register (Figure 10-16) provides access to the RISC core’s register 2.
Module Base +0x00024

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

XGR2
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 10-16. XGATE Register 2 (XGR2)

Read: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Write: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Table 10-16. XGR2 Field Descriptions

Field Description

15–0 XGATE Register 2 — The RISC core’s register 2
XGR2[15:0]

********5 XGATE Register 3 (XGR3)
The XGR3 register (Figure 10-17) provides access to the RISC core’s register 3.
Module Base +0x00026

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

XGR3
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 10-17. XGATE Register 3 (XGR3)

Read: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Write: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Table 10-17. XGR3 Field Descriptions

Field Description

15–0 XGATE Register 3 — The RISC core’s register 3
XGR3[15:0]

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 371



Chapter 10 XGATE (S12XGATEV3)

********6 XGATE Register 4 (XGR4)
The XGR4 register (Figure 10-18) provides access to the RISC core’s register 4.
Module Base +0x00028

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

XGR4
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 10-18. XGATE Register 4 (XGR4)

Read: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Write: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Table 10-18. XGR4 Field Descriptions

Field Description

15–0 XGATE Register 4 — The RISC core’s register 4
XGR4[15:0]

********7 XGATE Register 5 (XGR5)
The XGR5 register (Figure 10-19) provides access to the RISC core’s register 5.
Module Base +0x0002A

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

XGR5
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 10-19. XGATE Register 5 (XGR5)

Read: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Write: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Table 10-19. XGR5 Field Descriptions

Field Description

15–0 XGATE Register 5 — The RISC core’s register 5
XGR5[15:0]

MC9S12XE-Family Reference Manual  Rev. 1.25
372 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

********8 XGATE Register 6 (XGR6)
The XGR6 register (Figure 10-20) provides access to the RISC core’s register 6.
Module Base +0x0002C

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

XGR6
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 10-20. XGATE Register 6 (XGR6)

Read: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Write: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Table 10-20. XGR6 Field Descriptions

Field Description

15–0 XGATE Register 6 — The RISC core’s register 6
XGR6[15:0]

********9 XGATE Register 7 (XGR7)
The XGR7 register (Figure 10-21) provides access to the RISC core’s register 7.

Module Base +0x0002E

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

XGR7
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 10-21. XGATE Register 7 (XGR7)

Read: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Write: In debug mode if unsecured and not idle (XGCHID ≠ 0x00)

Table 10-21. XGR7 Field Descriptions

Field Description

15–0 XGATE Register 7 — The RISC core’s register 7
XGR7[15:0]

10.4 Functional Description
The core of the XGATE module is a RISC processor which is able to access the MCU’s internal memories
and peripherals (see Figure 10-1). The RISC processor always remains in an idle state until it is triggered
by an XGATE request. Then it executes a code sequence (thread) that is associated with the requested
XGATE channel. Each thread can run on a priority level ranging from 1 to 7. Refer to the S12X_INT

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 373



Chapter 10 XGATE (S12XGATEV3)

Section for information on how to select priority levels for XGATE threads. Low priority threads (interrupt
levels 1 to 3) can be interrupted by high priority threads (interrupt levels 4 to 7). High priority threads are
not interruptible. The register content of an interrupted thread is maintained and restored by the XGATE
hardware.

To signal the completion of a task the XGATE is able to send interrupts to the S12X_CPU. Each XGATE
channel has its own interrupt vector. Refer to the S12X_INT Section for detailed information.

The XGATE module also provides a set of hardware semaphores which are necessary to ensure data
consistency whenever RAM locations or peripherals are shared with the S12X_CPU.

The following sections describe the components of the XGATE module in further detail.

10.4.1 XGATE RISC Core
The RISC core is a 16 bit processor with an instruction set that is well suited for data transfers, bit
manipulations, and simple arithmetic operations (see Section 10.8, “Instruction Set”).

It is able to access the MCU’s internal memories and peripherals without blocking these resources from
the S12X_CPU1. Whenever the S12X_CPU and the RISC core access the same resource, the RISC core
will be stalled until the resource becomes available again.1

The XGATE offers a high access rate to the MCU’s internal RAM. Depending on the bus load, the RISC
core can perform up to two RAM accesses per S12X_CPU bus cycle.

Bus accesses to peripheral registers or flash are slower. A transfer rate of one bus access per S12X_CPU
cycle can not be exceeded.

The XGATE module is intended to execute short interrupt service routines that are triggered by peripheral
modules or by software.

10.4.2 Programmer’s Model

Register Block Program Counter
15 0 15 0

R7 (Stack Pointer) PC
15 0

R6 Condition
15 0 Code

R5 Register
15 0

R4 N Z V C
15 0

R3 3 2 1 0

15 0
R2

15 0
R1(Data Pointer)

15 0
R0 = 0

Figure 10-22. Programmer’s Model

1. With the exception of PRR registers (see Section “S12X_MMC”).

MC9S12XE-Family Reference Manual  Rev. 1.25
374 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

The programmer’s model of the XGATE RISC core is shown in Figure 10-22. The processor offers a set
of seven general purpose registers (R1 - R7), which serve as accumulators and index registers. An
additional eighth register (R0) is tied to the value “$0000”. Registers R1 and R7 have additional
functionality. R1 is preloaded with the initial data pointer of the channel’s service request vector (see
Figure 10-23). R7 is either preloaded with the content of XGISP74 if the interrupt priority of the current
channel is in the range 7 to 4, or it is with preloaded the content of XGISP31 if the interrupt priority of the
current channel is in the range 3 to 1. The remaining general purpose registers will be reset to an
unspecified value at the beginning of each thread.

The 16 bit program counter allows the addressing of a 64 kbyte address space.

The condition code register contains four bits: the sign bit (S), the zero flag (Z), the overflow flag (V), and
the carry bit (C). The initial content of the condition code register is undefined.

10.4.3 Memory Map
The XGATE’s RISC core is able to access an address space of 64K bytes. The allocation of memory blocks
within this address space is determined on chip level. Refer to the S12X_MMC Section for a detailed
information.

The XGATE vector block assigns a start address and a data pointer to each XGATE channel. Its position
in the XGATE memory map can be adjusted through the XGVBR register (see Section ********, “XGATE
Vector Base Address Register (XGVBR)”). Figure 10-23 shows the layout of the vector block. Each vector
consists of two 16 bit words. The first contains the start address of the service routine. This value will be
loaded into the program counter before a service routine is executed. The second word is a pointer to the
service routine’s data space. This value will be loaded into register R1 before a service routine is executed.

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 375



Chapter 10 XGATE (S12XGATEV3)

XGVBR
+$0000

unused

Code
+$0024

Channel $09 Initial Program Counter

Channel $09 Initial Data Pointer
+$0028

Channel $0A Initial Program Counter Data
Channel $0A Initial Data Pointer

+$002C
Channel $0B Initial Program Counter

Channel $0B Initial Data Pointer
+$0030

Channel $0C Initial Program Counter Code
Channel $0C Initial Data Pointer

+$01E0
Channel $78 Initial Program Counter Data
Channel $78 Initial Data Pointer

Figure 10-23. XGATE Vector Block

10.4.4 Semaphores
The XGATE module offers a set of eight hardware semaphores. These semaphores provide a mechanism
to protect system resources that are shared between two concurrent threads of program execution; one
thread running on the S12X_CPU and one running on the XGATE RISC core.

Each semaphore can only be in one of the three states: “Unlocked”, “Locked by S12X_CPU”, and “Locked
by XGATE”. The S12X_CPU can check and change a semaphore’s state through the XGATE semaphore
register (XGSEM, see Section ********0, “XGATE Semaphore Register (XGSEM)”). The RISC core does
this through its SSEM and CSEM instructions.

IFigure 10-24 illustrates the valid state transitions.

MC9S12XE-Family Reference Manual  Rev. 1.25
376 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

set_xgsem: 1 is written to XGSEM[n] (and 1 is written to XGSEMM[n])
clr_xgsem: 0 is written to XGSEM[n] (and 1 is written to XGSEMM[n])
ssem: Executing SSEM instruction (on semaphore n)
csem: Executing CSEM instruction (on semaphore n)

clr_xgsem csem

LOCKED BY LOCKED BY
S12X_CPU XGATE

clr_xgsem csem

ssem & ssem
set_xgsem

UNLOCKED

ssem & set_xgsem

Figure 10-24. Semaphore State Transitions

Figure 10-25 gives an example of the typical usage of the XGATE hardware semaphores.

Two concurrent threads are running on the system. One is running on the S12X_CPU and the other is
running on the RISC core. They both have a critical section of code that accesses the same system resource.
To guarantee that the system resource is only accessed by one thread at a time, the critical code sequence
must be embedded in a semaphore lock/release sequence as shown.

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 377



Chapter 10 XGATE (S12XGATEV3)

S12X_CPU XGATE
......... .........

1 ⇒ XGSEM[n] SSEM

XGSEM[n]  1? BCC?

critical critical
code code

sequence sequence

0 ⇒ XGSEM[n] CSEM

......... .........
Figure 10-25. Algorithm for Locking and Releasing Semaphores

10.4.5 Software Error Detection
Upon detecting an error condition caused by erratic application code, the XGATE module will
immediately terminate program execution and trigger a non-maskable interrupt to the S12X_CPU. There
are three error conditions:

• Execution of an illegal opcode
• Illegal opcode fetches
• Illegal load or store accesses

All opcodes which are not listed in section Section 10.8, “Instruction Set” are illegal opcodes. Illegal
opcode fetches as well as illegal load and store accesses are defined on chip level. Refer to the
S12X_MMC Section for a detailed information.

NOTE
When executing a branch (BCC, BCS,...), a jump (JAL) or an RTS
instruction, the XGATE prefetches and discards the opcode of the following
instruction. The XGATE will perform its software error handling actions
(see above) if this opcode fetch is illegal.

MC9S12XE-Family Reference Manual  Rev. 1.25
378 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

10.5 Interrupts

10.5.1 Incoming Interrupt Requests
XGATE threads are triggered by interrupt requests which are routed to the XGATE module (see
S12X_INT Section). Only a subset of the MCU’s interrupt requests can be routed to the XGATE. Which
specific interrupt requests these are and which channel ID they are assigned to is documented in Section
“Interrupts” of the device overview.

10.5.2 Outgoing Interrupt Requests
There are three types of interrupt requests which can be triggered by the XGATE module:

4. Channel interrupts
For each XGATE channel there is an associated interrupt flag in the XGATE interrupt flag vector
(XGIF, see Section ********, “XGATE Channel Interrupt Flag Vector (XGIF)”). These flags can be
set through the "SIF" instruction by the RISC core. They are typically used to flag an interrupt to
the S12X_CPU when the XGATE has completed one of its task.

5. Software triggers
Software triggers are interrupt flags, which can be set and cleared by software (see
Section ********, “XGATE Software Trigger Register (XGSWT)”). They are typically used to
trigger XGATE tasks by the S12X_CPU software. However these interrupts can also be routed to
the S12X_CPU (see S12X_INT Section) and triggered by the XGATE software.

6. Software error interrupt
The software error interrupt signals to the S12X_CPU the detection of an error condition in the
XGATE application code (see Section 10.4.5, “Software Error Detection”). This is a non-maskable
interrupt. Executing the interrupt service routine will automatically reset the interrupt line.

All outgoing XGATE interrupts, except software error interrupts, can be disabled by the XGIE bit in the
XGATE module control register (XGMCTL, see Section ********, “XGATE Control Register
(XGMCTL)”).

10.6 Debug Mode
The XGATE debug mode is a feature to allow debugging of application code.

10.6.1 Debug Features
In debug mode the RISC core will be halted and the following debug features will be enabled:

• Read and Write accesses to RISC core registers (XGCCR, XGPC, XGR1–XGR7)1

All RISC core registers can be modified. Leaving debug mode will cause the RISC core to continue
program execution with the modified register values.

1. Only possible if MCU is unsecured

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 379



Chapter 10 XGATE (S12XGATEV3)

• Single Stepping
Writing a "1" to the XGSS bit will call the RISC core to execute a single instruction. All RISC core
registers will be updated accordingly.

• Write accesses to the XGCHID register and the XGCHPL register
XGATE threads can be initiated and terminated through a 16 write access to the XGCHID and the
XGCHPL register or through a 8 bit write access to the XGCHID register. Detailed operation is
shown in Table 10-22. Once a thread has been initiated it’s code can be either single stepped or it
can be executed by leaving debug mode.

Table 10-22. Initiating and Terminating Threads in Debug Mode

Single Cycle Write
Register Content

Access to... Action
XGCHID XGCHPL XGCHID XGCHPL

Set new XGCHID
0 0 1..127 -(1) Set XGCHPL to 0x01

Initiate new thread
Set new XGCHID

0 0 1..127 0..7 Set new XGCHPL
Initiate new thread

Interrupt current thread
Set new XGCHID

1..127 0..3 1..127 4..7
Set new XGCHPL
Initiate new thread

0..7 Terminate current thread.
1..127 0..7 0 Resume interrupted thread or become idle if

-1 no interrupted thread is pending

All other combinations No action
1. 8 bit write access to XGCHID

NOTE
Even though zero is not a valid interrupt priority level of the S12X_INT
module, a thread of priority level 0 can be initiated in debug mode. The
XGATE handles requests of priority level 0 in the same way as it handles
requests of priority levels 1 to 3.

NOTE
All channels 1 to 127 can be initiated by writing to the XGCHID register,
even if they are not assigned to any peripheral module.

NOTE
In Debug Mode the XGATE will ignore all requests from peripheral
modules.

********.1 Entering Debug Mode
Debug mode can be entered in four ways:

1. Setting XGDBG to "1"

MC9S12XE-Family Reference Manual  Rev. 1.25
380 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

Writing a "1" to XGDBG and XGDBGM in the same write access causes the XGATE to enter
debug mode upon completion of the current instruction.

NOTE
After writing to the XGDBG bit the XGATE will not immediately enter
debug mode. Depending on the instruction that is executed at this time there
may be a delay of several clock cycles. The XGDBG will read "0" until
debug mode is entered.

2. Software breakpoints
XGATE programs which are stored in the internal RAM allow the use of software breakpoints. A
software breakpoint is set by replacing an instruction of the program code with the "BRK"
instruction.
As soon as the program execution reaches the "BRK" instruction, the XGATE enters debug mode.
Additionally a software breakpoint request is sent to the S12X_DBG module (see section 4.9 of
the S12X_DBG Section).
Upon entering debug mode, the program counter will point to the "BRK" instruction. The other
RISC core registers will hold the result of the previous instruction.
To resume program execution, the "BRK" instruction must be replaced by the original instruction
before leaving debug mode.

3. Tagged Breakpoints
The S12X_DBG module is able to place tags on fetched opcodes. The XGATE is able to enter
debug mode right before a tagged opcode is executed (see section 4.9 of the S12X_DBG Section).
Upon entering debug mode, the program counter will point to the tagged instruction. The other
RISC core registers will hold the result of the previous instruction.

4. Forced Breakpoints
Forced breakpoints are triggered by the S12X_DBG module (see section 4.9 of the S12X_DBG
Section). When a forced breakpoint occurs, the XGATE will enter debug mode upon completion
of the current instruction.

10.6.2 Leaving Debug Mode
Debug mode can only be left by setting the XGDBG bit to "0". If a thread is active (XGCHID has not been
cleared in debug mode), program execution will resume at the value of XGPC.

10.7 Security
In order to protect XGATE application code on secured S12X devices, a few restrictions in the debug
features have been made. These are:

• Registers XGCCR, XGPC, and XGR1–XGR7 will read zero on a secured device
• Registers XGCCR, XGPC, and XGR1–XGR7 can not be written on a secured device
• Single stepping is not possible on a secured device

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 381



Chapter 10 XGATE (S12XGATEV3)

10.8 Instruction Set

10.8.1 Addressing Modes
For the ease of implementation the architecture is a strict Load/Store RISC machine, which means all
operations must have one of the eight general purpose registers R0 … R7 as their source as well their
destination.

All word accesses must work with a word aligned address, that is A[0] = 0!

******** Naming Conventions
RD Destination register, allowed range is R0–R7
RD.L Low byte of the destination register, bits [7:0]
RD.H High byte of the destination register, bits [15:8]
RS, RS1, RS2 Source register, allowed range is R0–R7
RS.L, RS1.L, RS2.L Low byte of the source register, bits [7:0]
RS.H, RS1.H, RS2.H High byte of the source register, bits[15:8]
RB Base register for indexed addressing modes, allowed

range is R0–R7
RI Offset register for indexed addressing modes with

register offset, allowed range is R0–R7
RI+ Offset register for indexed addressing modes with

register offset and post-increment,
Allowed range is R0–R7 (R0+ is equivalent to R0)

–RI Offset register for indexed addressing modes with
register offset and pre-decrement,
Allowed range is R0–R7 (–R0 is equivalent to R0)

NOTE
Even though register R1 is intended to be used as a pointer to the data
segment, it may be used as a general purpose data register as well.

Selecting R0 as destination register will discard the result of the instruction.
Only the condition code register will be updated

******** Inherent Addressing Mode (INH)
Instructions that use this addressing mode either have no operands or all operands are in internal XGATE
registers.

Examples:
BRK
RTS

MC9S12XE-Family Reference Manual  Rev. 1.25
382 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

******** Immediate 3-Bit Wide (IMM3)
Operands for immediate mode instructions are included in the instruction stream and are fetched into the
instruction queue along with the rest of the 16 bit instruction. The ’#’ symbol is used to indicate an
immediate addressing mode operand. This address mode is used for semaphore instructions.

Examples:
CSEM #1 ; Unlock semaphore 1
SSEM #3 ; Lock Semaphore 3

******** Immediate 4 Bit Wide (IMM4)
The 4 bit wide immediate addressing mode is supported by all shift instructions.

RD = RD ∗ IMM4

Examples:
LSL R4,#1 ; R4 = R4 << 1; shift register R4 by 1 bit to the left
LSR R4,#3 ; R4 = R4 >> 3; shift register R4 by 3 bits to the right

******** Immediate 8 Bit Wide (IMM8)
The 8 bit wide immediate addressing mode is supported by four major commands (ADD, SUB, LD, CMP).

RD = RD ∗ imm8

Examples:
ADDL R1,#1 ; adds an 8 bit value to register R1
SUBL R2,#2 ; subtracts an 8 bit value from register R2
LDH R3,#3 ; loads an 8 bit immediate into the high byte of Register R3
CMPL R4,#4 ; compares the low byte of register R4 with an immediate value

******** Immediate 16 Bit Wide (IMM16)
The 16 bit wide immediate addressing mode is a construct to simplify assembler code. Instructions which
offer this mode are translated into two opcodes using the eight bit wide immediate addressing mode.

RD = RD ∗ IMM16

Examples:
LDW R4,#$1234 ; translated to LDL R4,#$34; LDH R4,#$12
ADD R4,#$5678 ; translated to ADDL R4,#$78; ADDH R4,#$56

******** Monadic Addressing (MON)
In this addressing mode only one operand is explicitly given. This operand can either be the source (f(RD)),
the target (RD = f()), or both source and target of the operation (RD = f(RD)).

Examples:
JAL R1 ; PC = R1, R1 = PC+2
SIF R2 ; Trigger IRQ associated with the channel number in R2.L

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 383



Chapter 10 XGATE (S12XGATEV3)

******** Dyadic Addressing (DYA)
In this mode the result of an operation between two registers is stored in one of the registers used as
operands.

RD = RD ∗ RS is the general register to register format, with register RD being the first operand and RS
the second. RD and RS can be any of the 8 general purpose registers R0 … R7. If R0 is used as the
destination register, only the condition code flags are updated. This addressing mode is used only for shift
operations with a variable shift value

Examples:
LSL R4,R5 ; R4 = R4 << R5
LSR R4,R5 ; R4 = R4 >> R5

******** Triadic Addressing (TRI)
In this mode the result of an operation between two or three registers is stored into a third one.
RD = RS1 ∗ RS2 is the general format used in the order RD, RS1, RS1. RD, RS1, RS2 can be any of the
8 general purpose registers R0 … R7. If R0 is used as the destination register RD, only the condition code
flags are updated. This addressing mode is used for all arithmetic and logical operations.

Examples:
ADC R5,R6,R7 ; R5 = R6 + R7 + Carry
SUB R5,R6,R7 ; R5 = R6 - R7

********0 Relative Addressing 9-Bit Wide (REL9)
A 9-bit signed word address offset is included in the instruction word. This addressing mode is used for
conditional branch instructions.

Examples:
BCC REL9 ; PC = PC + 2 + (REL9 << 1)
BEQ REL9 ; PC = PC + 2 + (REL9 << 1)

********1 Relative Addressing 10-Bit Wide (REL10)
An 10-bit signed word address offset is included in the instruction word. This addressing mode is used for
the unconditional branch instruction.

Examples:
BRA REL10 ; PC = PC + 2 + (REL10 << 1)

********2 Index Register plus Immediate Offset (IDO5)
(RS, #OFFS5) provides an unsigned offset from the base register.

Examples:
LDB R4,(R1,#OFFS5) ; loads a byte from (R1+OFFS5) into R4
STW R4,(R1,#OFFS5) ; stores R4 as a word to (R1+OFFS5)

MC9S12XE-Family Reference Manual  Rev. 1.25
384 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

********3 Index Register plus Register Offset (IDR)
For load and store instructions (RS, RI) provides a variable offset in a register.

Examples:
LDB R4,(R1,R2) ; loads a byte from (R1+R2) into R4
STW R4,(R1,R2) ; stores R4 as a word to (R1+R2)

********* Index Register plus Register Offset with Post-increment (IDR+)
[RS, RI+] provides a variable offset in a register, which is incremented after accessing the memory. In case
of a byte access the index register will be incremented by one. In case of a word access it will be
incremented by two.

Examples:
LDB R4,(R1,R2+) ; loads a byte from (R1+R2) into R4, R2+=1
STW R4,(R1,R2+) ; stores R4 as a word to (R1+R2), R2+=2

********5 Index Register plus Register Offset with Pre-decrement (–IDR)
[RS, -RI] provides a variable offset in a register, which is decremented before accessing the memory. In
case of a byte access the index register will be decremented by one. In case of a word access it will be
decremented by two.

Examples:
LDB R4,(R1,-R2) ; R2 -=1, loads a byte from (R1+R2) into R4
STW R4,(R1,-R2) ; R2 -=2, stores R4 as a word to (R1+R2)

10.8.2 Instruction Summary and Usage

******** Load & Store Instructions
Any register can be loaded either with an immediate or from the address space using indexed addressing
modes.

LDL RD,#IMM8 ; loads an immediate 8 bit value to the lower byte of RD
LDW RD,(RB,RI) ; loads data using RB+RI as effective address

LDB RD,(RB, RI+) ; loads data using RB+RI as effective address
; followed by an increment of RI depending on
; the size of the operation

The same set of modes is available for the store instructions
STB RS,(RB, RI) ; stores data using RB+RI as effective address

STW RS,(RB, RI+) ; stores data using RB+RI as effective address
; followed by an increment of RI depending on
; the size of the operation.

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 385



Chapter 10 XGATE (S12XGATEV3)

******** Logic and Arithmetic Instructions
All logic and arithmetic instructions support the 8 bit immediate addressing mode (IMM8: RD = RD ∗
#IMM8) and the triadic addressing mode (TRI: RD = RS1 ∗ RS2).

All arithmetic is considered as signed, sign, overflow, zero and carry flag will be updated. The carry will
not be affected for logical operations.

ADDL R2,#1 ; increment R2
ANDH R4,#$FE ; R4.H = R4.H & $FE, clear lower bit of higher byte

ADD R3,R4,R5 ; R3 = R4 + R5
SUB R3,R4,R5 ; R3 = R4 - R5

AND R3,R4,R5 ; R3 = R4 & R5 logical AND on the whole word
OR R3,R4,R5 ; R3 = R4 | R5

******** Register – Register Transfers
This group comprises transfers from and to some special registers

TFR R3,CCR ; transfers the condition code register to the low byte of
; register R3

Branch Instructions

The branch offset is +255 words or -256 words counted from the beginning of the next instruction. Since
instructions have a fixed 16 bit width, the branch offsets are word aligned by shifting the offset value by 2.

BEQ label ; if Z flag = 1 branch to label

An unconditional branch allows a +511 words or -512 words branch distance.
BRA label

******** Shift Instructions
Shift operations allow the use of a 4 bit wide immediate value to identify a shift width within a 16 bit word.
For shift operations a value of 0 does not shift at all, while a value of 15 shifts the register RD by 15 bits.
In a second form the shift value is contained in the bits 3:0 of the register RS.

Examples:
LSL R4,#1 ; R4 = R4 << 1; shift register R4 by 1 bit to the left
LSR R4,#3 ; R4 = R4 >> 3; shift register R4 by 3 bits to the right
ASR R4,R2 ; R4 = R4 >> R2;arithmetic shift register R4 right by the amount

;            of bits contained in R2[3:0].

MC9S12XE-Family Reference Manual  Rev. 1.25
386 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

******** Bit Field Operations
This addressing mode is used to identify the position and size of a bit field for insertion or extraction. The
width and offset are coded in the lower byte of the source register 2, RS2. The content of the upper byte is
ignored. An offset of 0 denotes the right most position and a width of 0 denotes 1 bit. These instructions
are very useful to extract, insert, clear, set or toggle portions of a 16 bit word

7 4 3 0
W4 O4 RS2

15 5 2 0
W4=3, O4=2 RS1

Bit Field Extract
Bit Field Insert

15 3 0
RD

Figure 10-26. Bit Field Addressing

BFEXT R3,R4,R5 ; R5: W4+1 bits with offset O4, will be extracted from R4 into R3

******** Special Instructions for DMA Usage
The XGATE offers a number of additional instructions for flag manipulation, program flow control and
debugging:

1. SIF: Set a channel interrupt flag
2. SSEM: Test and set a hardware semaphore
3. CSEM: Clear a hardware semaphore
4. BRK: Software breakpoint
5. NOP: No Operation
6. RTS: Terminate the current thread

10.8.3 Cycle Notation
Table 10-23 show the XGATE access detail notation. Each code letter equals one XGATE cycle. Each
letter implies additional wait cycles if memories or peripherals are not accessible. Memories or peripherals
are not accessible if they are blocked by the S12X_CPU. In addition to this Peripherals are only accessible
every other XGATE cycle. Uppercase letters denote 16 bit operations. Lowercase letters denote 8 bit
operations. The XGATE is able to perform two bus or wait cycles per S12X_CPU cycle.

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 387



Chapter 10 XGATE (S12XGATEV3)

Table 10-23. Access Detail Notation

V — Vector fetch: always an aligned word read, lasts for at least one RISC core cycle

P — Program word fetch: always an aligned word read, lasts for at least one RISC core cycle

r — 8 bit data read: lasts for at least one RISC core cycle

R — 16 bit data read: lasts for at least one RISC core cycle

w — 8 bit data write: lasts for at least one RISC core cycle

W — 16 bit data write: lasts for at least one RISC core cycle

A — Alignment cycle: no read or write, lasts for zero or one RISC core cycles

f — Free cycle: no read or write, lasts for one RISC core cycles

Special Cases

PP/P — Branch: PP if branch taken, P if not

10.8.4 Thread Execution
When the RISC core is triggered by an interrupt request (see Figure 10-1) it first executes a vector fetch
sequence which performs three bus accesses:

1. A V-cycle to fetch the initial content of the program counter.
2. A V-cycle to fetch the initial content of the data segment pointer (R1).
3. A P-cycle to load the initial opcode.

Afterwards a sequence of instructions (thread) is executed which is terminated by an "RTS" instruction. If
further interrupt requests are pending after a thread has been terminated, a new vector fetch will be
performed. Otherwise the RISC core will either resume a previous thread (beginning with a P-cycle to
refetch the interrupted opcode) or it will become idle until a new interrupt request is received. A thread can
only be interrupted by an interrupt request of higher priority.

10.8.5 Instruction Glossary
This section describes the XGATE instruction set in alphabetical order.

MC9S12XE-Family Reference Manual  Rev. 1.25
388 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

ADC Add with Carry ADC
Operation

RS1 + RS2 + C ⇒ RD

Adds the content of register RS1, the content of register RS2 and the value of the Carry bit using binary
addition and stores the result in the destination register RD. The Zero Flag is also carried forward from the
previous operation allowing 32 and more bit additions.

Example:
ADD R6,R2,R2
ADC R7,R3,R3 ; R7:R6 = R5:R4 + R3:R2
BCC ; conditional branch on 32 bit addition

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000 and Z was set before this operation; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RS1[15] & RS2[15] & RD[15]new | RS1[15] & RS2[15] & RD[15]new
C: Set if there is a carry from bit 15 of the result; cleared otherwise.

RS1[15] & RS2[15] | RS1[15] & RD[15]new | RS2[15] & RD[15]new

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

ADC RD, RS1, RS2 TRI 0 0 0 1 1 RD RS1 RS2 1 1 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 389



Chapter 10 XGATE (S12XGATEV3)

ADD Add without Carry ADD
Operation

RS1 + RS2 ⇒ RD
RD + IMM16 ⇒ RD (translates to ADDL RD, #IMM16[7:0]; ADDH RD, #IMM16[15:8])

Performs a 16 bit addition and stores the result in the destination register RD.

NOTE
When using immediate addressing mode (ADD RD, #IMM16), the V-flag
and the C-Flag of the first instruction (ADDL RD, #IMM16[7:0]) are not
considered by the second instruction (ADDH RD, #IMM16[15:8]).
⇒ Don’t rely on the V-Flag if RD + IMM16[7:0] ≥ 215.
⇒ Don’t rely on the C-Flag if RD + IMM16[7:0] ≥ 216.

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RS1[15] & RS2[15] & RD[15]new | RS1[15] & RS2[15] & RD[15]new
Refer to ADDH instruction for #IMM16 operations.

C: Set if there is a carry from bit 15 of the result; cleared otherwise.
RS1[15] & RS2[15] | RS1[15] & RD[15]new | RS2[15] & RD[15]new
Refer to ADDH instruction for #IMM16 operations.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

ADD RD, RS1, RS2 TRI 0 0 0 1 1 RD RS1 RS2 1 0 P

ADD RD, #IMM16 IMM8 1 1 1 0 0 RD IMM16[7:0] P

IMM8 1 1 1 0 1 RD IMM16[15:8] P

MC9S12XE-Family Reference Manual  Rev. 1.25
390 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

ADDH Add Immediate 8 bit Constant
(High Byte) ADDH

Operation

RD + IMM8:$00 ⇒ RD

Adds the content of high byte of register RD and a signed immediate 8 bit constant using binary addition
and stores the result in the high byte of the destination register RD. This instruction can be used after an
ADDL for a 16 bit immediate addition.

Example:
ADDL R2,#LOWBYTE
ADDH R2,#HIGHBYTE ; R2 = R2 + 16 bit immediate

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RD[15]old & IMM8[7] & RD[15]new | RD[15]old & IMM8[7] & RD[15]new
C: Set if there is a carry from the bit 15 of the result; cleared otherwise.

RD[15]old & IMM8[7] | RD[15]old & RD[15]new | IMM8[7] & RD[15]new

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

ADDH RD, #IMM8 IMM8 1 1 1 0 1 RD IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 391



Chapter 10 XGATE (S12XGATEV3)

ADDL Add Immediate 8 bit Constant
(Low Byte) ADDL

Operation

RD + $00:IMM8 ⇒ RD

Adds the content of register RD and an unsigned immediate 8 bit constant using binary addition and stores
the result in the destination register RD. This instruction must be used first for a 16 bit immediate addition
in conjunction with the ADDH instruction.

CCR Effects

N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the 8 bit operation; cleared otherwise.

RD[15]old & RD[15]new
C: Set if there is a carry from the bit 15 of the result; cleared otherwise.

RD[15]old & RD[15]new

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

ADDL RD, #IMM8 IMM8 1 1 1 0 0 RD IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
392 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

AND Logical AND AND
Operation

RS1 & RS2 ⇒ RD
RD & IMM16 ⇒ RD (translates to ANDL RD, #IMM16[7:0]; ANDH RD, #IMM16[15:8])

Performs a bit wise logical AND of two 16 bit values and stores the result in the destination register RD.

NOTE
When using immediate addressing mode (AND RD, #IMM16), the Z-flag
of the first instruction (ANDL RD, #IMM16[7:0]) is not considered by the
second instruction (ANDH RD, #IMM16[15:8]).
⇒ Don’t rely on the Z-Flag.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.

Refer to ANDH instruction for #IMM16 operations.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

AND RD, RS1, RS2 TRI 0 0 0 1 0 RD RS1 RS2 0 0 P

AND RD, #IMM16 IMM8 1 0 0 0 0 RD IMM16[7:0] P

IMM8 1 0 0 0 1 RD IMM16[15:8] P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 393



Chapter 10 XGATE (S12XGATEV3)

ANDH Logical AND Immediate 8 bit Constant
(High Byte) ANDH

Operation

RD.H & IMM8 ⇒ RD.H

Performs a bit wise logical AND between the high byte of register RD and an immediate 8 bit constant and
stores the result in the destination register RD.H. The low byte of RD is not affected.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the 8 bit result is $00; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

ANDH RD, #IMM8 IMM8 1 0 0 0 1 RD IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
394 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

ANDL Logical AND Immediate 8 bit Constant
(Low Byte) ANDL

Operation

RD.L & IMM8 ⇒ RD.L

Performs a bit wise logical AND between the low byte of register RD and an immediate 8 bit constant and
stores the result in the destination register RD.L. The high byte of RD is not affected.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 7 of the result is set; cleared otherwise.
Z: Set if the 8 bit result is $00; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

ANDL RD, #IMM8 IMM8 1 0 0 0 0 RD IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 395



Chapter 10 XGATE (S12XGATEV3)

ASR Arithmetic Shift Right ASR
Operation

n

b15 RD C

n = RS or IMM4

Shifts the bits in register RD n positions to the right. The higher n bits of the register RD become filled
with the sign bit (RD[15]). The carry flag will be updated to the bit contained in RD[n-1] before the shift
for n > 0.

n can range from 0 to 16.

In immediate address mode, n is determined by the operand IMM4. n is considered to be 16 if IMM4 is
equal to 0.

In dyadic address mode, n is determined by the content of RS. n is considered to be 16 if the content of RS
is greater than 15.

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RD[15]old ^ RD[15]new
C: Set if n > 0 and RD[n-1] = 1; if n = 0 unaffected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

ASR RD, #IMM4 IMM4 0 0 0 0 1 RD IMM4 1 0 0 1 P

ASR RD, RS DYA 0 0 0 0 1 RD RS 1 0 0 0 1 P

MC9S12XE-Family Reference Manual  Rev. 1.25
396 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

BCC Branch if Carry Cleared
(Same as BHS) BCC

Operation

If C = 0, then PC + $0002 + (REL9 << 1) ⇒ PC

Tests the Carry flag and branches if C = 0.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BCC REL9 REL9 0 0 1 0 0 0 0 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 397



Chapter 10 XGATE (S12XGATEV3)

BCS Branch if Carry Set
(Same as BLO) BCS

Operation

If C = 1, then PC + $0002 + (REL9 << 1) ⇒ PC

Tests the Carry flag and branches if C = 1.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BCS REL9 REL9 0 0 1 0 0 0 1 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
398 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

BEQ Branch if Equal BEQ
Operation

If Z = 1, then PC + $0002 + (REL9 << 1) ⇒ PC

Tests the Zero flag and branches if Z = 1.

CCR Effect
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BEQ REL9 REL9 0 0 1 0 0 1 1 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 399



Chapter 10 XGATE (S12XGATEV3)

BFEXT Bit Field Extract BFEXT
Operation

RS1[(o+w):o] ⇒ RD[w:0]; 0 ⇒ RD[15:(w+1)]
w = (RS2[7:4])
o = (RS2[3:0])

Extracts w+1 bits from register RS1 starting at position o and writes them right aligned into register RD.
The remaining bits in RD will be cleared. If (o+w) > 15 only bits [15:o] get extracted.

15 7 4 3 0

W4 O4 RS2

15 5 2 0

W4=3, O4=2 RS1

Bit Field Extract

15 3 0

0 RD

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BFEXT RD, RS1, RS2 TRI 0 1 1 0 0 RD RS1 RS2 1 1 P

MC9S12XE-Family Reference Manual  Rev. 1.25
400 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

BFFO Bit Field Find First One BFFO
Operation

FirstOne(RS) ⇒ RD;

Searches the first “1” in register RS (from MSB to LSB) and writes the bit position into the destination
register RD. The upper bits of RD are cleared. In case the content of RS is equal to $0000, RD will be
cleared and the carry flag will be set. This is used to distinguish a “1” in position 0 versus no “1” in the
whole RS register at all.

CCR Effects

N Z V C

0 ∆ 0 ∆

N: 0; cleared.
Z: Set if the result is $0000; cleared otherwise.
V: 0; cleared.
C: Set if RS = $0000(1); cleared otherwise.
1. Before executing the instruction

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BFFO RD, RS DYA 0 0 0 0 1 RD RS 1 0 0 0 0 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 401



Chapter 10 XGATE (S12XGATEV3)

BFINS Bit Field Insert BFINS
Operation

RS1[w:0] ⇒ RD[(w+o):o];
w = (RS2[7:4])
o = (RS2[3:0])

Extracts w+1 bits from register RS1 starting at position 0 and writes them into register RD starting at
position o. The remaining bits in RD are not affected. If (o+w) > 15 the upper bits are ignored. Using R0
as a RS1, this command can be used to clear bits.

15 7 4 3 0

W4 O4 RS2

15 3 0

RS1

Bit Field Insert

15 5 2 0

W4=3, O4=2 RD

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BFINS RD, RS1, RS2 TRI 0 1 1 0 1 RD RS1 RS2 1 1 P

MC9S12XE-Family Reference Manual  Rev. 1.25
402 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

BFINSI Bit Field Insert and Invert BFINSI
Operation

!RS1[w:0] ⇒ RD[w+o:o];
w = (RS2[7:4])
o = (RS2[3:0])

Extracts w+1 bits from register RS1 starting at position 0, inverts them and writes into register RD starting
at position o. The remaining bits in RD are not affected. If (o+w) > 15 the upper bits are ignored. Using
R0 as a RS1, this command can be used to set bits.

15 7 4 3 0

W4 O4 RS2

15 3 0

RS1

Inverted Bit Field Insert

15 5 2 0

W4=3, O4=2 RD

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BFINSI RD, RS1, RS2 TRI 0 1 1 1 0 RD RS1 RS2 1 1 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 403



Chapter 10 XGATE (S12XGATEV3)

BFINSX Bit Field Insert and XNOR BFINSX
Operation

!(RS1[w:0] ^ RD[w+o:o]) ⇒ RD[w+o:o];
w = (RS2[7:4])
o = (RS2[3:0])

Extracts w+1 bits from register RS1 starting at position 0, performs an XNOR with RD[w+o:o] and writes
the bits back to RD. The remaining bits in RD are not affected. If (o+w) > 15 the upper bits are ignored.
Using R0 as a RS1, this command can be used to toggle bits.

15 7 4 3 0

W4 O4 RS2

15 3 0

RS1

Bit Field Insert XNOR

15 5 2 0

W4=3, O4=2 RD

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BFINSX RD, RS1, RS2 TRI 0 1 1 1 1 RD RS1 RS2 1 1 P

MC9S12XE-Family Reference Manual  Rev. 1.25
404 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

BGE Branch if Greater than or Equal to Zero BGE
Operation

If N ^ V = 0, then PC + $0002 + (REL9 << 1) ⇒ PC

Branch instruction to compare signed numbers.

Branch if RS1 ≥ RS2:
SUB R0,RS1,RS2
BGE REL9

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BGE REL9 REL9 0 0 1 1 0 1 0 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 405



Chapter 10 XGATE (S12XGATEV3)

BGT Branch if Greater than Zero BGT
Operation

If Z | (N ^ V) = 0, then PC + $0002 + (REL9 << 1) ⇒ PC

Branch instruction to compare signed numbers.

Branch if RS1 > RS2:
SUB R0,RS1,RS2
BGT REL9

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BGT REL9 REL9 0 0 1 1 1 0 0 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
406 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

BHI Branch if Higher BHI
Operation

If C | Z = 0, then PC + $0002 + (REL9 << 1) ⇒ PC

Branch instruction to compare unsigned numbers.

Branch if RS1 > RS2:
SUB R0,RS1,RS2
BHI REL9

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BHI REL9 REL9 0 0 1 1 0 0 0 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 407



Chapter 10 XGATE (S12XGATEV3)

BHS Branch if Higher or Same
(Same as BCC) BHS

Operation

If C = 0, then PC + $0002 + (REL9 << 1) ⇒ PC

Branch instruction to compare unsigned numbers.

Branch if RS1 ≥ RS2:
SUB R0,RS1,RS2
BHS REL9

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BHS REL9 REL9 0 0 1 0 0 0 0 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
408 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

BITH Bit Test Immediate 8 bit Constant
(High Byte) BITH

Operation

RD.H & IMM8 ⇒ NONE

Performs a bit wise logical AND between the high byte of register RD and an immediate 8 bit constant.
Only the condition code flags get updated, but no result is written back.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the 8 bit result is $00; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BITH RD, #IMM8 IMM8 1 0 0 1 1 RD IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 409



Chapter 10 XGATE (S12XGATEV3)

BITL Bit Test Immediate 8 bit Constant
(Low Byte) BITL

Operation

RD.L & IMM8 ⇒ NONE

Performs a bit wise logical AND between the low byte of register RD and an immediate 8 bit constant.
Only the condition code flags get updated, but no result is written back.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 7 of the result is set; cleared otherwise.
Z: Set if the 8 bit result is $00; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BITL RD, #IMM8 IMM8 1 0 0 1 0 RD IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
410 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

BLE Branch if Less or Equal to Zero BLE
Operation

If Z | (N ^ V) = 1, then PC + $0002 + (REL9 << 1) ⇒ PC

Branch instruction to compare signed numbers.

Branch if RS1 ≤ RS2:
SUB R0,RS1,RS2
BLE REL9

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BLE REL9 REL9 0 0 1 1 1 0 1 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 411



Chapter 10 XGATE (S12XGATEV3)

BLO Branch if Carry Set
(Same as BCS) BLO

Operation

If C = 1, then PC + $0002 + (REL9 << 1) ⇒ PC

Branch instruction to compare unsigned numbers.

Branch if RS1 < RS2:
SUB R0,RS1,RS2
BLO REL9

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BLO REL9 REL9 0 0 1 0 0 0 1 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
412 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

BLS Branch if Lower or Same BLS
Operation

If C | Z = 1, then PC + $0002 + (REL9 << 1) ⇒ PC

Branch instruction to compare unsigned numbers.

Branch if RS1 ≤ RS2:
SUB R0,RS1,RS2
BLS REL9

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BLS REL9 REL9 0 0 1 1 0 0 1 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 413



Chapter 10 XGATE (S12XGATEV3)

BLT Branch if Lower than Zero BLT
Operation

If N ^ V = 1, then PC + $0002 + (REL9 << 1) ⇒ PC

Branch instruction to compare signed numbers.

Branch if RS1 < RS2:
SUB R0,RS1,RS2
BLT REL9

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BLT REL9 REL9 0 0 1 1 0 1 1 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
414 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

BMI Branch if Minus BMI
Operation

If N = 1, then PC + $0002 + (REL9 << 1) ⇒ PC

Tests the sign flag and branches if N = 1.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BMI REL9 REL9 0 0 1 0 1 0 1 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 415



Chapter 10 XGATE (S12XGATEV3)

BNE Branch if Not Equal BNE
Operation

If Z = 0, then PC + $0002 + (REL9 << 1) ⇒ PC

Tests the Zero flag and branches if Z = 0.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BNE REL9 REL9 0 0 1 0 0 1 0 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
416 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

BPL Branch if Plus BPL
Operation

If N = 0, then PC + $0002 + (REL9 << 1) ⇒ PC

Tests the Sign flag and branches if N = 0.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BPL REL9 REL9 0 0 1 0 1 0 0 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 417



Chapter 10 XGATE (S12XGATEV3)

BRA Branch Always BRA
Operation

PC + $0002 + (REL10 << 1) ⇒ PC

Branches always.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BRA REL10 REL10 0 0 1 1 1 1 REL10 PP

MC9S12XE-Family Reference Manual  Rev. 1.25
418 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

BRK Break BRK
Operation

Put XGATE into Debug Mode (see Section ********.1, “Entering Debug Mode”) and signals a software
breakpoint to the S12X_DBG module (see section 4.9 of the S12X_DBG Section).

NOTE
It is not possible to single step over a BRK instruction. This instruction does
not advance the program counter.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BRK INH 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 PAff

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 419



Chapter 10 XGATE (S12XGATEV3)

BVC Branch if Overflow Cleared BVC
Operation

If V = 0, then PC + $0002 + (REL9 << 1) ⇒ PC

Tests the Overflow flag and branches if V = 0.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BVC REL9 REL9 0 0 1 0 1 1 0 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
420 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

BVS Branch if Overflow Set BVS
Operation

If V = 1, then PC + $0002 + (REL9 << 1) ⇒ PC

Tests the Overflow flag and branches if V = 1.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

BVS REL9 REL9 0 0 1 0 1 1 1 REL9 PP/P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 421



Chapter 10 XGATE (S12XGATEV3)

CMP Compare CMP
Operation

RS1 – RS2 ⇒ NONE (translates to SUB R0, RS1, RS2)
RD – IMM16 ⇒ NONE (translates to CMPL RD, #IMM16[7:0]; CPCH RD, #IMM16[15:8])

Subtracts two 16 bit values and discards the result.

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RS1[15] & RS2[15] & result[15] | RS1[15] & RS2[15] & result[15]
RD[15] & IMM16[15] & result[15] | RD[15] & IMM16[15] & result[15]

C: Set if there is a carry from the bit 15 of the result; cleared otherwise.
RS1[15] & RS2[15] | RS1[15] & result[15] | RS2[15] & result[15]
RD[15] & IMM16[15] | RD[15] & result[15] | IMM16[15] & result[15]

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

CMP RS1, RS2 TRI 0 0 0 1 1 0 0 0 RS1 RS2 0 0 P

CMP RS, #IMM16 IMM8 1 1 0 1 0 RS IMM16[7:0] P

IMM8 1 1 0 1 1 RS IMM16[15:8] P

MC9S12XE-Family Reference Manual  Rev. 1.25
422 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

CMPL Compare Immediate 8 bit Constant
(Low Byte) CMPL

Operation

RS.L – IMM8 ⇒ NONE, only condition code flags get updated

Subtracts the 8 bit constant IMM8 contained in the instruction code from the low byte of the source register
RS.L using binary subtraction and updates the condition code register accordingly.

Remark: There is no equivalent operation using triadic addressing. Comparing the values of two registers
can be performed by using the subtract instruction with R0 as destination register.

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 7 of the result is set; cleared otherwise.
Z: Set if the 8 bit result is $00; cleared otherwise.
V: Set if a two´s complement overflow resulted from the 8 bit operation; cleared otherwise.

RS[7] & IMM8[7] & result[7] | RS[7] & IMM8[7] & result[7]
C: Set if there is a carry from the Bit 7 to Bit 8 of the result; cleared otherwise.

RS[7] & IMM8[7] | RS[7] & result[7] | IMM8[7] & result[7]

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

CMPL RS, #IMM8 IMM8 1 1 0 1 0 RS IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 423



Chapter 10 XGATE (S12XGATEV3)

COM One’s Complement COM
Operation

~RS ⇒ RD (translates to XNOR RD, R0, RS)
~RD ⇒ RD (translates to XNOR RD, R0, RD)

Performs a one’s complement on a general purpose register.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

COM RD, RS TRI 0 0 0 1 0 RD 0 0 0 RS 1 1 P

COM RD TRI 0 0 0 1 0 RD 0 0 0 RD 1 1 P

MC9S12XE-Family Reference Manual  Rev. 1.25
424 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

CPC Compare with Carry CPC
Operation

RS1 – RS2 - C ⇒ NONE (translates to SBC R0, RS1, RS2)

Subtracts the carry bit and the content of register RS2 from the content of register RS1 using binary
subtraction and discards the result.

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RS1[15] & RS2[15] & result[15] | RS1[15] & RS2[15] & result[15]
C: Set if there is a carry from the bit 15 of the result; cleared otherwise.

RS1[15] & RS2[15] | RS1[15] & result[15] | RS2[15] & result[15]

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

CPC RS1, RS2 TRI 0 0 0 1 1 0 0 0 RS1 RS2 0 1 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 425



Chapter 10 XGATE (S12XGATEV3)

CPCH Compare Immediate 8 bit Constant with
Carry (High Byte) CPCH

Operation

RS.H - IMM8 - C ⇒ NONE, only condition code flags get updated

Subtracts the carry bit and the 8 bit constant IMM8 contained in the instruction code from the high byte of
the source register RD using binary subtraction and updates the condition code register accordingly. The
carry bit and Zero bits are taken into account to allow a 16 bit compare in the form of

CMPL R2,#LOWBYTE
CPCH R2,#HIGHBYTE
BCC ; branch condition

Remark: There is no equivalent operation using triadic addressing. Comparing the values of two registers
can be performed by using the subtract instruction with R0 as destination register.

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $00 and Z was set before this operation; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RS[15] & IMM8[7] & result[15] | RS[15] & IMM8[7] & result[15]
C: Set if there is a carry from the bit 15 of the result; cleared otherwise.

RS[15] & IMM8[7] | RS[15] & result[15] | IMM8[7] & result[15]

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

CPCH RD, #IMM8 IMM8 1 1 0 1 1 RS IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
426 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

CSEM Clear Semaphore CSEM
Operation

Unlocks a semaphore that was locked by the RISC core.

In monadic address mode, bits RS[2:0] select the semaphore to be cleared.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

CSEM #IMM3 IMM3 0 0 0 0 0 IMM3 1 1 1 1 0 0 0 0 PA

CSEM RS MON 0 0 0 0 0 RS 1 1 1 1 0 0 0 1 PA

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 427



Chapter 10 XGATE (S12XGATEV3)

CSL Logical Shift Left with Carry CSL
Operation

n

C RD C C C C

n bits

n = RS or IMM4

Shifts the bits in register RD n positions to the left. The lower n bits of the register RD become filled with
the carry flag. The carry flag will be updated to the bit contained in RD[16-n] before the shift for n > 0.
n can range from 0 to 16.

In immediate address mode, n is determined by the operand IMM4. n is considered to be 16 if IMM4 is
equal to 0.

In dyadic address mode, n is determined by the content of RS. n is considered to be 16 if the content of RS
is greater than 15.

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RD[15]old ^ RD[15]new
C: Set if n > 0 and RD[16-n] = 1; if n = 0 unaffected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

CSL RD, #IMM4 IMM4 0 0 0 0 1 RD IMM4 1 0 1 0 P

CSL RD, RS DYA 0 0 0 0 1 RD RS 1 0 0 1 0 P

MC9S12XE-Family Reference Manual  Rev. 1.25
428 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

CSR Logical Shift Right with Carry CSR
Operation

n

C C C C RD C

n bits

n = RS or IMM4

Shifts the bits in register RD n positions to the right. The higher n bits of the register RD become filled
with the carry flag. The carry flag will be updated to the bit contained in RD[n-1] before the shift for n > 0.
n can range from 0 to 16.

In immediate address mode, n is determined by the operand IMM4. n is considered to be 16 if IMM4 is
equal to 0.

In dyadic address mode, n is determined by the content of RS. n is considered to be 16 if the content of RS
is greater than 15.

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RD[15]old ^ RD[15]new
C: Set if n > 0 and RD[n-1] = 1; if n = 0 unaffected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

CSR RD, #IMM4 IMM4 0 0 0 0 1 RD IMM4 1 0 1 1 P

CSR RD, RS DYA 0 0 0 0 1 RD RS 1 0 0 1 1 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 429



Chapter 10 XGATE (S12XGATEV3)

JAL Jump and Link JAL
Operation

PC + $0002 ⇒ RD; RD ⇒ PC

Jumps to the address stored in RD and saves the return address in RD.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

JAL RD MON 0 0 0 0 0 RD 1 1 1 1 0 1 1 0 PP

MC9S12XE-Family Reference Manual  Rev. 1.25
430 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

LDB Load Byte from Memory
(Low Byte) LDB

Operation

M[RB, #OFFS5] ⇒ RD.L; $00 ⇒ RD.H
M[RB, RI] ⇒ RD.L; $00 ⇒ RD.H
M[RB, RI] ⇒ RD.L; $00 ⇒ RD.H; RI+1 ⇒ RI;1
RI-1 ⇒ RI; M[RS, RI] ⇒ RD.L; $00 ⇒ RD.H

Loads a byte from memory into the low byte of register RD. The high byte is cleared.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

LDB RD, (RB, #OFFS5) IDO5 0 1 0 0 0 RD RB OFFS5 Pr

LDB RD, (RS, RI) IDR 0 1 1 0 0 RD RB RI 0 0 Pr

LDB RD, (RS, RI+) IDR+ 0 1 1 0 0 RD RB RI 0 1 Pr

LDB RD, (RS, -RI) -IDR 0 1 1 0 0 RD RB RI 1 0 Pr

1. If the same general purpose register is used as index (RI) and destination register (RD), the content of the register will not
be incremented after the data move: M[RB, RI] ⇒ RD.L; $00 ⇒ RD.H

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 431



Chapter 10 XGATE (S12XGATEV3)

LDH Load Immediate 8 bit Constant
(High Byte) LDH

Operation

IMM8 ⇒ RD.H;

Loads an 8 bit immediate constant into the high byte of register RD. The low byte is not affected.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

LDH RD, #IMM8 IMM8 1 1 1 1 1 RD IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
432 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

LDL Load Immediate 8 bit Constant
(Low Byte) LDL

Operation

IMM8 ⇒ RD.L; $00 ⇒ RD.H

Loads an 8 bit immediate constant into the low byte of register RD. The high byte is cleared.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

LDL RD, #IMM8 IMM8 1 1 1 1 0 RD IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 433



Chapter 10 XGATE (S12XGATEV3)

LDW Load Word from Memory LDW
Operation

M[RB, #OFFS5] ⇒ RD
M[RB, RI] ⇒ RD
M[RB, RI] ⇒ RD; RI+2 ⇒ RI1

RI-2 ⇒ RI; M[RS, RI] ⇒ RD
IMM16 ⇒ RD (translates to LDL RD, #IMM16[7:0]; LDH RD, #IMM16[15:8])

Loads a 16 bit value into the register RD.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

LDW RD, (RB, #OFFS5) IDO5 0 1 0 0 1 RD RB OFFS5 PR

LDW RD, (RB, RI) IDR 0 1 1 0 1 RD RB RI 0 0 PR

LDW RD, (RB, RI+) IDR+ 0 1 1 0 1 RD RB RI 0 1 PR

LDW RD, (RB, -RI) -IDR 0 1 1 0 1 RD RB RI 1 0 PR

LDW RD, #IMM16 IMM8 1 1 1 1 0 RD IMM16[7:0] P

IMM8 1 1 1 1 1 RD IMM16[15:8] P

1. If the same general purpose register is used as index (RI) and destination register (RD), the content of the register will not be
incremented after the data move: M[RB, RI] ⇒ RD

MC9S12XE-Family Reference Manual  Rev. 1.25
434 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

LSL Logical Shift Left LSL
Operation

n

C RD 0 0 0 0

n bits

n = RS or IMM4

Shifts the bits in register RD n positions to the left. The lower n bits of the register RD become filled with
zeros. The carry flag will be updated to the bit contained in RD[16-n] before the shift for n > 0.

n can range from 0 to 16.

In immediate address mode, n is determined by the operand IMM4. n is considered to be 16 in IMM4 is
equal to 0.

In dyadic address mode, n is determined by the content of RS. n is considered to be 16 if the content of RS
is greater than 15.

CCR Effects

N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RD[15]old ^ RD[15]new
C: Set if n > 0 and RD[16-n] = 1; if n = 0 unaffected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

LSL RD, #IMM4 IMM4 0 0 0 0 1 RD IMM4 1 1 0 0 P

LSL RD, RS DYA 0 0 0 0 1 RD RS 1 0 1 0 0 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 435



Chapter 10 XGATE (S12XGATEV3)

LSR Logical Shift Right LSR
Operation

n

0 0 0 0 RD C

n bits

n = RS or IMM4

Shifts the bits in register RD n positions to the right. The higher n bits of the register RD become filled
with zeros. The carry flag will be updated to the bit contained in RD[n-1] before the shift for n > 0.

n can range from 0 to 16.

In immediate address mode, n is determined by the operand IMM4. n is considered to be 16 in IMM4 is
equal to 0.

In dyadic address mode, n is determined by the content of RS. n is considered to be 16 if the content of RS
is greater than 15.

CCR Effects

N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RD[15]old ^ RD[15]new
C: Set if n > 0 and RD[n-1] = 1; if n = 0 unaffected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

LSR RD, #IMM4 IMM4 0 0 0 0 1 RD IMM4 1 1 0 1 P

LSR RD, RS DYA 0 0 0 0 1 RD RS 1 0 1 0 1 P

MC9S12XE-Family Reference Manual  Rev. 1.25
436 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

MOV Move Register Content MOV
Operation

RS ⇒ RD (translates to OR RD, R0, RS)

Copies the content of RS to RD.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

MOV RD, RS TRI 0 0 0 1 0 RD 0 0 0 RS 1 0 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 437



Chapter 10 XGATE (S12XGATEV3)

NEG Two’s Complement NEG
Operation

–RS ⇒ RD (translates to SUB RD, R0, RS)
–RD ⇒ RD (translates to SUB RD, R0, RD)

Performs a two’s complement on a general purpose register.

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

 RS[15] & RD[15]new
C: Set if there is a carry from the bit 15 of the result; cleared otherwise

RS[15] | RD[15]new

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

NEG RD, RS TRI 0 0 0 1 1 RD 0 0 0 RS 0 0 P

NEG RD TRI 0 0 0 1 1 RD 0 0 0 RD 0 0 P

MC9S12XE-Family Reference Manual  Rev. 1.25
438 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

NOP No Operation NOP
Operation

No Operation for one cycle.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

NOP INH 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 439



Chapter 10 XGATE (S12XGATEV3)

OR Logical OR OR
Operation

RS1 | RS2 ⇒ RD
RD | IMM16⇒ RD (translates to ORL RD, #IMM16[7:0]; ORH RD, #IMM16[15:8]

Performs a bit wise logical OR between two 16 bit values and stores the result in the destination
register RD.

NOTE
When using immediate addressing mode (OR RD, #IMM16), the Z-flag of
the first instruction (ORL RD, #IMM16[7:0]) is not considered by the
second instruction (ORH RD, #IMM16[15:8]).
⇒ Don’t rely on the Z-Flag.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.

Refer to ORH instruction for #IMM16 operations.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

OR RD, RS1, RS2 TRI 0 0 0 1 0 RD RS1 RS2 1 0 P

OR RD, #IMM16 IMM8 1 0 1 0 0 RD IMM16[7:0] P

IMM8 1 0 1 0 1 RD IMM16[15:8] P

MC9S12XE-Family Reference Manual  Rev. 1.25
440 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

ORH Logical OR Immediate 8 bit Constant
(High Byte) ORH

Operation

RD.H | IMM8 ⇒ RD.H

Performs a bit wise logical OR between the high byte of register RD and an immediate 8 bit constant and
stores the result in the destination register RD.H. The low byte of RD is not affected.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the 8 bit result is $00; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

ORH RD, #IMM8 IMM8 1 0 1 0 1 RD IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 441



Chapter 10 XGATE (S12XGATEV3)

ORL Logical OR Immediate 8 bit Constant
(Low Byte) ORL

Operation

RD.L | IMM8 ⇒ RD.L

Performs a bit wise logical OR between the low byte of register RD and an immediate 8 bit constant and
stores the result in the destination register RD.L. The high byte of RD is not affected.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 7 of the result is set; cleared otherwise.
Z: Set if the 8 bit result is $00; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

ORL RD, #IMM8 IMM8 1 0 1 0 0 RD IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
442 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

PAR Calculate Parity PAR
Operation

Calculates the number of ones in the register RD. The Carry flag will be set if the number is odd, otherwise
it will be cleared.

CCR Effects
N Z V C

0 ∆ 0 ∆

N: 0; cleared.
Z: Set if RD is $0000; cleared otherwise.
V: 0; cleared.
C: Set if the number of ones in the register RD is odd; cleared otherwise.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

PAR, RD MON 0 0 0 0 0 RD 1 1 1 1 0 1 0 1 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 443



Chapter 10 XGATE (S12XGATEV3)

ROL Rotate Left ROL
Operation

RD

n bits

n = RS or IMM4

Rotates the bits in register RD n positions to the left. The lower n bits of the register RD are filled with the
upper n bits. Two source forms are available. In the first form, the parameter n is contained in the
instruction code as an immediate operand. In the second form, the parameter is contained in the lower bits
of the source register RS[3:0]. All other bits in RS are ignored. If n is zero, no shift will take place and the
register RD will be unaffected; however, the condition code flags will be updated.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

ROL RD, #IMM4 IMM4 0 0 0 0 1 RD IMM4 1 1 1 0 P

ROL RD, RS DYA 0 0 0 0 1 RD RS 1 0 1 1 0 P

MC9S12XE-Family Reference Manual  Rev. 1.25
444 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

ROR Rotate Right ROR
Operation

RD

n bits

n = RS or IMM4

Rotates the bits in register RD n positions to the right. The upper n bits of the register RD are filled with
the lower n bits. Two source forms are available. In the first form, the parameter n is contained in the
instruction code as an immediate operand. In the second form, the parameter is contained in the lower bits
of the source register RS[3:0]. All other bits in RS are ignored. If n is zero no shift will take place and the
register RD will be unaffected; however, the condition code flags will be updated.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

ROR RD, #IMM4 IMM4 0 0 0 0 1 RD IMM4 1 1 1 1 P

ROR RD, RS DYA 0 0 0 0 1 RD RS 1 0 1 1 1 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 445



Chapter 10 XGATE (S12XGATEV3)

RTS Return to Scheduler RTS
Operation

Terminates the current thread of program execution.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

RTS INH 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 PA

MC9S12XE-Family Reference Manual  Rev. 1.25
446 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

SBC Subtract with Carry SBC
Operation

RS1 - RS2 - C ⇒ RD

Subtracts the content of register RS2 and the value of the Carry bit from the content of register RS1 using
binary subtraction and stores the result in the destination register RD. Also the zero flag is carried forward
from the previous operation allowing 32 and more bit subtractions.

Example:
SUB R6,R4,R2
SBC R7,R5,R3 ; R7:R6 = R5:R4 - R3:R2
BCC ; conditional branch on 32 bit subtraction

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000 and Z was set before this operation; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RS1[15] & RS2[15] & RD[15]new | RS1[15] & RS2[15] & RD[15]new
C: Set if there is a carry from bit 15 of the result; cleared otherwise.

RS1[15] & RS2[15] | RS1[15] & RD[15]new | RS2[15] & RD[15]new

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

SBC RD, RS1, RS2 TRI 0 0 0 1 1 RD RS1 RS2 0 1 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 447



Chapter 10 XGATE (S12XGATEV3)

SEX Sign Extend Byte to Word SEX
Operation

The result in RD is the 16 bit sign extended representation of the original two’s complement number in the
low byte of RD.L.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

SEX RD MON 0 0 0 0 0 RD 1 1 1 1 0 1 0 0 P

MC9S12XE-Family Reference Manual  Rev. 1.25
448 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

SIF Set Interrupt Flag SIF
Operation

Sets the interrupt flag of an XGATE channel (XGIF). This instruction supports two source forms. If
inherent address mode is used, then the interrupt flag of the current channel (XGCHID) will be set. If the
monadic address form is used, the interrupt flag associated with the channel id number contained in
RS[6:0] is set. The content of RS[15:7] is ignored.

NOTE
Interrupt flags of reserved channels (see Device User Guide) can’t be set.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

SIF INH 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 PA

SIF RS MON 0 0 0 0 0 RS 1 1 1 1 0 1 1 1 PA

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 449



Chapter 10 XGATE (S12XGATEV3)

SSEM Set Semaphore SSEM
Operation

Attempts to set a semaphore. The state of the semaphore will be stored in the Carry-Flag:
1 = Semaphore is locked by the RISC core
0 = Semaphore is locked by the S12X_CPU

In monadic address mode, bits RS[2:0] select the semaphore to be set.

CCR Effects
N Z V C

— — — ∆

N: Not affected.
Z: Not affected.
V: Not affected.
C: Set if semaphore is locked by the RISC core; cleared otherwise.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

SSEM #IMM3 IMM3 0 0 0 0 0 IMM3 1 1 1 1 0 0 1 0 PA

SSEM RS MON 0 0 0 0 0 RS 1 1 1 1 0 0 1 1 PA

MC9S12XE-Family Reference Manual  Rev. 1.25
450 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

STB Store Byte to Memory
(Low Byte) STB

Operation

RS.L ⇒ M[RB, #OFFS5]
RS.L ⇒ M[RB, RI]
RS.L ⇒ M[RB, RI]; RI+1 ⇒ RI;
RI–1 ⇒ RI; RS.L ⇒ M[RB, RI]1

Stores the low byte of register RS to memory.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

STB RS, (RB, #OFFS5), IDO5 0 1 0 1 0 RS RB OFFS5 Pw

STB RS, (RB, RI) IDR 0 1 1 1 0 RS RB RI 0 0 Pw

STB RS, (RB, RI+) IDR+ 0 1 1 1 0 RS RB RI 0 1 Pw

STB RS, (RB, -RI) -IDR 0 1 1 1 0 RS RB RI 1 0 Pw

1. If the same general purpose register is used as index (RI) and source register (RS), the unmodified content of the source
register is written to the memory: RS.L ⇒ M[RB, RS-1]; RS-1 ⇒ RS

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 451



Chapter 10 XGATE (S12XGATEV3)

STW Store Word to Memory STW
Operation

RS ⇒ M[RB, #OFFS5]
RS ⇒ M[RB, RI]
RS ⇒ M[RB, RI]; RI+2 ⇒ RI;
RI–2 ⇒ RI; RS ⇒ M[RB, RI]1

Stores the content of register RS to memory.

CCR Effects
N Z V C

— — — —

N: Not affected.
Z: Not affected.
V: Not affected.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

STW RS, (RB, #OFFS5) IDO5 0 1 0 1 1 RS RB OFFS5 PW

STW RS, (RB, RI) IDR 0 1 1 1 1 RS RB RI 0 0 PW

STW RS, (RB, RI+) IDR+ 0 1 1 1 1 RS RB RI 0 1 PW

STW RS, (RB, -RI) -IDR 0 1 1 1 1 RS RB RI 1 0 PW

1. If the same general purpose register is used as index (RI) and source register (RS), the unmodified content of the source
register is written to the memory: RS ⇒ M[RB, RS–2]; RS–2 ⇒ RS

MC9S12XE-Family Reference Manual  Rev. 1.25
452 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

SUB Subtract without Carry SUB
Operation

RS1 – RS2 ⇒ RD
RD − IMM16 ⇒ RD (translates to SUBL RD, #IMM16[7:0]; SUBH RD, #IMM16{15:8])

Subtracts two 16 bit values and stores the result in the destination register RD.

NOTE
When using immediate addressing mode (SUB RD, #IMM16), the V-flag
and the C-Flag of the first instruction (SUBL RD, #IMM16[7:0]) are not
considered by the second instruction (SUBH RD, #IMM16[15:8]).
⇒ Don’t rely on the V-Flag if RD - IMM16[7:0] < −215.
⇒ Don’t rely on the C-Flag if RD < IMM16[7:0].

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RS1[15] & RS2[15] & RD[15]new | RS1[15] & RS2[15] & RD[15]new
Refer to SUBH instruction for #IMM16 operations.

C: Set if there is a carry from the bit 15 of the result; cleared otherwise.
RS1[15] & RS2[15] | RS1[15] & RD[15]new | RS2[15] & RD[15]new
Refer to SUBH instruction for #IMM16 operations.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

SUB RD, RS1, RS2 TRI 0 0 0 1 1 RD RS1 RS2 0 0 P

SUB RD, #IMM16 IMM8 1 1 0 0 0 RD IMM16[7:0] P

IMM8 1 1 0 0 1 RD IMM16[15:8] P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 453



Chapter 10 XGATE (S12XGATEV3)

SUBH Subtract Immediate 8 bit Constant
(High Byte) SUBH

Operation

RD – IMM8:$00 ⇒ RD

Subtracts a signed immediate 8 bit constant from the content of high byte of register RD and using binary
subtraction and stores the result in the high byte of destination register RD. This instruction can be used
after an SUBL for a 16 bit immediate subtraction.

Example:
SUBL R2,#LOWBYTE
SUBH R2,#HIGHBYTE ; R2 = R2 - 16 bit immediate

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RD[15]old & IMM8[7] & RD[15]new | RD[15]old & IMM8[7] & RD[15]new
C: Set if there is a carry from the bit 15 of the result; cleared otherwise.

RD[15]old & IMM8[7] | RD[15]old & RD[15]new | IMM8[7] & RD[15]new

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

SUBH RD, #IMM8 IMM8 1 1 0 0 1 RD IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
454 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

SUBL Subtract Immediate 8 bit Constant
(Low Byte) SUBL

Operation

RD – $00:IMM8 ⇒ RD

Subtracts an immediate 8 bit constant from the content of register RD using binary subtraction and stores
the result in the destination register RD.

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the 8 bit operation; cleared otherwise.

RD[15]old & RD[15]new
C: Set if there is a carry from the bit 15 of the result; cleared otherwise.

RD[15]old & RD[15]new

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

SUBL RD, #IMM8 IMM8 1 1 0 0 0 RD IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 455



Chapter 10 XGATE (S12XGATEV3)

TFR Transfer from and to Special Registers TFR
Operation

TFR RD,CCR: CCR ⇒ RD[3:0]; 0 ⇒ RD[15:4]
TFR CCR,RD: RD[3:0] ⇒ CCR
TFR RD,PC: PC+4 ⇒ RD

Transfers the content of one RISC core register to another.
The TFR RD,PC instruction can be used to implement relative subroutine calls.

Example:
TFR R7,PC ;Return address (RETADDR) is stored in R7
BRA SUBR ;Relative branch to subroutine (SUBR)

RETADDR ...

SUBR ...
JAL R7 ;Jump to return address (RETADDR)

CCR Effects
TFR RD,CCR, TFR RD,PC: TFR CCR,RS:

N Z V C N Z V C

— — — — ∆ ∆ ∆ ∆

N: Not affected. N: RS[3].
Z: Not affected. Z: RS[2].
V: Not affected. V: RS[1].
C: Not affected. C: RS[0].

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

TFR RD,CCR CCR ⇒ RD MON 0 0 0 0 0 RD 1 1 1 1 1 0 0 0 P

TFR CCR,RS RS ⇒ CCR MON 0 0 0 0 0 RS 1 1 1 1 1 0 0 1 P

TFR RD,PCPC+4 ⇒ RD MON 0 0 0 0 0 RD 1 1 1 1 1 0 1 0 P

MC9S12XE-Family Reference Manual  Rev. 1.25
456 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

TST Test Register TST
Operation

RS – 0 ⇒ NONE (translates to SUB R0, RS, R0)

Subtracts zero from the content of register RS using binary subtraction and discards the result.

CCR Effects
N Z V C

∆ ∆ ∆ ∆

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.
V: Set if a two´s complement overflow resulted from the operation; cleared otherwise.

RS[15] & result[15]
C: Set if there is a carry from the bit 15 of the result; cleared otherwise.

RS1[15] & result[15]

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

TST RS TRI 0 0 0 1 1 0 0 0 RS1 0 0 0 0 0 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 457



Chapter 10 XGATE (S12XGATEV3)

XNOR Logical Exclusive NOR XNOR
Operation

~(RS1 ^ RS2) ⇒ RD
~(RD ^ IMM16)⇒ RD
(translates to XNOR RD, #IMM16{15:8]; XNOR RD, #IMM16[7:0])

Performs a bit wise logical exclusive NOR between two 16 bit values and stores the result in the destination
register RD.

Remark: Using R0 as a source registers will calculate the one’s complement of the other source register.
Using R0 as both source operands will fill RD with $FFFF.

NOTE
When using immediate addressing mode (XNOR RD, #IMM16), the Z-flag
of the first instruction (XNORL RD, #IMM16[7:0]) is not considered by the
second instruction (XNORH RD, #IMM16[15:8]).
⇒ Don’t rely on the Z-Flag.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the result is $0000; cleared otherwise.

Refer to XNORH instruction for #IMM16 operations.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

XNOR RD, RS1, RS2 TRI 0 0 0 1 0 RD RS1 RS2 1 1 P

XNOR RD, #IMM16 IMM8 1 0 1 1 0 RD IMM16[7:0] P

IMM8 1 0 1 1 1 RD IMM16[15:8] P

MC9S12XE-Family Reference Manual  Rev. 1.25
458 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

XNORH Logical Exclusive NOR Immediate
8 bit Constant (High Byte) XNORH

Operation

~(RD.H ^ IMM8) ⇒ RD.H

Performs a bit wise logical exclusive NOR between the high byte of register RD and an immediate 8 bit
constant and stores the result in the destination register RD.H. The low byte of RD is not affected.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 15 of the result is set; cleared otherwise.
Z: Set if the 8 bit result is $00; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

XNORH RD, #IMM8 IMM8 1 0 1 1 1 RD IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 459



Chapter 10 XGATE (S12XGATEV3)

XNORL Logical Exclusive NOR Immediate
8 bit Constant (Low Byte) XNORL

Operation

~(RD.L ^ IMM8) ⇒ RD.L

Performs a bit wise logical exclusive NOR between the low byte of register RD and an immediate 8 bit
constant and stores the result in the destination register RD.L. The high byte of RD is not affected.

CCR Effects
N Z V C

∆ ∆ 0 —

N: Set if bit 7 of the result is set; cleared otherwise.
Z: Set if the 8 bit result is $00; cleared otherwise.
V: 0; cleared.
C: Not affected.

Code and CPU Cycles

Address
Source Form Machine Code Cycles

Mode

XNORL RD, #IMM8 IMM8 1 0 1 1 0 RD IMM8 P

MC9S12XE-Family Reference Manual  Rev. 1.25
460 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

10.8.6 Instruction Coding
Table 10-24 summarizes all XGATE instructions in the order of their machine coding.

Table 10-24. Instruction Set Summary (Sheet 1 of 3)
Functionality 15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0

Return to Scheduler and Others
BRK 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
NOP 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0
RTS 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0
SIF 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0

Semaphore Instructions
CSEM IMM3 0 0 0 0 0 IMM3 1 1 1 1 0 0 0 0
CSEM RS 0 0 0 0 0 RS 1 1 1 1 0 0 0 1
SSEM IMM3 0 0 0 0 0 IMM3 1 1 1 1 0 0 1 0
SSEM RS 0 0 0 0 0 RS 1 1 1 1 0 0 1 1

Single Register Instructions
SEX RD 0 0 0 0 0 RD 1 1 1 1 0 1 0 0
PAR RD 0 0 0 0 0 RD 1 1 1 1 0 1 0 1
JAL RD 0 0 0 0 0 RD 1 1 1 1 0 1 1 0
SIF RS 0 0 0 0 0 RS 1 1 1 1 0 1 1 1

Special Move instructions
TFR RD,CCR 0 0 0 0 0 RD 1 1 1 1 1 0 0 0
TFR CCR,RS 0 0 0 0 0 RS 1 1 1 1 1 0 0 1
TFR RD,PC 0 0 0 0 0 RD 1 1 1 1 1 0 1 0

Shift instructions Dyadic
BFFO RD, RS 0 0 0 0 1 RD RS 1 0 0 0 0
ASR RD, RS 0 0 0 0 1 RD RS 1 0 0 0 1
CSL RD, RS 0 0 0 0 1 RD RS 1 0 0 1 0
CSR RD, RS 0 0 0 0 1 RD RS 1 0 0 1 1
LSL RD, RS 0 0 0 0 1 RD RS 1 0 1 0 0
LSR RD, RS 0 0 0 0 1 RD RS 1 0 1 0 1
ROL RD, RS 0 0 0 0 1 RD RS 1 0 1 1 0
ROR RD, RS 0 0 0 0 1 RD RS 1 0 1 1 1

Shift instructions immediate
ASR RD, #IMM4 0 0 0 0 1 RD IMM4 1 0 0 1
CSL RD, #IMM4 0 0 0 0 1 RD IMM4 1 0 1 0
CSR RD, #IMM4 0 0 0 0 1 RD IMM4 1 0 1 1
LSL RD, #IMM4 0 0 0 0 1 RD IMM4 1 1 0 0
LSR RD, #IMM4 0 0 0 0 1 RD IMM4 1 1 0 1
ROL RD, #IMM4 0 0 0 0 1 RD IMM4 1 1 1 0
ROR RD, #IMM4 0 0 0 0 1 RD IMM4 1 1 1 1

Logical Triadic
AND RD, RS1, RS2 0 0 0 1 0 RD RS1 RS2 0 0
OR RD, RS1, RS2 0 0 0 1 0 RD RS1 RS2 1 0
XNOR RD, RS1, RS2 0 0 0 1 0 RD RS1 RS2 1 1

Arithmetic Triadic For compare use SUB R0,Rs1,Rs2
SUB RD, RS1, RS2 0 0 0 1 1 RD RS1 RS2 0 0
SBC RD, RS1, RS2 0 0 0 1 1 RD RS1 RS2 0 1
ADD RD, RS1, RS2 0 0 0 1 1 RD RS1 RS2 1 0
ADC RD, RS1, RS2 0 0 0 1 1 RD RS1 RS2 1 1

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 461



Chapter 10 XGATE (S12XGATEV3)

Table 10-24. Instruction Set Summary (Sheet 2 of 3)
Functionality 15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0

Branches
BCC REL9 0 0 1 0 0 0 0 REL9
BCS REL9 0 0 1 0 0 0 1 REL9
BNE REL9 0 0 1 0 0 1 0 REL9
BEQ REL9 0 0 1 0 0 1 1 REL9
BPL REL9 0 0 1 0 1 0 0 REL9
BMI REL9 0 0 1 0 1 0 1 REL9
BVC REL9 0 0 1 0 1 1 0 REL9
BVS REL9 0 0 1 0 1 1 1 REL9
BHI REL9 0 0 1 1 0 0 0 REL9
BLS REL9 0 0 1 1 0 0 1 REL9
BGE REL9 0 0 1 1 0 1 0 REL9
BLT REL9 0 0 1 1 0 1 1 REL9
BGT REL9 0 0 1 1 1 0 0 REL9
BLE REL9 0 0 1 1 1 0 1 REL9
BRA REL10 0 0 1 1 1 1 REL10

Load and Store Instructions
LDB RD, (RB, #OFFS5) 0 1 0 0 0 RD RB OFFS5
LDW RD, (RB, #OFFS5) 0 1 0 0 1 RD RB OFFS5
STB RS, (RB, #OFFS5) 0 1 0 1 0 RS RB OFFS5
STW RS, (RB, #OFFS5) 0 1 0 1 1 RS RB OFFS5
LDB RD, (RB, RI) 0 1 1 0 0 RD RB RI 0 0
LDW RD, (RB, RI) 0 1 1 0 1 RD RB RI 0 0
STB RS, (RB, RI) 0 1 1 1 0 RS RB RI 0 0
STW RS, (RB, RI) 0 1 1 1 1 RS RB RI 0 0
LDB RD, (RB, RI+) 0 1 1 0 0 RD RB RI 0 1
LDW RD, (RB, RI+) 0 1 1 0 1 RD RB RI 0 1
STB RS, (RB, RI+) 0 1 1 1 0 RS RB RI 0 1
STW RS, (RB, RI+) 0 1 1 1 1 RS RB RI 0 1
LDB RD, (RB, –RI) 0 1 1 0 0 RD RB RI 1 0
LDW RD, (RB, –RI) 0 1 1 0 1 RD RB RI 1 0
STB RS, (RB, –RI) 0 1 1 1 0 RS RB RI 1 0
STW RS, (RB, –RI) 0 1 1 1 1 RS RB RI 1 0

Bit Field Instructions
BFEXT RD, RS1, RS2 0 1 1 0 0 RD RS1 RS2 1 1
BFINS RD, RS1, RS2 0 1 1 0 1 RD RS1 RS2 1 1
BFINSI RD, RS1, RS2 0 1 1 1 0 RD RS1 RS2 1 1
BFINSX RD, RS1, RS2 0 1 1 1 1 RD RS1 RS2 1 1

Logic Immediate Instructions
ANDL RD, #IMM8 1 0 0 0 0 RD IMM8
ANDH RD, #IMM8 1 0 0 0 1 RD IMM8
BITL RD, #IMM8 1 0 0 1 0 RD IMM8
BITH RD, #IMM8 1 0 0 1 1 RD IMM8
ORL RD, #IMM8 1 0 1 0 0 RD IMM8
ORH RD, #IMM8 1 0 1 0 1 RD IMM8
XNORL RD, #IMM8 1 0 1 1 0 RD IMM8
XNORH RD, #IMM8 1 0 1 1 1 RD IMM8

MC9S12XE-Family Reference Manual  Rev. 1.25
462 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

Table 10-24. Instruction Set Summary (Sheet 3 of 3)
Functionality 15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0

Arithmetic Immediate Instructions
SUBL RD, #IMM8 1 1 0 0 0 RD IMM8
SUBH RD, #IMM8 1 1 0 0 1 RD IMM8
CMPL RS, #IMM8 1 1 0 1 0 RS IMM8
CPCH RS, #IMM8 1 1 0 1 1 RS IMM8
ADDL RD, #IMM8 1 1 1 0 0 RD IMM8
ADDH RD, #IMM8 1 1 1 0 1 RD IMM8
LDL RD, #IMM8 1 1 1 1 0 RD IMM8
LDH RD, #IMM8 1 1 1 1 1 RD IMM8

10.9 Initialization and Application Information

10.9.1 Initialization
The recommended initialization of the XGATE is as follows:

1. Clear the XGE bit to suppress any incoming service requests.
2. Make sure that no thread is running on the XGATE. This can be done in several ways:

a) Poll the XGCHID register until it reads $00. Also poll XGDBG and XGSWEF to make sure
that the XGATE has not been stopped.

b) Enter Debug Mode by setting the XGDBG bit. Clear the XGCHID register. Clear the XGDBG
bit.

The recommended method is a).
3. Set the XGVBR register to the lowest address of the XGATE vector space.
4. Clear all Channel ID flags.
5. Copy XGATE vectors and code into the RAM.
6. Initialize the S12X_INT module.
7. Enable the XGATE by setting the XGE bit.

The following code example implements the XGATE initialization sequence.

10.9.2 Code Example (Transmit "Hello World!" on SCI)
CPU S12X
;###########################################
;# SYMBOLS #
;###########################################

SCI_REGS EQU $00C8 ;SCI register space
SCIBDH EQU SCI_REGS+$00; ;SCI Baud Rate Register
SCIBDL EQU SCI_REGS+$00 ;SCI Baud Rate Register
SCICR2 EQU SCI_REGS+$03 ;SCI Control Register 2
SCISR1 EQU SCI_REGS+$04 ;SCI Status  Register 1
SCIDRL EQU SCI_REGS+$07 ;SCI Control Register 2
TIE EQU $80 ;TIE bit mask
TE EQU $08 ;TE bit mask
RE EQU $04 ;RE bit mask

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 463



Chapter 10 XGATE (S12XGATEV3)

SCI_VEC EQU $D6 ;SCI vector number

INT_REGS EQU $0120 ;S12X_INT register space
INT_CFADDR EQU INT_REGS+$07 ;Interrupt Configuration Address Register
INT_CFDATA EQU INT_REGS+$08 ;Interrupt Configuration Data Registers
RQST EQU $80 ;RQST bit mask

XGATE_REGS EQU $0380 ;XGATE register space
XGMCTL EQU XGATE_REGS+$00 ;XGATE Module Control Register
XGMCTL_CLEAR EQU $FA02 ;Clear all XGMCTL bits
XGMCTL_ENABLE EQU $8282 ;Enable XGATE
XGCHID EQU XGATE_REGS+$02 ;XGATE Channel ID Register
XGISPSEL EQU XGATE_REGS+$05 ;XGATE Channel ID Register
XGVBR EQU XGATE_REGS+$06 ;XGATE ISP Select Register
XGIF EQU XGATE_REGS+$08 ;XGATE Interrupt Flag Vector
XGSWT EQU XGATE_REGS+$18 ;XGATE Software Trigger Register
XGSEM EQU XGATE_REGS+$1A ;XGATE Semaphore Register

RPAGE EQU $0016

RAM_SIZE EQU 32*$400 ;32k RAM

RAM_START EQU $1000
RAM_START_XG EQU $10000-RAM_SIZE
RAM_START_GLOB EQU $100000-RAM_SIZE

XGATE_VECTORS EQU RAM_START
XGATE_VECTORS_XG EQU RAM_START_XG

XGATE_DATA EQU RAM_START+(4*128)
XGATE_DATA_XG EQU RAM_START_XG+(4*128)

XGATE_CODE EQU XGATE_DATA+(XGATE_CODE_FLASH-XGATE_DATA_FLASH)
XGATE_CODE_XG EQU XGATE_DATA_XG+(XGATE_CODE_FLASH-XGATE_DATA_FLASH)

BUS_FREQ_HZ EQU 40000000

;###########################################
;# S12XE VECTOR TABLE #
;###########################################
ORG $FF10 ;non-maskable interrupts
DW DUMMY_ISR DUMMY_ISR DUMMY_ISR DUMMY_ISR

ORG $FFF4 ;non-maskable interrupts
DW DUMMY_ISR DUMMY_ISR DUMMY_ISR

ORG $FFFA ;resets
DW START_OF_CODE START_OF_CODE START_OF_CODE

;###########################################
;# DISABLE COP #
;###########################################
ORG $FF0E
DW $FFFE

ORG $C000
START_OF_CODE

MC9S12XE-Family Reference Manual  Rev. 1.25
464 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

;###########################################
;# INITIALIZE S12XE CORE #
;###########################################
SEI
MOVB #(RAM_START_GLOB>>12), RPAGE ;set RAM page

;###########################################
;# INITIALIZE SCI #
;###########################################

INIT_SCI MOVW #(BUS_FREQ_HZ/(16*9600)), SCIBDH;set baud rate
MOVB #(TIE|TE), SCICR2;enable tx buffer empty interrupt

;###########################################
;# INITIALIZE S12X_INT #
;###########################################

INIT_INT MOVB #(SCI_VEC&$F0), INT_CFADDR ;switch SCI interrupts to XGATE
MOVB #RQST|$01, INT_CFDATA+((SCI_VEC&$0F)>>1)

;###########################################
;# INITIALIZE XGATE #
;###########################################

INIT_XGATE MOVW #XGMCTL_CLEAR, XGMCTL ;clear all XGMCTL bits

INIT_XGATE_BUSY_LOOP TST XGCHID ;wait until current thread is finished
BNE INIT_XGATE_BUSY_LOOP

LDX #XGIF ;clear all channel interrupt flags
LDD #$FFFF
STD 2,X+
STD 2,X+
STD 2,X+
STD 2,X+
STD 2,X+
STD 2,X+
STD 2,X+
STD 2,X+

CLR XGISPSEL ;set vector base register
MOVW #XGATE_VECTORS_XG, XGVBR
MOVW #$FF00, XGSWT ;clear all software triggers

;###########################################
;# INITIALIZE XGATE VECTOR TABLE #
;###########################################
LDAA #128 ;build XGATE vector table
LDY #XGATE_VECTORS

INIT_XGATE_VECTAB_LOOP MOVW #XGATE_DUMMY_ISR_XG, 4,Y+
DBNE A, INIT_XGATE_VECTAB_LOOP

MOVW #XGATE_CODE_XG, RAM_START+(2*SCI_VEC)
MOVW #XGATE_DATA_XG, RAM_START+(2*SCI_VEC)+2

;###########################################
;# COPY XGATE CODE #
;###########################################

COPY_XGATE_CODE LDX #XGATE_DATA_FLASH
COPY_XGATE_CODE_LOOP MOVW 2,X+, 2,Y+

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 465



Chapter 10 XGATE (S12XGATEV3)

MOVW 2,X+, 2,Y+
MOVW 2,X+, 2,Y+
MOVW 2,X+, 2,Y+
CPX #XGATE_CODE_FLASH_END
BLS COPY_XGATE_CODE_LOOP

;###########################################
;# START XGATE #
;###########################################

START_XGATE MOVW #XGMCTL_ENABLE, XGMCTL ;enable XGATE
BRA *

;###########################################
;# DUMMY INTERRUPT SERVICE ROUTINE #
;###########################################

DUMMY_ISR RTI

CPU XGATE
;###########################################
;# XGATE DATA #
;###########################################
ALIGN 1

XGATE_DATA_FLASH EQU *
XGATE_DATA_SCI EQU *-XGATE_DATA_FLASH

DW SCI_REGS ;pointer to SCI register space
XGATE_DATA_IDX EQU *-XGATE_DATA_FLASH

DB XGATE_DATA_MSG ;string pointer
XGATE_DATA_MSG EQU *-XGATE_DATA_FLASH

FCC "Hello World! ;ASCII string
DB $0D ;CR

;###########################################
;# XGATE CODE #
;###########################################
ALIGN 1

XGATE_CODE_FLASH LDW R2,(R1,#XGATE_DATA_SCI) ;SCI -> R2
LDB R3,(R1,#XGATE_DATA_IDX) ;msg -> R3
LDB R4,(R1,R3+) ;curr. char -> R4
STB R3,(R1,#XGATE_DATA_IDX) ;R3 -> idx
LDB R0,(R2,#(SCISR1-SCI_REGS)) ;initiate SCI transmit
STB R4,(R2,#(SCIDRL-SCI_REGS)) ;initiate SCI transmit
CMPL R4,#$0D
BEQ XGATE_CODE_DONE
RTS

XGATE_CODE_DONE LDL R4,#$00 ;disable SCI interrupts
STB R4,(R2,#(SCICR2-SCI_REGS))
LDL R3,#XGATE_DATA_MSG;reset R3
STB R3,(R1,#XGATE_DATA_IDX)

XGATE_CODE_FLASH_END RTS
XGATE_DUMMY_ISR_XG EQU (XGATE_CODE_FLASH_END-XGATE_CODE_FLASH)+XGATE_CODE_XG

10.9.3 Stack Support
To simplify the implementation of a program stack the XGATE can be configured to set RISC core register
R7 to the beginning of a stack region before executing a thread. Two separate stack regions can be defined:
One for threads of priority level 7 to 4 (refer to Section ********, “XGATE Initial Stack Pointer for

MC9S12XE-Family Reference Manual  Rev. 1.25
466 Freescale Semiconductor



Chapter 10 XGATE (S12XGATEV3)

Interrupt Priorities 7 to 4 (XGISP74)”) and one for threads of priority level 3 to 1 (refer to Section ********,
“XGATE Initial Stack Pointer for Interrupt Priorities 3 to 1 (XGISP31)”).

MC9S12XE-Family Reference Manual  Rev. 1.25
Freescale Semiconductor 467



Chapter 10 XGATE (S12XGATEV3)

MC9S12XE-Family Reference Manual  Rev. 1.25
468 Freescale Semiconductor