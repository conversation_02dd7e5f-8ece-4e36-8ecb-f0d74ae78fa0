<Window x:Class="VolvoFlashWR.UI.Views.SettingsView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:VolvoFlashWR.UI.Views"
        mc:Ignorable="d"
        Title="Application Settings" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Settings TabControl -->
        <TabControl Grid.Row="0" Margin="0,0,0,10">
            <!-- General Settings Tab -->
            <TabItem Header="General">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <GroupBox Header="User Interface" Margin="0,0,0,10">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="Theme:" VerticalAlignment="Center"/>
                                <ComboBox Grid.Row="0" Grid.Column="1" ItemsSource="{Binding AvailableThemes}" 
                                          SelectedItem="{Binding UITheme}" Margin="5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="Language:" VerticalAlignment="Center"/>
                                <ComboBox Grid.Row="1" Grid.Column="1" ItemsSource="{Binding AvailableLanguages}" 
                                          SelectedItem="{Binding UILanguage}" Margin="5"/>
                            </Grid>
                        </GroupBox>

                        <GroupBox Header="Logging" Margin="0,0,0,10">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="Detailed Logging:" VerticalAlignment="Center"/>
                                <CheckBox Grid.Row="0" Grid.Column="1" IsChecked="{Binding DetailedLogging}" 
                                          VerticalAlignment="Center" Margin="5"/>
                            </Grid>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- Backup Settings Tab -->
            <TabItem Header="Backup">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <GroupBox Header="Backup Options" Margin="0,0,0,10">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="Use Compression:" VerticalAlignment="Center"/>
                                <CheckBox Grid.Row="0" Grid.Column="1" IsChecked="{Binding UseCompression}" 
                                          VerticalAlignment="Center" Margin="5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="Use Encryption:" VerticalAlignment="Center"/>
                                <CheckBox Grid.Row="1" Grid.Column="1" IsChecked="{Binding UseEncryption}" 
                                          VerticalAlignment="Center" Margin="5"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="Max Backups to Keep:" VerticalAlignment="Center"/>
                                <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="5">
                                    <TextBox Text="{Binding MaxBackupsToKeep}" Width="100" VerticalAlignment="Center"/>
                                    <TextBlock Text="(0 = keep all)" Margin="10,0,0,0" VerticalAlignment="Center" Foreground="Gray"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- Communication Settings Tab -->
            <TabItem Header="Communication">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <GroupBox Header="Vocom Connection" Margin="0,0,0,10">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="Auto Connect:" VerticalAlignment="Center"/>
                                <CheckBox Grid.Row="0" Grid.Column="1" IsChecked="{Binding AutoConnectVocom}" 
                                          VerticalAlignment="Center" Margin="5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="Use WiFi Fallback:" VerticalAlignment="Center"/>
                                <CheckBox Grid.Row="1" Grid.Column="1" IsChecked="{Binding UseWiFiFallback}" 
                                          VerticalAlignment="Center" Margin="5"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="Connection Timeout:" VerticalAlignment="Center"/>
                                <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="5">
                                    <TextBox Text="{Binding ConnectionTimeout}" Width="100" VerticalAlignment="Center"/>
                                    <TextBlock Text="ms" Margin="5,0,0,0" VerticalAlignment="Center"/>
                                </StackPanel>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="Retry Attempts:" VerticalAlignment="Center"/>
                                <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding RetryAttempts}" Width="100" 
                                         HorizontalAlignment="Left" Margin="5"/>
                            </Grid>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- ECU Settings Tab -->
            <TabItem Header="ECU">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <GroupBox Header="ECU Options" Margin="0,0,0,10">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="Auto Scan ECUs:" VerticalAlignment="Center"/>
                                <CheckBox Grid.Row="0" Grid.Column="1" IsChecked="{Binding AutoScanECUs}" 
                                          VerticalAlignment="Center" Margin="5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="Operating Mode:" VerticalAlignment="Center"/>
                                <ComboBox Grid.Row="1" Grid.Column="1" ItemsSource="{Binding AvailableOperatingModes}" 
                                          SelectedItem="{Binding OperatingMode}" Margin="5"/>
                            </Grid>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- Status and Buttons -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Status Message -->
            <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" VerticalAlignment="Center" 
                       Foreground="{Binding StatusMessageColor}"/>

            <!-- Reset Button -->
            <Button Grid.Column="1" Content="Reset to Defaults" Command="{Binding ResetToDefaultsCommand}" 
                    Margin="5" Padding="10,5" Width="120"/>

            <!-- Cancel Button -->
            <Button Grid.Column="2" Content="Cancel" Command="{Binding CancelCommand}" 
                    Margin="5" Padding="10,5" Width="80"/>

            <!-- Save Button -->
            <Button Grid.Column="3" Content="Save" Command="{Binding SaveCommand}" 
                    Margin="5" Padding="10,5" Width="80" IsDefault="True"/>
        </Grid>

        <!-- Busy Indicator -->
        <Grid Grid.Row="0" Grid.RowSpan="2" Background="#80000000" Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <TextBlock Text="Please wait..." Foreground="White" FontSize="16" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                <ProgressBar IsIndeterminate="True" Width="200" Height="20"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
