using System;
using System.Collections.Generic;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents backup data from an ECU
    /// </summary>
    public class BackupData
    {
        /// <summary>
        /// Unique identifier for the backup
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Timestamp when the backup was created
        /// </summary>
        public DateTime CreationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// ID of the ECU from which the backup was created
        /// </summary>
        public string ECUId { get; set; }

        /// <summary>
        /// Name of the ECU from which the backup was created
        /// </summary>
        public string ECUName { get; set; }

        /// <summary>
        /// Serial number of the ECU
        /// </summary>
        public string ECUSerialNumber { get; set; }

        /// <summary>
        /// Hardware version of the ECU
        /// </summary>
        public string ECUHardwareVersion { get; set; }

        /// <summary>
        /// Software version of the ECU
        /// </summary>
        public string ECUSoftwareVersion { get; set; }

        /// <summary>
        /// EEPROM data from the ECU
        /// </summary>
        public byte[] EEPROMData { get; set; }

        /// <summary>
        /// Microcontroller code from the ECU
        /// </summary>
        public byte[] MicrocontrollerCode { get; set; }

        /// <summary>
        /// Parameters from the ECU at the time of backup
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Description or notes about the backup
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Name of the user who created the backup
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Checksum of the backup data for integrity verification
        /// </summary>
        public string Checksum { get; set; }

        /// <summary>
        /// Indicates if the backup is complete (contains all data)
        /// </summary>
        public bool IsComplete { get; set; }

        /// <summary>
        /// File path where the backup is stored
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// Alternative file paths where the backup is stored
        /// </summary>
        public List<string> AlternativeFilePaths { get; set; } = new List<string>();

        /// <summary>
        /// Category of the backup (e.g., "Production", "Development", "Testing")
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// Tags associated with the backup for filtering and organization
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// Version number for the backup (for tracking changes over time)
        /// </summary>
        public int Version { get; set; } = 1;

        /// <summary>
        /// Indicates if the backup is compressed
        /// </summary>
        public bool IsCompressed { get; set; }

        /// <summary>
        /// Indicates if the backup is encrypted
        /// </summary>
        public bool IsEncrypted { get; set; }

        /// <summary>
        /// Timestamp when the backup was last modified
        /// </summary>
        public DateTime LastModifiedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// Name of the user who last modified the backup
        /// </summary>
        public string LastModifiedBy { get; set; }

        /// <summary>
        /// Size of the backup in bytes
        /// </summary>
        public long SizeInBytes { get; set; }

        /// <summary>
        /// Parent backup ID if this is a version of another backup
        /// </summary>
        public string ParentBackupId { get; set; }

        /// <summary>
        /// Root backup ID (the first version in the chain)
        /// </summary>
        public string RootBackupId { get; set; }

        /// <summary>
        /// List of child backup IDs (newer versions of this backup)
        /// </summary>
        public List<string> ChildBackupIds { get; set; } = new List<string>();

        /// <summary>
        /// Version notes describing what changed in this version
        /// </summary>
        public string VersionNotes { get; set; }

        /// <summary>
        /// Timestamp when this version was created
        /// </summary>
        public DateTime VersionCreationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// Indicates if this is the latest version of the backup
        /// </summary>
        public bool IsLatestVersion { get; set; } = true;
    }
}
