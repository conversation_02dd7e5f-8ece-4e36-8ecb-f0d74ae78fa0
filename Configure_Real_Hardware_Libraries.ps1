# Configure Real Hardware Libraries Script
# This script configures the application to use real Vocom hardware libraries

Write-Host "=== Configuring Real Hardware Libraries ===" -ForegroundColor Green

# Define library paths
$LibrariesPath = ".\Libraries"
$DriversPath = ".\Drivers"
$AppPath = "."

# Create necessary directories if they don't exist
if (-not (Test-Path $DriversPath)) {
    New-Item -ItemType Directory -Path $DriversPath -Force
    Write-Host "Created Drivers directory" -ForegroundColor Yellow
}

if (-not (Test-Path "$DriversPath\Vocom")) {
    New-Item -ItemType Directory -Path "$DriversPath\Vocom" -Force
    Write-Host "Created Drivers\Vocom directory" -ForegroundColor Yellow
}

# Copy critical Vocom driver files to Drivers folder
$VocomDriverFiles = @(
    "WUDFPuma.dll",
    "WUDFUpdate_01009.dll",
    "WdfCoInstaller01009.dll",
    "winusbcoinstaller2.dll"
)

Write-Host "`nCopying Vocom driver files to Drivers folder..." -ForegroundColor Cyan
foreach ($file in $VocomDriverFiles) {
    $sourcePath = Join-Path $LibrariesPath $file
    $destPath = Join-Path "$DriversPath\Vocom" $file

    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $destPath -Force
        Write-Host "Copied $file to Drivers\Vocom" -ForegroundColor Green
    } else {
        Write-Host "File $file not found in Libraries" -ForegroundColor Red
    }
}

# Create app.config content
$appConfigContent = '<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="Libraries;Drivers\Vocom" />
    </assemblyBinding>
    <loadFromRemoteSources enabled="true" />
  </runtime>
  <appSettings>
    <add key="VocomDriverPath" value=".\Drivers\Vocom" />
    <add key="LibrariesPath" value=".\Libraries" />
    <add key="UseRealHardware" value="true" />
    <add key="VocomDriverDll" value="WUDFPuma.dll" />
    <add key="ApciDriverDll" value="apci.dll" />
    <add key="EnableVocomLogging" value="true" />
  </appSettings>
</configuration>'

# Write app.config files for all executables
$configFiles = @(
    "VolvoFlashWR.UI.exe.config",
    "VolvoFlashWR.Launcher.exe.config"
)

foreach ($configFile in $configFiles) {
    $configPath = Join-Path $AppPath $configFile
    $appConfigContent | Out-File -FilePath $configPath -Encoding UTF8
    Write-Host "Created $configFile" -ForegroundColor Green
}

Write-Host "`n=== Configuration Complete ===" -ForegroundColor Green
Write-Host "The application is now configured for real Vocom hardware communication." -ForegroundColor Cyan
Write-Host "`nLibraries have been organized and configuration files created." -ForegroundColor Yellow
Write-Host "Connect your Vocom adapter and run the application in normal mode." -ForegroundColor White
