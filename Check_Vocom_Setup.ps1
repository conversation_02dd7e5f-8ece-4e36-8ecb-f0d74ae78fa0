# Quick Vocom Setup Verification Script
Write-Host "=== Vocom Setup Verification ===" -ForegroundColor Green

# Check critical libraries in Libraries folder
$LibrariesPath = ".\Libraries"
$CriticalLibraries = @(
    "WUDFPuma.dll",
    "apci.dll", 
    "apcidb.dll",
    "Volvo.ApciPlus.dll",
    "Volvo.ApciPlusData.dll",
    "Volvo.ApciPlusTea2Data.dll"
)

Write-Host "`n1. Checking critical libraries in Libraries folder..." -ForegroundColor Yellow
$MissingLibraries = @()
foreach ($lib in $CriticalLibraries) {
    $libPath = Join-Path $LibrariesPath $lib
    if (Test-Path $libPath) {
        Write-Host "✓ $lib" -ForegroundColor Green
    } else {
        Write-Host "✗ $lib" -ForegroundColor Red
        $MissingLibraries += $lib
    }
}

# Check Vocom driver installation
Write-Host "`n2. Checking Vocom driver installation..." -ForegroundColor Yellow
$VocomDriverPath = "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll"
if (Test-Path $VocomDriverPath) {
    Write-Host "✓ Vocom driver installed at: $VocomDriverPath" -ForegroundColor Green
} else {
    Write-Host "✗ Vocom driver NOT installed" -ForegroundColor Red
    Write-Host "  Please install CommunicationUnitInstaller-*******.msi" -ForegroundColor Yellow
}

# Check for Phoenix Diag installation
Write-Host "`n3. Checking Phoenix Diag installation..." -ForegroundColor Yellow
$PhoenixPath = "C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021"
if (Test-Path $PhoenixPath) {
    Write-Host "✓ Phoenix Diag found at: $PhoenixPath" -ForegroundColor Green
} else {
    Write-Host "✗ Phoenix Diag not found" -ForegroundColor Yellow
}

# Check USB devices for Vocom adapter
Write-Host "`n4. Checking for connected USB devices..." -ForegroundColor Yellow
try {
    $UsbDevices = Get-WmiObject -Class Win32_USBHub | Where-Object { $_.Description -like "*Vocom*" -or $_.Description -like "*88890020*" }
    if ($UsbDevices) {
        Write-Host "✓ Vocom-related USB device found" -ForegroundColor Green
        foreach ($device in $UsbDevices) {
            Write-Host "  - $($device.Description)" -ForegroundColor Cyan
        }
    } else {
        Write-Host "✗ No Vocom USB devices detected" -ForegroundColor Yellow
        Write-Host "  (This is normal if Vocom adapter is not connected)" -ForegroundColor Gray
    }
} catch {
    Write-Host "! Unable to check USB devices" -ForegroundColor Yellow
}

# Summary
Write-Host "`n=== SUMMARY ===" -ForegroundColor Green
if ($MissingLibraries.Count -eq 0) {
    Write-Host "✓ All critical libraries are present" -ForegroundColor Green
} else {
    Write-Host "✗ Missing $($MissingLibraries.Count) critical libraries" -ForegroundColor Red
}

if (Test-Path $VocomDriverPath) {
    Write-Host "✓ Vocom driver is installed" -ForegroundColor Green
} else {
    Write-Host "✗ Vocom driver needs to be installed" -ForegroundColor Red
}

Write-Host "`n=== NEXT STEPS ===" -ForegroundColor Yellow
if (-not (Test-Path $VocomDriverPath)) {
    Write-Host "1. Install Vocom driver: CommunicationUnitInstaller-*******.msi" -ForegroundColor Cyan
}
if ($MissingLibraries.Count -gt 0) {
    Write-Host "2. Copy missing libraries to Libraries folder" -ForegroundColor Cyan
}
Write-Host "3. Connect Vocom 1 adapter via USB" -ForegroundColor Cyan
Write-Host "4. Run application with: .\Run_Real_Hardware_Mode.bat" -ForegroundColor Cyan
