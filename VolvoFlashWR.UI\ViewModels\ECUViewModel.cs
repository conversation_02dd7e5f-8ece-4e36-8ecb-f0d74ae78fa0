using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using Microsoft.Win32;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.UI.ViewModels
{
    public class ECUViewModel : INotifyPropertyChanged
    {
        #region Private Fields

        private readonly ILoggingService _loggingService;
        private readonly IECUCommunicationService _ecuCommunicationService;
        private readonly IBackupService _backupService;
        private ECUDevice _ecuDevice;
        private bool _isBusy;
        private string _statusMessage;
        private ObservableCollection<ECUFault> _activeFaults;
        private ObservableCollection<ECUFault> _inactiveFaults;
        private DiagnosticData _diagnosticData;
        private byte[] _eepromData;
        private byte[] _microcontrollerCode;

        #endregion

        #region Properties

        public ECUDevice ECUDevice
        {
            get => _ecuDevice;
            set
            {
                _ecuDevice = value;
                OnPropertyChanged();
                // Update command can execute status
                (ReadEEPROMCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (WriteEEPROMCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (ReadMicrocontrollerCodeCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (WriteMicrocontrollerCodeCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (ReadFaultsCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (ClearFaultsCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (PerformDiagnosticsCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (BackupECUCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (RestoreECUCommand as RelayCommand)?.RaiseCanExecuteChanged();
            }
        }

        public bool IsBusy
        {
            get => _isBusy;
            set
            {
                _isBusy = value;
                OnPropertyChanged();
                // Update command can execute status
                (ReadEEPROMCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (WriteEEPROMCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (ReadMicrocontrollerCodeCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (WriteMicrocontrollerCodeCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (ReadFaultsCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (ClearFaultsCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (PerformDiagnosticsCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (BackupECUCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (RestoreECUCommand as RelayCommand)?.RaiseCanExecuteChanged();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<ECUFault> ActiveFaults
        {
            get => _activeFaults;
            set
            {
                _activeFaults = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<ECUFault> InactiveFaults
        {
            get => _inactiveFaults;
            set
            {
                _inactiveFaults = value;
                OnPropertyChanged();
            }
        }

        public DiagnosticData DiagnosticData
        {
            get => _diagnosticData;
            set
            {
                _diagnosticData = value;
                OnPropertyChanged();
            }
        }

        public byte[] EEPROMData
        {
            get => _eepromData;
            set
            {
                _eepromData = value;
                OnPropertyChanged();
                (SaveEEPROMCommand as RelayCommand)?.RaiseCanExecuteChanged();
            }
        }

        public byte[] MicrocontrollerCode
        {
            get => _microcontrollerCode;
            set
            {
                _microcontrollerCode = value;
                OnPropertyChanged();
                (SaveMicrocontrollerCodeCommand as RelayCommand)?.RaiseCanExecuteChanged();
            }
        }

        #endregion

        #region Commands

        public ICommand ReadEEPROMCommand { get; private set; }
        public ICommand WriteEEPROMCommand { get; private set; }
        public ICommand SaveEEPROMCommand { get; private set; }
        public ICommand LoadEEPROMCommand { get; private set; }
        public ICommand ReadMicrocontrollerCodeCommand { get; private set; }
        public ICommand WriteMicrocontrollerCodeCommand { get; private set; }
        public ICommand SaveMicrocontrollerCodeCommand { get; private set; }
        public ICommand LoadMicrocontrollerCodeCommand { get; private set; }
        public ICommand ReadFaultsCommand { get; private set; }
        public ICommand ClearFaultsCommand { get; private set; }
        public ICommand PerformDiagnosticsCommand { get; private set; }
        public ICommand BackupECUCommand { get; private set; }
        public ICommand RestoreECUCommand { get; private set; }

        #endregion

        #region Constructor

        public ECUViewModel(ILoggingService loggingService, IECUCommunicationService ecuCommunicationService, IBackupService backupService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _ecuCommunicationService = ecuCommunicationService ?? throw new ArgumentNullException(nameof(ecuCommunicationService));
            _backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));

            // Initialize collections
            ActiveFaults = new ObservableCollection<ECUFault>();
            InactiveFaults = new ObservableCollection<ECUFault>();

            // Initialize commands
            ReadEEPROMCommand = new RelayCommand(
                _ => { ReadEEPROMAsync().ConfigureAwait(false); },
                _ => CanPerformECUOperation());
            WriteEEPROMCommand = new RelayCommand(
                _ => { WriteEEPROMAsync().ConfigureAwait(false); },
                _ => CanWriteEEPROM());
            SaveEEPROMCommand = new RelayCommand(
                _ => SaveEEPROM(),
                _ => CanSaveEEPROM());
            LoadEEPROMCommand = new RelayCommand(
                _ => LoadEEPROM(),
                _ => CanPerformECUOperation());
            ReadMicrocontrollerCodeCommand = new RelayCommand(
                _ => { ReadMicrocontrollerCodeAsync().ConfigureAwait(false); },
                _ => CanPerformECUOperation());
            WriteMicrocontrollerCodeCommand = new RelayCommand(
                _ => { WriteMicrocontrollerCodeAsync().ConfigureAwait(false); },
                _ => CanWriteMicrocontrollerCode());
            SaveMicrocontrollerCodeCommand = new RelayCommand(
                _ => SaveMicrocontrollerCode(),
                _ => CanSaveMicrocontrollerCode());
            LoadMicrocontrollerCodeCommand = new RelayCommand(
                _ => LoadMicrocontrollerCode(),
                _ => CanPerformECUOperation());
            ReadFaultsCommand = new RelayCommand(
                _ => { ReadFaultsAsync().ConfigureAwait(false); },
                _ => CanPerformECUOperation());
            ClearFaultsCommand = new RelayCommand(
                _ => { ClearFaultsAsync().ConfigureAwait(false); },
                _ => CanPerformECUOperation());
            PerformDiagnosticsCommand = new RelayCommand(
                _ => { PerformDiagnosticsAsync().ConfigureAwait(false); },
                _ => CanPerformECUOperation());
            BackupECUCommand = new RelayCommand(
                _ => { BackupECUAsync().ConfigureAwait(false); },
                _ => CanPerformECUOperation());
            RestoreECUCommand = new RelayCommand(
                _ => { RestoreECUAsync().ConfigureAwait(false); },
                _ => CanPerformECUOperation());
        }

        #endregion

        #region ECU Operations

        private async Task ReadEEPROMAsync()
        {
            if (ECUDevice == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            try
            {
                IsBusy = true;
                StatusMessage = $"Reading EEPROM from ECU {ECUDevice.Name}...";
                _loggingService.LogInformation($"Reading EEPROM from ECU {ECUDevice.Name}", "ECUViewModel");

                // Read EEPROM data
                EEPROMData = await _ecuCommunicationService.ReadEEPROMAsync(ECUDevice);

                if (EEPROMData != null)
                {
                    StatusMessage = $"Read {EEPROMData.Length} bytes of EEPROM data from ECU {ECUDevice.Name}";
                    _loggingService.LogInformation($"Read {EEPROMData.Length} bytes of EEPROM data from ECU {ECUDevice.Name}", "ECUViewModel");
                }
                else
                {
                    StatusMessage = $"Failed to read EEPROM data from ECU {ECUDevice.Name}";
                    _loggingService.LogError($"Failed to read EEPROM data from ECU {ECUDevice.Name}", "ECUViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error reading EEPROM: {ex.Message}";
                _loggingService.LogError("Error reading EEPROM", "ECUViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task WriteEEPROMAsync()
        {
            if (ECUDevice == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            if (EEPROMData == null || EEPROMData.Length == 0)
            {
                StatusMessage = "No EEPROM data to write";
                return;
            }

            try
            {
                IsBusy = true;
                StatusMessage = $"Writing EEPROM to ECU {ECUDevice.Name}...";
                _loggingService.LogInformation($"Writing EEPROM to ECU {ECUDevice.Name}", "ECUViewModel");

                // Write EEPROM data
                bool success = await _ecuCommunicationService.WriteEEPROMAsync(ECUDevice, EEPROMData);

                if (success)
                {
                    StatusMessage = $"Wrote {EEPROMData.Length} bytes of EEPROM data to ECU {ECUDevice.Name}";
                    _loggingService.LogInformation($"Wrote {EEPROMData.Length} bytes of EEPROM data to ECU {ECUDevice.Name}", "ECUViewModel");
                }
                else
                {
                    StatusMessage = $"Failed to write EEPROM data to ECU {ECUDevice.Name}";
                    _loggingService.LogError($"Failed to write EEPROM data to ECU {ECUDevice.Name}", "ECUViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error writing EEPROM: {ex.Message}";
                _loggingService.LogError("Error writing EEPROM", "ECUViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void SaveEEPROM()
        {
            if (EEPROMData == null || EEPROMData.Length == 0)
            {
                StatusMessage = "No EEPROM data to save";
                return;
            }

            try
            {
                // Create a save file dialog
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "Binary Files (*.bin)|*.bin|All Files (*.*)|*.*",
                    DefaultExt = ".bin",
                    FileName = $"{ECUDevice.Name}_EEPROM_{DateTime.Now:yyyyMMdd_HHmmss}.bin"
                };

                // Show the dialog and get the result
                bool? result = saveFileDialog.ShowDialog();

                if (result == true)
                {
                    // Save the EEPROM data to the selected file
                    File.WriteAllBytes(saveFileDialog.FileName, EEPROMData);

                    StatusMessage = $"EEPROM data saved to {saveFileDialog.FileName}";
                    _loggingService.LogInformation($"EEPROM data saved to {saveFileDialog.FileName}", "ECUViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error saving EEPROM data: {ex.Message}";
                _loggingService.LogError("Error saving EEPROM data", "ECUViewModel", ex);
            }
        }

        private void LoadEEPROM()
        {
            try
            {
                // Create an open file dialog
                OpenFileDialog openFileDialog = new OpenFileDialog
                {
                    Filter = "Binary Files (*.bin)|*.bin|All Files (*.*)|*.*",
                    DefaultExt = ".bin"
                };

                // Show the dialog and get the result
                bool? result = openFileDialog.ShowDialog();

                if (result == true)
                {
                    // Load the EEPROM data from the selected file
                    EEPROMData = File.ReadAllBytes(openFileDialog.FileName);

                    StatusMessage = $"EEPROM data loaded from {openFileDialog.FileName}";
                    _loggingService.LogInformation($"EEPROM data loaded from {openFileDialog.FileName}", "ECUViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading EEPROM data: {ex.Message}";
                _loggingService.LogError("Error loading EEPROM data", "ECUViewModel", ex);
            }
        }

        private async Task ReadMicrocontrollerCodeAsync()
        {
            if (ECUDevice == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            try
            {
                IsBusy = true;
                StatusMessage = $"Reading microcontroller code from ECU {ECUDevice.Name}...";
                _loggingService.LogInformation($"Reading microcontroller code from ECU {ECUDevice.Name}", "ECUViewModel");

                // Read microcontroller code
                MicrocontrollerCode = await _ecuCommunicationService.ReadMicrocontrollerCodeAsync(ECUDevice);

                if (MicrocontrollerCode != null)
                {
                    StatusMessage = $"Read {MicrocontrollerCode.Length} bytes of microcontroller code from ECU {ECUDevice.Name}";
                    _loggingService.LogInformation($"Read {MicrocontrollerCode.Length} bytes of microcontroller code from ECU {ECUDevice.Name}", "ECUViewModel");
                }
                else
                {
                    StatusMessage = $"Failed to read microcontroller code from ECU {ECUDevice.Name}";
                    _loggingService.LogError($"Failed to read microcontroller code from ECU {ECUDevice.Name}", "ECUViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error reading microcontroller code: {ex.Message}";
                _loggingService.LogError("Error reading microcontroller code", "ECUViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task WriteMicrocontrollerCodeAsync()
        {
            if (ECUDevice == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            if (MicrocontrollerCode == null || MicrocontrollerCode.Length == 0)
            {
                StatusMessage = "No microcontroller code to write";
                return;
            }

            try
            {
                IsBusy = true;
                StatusMessage = $"Writing microcontroller code to ECU {ECUDevice.Name}...";
                _loggingService.LogInformation($"Writing microcontroller code to ECU {ECUDevice.Name}", "ECUViewModel");

                // Write microcontroller code
                bool success = await _ecuCommunicationService.WriteMicrocontrollerCodeAsync(ECUDevice, MicrocontrollerCode);

                if (success)
                {
                    StatusMessage = $"Wrote {MicrocontrollerCode.Length} bytes of microcontroller code to ECU {ECUDevice.Name}";
                    _loggingService.LogInformation($"Wrote {MicrocontrollerCode.Length} bytes of microcontroller code to ECU {ECUDevice.Name}", "ECUViewModel");
                }
                else
                {
                    StatusMessage = $"Failed to write microcontroller code to ECU {ECUDevice.Name}";
                    _loggingService.LogError($"Failed to write microcontroller code to ECU {ECUDevice.Name}", "ECUViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error writing microcontroller code: {ex.Message}";
                _loggingService.LogError("Error writing microcontroller code", "ECUViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void SaveMicrocontrollerCode()
        {
            if (MicrocontrollerCode == null || MicrocontrollerCode.Length == 0)
            {
                StatusMessage = "No microcontroller code to save";
                return;
            }

            try
            {
                // Create a save file dialog
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "Binary Files (*.bin)|*.bin|All Files (*.*)|*.*",
                    DefaultExt = ".bin",
                    FileName = $"{ECUDevice.Name}_MCU_{DateTime.Now:yyyyMMdd_HHmmss}.bin"
                };

                // Show the dialog and get the result
                bool? result = saveFileDialog.ShowDialog();

                if (result == true)
                {
                    // Save the microcontroller code to the selected file
                    File.WriteAllBytes(saveFileDialog.FileName, MicrocontrollerCode);

                    StatusMessage = $"Microcontroller code saved to {saveFileDialog.FileName}";
                    _loggingService.LogInformation($"Microcontroller code saved to {saveFileDialog.FileName}", "ECUViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error saving microcontroller code: {ex.Message}";
                _loggingService.LogError("Error saving microcontroller code", "ECUViewModel", ex);
            }
        }

        private void LoadMicrocontrollerCode()
        {
            try
            {
                // Create an open file dialog
                OpenFileDialog openFileDialog = new OpenFileDialog
                {
                    Filter = "Binary Files (*.bin)|*.bin|All Files (*.*)|*.*",
                    DefaultExt = ".bin"
                };

                // Show the dialog and get the result
                bool? result = openFileDialog.ShowDialog();

                if (result == true)
                {
                    // Load the microcontroller code from the selected file
                    MicrocontrollerCode = File.ReadAllBytes(openFileDialog.FileName);

                    StatusMessage = $"Microcontroller code loaded from {openFileDialog.FileName}";
                    _loggingService.LogInformation($"Microcontroller code loaded from {openFileDialog.FileName}", "ECUViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading microcontroller code: {ex.Message}";
                _loggingService.LogError("Error loading microcontroller code", "ECUViewModel", ex);
            }
        }

        private async Task ReadFaultsAsync()
        {
            if (ECUDevice == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            try
            {
                IsBusy = true;
                StatusMessage = $"Reading faults from ECU {ECUDevice.Name}...";
                _loggingService.LogInformation($"Reading faults from ECU {ECUDevice.Name}", "ECUViewModel");

                // Read active faults
                var activeFaults = await _ecuCommunicationService.ReadActiveFaultsAsync(ECUDevice);

                // Read inactive faults
                var inactiveFaults = await _ecuCommunicationService.ReadInactiveFaultsAsync(ECUDevice);

                // Update collections
                ActiveFaults.Clear();
                InactiveFaults.Clear();

                if (activeFaults != null)
                {
                    foreach (var fault in activeFaults)
                    {
                        ActiveFaults.Add(fault);
                    }
                }

                if (inactiveFaults != null)
                {
                    foreach (var fault in inactiveFaults)
                    {
                        InactiveFaults.Add(fault);
                    }
                }

                StatusMessage = $"Read {ActiveFaults.Count} active faults and {InactiveFaults.Count} inactive faults from ECU {ECUDevice.Name}";
                _loggingService.LogInformation($"Read {ActiveFaults.Count} active faults and {InactiveFaults.Count} inactive faults from ECU {ECUDevice.Name}", "ECUViewModel");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error reading faults: {ex.Message}";
                _loggingService.LogError("Error reading faults", "ECUViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task ClearFaultsAsync()
        {
            if (ECUDevice == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            try
            {
                IsBusy = true;
                StatusMessage = $"Clearing faults from ECU {ECUDevice.Name}...";
                _loggingService.LogInformation($"Clearing faults from ECU {ECUDevice.Name}", "ECUViewModel");

                // Clear faults
                bool success = await _ecuCommunicationService.ClearFaultsAsync(ECUDevice);

                if (success)
                {
                    // Clear local collections
                    ActiveFaults.Clear();
                    InactiveFaults.Clear();

                    StatusMessage = $"Cleared faults from ECU {ECUDevice.Name}";
                    _loggingService.LogInformation($"Cleared faults from ECU {ECUDevice.Name}", "ECUViewModel");
                }
                else
                {
                    StatusMessage = $"Failed to clear faults from ECU {ECUDevice.Name}";
                    _loggingService.LogError($"Failed to clear faults from ECU {ECUDevice.Name}", "ECUViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error clearing faults: {ex.Message}";
                _loggingService.LogError("Error clearing faults", "ECUViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task PerformDiagnosticsAsync()
        {
            if (ECUDevice == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            try
            {
                IsBusy = true;
                StatusMessage = $"Performing diagnostics on ECU {ECUDevice.Name}...";
                _loggingService.LogInformation($"Performing diagnostics on ECU {ECUDevice.Name}", "ECUViewModel");

                // Perform diagnostics
                DiagnosticData = await _ecuCommunicationService.PerformDiagnosticSessionAsync(ECUDevice);

                if (DiagnosticData != null && DiagnosticData.IsSuccessful)
                {
                    // Update collections
                    ActiveFaults.Clear();
                    InactiveFaults.Clear();

                    if (DiagnosticData.ActiveFaults != null)
                    {
                        foreach (var fault in DiagnosticData.ActiveFaults)
                        {
                            ActiveFaults.Add(fault);
                        }
                    }

                    if (DiagnosticData.InactiveFaults != null)
                    {
                        foreach (var fault in DiagnosticData.InactiveFaults)
                        {
                            InactiveFaults.Add(fault);
                        }
                    }

                    StatusMessage = $"Performed diagnostics on ECU {ECUDevice.Name}";
                    _loggingService.LogInformation($"Performed diagnostics on ECU {ECUDevice.Name}", "ECUViewModel");
                }
                else
                {
                    StatusMessage = $"Failed to perform diagnostics on ECU {ECUDevice.Name}: {DiagnosticData?.ErrorMessage ?? "Unknown error"}";
                    _loggingService.LogError($"Failed to perform diagnostics on ECU {ECUDevice.Name}: {DiagnosticData?.ErrorMessage ?? "Unknown error"}", "ECUViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error performing diagnostics: {ex.Message}";
                _loggingService.LogError("Error performing diagnostics", "ECUViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task BackupECUAsync()
        {
            if (ECUDevice == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            try
            {
                IsBusy = true;
                StatusMessage = $"Backing up ECU {ECUDevice.Name}...";
                _loggingService.LogInformation($"Backing up ECU {ECUDevice.Name}", "ECUViewModel");

                // Create a save file dialog
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "ECU Backup Files (*.ecubackup)|*.ecubackup|All Files (*.*)|*.*",
                    DefaultExt = ".ecubackup",
                    FileName = $"{ECUDevice.Name}_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.ecubackup"
                };

                // Show the dialog and get the result
                bool? result = saveFileDialog.ShowDialog();

                if (result == true)
                {
                    // Perform backup
                    var progress = new Progress<int>(percent => {
                        // Update progress if needed
                    });
                    var backup = await _backupService.BackupECUAsync(ECUDevice, progress);
                    bool success = backup != null;

                    if (success)
                    {
                        StatusMessage = $"ECU {ECUDevice.Name} backed up to {saveFileDialog.FileName}";
                        _loggingService.LogInformation($"ECU {ECUDevice.Name} backed up to {saveFileDialog.FileName}", "ECUViewModel");
                    }
                    else
                    {
                        StatusMessage = $"Failed to backup ECU {ECUDevice.Name}";
                        _loggingService.LogError($"Failed to backup ECU {ECUDevice.Name}", "ECUViewModel");
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error backing up ECU: {ex.Message}";
                _loggingService.LogError("Error backing up ECU", "ECUViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task RestoreECUAsync()
        {
            if (ECUDevice == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            try
            {
                IsBusy = true;
                StatusMessage = $"Restoring ECU {ECUDevice.Name}...";
                _loggingService.LogInformation($"Restoring ECU {ECUDevice.Name}", "ECUViewModel");

                // Create an open file dialog
                OpenFileDialog openFileDialog = new OpenFileDialog
                {
                    Filter = "ECU Backup Files (*.ecubackup)|*.ecubackup|All Files (*.*)|*.*",
                    DefaultExt = ".ecubackup"
                };

                // Show the dialog and get the result
                bool? result = openFileDialog.ShowDialog();

                if (result == true)
                {
                    // Load backup from file
                    var backup = await _backupService.LoadBackupFromFileAsync(openFileDialog.FileName);

                    if (backup == null)
                    {
                        StatusMessage = $"Failed to load backup from {openFileDialog.FileName}";
                        return;
                    }

                    // Perform restore with progress
                    var progress = new Progress<int>(percent => {
                        // Update progress if needed
                    });

                    bool success = await _backupService.RestoreECUAsync(backup, ECUDevice, progress);

                    if (success)
                    {
                        StatusMessage = $"ECU {ECUDevice.Name} restored from {openFileDialog.FileName}";
                        _loggingService.LogInformation($"ECU {ECUDevice.Name} restored from {openFileDialog.FileName}", "ECUViewModel");
                    }
                    else
                    {
                        StatusMessage = $"Failed to restore ECU {ECUDevice.Name}";
                        _loggingService.LogError($"Failed to restore ECU {ECUDevice.Name}", "ECUViewModel");
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error restoring ECU: {ex.Message}";
                _loggingService.LogError("Error restoring ECU", "ECUViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        #endregion

        #region Command Can Execute Methods

        private bool CanPerformECUOperation()
        {
            return ECUDevice != null && ECUDevice.ConnectionStatus == ECUConnectionStatus.Connected && !IsBusy;
        }

        private bool CanWriteEEPROM()
        {
            return CanPerformECUOperation() && EEPROMData != null && EEPROMData.Length > 0;
        }

        private bool CanSaveEEPROM()
        {
            return EEPROMData != null && EEPROMData.Length > 0;
        }

        private bool CanWriteMicrocontrollerCode()
        {
            return CanPerformECUOperation() && MicrocontrollerCode != null && MicrocontrollerCode.Length > 0;
        }

        private bool CanSaveMicrocontrollerCode()
        {
            return MicrocontrollerCode != null && MicrocontrollerCode.Length > 0;
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}

