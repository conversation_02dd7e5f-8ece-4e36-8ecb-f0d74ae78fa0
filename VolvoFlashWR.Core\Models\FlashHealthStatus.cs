using System;
using System.Collections.Generic;
using VolvoFlashWR.Core.Enums;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents the health status of flash memory
    /// </summary>
    public class FlashHealthStatus
    {
        /// <summary>
        /// Gets or sets the total number of program/erase cycles
        /// </summary>
        public int TotalProgramEraseCycles { get; set; }

        /// <summary>
        /// Gets or sets the maximum number of program/erase cycles supported by the flash memory
        /// </summary>
        public int MaxProgramEraseCycles { get; set; }

        /// <summary>
        /// Gets or sets the number of single-bit errors detected and corrected
        /// </summary>
        public int SingleBitErrorCount { get; set; }

        /// <summary>
        /// Gets or sets the number of multi-bit errors detected (cannot be corrected)
        /// </summary>
        public int MultiBitErrorCount { get; set; }

        /// <summary>
        /// Gets or sets the list of bad blocks (addresses)
        /// </summary>
        public List<uint> BadBlocks { get; set; } = new List<uint>();

        /// <summary>
        /// Gets or sets the timestamp of the last health check
        /// </summary>
        public DateTime LastHealthCheckTimestamp { get; set; }

        /// <summary>
        /// Gets or sets the wear level percentage (0-100)
        /// </summary>
        public double WearLevelPercentage => MaxProgramEraseCycles > 0
            ? Math.Min(100.0, (double)TotalProgramEraseCycles / MaxProgramEraseCycles * 100.0)
            : 0.0;

        /// <summary>
        /// Gets or sets the health status based on wear level and error counts
        /// </summary>
        public HealthStatusEnum HealthStatus
        {
            get
            {
                if (_isHealthStatusOverridden)
                {
                    return _manualHealthStatus;
                }

                if (MultiBitErrorCount > 0 || BadBlocks.Count > 10)
                {
                    return HealthStatusEnum.Critical;
                }
                else if (WearLevelPercentage > 90 || SingleBitErrorCount > 100 || BadBlocks.Count > 5)
                {
                    return HealthStatusEnum.Warning;
                }
                else if (WearLevelPercentage > 70 || SingleBitErrorCount > 50 || BadBlocks.Count > 0)
                {
                    return HealthStatusEnum.Fair;
                }
                else
                {
                    return HealthStatusEnum.Good;
                }
            }
            set
            {
                // This setter is used to manually override the health status if needed
                // For example, when a critical error is detected that doesn't fit the normal criteria
                _manualHealthStatus = value;
                _isHealthStatusOverridden = true;
            }
        }

        /// <summary>
        /// Gets or sets the health status level
        /// </summary>
        public FlashHealthStatusLevel HealthStatusLevel
        {
            get
            {
                if (_isHealthStatusLevelOverridden)
                {
                    return _manualHealthStatusLevel;
                }

                switch (HealthStatus)
                {
                    case HealthStatusEnum.Good:
                        return FlashHealthStatusLevel.Good;
                    case HealthStatusEnum.Fair:
                        return FlashHealthStatusLevel.Fair;
                    case HealthStatusEnum.Warning:
                        return FlashHealthStatusLevel.Warning;
                    case HealthStatusEnum.Critical:
                        return FlashHealthStatusLevel.Critical;
                    default:
                        return FlashHealthStatusLevel.Unknown;
                }
            }
            set
            {
                // This setter is used to manually override the health status level if needed
                _manualHealthStatusLevel = value;
                _isHealthStatusLevelOverridden = true;
            }
        }

        private HealthStatusEnum _manualHealthStatus = HealthStatusEnum.Good;
        private bool _isHealthStatusOverridden = false;
        private FlashHealthStatusLevel _manualHealthStatusLevel = FlashHealthStatusLevel.Good;
        private bool _isHealthStatusLevelOverridden = false;

        /// <summary>
        /// Gets a description of the health status
        /// </summary>
        public string HealthStatusDescription
        {
            get
            {
                // First check if the health status level is overridden
                if (_isHealthStatusLevelOverridden)
                {
                    switch (_manualHealthStatusLevel)
                    {
                        case FlashHealthStatusLevel.Good:
                            return "Flash memory is in good health.";
                        case FlashHealthStatusLevel.Fair:
                            return "Flash memory is in fair health. Consider monitoring more frequently.";
                        case FlashHealthStatusLevel.Warning:
                            return "Flash memory is showing signs of wear. Consider backing up data and planning for replacement.";
                        case FlashHealthStatusLevel.Critical:
                            return "Flash memory is in critical condition. Backup data immediately and replace as soon as possible.";
                        case FlashHealthStatusLevel.Failed:
                            return "Flash memory has failed and is not operational. Replace immediately.";
                        default:
                            return "Unknown health status.";
                    }
                }

                // Otherwise use the calculated health status
                switch (HealthStatus)
                {
                    case HealthStatusEnum.Good:
                        return "Flash memory is in good health.";
                    case HealthStatusEnum.Fair:
                        return "Flash memory is in fair health. Consider monitoring more frequently.";
                    case HealthStatusEnum.Warning:
                        return "Flash memory is showing signs of wear. Consider backing up data and planning for replacement.";
                    case HealthStatusEnum.Critical:
                        return "Flash memory is in critical condition. Backup data immediately and replace as soon as possible.";
                    default:
                        return "Unknown health status.";
                }
            }
        }

        /// <summary>
        /// Gets a dictionary of sector-specific program/erase cycle counts
        /// </summary>
        public Dictionary<uint, int> SectorProgramEraseCycles { get; set; } = new Dictionary<uint, int>();

        /// <summary>
        /// Gets a dictionary of sector-specific error counts
        /// </summary>
        public Dictionary<uint, int> SectorErrorCounts { get; set; } = new Dictionary<uint, int>();

        /// <summary>
        /// Gets a dictionary of byte position error counts for error pattern analysis
        /// </summary>
        public Dictionary<int, int> BytePositionErrorCounts { get; set; } = new Dictionary<int, int>();

        /// <summary>
        /// Gets a dictionary of bit position error counts for error pattern analysis
        /// </summary>
        public Dictionary<int, int> BitPositionErrorCounts { get; set; } = new Dictionary<int, int>();

        /// <summary>
        /// Gets a dictionary of combined byte-bit position error counts for detailed error pattern analysis
        /// </summary>
        public Dictionary<string, int> DetailedErrorPatterns { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// Creates a new instance of the FlashHealthStatus class
        /// </summary>
        public FlashHealthStatus()
        {
            LastHealthCheckTimestamp = DateTime.Now;
            MaxProgramEraseCycles = 100000; // Default value for MC9S12XEP100

            // Initialize error pattern dictionaries
            BytePositionErrorCounts = new Dictionary<int, int>();
            BitPositionErrorCounts = new Dictionary<int, int>();
            DetailedErrorPatterns = new Dictionary<string, int>();
        }

        /// <summary>
        /// Adds a program/erase cycle to the specified sector
        /// </summary>
        /// <param name="sectorAddress">The sector address</param>
        public void AddProgramEraseCycle(uint sectorAddress)
        {
            TotalProgramEraseCycles++;

            if (SectorProgramEraseCycles.ContainsKey(sectorAddress))
            {
                SectorProgramEraseCycles[sectorAddress]++;
            }
            else
            {
                SectorProgramEraseCycles[sectorAddress] = 1;
            }
        }

        /// <summary>
        /// Adds an error to the specified sector
        /// </summary>
        /// <param name="sectorAddress">The sector address</param>
        /// <param name="isSingleBitError">Whether the error is a single-bit error</param>
        public void AddError(uint sectorAddress, bool isSingleBitError)
        {
            if (isSingleBitError)
            {
                SingleBitErrorCount++;
            }
            else
            {
                MultiBitErrorCount++;
            }

            if (SectorErrorCounts.ContainsKey(sectorAddress))
            {
                SectorErrorCounts[sectorAddress]++;
            }
            else
            {
                SectorErrorCounts[sectorAddress] = 1;
            }
        }

        /// <summary>
        /// Adds a bad block to the list
        /// </summary>
        /// <param name="blockAddress">The block address</param>
        public void AddBadBlock(uint blockAddress)
        {
            if (!BadBlocks.Contains(blockAddress))
            {
                BadBlocks.Add(blockAddress);
            }
        }

        /// <summary>
        /// Updates the timestamp of the last health check
        /// </summary>
        public void UpdateHealthCheckTimestamp()
        {
            LastHealthCheckTimestamp = DateTime.Now;
        }

        /// <summary>
        /// Updates the error pattern statistics with a new error
        /// </summary>
        /// <param name="bytePosition">The byte position of the error</param>
        /// <param name="bitPosition">The bit position of the error</param>
        public void UpdateErrorPatternStatistics(int bytePosition, int bitPosition)
        {
            // Update byte position statistics
            if (BytePositionErrorCounts.ContainsKey(bytePosition))
            {
                BytePositionErrorCounts[bytePosition]++;
            }
            else
            {
                BytePositionErrorCounts[bytePosition] = 1;
            }

            // Update bit position statistics
            if (BitPositionErrorCounts.ContainsKey(bitPosition))
            {
                BitPositionErrorCounts[bitPosition]++;
            }
            else
            {
                BitPositionErrorCounts[bitPosition] = 1;
            }

            // Update detailed error pattern statistics
            string pattern = $"{bytePosition}:{bitPosition}";
            if (DetailedErrorPatterns.ContainsKey(pattern))
            {
                DetailedErrorPatterns[pattern]++;
            }
            else
            {
                DetailedErrorPatterns[pattern] = 1;
            }

            // Analyze patterns to detect potential systematic issues
            AnalyzeErrorPatterns();
        }

        /// <summary>
        /// Analyzes error patterns to detect potential systematic issues
        /// </summary>
        private void AnalyzeErrorPatterns()
        {
            // Check for byte position patterns (same byte affected multiple times)
            foreach (var kvp in BytePositionErrorCounts)
            {
                if (kvp.Value > 5) // Threshold for considering it a pattern
                {
                    // If the same byte position has errors more than 5 times, it might indicate
                    // a systematic issue with that byte position
                    // This could be logged or reported for further investigation
                }
            }

            // Check for bit position patterns (same bit affected multiple times)
            foreach (var kvp in BitPositionErrorCounts)
            {
                if (kvp.Value > 10) // Threshold for considering it a pattern
                {
                    // If the same bit position has errors more than 10 times, it might indicate
                    // a systematic issue with that bit position
                    // This could be logged or reported for further investigation
                }
            }
        }
    }

    /// <summary>
    /// Flash memory health status level
    /// </summary>
    public enum HealthStatusEnum
    {
        /// <summary>
        /// Flash memory is in good health
        /// </summary>
        Good,

        /// <summary>
        /// Flash memory is in fair health
        /// </summary>
        Fair,

        /// <summary>
        /// Flash memory is showing signs of wear
        /// </summary>
        Warning,

        /// <summary>
        /// Flash memory is in critical condition
        /// </summary>
        Critical
    }


}
