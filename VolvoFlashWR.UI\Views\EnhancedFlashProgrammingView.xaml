<UserControl x:Class="VolvoFlashWR.UI.Views.EnhancedFlashProgrammingView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:VolvoFlashWR.UI.Views"
             xmlns:viewmodels="clr-namespace:VolvoFlashWR.UI.ViewModels"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <UserControl.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="120"/>
        </Style>

        <Style x:Key="InfoLabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,2"/>
        </Style>

        <Style x:Key="InfoValueStyle" TargetType="TextBlock">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,2"/>
        </Style>

        <Style x:Key="ProgressBarStyle" TargetType="ProgressBar">
            <Setter Property="Height" Value="15"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Grid Grid.Row="0" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- ECU Selection and Connection Status -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="Flash Programming" FontSize="20" FontWeight="Bold" Margin="0,0,0,10"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="Selected ECU:" Style="{StaticResource InfoLabelStyle}"/>
                        <ComboBox Grid.Column="1" ItemsSource="{Binding ConnectedECUs}"
                                  SelectedItem="{Binding SelectedECUForFlash}"
                                  DisplayMemberPath="Name" Margin="5,2"/>

                        <TextBlock Grid.Column="2" Text="Status:" Style="{StaticResource InfoLabelStyle}" Margin="15,2,5,2"/>
                        <Border Grid.Column="3" CornerRadius="3" Padding="5,2" Margin="5,2"
                                Background="{Binding SelectedECUForFlash.ConnectionStatus, Converter={StaticResource ConnectionStatusToBrushConverter}}">
                            <TextBlock Text="{Binding SelectedECUForFlash.ConnectionStatus}" Foreground="White"/>
                        </Border>
                    </Grid>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Vertical" Margin="10,0,0,0">
                    <Button Content="Refresh ECU List" Command="{Binding RefreshECUCommand}" Style="{StaticResource ActionButtonStyle}"/>
                    <Button Content="Connect" Command="{Binding ConnectToECUCommand}" Style="{StaticResource ActionButtonStyle}"/>
                    <Button Content="Disconnect" Command="{Binding DisconnectECUCommand}" Style="{StaticResource ActionButtonStyle}"/>
                </StackPanel>
            </Grid>

            <!-- Flash Operation Controls -->
            <Grid Grid.Row="1" Margin="0,10,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <Grid Margin="0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Operation Type:" Width="120" Style="{StaticResource InfoLabelStyle}"/>
                        <ComboBox Grid.Column="1" ItemsSource="{Binding OperationTypes}"
                                  SelectedItem="{Binding SelectedOperationType}" Margin="5,2"/>
                    </Grid>

                    <Grid Margin="0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="File Path:" Width="120" Style="{StaticResource InfoLabelStyle}"/>
                        <TextBox Grid.Column="1" Text="{Binding FilePath}" IsReadOnly="True" Margin="5,2"/>
                        <Button Grid.Column="2" Content="Browse..." Command="{Binding BrowseFileCommand}" Width="80" Margin="5,2"/>
                    </Grid>

                    <Grid Margin="0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Operation Status:" Width="120" Style="{StaticResource InfoLabelStyle}"/>
                        <TextBlock Grid.Column="1" Text="{Binding OperationStatus}" Style="{StaticResource InfoValueStyle}"/>
                    </Grid>

                    <Grid Margin="0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Progress:" Width="120" Style="{StaticResource InfoLabelStyle}"/>
                        <ProgressBar Grid.Column="1" Value="{Binding OperationProgress}" Style="{StaticResource ProgressBarStyle}"/>
                    </Grid>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Vertical" Margin="10,0,0,0">
                    <Button Content="Read EEPROM" Command="{Binding ReadEEPROMCommand}" Style="{StaticResource ActionButtonStyle}"/>
                    <Button Content="Write EEPROM" Command="{Binding WriteEEPROMCommand}" Style="{StaticResource ActionButtonStyle}"/>
                    <Button Content="Read MCU Code" Command="{Binding ReadMicrocontrollerCodeCommand}" Style="{StaticResource ActionButtonStyle}"/>
                    <Button Content="Write MCU Code" Command="{Binding WriteMicrocontrollerCodeCommand}" Style="{StaticResource ActionButtonStyle}"/>
                    <Button Content="Cancel Operation" Command="{Binding CancelOperationCommand}" Style="{StaticResource ActionButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Grid>

        <!-- Content Section -->
        <TabControl Grid.Row="1" Margin="10">
            <!-- Hex Viewer Tab -->
            <TabItem Header="Hex Viewer">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Address Navigation -->
                    <Grid Grid.Row="0" Margin="0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="Address:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <TextBox Grid.Column="1" Width="120" Text="{Binding CurrentAddress}" Margin="0,0,5,0"/>
                        <Button Grid.Column="2" Content="Go" Command="{Binding GoToAddressCommand}" Width="50" Margin="0,0,10,0"/>

                        <TextBlock Grid.Column="3" Text="Size:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <TextBlock Grid.Column="4" Text="{Binding DataSize, StringFormat='{}{0:N0} bytes'}" VerticalAlignment="Center"/>
                    </Grid>

                    <!-- Hex Data Grid -->
                    <DataGrid Grid.Row="1" ItemsSource="{Binding HexLines}"
                              AutoGenerateColumns="False" IsReadOnly="True"
                              AlternatingRowBackground="#F5F5F5"
                              VirtualizingPanel.IsVirtualizing="True"
                              VirtualizingPanel.VirtualizationMode="Recycling"
                              ScrollViewer.CanContentScroll="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Address" Binding="{Binding Address}" Width="100"/>
                            <DataGridTextColumn Header="Hex Data" Binding="{Binding HexData}" Width="*"/>
                            <DataGridTextColumn Header="ASCII" Binding="{Binding AsciiData}" Width="200"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
                        <Button Content="Save As..." Command="{Binding SaveDataCommand}" Style="{StaticResource ActionButtonStyle}"/>
                        <Button Content="Compare..." Command="{Binding CompareDataCommand}" Style="{StaticResource ActionButtonStyle}"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <!-- Operation Log Tab -->
            <TabItem Header="Operation Log">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Operation Info -->
                    <Grid Grid.Row="0" Margin="0,5,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="Current Operation:" Style="{StaticResource InfoLabelStyle}"/>
                        <TextBlock Grid.Column="1" Text="{Binding CurrentOperation}" Style="{StaticResource InfoValueStyle}"/>

                        <TextBlock Grid.Column="2" Text="Status:" Style="{StaticResource InfoLabelStyle}" Margin="20,2,5,2"/>
                        <TextBlock Grid.Column="3" Text="{Binding OperationStatus}" Style="{StaticResource InfoValueStyle}"/>
                    </Grid>

                    <!-- Log Entries -->
                    <DataGrid Grid.Row="1" ItemsSource="{Binding OperationLogs}"
                              AutoGenerateColumns="False" IsReadOnly="True"
                              AlternatingRowBackground="#F5F5F5">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Time" Binding="{Binding Timestamp, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" Width="150"/>
                            <DataGridTextColumn Header="Message" Binding="{Binding Message}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- ECU Details Tab -->
            <TabItem Header="ECU Details">
                <Grid Margin="5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Basic ECU Information -->
                    <GroupBox Grid.Column="0" Header="Basic Information" Margin="0,0,5,0">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <StackPanel>
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="ECU Name:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Grid.Column="1" Text="{Binding SelectedECUForFlash.Name}" Style="{StaticResource InfoValueStyle}"/>
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Serial Number:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Grid.Column="1" Text="{Binding SelectedECUForFlash.SerialNumber}" Style="{StaticResource InfoValueStyle}"/>
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Hardware Version:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Grid.Column="1" Text="{Binding SelectedECUForFlash.HardwareVersion}" Style="{StaticResource InfoValueStyle}"/>
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Software Version:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Grid.Column="1" Text="{Binding SelectedECUForFlash.SoftwareVersion}" Style="{StaticResource InfoValueStyle}"/>
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Protocol Type:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Grid.Column="1" Text="{Binding SelectedECUForFlash.ProtocolType}" Style="{StaticResource InfoValueStyle}"/>
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Last Connected:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Grid.Column="1" Text="{Binding SelectedECUForFlash.LastCommunicationTime, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" Style="{StaticResource InfoValueStyle}"/>
                                </Grid>
                            </StackPanel>
                        </ScrollViewer>
                    </GroupBox>

                    <!-- Memory Information -->
                    <GroupBox Grid.Column="1" Header="Memory Information" Margin="5,0,0,0">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <StackPanel>
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="EEPROM Size:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Grid.Column="1" Text="{Binding EEPROMSize, StringFormat='{}{0:N0} bytes'}" Style="{StaticResource InfoValueStyle}"/>
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="MCU Code Size:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Grid.Column="1" Text="{Binding MCUCodeSize, StringFormat='{}{0:N0} bytes'}" Style="{StaticResource InfoValueStyle}"/>
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Flash Memory Size:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Grid.Column="1" Text="{Binding FlashMemorySize, StringFormat='{}{0:N0} bytes'}" Style="{StaticResource InfoValueStyle}"/>
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="RAM Size:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Grid.Column="1" Text="{Binding RAMSize, StringFormat='{}{0:N0} bytes'}" Style="{StaticResource InfoValueStyle}"/>
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Bootloader Version:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Grid.Column="1" Text="{Binding BootloaderVersion}" Style="{StaticResource InfoValueStyle}"/>
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Security Level:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Grid.Column="1" Text="{Binding SecurityLevel}" Style="{StaticResource InfoValueStyle}"/>
                                </Grid>
                            </StackPanel>
                        </ScrollViewer>
                    </GroupBox>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
