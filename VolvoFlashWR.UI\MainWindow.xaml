﻿<Window x:Class="VolvoFlashWR.UI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:VolvoFlashWR.UI"
        xmlns:views="clr-namespace:VolvoFlashWR.UI.Views"
        xmlns:converters="clr-namespace:VolvoFlashWR.UI.Converters"
        xmlns:viewmodels="clr-namespace:VolvoFlashWR.UI.ViewModels"
        mc:Ignorable="d"
        Title="Volvo Flash WR - ECU Management Tool" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        WindowState="Normal"
        Topmost="True"
        ShowInTaskbar="True"
        Visibility="Visible">

    <Window.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <Style TargetType="Button">
            <Setter Property="Margin" Value="5" />
            <Setter Property="Padding" Value="10,5" />
            <Setter Property="MinWidth" Value="100" />
        </Style>
        <Style TargetType="TextBlock">
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>
        <Style TargetType="GroupBox">
            <Setter Property="Margin" Value="5" />
            <Setter Property="Padding" Value="5" />
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Menu -->
        <Menu Grid.Row="0">
            <MenuItem Header="_File">
                <MenuItem Header="E_xit" Click="ExitMenuItem_Click"/>
            </MenuItem>
            <MenuItem Header="_Tools">
                <MenuItem Header="_Settings" Command="{Binding OpenSettingsCommand}"/>
                <Separator/>
                <MenuItem Header="Flash Operation _Monitor" Click="FlashMonitorMenuItem_Click"/>
            </MenuItem>
            <MenuItem Header="_Help">
                <MenuItem Header="_License" Click="LicenseMenuItem_Click"/>
                <Separator/>
                <MenuItem Header="_About" Click="AboutMenuItem_Click"/>
            </MenuItem>
        </Menu>

        <!-- Header -->
        <Border Grid.Row="1" Background="#0066CC" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="Volvo Flash WR" FontSize="20" FontWeight="Bold" Foreground="White" />
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="Operating Mode: " Foreground="White" />
                    <ComboBox Width="120" Margin="5,0,0,0"
                              ItemsSource="{Binding OperatingModes}"
                              SelectedItem="{Binding SelectedOperatingMode}" />
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <TabControl Grid.Row="2" Margin="5">
            <!-- Connection Tab -->
            <TabItem Header="Connection">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!-- Connection Controls -->
                    <GroupBox Grid.Row="0" Header="Vocom Connection">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Connection Type:" Width="120" />
                                    <ComboBox Grid.Column="1" ItemsSource="{Binding ConnectionTypes}"
                                              SelectedItem="{Binding SelectedConnectionType}" />
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Selected Device:" Width="120" />
                                    <ComboBox Grid.Column="1" ItemsSource="{Binding VocomDevices}"
                                              SelectedItem="{Binding SelectedVocomDevice}"
                                              DisplayMemberPath="SerialNumber" />
                                </Grid>

                                <CheckBox Content="Auto-check Bluetooth" IsChecked="{Binding AutoCheckBluetooth}" Margin="120,5,0,0" />
                                <CheckBox Content="Use WiFi Fallback" IsChecked="{Binding UseWiFiFallback}" Margin="120,5,0,0" />
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Vertical">
                                <Button Content="Scan Devices" Command="{Binding ScanForVocomDevicesCommand}" />
                                <Button Content="Connect" Command="{Binding ConnectToVocomDeviceCommand}" />
                                <Button Content="Disconnect" Command="{Binding DisconnectVocomCommand}" />
                                <Button Content="Check PTT" Command="{Binding CheckPTTCommand}" />
                            </StackPanel>
                        </Grid>
                    </GroupBox>

                    <!-- Connection Status -->
                    <GroupBox Grid.Row="1" Header="Connection Status">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>

                            <Grid Grid.Row="0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="Status:" FontWeight="Bold" Margin="0,0,5,0" />
                                <TextBlock Grid.Column="1" Text="{Binding ConnectionStatus}" />

                                <TextBlock Grid.Column="2" Text="Device:" FontWeight="Bold" Margin="10,0,5,0" />
                                <TextBlock Grid.Column="3" Text="{Binding CurrentDeviceInfo}" />
                            </Grid>

                            <ListView Grid.Row="1" Margin="0,10,0,0" ItemsSource="{Binding ConnectionLogs}">
                                <ListView.View>
                                    <GridView>
                                        <GridViewColumn Header="Time" Width="150" DisplayMemberBinding="{Binding Timestamp}" />
                                        <GridViewColumn Header="Type" Width="80" DisplayMemberBinding="{Binding Type}" />
                                        <GridViewColumn Header="Message" Width="500" DisplayMemberBinding="{Binding Message}" />
                                    </GridView>
                                </ListView.View>
                            </ListView>
                        </Grid>
                    </GroupBox>
                </Grid>
            </TabItem>

            <!-- ECU Management Tab -->
            <TabItem Header="ECU Management">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!-- ECU Scanning Controls -->
                    <GroupBox Grid.Row="0" Header="ECU Scanning">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Selected ECU:" Width="120" />
                                    <ComboBox Grid.Column="1" ItemsSource="{Binding ECUDevices}"
                                              SelectedItem="{Binding SelectedECUDevice}"
                                              DisplayMemberPath="Name" />
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Protocol Type:" Width="120" />
                                    <ComboBox Grid.Column="1" ItemsSource="{Binding ProtocolTypes}"
                                              SelectedItem="{Binding SelectedProtocolType}" />
                                </Grid>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Vertical">
                                <Button Content="Scan ECUs" Command="{Binding ScanForECUsCommand}" />
                                <Button Content="Connect" Command="{Binding ConnectToECUCommand}" />
                                <Button Content="Disconnect" Command="{Binding DisconnectECUCommand}" />
                                <Button Content="Refresh" Command="{Binding RefreshECUCommand}" />
                            </StackPanel>
                        </Grid>
                    </GroupBox>

                    <!-- ECU Details -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!-- ECU List -->
                        <GroupBox Grid.Column="0" Header="Available ECUs">
                            <ListView ItemsSource="{Binding ECUDevices}"
                                      SelectedItem="{Binding SelectedECUDevice}">
                                <ListView.View>
                                    <GridView>
                                        <GridViewColumn Header="Name" Width="100" DisplayMemberBinding="{Binding Name}" />
                                        <GridViewColumn Header="Serial" Width="120" DisplayMemberBinding="{Binding SerialNumber}" />
                                        <GridViewColumn Header="Status" Width="100" DisplayMemberBinding="{Binding ConnectionStatus}" />
                                        <GridViewColumn Header="Protocol" Width="80" DisplayMemberBinding="{Binding ProtocolType}" />
                                    </GridView>
                                </ListView.View>
                            </ListView>
                        </GroupBox>

                        <!-- ECU Details -->
                        <GroupBox Grid.Column="1" Header="ECU Details">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <StackPanel Visibility="{Binding IsECUSelected, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Name:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedECUDevice.Name}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Serial Number:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedECUDevice.SerialNumber}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Hardware Version:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedECUDevice.HardwareVersion}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Software Version:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedECUDevice.SoftwareVersion}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Microcontroller:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedECUDevice.MicrocontrollerType}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="EEPROM Size:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedECUDevice.EEPROMSize, StringFormat='{}{0:N0} bytes'}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Flash Size:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedECUDevice.FlashSize, StringFormat='{}{0:N0} bytes'}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="RAM Size:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedECUDevice.RAMSize, StringFormat='{}{0:N0} bytes'}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Protocol Type:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedECUDevice.ProtocolType}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Connection Status:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedECUDevice.ConnectionStatus}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="High Speed Comm:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedECUDevice.SupportsHighSpeedCommunication}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Low Speed Comm:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedECUDevice.SupportsLowSpeedCommunication}" />
                                    </Grid>
                                </StackPanel>
                            </ScrollViewer>
                        </GroupBox>
                    </Grid>
                </Grid>
            </TabItem>

            <!-- Enhanced Flash Programming Tab -->
            <TabItem Header="Flash Programming">
                <views:EnhancedFlashProgrammingView DataContext="{Binding EnhancedFlashProgrammingViewModel}" />
            </TabItem>

            <!-- ECU Flash Operations Tab -->
            <TabItem Header="ECU Flash">
                <views:ECUFlashView />
            </TabItem>

            <!-- Enhanced Diagnostics Tab -->
            <TabItem Header="Diagnostics">
                <views:EnhancedDiagnosticsView DataContext="{Binding EnhancedDiagnosticsViewModel}" />
            </TabItem>

            <!-- Backup Tab -->
            <TabItem Header="Backup">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!-- Backup Controls -->
                    <GroupBox Grid.Row="0" Header="Backup Controls">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Selected ECU:" Width="120" />
                                    <ComboBox Grid.Column="1" ItemsSource="{Binding ConnectedECUs}"
                                              SelectedItem="{Binding SelectedECUForBackup}"
                                              DisplayMemberPath="Name" />
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Description:" Width="120" />
                                    <TextBox Grid.Column="1" Text="{Binding BackupDescription}" />
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Category:" Width="120" />
                                    <ComboBox Grid.Column="1" ItemsSource="{Binding BackupCategories}"
                                              SelectedItem="{Binding SelectedBackupCategory}"
                                              IsEditable="True" />
                                </Grid>

                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Tags:" Width="120" />
                                    <TextBox Grid.Column="1" Text="{Binding BackupTags}"
                                             ToolTip="Enter tags separated by commas" />
                                </Grid>

                                <StackPanel Orientation="Horizontal" Margin="120,5,0,0">
                                    <CheckBox Content="Include EEPROM" IsChecked="{Binding IncludeEEPROM}" Margin="0,0,10,0" />
                                    <CheckBox Content="Include MCU Code" IsChecked="{Binding IncludeMicrocontrollerCode}" Margin="0,0,10,0" />
                                    <CheckBox Content="Include Parameters" IsChecked="{Binding IncludeParameters}" />
                                </StackPanel>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Vertical">
                                <Button Content="Create Backup" Command="{Binding CreateBackupCommand}" />
                                <Button Content="Restore Backup" Command="{Binding RestoreBackupCommand}" />
                                <Button Content="Manage Versions" Command="{Binding ManageVersionsCommand}" />
                                <Button Content="Schedule Backups" Command="{Binding ScheduleBackupsCommand}" />
                            </StackPanel>
                        </Grid>
                    </GroupBox>

                    <!-- Backup List and Details -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!-- Backup List -->
                        <GroupBox Grid.Column="0" Header="Available Backups">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>

                                <!-- Filter Controls -->
                                <Grid Grid.Row="0" Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0" Text="Filter:" VerticalAlignment="Center" Margin="0,0,5,0" />
                                    <TextBox Grid.Column="1" Text="{Binding BackupFilter, UpdateSourceTrigger=PropertyChanged}" />
                                    <Button Grid.Column="2" Content="Clear" Command="{Binding ClearBackupFilterCommand}" Width="60" Margin="5,0,0,0" />
                                </Grid>

                                <!-- Backup List -->
                                <ListView Grid.Row="1" ItemsSource="{Binding Backups}"
                                          SelectedItem="{Binding SelectedBackup}">
                                    <ListView.View>
                                        <GridView>
                                            <GridViewColumn Header="ECU" Width="80" DisplayMemberBinding="{Binding ECUName}" />
                                            <GridViewColumn Header="Date" Width="150" DisplayMemberBinding="{Binding CreationTime, StringFormat='{}{0:yyyy-MM-dd HH:mm}'}" />
                                            <GridViewColumn Header="Version" Width="60" DisplayMemberBinding="{Binding Version}" />
                                            <GridViewColumn Header="Category" Width="100" DisplayMemberBinding="{Binding Category}" />
                                            <GridViewColumn Header="Description" Width="200" DisplayMemberBinding="{Binding Description}" />
                                        </GridView>
                                    </ListView.View>
                                </ListView>
                            </Grid>
                        </GroupBox>

                        <!-- Backup Details -->
                        <GroupBox Grid.Column="1" Header="Backup Details">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <StackPanel Visibility="{Binding IsBackupSelected, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="ECU Name:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.ECUName}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="ECU Serial Number:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.ECUSerialNumber}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Hardware Version:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.ECUHardwareVersion}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Software Version:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.ECUSoftwareVersion}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Creation Time:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.CreationTime}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Created By:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.CreatedBy}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Last Modified:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.LastModifiedTime}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Modified By:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.LastModifiedBy}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Version:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.Version}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Category:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.Category}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Tags:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackupTags}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Description:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.Description}" TextWrapping="Wrap" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Size:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.SizeInBytes, StringFormat='{}{0:N0} bytes'}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Contains EEPROM:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding HasEEPROMData}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Contains MCU Code:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding HasMicrocontrollerCode}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Contains Parameters:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding HasParameters}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Is Compressed:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.IsCompressed}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Is Encrypted:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.IsEncrypted}" />
                                    </Grid>

                                    <Grid Margin="0,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="150" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="File Path:" FontWeight="Bold" />
                                        <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.FilePath}" TextWrapping="Wrap" />
                                    </Grid>

                                    <!-- Action Buttons -->
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
                                        <Button Content="Create New Version" Command="{Binding CreateBackupVersionCommand}" />
                                        <Button Content="Export Backup" Command="{Binding ExportBackupCommand}" />
                                        <Button Content="Delete Backup" Command="{Binding DeleteBackupCommand}" />
                                    </StackPanel>
                                </StackPanel>
                            </ScrollViewer>
                        </GroupBox>
                    </Grid>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Status Bar -->
        <StatusBar Grid.Row="3">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" />
            </StatusBarItem>
            <Separator />
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="Vocom: " />
                    <TextBlock Text="{Binding VocomStatus}" />
                </StackPanel>
            </StatusBarItem>
            <Separator />
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="ECUs Connected: " />
                    <TextBlock Text="{Binding ConnectedECUCount}" />
                </StackPanel>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <ProgressBar Width="100" Height="15" IsIndeterminate="{Binding IsBusy}"
                             Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}" />
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
