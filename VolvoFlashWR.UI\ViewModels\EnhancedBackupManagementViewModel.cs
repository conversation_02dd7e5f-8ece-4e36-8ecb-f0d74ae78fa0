using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Microsoft.Win32;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.UI.Commands;
using VolvoFlashWR.UI.Models;

namespace VolvoFlashWR.UI.ViewModels
{
    public class EnhancedBackupManagementViewModel : INotifyPropertyChanged
    {
        #region Private Fields

        private readonly ILoggingService _loggingService;
        private readonly IBackupService _backupService;
        private readonly IECUCommunicationService _ecuCommunicationService;
        private readonly IDialogService _dialogService;

        private ObservableCollection<ECUDevice> _connectedECUs;
        private ObservableCollection<BackupData> _backups;
        private ObservableCollection<BackupSchedule> _scheduledBackups;
        private ECUDevice _selectedECUForBackup;
        private BackupData _selectedBackup;
        private BackupData _selectedVersion;
        private BackupVersionTree _versionTree;
        private string _backupDescription;
        private string _backupTags;
        private string _selectedBackupCategory;
        private string _backupFilter;
        private bool _includeEEPROM;
        private bool _includeMicrocontrollerCode;
        private bool _includeParameters;
        private bool _useCompression;
        private bool _useEncryption;
        private bool _autoVersion;
        private bool _isCreatingBackup;
        private bool _isRestoringBackup;
        private string _statusMessage;

        // Retention policy properties
        private bool _useMinimumRetentionTime;
        private int _minimumRetentionDays;
        private bool _useLimitBackupCount;
        private int _maximumBackupCount;
        private bool _useMaximumRetentionTime;
        private int _maximumRetentionDays;
        private bool _keepAllVersionsForPeriod;
        private int _keepAllVersionsDays;
        private bool _limitVersionCount;
        private int _maxVersionCount;
        private bool _limitTotalStorage;
        private int _maxStorageMB;
        private bool _autoCompressOldBackups;
        private int _compressBackupsAfterDays;

        // Version comparison properties
        private ObservableCollection<BackupVersionInfo> _versionsForComparison;
        private BackupVersionInfo _selectedVersionForComparisonA;
        private BackupVersionInfo _selectedVersionForComparisonB;
        private ObservableCollection<ParameterComparisonResult> _comparisonResults;
        private int _timelineZoom;
        private string _selectedVersionViewMode;
        private ObservableCollection<ScheduleHistoryEntry> _scheduleHistory;
        private BackupSchedule _selectedSchedule;

        #endregion

        #region Properties

        public event PropertyChangedEventHandler? PropertyChanged;

        public ObservableCollection<ECUDevice> ConnectedECUs
        {
            get => _connectedECUs;
            set
            {
                _connectedECUs = value;
                OnPropertyChanged(nameof(ConnectedECUs));
            }
        }

        public ObservableCollection<BackupData> Backups
        {
            get => _backups;
            set
            {
                _backups = value;
                OnPropertyChanged(nameof(Backups));
            }
        }

        public ObservableCollection<BackupSchedule> ScheduledBackups
        {
            get => _scheduledBackups;
            set
            {
                _scheduledBackups = value;
                OnPropertyChanged(nameof(ScheduledBackups));
            }
        }

        public ECUDevice SelectedECUForBackup
        {
            get => _selectedECUForBackup;
            set
            {
                _selectedECUForBackup = value;
                OnPropertyChanged(nameof(SelectedECUForBackup));
                UpdateCommandStates();

                // Load backups for the selected ECU
                if (_selectedECUForBackup != null)
                {
                    LoadBackupsForSelectedECUAsync().ConfigureAwait(false);
                }
            }
        }

        public BackupData SelectedBackup
        {
            get => _selectedBackup;
            set
            {
                _selectedBackup = value;
                OnPropertyChanged(nameof(SelectedBackup));
                OnPropertyChanged(nameof(IsBackupSelected));
                OnPropertyChanged(nameof(SelectedBackupTags));
                OnPropertyChanged(nameof(HasEEPROMData));
                OnPropertyChanged(nameof(HasMicrocontrollerCode));
                OnPropertyChanged(nameof(HasParameters));
                UpdateCommandStates();

                // Load version tree for the selected backup
                if (_selectedBackup != null)
                {
                    LoadVersionTreeAsync().ConfigureAwait(false);
                }
            }
        }

        public BackupData SelectedVersion
        {
            get => _selectedVersion;
            set
            {
                _selectedVersion = value;
                OnPropertyChanged(nameof(SelectedVersion));
                UpdateCommandStates();
            }
        }

        public BackupVersionTree VersionTree
        {
            get => _versionTree;
            set
            {
                _versionTree = value;
                OnPropertyChanged(nameof(VersionTree));
            }
        }

        public string BackupDescription
        {
            get => _backupDescription;
            set
            {
                _backupDescription = value;
                OnPropertyChanged(nameof(BackupDescription));
            }
        }

        public string BackupTags
        {
            get => _backupTags;
            set
            {
                _backupTags = value;
                OnPropertyChanged(nameof(BackupTags));
            }
        }

        public string SelectedBackupCategory
        {
            get => _selectedBackupCategory;
            set
            {
                _selectedBackupCategory = value;
                OnPropertyChanged(nameof(SelectedBackupCategory));
            }
        }

        public string BackupFilter
        {
            get => _backupFilter;
            set
            {
                _backupFilter = value;
                OnPropertyChanged(nameof(BackupFilter));
                ApplyBackupFilter();
            }
        }

        public bool IncludeEEPROM
        {
            get => _includeEEPROM;
            set
            {
                _includeEEPROM = value;
                OnPropertyChanged(nameof(IncludeEEPROM));
            }
        }

        public bool IncludeMicrocontrollerCode
        {
            get => _includeMicrocontrollerCode;
            set
            {
                _includeMicrocontrollerCode = value;
                OnPropertyChanged(nameof(IncludeMicrocontrollerCode));
            }
        }

        public bool IncludeParameters
        {
            get => _includeParameters;
            set
            {
                _includeParameters = value;
                OnPropertyChanged(nameof(IncludeParameters));
            }
        }

        public bool UseCompression
        {
            get => _useCompression;
            set
            {
                _useCompression = value;
                OnPropertyChanged(nameof(UseCompression));

                // Update the backup service setting
                if (_backupService != null)
                {
                    _backupService.UseCompression = value;
                }
            }
        }

        public bool UseEncryption
        {
            get => _useEncryption;
            set
            {
                _useEncryption = value;
                OnPropertyChanged(nameof(UseEncryption));

                // Update the backup service setting
                if (_backupService != null)
                {
                    _backupService.UseEncryption = value;
                }
            }
        }

        public bool AutoVersion
        {
            get => _autoVersion;
            set
            {
                _autoVersion = value;
                OnPropertyChanged(nameof(AutoVersion));
            }
        }

        public bool IsCreatingBackup
        {
            get => _isCreatingBackup;
            set
            {
                _isCreatingBackup = value;
                OnPropertyChanged(nameof(IsCreatingBackup));
                UpdateCommandStates();
            }
        }

        public bool IsRestoringBackup
        {
            get => _isRestoringBackup;
            set
            {
                _isRestoringBackup = value;
                OnPropertyChanged(nameof(IsRestoringBackup));
                UpdateCommandStates();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged(nameof(StatusMessage));
            }
        }

        public bool IsBackupSelected => SelectedBackup != null;

        public string SelectedBackupTags => SelectedBackup != null && SelectedBackup.Tags != null && SelectedBackup.Tags.Count > 0
            ? string.Join(", ", SelectedBackup.Tags)
            : "None";

        public string HasEEPROMData => SelectedBackup != null && SelectedBackup.EEPROMData != null && SelectedBackup.EEPROMData.Length > 0
            ? "Yes"
            : "No";

        public string HasMicrocontrollerCode => SelectedBackup != null && SelectedBackup.MicrocontrollerCode != null && SelectedBackup.MicrocontrollerCode.Length > 0
            ? "Yes"
            : "No";

        public string HasParameters => SelectedBackup != null && SelectedBackup.Parameters != null && SelectedBackup.Parameters.Count > 0
            ? "Yes"
            : "No";

        public ObservableCollection<string> BackupCategories { get; } = new ObservableCollection<string>();

        public IBackupService BackupService => _backupService;

        #region Retention Policy Properties

        public bool UseMinimumRetentionTime
        {
            get => _useMinimumRetentionTime;
            set
            {
                _useMinimumRetentionTime = value;
                OnPropertyChanged(nameof(UseMinimumRetentionTime));
            }
        }

        public int MinimumRetentionDays
        {
            get => _minimumRetentionDays;
            set
            {
                _minimumRetentionDays = value;
                OnPropertyChanged(nameof(MinimumRetentionDays));
            }
        }

        public bool UseLimitBackupCount
        {
            get => _useLimitBackupCount;
            set
            {
                _useLimitBackupCount = value;
                OnPropertyChanged(nameof(UseLimitBackupCount));
            }
        }

        public int MaximumBackupCount
        {
            get => _maximumBackupCount;
            set
            {
                _maximumBackupCount = value;
                OnPropertyChanged(nameof(MaximumBackupCount));
            }
        }

        public bool UseMaximumRetentionTime
        {
            get => _useMaximumRetentionTime;
            set
            {
                _useMaximumRetentionTime = value;
                OnPropertyChanged(nameof(UseMaximumRetentionTime));
            }
        }

        public int MaximumRetentionDays
        {
            get => _maximumRetentionDays;
            set
            {
                _maximumRetentionDays = value;
                OnPropertyChanged(nameof(MaximumRetentionDays));
            }
        }

        public bool KeepAllVersionsForPeriod
        {
            get => _keepAllVersionsForPeriod;
            set
            {
                _keepAllVersionsForPeriod = value;
                OnPropertyChanged(nameof(KeepAllVersionsForPeriod));
            }
        }

        public int KeepAllVersionsDays
        {
            get => _keepAllVersionsDays;
            set
            {
                _keepAllVersionsDays = value;
                OnPropertyChanged(nameof(KeepAllVersionsDays));
            }
        }

        public bool LimitVersionCount
        {
            get => _limitVersionCount;
            set
            {
                _limitVersionCount = value;
                OnPropertyChanged(nameof(LimitVersionCount));
            }
        }

        public int MaxVersionCount
        {
            get => _maxVersionCount;
            set
            {
                _maxVersionCount = value;
                OnPropertyChanged(nameof(MaxVersionCount));
            }
        }

        public bool LimitTotalStorage
        {
            get => _limitTotalStorage;
            set
            {
                _limitTotalStorage = value;
                OnPropertyChanged(nameof(LimitTotalStorage));
            }
        }

        public int MaxStorageMB
        {
            get => _maxStorageMB;
            set
            {
                _maxStorageMB = value;
                OnPropertyChanged(nameof(MaxStorageMB));
            }
        }

        public bool AutoCompressOldBackups
        {
            get => _autoCompressOldBackups;
            set
            {
                _autoCompressOldBackups = value;
                OnPropertyChanged(nameof(AutoCompressOldBackups));
            }
        }

        public int CompressBackupsAfterDays
        {
            get => _compressBackupsAfterDays;
            set
            {
                _compressBackupsAfterDays = value;
                OnPropertyChanged(nameof(CompressBackupsAfterDays));
            }
        }

        #endregion

        #region Version Comparison Properties

        public ObservableCollection<BackupVersionInfo> VersionsForComparison
        {
            get => _versionsForComparison;
            set
            {
                _versionsForComparison = value;
                OnPropertyChanged(nameof(VersionsForComparison));
            }
        }

        public BackupVersionInfo SelectedVersionForComparisonA
        {
            get => _selectedVersionForComparisonA;
            set
            {
                _selectedVersionForComparisonA = value;
                OnPropertyChanged(nameof(SelectedVersionForComparisonA));
                UpdateComparisonResults();
            }
        }

        public BackupVersionInfo SelectedVersionForComparisonB
        {
            get => _selectedVersionForComparisonB;
            set
            {
                _selectedVersionForComparisonB = value;
                OnPropertyChanged(nameof(SelectedVersionForComparisonB));
                UpdateComparisonResults();
            }
        }

        public ObservableCollection<ParameterComparisonResult> ComparisonResults
        {
            get => _comparisonResults;
            set
            {
                _comparisonResults = value;
                OnPropertyChanged(nameof(ComparisonResults));
            }
        }

        public int TimelineZoom
        {
            get => _timelineZoom;
            set
            {
                _timelineZoom = value;
                OnPropertyChanged(nameof(TimelineZoom));
            }
        }

        public string SelectedVersionViewMode
        {
            get => _selectedVersionViewMode;
            set
            {
                _selectedVersionViewMode = value;
                OnPropertyChanged(nameof(SelectedVersionViewMode));
            }
        }

        public List<string> ViewModes => new List<string> { "Tree", "Timeline", "List" };

        public ObservableCollection<ScheduleHistoryEntry> ScheduleHistory
        {
            get => _scheduleHistory;
            set
            {
                _scheduleHistory = value;
                OnPropertyChanged(nameof(ScheduleHistory));
            }
        }

        public BackupSchedule SelectedSchedule
        {
            get => _selectedSchedule;
            set
            {
                _selectedSchedule = value;
                OnPropertyChanged(nameof(SelectedSchedule));
                UpdateCommandStates();
            }
        }

        #endregion

        #endregion

        #region Commands

        public ICommand RefreshECUCommand { get; private set; }
        public ICommand ConnectToECUCommand { get; private set; }
        public ICommand DisconnectECUCommand { get; private set; }
        public ICommand CreateBackupCommand { get; private set; }
        public ICommand RestoreBackupCommand { get; private set; }
        public ICommand ExportBackupCommand { get; private set; }
        public ICommand ImportBackupCommand { get; private set; }
        public ICommand DeleteBackupCommand { get; private set; }
        public ICommand RefreshBackupsCommand { get; private set; }
        public ICommand ClearBackupFilterCommand { get; private set; }
        public ICommand CreateNewVersionCommand { get; private set; }
        public ICommand CompareVersionsCommand { get; private set; }
        public ICommand AddScheduleCommand { get; private set; }
        public ICommand EditScheduleCommand { get; private set; }
        public ICommand RemoveScheduleCommand { get; private set; }

        // New commands for enhanced backup management
        public ICommand RunAllSchedulesCommand { get; private set; }
        public ICommand RunScheduleNowCommand { get; private set; }
        public ICommand DisableScheduleCommand { get; private set; }
        public ICommand ApplyRetentionPolicyCommand { get; private set; }
        public ICommand ResetRetentionPolicyCommand { get; private set; }
        public ICommand ExportComparisonCommand { get; private set; }
        public ICommand SetAsBaselineCommand { get; private set; }

        #endregion

        #region Constructor

        public EnhancedBackupManagementViewModel(
            ILoggingService loggingService,
            IBackupService backupService,
            IECUCommunicationService ecuCommunicationService,
            IDialogService dialogService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));
            _ecuCommunicationService = ecuCommunicationService ?? throw new ArgumentNullException(nameof(ecuCommunicationService));
            _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));

            // Initialize collections
            ConnectedECUs = new ObservableCollection<ECUDevice>();
            Backups = new ObservableCollection<BackupData>();
            ScheduledBackups = new ObservableCollection<BackupSchedule>();
            VersionsForComparison = new ObservableCollection<BackupVersionInfo>();
            ComparisonResults = new ObservableCollection<ParameterComparisonResult>();
            ScheduleHistory = new ObservableCollection<ScheduleHistoryEntry>();

            // Initialize default values
            IncludeEEPROM = true;
            IncludeMicrocontrollerCode = true;
            IncludeParameters = true;
            UseCompression = _backupService.UseCompression;
            UseEncryption = _backupService.UseEncryption;
            AutoVersion = true;
            StatusMessage = "Ready";

            // Initialize retention policy defaults
            UseMinimumRetentionTime = true;
            MinimumRetentionDays = 30;
            UseLimitBackupCount = true;
            MaximumBackupCount = 10;
            UseMaximumRetentionTime = true;
            MaximumRetentionDays = 365;
            KeepAllVersionsForPeriod = true;
            KeepAllVersionsDays = 7;
            LimitVersionCount = true;
            MaxVersionCount = 5;
            LimitTotalStorage = false;
            MaxStorageMB = 1000;
            AutoCompressOldBackups = true;
            CompressBackupsAfterDays = 30;

            // Initialize version comparison defaults
            TimelineZoom = 5;
            SelectedVersionViewMode = ViewModes[0]; // Default to Tree view

            // Initialize commands
            InitializeCommands();

            // Load connected ECUs
            LoadConnectedECUs();

            // Load backup categories
            LoadBackupCategories();

            // Initialize the backup service
            InitializeBackupServiceAsync().ConfigureAwait(false);
        }

        #region Private Methods

        /// <summary>
        /// Notifies that a property value has changed
        /// </summary>
        /// <param name="propertyName">Name of the property that changed</param>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Updates the command states based on current conditions
        /// </summary>
        private void UpdateCommandStates()
        {
            // Update command can-execute states based on current conditions
            var createBackupCommand = CreateBackupCommand as AsyncRelayCommand;
            if (createBackupCommand != null)
            {
                createBackupCommand.RaiseCanExecuteChanged();
            }

            var restoreBackupCommand = RestoreBackupCommand as AsyncRelayCommand;
            if (restoreBackupCommand != null)
            {
                restoreBackupCommand.RaiseCanExecuteChanged();
            }

            var exportBackupCommand = ExportBackupCommand as AsyncRelayCommand;
            if (exportBackupCommand != null)
            {
                exportBackupCommand.RaiseCanExecuteChanged();
            }

            var deleteBackupCommand = DeleteBackupCommand as AsyncRelayCommand;
            if (deleteBackupCommand != null)
            {
                deleteBackupCommand.RaiseCanExecuteChanged();
            }

            var createNewVersionCommand = CreateNewVersionCommand as AsyncRelayCommand;
            if (createNewVersionCommand != null)
            {
                createNewVersionCommand.RaiseCanExecuteChanged();
            }

            var compareVersionsCommand = CompareVersionsCommand as AsyncRelayCommand;
            if (compareVersionsCommand != null)
            {
                compareVersionsCommand.RaiseCanExecuteChanged();
            }

            var runScheduleNowCommand = RunScheduleNowCommand as AsyncRelayCommand;
            if (runScheduleNowCommand != null)
            {
                runScheduleNowCommand.RaiseCanExecuteChanged();
            }

            var disableScheduleCommand = DisableScheduleCommand as AsyncRelayCommand;
            if (disableScheduleCommand != null)
            {
                disableScheduleCommand.RaiseCanExecuteChanged();
            }

            var removeScheduleCommand = RemoveScheduleCommand as AsyncRelayCommand;
            if (removeScheduleCommand != null)
            {
                removeScheduleCommand.RaiseCanExecuteChanged();
            }
        }

        /// <summary>
        /// Loads backups for the selected ECU
        /// </summary>
        private async Task LoadBackupsForSelectedECUAsync()
        {
            try
            {
                if (SelectedECUForBackup == null)
                {
                    Backups.Clear();
                    return;
                }

                StatusMessage = $"Loading backups for {SelectedECUForBackup.Name}...";
                _loggingService.LogInformation($"Loading backups for ECU {SelectedECUForBackup.Name}", "EnhancedBackupManagementViewModel");

                // Get backups for the selected ECU
                var backups = await _backupService.GetBackupsForECUAsync(SelectedECUForBackup.Id);

                // Update the backups collection
                Backups.Clear();
                foreach (var backup in backups)
                {
                    Backups.Add(backup);
                }

                StatusMessage = $"Loaded {backups.Count} backups for {SelectedECUForBackup.Name}";
                _loggingService.LogInformation($"Loaded {backups.Count} backups for ECU {SelectedECUForBackup.Name}", "EnhancedBackupManagementViewModel");

                // Apply any filter
                if (!string.IsNullOrEmpty(BackupFilter))
                {
                    ApplyBackupFilter();
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Error loading backups for ECU {SelectedECUForBackup?.Name}", "EnhancedBackupManagementViewModel", ex);
                StatusMessage = $"Error loading backups: {ex.Message}";
            }
        }

        /// <summary>
        /// Loads the version tree for the selected backup
        /// </summary>
        private async Task LoadVersionTreeAsync()
        {
            try
            {
                if (SelectedBackup == null)
                {
                    VersionTree = null;
                    VersionsForComparison.Clear();
                    return;
                }

                StatusMessage = $"Loading version history for backup {SelectedBackup.Id}...";
                _loggingService.LogInformation($"Loading version history for backup {SelectedBackup.Id}", "EnhancedBackupManagementViewModel");

                // Get the version tree for the selected backup
                var versionTree = await _backupService.GetBackupVersionTreeAsync(SelectedBackup.Id);
                VersionTree = versionTree;

                // Update the versions for comparison
                VersionsForComparison.Clear();
                if (versionTree != null && versionTree.AllVersions != null)
                {
                    foreach (var version in versionTree.AllVersions)
                    {
                        VersionsForComparison.Add(new BackupVersionInfo(version));
                    }
                }

                StatusMessage = $"Loaded version history for backup {SelectedBackup.Id}";
                _loggingService.LogInformation($"Loaded version history for backup {SelectedBackup.Id}", "EnhancedBackupManagementViewModel");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Error loading version history for backup {SelectedBackup?.Id}", "EnhancedBackupManagementViewModel", ex);
                StatusMessage = $"Error loading version history: {ex.Message}";
            }
        }

        /// <summary>
        /// Applies the backup filter to the backups collection
        /// </summary>
        private void ApplyBackupFilter()
        {
            try
            {
                if (string.IsNullOrEmpty(BackupFilter))
                {
                    // If no filter, reload all backups
                    LoadBackupsForSelectedECUAsync().ConfigureAwait(false);
                    return;
                }

                StatusMessage = $"Filtering backups with '{BackupFilter}'...";
                _loggingService.LogInformation($"Filtering backups with '{BackupFilter}'", "EnhancedBackupManagementViewModel");

                // Get all backups for the selected ECU
                var allBackups = _backupService.GetBackupsForECUAsync(SelectedECUForBackup.Id).Result;

                // Filter backups based on the filter text
                var filteredBackups = allBackups.Where(b =>
                    b.Description.Contains(BackupFilter, StringComparison.OrdinalIgnoreCase) ||
                    b.Category.Contains(BackupFilter, StringComparison.OrdinalIgnoreCase) ||
                    b.Tags.Any(t => t.Contains(BackupFilter, StringComparison.OrdinalIgnoreCase)) ||
                    b.CreationTime.ToString().Contains(BackupFilter, StringComparison.OrdinalIgnoreCase)
                ).ToList();

                // Update the backups collection
                Backups.Clear();
                foreach (var backup in filteredBackups)
                {
                    Backups.Add(backup);
                }

                StatusMessage = $"Found {filteredBackups.Count} backups matching '{BackupFilter}'";
                _loggingService.LogInformation($"Found {filteredBackups.Count} backups matching '{BackupFilter}'", "EnhancedBackupManagementViewModel");
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"Error filtering backups with '{BackupFilter}'", "EnhancedBackupManagementViewModel", ex);
                StatusMessage = $"Error filtering backups: {ex.Message}";
            }
        }

        /// <summary>
        /// Initializes the commands
        /// </summary>
        private void InitializeCommands()
        {
            RefreshECUCommand = new AsyncRelayCommand(RefreshECUAsync);
            ConnectToECUCommand = new AsyncRelayCommand(ConnectToECUAsync, CanConnectToECU);
            DisconnectECUCommand = new AsyncRelayCommand(DisconnectECUAsync, CanDisconnectECU);
            CreateBackupCommand = new AsyncRelayCommand(CreateBackupAsync, CanCreateBackup);
            RestoreBackupCommand = new AsyncRelayCommand(RestoreBackupAsync, CanRestoreBackup);
            ExportBackupCommand = new AsyncRelayCommand(ExportBackupAsync, CanExportBackup);
            ImportBackupCommand = new AsyncRelayCommand(ImportBackupAsync);
            DeleteBackupCommand = new AsyncRelayCommand(DeleteBackupAsync, CanDeleteBackup);
            RefreshBackupsCommand = new AsyncRelayCommand(RefreshBackupsAsync);
            ClearBackupFilterCommand = new RelayCommand(param => ClearBackupFilter());
            CreateNewVersionCommand = new AsyncRelayCommand(CreateNewVersionAsync, CanCreateNewVersion);
            CompareVersionsCommand = new AsyncRelayCommand(CompareVersionsAsync, CanCompareVersions);
            AddScheduleCommand = new AsyncRelayCommand(AddScheduleAsync);
            EditScheduleCommand = new AsyncRelayCommand(EditScheduleAsync, CanEditSchedule);
            RemoveScheduleCommand = new AsyncRelayCommand(RemoveScheduleAsync, CanRemoveSchedule);
            RunAllSchedulesCommand = new AsyncRelayCommand(RunAllSchedulesAsync);
            RunScheduleNowCommand = new AsyncRelayCommand(RunScheduleNowAsync, CanRunScheduleNow);
            DisableScheduleCommand = new AsyncRelayCommand(DisableScheduleAsync, CanDisableSchedule);
            ApplyRetentionPolicyCommand = new AsyncRelayCommand(ApplyRetentionPolicyAsync);
            ResetRetentionPolicyCommand = new RelayCommand(param => ResetRetentionPolicy());
            ExportComparisonCommand = new AsyncRelayCommand(ExportComparisonAsync, CanExportComparison);
            SetAsBaselineCommand = new AsyncRelayCommand(SetAsBaselineAsync, CanSetAsBaseline);
        }

        /// <summary>
        /// Loads the connected ECUs
        /// </summary>
        private void LoadConnectedECUs()
        {
            try
            {
                _loggingService.LogInformation("Loading connected ECUs", "EnhancedBackupManagementViewModel");

                // Get connected ECUs from the ECU communication service
                var connectedECUs = _ecuCommunicationService.ConnectedECUs;

                // Update the connected ECUs collection
                ConnectedECUs.Clear();
                foreach (var ecu in connectedECUs)
                {
                    ConnectedECUs.Add(ecu);
                }

                _loggingService.LogInformation($"Loaded {connectedECUs.Count} connected ECUs", "EnhancedBackupManagementViewModel");
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error loading connected ECUs", "EnhancedBackupManagementViewModel", ex);
                StatusMessage = $"Error loading connected ECUs: {ex.Message}";
            }
        }

        /// <summary>
        /// Loads the backup categories
        /// </summary>
        private void LoadBackupCategories()
        {
            try
            {
                _loggingService.LogInformation("Loading backup categories", "EnhancedBackupManagementViewModel");

                // Get backup categories from the backup service
                var categories = _backupService.GetCategoriesAsync().Result;

                // Update the backup categories collection
                BackupCategories.Clear();
                foreach (var category in categories)
                {
                    BackupCategories.Add(category);
                }

                _loggingService.LogInformation($"Loaded {categories.Count} backup categories", "EnhancedBackupManagementViewModel");
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error loading backup categories", "EnhancedBackupManagementViewModel", ex);
                StatusMessage = $"Error loading backup categories: {ex.Message}";
            }
        }

        /// <summary>
        /// Initializes the backup service
        /// </summary>
        private async Task InitializeBackupServiceAsync()
        {
            try
            {
                _loggingService.LogInformation("Initializing backup service", "EnhancedBackupManagementViewModel");

                // Initialize the backup service
                await _backupService.InitializeAsync(_ecuCommunicationService);

                // Load scheduled backups
                // This method doesn't exist in IBackupService, we need to implement it or use a different approach
                ScheduledBackups.Clear();

                // TODO: Implement loading schedules from a BackupSchedulerService
                // For now, we'll leave the collection empty

                _loggingService.LogInformation("Backup service initialized successfully", "EnhancedBackupManagementViewModel");
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error initializing backup service", "EnhancedBackupManagementViewModel", ex);
                StatusMessage = $"Error initializing backup service: {ex.Message}";
            }
        }

        // Command can-execute methods
        private bool CanConnectToECU() => SelectedECUForBackup != null && SelectedECUForBackup.ConnectionStatus != ECUConnectionStatus.Connected;
        private bool CanDisconnectECU() => SelectedECUForBackup != null && SelectedECUForBackup.ConnectionStatus == ECUConnectionStatus.Connected;
        private bool CanCreateBackup() => SelectedECUForBackup != null && SelectedECUForBackup.ConnectionStatus == ECUConnectionStatus.Connected && !IsCreatingBackup;
        private bool CanRestoreBackup() => SelectedBackup != null && SelectedECUForBackup != null && SelectedECUForBackup.ConnectionStatus == ECUConnectionStatus.Connected && !IsRestoringBackup;
        private bool CanExportBackup() => SelectedBackup != null;
        private bool CanDeleteBackup() => SelectedBackup != null;
        private bool CanCreateNewVersion() => SelectedBackup != null && SelectedECUForBackup != null && SelectedECUForBackup.ConnectionStatus == ECUConnectionStatus.Connected;
        private bool CanCompareVersions() => SelectedVersionForComparisonA != null && SelectedVersionForComparisonB != null;
        private bool CanEditSchedule() => SelectedSchedule != null;
        private bool CanRemoveSchedule() => SelectedSchedule != null;
        private bool CanRunScheduleNow() => SelectedSchedule != null && SelectedSchedule.IsEnabled;
        private bool CanDisableSchedule() => SelectedSchedule != null && SelectedSchedule.IsEnabled;
        private bool CanExportComparison() => ComparisonResults != null && ComparisonResults.Count > 0;
        private bool CanSetAsBaseline() => SelectedVersion != null;

        // Command implementation stubs (to be implemented as needed)
        private async Task RefreshECUAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task ConnectToECUAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task DisconnectECUAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task CreateBackupAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task RestoreBackupAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task ExportBackupAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task ImportBackupAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task DeleteBackupAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task RefreshBackupsAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private void ClearBackupFilter()
        {
            BackupFilter = string.Empty;
        }

        private async Task CreateNewVersionAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task CompareVersionsAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task AddScheduleAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task EditScheduleAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task RemoveScheduleAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task RunAllSchedulesAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task RunScheduleNowAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task DisableScheduleAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task ApplyRetentionPolicyAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private void ResetRetentionPolicy()
        {
            // Implementation will be added as needed
        }

        private async Task ExportComparisonAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private async Task SetAsBaselineAsync()
        {
            // Implementation will be added as needed
            await Task.CompletedTask;
        }

        private void UpdateComparisonResults()
        {
            if (SelectedVersionForComparisonA == null || SelectedVersionForComparisonB == null)
                return;

            try
            {
                ComparisonResults.Clear();

                var backupA = SelectedVersionForComparisonA.Backup;
                var backupB = SelectedVersionForComparisonB.Backup;

                // Compare parameters
                var allParameterKeys = new HashSet<string>();

                if (backupA.Parameters != null)
                {
                    foreach (var key in backupA.Parameters.Keys)
                    {
                        allParameterKeys.Add(key);
                    }
                }

                if (backupB.Parameters != null)
                {
                    foreach (var key in backupB.Parameters.Keys)
                    {
                        allParameterKeys.Add(key);
                    }
                }

                foreach (var key in allParameterKeys)
                {
                    bool hasA = backupA.Parameters != null && backupA.Parameters.ContainsKey(key);
                    bool hasB = backupB.Parameters != null && backupB.Parameters.ContainsKey(key);

                    string valueA = hasA ? backupA.Parameters[key]?.ToString() ?? "null" : "N/A";
                    string valueB = hasB ? backupB.Parameters[key]?.ToString() ?? "null" : "N/A";

                    string status;
                    string difference = "";

                    if (!hasA)
                    {
                        status = "Added in B";
                    }
                    else if (!hasB)
                    {
                        status = "Removed in B";
                    }
                    else if (valueA == valueB)
                    {
                        status = "Same";
                    }
                    else
                    {
                        status = "Changed";

                        // Try to calculate numeric difference
                        if (double.TryParse(valueA, out double numA) && double.TryParse(valueB, out double numB))
                        {
                            double diff = numB - numA;
                            difference = diff.ToString("+0.###;-0.###;0");
                        }
                    }

                    ComparisonResults.Add(new ParameterComparisonResult
                    {
                        ParameterName = key,
                        ValueA = valueA,
                        ValueB = valueB,
                        Difference = difference,
                        Status = status
                    });
                }

                // Sort results by status (changed first, then added, then removed, then same)
                var sorted = ComparisonResults.OrderBy(r =>
                {
                    switch (r.Status)
                    {
                        case "Changed": return 0;
                        case "Added in B": return 1;
                        case "Removed in B": return 2;
                        case "Same": return 3;
                        default: return 4;
                    }
                }).ToList();

                ComparisonResults.Clear();
                foreach (var result in sorted)
                {
                    ComparisonResults.Add(result);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error updating comparison results", "EnhancedBackupManagementViewModel", ex);
                StatusMessage = $"Error comparing versions: {ex.Message}";
            }
        }

        #endregion

        #endregion
    }
}

