namespace VolvoFlashWR.Core.Enums
{
    /// <summary>
    /// Defines the available formats for backup export
    /// </summary>
    public enum BackupExportFormat
    {
        /// <summary>
        /// Binary format (default)
        /// </summary>
        Binary = 0,

        /// <summary>
        /// JSON format
        /// </summary>
        JSON = 1,

        /// <summary>
        /// XML format
        /// </summary>
        XML = 2,

        /// <summary>
        /// CSV format (for parameters only)
        /// </summary>
        CSV = 3,

        /// <summary>
        /// Hex format (for binary data)
        /// </summary>
        Hex = 4,

        /// <summary>
        /// Zip archive format (compressed)
        /// </summary>
        Zip = 5
    }
}
