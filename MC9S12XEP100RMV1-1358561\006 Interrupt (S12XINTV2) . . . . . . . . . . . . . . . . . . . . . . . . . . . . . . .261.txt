﻿Chapter 6
Interrupt (S12XINTV2)

Table 6-1. Revision History

Revision Sections
Revision Date Description of Changes

Number Affected

V02.00 01 Jul 2005 6.1.2/6-262 Initial V2 release, added new features:
- XGATE threads can be interrupted.
- SYS instruction vector.
- Access violation interrupt vectors.

V02.04 11 Jan 2007 *******/6-267 - Added Notes for devices without XGATE module.
*******/6-268

V02.05 20 Mar 2007 6.4.6/6-274 - Fixed priority definition for software exceptions.
V02.07 13 Dec 2011 *******/6-276 - Re-worded for difference of Wake-up feature between STOP and WAIT

modes.

6.1 Introduction
The XINT module decodes the priority of all system exception requests and provides the applicable vector
for processing the exception to either the CPU or the XGATE module. The XINT module supports:

• I bit and X bit maskable interrupt requests
• One non-maskable unimplemented op-code trap
• One non-maskable software interrupt (SWI) or background debug mode request
• One non-maskable system call interrupt (SYS)
• Three non-maskable access violation interrupts
• One spurious interrupt vector request
• Three system reset vector requests

Each of the I bit maskable interrupt requests can be assigned to one of seven priority levels supporting a
flexible priority scheme. For interrupt requests that are configured to be handled by the CPU, the priority
scheme can be used to implement nested interrupt capability where interrupts from a lower level are
automatically blocked if a higher level interrupt is being processed. Interrupt requests configured to be
handled by the XGATE module can be nested one level deep.

NOTE
The HPRIO register and functionality of the original S12 interrupt module
is no longer supported. It is superseded by the 7-level interrupt request
priority scheme.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 261



Chapter 6 Interrupt (S12XINTV2)

6.1.1 Glossary
The following terms and abbreviations are used in the document.

Table 6-2. Terminology

Term Meaning
CCR Condition Code Register (in the S12X CPU)

DMA Direct Memory Access

INT Interrupt

IPL Interrupt Processing Level

ISR Interrupt Service Routine

MCU Micro-Controller Unit

XGATE refers to the XGATE co-processor; XGATE is an optional feature

IRQ refers to the interrupt request associated with the IRQ pin

XIRQ refers to the interrupt request associated with the XIRQ pin

6.1.2 Features
• Interrupt vector base register (IVBR)
• One spurious interrupt vector (at address vector base1 + 0x0010).
• One non-maskable system call interrupt vector request (at address vector base + 0x0012).
• Three non-maskable access violation interrupt vector requests (at address vector base + 0x0014−

0x0018).
• 2–109 I bit maskable interrupt vector requests (at addresses vector base + 0x001A–0x00F2).
• Each I bit maskable interrupt request has a configurable priority level and can be configured to be

handled by either the CPU or the XGATE module2.
• I bit maskable interrupts can be nested, depending on their priority levels.
• One X bit maskable interrupt vector request (at address vector base + 0x00F4).
• One non-maskable software interrupt request (SWI) or background debug mode vector request (at

address vector base + 0x00F6).
• One non-maskable unimplemented op-code trap (TRAP) vector (at address vector base + 0x00F8).
• Three system reset vectors (at addresses 0xFFFA–0xFFFE).
• Determines the highest priority XGATE and interrupt vector requests, drives the vector to the

XGATE module or to the bus on CPU request, respectively.
• Wakes up the system from stop or wait mode when an appropriate interrupt request occurs or

whenever XIRQ is asserted, even if X interrupt is masked.
• XGATE can wake up and execute code, even with the CPU remaining in stop or wait mode.

1. The vector base is a 16-bit address which is accumulated from the contents of the interrupt vector base register (IVBR, used
as upper byte) and 0x00 (used as lower byte).

2. The IRQ interrupt can only be handled by the CPU

MC9S12XE-Family Reference Manual  Rev. 1.25

262 Freescale Semiconductor



Chapter 6 Interrupt (S12XINTV2)

6.1.3 Modes of Operation
• Run mode

This is the basic mode of operation.
• Wait mode

In wait mode, the XINT module is frozen. It is however capable of either waking up the CPU if an
interrupt occurs or waking up the XGATE if an XGATE request occurs. Please refer to
Section 6.5.3, “Wake Up from Stop or Wait Mode” for details.

• Stop Mode
In stop mode, the XINT module is frozen. It is however capable of either waking up the CPU if an
interrupt occurs or waking up the XGATE if an XGATE request occurs. Please refer to
Section 6.5.3, “Wake Up from Stop or Wait Mode” for details.

• Freeze mode (BDM active)
In freeze mode (BDM active), the interrupt vector base register is overridden internally. Please
refer to Section *******, “Interrupt Vector Base Register (IVBR)” for details.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 263



Chapter 6 Interrupt (S12XINTV2)

6.1.4 Block Diagram
Figure 6-1 shows a block diagram of the XINT module.

Peripheral Wake Up
Interrupt Requests CPU

Non I Bit Maskable
Channels

Vector
IRQ Channel Address

IVBR
Interrupt
Requests New

IPL
PRIOLVL2
PRIOLVL1

RQST PRIOLVL0 Current
IPL

One Set Per Channel
(Up to 108 Channels)

INT_XGPRIO
XGATE
Requests

Priority
Decoder

Wake up Vector XGATE
XGATE ID Interrupts

RQST XGATE Request Route,
PRIOLVLn Priority Level

To XGATE Module = bits from the channel configuration
in the associated configuration register

INT_XGPRIO = XGATE Interrupt Priority
IVBR = Interrupt Vector Base
IPL = Interrupt Processing Level

Figure 6-1. XINT Block Diagram

6.2 External Signal Description
The XINT module has no external signals.

MC9S12XE-Family Reference Manual  Rev. 1.25

264 Freescale Semiconductor

Priority
Decoder

To CPU



Chapter 6 Interrupt (S12XINTV2)

6.3 Memory Map and Register Definition
This section provides a detailed description of all registers accessible in the XINT module.

6.3.1 Module Memory Map
Table 6-3 gives an overview over all XINT module registers.

Table 6-3. XINT Memory Map

Address Use Access

0x0120 RESERVED —
0x0121 Interrupt Vector Base Register (IVBR) R/W

0x0122–0x0125 RESERVED —
0x0126 XGATE Interrupt Priority Configuration Register R/W

(INT_XGPRIO)
0x0127 Interrupt Request Configuration Address Register R/W

(INT_CFADDR)
0x0128 Interrupt Request Configuration Data Register 0 R/W

(INT_CFDATA0)
0x0129 Interrupt Request Configuration Data Register 1 R/W

(INT_CFDATA1)
0x012A Interrupt Request Configuration Data Register 2 R/W

(INT_CFDATA2
0x012B Interrupt Request Configuration Data Register 3 R/W

(INT_CFDATA3)
0x012C Interrupt Request Configuration Data Register 4 R/W

(INT_CFDATA4)
0x012D Interrupt Request Configuration Data Register 5 R/W

(INT_CFDATA5)
0x012E Interrupt Request Configuration Data Register 6 R/W

(INT_CFDATA6)
0x012F Interrupt Request Configuration Data Register 7 R/W

(INT_CFDATA7)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 265



Chapter 6 Interrupt (S12XINTV2)

6.3.2 Register Descriptions
This section describes in address order all the XINT module registers and their individual bits.

Register
Address Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0121 IVBR R
IVB_ADDR[7:0]7

W

0x0126 INT_XGPRIO R 0 0 0 0 0
XILVL[2:0]

W

0x0127 INT_CFADDR R 0 0 0 0
INT_CFADDR[7:4]

W

0x0128 INT_CFDATA0 R 0 0 0 0
RQST PRIOLVL[2:0]

W

0x0129 INT_CFDATA1 R 0 0 0 0
RQST PRIOLVL[2:0]

W

0x012A INT_CFDATA2 R 0 0 0 0
RQST PRIOLVL[2:0]

W

0x012B INT_CFDATA3 R 0 0 0 0
RQST PRIOLVL[2:0]

W

0x012C INT_CFDATA4 R 0 0 0 0
RQST PRIOLVL[2:0]

W

0x012D INT_CFDATA5 R 0 0 0 0
RQST PRIOLVL[2:0]

W

0x012E INT_CFDATA6 R 0 0 0 0
RQST PRIOLVL[2:0]

W

0x012F INT_CFDATA7 R 0 0 0 0
RQST PRIOLVL[2:0]

W

= Unimplemented or Reserved

Figure 6-2. XINT Register Summary

MC9S12XE-Family Reference Manual  Rev. 1.25

266 Freescale Semiconductor



Chapter 6 Interrupt (S12XINTV2)

******* Interrupt Vector Base Register (IVBR)

Address: 0x0121

7 6 5 4 3 2 1 0
R

IVB_ADDR[7:0]
W

Reset 1 1 1 1 1 1 1 1

Figure 6-3. Interrupt Vector Base Register (IVBR)

Read: Anytime

Write: Anytime

Table 6-4. IVBR Field Descriptions

Field Description

7–0 Interrupt Vector Base Address Bits — These bits represent the upper byte of all vector addresses. Out of
IVB_ADDR[7:0] reset these bits are set to 0xFF (i.e., vectors are located at 0xFF10–0xFFFE) to ensure compatibility to

previous S12 microcontrollers.
Note: A system reset will initialize the interrupt vector base register with “0xFF” before it is used to determine

the reset vector address. Therefore, changing the IVBR has no effect on the location of the three reset
vectors (0xFFFA–0xFFFE).

Note: If the BDM is active (i.e., the CPU is in the process of executing BDM firmware code), the contents of
IVBR are ignored and the upper byte of the vector address is fixed as “0xFF”.

******* XGATE Interrupt Priority Configuration Register (INT_XGPRIO)

Address: 0x0126

7 6 5 4 3 2 1 0
R 0 0 0 0 0

XILVL[2:0]
W

Reset 0 0 0 0 0 0 0 1
= Unimplemented or Reserved

Figure 6-4. XGATE Interrupt Priority Configuration Register (INT_XGPRIO)

Read: Anytime

Write: Anytime

Table 6-5. INT_XGPRIO Field Descriptions

Field Description

2–0 XGATE Interrupt Priority Level — The XILVL[2:0] bits configure the shared interrupt level of the XGATE
XILVL[2:0] interrupts coming from the XGATE module. Out of reset the priority is set to the lowest active level (“1”).

Note: If the XGATE module is not available on the device, write accesses to this register are ignored and read
accesses to this register will return all 0.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 267



Chapter 6 Interrupt (S12XINTV2)

Table 6-6. XGATE Interrupt Priority Levels

Priority XILVL2 XILVL1 XILVL0 Meaning

0 0 0 Interrupt request is disabled
low 0 0 1 Priority level 1

0 1 0 Priority level 2
0 1 1 Priority level 3
1 0 0 Priority level 4
1 0 1 Priority level 5
1 1 0 Priority level 6

high 1 1 1 Priority level 7

******* Interrupt Request Configuration Address Register (INT_CFADDR)

Address: 0x0127

7 6 5 4 3 2 1 0
R 0 0 0 0

INT_CFADDR[7:4]
W

Reset 0 0 0 1 0 0 0 0
= Unimplemented or Reserved

Figure 6-5. Interrupt Configuration Address Register (INT_CFADDR)

Read: Anytime

Write: Anytime

Table 6-7. INT_CFADDR Field Descriptions

Field Description

7–4 Interrupt Request Configuration Data Register Select Bits — These bits determine which of the 128
INT_CFADDR[7:4] configuration data registers are accessible in the 8 register window at INT_CFDATA0–7. The hexadecimal

value written to this register corresponds to the upper nibble of the lower byte of the address of the interrupt
vector, i.e., writing 0xE0 to this register selects the configuration data register block for the 8 interrupt vector
requests starting with vector at address (vector base + 0x00E0) to be accessible as INT_CFDATA0–7.
Note: Writing all 0s selects non-existing configuration registers. In this case write accesses to

INT_CFDATA0–7 will be ignored and read accesses will return all 0.

******* Interrupt Request Configuration Data Registers (INT_CFDATA0–7)
The eight register window visible at addresses INT_CFDATA0–7 contains the configuration data for the
block of eight interrupt requests (out of 128) selected by the interrupt configuration address register
(INT_CFADDR) in ascending order. INT_CFDATA0 represents the interrupt configuration data register
of the vector with the lowest address in this block, while INT_CFDATA7 represents the interrupt
configuration data register of the vector with the highest address, respectively.

MC9S12XE-Family Reference Manual  Rev. 1.25

268 Freescale Semiconductor



Chapter 6 Interrupt (S12XINTV2)

Address: 0x0128

7 6 5 4 3 2 1 0
R 0 0 0 0

RQST PRIOLVL[2:0]
W

Reset 0 0 0 0 0 0 0 1(1)

= Unimplemented or Reserved

Figure 6-6. Interrupt Request Configuration Data Register 0 (INT_CFDATA0)
1. Please refer to the notes following the PRIOLVL[2:0] description below.

Address: 0x0129

7 6 5 4 3 2 1 0
R 0 0 0 0

RQST PRIOLVL[2:0]
W

Reset 0 0 0 0 0 0 0 1(1)

= Unimplemented or Reserved

Figure 6-7. Interrupt Request Configuration Data Register 1 (INT_CFDATA1)
1. Please refer to the notes following the PRIOLVL[2:0] description below.

Address: 0x012A

7 6 5 4 3 2 1 0
R 0 0 0 0

RQST PRIOLVL[2:0]
W

Reset 0 0 0 0 0 0 0 1(1)

= Unimplemented or Reserved

Figure 6-8. Interrupt Request Configuration Data Register 2 (INT_CFDATA2)
1. Please refer to the notes following the PRIOLVL[2:0] description below.

Address: 0x012B

7 6 5 4 3 2 1 0
R 0 0 0 0

RQST PRIOLVL[2:0]
W

Reset 0 0 0 0 0 0 0 1(1)

= Unimplemented or Reserved

Figure 6-9. Interrupt Request Configuration Data Register 3 (INT_CFDATA3)
1. Please refer to the notes following the PRIOLVL[2:0] description below.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 269



Chapter 6 Interrupt (S12XINTV2)

Address: 0x012C

7 6 5 4 3 2 1 0
R 0 0 0 0

RQST PRIOLVL[2:0]
W

Reset 0 0 0 0 0 0 0 1(1)

= Unimplemented or Reserved

Figure 6-10. Interrupt Request Configuration Data Register 4 (INT_CFDATA4)
1. Please refer to the notes following the PRIOLVL[2:0] description below.

Address: 0x012D

7 6 5 4 3 2 1 0
R 0 0 0 0

RQST PRIOLVL[2:0]
W

Reset 0 0 0 0 0 0 0 1(1)

= Unimplemented or Reserved

Figure 6-11. Interrupt Request Configuration Data Register 5 (INT_CFDATA5)
1. Please refer to the notes following the PRIOLVL[2:0] description below.

Address: 0x012E

7 6 5 4 3 2 1 0
R 0 0 0 0

RQST PRIOLVL[2:0]
W

Reset 0 0 0 0 0 0 0 1(1)

= Unimplemented or Reserved

Figure 6-12. Interrupt Request Configuration Data Register 6 (INT_CFDATA6)
1. Please refer to the notes following the PRIOLVL[2:0] description below.

Address: 0x012F

7 6 5 4 3 2 1 0
R 0 0 0 0

RQST PRIOLVL[2:0]
W

Reset 0 0 0 0 0 0 0 1(1)

= Unimplemented or Reserved

Figure 6-13. Interrupt Request Configuration Data Register 7 (INT_CFDATA7)
1. Please refer to the notes following the PRIOLVL[2:0] description below.

Read: Anytime

Write: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

270 Freescale Semiconductor



Chapter 6 Interrupt (S12XINTV2)

Table 6-8. INT_CFDATA0–7 Field Descriptions

Field Description

7 XGATE Request Enable — This bit determines if the associated interrupt request is handled by the CPU or by
RQST the XGATE module.

0 Interrupt request is handled by the CPU
1 Interrupt request is handled by the XGATE module
Note: The IRQ interrupt cannot be handled by the XGATE module. For this reason, the configuration register

for vector (vector base + 0x00F2) = IRQ vector address) does not contain a RQST bit. Writing a 1 to the
location of the RQST bit in this register will be ignored and a read access will return 0.

Note: If the XGATE module is not available on the device, writing a 1 to the location of the RQST bit in this
register will be ignored and a read access will return 0.

2–0 Interrupt Request Priority Level Bits — The PRIOLVL[2:0] bits configure the interrupt request priority level of
PRIOLVL[2:0] the associated interrupt request. Out of reset all interrupt requests are enabled at the lowest active level (“1”)

to provide backwards compatibility with previous S12 interrupt controllers. Please also refer to Table 6-9 for
available interrupt request priority levels.
Note: Write accesses to configuration data registers of unused interrupt channels will be ignored and read

accesses will return all 0. For information about what interrupt channels are used in a specific MCU,
please refer to the Device Reference Manual of that MCU.

Note: When vectors (vector base + 0x00F0–0x00FE) are selected by writing 0xF0 to INT_CFADDR, writes to
INT_CFDATA2–7 (0x00F4–0x00FE) will be ignored and read accesses will return all 0s. The
corresponding vectors do not have configuration data registers associated with them.

Note: When vectors (vector base + 0x0010–0x001E) are selected by writing 0x10 to INT_CFADDR, writes to
INT_CFDATA1–INT_CFDATA4 (0x0012–0x0018) will be ignored and read accesses will return all 0s. The
corresponding vectors do not have configuration data registers associated with them.

Note: Write accesses to the configuration register for the spurious interrupt vector request
(vector base + 0x0010) will be ignored and read accesses will return 0x07 (request is handled by the
CPU, PRIOLVL = 7).

Table 6-9. Interrupt Priority Levels

Priority PRIOLVL2 PRIOLVL1 PRIOLVL0 Meaning

0 0 0 Interrupt request is disabled
low 0 0 1 Priority level 1

0 1 0 Priority level 2
0 1 1 Priority level 3
1 0 0 Priority level 4
1 0 1 Priority level 5
1 1 0 Priority level 6

high 1 1 1 Priority level 7

6.4 Functional Description
The XINT module processes all exception requests to be serviced by the CPU module. These exceptions
include interrupt vector requests and reset vector requests. Each of these exception types and their overall
priority level is discussed in the subsections below.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 271



Chapter 6 Interrupt (S12XINTV2)

6.4.1 S12X Exception Requests
The CPU handles both reset requests and interrupt requests. The XINT module contains registers to
configure the priority level of each I bit maskable interrupt request which can be used to implement an
interrupt priority scheme. This also includes the possibility to nest interrupt requests. A priority decoder
is used to evaluate the priority of a pending interrupt request.

6.4.2 Interrupt Prioritization
After system reset all interrupt requests with a vector address lower than or equal to (vector base + 0x00F2)
are enabled, are set up to be handled by the CPU and have a pre-configured priority level of 1. Exceptions
to this rule are the non-maskable interrupt requests and the spurious interrupt vector request at (vector base
+ 0x0010) which cannot be disabled, are always handled by the CPU and have a fixed priority levels. A
priority level of 0 effectively disables the associated I bit maskable interrupt request.

If more than one interrupt request is configured to the same interrupt priority level the interrupt request
with the higher vector address wins the prioritization.

The following conditions must be met for an I bit maskable interrupt request to be processed.
1. The local interrupt enabled bit in the peripheral module must be set.
2. The setup in the configuration register associated with the interrupt request channel must meet the

following conditions:
a) The XGATE request enable bit must be 0 to have the CPU handle the interrupt request.
b) The priority level must be set to non zero.
c) The priority level must be greater than the current interrupt processing level in the condition

code register (CCR) of the CPU (PRIOLVL[2:0] > IPL[2:0]).
3. The I bit in the condition code register (CCR) of the CPU must be cleared.
4. There is no access violation interrupt request pending.
5. There is no SYS, SWI, BDM, TRAP, or XIRQ request pending.

NOTE
All non I bit maskable interrupt requests always have higher priority than
I bit maskable interrupt requests. If an I bit maskable interrupt request is
interrupted by a non I bit maskable interrupt request, the currently active
interrupt processing level (IPL) remains unaffected. It is possible to nest
non I bit maskable interrupt requests, e.g., by nesting SWI or TRAP calls.

******* Interrupt Priority Stack
The current interrupt processing level (IPL) is stored in the condition code register (CCR) of the CPU. This
way the current IPL is automatically pushed to the stack by the standard interrupt stacking procedure. The
new IPL is copied to the CCR from the priority level of the highest priority active interrupt request channel
which is configured to be handled by the CPU. The copying takes place when the interrupt vector is
fetched. The previous IPL is automatically restored by executing the RTI instruction.

MC9S12XE-Family Reference Manual  Rev. 1.25

272 Freescale Semiconductor



Chapter 6 Interrupt (S12XINTV2)

6.4.3 XGATE Requests
If the XGATE module is implemented on the device, the XINT module is also used to process all exception
requests to be serviced by the XGATE module. The overall priority level of those exceptions is discussed
in the subsections below.

******* XGATE Request Prioritization
An interrupt request channel is configured to be handled by the XGATE module, if the RQST bit of the
associated configuration register is set to 1 (please refer to Section *******, “Interrupt Request
Configuration Data Registers (INT_CFDATA0–7)”). The priority level configuration (PRIOLVL) for this
channel becomes the XGATE priority which will be used to determine the highest priority XGATE request
to be serviced next by the XGATE module. Additionally, XGATE interrupts may be raised by the XGATE
module by setting one or more of the XGATE channel interrupt flags (by using the SIF instruction). This
will result in an CPU interrupt with vector address vector base + (2 * channel ID number), where the
channel ID number corresponds to the highest set channel interrupt flag, if the XGIE and channel RQST
bits are set.

The shared interrupt priority for the XGATE interrupt requests is taken from the XGATE interrupt priority
configuration register (please refer to Section *******, “XGATE Interrupt Priority Configuration Register
(INT_XGPRIO)”). If more than one XGATE interrupt request channel becomes active at the same time,
the channel with the highest vector address wins the prioritization.

6.4.4 Priority Decoders
The XINT module contains priority decoders to determine the priority for all interrupt requests pending
for the respective target.

There are two priority decoders, one for each interrupt request target, CPU or XGATE. The function of
both priority decoders is basically the same with one exception: the priority decoder for the XGATE
module does not take the current XGATE thread processing level into account. Instead, XGATE requests
are handed to the XGATE module including a 1-bit priority identifier. The XGATE module uses this
additional information to decide if the new request can interrupt a currently running thread. The 1-bit
priority identifier corresponds to the most significant bit of the priority level configuration of the requesting
channel. This means that XGATE requests with priority levels 4, 5, 6 or 7 can interrupt running XGATE
threads with priority levels 1, 2 and 3.

A CPU interrupt vector is not supplied until the CPU requests it. Therefore, it is possible that a higher
priority interrupt request could override the original exception which caused the CPU to request the vector.
In this case, the CPU will receive the highest priority vector and the system will process this exception
instead of the original request.

If the interrupt source is unknown (for example, in the case where an interrupt request becomes inactive
after the interrupt has been recognized, but prior to the vector request), the vector address supplied to the
CPU will default to that of the spurious interrupt vector.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 273



Chapter 6 Interrupt (S12XINTV2)

NOTE
Care must be taken to ensure that all exception requests remain active until
the system begins execution of the applicable service routine; otherwise, the
exception request may not get processed at all or the result may be a
spurious interrupt request (vector at address (vector base + 0x0010)).

6.4.5 Reset Exception Requests
The XINT module supports three system reset exception request types (for details please refer to the Clock
and Reset Generator module (CRG)):

1. Pin reset, power-on reset, low-voltage reset, or illegal address reset
2. Clock monitor reset request
3. COP watchdog reset request

6.4.6 Exception Priority
The priority (from highest to lowest) and address of all exception vectors issued by the XINT module upon
request by the CPU is shown in Table 6-10. Generally, all non-maskable interrupts have higher priorities
than maskable interrupts. Please note that between the three software interrupts (Unimplemented op-code
trap request, SWI/BGND request, SYS request) there is no real priority defined because they cannot occur
simultaneously (the S12XCPU executes one instruction at a time).

Table 6-10. Exception Vector Map and Priority

Vector Address(1)  Source

0xFFFE Pin reset, power-on reset, low-voltage reset, illegal address reset
0xFFFC Clock monitor reset
0xFFFA COP watchdog reset

(Vector base + 0x00F8) Unimplemented op-code trap
(Vector base + 0x00F6) Software interrupt instruction (SWI) or BDM vector request
(Vector base + 0x0012) System call interrupt instruction (SYS)
(Vector base + 0x0018) (reserved for future use)
(Vector base + 0x0016) XGATE Access violation interrupt request(2)

(Vector base + 0x0014) CPU Access violation interrupt request(3)

(Vector base + 0x00F4) XIRQ interrupt request
(Vector base + 0x00F2) IRQ interrupt request

(Vector base + Device specific I bit maskable interrupt sources (priority determined by the associated
0x00F0–0x001A) configuration registers, in descending order)

(Vector base + 0x0010) Spurious interrupt
1. 16 bits vector address based
2. only implemented if device features both a Memory Protection Unit (MPU) and an XGATE co-processor
3. only implemented if device features a Memory Protection Unit (MPU)

MC9S12XE-Family Reference Manual  Rev. 1.25

274 Freescale Semiconductor



Chapter 6 Interrupt (S12XINTV2)

6.5 Initialization/Application Information

6.5.1 Initialization
After system reset, software should:

• Initialize the interrupt vector base register if the interrupt vector table is not located at the default
location (0xFF10–0xFFF9).

• Initialize the interrupt processing level configuration data registers (INT_CFADDR,
INT_CFDATA0–7) for all interrupt vector requests with the desired priority levels and the request
target (CPU or XGATE module). It might be a good idea to disable unused interrupt requests.

• If the XGATE module is used, setup the XGATE interrupt priority register (INT_XGPRIO) and
configure the XGATE module (please refer the XGATE Block Guide for details).

• Enable I maskable interrupts by clearing the I bit in the CCR.
• Enable the X maskable interrupt by clearing the X bit in the CCR (if required).

6.5.2 Interrupt Nesting
The interrupt request priority level scheme makes it possible to implement priority based interrupt request
nesting for the I bit maskable interrupt requests handled by the CPU.

• I bit maskable interrupt requests can be interrupted by an interrupt request with a higher priority,
so that there can be up to seven nested I bit maskable interrupt requests at a time (refer to Figure 6-
14 for an example using up to three nested interrupt requests).

I bit maskable interrupt requests cannot be interrupted by other I bit maskable interrupt requests per
default. In order to make an interrupt service routine (ISR) interruptible, the ISR must explicitly clear the
I bit in the CCR (CLI). After clearing the I bit, I bit maskable interrupt requests with higher priority can
interrupt the current ISR.

An ISR of an interruptible I bit maskable interrupt request could basically look like this:
• Service interrupt, e.g., clear interrupt flags, copy data, etc.
• Clear I bit in the CCR by executing the instruction CLI (thus allowing interrupt requests with

higher priority)
• Process data
• Return from interrupt by executing the instruction RTI

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 275



Chapter 6 Interrupt (S12XINTV2)

0

Stacked IPL 0 4 0 0 0

IPL in CCR 0 4 7 4 3 1 0

7

6
L7 RTI

5

4
Processing Levels RTI

3
L3 (Pending)

2 L4 RTI

1
L1 (Pending) RTI

0
Reset

Figure 6-14. Interrupt Processing Example

6.5.3 Wake Up from Stop or Wait Mode

******* CPU Wake Up from Stop or Wait Mode
Only I bit maskable interrupt requests which are configured to be handled by the CPU are capable of
waking the MCU from wait mode.

Since bus and core clocks are disabled in stop mode, only interrupt requests that can be generated without
these clocks can wake the MCU from stop mode. These are listed in the device overview interrupt vector
table. Only I bit maskable interrupt requests which are configured to be handled by the CPU are capable
of waking the MCU from stop mode.

To determine whether an I bit maskable interrupt is qualified to wake up the CPU or not, the same settings
as in normal run mode are applied during stop or wait mode:

• If the I bit in the CCR is set, all I bit maskable interrupts are masked from waking up the MCU.
• An I bit maskable interrupt is ignored if it is configured to a priority level below or equal to the

current IPL in CCR.
• I bit maskable interrupt requests which are configured to be handled by the XGATE module are not

capable of waking up the CPU.

The X bit maskable interrupt request can wake up the MCU from stop or wait mode at anytime, even if the
X bit in CCR is set. If the X bit maskable interrupt request is used to wake-up the MCU with the X bit in
the CCR set, the associated ISR is not called. The CPU then resumes program execution with the
instruction following the WAI or STOP instruction. This features works following the same rules like any
interrupt request, i.e. care must be taken that the X interrupt request used for wake-up remains active at
least until the system begins execution of the instruction following the WAI or STOP instruction;
otherwise, wake-up may not occur.

MC9S12XE-Family Reference Manual  Rev. 1.25

276 Freescale Semiconductor



Chapter 6 Interrupt (S12XINTV2)

******* XGATE Wake Up from Stop or Wait Mode
Interrupt request channels which are configured to be handled by the XGATE module are capable of
waking up the XGATE module. Interrupt request channels handled by the XGATE module do not affect
the state of the CPU.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 277



Chapter 6 Interrupt (S12XINTV2)

MC9S12XE-Family Reference Manual  Rev. 1.25

278 Freescale Semiconductor