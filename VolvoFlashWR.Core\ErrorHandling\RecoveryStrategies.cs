using System;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Diagnostics;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Utilities;

namespace VolvoFlashWR.Core.ErrorHandling
{
    /// <summary>
    /// Base class for flash error recovery strategies
    /// </summary>
    public abstract class BaseRecoveryStrategy : IFlashErrorRecoveryStrategy
    {
        protected readonly ILoggingService _logger;

        /// <summary>
        /// Initializes a new instance of the BaseRecoveryStrategy class
        /// </summary>
        /// <param name="logger">The logger to use</param>
        protected BaseRecoveryStrategy(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Attempts to recover from an error in a flash operation
        /// </summary>
        /// <param name="operation">The flash operation</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="errorData">Additional error data</param>
        /// <returns>A recovery result indicating the outcome of the recovery attempt</returns>
        public abstract Task<FlashErrorRecoveryResult> RecoverAsync(FlashOperation operation, string errorMessage, object? errorData = null);

        /// <summary>
        /// Logs information about the recovery attempt
        /// </summary>
        /// <param name="operation">The flash operation</param>
        /// <param name="message">The message to log</param>
        protected void LogRecoveryInfo(FlashOperation operation, string message)
        {
            _logger.LogInformation($"Recovery for operation {operation.Id} ({operation.OperationType} at 0x{operation.Address:X8}): {message}", GetType().Name);
        }

        /// <summary>
        /// Logs an error about the recovery attempt
        /// </summary>
        /// <param name="operation">The flash operation</param>
        /// <param name="message">The message to log</param>
        protected void LogRecoveryError(FlashOperation operation, string message)
        {
            _logger.LogError($"Recovery for operation {operation.Id} ({operation.OperationType} at 0x{operation.Address:X8}) failed: {message}", GetType().Name);
        }
    }

    /// <summary>
    /// Recovery strategy for timeout errors
    /// </summary>
    public class TimeoutRecoveryStrategy : BaseRecoveryStrategy
    {
        private const int MaxRetries = 3;

        /// <summary>
        /// Initializes a new instance of the TimeoutRecoveryStrategy class
        /// </summary>
        /// <param name="logger">The logger to use</param>
        public TimeoutRecoveryStrategy(ILoggingService logger) : base(logger)
        {
        }

        /// <summary>
        /// Attempts to recover from a timeout error
        /// </summary>
        /// <param name="operation">The flash operation</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="errorData">Additional error data</param>
        /// <returns>A recovery result indicating the outcome of the recovery attempt</returns>
        public override async Task<FlashErrorRecoveryResult> RecoverAsync(FlashOperation operation, string errorMessage, object? errorData = null)
        {
            // Get the current retry count from the operation's additional info
            int retryCount = 0;
            if (operation.AdditionalInfo != null && operation.AdditionalInfo.Contains("RetryCount:"))
            {
                var parts = operation.AdditionalInfo.Split(';');
                foreach (var part in parts)
                {
                    if (part.Trim().StartsWith("RetryCount:", StringComparison.OrdinalIgnoreCase))
                    {
                        if (int.TryParse(part.Trim().Substring("RetryCount:".Length), out int count))
                        {
                            retryCount = count;
                        }
                    }
                }
            }

            // Check if we've exceeded the maximum number of retries
            if (retryCount >= MaxRetries)
            {
                LogRecoveryError(operation, $"Maximum retry count ({MaxRetries}) exceeded");
                return new FlashErrorRecoveryResult(false, $"Maximum retry count ({MaxRetries}) exceeded");
            }

            // Increment the retry count
            retryCount++;

            // Update the operation's additional info
            string additionalInfo = operation.AdditionalInfo ?? "";
            if (additionalInfo.Contains("RetryCount:"))
            {
                additionalInfo = additionalInfo.Replace($"RetryCount:{retryCount - 1}", $"RetryCount:{retryCount}");
            }
            else
            {
                additionalInfo = string.IsNullOrEmpty(additionalInfo) ? $"RetryCount:{retryCount}" : $"{additionalInfo};RetryCount:{retryCount}";
            }
            operation.AdditionalInfo = additionalInfo;

            // Log the recovery attempt
            LogRecoveryInfo(operation, $"Retrying operation after timeout (attempt {retryCount} of {MaxRetries})");

            // Wait a bit before retrying
            await Task.Delay(1000 * retryCount); // Exponential backoff

            // Return success to indicate that the operation should be retried
            return new FlashErrorRecoveryResult(true, $"Retrying operation (attempt {retryCount} of {MaxRetries})");
        }
    }

    /// <summary>
    /// Recovery strategy for verification errors
    /// </summary>
    public class VerificationRecoveryStrategy : BaseRecoveryStrategy
    {
        private const int MaxRetries = 2;

        /// <summary>
        /// Initializes a new instance of the VerificationRecoveryStrategy class
        /// </summary>
        /// <param name="logger">The logger to use</param>
        public VerificationRecoveryStrategy(ILoggingService logger) : base(logger)
        {
        }

        /// <summary>
        /// Attempts to recover from a verification error
        /// </summary>
        /// <param name="operation">The flash operation</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="errorData">Additional error data</param>
        /// <returns>A recovery result indicating the outcome of the recovery attempt</returns>
        public override async Task<FlashErrorRecoveryResult> RecoverAsync(FlashOperation operation, string errorMessage, object? errorData = null)
        {
            // Get the current retry count from the operation's additional info
            int retryCount = 0;
            if (operation.AdditionalInfo != null && operation.AdditionalInfo.Contains("RetryCount:"))
            {
                var parts = operation.AdditionalInfo.Split(';');
                foreach (var part in parts)
                {
                    if (part.Trim().StartsWith("RetryCount:", StringComparison.OrdinalIgnoreCase))
                    {
                        if (int.TryParse(part.Trim().Substring("RetryCount:".Length), out int count))
                        {
                            retryCount = count;
                        }
                    }
                }
            }

            // Check if we've exceeded the maximum number of retries
            if (retryCount >= MaxRetries)
            {
                LogRecoveryError(operation, $"Maximum retry count ({MaxRetries}) exceeded");
                return new FlashErrorRecoveryResult(false, $"Maximum retry count ({MaxRetries}) exceeded");
            }

            // Increment the retry count
            retryCount++;

            // Update the operation's additional info
            string additionalInfo = operation.AdditionalInfo ?? "";
            if (additionalInfo.Contains("RetryCount:"))
            {
                additionalInfo = additionalInfo.Replace($"RetryCount:{retryCount - 1}", $"RetryCount:{retryCount}");
            }
            else
            {
                additionalInfo = string.IsNullOrEmpty(additionalInfo) ? $"RetryCount:{retryCount}" : $"{additionalInfo};RetryCount:{retryCount}";
            }
            operation.AdditionalInfo = additionalInfo;

            // Log the recovery attempt
            LogRecoveryInfo(operation, $"Retrying operation after verification failure (attempt {retryCount} of {MaxRetries})");

            // Wait a bit before retrying
            await Task.Delay(500 * retryCount); // Exponential backoff

            // Return success to indicate that the operation should be retried
            return new FlashErrorRecoveryResult(true, $"Retrying operation (attempt {retryCount} of {MaxRetries})");
        }
    }

    /// <summary>
    /// Recovery strategy for address errors
    /// </summary>
    public class AddressErrorRecoveryStrategy : BaseRecoveryStrategy
    {
        /// <summary>
        /// Initializes a new instance of the AddressErrorRecoveryStrategy class
        /// </summary>
        /// <param name="logger">The logger to use</param>
        public AddressErrorRecoveryStrategy(ILoggingService logger) : base(logger)
        {
        }

        /// <summary>
        /// Attempts to recover from an address error
        /// </summary>
        /// <param name="operation">The flash operation</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="errorData">Additional error data</param>
        /// <returns>A recovery result indicating the outcome of the recovery attempt</returns>
        public override Task<FlashErrorRecoveryResult> RecoverAsync(FlashOperation operation, string errorMessage, object? errorData = null)
        {
            // Address errors are typically not recoverable
            LogRecoveryError(operation, "Address errors are not recoverable");
            return Task.FromResult(new FlashErrorRecoveryResult(false, "Address errors are not recoverable"));
        }
    }

    /// <summary>
    /// Recovery strategy for protection violation errors
    /// </summary>
    public class ProtectionViolationRecoveryStrategy : BaseRecoveryStrategy
    {
        /// <summary>
        /// Initializes a new instance of the ProtectionViolationRecoveryStrategy class
        /// </summary>
        /// <param name="logger">The logger to use</param>
        public ProtectionViolationRecoveryStrategy(ILoggingService logger) : base(logger)
        {
        }

        /// <summary>
        /// Attempts to recover from a protection violation error
        /// </summary>
        /// <param name="operation">The flash operation</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="errorData">Additional error data</param>
        /// <returns>A recovery result indicating the outcome of the recovery attempt</returns>
        public override Task<FlashErrorRecoveryResult> RecoverAsync(FlashOperation operation, string errorMessage, object? errorData = null)
        {
            // Protection violations are typically not recoverable
            LogRecoveryError(operation, "Protection violations are not recoverable");
            return Task.FromResult(new FlashErrorRecoveryResult(false, "Protection violations are not recoverable"));
        }
    }

    /// <summary>
    /// Recovery strategy for communication errors
    /// </summary>
    public class CommunicationErrorRecoveryStrategy : BaseRecoveryStrategy
    {
        private const int MaxRetries = 3;

        /// <summary>
        /// Initializes a new instance of the CommunicationErrorRecoveryStrategy class
        /// </summary>
        /// <param name="logger">The logger to use</param>
        public CommunicationErrorRecoveryStrategy(ILoggingService logger) : base(logger)
        {
        }

        /// <summary>
        /// Attempts to recover from a communication error
        /// </summary>
        /// <param name="operation">The flash operation</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="errorData">Additional error data</param>
        /// <returns>A recovery result indicating the outcome of the recovery attempt</returns>
        public override async Task<FlashErrorRecoveryResult> RecoverAsync(FlashOperation operation, string errorMessage, object? errorData = null)
        {
            // Get the current retry count from the operation's additional info
            int retryCount = 0;
            if (operation.AdditionalInfo != null && operation.AdditionalInfo.Contains("RetryCount:"))
            {
                var parts = operation.AdditionalInfo.Split(';');
                foreach (var part in parts)
                {
                    if (part.Trim().StartsWith("RetryCount:", StringComparison.OrdinalIgnoreCase))
                    {
                        if (int.TryParse(part.Trim().Substring("RetryCount:".Length), out int count))
                        {
                            retryCount = count;
                        }
                    }
                }
            }

            // Check if we've exceeded the maximum number of retries
            if (retryCount >= MaxRetries)
            {
                LogRecoveryError(operation, $"Maximum retry count ({MaxRetries}) exceeded");
                return new FlashErrorRecoveryResult(false, $"Maximum retry count ({MaxRetries}) exceeded");
            }

            // Increment the retry count
            retryCount++;

            // Update the operation's additional info
            string additionalInfo = operation.AdditionalInfo ?? "";
            if (additionalInfo.Contains("RetryCount:"))
            {
                additionalInfo = additionalInfo.Replace($"RetryCount:{retryCount - 1}", $"RetryCount:{retryCount}");
            }
            else
            {
                additionalInfo = string.IsNullOrEmpty(additionalInfo) ? $"RetryCount:{retryCount}" : $"{additionalInfo};RetryCount:{retryCount}";
            }
            operation.AdditionalInfo = additionalInfo;

            // Log the recovery attempt
            LogRecoveryInfo(operation, $"Retrying operation after communication error (attempt {retryCount} of {MaxRetries})");

            // Wait a bit before retrying
            await Task.Delay(2000 * retryCount); // Exponential backoff

            // Return success to indicate that the operation should be retried
            return new FlashErrorRecoveryResult(true, $"Retrying operation (attempt {retryCount} of {MaxRetries})");
        }
    }

    /// <summary>
    /// Recovery strategy for stalled operations
    /// </summary>
    public class StalledOperationRecoveryStrategy : BaseRecoveryStrategy, IDisposable
    {
        private readonly FlashOperationMonitor _monitor;
        private const int MaxRetries = 2;

        /// <summary>
        /// Initializes a new instance of the StalledOperationRecoveryStrategy class
        /// </summary>
        /// <param name="logger">The logger to use</param>
        /// <param name="monitor">The flash operation monitor</param>
        public StalledOperationRecoveryStrategy(ILoggingService logger, FlashOperationMonitor monitor) : base(logger)
        {
            _monitor = monitor ?? throw new ArgumentNullException(nameof(monitor));
        }

        /// <summary>
        /// Attempts to recover from a stalled operation
        /// </summary>
        /// <param name="operation">The flash operation</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="errorData">Additional error data</param>
        /// <returns>A recovery result indicating the outcome of the recovery attempt</returns>
        public override async Task<FlashErrorRecoveryResult> RecoverAsync(FlashOperation operation, string errorMessage, object? errorData = null)
        {
            // Get the current retry count from the operation's additional info
            int retryCount = 0;
            if (operation.AdditionalInfo != null && operation.AdditionalInfo.Contains("RetryCount:"))
            {
                var parts = operation.AdditionalInfo.Split(';');
                foreach (var part in parts)
                {
                    if (part.Trim().StartsWith("RetryCount:", StringComparison.OrdinalIgnoreCase))
                    {
                        if (int.TryParse(part.Trim().Substring("RetryCount:".Length), out int count))
                        {
                            retryCount = count;
                        }
                    }
                }
            }

            // Check if we've exceeded the maximum number of retries
            if (retryCount >= MaxRetries)
            {
                LogRecoveryError(operation, $"Maximum retry count ({MaxRetries}) exceeded");
                return new FlashErrorRecoveryResult(false, $"Maximum retry count ({MaxRetries}) exceeded");
            }

            // Increment the retry count
            retryCount++;

            // Update the operation's additional info
            string additionalInfo = operation.AdditionalInfo ?? "";
            if (additionalInfo.Contains("RetryCount:"))
            {
                additionalInfo = additionalInfo.Replace($"RetryCount:{retryCount - 1}", $"RetryCount:{retryCount}");
            }
            else
            {
                additionalInfo = string.IsNullOrEmpty(additionalInfo) ? $"RetryCount:{retryCount}" : $"{additionalInfo};RetryCount:{retryCount}";
            }
            operation.AdditionalInfo = additionalInfo;

            // Log the recovery attempt
            LogRecoveryInfo(operation, $"Attempting to recover stalled operation (attempt {retryCount} of {MaxRetries})");

            // Update the operation status to indicate that recovery is in progress
            _monitor.UpdateOperationProgress(operation.Id, operation.BytesProcessed, FlashOperationStatus.InProgress);

            // Wait a bit before considering the operation recovered
            await Task.Delay(1000);

            // Return success to indicate that the operation should be continued
            return new FlashErrorRecoveryResult(true, $"Operation resumed (attempt {retryCount} of {MaxRetries})");
        }

        /// <summary>
        /// Disposes the strategy
        /// </summary>
        public void Dispose()
        {
            // Nothing to dispose
        }
    }
}
