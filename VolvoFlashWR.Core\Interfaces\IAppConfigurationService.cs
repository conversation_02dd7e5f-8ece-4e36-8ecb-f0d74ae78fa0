using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for application configuration service
    /// </summary>
    public interface IAppConfigurationService
    {
        /// <summary>
        /// Event triggered when configuration is changed
        /// </summary>
        event EventHandler<string> ConfigurationChanged;

        /// <summary>
        /// Initializes the configuration service
        /// </summary>
        /// <param name="configFilePath">Path to the configuration file (optional)</param>
        /// <returns>True if initialization is successful, false otherwise</returns>
        Task<bool> InitializeAsync(string configFilePath = null);

        /// <summary>
        /// Gets a configuration value by key
        /// </summary>
        /// <typeparam name="T">The type of the configuration value</typeparam>
        /// <param name="key">The configuration key</param>
        /// <param name="defaultValue">The default value to return if the key is not found</param>
        /// <returns>The configuration value, or the default value if not found</returns>
        T GetValue<T>(string key, T defaultValue = default);

        /// <summary>
        /// Sets a configuration value
        /// </summary>
        /// <typeparam name="T">The type of the configuration value</typeparam>
        /// <param name="key">The configuration key</param>
        /// <param name="value">The configuration value</param>
        /// <returns>True if the value was set successfully, false otherwise</returns>
        Task<bool> SetValueAsync<T>(string key, T value);

        /// <summary>
        /// Gets all configuration values
        /// </summary>
        /// <returns>Dictionary of all configuration values</returns>
        Dictionary<string, object> GetAllValues();

        /// <summary>
        /// Saves the current configuration to the file
        /// </summary>
        /// <returns>True if the configuration was saved successfully, false otherwise</returns>
        Task<bool> SaveConfigurationAsync();

        /// <summary>
        /// Loads the configuration from the file
        /// </summary>
        /// <returns>True if the configuration was loaded successfully, false otherwise</returns>
        Task<bool> LoadConfigurationAsync();

        /// <summary>
        /// Resets the configuration to default values
        /// </summary>
        /// <returns>True if the configuration was reset successfully, false otherwise</returns>
        Task<bool> ResetToDefaultsAsync();

        /// <summary>
        /// Gets the path to the configuration file
        /// </summary>
        string ConfigFilePath { get; }

        /// <summary>
        /// Gets whether the configuration has been modified since last save
        /// </summary>
        bool IsModified { get; }
    }
}
