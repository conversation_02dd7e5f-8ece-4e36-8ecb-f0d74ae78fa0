﻿Chapter 13
Analog-to-Digital Converter (ADC12B16CV1)

Table 13-1. Revision History

Revision Sections
Revision Date Description of Changes

Number Affected

V01.00 13 Oct. 2005 - Initial version

V01.01 04 Mar. 2008 corrected reference to DJM bit

13.1 Introduction
The ADC12B16C is a 16-channel, 12-bit, multiplexed input successive approximation analog-to-digital
converter. Refer to device electrical specifications for ATD accuracy.

13.1.1 Features
• 8-, 10-, or 12-bit resolution.
• Conversion in Stop Mode using internally generated clock
• Automatic return to low power after conversion sequence
• Automatic compare with interrupt for higher than or less/equal than programmable value
• Programmable sample time.
• Left/right justified result data.
• External trigger control.
• Sequence complete interrupt.
• Analog input multiplexer for 16 analog input channels.
• Special conversions for VRH, VRL, (VRL+VRH)/2.
• 1-to-16 conversion sequence lengths.
• Continuous conversion mode.
• Multiple channel scans.
• Configurable external trigger functionality on any AD channel or any of four additional trigger

inputs. The four additional trigger inputs can be chip external or internal. Refer to device
specification for availability and connectivity.

• Configurable location for channel wrap around (when converting multiple channels in a sequence).

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 503



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

13.1.2 Modes of Operation

******** Conversion Modes
There is software programmable selection between performing single or continuous conversion on a
single channel or multiple channels.

******** MCU Operating Modes
• Stop Mode

— ICLKSTP=0 (in ATDCTL2 register)
Entering Stop Mode aborts any conversion sequence in progress and if a sequence was aborted
restarts it after exiting stop mode. This has the same effect/consequences as starting a
conversion sequence with write to ATDCTL5. So after exiting from stop mode with a
previously aborted sequence all flags are cleared etc.

— ICLKSTP=1 (in ATDCTL2 register)
A/D conversion sequence seamless continues in Stop Mode based on the internally generated
clock ICLK as ATD clock. For conversions during transition from Run to Stop Mode or vice
versa the result is not written to the results register, no CCF flag is set and no compare is done.
When converting in Stop Mode (ICLKSTP=1) an ATD Stop Recovery time tATDSTPRCV is
required to switch back to bus clock based ATDCLK when leaving Stop Mode. Do not access
ATD registers during this time.

• Wait Mode
ADC12B16C behaves same in Run and Wait Mode. For reduced power consumption continuos
conversions should be aborted before entering Wait mode.

• Freeze Mode
In Freeze Mode the ADC12B16C will either continue or finish or stop converting according to the
FRZ1 and FRZ0 bits. This is useful for debugging and emulation.

MC9S12XE-Family Reference Manual  Rev. 1.25

504 Freescale Semiconductor



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

13.1.3 Block Diagram

Bus Clock ICLK
Clock Internal ATD_12B16C

Prescaler Clock

ATD Clock
Sequence Complete

 ETRIG0 Trigger Interrupt
 ETRIG1 Mux

Mode and
 ETRIG2 Compare Interrupt
 ETRIG3 Timing Control

(See device specifi-
cation for availability
and connectivity)

ATDCTL1 ATDDIEN

Results
ATD 0
ATD 1
ATD 2

 VDDA ATD 3
 V ATD 4

SSA ATD 5
Successive

 V ATD 6
RH Approximation ATD 7

 VRL Register (SAR) ATD 8
ATD 9

AN15 and DAC ATD 10
ATD 11

AN14 ATD 12
ATD 13

AN13 ATD 14
ATD 15

AN12

AN11
+

AN10 Sample & Hold
   AN9

-
 AN8

Analog Comparator

MUX
 AN7

 AN6

 AN5

 AN4

 AN3

 AN2

 AN1

 AN0

Figure 13-1. ADC12B16C Block Diagram

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 505



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

13.2 Signal Description
This section lists all inputs to the ADC12B16C block.

13.2.1 Detailed Signal Descriptions

******** ANx (x = 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0)
This pin serves as the analog input Channel x. It can also be configured as digital port or external trigger
for the ATD conversion.

******** ETRIG3, ETRIG2, ETRIG1, ETRIG0
These inputs can be configured to serve as an external trigger for the ATD conversion.

Refer to device specification for availability and connection of these inputs!

******** VRH, VRL
VRH is the high reference voltage, VRL is the low reference voltage for ATD conversion.

******** VDDA, VSSA
These pins are the power supplies for the analog circuitry of the ADC12B16C block.

13.3 Memory Map and Register Definition
This section provides a detailed description of all registers accessible in the ADC12B16C.

13.3.1 Module Memory Map
Figure 13-2 gives an overview on all ADC12B16C registers.

NOTE
Register Address = Base Address + Address Offset, where the Base Address
is defined at the MCU level and the Address Offset is defined at the module
level.

Address Name Bit 7 6 5 4 3 2 1 Bit 0
R 0 0 0

0x0000 ATDCTL0 Reserved WRAP3 WRAP2 WRAP1 WRAP0
W
R

0x0001 ATDCTL1 ETRIGSEL SRES1 SRES0 SMP_DIS ETRIGCH3 ETRIGCH2 ETRIGCH1 ETRIGCH0
W
R 0

0x0002 ATDCTL2 AFFC ICLKSTP ETRIGLE ETRIGP ETRIGE ASCIE ACMPIE
W

= Unimplemented or Reserved

Figure 13-2. ADC12B16C Register Summary (Sheet 1 of 3)

MC9S12XE-Family Reference Manual  Rev. 1.25

506 Freescale Semiconductor



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

Address Name Bit 7 6 5 4 3 2 1 Bit 0
R

0x0003 ATDCTL3 DJM S8C S4C S2C S1C FIFO FRZ1 FRZ0
W
R

0x0004 ATDCTL4 SMP2 SMP1 SMP0 PRS[4:0]
W
R 0

0x0005 ATDCTL5 SC SCAN MULT CD CC CB CA
W
R 0 CC3 CC2 CC1 CC0

0x0006 ATDSTAT0 SCF ETORF FIFOR
W

Unimple- R 0 0 0 0 0 0 0 0
0x0007

mented W
R

0x0008 ATDCMPEH CMPE[15:8]
W
R

0x0009 ATDCMPEL CMPE[7:0]
W
R CCF[15:8]

0x000A ATDSTAT2H
W
R CCF[7:0]

0x000B ATDSTAT2L
W
R

0x000C ATDDIENH IEN[15:8]
W
R

0x000D ATDDIENL IEN[7:0]
W
R

0x000E ATDCMPHTH CMPHT[15:8]
W
R

0x000F ATDCMPHTL CMPHT[7:0]
W
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x0010 ATDDR0
W and Section *********.2, “Right Justified Result Data (DJM=1)”
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x0012 ATDDR1
W and Section *********.2, “Right Justified Result Data (DJM=1)”
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x0014 ATDDR2
W and Section *********.2, “Right Justified Result Data (DJM=1)”
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x0016 ATDDR3
W and Section *********.2, “Right Justified Result Data (DJM=1)”
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x0018 ATDDR4
W and Section *********.2, “Right Justified Result Data (DJM=1)”
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x001A ATDDR5
W and Section *********.2, “Right Justified Result Data (DJM=1)”
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x001C ATDDR6
W and Section *********.2, “Right Justified Result Data (DJM=1)”
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x001E ATDDR7
W and Section *********.2, “Right Justified Result Data (DJM=1)”
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x0020 ATDDR8
W and Section *********.2, “Right Justified Result Data (DJM=1)”
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x0022 ATDDR9
W and Section *********.2, “Right Justified Result Data (DJM=1)”

= Unimplemented or Reserved

Figure 13-2. ADC12B16C Register Summary (Sheet 2 of 3)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 507



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

Address Name Bit 7 6 5 4 3 2 1 Bit 0
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x0024 ATDDR10
W and Section *********.2, “Right Justified Result Data (DJM=1)”
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x0026 ATDDR11
W and Section *********.2, “Right Justified Result Data (DJM=1)”
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x0028 ATDDR12
W and Section *********.2, “Right Justified Result Data (DJM=1)”
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x002A ATDDR13
W and Section *********.2, “Right Justified Result Data (DJM=1)”
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x002C ATDDR14
W and Section *********.2, “Right Justified Result Data (DJM=1)”
R See Section *********.1, “Left Justified Result Data (DJM=0)”

0x002E ATDDR15
W and Section *********.2, “Right Justified Result Data (DJM=1)”

= Unimplemented or Reserved

Figure 13-2. ADC12B16C Register Summary (Sheet 3 of 3)

13.3.2 Register Descriptions
This section describes in address order all the ADC12B16C registers and their individual bits.

******** ATD Control Register 0 (ATDCTL0)
Writes to this register will abort current conversion sequence.

 Module Base + 0x0000

7 6 5 4 3 2 1 0

R 0 0 0
Reserved WRAP3 WRAP2 WRAP1 WRAP0

W
Reset 0 0 0 0 1 1 1 1

= Unimplemented or Reserved

Figure 13-3. ATD Control Register 0 (ATDCTL0)
Read: Anytime

Write: Anytime, in special modes always write 0 to Reserved Bit 7.
Table 13-2. ATDCTL0 Field Descriptions

Field Description

3-0 Wrap Around Channel Select Bits — These bits determine the channel for wrap around when doing multi-
WRAP[3-0] channel conversions. The coding is summarized in Table 13-3.

Table 13-3. Multi-Channel Wrap Around Coding

Multiple Channel Conversions (MULT = 1)
WRAP3 WRAP2 WRAP1 WRAP0

Wraparound to AN0 after Converting

0 0 0 0 Reserved(1)

MC9S12XE-Family Reference Manual  Rev. 1.25

508 Freescale Semiconductor



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

Table 13-3. Multi-Channel Wrap Around Coding

Multiple Channel Conversions (MULT = 1)
WRAP3 WRAP2 WRAP1 WRAP0

Wraparound to AN0 after Converting

0 0 0 1 AN1
0 0 1 0 AN2
0 0 1 1 AN3
0 1 0 0 AN4
0 1 0 1 AN5
0 1 1 0 AN6
0 1 1 1 AN7
1 0 0 0 AN8
1 0 0 1 AN9
1 0 1 0 AN10
1 0 1 1 AN11
1 1 0 0 AN12
1 1 0 1 AN13
1 1 1 0 AN14
1 1 1 1 AN15

1. If only AN0 should be converted use MULT=0.

******** ATD Control Register 1 (ATDCTL1)
Writes to this register will abort current conversion sequence.

 Module Base + 0x0001

7 6 5 4 3 2 1 0

R
ETRIGSEL SRES1 SRES0 SMP_DIS ETRIGCH3 ETRIGCH2 ETRIGCH1 ETRIGCH0

W

Reset 0 0 1 0 1 1 1 1

Figure 13-4. ATD Control Register 1 (ATDCTL1)

Read: Anytime

Write: Anytime
Table 13-4. ATDCTL1 Field Descriptions

Field Description

7 External Trigger Source Select — This bit selects the external trigger source to be either one of the AD
ETRIGSEL channels or one of the ETRIG3-0 inputs. See device specification for availability and connectivity of ETRIG3-

0 inputs. If a particular ETRIG3-0 input option is not available, writing a 1 to ETRISEL only sets the bit but has
not effect, this means that one of the AD channels (selected by ETRIGCH3-0) is configured as the source for
external trigger. The coding is summarized in Table 13-6.

6–5 A/D Resolution Select — These bits select the resolution of A/D conversion results. See Table 13-5 for
SRES[1:0] coding.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 509



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

Table 13-4. ATDCTL1 Field Descriptions (continued)

Field Description

4 Discharge Before Sampling Bit
SMP_DIS 0 No discharge before sampling.

1 The internal sample capacitor is discharged before sampling the channel. This adds 2 ATD clock cycles to
the sampling time. This can help to detect an open circuit instead of measuring the previous sampled
channel.

3–0 External Trigger Channel Select — These bits select one of the AD channels or one of the ETRIG3-0 inputs
ETRIGCH[3:0] as source for the external trigger. The coding is summarized in Table 13-6.

Table 13-5. A/D Resolution Coding

SRES1 SRES0 A/D Resolution
0 0 8-bit data
0 1 10-bit data
1 0 12-bit data
1 1 Reserved

Table 13-6. External Trigger Channel Select Coding

ETRIGSEL ETRIGCH3 ETRIGCH2 ETRIGCH1 ETRIGCH0 External trigger source is

0 0 0 0 0 AN0
0 0 0 0 1 AN1
0 0 0 1 0 AN2
0 0 0 1 1 AN3
0 0 1 0 0 AN4
0 0 1 0 1 AN5
0 0 1 1 0 AN6
0 0 1 1 1 AN7
0 1 0 0 0 AN8
0 1 0 0 1 AN9
0 1 0 1 0 AN10
0 1 0 1 1 AN11
0 1 1 0 0 AN12
0 1 1 0 1 AN13
0 1 1 1 0 AN14
0 1 1 1 1 AN15
1 0 0 0 0 ETRIG0(1)

1 0 0 0 1 ETRIG11

1 0 0 1 0 ETRIG21

1 0 0 1 1 ETRIG31

1 0 1 X X Reserved
1 1 X X X Reserved

1. Only if ETRIG3-0 input option is available (see device specification), else ETRISEL is ignored, that means
external trigger source is still on one of the AD channels selected by ETRIGCH3-0

MC9S12XE-Family Reference Manual  Rev. 1.25

510 Freescale Semiconductor



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

******** ATD Control Register 2 (ATDCTL2)
Writes to this register will abort current conversion sequence.

 Module Base + 0x0002

7 6 5 4 3 2 1 0

R 0
AFFC ICLKSTP ETRIGLE ETRIGP ETRIGE ASCIE ACMPIE

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 13-5. ATD Control Register 2 (ATDCTL2)

Read: Anytime

Write: Anytime
Table 13-7. ATDCTL2 Field Descriptions

Field Description

6 ATD Fast Flag Clear All
AFFC 0 ATD flag clearing done by write 1 to respective CCF[n] flag.

1 Changes all ATD conversion complete flags to a fast clear sequence.
For compare disabled (CMPE[n]=0) a read access to the result register will cause the associated CCF[n] flag
to clear automatically.
For compare enabled (CMPE[n]=1) a write access to the result register will cause the associated CCF[n] flag
to clear automatically.

5 Internal Clock in Stop Mode Bit — This bit enables A/D conversions in stop mode. When going into stop mode
ICLKSTP and ICLKSTP=1 the ATD conversion clock is automatically switched to the internally generated clock ICLK.

Current conversion sequence will seamless continue. Conversion speed will change from prescaled bus
frequency to the ICLK frequency (see ATD Electrical Characteristics in device description). The prescaler bits
PRS4-0 in ATDCTL4 have no effect on the ICLK frequency. For conversions during stop mode the automatic
compare interrupt or the sequence complete interrupt can be used to inform software handler about changing
A/D values. External trigger will not work while converting in stop mode. For conversions during transition from
Run to Stop Mode or vice versa the result is not written to the results register, no CCF flag is set and no compare
is done. When converting in Stop Mode (ICLKSTP=1) an ATD Stop Recovery time tATDSTPRCV is required to
switch back to bus clock based ATDCLK when leaving Stop Mode. Do not access ATD registers during this time.
0 If A/D conversion sequence is ongoing when going into stop mode, the actual conversion sequence will be

aborted and automatically restarted when exiting stop mode.
1 A/D continues to convert in stop mode using internally generated clock (ICLK)

4 External Trigger Level/Edge Control — This bit controls the sensitivity of the external trigger signal. See
ETRIGLE Table 13-8 for details.

3 External Trigger Polarity — This bit controls the polarity of the external trigger signal. See Table 13-8 for details.
ETRIGP

2 External Trigger Mode Enable — This bit enables the external trigger on one of the AD channels or one of the
ETRIGE ETRIG3-0 inputs as described in Table 13-6. If external trigger source is one of the AD channels, the digital input

buffer of this channel is enabled. The external trigger allows to synchronize the start of conversion with external
events. External trigger will not work while converting in stop mode.
0 Disable external trigger
1 Enable external trigger

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 511



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

Table 13-7. ATDCTL2 Field Descriptions (continued)

Field Description

1 ATD Sequence Complete Interrupt Enable
ASCIE 0 ATD Sequence Complete interrupt requests are disabled.

1 ATD Sequence Complete interrupt will be requested whenever SCF=1 is set.

0 ATD Compare Interrupt Enable — If automatic compare is enabled for conversion n (CMPE[n]=1 in ATDCMPE
ACMPIE register) this bit enables the compare interrupt. If the CCF[n] flag is set (showing a successful compare for

conversion n), the compare interrupt is triggered.
0 ATD Compare interrupt requests are disabled.
1 For the conversions in a sequence for which automatic compare is enabled (CMPE[n]=1), ATD Compare

Interrupt will be requested whenever any of the respective CCF flags is set.

Table 13-8. External Trigger Configurations

ETRIGLE ETRIGP External Trigger Sensitivity

0 0 Falling edge
0 1 Rising edge
1 0 Low level
1 1 High level

******** ATD Control Register 3 (ATDCTL3)
Writes to this register will abort current conversion sequence.

 Module Base + 0x0003

7 6 5 4 3 2 1 0

R
DJM S8C S4C S2C S1C FIFO FRZ1 FRZ0

W

Reset 0 0 1 0 0 0 0 0

= Unimplemented or Reserved

Figure 13-6. ATD Control Register 3 (ATDCTL3)

Read: Anytime

Write: Anytime

Field Description

7 Result Register Data Justification — Result data format is always unsigned. This bit controls justification of
DJM conversion data in the result registers.

0 Left justified data in the result registers.
1 Right justified data in the result registers.
Table 13-10 gives examples ATD results for an input signal range between 0 and 5.12 Volts.

Table 13-9. ATDCTL3 Field Descriptions

MC9S12XE-Family Reference Manual  Rev. 1.25

512 Freescale Semiconductor



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

Field Description

6–3 Conversion Sequence Length — These bits control the number of conversions per sequence. Table 13-11
S8C, S4C, shows all combinations. At reset, S4C is set to 1 (sequence length is 4). This is to maintain software continuity
S2C, S1C to HC12 family.

2 Result Register FIFO Mode — If this bit is zero (non-FIFO mode), the A/D conversion results map into the result
FIFO registers based on the conversion sequence; the result of the first conversion appears in the first result register

(ATDDR0), the second result in the second result register (ATDDR1), and so on.
If this bit is one (FIFO mode) the conversion counter is not reset at the beginning or ending of a conversion
sequence; sequential conversion results are placed in consecutive result registers. In a continuously scanning
conversion sequence, the result register counter will wrap around when it reaches the end of the result register
file. The conversion counter value (CC3-0 in ATDSTAT0) can be used to determine where in the result register
file, the current conversion result will be placed.
Aborting a conversion or starting a new conversion clears the conversion counter even if FIFO=1. So the first
result of a new conversion sequence, started by writing to ATDCTL5, will always be place in the first result register
(ATDDDR0). Intended usage of FIFO mode is continuos conversion (SCAN=1) or triggered conversion
(ETRIG=1).
Which result registers hold valid data can be tracked using the conversion complete flags. Fast flag clear mode
may or may not be useful in a particular application to track valid data.
If this bit is one, automatic compare of result registers is always disabled, that is ADC12B16C will behave as if
ACMPIE and all CPME[n] were zero.
0 Conversion results are placed in the corresponding result register up to the selected sequence length.
1 Conversion results are placed in consecutive result registers (wrap around at end).

1–0 Background Debug Freeze Enable — When debugging an application, it is useful in many cases to have the
FRZ[1:0] ATD pause when a breakpoint (Freeze Mode) is encountered. These 2 bits determine how the ATD will respond

to a breakpoint as shown in Table 13-12. Leakage onto the storage node and comparator reference capacitors
may compromise the accuracy of an immediately frozen conversion depending on the length of the freeze period.

Table 13-9. ATDCTL3 Field Descriptions (continued)

Table 13-10. Examples of ideal decimal ATD Results

12-Bit
Input Signal 8-Bit 10-Bit Codes
VRL = 0 Volts  Codes Codes (transfer curve has

VRH = 5.12 Volts (resolution=20mV) (resolution=5mV) 1.25mV offset)
(resolution=1.25mV)

5.120 Volts 255 1023 4095
... ... ... ...

0.022 1 4 17
0.020 1 4 16
0.018 1 4 14
0.016 1 3 12
0.014 1 3 11
0.012 1 2 9
0.010 1 2 8
0.008 0 2 6
0.006 0 1 4
0.004 0 1 3
0.003 0 0 2
0.002 0 0 1
0.000 0 0 0

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 513



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

Table 13-11. Conversion Sequence Length Coding

Number of Conversions
S8C S4C S2C S1C

per Sequence

0 0 0 0 16
0 0 0 1 1
0 0 1 0 2
0 0 1 1 3
0 1 0 0 4
0 1 0 1 5
0 1 1 0 6
0 1 1 1 7
1 0 0 0 8
1 0 0 1 9
1 0 1 0 10
1 0 1 1 11
1 1 0 0 12
1 1 0 1 13
1 1 1 0 14
1 1 1 1 15

Table 13-12. ATD Behavior in Freeze Mode (Breakpoint)

FRZ1 FRZ0 Behavior in Freeze Mode

0 0 Continue conversion
0 1 Reserved
1 0 Finish current conversion, then freeze
1 1 Freeze Immediately

******** ATD Control Register 4 (ATDCTL4)
Writes to this register will abort current conversion sequence.

 Module Base + 0x0004

7 6 5 4 3 2 1 0

R
SMP2 SMP1 SMP0 PRS[4:0]

W

Reset 0 0 0 0 0 1 0 1

Figure 13-7. ATD Control Register 4 (ATDCTL4)

Read: Anytime

Write: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

514 Freescale Semiconductor



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

Table 13-13. ATDCTL4 Field Descriptions

Field Description

7–5 Sample Time Select — These three bits select the length of the sample time in units of ATD conversion clock
SMP[2:0] cycles. Note that the ATD conversion clock period is itself a function of the prescaler value (bits PRS4-0).

Table 13-14 lists the available sample time lengths.

4–0 ATD Clock Prescaler — These 5 bits are the binary prescaler value PRS. The ATD conversion clock frequency
PRS[4:0] is calculated as follows:

fBUS
fATDCLK = ------------------------------------

2 × (PRS + 1)
Refer to Device Specification for allowed frequency range of fATDCLK.

Table 13-14. Sample Time Select

Sample Time
SMP2 SMP1 SMP0 in Number of

ATD Clock Cycles
0 0 0 4
0 0 1 6
0 1 0 8
0 1 1 10
1 0 0 12
1 0 1 16
1 1 0 20
1 1 1 24

******** ATD Control Register 5 (ATDCTL5)
Writes to this register will abort current conversion sequence and start a new conversion sequence. If
external trigger is enabled (ETRIGE=1) an initial write to ATDCTL5 is required to allow starting of a
conversion sequence which will then occur on each trigger event. Start of conversion means the beginning
of the sampling phase.

 Module Base + 0x0005

7 6 5 4 3 2 1 0

R 0
SC SCAN MULT CD CC CB CA

W

Reset 0 0 0 0 0 0 0 0

Figure 13-8. ATD Control Register 5 (ATDCTL5)

Read: Anytime

Write: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 515



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

Table 13-15. ATDCTL5 Field Descriptions

Field Description

6 Special Channel Conversion Bit — If this bit is set, then special channel conversion can be selected using CD,
SC CC, CB and CA of ATDCTL5. Table 13-16 lists the coding.

0 Special channel conversions disabled
1 Special channel conversions enabled

5 Continuous Conversion Sequence Mode — This bit selects whether conversion sequences are performed
SCAN continuously or only once. If external trigger is enabled (ETRIGE=1) setting this bit has no effect, that means

external trigger always starts a single conversion sequence.
0 Single conversion sequence
1 Continuous conversion sequences (scan mode)

4 Multi-Channel Sample Mode — When MULT is 0, the ATD sequence controller samples only from the specified
MULT analog input channel for an entire conversion sequence. The analog channel is selected by channel selection

code (control bits CD/CC/CB/CA located in ATDCTL5). When MULT is 1, the ATD sequence controller samples
across channels. The number of channels sampled is determined by the sequence length value (S8C, S4C, S2C,
S1C). The first analog channel examined is determined by channel selection code (CD, CC, CB, CA control bits);
subsequent channels sampled in the sequence are determined by incrementing the channel selection code or
wrapping around to AN0 (channel 0).
0 Sample only one channel
1 Sample across several channels

3–0 Analog Input Channel Select Code — These bits select the analog input channel(s) whose signals are
CD, CC, sampled and converted to digital codes. Table 13-16 lists the coding used to select the various analog input
CB, CA channels.

In the case of single channel conversions (MULT=0), this selection code specifies the channel to be examined.
In the case of multiple channel conversions (MULT=1), this selection code specifies the first channel to be
examined in the conversion sequence. Subsequent channels are determined by incrementing the channel
selection code or wrapping around to AN0 (after converting the channel defined by the Wrap Around Channel
Select Bits WRAP3-0 in ATDCTL0). In case of starting with a channel number higher than the one defined by
WRAP3-0 the first wrap around will be AN15 to AN0.

MC9S12XE-Family Reference Manual  Rev. 1.25

516 Freescale Semiconductor



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

Table 13-16. Analog Input Channel Select Coding

Analog Input
SC CD CC CB CA

Channel

0 0 0 0 0 AN0
0 0 0 1 AN1
0 0 1 0 AN2
0 0 1 1 AN3
0 1 0 0 AN4
0 1 0 1 AN5
0 1 1 0 AN6
0 1 1 1 AN7
1 0 0 0 AN8
1 0 0 1 AN9
1 0 1 0 AN10
1 0 1 1 AN11
1 1 0 0 AN12
1 1 0 1 AN13
1 1 1 0 AN14
1 1 1 1 AN15

1 0 0 0 0  Reserved
0 0 0 1  Reserved
0 0 1 X Reserved
0 1 0 0 VRH

0 1 0 1 VRL

0 1 1 0 (VRH+VRL) / 2
0 1 1 1 Reserved
1 X X X Reserved

13.3.2.7 ATD Status Register 0 (ATDSTAT0)
This register contains the Sequence Complete Flag, overrun flags for external trigger and FIFO mode, and
the conversion counter.

 Module Base + 0x0006

7 6 5 4 3 2 1 0

R 0 CC3 CC2 CC1 CC0
SCF ETORF FIFOR

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 13-9. ATD Status Register 0 (ATDSTAT0)

Read: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 517



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

Write: Anytime (No effect on (CC3, CC2, CC1, CC0))
Table 13-17. ATDSTAT0 Field Descriptions

Field Description

7 Sequence Complete Flag — This flag is set upon completion of a conversion sequence. If conversion
SCF sequences are continuously performed (SCAN=1), the flag is set after each one is completed. This flag is cleared

when one of the following occurs:
A) Write “1” to SCF
B) Write to ATDCTL5 (a new conversion sequence is started)
C) If AFFC=1 and read of a result register

0 Conversion sequence not completed
1 Conversion sequence has completed

5 External Trigger Overrun Flag — While in edge trigger mode (ETRIGLE=0), if additional active edges are
ETORF detected while a conversion sequence is in process the overrun flag is set. This flag is cleared when one of the

following occurs:
A) Write “1” to ETORF
B) Write to ATDCTL0,1,2,3,4, ATDCMPE or ATDCMPHT (a conversion sequence is aborted)
C) Write to ATDCTL5 (a new conversion sequence is started)

0 No External trigger over run error has occurred
1 External trigger over run error has occurred

4 Result Register Over Run Flag — This bit indicates that a result register has been written to before its
FIFOR associated conversion complete flag (CCF) has been cleared. This flag is most useful when using the FIFO mode

because the flag potentially indicates that result registers are out of sync with the input channels. However, it is
also practical for non-FIFO modes, and indicates that a result register has been over written before it has been
read (i.e. the old data has been lost). This flag is cleared when one of the following occurs:

A) Write “1” to FIFOR
B) Write to ATDCTL0,1,2,3,4, ATDCMPE or ATDCMPHT (a conversion sequence is aborted)
C) Write to ATDCTL5 (a new conversion sequence is started)

0 No over run has occurred
1 Overrun condition exists (result register has been written while associated CCFx flag was still set)

3–0 Conversion Counter — These 4 read-only bits are the binary value of the conversion counter. The conversion
CC[3:0] counter points to the result register that will receive the result of the current conversion. E.g. CC3=0, CC2=1,

CC1=1, CC0=0 indicates that the result of the current conversion will be in ATD Result Register 6. If in non-FIFO
mode (FIFO=0) the conversion counter is initialized to zero at the begin and end of the conversion sequence. If
in FIFO mode (FIFO=1) the register counter is not initialized. The conversion counters wraps around when its
maximum value is reached.
Aborting a conversion or starting a new conversion clears the conversion counter even if FIFO=1.

******** ATD Compare Enable Register (ATDCMPE)
Writes to this register will abort current conversion sequence.

Read: Anytime

Write: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

518 Freescale Semiconductor



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

 Module Base + 0x0008

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

CMPE[15:0]
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 13-10. ATD Compare Enable Register (ATDCMPE)

Table 13-18. ATDCMPE Field Descriptions

Field Description

15–0 Compare Enable for Conversion Number n (n= 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0) of a Sequence
CMPE[15:0] — These bits enable automatic compare of conversion results individually for conversions of a sequence. The

sense of each comparison is determined by the CMPHT[n] bit in the ATDCMPHT register.
For each conversion number with CMPE[n]=1 do the following:

1) Write compare value to ATDDRn result register
2) Write compare operator with CMPHT[n] in ATDCPMHT register

CCF[n] in ATDSTAT2 register will flag individual success of any comparison.
0 No automatic compare
1 Automatic compare of results for conversion n of a sequence is enabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 519



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

******** ATD Status Register 2 (ATDSTAT2)
This read-only register contains the Conversion Complete Flags CCF[15:0].

 Module Base + 0x000A

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R CCF[15:0]
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 13-11. ATD Status Register 2 (ATDSTAT2)

Read: Anytime

Write: Anytime, no effect
Table 13-19. ATDSTAT2 Field Descriptions

Field Description

15–0 Conversion Complete Flag n (n= 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0) — A conversion complete
CCF[15:0] flag is set at the end of each conversion in a sequence. The flags are associated with the conversion position in

a sequence (and also the result register number). Therefore in non-fifo mode, CCF[8] is set when the ninth
conversion in a sequence is complete and the result is available in result register ATDDR8; CCF[9] is set when
the tenth conversion in a sequence is complete and the result is available in ATDDR9, and so forth.
If automatic compare of conversion results is enabled (CMPE[n]=1 in ATDCMPE), the conversion complete flag
is only set if comparison with ATDDRn is true and if ACMPIE=1 a compare interrupt will be requested. In this
case, as the ATDDRn result register is used to hold the compare value, the result will not be stored there at the
end of the conversion but is lost.

A flag CCF[n] is cleared when one of the following occurs:
A) Write to ATDCTL5 (a new conversion sequence is started)
B) If AFFC=0, write “1” to CCF[n]
C) If AFFC=1 and CMPE[n]=0, read of result register ATDDRn
D) If AFFC=1 and CMPE[n]=1, write to result register ATDDRn

In case of a concurrent set and clear on CCF[n]: The clearing by method A) will overwrite the set. The clearing
by methods B) or C) or D) will be overwritten by the set.
0 Conversion number n not completed or successfully compared
1 If (CMPE[n]=0): Conversion number n has completed. Result is ready in ATDDRn.

If (CMPE[n]=1): Compare for conversion result number n with compare value in ATDDRn, using compare
operator CMPGT[n] is true. (No result available in ATDDRn)

MC9S12XE-Family Reference Manual  Rev. 1.25

520 Freescale Semiconductor



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

********* ATD Input Enable Register (ATDDIEN)

 Module Base + 0x000C

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

IEN[15:0]
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 13-12. ATD Input Enable Register (ATDDIEN)

Read: Anytime

Write: Anytime
Table 13-20. ATDDIEN Field Descriptions

Field Description

15–0 ATD Digital Input Enable on channel x (x= 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0) — This bit controls
IEN[15:0] the digital input buffer from the analog input pin (ANx) to the digital data register.

0 Disable digital input buffer to ANx pin
1 Enable digital input buffer on ANx pin.
Note: Setting this bit will enable the corresponding digital input buffer continuously. If this bit is set while

simultaneously using it as an analog port, there is potentially increased power consumption because the
digital input buffer maybe in the linear region.

********* ATD Compare Higher Than Register (ATDCMPHT)
Writes to this register will abort current conversion sequence.

Read: Anytime

Write: Anytime

 Module Base + 0x000E

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

CMPHT[15:0]
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 13-13. ATD Compare Higher Than Register (ATDCMPHT)

Table 13-21. ATDCMPHT Field Descriptions

Field Description

15–0 Compare Operation Higher Than Enable for conversion number n (n= 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5,
CMPHT[15:0] 4, 3, 2, 1, 0) of a Sequence — This bit selects the operator for comparison of conversion results.

0 If result of conversion n is lower or same than compare value in ATDDRn, this is flagged in ATDSTAT2
1 If result of conversion n is higher than compare value in ATDDRn, this is flagged in ATDSTAT2

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 521



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

********* ATD Conversion Result Registers (ATDDRn)
The A/D conversion results are stored in 16 result registers. Results are always in unsigned data
representation. Left and right justification is selected using the DJM control bit in ATDCTL3.

If automatic compare of conversions results is enabled (CMPE[n]=1 in ATDCMPE), these registers must
be written with the compare values in left or right justified format depending on the actual value of the
DJM bit. In this case, as the ATDDRn register is used to hold the compare value, the result will not be
stored there at the end of the conversion but is lost.

Read: Anytime

Write: Anytime

NOTE
For conversions not using automatic compare, results are stored in the result
registers after each conversion. In this case avoid writing to ATDDRn except
for initial values, because an A/D result might be overwritten.

*********.1 Left Justified Result Data (DJM=0)

Module Base +
0x0010 = ATDDR0, 0x0012 = ATDDR1, 0x0014 = ATDDR2, 0x0016 = ATDDR3
0x0018 = ATDDR4, 0x001A = ATDDR5, 0x001C = ATDDR6, 0x001E = ATDDR7
0x0020 = ATDDR8, 0x0022 = ATDDR9, 0x0024 = ATDDR10, 0x0026 = ATDDR11
0x0028 = ATDDR12, 0x002A = ATDDR13, 0x002C = ATDDR14, 0x002E = ATDDR15

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R 0 0 0 0

Bit 11 Bit 10 Bit 9 Bit 8 Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 13-14. Left justified ATD conversion result register (ATDDRn)

*********.2 Right Justified Result Data (DJM=1)

Module Base +
0x0010 = ATDDR0, 0x0012 = ATDDR1, 0x0014 = ATDDR2, 0x0016 = ATDDR3
0x0018 = ATDDR4, 0x001A = ATDDR5, 0x001C = ATDDR6, 0x001E = ATDDR7
0x0020 = ATDDR8, 0x0022 = ATDDR9, 0x0024 = ATDDR10, 0x0026 = ATDDR11
0x0028 = ATDDR12, 0x002A = ATDDR13, 0x002C = ATDDR14, 0x002E = ATDDR15

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R 0 0 0 0

Bit 11 Bit 10 Bit 9 Bit 8 Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bi1 1 Bit 0
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 13-15. Right justified ATD conversion result register (ATDDRn)

Table 13-16 shows how depending on the A/D resolution the conversion result is transferred to the ATD
result registers. Compare is always done using all 12 bits of both the conversion result and the compare
value in ATDDRn.

MC9S12XE-Family Reference Manual  Rev. 1.25

522 Freescale Semiconductor



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

Table 13-22. Conversion result mapping to ATDDRn

A/D conversion result mapping to
DJM

resolution ATDDRn

8-bit data 0 Bit[11:4] = result, Bit[3:0]=0000

8-bit data 1 Bit[7:0] = result, Bit[11:8]=0000

10-bit data 0 Bit[11:2] = result, Bit[1:0]=00

10-bit data 1 Bit[9:0] = result, Bit[11:10]=00

12-bit data X Bit[11:0] = result

13.4 Functional Description
The ADC12B16C is structured into an analog sub-block and a digital sub-block.

13.4.1 Analog Sub-Block
The analog sub-block contains all analog electronics required to perform a single conversion. Separate
power supplies VDDA and VSSA allow to isolate noise of other MCU circuitry from the analog sub-block.

******** Sample and Hold Machine
The Sample and Hold (S/H) Machine accepts analog signals from the external world and stores them as
capacitor charge on a storage node.

During the sample process the analog input connects directly to the storage node.

The input analog signals are unipolar and must fall within the potential range of VSSA to VDDA.

During the hold process the analog input is disconnected from the storage node.

******** Analog Input Multiplexer
The analog input multiplexer connects one of the 16 external analog input channels to the sample and hold
machine.

******** Analog-to-Digital (A/D) Machine
The A/D Machine performs analog to digital conversions. The resolution is program selectable at either 8
or 10 or 12 bits. The A/D machine uses a successive approximation architecture. It functions by comparing
the stored analog sample potential with a series of digitally generated analog potentials. By following a
binary search algorithm, the A/D machine locates the approximating potential that is nearest to the
sampled potential.

When not converting the A/D machine is automatically powered down.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 523



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

Only analog input signals within the potential range of VRL to VRH (A/D reference potentials) will result
in a non-railed digital output code.

13.4.2 Digital Sub-Block
This subsection explains some of the digital features in more detail. See Section 13.3.2, “Register
Descriptions” for all details.

******** External Trigger Input
The external trigger feature allows the user to synchronize ATD conversions to the external environment
events rather than relying on software to signal the ATD module when ATD conversions are to take place.
The external trigger signal (out of reset ATD channel 15, configurable in ATDCTL1) is programmable to
be edge or level sensitive with polarity control. Table 13-23 gives a brief description of the different
combinations of control bits and their effect on the external trigger function.

Table 13-23. External Trigger Control Bits

ETRIGLE ETRIGP ETRIGE SCAN Description

X X 0 0 Ignores external trigger. Performs one
conversion sequence and stops.

X X 0 1 Ignores external trigger. Performs
continuous conversion sequences.

0 0 1 X Falling edge triggered. Performs one
conversion sequence per trigger.

0 1 1 X Rising edge triggered. Performs one
conversion sequence per trigger.

1 0 1 X Trigger active low. Performs continuous
conversions while trigger is active.

1 1 1 X Trigger active high. Performs continuous
conversions while trigger is active.

During a conversion, if additional active edges are detected the overrun error flag ETORF is set.

In either level or edge triggered modes, the first conversion begins when the trigger is received.

Once ETRIGE is enabled, conversions cannot be started by a write to ATDCTL5, but rather must be
triggered externally.

If the level mode is active and the external trigger both de-asserts and re-asserts itself during a conversion
sequence, this does not constitute an overrun. Therefore, the flag is not set. If the trigger is left asserted in
level mode while a sequence is completing, another sequence will be triggered immediately.

MC9S12XE-Family Reference Manual  Rev. 1.25

524 Freescale Semiconductor



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

******** General-Purpose Digital Port Operation
The input channel pins can be multiplexed between analog and digital data. As analog inputs, they are
multiplexed and sampled as analog channels to the A/D converter. The analog/digital multiplex operation
is performed in the input pads. The input pad is always connected to the analog input channels of the
ADC12B16C. The input pad signal is buffered to the digital port registers. This buffer can be turned on or
off with the ATDDIEN register. This is important so that the buffer does not draw excess current when
analog potentials are presented at its input.

13.5 Resets
At reset the ADC12B16C is in a power down state. The reset state of each individual bit is listed within
the Register Description section (see Section 13.3.2, “Register Descriptions”) which details the registers
and their bit-field.

13.6 Interrupts
The interrupts requested by the ADC12B16C are listed in Table 13-24. Refer to MCU specification for
related vector address and priority.

Table 13-24. ATD Interrupt Vectors

CCR
Interrupt Source Local Enable

Mask

Sequence Complete Interrupt I bit ASCIE in ATDCTL2

Compare Interrupt I bit ACMPIE in ATDCTL2

See Section 13.3.2, “Register Descriptions” for further details.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 525



Chapter 13 Analog-to-Digital Converter (ADC12B16CV1)

MC9S12XE-Family Reference Manual  Rev. 1.25

526 Freescale Semiconductor