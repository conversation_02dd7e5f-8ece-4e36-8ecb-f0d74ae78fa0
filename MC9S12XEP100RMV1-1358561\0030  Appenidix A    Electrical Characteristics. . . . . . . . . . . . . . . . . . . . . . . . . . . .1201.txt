﻿Appendix A Electrical Characteristics

Appendix A
Electrical Characteristics

A.1 General
NOTE

The electrical characteristics given in this section should be used as a guide
only. Values cannot be guaranteed by Freescale and are subject to change
without notice.

This supplement contains the most accurate electrical information for the MC9S12XE-Family
microcontroller available at the time of publication.

This introduction is intended to give an overview on several common topics like power supply, current
injection etc.

A.1.1 Parameter Classification
The electrical parameters shown in this supplement are guaranteed by various methods. To give the
customer a better understanding the following classification is used and the parameters are tagged
accordingly in the tables where appropriate.

NOTE
This classification is shown in the column labeled “C” in the parameter
tables where appropriate.

P: Those parameters are guaranteed during production testing on each individual device.
C: Those parameters are achieved by the design characterization by measuring a statistically relevant

sample size across process variations.
T: Those parameters are achieved by design characterization on a small sample size from typical

devices under typical conditions unless otherwise noted. All values shown in the typical column
are within this category.

D: Those parameters are derived mainly from simulations.

A.1.2 Power Supply
The MC9S12XE-Family utilizes several pins to supply power to the I/O ports, A/D converter, oscillator,
and PLL as well as the digital core.

The VDDA, VSSA pin pairs supply the A/D converter and parts of the internal voltage regulator.

The VDDX, VSSX pin pairs [7:1] supply the I/O pins.

VDDR supplies the internal voltage regulator.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1201



Appendix A Electrical Characteristics

NOTE
Connecting VDDR to VSS disables the internal voltage regulator.

The VDDF, VSS1 pin pair supplies the internal NVM logic.

The VDD, VSS2 are the supply pins for the internal digital logic.

VDDPLL, VSSPLL pin pair supply the oscillator and the PLL.

VSS1, VSS2 and VSS3 are internally connected by metal.

VDDA1, and VDDA2 are internally connected by metal.

All VDDX pins are internally connected by metal.

All VSSX pins are internally connected by metal.

VDDA is connected to all VDDX pins by diodes for ESD protection such that VDDX must not exceed
VDDA by more than a diode voltage drop. VDDA can exceed VDDX by more than a diode drop in order
to support applications with a 5V A/D converter range and 3.3V I/O pin range.
VSSA and VSSX are connected by anti-parallel diodes for ESD protection.

NOTE
In the following context VDD35 is used for either VDDA, VDDR, and
VDDX; VSS35 is used for either VSSA and VSSX unless otherwise noted.

IDD35 denotes the sum of the currents flowing into the VDDA and VDDR
pins. The Run mode current in the VDDX domain is external load
dependent.

VDD is used for VDD, VSS is used for VSS1, VSS2 and VSS3.

VDDPLL is used for VDDPLL, VSSPLL is used for VSSPLL

IDD is used for the sum of the currents flowing into VDD, VDDF and
VDDPLL.

A.1.3 Pins
There are four groups of functional pins.

A.1.3.1 I/O Pins
Standard I/O pins have a level in the range of 3.13V to 5.5 V. This class of pins is comprised of all port I/O
pins (including PortAD), BKGD and the RESET pins.The internal structure of all those pins is identical;
however, some of the functionality may be disabled. For example the BKGD pin pull up is always enabled.

A.1.3.2 Analog Reference
This group is made up by the VRH and VRL pins.

MC9S12XE-Family Reference Manual  Rev. 1.25

1202 Freescale Semiconductor



Appendix A Electrical Characteristics

A.1.3.3 Oscillator
The pins EXTAL, XTAL dedicated to the oscillator have a nominal 1.8 V level. They are supplied by
VDDPLL.

A.1.3.4 TEST
This pin is used for production testing only.

A.1.4 Current Injection
Power supply must maintain regulation within operating VDD35 or VDD range during instantaneous and
operating maximum current conditions. If positive injection current (Vin > VDD35) is greater than IDD35,
the injection current may flow out of VDD35 and could result in external power supply going out of
regulation. Ensure external VDD35 load will shunt current greater than maximum injection current. This
will be the greatest risk when the MCU is not consuming power; e.g., if no system clock is present, or if
clock rate is very low which would reduce overall power consumption.

A.1.5 Absolute Maximum Ratings
Absolute maximum ratings are stress ratings only. A functional operation under or outside those maxima
is not guaranteed. Stress beyond those limits may affect the reliability or cause permanent damage of the
device.

This device contains circuitry protecting against damage due to high static voltage or electrical fields;
however, it is advised that normal precautions be taken to avoid application of any voltages higher than

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1203



Appendix A Electrical Characteristics

maximum-rated voltages to this high-impedance circuit. Reliability of operation is enhanced if unused
inputs are tied to an appropriate logic voltage level (e.g., either VSS35 or VDD35).

Table A-1. Absolute Maximum Ratings(1)

Num Rating Symbol Min Max Unit

1 I/O, regulator and analog supply voltage VDD35 –0.3 6.0 V

2 Digital logic supply voltage(2) VDD –0.3 2.16 V

3 PLL supply voltage2 VDDPLL –0.3 2.16 V

4 NVM supply voltage2 VDDF –0.3 3.6 V

5 Voltage difference VDDX to VDDA ∆VDDX –6.0 0.3 V

6 Voltage difference VSSX to VSSA ∆VSSX –0.3 0.3 V

7 Digital I/O input voltage VIN –0.3 6.0 V

8 Analog reference VRH, VRL –0.3 6.0 V

9 EXTAL, XTAL VILV –0.3 2.16 V

10 TEST input VTEST –0.3 10.0 V

11 Instantaneous maximum current I –25 +25 mA
Single pin limit for all digital I/O pins(3) D

12 Instantaneous maximum current I –25 +25 mA
Single pin limit for EXTAL, XTAL(4) DL

13 Instantaneous maximum current I –0.25 0 mA
Single pin limit for TEST (5) DT

14 Maximum current I –100 +100 mA
DV

Single pin limit for power supply pins

15 Storage temperature range Tstg –65 155 °C
1. Beyond absolute maximum ratings device might be damaged.
2. The device contains an internal voltage regulator to generate the logic and PLL supply out of the I/O supply. The absolute

maximum ratings apply when the device is powered from an external source.
3. All digital I/O pins are internally clamped to VSSX and VDDX, or VSSA and VDDA.
4. Those pins are internally clamped to VSSPLL and VDDPLL.
5. This pin is clamped low to VSSPLL, but not clamped high. This pin must be tied low in applications.

A.1.6 ESD Protection and Latch-up Immunity
All ESD testing is in conformity with CDF-AEC-Q100 stress test qualification for automotive grade
integrated circuits. During the device qualification ESD stresses were performed for the Human Body
Model (HBM) and the Charge Device Model.

A device will be defined as a failure if after exposure to ESD pulses the device no longer meets the device
specification. Complete DC parametric and functional testing is performed per the applicable device

MC9S12XE-Family Reference Manual  Rev. 1.25

1204 Freescale Semiconductor



Appendix A Electrical Characteristics

specification at room temperature followed by hot temperature, unless specified otherwise in the device
specification.

Table A-2. ESD and Latch-up Test Conditions

Model Description Symbol Value Unit

Human Body Series resistance R1 1500 Ohm

Storage capacitance C 100 pF

Number of pulse per pin
Positive — 1
Negative — 1

Charged Device Number of pulse per pin
Positive — 3
Negative — 3

Latch-up Minimum input voltage limit –2.5 V

Maximum input voltage limit 7.5 V

Table A-3. ESD and Latch-Up Protection Characteristics

Num C Rating Symbol Min Max Unit

1 C Human Body Model (HBM) VHBM 2000 — V

2 C Charge Device Model (CDM) corner pins VCDM 750 — V
Charge Device Model (CDM) edge pins 500 —

3 C Latch-up current at TA = 125°C ILAT mA
Positive +100 —
Negative –100 —

4 C Latch-up current at TA = 27°C ILAT mA
Positive +200 —
Negative –200 —

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1205



Appendix A Electrical Characteristics

A.1.7 Operating Conditions
This section describes the operating conditions of the device. Unless otherwise noted those conditions
apply to all the following data.

NOTE
Please refer to the temperature rating of the device (C, V, M) with regards to
the ambient temperature TA and the junction temperature TJ. For power
dissipation calculations refer to Section A.1.8, “Power Dissipation and
Thermal Characteristics”.

Table A-4. Operating Conditions

Rating Symbol Min Typ Max Unit

I/O, regulator and analog supply voltage VDD35 3.13 5 5.5 V

NVM logic supply voltage(1) VDDF 2.7 2.8 2.9 V

Voltage difference VDDX to VDDA ∆VDDX refer to Table A-15

Voltage difference VDDR to VDDX ∆VDDR –0.1 0 0.1 V

Voltage difference VSSX to VSSA ∆VSSX refer to Table A-15

Voltage difference VSS1 , VSS2 , VSS3 , VSSPLL to VSSX ∆VSS –0.1 0 0.1 V

Digital logic supply voltage1 VDD 1.72 1.8 1.98 V

PLL supply voltage VDDPLL 1.72 1.8 1.98 V

Oscillator(2) (Loop Controlled Pierce) fosc 4 — 16 MHz
(Full Swing Pierce) 2 — 40

Bus frequency(3) fbus 0.5 — 50 MHz

C Operating junction temperature range °C
Operating ambient temperature range(4) TJ –40 — 110

TA –40 27 85

V Operating junction temperature range °C
Operating ambient temperature range2 TJ –40 — 130

TA –40 27 105

M Operating junction temperature range °C
Operating ambient temperature range2 TJ –40 — 150

TA –40 27 125
1. The device contains an internal voltage regulator to generate the logic and PLL supply out of the I/O supply. .
2. This refers to the oscillator base frequency. Typical crystal & resonator tolerances are supported.
3. Please refer to Table A-25 for maximum bus frequency limits with frequency modulation enabled
4. Please refer to Section A.1.8, “Power Dissipation and Thermal Characteristics” for more details about the relation between

ambient temperature TA and device junction temperature TJ.

NOTE
Using the internal voltage regulator, operation is guaranteed in a power
down until a low voltage reset assertion.

MC9S12XE-Family Reference Manual  Rev. 1.25

1206 Freescale Semiconductor



Appendix A Electrical Characteristics

A.1.8 Power Dissipation and Thermal Characteristics
Power dissipation and thermal characteristics are closely related. The user must assure that the maximum
operating junction temperature is not exceeded. The average chip-junction temperature (TJ) in °C can be
obtained from:

TJ = TA + (PD • ΘJA)

TJ = Junction Temperature, [°C ]

TA = Ambient Temperature, [°C ]

PD = Total Chip Power Dissipation, [W]

ΘJA = Package Thermal Resistance, [°C/W]

The total power dissipation can be calculated from:

PD = PINT + PIO

PINT = Chip Internal Power Dissipation, [W]

2
PIO = ∑RDSON ⋅ IIOi

i

PIO is the sum of all output currents on I/O ports associated with VDDX, whereby
VOL

RDSON = ----------- ;for outputs driven low
IOL

VDD35 – VOH
RDSON = ---------------------------------------;for outputs driven high

IOH

Two cases with internal voltage regulator enabled and disabled must be considered:
1. Internal voltage regulator disabled

PINT = IDD ⋅ VDD + IDDPLL ⋅ VDDPLL + IDDA ⋅ VDDA
2. Internal voltage regulator enabled

PINT = IDDR ⋅ VDDR + IDDA ⋅ VDDA

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1207



Appendix A Electrical Characteristics

Table A-5. Thermal Package Characteristics (9S12XEP100)(1)

Num C Rating Symbol Min Typ Max Unit

208MAPBGA

1 D Thermal resistance 208MAPBGA, single sided PCB2 θJA — — 53 °C/W

2 D Thermal resistance208MAPBGA, double sided PCB θJA — — 31 °C/W
with 2 internal planes3

3 D Junction to Board 208MAPBGA(2) θJB — — 20 °C/W

4 D Junction to Case 208MAPBGA4 θJC — — 9 °C/W

5 D Junction to Package Top 208MAPBGA5 ΨJT — — 2 °C/W

LQFP144

6 D Thermal resistance LQFP144, single sided PCB3 θJA — — 41 °C/W

7 D Thermal resistance LQFP144, double sided PCB θJA — — 32 °C/W
with 2 internal planes3

8 D Junction to Board LQFP 144 θJB — — 22 °C/W

9 D Junction to Case LQFP 1444 θJC — — 7.4 °C/W

10 D Junction to Package Top LQFP1445 ΨJT — — 3 °C/W

LQFP112

11 D Thermal resistance LQFP112, single sided PCB(3) θJA — — 43 °C/W

12 D Thermal resistance LQFP112, double sided PCB θJA — — 32 °C/W
with 2 internal planes(4)

13 D Junction to Board LQFP112 θJB — — 22 °C/W

14 D Junction to Case LQFP1124 θJC — — 7 °C/W

15 D Junction to Package Top LQFP1125 ΨJT — — 3 °C/W

QFP80

16 D Thermal resistance QFP 80, single sided PCB3 θJA — — 45 °C/W

17 D Thermal resistance QFP 80, double sided PCB θJA — — 33 °C/W
with 2 internal planes3

18 D Junction to Board QFP 80 θJB — — 19 °C/W

19 D Junction to Case QFP 80(5) θJC — — 11 °C/W

20 D Junction to Package Top QFP 80(6) ΨJT — — 3 °C/W
1. The values for thermal resistance are achieved by package simulations for the 9S12XEP100 die.
2. Measured per JEDEC JESD51-8. Measured on top surface of the board near the package.
3. Junction to ambient thermal resistance, θJA was simulated to be equivalent to the JEDEC specification JESD51-2 in a

horizontal configuration in natural convection.
4. Junction to ambient thermal resistance, θJA was simulated to be equivalent to the JEDEC specification JESD51-7 in a

horizontal configuration in natural convection.
5. Junction to case thermal resistance was simulated to be equivalent to the measured values using the cold plate technique with

the cold plate temperature used as the “case” temperature. This basic cold plate measurement technique is described by MIL-
STD 883D, Method 1012.1. This is the correct thermal metric to use to calculate thermal performance when the package is
being used with a heat sink.

6. Thermal characterization parameter ΨJT is the “resistance” from junction to reference point thermocouple on top center of the
case as defined in JESD51-2. ΨJT is a useful value to use to estimate junction temperature in a steady state customer
enviroment.

MC9S12XE-Family Reference Manual  Rev. 1.25

1208 Freescale Semiconductor



Appendix A Electrical Characteristics

Table A-6. Thermal Package Characteristics (9S12XEQ512)(1)

Num C Rating Symbol Min Typ Max Unit

LQFP144

1a D Thermal resistance single sided PCB, natural convection θJA — — 49 °C/W

1b D Thermal resistance single sided PCB @ 200 ft/min θJA — — 40 °C/W

2a D Thermal resistance double sided PCB θJA — — 40 °C/W
with 2 internal planes, natural convection

2b D Thermal resistance double sided PCB θJA — — 34 °C/W
with 2 internal planes @ 200 ft/min

3 D Junction to Board LQFP 144 θJB — — 28 °C/W

4 D Junction to Case LQFP 1442. θJC — — 9 °C/W

5 D Junction to Package Top LQFP1443 ΨJT — — 2 °C/W

LQFP112

6a D Thermal resistance single sided PCB, natural convection θJA — — 50 °C/W

6b D Thermal resistance single sided PCB @ 200 ft/min θJA — — 40 °C/W

7a D Thermal resistance double sided PCB θJA — — 40 °C/W
with 2 internal planes, natural convection

7b D Thermal resistance double sided PCB θJA — — 34 °C/W
with 2 internal planes @ 200 ft/min

8 D Junction to Board LQFP112 θJB — — 28 °C/W

9 D Junction to Case LQFP1122. θJC — — 9 °C/W

10 D Junction to Package Top LQFP1123 ΨJT — — 2 °C/W

QFP80

11a D Thermal resistance single sided PCB, natural convection θJA — — 50 °C/W

11b D Thermal resistance single sided PCB @ 200 ft/min θJA — — 40 °C/W

12a D Thermal resistance double sided PCB θJA — — 37 °C/W
with 2 internal planes, natural convection

12b D Thermal resistance double sided PCB θJA — — 31 °C/W
with 2 internal planes @ 200 ft/min

13 D Junction to Board QFP 80 θJB — — 23 °C/W

14 D Junction to Case QFP 80(2) θJC — — 13 °C/W

15 D Junction to Package Top QFP 80(3) ΨJT — — 3 °C/W
1. The values for thermal resistance are achieved by package simulations for the 9S12XEQ512 die.
2. Junction to case thermal resistance was simulated to be equivalent to the measured values using the cold plate technique

with the cold plate temperature used as the “case” temperature. This basic cold plate measurement technique is described
by MIL-STD 883D, Method 1012.1. This is the correct thermal metric to use to calculate thermal performance when the
package is being used with a heat sink.

3. Thermal characterization parameter ΨJT is the “resistance” from junction to reference point thermocouple on top center of the
case as defined in JESD51-2. ΨJT is a useful value to use to estimate junction temperature in a steady state customer
enviroment.

A.1.9 I/O Characteristics

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1209



Appendix A Electrical Characteristics

This section describes the characteristics of all I/O pins except EXTAL, XTAL, TEST and supply pins.
s

Table A-7. 3.3-V I/O Characteristics

Conditions are 3.13 V < VDD35 < 3.6 V temperature from –40°C to +150°C, unless otherwise noted
I/O Characteristics for all I/O pins except EXTAL, XTAL,TEST and supply pins.

Num C Rating Symbol Min Typ Max Unit

1 P Input high voltage VIH 0.65*VDD35 — — V

T Input high voltage VIH — — VDD35 + 0.3 V

2 P Input low voltage VIL — — 0.35*VDD35 V

T Input low voltage VIL VSS35 – 0.3 — — V

3 T Input hysteresis VHYS — 250 — mV

4a P Input leakage current (pins in high impedance input I
in

mode)(1) Vin = VDD35 or VSS35
M Temperature range -40°C to 150°C –1 — 1 µA
V Temperature range -40°C to 130°C –0.75 — 0.75
C Temperature range -40°C to 110°C –0.5 — 0.5

4b C Input leakage current (pins in high impedance input I — — — nA
in

mode) Vin = VDD35 or VSS35 —
-40°C ±1
27°C ±1
70°C ±8
85°C ±14
100°C ±26
105°C ±32
110°C 40
120°C ±60
125°C ±74
130°C ±92
150°C ±240

5 C Output high voltage (pins in output mode) V V
OH DD35 – 0.4 — — V

Partial drive IOH = –0.75 mA

6 P Output high voltage (pins in output mode) VOH VDD35 – 0.4 — — V
Full drive IOH = –4 mA

7 C Output low voltage (pins in output mode) VOL — — 0.4 V
Partial Drive IOL = +0.9 mA

8 P Output low voltage (pins in output mode) V — — 0.4 V
OL

Full Drive IOL = +4.75 mA

9 P Internal pull up resistance RPUL 25 — 50 KΩ
VIH min > input voltage > VIL max

10 P Internal pull down resistance RPDH 25 — 50 KΩ
VIH min > input voltage > VIL max

11 D Input capacitance Cin — 6 — pF

12 T Injection current(2) — mA
Single pin limit IICS –2.5 2.5
Total device limit, sum of all injected currents IICP –25 25

13 D Port H, J, P interrupt input pulse filtered (STOP)(3) tPULSE — — 3 µs

MC9S12XE-Family Reference Manual  Rev. 1.25

1210 Freescale Semiconductor



Appendix A Electrical Characteristics

Table A-7. 3.3-V I/O Characteristics

Conditions are 3.13 V < VDD35 < 3.6 V temperature from –40°C to +150°C, unless otherwise noted
I/O Characteristics for all I/O pins except EXTAL, XTAL,TEST and supply pins.

14 D Port H, J, P interrupt input pulse passed(STOP)3 tPULSE 10 — — µs

15 D Port H, J, P interrupt input pulse filtered (STOP) tPULSE — — 3 tcyc

16 D Port H, J, P interrupt input pulse passed(STOP) tPULSE 4 — — tcyc

17 D IRQ pulse width, edge-sensitive mode (STOP) PWIRQ 1 — — tcyc

18 D XIRQ pulse width with X-bit set (STOP) PWXIRQ 4 — — tosc
1. Maximum leakage current occurs at maximum operating temperature.
2. Refer to Section A.1.4, “Current Injection” for more details
3. Parameter only applies in stop or pseudo stop mode.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1211



Appendix A Electrical Characteristics

Table A-8. 5V I/O Characteristics

Conditions are 4.5 V < VDD35 < 5.5 V temperature from –40°C to +150°C, unless otherwise noted
I/O Characteristics for all I/O pins except EXTAL, XTAL,TEST and supply pins.

Num C Rating Symbol Min Typ Max Unit

1 P Input high voltage V 0.65*V
IH DD35 — — V

T Input high voltage VIH — — VDD35 + 0.3 V

2 P Input low voltage VIL — — 0.35*VDD35 V

T Input low voltage VIL VSS35 – 0.3 — — V

3 T Input hysteresis VHYS — 250 — mV

4a P Input leakage current (pins in high impedance input I
mode)(1) in

 Vin = VDD35 or VSS35
M Temperature range -40°C to 150°C –1 — 1 µA
V Temperature range -40°C to 130°C –0.75 — 0.75
C Temperature range -40°C to 110°C –0.5 — 0.5

4b C Input leakage current (pins in high impedance input I — — — nA
in

mode) Vin = VDD35 or VSS35 —
-40°C ±1
27°C ±1
70°C ±8
85°C ±14
100°C ±26
105°C ±32
110°C 40
120°C ±60
125°C ±74
130°C ±92
150°C ±240

5 C Output high voltage (pins in output mode) V V
OH DD35 – 0.8 — — V

Partial drive IOH = –2 mA

6 P Output high voltage (pins in output mode) VOH VDD35 – 0.8 — — V
Full drive IOH = –10 mA

7 C Output low voltage (pins in output mode) VOL — — 0.8 V
Partial drive IOL = +2 mA

8 P Output low voltage (pins in output mode) V — — 0.8 V
OL

Full drive IOL = +10 mA

9 P Internal pull up resistance RPUL 25 — 50 KΩ
VIH min > input voltage > VIL max

10 P Internal pull down resistance RPDH 25 — 50 KΩ
VIH min > input voltage > VIL max

11 D Input capacitance Cin — 6 — pF

12 T Injection current(2) — mA
Single pin limit IICS –2.5 2.5
Total device Limit, sum of all injected currents IICP –25 25

13 P Port H, J, P interrupt input pulse filtered(STOP)(3) tPULSE — — 3 µs

14 P Port H, J, P interrupt input pulse passed(STOP)3 tPULSE 10 — — µs

15 D Port H, J, P interrupt input pulse filtered (STOP) tPULSE — — 3 tcyc

MC9S12XE-Family Reference Manual  Rev. 1.25

1212 Freescale Semiconductor



Appendix A Electrical Characteristics

Table A-8. 5V I/O Characteristics

Conditions are 4.5 V < VDD35 < 5.5 V temperature from –40°C to +150°C, unless otherwise noted
I/O Characteristics for all I/O pins except EXTAL, XTAL,TEST and supply pins.

16 D Port H, J, P interrupt input pulse passed (STOP) tPULSE 4 — — tcyc

17 D IRQ pulse width, edge-sensitive mode (STOP) PWIRQ 1 — — tcyc

18 D XIRQ pulse width with X-bit set (STOP) PWXIRQ 4 — — tosc
1. Maximum leakage current occurs at maximum operating temperature.
2. Refer to Section A.1.4, “Current Injection” for more details
3. Parameter only applies in stop or pseudo stop mode.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1213



Appendix A Electrical Characteristics

Table A-9.  Characteristics of Expantion Bus Inputs Port C, D, PE5, PE6, and PE7
for Reduced Input Voltage Thresholds

Conditions are 4.5 V < VDD35 < 5.5 V Temperature from –40°C to +150°C, unless otherwise noted

Num C Rating Symbol Min Typ Max Unit

1 D Input high voltage VIH 1.75 — — V

2 D Input low voltage VIL — — 0.75 V

3 T Input hysteresis VHYS — 100 — mV

A.1.10 Supply Currents
This section describes the current consumption characteristics of the device family as well as the
conditions for the measurements.

A.1.10.1 Typical Run Current Measurement Conditions
Since the current consumption of the output drivers is load dependent, all measurements are without output
loads and with minimum I/O activity. The currents are measured in single chip mode, S12XCPU code is
executed from Flash and XGATE code is executed from RAM. VDD35=5V, internal voltage regulator is
enabled and the bus frequency is 50MHz using a 4-MHz oscillator in loop controlled Pierce mode.

Furthermore in expanded modes the currents flowing in the system are highly dependent on the load at the
address, data, and control signals as well as on the duty cycle of those signals. No generally applicable
numbers can be given. A very good estimate is to take the single chip currents and add the currents due to
the external loads.

Since the DBG and BDM modules are typically not used in the end application, the supply current values
for these modules is not specified.

An overhead of current consumption exisits independent of the listed modules, due to voltage regulation
and clock logic that is not dedicated to a specific module. This is listed in the table row named “overhead”.

A.1.10.2 Maximum Run Current Measurement Conditions
Currents are measured in single chip mode, S12XCPU and XGATE code is executed from RAM with
VDD35=5.5V, internal voltage regulator enabled and a 50MHz bus frequency from a 4-MHz input.
Characterized parameters are derived using a 4MHz loop controlled Pierce oscillator. Production test
parameters are tested with a 4MHz square wave oscillator.

A.1.10.3 Current Conditions
Unbonded ports must be correctly initialized to prevent current consumption due to floating inputs. Typical
Stop current is measured with VDD35=5V, maximum Stop current is measured with VDD35=5.5V. Pseudo
Stop currents are measured with the oscillator configured for 4MHz LCP mode.

Table A-10. shows the configuration of the peripherals for typical run current.

MC9S12XE-Family Reference Manual  Rev. 1.25

1214 Freescale Semiconductor



Appendix A Electrical Characteristics

Table A-10. Module Configurations for Typical Run Supply Current VDD35=5V
Peripheral Configuration

S12XCPU 420 cycle loop: 384 DBNE cycles plus subroutine entry to stimulate stacking (RAM access)

XGATE XGATE fetches code from RAM, XGATE runs in an infinite loop, reading the Status and Flag
registers of CAN’s, SPI’s, SCI’s in sequence and doing some bit manipulation on the data

MSCAN Configured to loop-back mode using a bit rate of 500kbit/s

SPI Configured to master mode, continuously transmit data (0x55 or 0xAA) at 2Mbit/s

SCI Configured into loop mode, continuously transmit data (0x55) at speed of 19200 baud

IIC Operate in master mode and continuously transmit data (0x55 or 0xAA) at 100Kbit/s

PWM Configured to toggle its pins at the rate of 1kHz

ECT The peripheral shall be configured in output compare mode. Pulse accumulator and modulus
counter enabled.

ATD The peripheral is configured to operate at its maximum specified
frequency and to continuously convert voltages on all input channels in sequence.

PIT PIT is enabled, Micro-timer register 0 and 1 loaded with $0F and timer registers 0 to 3 are loaded
with $03/07/0F/1F.

RTI Enabled with RTI Control Register (RTICTL) set to $59

Overhead VREG supplying 1.8V from a 5V input voltage, core clock tree active, PLL on

Table A-11. Module Configurations for Maximum Run Supply Current VDD35=5.5V
Peripheral Configuration

S12XCPU 420 cycle loop: 384 DBNE cycles plus subroutine entry to stimulate stacking (RAM access)

XGATE XGATE fetches code from RAM, XGATE runs in an infinite loop, reading the Status and Flag
registers of CAN’s, SPI’s, SCI’s in sequence and doing some bit manipulation on the data

MSCAN Configured to loop-back mode using a bit rate of 1Mbit/s

SPI Configured to master mode, continuously transmit data (0x55 or 0xAA) at 4Mbit/s

SCI Configured into loop mode, continuously transmit data (0x55) at speed of 57600 baud

IIC Operate in master mode and continuously transmit data (0x55 or 0xAA) at 100Kbit/s

PWM Configured to toggle its pins at the rate of 40kHz

ECT The peripheral shall be configured in output compare mode. Pulse accumulator and modulus
counter enabled.

ATD The peripheral is configured to operate at its maximum specified
frequency and to continuously convert voltages on all input channels in sequence.

Overhead VREG supplying 1.8V from a 5V input voltage, PLL on

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1215



Appendix A Electrical Characteristics

Table A-12. Module Run Supply Currents

Conditions are shown in Table A-10 at ambient temperature unless otherwise noted

Num C Rating Min Typ Max Unit

1 T S12XCPU — 12.76 — mA
2 T XGATE — 24.20 —
3 T Each MSCAN — 1.05 —
4 T Each SPI — 0.22 —
5 T Each SCI — 0.28 —
6 T Each IIC — 0.40 —
7 T PWM — 0.55 —
8 T ECT — 1.16 —
9 T Each ATD — 0.82 —
10 T PIT — 0.61 —
11 T RTI — 0.17 —
12 T Overhead — 35.56 —

MC9S12XE-Family Reference Manual  Rev. 1.25

1216 Freescale Semiconductor



Appendix A Electrical Characteristics

Table A-13. Run and Wait Current Characteristics

Conditions are shown in Table A-4 unless otherwise noted

Num C Rating Symbol Min Typ Max Unit

Run supply current (No external load, Peripheral Configuration see Table A-11.)
1 P Peripheral Set1 IDD35 — — 100 mA

fosc=4MHz, fbus=50MHz
Run supply current (No external load, Peripheral Configuration see Table A-10.)

2 Peripheral Set(1) Devices S12XEP100, S12XEP768 IDD35 mA
C fosc=4MHz, fbus=50MHz — 84 —
T fosc=4MHz, fbus=20MHz — 43 —
T fosc=4MHz, fbus=8MHz — 24 —

2a Peripheral Set1 All other devices
T fosc=4MHz, fbus=50MHz — 72 — mA

3 Peripheral Set(2) mA
T fosc=4MHz, fbus=50MHz — 63 —
T fosc=4MHz, fbus=20MHz — 35 —
T fosc=4MHz, fbus=8MHz — 21 —

4 Peripheral Set(3) mA
T fosc=4MHz, fbus=50MHz — 62 —
T fosc=4MHz, fbus=20MHz — 34 —
T fosc=4MHz, fbus=8MHz — 21 —

5 Peripheral Set(4) mA
T fosc=4MHz, fbus=50MHz — 60 —
T fosc=4MHz, fbus=20MHz — 33 —
T fosc=4MHz, fbus=8MHz — 20 —

6 Peripheral Set(5) mA
T fosc=4MHz, fbus=50MHz — 59 —
T fosc=4MHz, fbus=20MHz — 33 —
T fosc=4MHz, fbus=8MHz — 20 —

7 Peripheral Set(6) mA
T fosc=4MHz, fbus=50MHz — 57 —
T fosc=4MHz, fbus=20MHz — 33 —
T fosc=4MHz, fbus=8MHz — 20 —

Wait supply current
8 C Peripheral Set1 ,PLL on IDDW — — 85 mA

XGATE executing code from RAM
9 Peripheral Set2

T fosc=4MHz, fbus=50MHz — 50 —
T fosc=4MHz, fbus=8MHz — 12 —

10 P All modules disabled, RTI enabled, PLL off — — 10
1. The following peripherals are on: ATD0/ATD1/ECT/IIC1/PWM/SPI0-SPI2/SCI0-SCI7/CAN0-CAN4/XGATE
2. The following peripherals are on: ATD0/ATD1/ECT/IIC1/PWM/SPI0-SPI2/SCI0-SCI7/CAN0-CAN4
3. The following peripherals are on: ATD0/ATD1/ECT/IIC1/PWM/SPI0-SPI2/SCI0-SCI7
4. The following peripherals are on: ATD0/ATD1/ECT/IIC1/PWM/SPI0-SPI2
5. The following peripherals are on: ATD0/ATD1/ECT/IIC1/PWM
6. The following peripherals are on: ATD0/ATD1/ECT/IIC1

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1217



Appendix A Electrical Characteristics

Table A-14. Pseudo Stop and Full Stop Current

Conditions are shown in Table A-4, junction temperature, unless otherwise noted

Num C Rating Symbol Min Typ Max Unit

Pseudo stop current (API, RTI, and COP disabled) PLL off, LCP mode
10 C –40°C IDDPS — 175 — µA

P 27°C — 185 255
C 70°C — 255 —
C 85°C — 305 —
C 105°C — 455 —
P 110°C — 505 2155
P 130°C — 805 3655
P 150°C — 1555 7655

Pseudo stop current (API, RTI, and COP enabled) PLL off, LCP mode
11 C 27°C IDDPS — 205 — µA

C 70°C — 275 —
C 85°C — 325 —
C 105°C — 475 —
C 125°C — 810 —
C 150°C — 1575 —

Stop Current
12 C –40°C IDDS — 20 — µA

P 27°C — 30 100
C 70°C — 100 —
C 85°C — 150 —
C 105°C — 300 —
P 110°C — 350 2000
C 125°C — 550 —
P 130°C — 650 3500
P 150°C — 1400 7500

Stop Current (API active)
13 T –40°C IDDS — 32 — µA

T 27°C — 42 —
T 85°C — 162 —
T 110°C — 362 —
T 130°C — 662 —

Stop Current (one ATD active)
14 T 27°C IDDS — 300 — µA

T 85°C — 420 —
T 125°C — 820 —

MC9S12XE-Family Reference Manual  Rev. 1.25

1218 Freescale Semiconductor



Appendix A Electrical Characteristics

A.2 ATD Characteristics
This section describes the characteristics of the analog-to-digital converter.

A.2.1 ATD Operating Characteristics
The Table A-15 and Table A-16 show conditions under which the ATD operates.

The following constraints exist to obtain full-scale, full range results:
VSSA ≤ VRL ≤ VIN ≤ VRH ≤ VDDA.

This constraint exists since the sample buffer amplifier can not drive beyond the power supply levels that
it ties to. If the input level goes outside of this range it will effectively be clipped.

Table A-15. ATD Operating Characteristics

Conditions are shown in Table A-4 unless otherwise noted, supply voltage 3.13V < VDDA < 5.5 V

Num C Rating Symbol Min Typ Max Unit

1 D Reference potential
Low VRL VSSA — VDDA/2 V
High VRH VDDA/2 — VDDA V

2 D Voltage difference VDDX to VDDA ∆VDDX –2.35 0 0.1 V

3 D Voltage difference VSSX to VSSA ∆VSSX –0.1 0 0.1 V

4 C Differential reference voltage VRH-VRL 3.13 5.0 5.5 V

5 C ATD Clock Frequency (derived from bus clock via the 0.25 — 8.3 MHz
prescaler) fATDCLk

P ATD Clock Frequency in Stop mode (internal generated 0.6 1 1.7 MHz
6

temperature and voltage dependent clock, ICLK)

7 D ADC conversion in stop, recovery time(1) tATDSTPRC — — 1.5 us
V

ATD Conversion Period(2)

12 bit resolution: NCONV12 20 — 42 ATD
8 D 10 bit resolution: NCONV10 19 — 41 clock

8 bit resolution: NCONV8 17 — 39 Cycles

1. When converting in Stop Mode (ICLKSTP=1) an ATD Stop Recovery time tATDSTPRCV is required to switch back to bus clock
based ATDCLK when leaving Stop Mode. Do not access ATD registers during this time.

2. The minimum time assumes a sample time of 4 ATD clock cycles. The maximum time assumes a sample time of 24 ATD clock
cycles and the discharge feature (SMP_DIS) enabled, which adds 2 ATD clock cycles.

A.2.2 Factors Influencing Accuracy
Source resistance, source capacitance and current injection have an influence on the accuracy of the ATD.
A further factor is that PortAD pins that are configured as output drivers switching.

A.2.2.1 Differential Reference Voltage
The accuracy is reduced if the differential reference voltage is less than 3.13V when using the ATD in the
3.3V range or if the differential reference voltage is less than 4.5V when using the ATD in the 5V range.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1219



Appendix A Electrical Characteristics

A.2.2.2 Port AD Output Drivers Switching
PortAD output drivers switching can adversely affect the ATD accuracy whilst converting the analog
voltage on other PortAD pins because the output drivers are supplied from the VDDA/VSSA ATD supply
pins. Although internal design measures are implemented to minimize the affect of output driver noise, it
is recommended to configure PortAD pins as outputs only for low frequency, low load outputs. The impact
on ATD accuracy is load dependent and not specified. The values specified are valid under condition that
no PortAD output drivers switch during conversion.

A.2.2.3 Source Resistance
Due to the input pin leakage current as specified in Table A-8 in conjunction with the source resistance
there will be a voltage drop from the signal source to the ATD input. The maximum source resistance RS
specifies results in an error (10-bit resolution) of less than 1/2 LSB (2.5 mV) at the maximum leakage
current. If device or operating conditions are less than worst case or leakage-induced error is acceptable,
larger values of source resistance of up to 10Kohm are allowed.

A.2.2.4 Source Capacitance
When sampling an additional internal capacitor is switched to the input. This can cause a voltage drop due
to charge sharing with the external and the pin capacitance. For a maximum sampling error of the input
voltage ≤ 1LSB (10-bit resolution), then the external filter capacitor, Cf ≥ 1024 * (CINS–CINN).

A.2.2.5 Current Injection
There are two cases to consider.

1. A current is injected into the channel being converted. The channel being stressed has conversion
values of $3FF (in 10-bit mode) for analog inputs greater than VRH and $000 for values less than
VRL unless the current is higher than specified as disruptive condition.

2. Current is injected into pins in the neighborhood of the channel being converted. A portion of this
current is picked up by the channel (coupling ratio K), This additional current impacts the accuracy
of the conversion depending on the source resistance.

MC9S12XE-Family Reference Manual  Rev. 1.25

1220 Freescale Semiconductor



Appendix A Electrical Characteristics

The additional input voltage error on the converted channel can be calculated as:
VERR = K * RS * IINJ

with IINJ being the sum of the currents injected into the two pins adjacent to the converted channel.

Table A-16. ATD Electrical Characteristics

Conditions are shown in Table A-4 unless otherwise noted

Num C Rating Symbol Min Typ Max Unit

1 C Max input source resistance(1) RS — — 1 KΩ
2 D Total input capacitance Non sampling CINN — — 10 pF

Total input capacitance Sampling CINS — — 16

3 D Input internal Resistance RINA — 5 15 kΩ

4 C Disruptive analog input current INA –2.5 — 2.5 mA

5 C Coupling ratio positive current injection Kp — — 1E-4 A/A

6 C Coupling ratio negative current injection Kn — — 2E-3 A/A
1. Refer to A.2.2.3 for further information concerning source resistance

A.2.3 ATD Accuracy
Table A-17 and Table A-18 specify the ATD conversion performance excluding any errors due to current
injection, input capacitance and source resistance.

A.2.3.1 ATD Accuracy Definitions
For the following definitions see also Figure A-1.
Differential non-linearity (DNL) is defined as the difference between two adjacent switching steps.

V V
DNL( i) i –

= i – 1
------------------------- – 1

1LSB

The integral non-linearity (INL) is defined as the sum of all DNLs:

n
V – V

INL(n) = ∑ DNL( i) = n 0
-------------------- – n
1LSB

i = 1

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1221



Appendix A Electrical Characteristics

DNL

LSB 10-Bit Absolute Error Boundary
Vi-1 Vi

$3FF 8-Bit Absolute Error Boundary
$3FE

$3FD

$3FC $FF

$3FB

$3FA

$3F9

$3F8 $FE

$3F7

$3F6

$3F5

$3F4 $FD

$3F3

9
Ideal Transfer Curve

8 2

7
10-Bit Transfer Curve

6

5

4 1

3
8-Bit Transfer Curve

2

1

0
5 10 15 20 25 30 35 40 50 50555060506550705075508050855090509551005105511051155120 Vin

mV

Figure A-1. ATD Accuracy Definitions

NOTE

Figure A-1 shows only definitions, for specification values refer to Table A-17 and Table A-18

MC9S12XE-Family Reference Manual  Rev. 1.25

1222 Freescale Semiconductor

10-Bit Resolution

8-Bit Resolution



Appendix A Electrical Characteristics

Table A-17. ATD Conversion Performance 5V range

Conditions are shown in Table A-4. unless otherwise noted. VREF = VRH - VRL = 5.12V. fATDCLK = 8.3MHz
The values are tested to be valid with no PortAD output drivers switching simultaneous with conversions.

Num C Rating(1) ,(2) Symbol Min Typ Max Unit

1 P Resolution 12-Bit LSB — 1.25 — mV

2 P Differential Nonlinearity 12-Bit DNL -4 ±2 4 counts

3 P Integral Nonlinearity 12-Bit INL -5 ±2.5 5 counts

4 P Absolute Error(3) 12-Bit AE -7 ±4 7 counts

5 C Resolution 10-Bit LSB — 5 — mV

6 C Differential Nonlinearity 10-Bit DNL -1 ±0.5 1 counts

7 C Integral Nonlinearity 10-Bit INL -2 ±1 2 counts

8 C Absolute Error3. 10-Bit AE -3 ±2 3 counts

9 C Resolution 8-Bit LSB — 20 — mV

10 C Differential Nonlinearity 8-Bit DNL -0.5 ±0.3 0.5 counts

11 C Integral Nonlinearity 8-Bit INL -1 ±0.5 1 counts

12 C Absolute Error3. 8-Bit AE -1.5 ±1 1.5 counts
1. The 8-bit and 10-bit mode operation is structurally tested in production test. Absolute values are tested in 12-bit mode.
2. Better performance is possible using specially designed multi-layer PCBs or averaging techniques.
3. These values include the quantization error which is inherently 1/2 count for any A/D converter.

Table A-18. ATD Conversion Performance 3.3V range

Conditions are shown in Table A-4. unless otherwise noted. VREF = VRH - VRL = 3.3V. fATDCLK = 8.3MHz
The values are tested to be valid with no PortAD output drivers switching simultaneous with conversions.

Num C Rating(1),(2) Symbol Min Typ Max Unit

1 P Resolution 12-Bit LSB — 0.80 — mV

2 P Differential Nonlinearity 12-Bit DNL -6 ±3 6 counts

3 P Integral Nonlinearity 12-Bit INL -7 ±3 7 counts

4 P Absolute Error(3) 12-Bit AE -8 ±4 8 counts

5 C Resolution 10-Bit LSB — 3.22 — mV

6 C Differential Nonlinearity 10-Bit DNL -1.5 ±1 1.5 counts

7 C Integral Nonlinearity 10-Bit INL -2 ±1 2 counts

8 C Absolute Error3. 10-Bit AE -3 ±2 3 counts

9 C Resolution 8-Bit LSB — 12.89 — mV

10 C Differential Nonlinearity 8-Bit DNL -0.5 ±0.3 0.5 counts

11 C Integral Nonlinearity 8-Bit INL -1 ±0.5 1 counts

12 C Absolute Error3. 8-Bit AE -1.5 ±1 1.5 counts
1. The 8-bit and 10-bit mode operation is structurally tested in production test. Absolute values are tested in 12-bit mode.
2. Better performance is possible using specially designed multi-layer PCBs or averaging techniques.
3. These values include the quantization error which is inherently 1/2 count for any A/D converter.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1223



Appendix A Electrical Characteristics

A.3 NVM, Flash and Emulated EEPROM

A.3.1 Timing Parameters
The time base for all NVM program or erase operations is derived from the oscillator. A minimum
oscillator frequency fNVMOSC is required for performing program or erase operations. The NVM modules
do not have any means to monitor the frequency and will not prevent program or erase operation at
frequencies above or below the specified minimum. When attempting to program or erase the NVM
modules at a lower frequency, a full program or erase transition is not assured.

The program and erase operations are timed using a clock derived from the oscillator using the FCLKDIV
register. The frequency of this clock must be set within the limits specified as fNVMOP.

The minimum program and erase times shown in Table A-19 are calculated for maximum fNVMOP and
maximum fNVMBUS unless otherwise shown. The maximum times are calculated for minimum fNVMOP

A.3.1.1 Erase Verify All Blocks (Blank Check) (FCMD=0x01)
The time it takes to perform a blank check is dependant on the location of the first non-blank word starting
at relative address zero. It takes one bus cycle per phrase to verify plus a setup of the command. Assuming
that no non blank location is found, then the erase verify all blocks is given by.

1
tcheck = 33500 ⋅ ---------------------

fNVMBUS

A.3.1.2 Erase Verify Block (Blank Check) (FCMD=0x02)
The time it takes to perform a blank check is dependant on the location of the first non-blank word starting
at relative address zero. It takes one bus cycle per phrase to verify plus a setup of the command. Assuming
that no non blank location is found, then the erase verify time for a single 256K NVM array is given by

1
tcheck = 33500 ⋅ ---------------------

fNVMBUS

For a 128K NVM or D-Flash array the erase verify time is given by
1

tcheck = 17200 ⋅ ---------------------
fNVMBUS

A.3.1.3 Erase Verify P-Flash Section (FCMD=0x03)
The maximum time depends on the number of phrases being verified (NVP)

tcheck ( 1
= 752 + NVP) ⋅ ---------------------

fNVMBUS

MC9S12XE-Family Reference Manual  Rev. 1.25

1224 Freescale Semiconductor



Appendix A Electrical Characteristics

A.3.1.4 Read Once (FCMD=0x04)
The maximum read once time is given by

t = ( 1
400) ⋅ ---------------------

fNVMBUS

A.3.1.5 Load Data Field (FCMD=0x05)
The maximum load data field time is given by

1
t = (450) ⋅ ---------------------

fNVMBUS

A.3.1.6 Program P-Flash (FCMD=0x06)
The programming time for a single phrase of four P-Flash words + associated eight ECC bits is dependant
on the bus frequency as a well as on the frequency fNVMOP and can be calculated according to the
following formulas, whereby NDLOAD is the number of extra blocks being programmed by the Load Data
Field command (DLOAD), i.e. programming 2,3,4 blocks using DLOAD, NDLOAD =1,2,3 respectively.

The typical phrase programming time can be calculated using the following equation

tbwpgm = (128 + (12 ⋅ N 1
DLOAD)) ⋅ + ( + ( ⋅ )) ⋅ 1

------------------------ 1725 <USER> <GROUP> ----------------------------
fNVMOP fNVMBUS

The maximum phrase programming time can be calculated using the following equation

tbwpgm = (130 + (14 ⋅ N 1
DLOAD)) ⋅ -- + ( + ( ⋅ )) ⋅ 1

---------------------- 2125 <USER> <GROUP> ----------------------------
fNVMOP fNVMBUS

A.3.1.7 P-Flash Program Once (FCMD=0x07)
The maximum P-Flash Program Once time is given by

tbwpgm ≈ 1
162 ⋅ 1

------------------------ + 2400 ⋅ ----------------------------
fNVMOP fNVMBUS

A.3.1.8 Erase All Blocks (FCMD=0x08)
For S12XEP100, S12XEP768, S12XEQ512 and S12XEQ384 erasing all blocks takes:

1
tmass ≈ 100100 ⋅ 1

------------------------ + 70000 ⋅ ----------------------------
fNVMOP fNVMBUS

For S12XET256, S12XEA256 and S12XEG128 erasing all blocks takes:

t 1
mass ≈ 100100 ⋅ 1

------------------------ + 35000 ⋅ ----------------------------
fNVMOP fNVMBUS

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1225



Appendix A Electrical Characteristics

A.3.1.9 Erase P-Flash Block (FCMD=0x09)
Erasing a 256K NVM block takes

tmass ≈ ⋅ 1
100100 1

------------------------ + 70000 ⋅ ----------------------------
fNVMOP fNVMBUS

Erasing a 128K NVM block takes

tmass ≈ 100100 ⋅ 1 1
------------------------ + 35000 ⋅ ----------------------------
fNVMOP fNVMBUS

A.3.1.10 Erase P-Flash Sector (FCMD=0x0A)
The typical time to erase a1024-byte P-Flash sector can be calculated using

⎛ 1 1
tera = ⎝20020 ⋅ ------------------⎞ + ⎛

f ⎠ ⎝700 ⋅ ---------------------⎞
f ⎠

NVMOP NVMBUS

The maximum time to erase a1024-byte P-Flash sector can be calculated using

t ⎛ 1 1
era = ⎝20020 ⋅ ------------------⎞ + ⎛

f ⎠ ⎝1100 ⋅ ---------------------⎞
NVMOP f ⎠

NVMBUS

A.3.1.11 Unsecure Flash (FCMD=0x0B)
The maximum time for unsecuring the flash is given by

tuns 100100 1 1
= ⎛

⎝ ⋅ ------------------------ + 70000 ⋅ ----------------------------⎞
fNVMOP f ⎠

NVMBUS

A.3.1.12 Verify Backdoor Access Key (FCMD=0x0C)
The maximum verify backdoor access key time is given by

1
t= 400 ⋅ ----------------------------

fNVMBUS

A.3.1.13 Set User Margin Level (FCMD=0x0D)
The maximum set user margin level time is given by

1
t= 350 ⋅ ----------------------------

fNVMBUS

A.3.1.14 Set Field Margin Level (FCMD=0x0E)
The maximum set field margin level time is given by

MC9S12XE-Family Reference Manual  Rev. 1.25

1226 Freescale Semiconductor



Appendix A Electrical Characteristics

1
t= 350 ⋅ ----------------------------

fNVMBUS

A.3.1.15 Full Partition D-Flash (FCMD=0x0F)
The maximum time for partitioning the D-flash (ERPART=16, DFPART=0) is given by :

tpart ≈ 21800 ⋅ 1
------------------------ + 400000 ⋅ 1

---------------------------- + t
f ass
NVMOP f m

NVMBUS

A.3.1.16 Erase Verify D-Flash Section (FCMD=0x10)
Erase Verify D-Flash for a given number of words NW is given by .

≈ ⋅ 1
tcheck (840 + NW) ----------------------------

fNVMBUS

A.3.1.17 D-Flash Programming (FCMD=0x11)
D-Flash programming time is dependent on the number of words being programmed and their location
with respect to a row boundary, because programming across a row boundary requires extra steps. The D-
Flash programming time is specified for different cases (1,2,3,4 words and 4 words across a row boundary)
at a 50MHz bus frequency. The typical programming time can be calculated using the following equation,
whereby Nw denotes the number of words; BC=0 if no boundary is crossed and BC=1 if a boundary is
crossed.

t = ⎛
dpgm (15 + (54 ⋅ Nw) + (16 ⋅ BC)) 1

------------------⎞
⎝ ⋅ + ⎛( 1

+ + ) -------------------- ⎞
f ⎠ ⎝ 460 (640 ⋅ NW) (500 ⋅ BC) ⋅ -
NVMOP f ⎠

NVMBUS

The maximum programming time can be calculated using the following equation

t = ⎛
dpgm (15 + (56 ⋅ Nw) + (16 ⋅ BC)) 1

---------------- ⎞
⎝ ⋅ -- + ⎛( 60 + (840 ⋅ N ⋅ -------------------⎞

f ⎠ ⎝ 4 W) + (500 ⋅ BC)) 1
-- ⎠

NVMOP fNVMBUS

A.3.1.18 Erase D-Flash Sector (FCMD=0x12)
Typical D-Flash sector erase times are those expected on a new device, where no margin verify fails occur.
They can be calculated using the following equation.

teradf ≈ 5025 ⋅ 1 1
------------------------ + 700 ⋅ ----------------------------
fNVMOP fNVMBUS

Maximum D-Fash sector erase times can be calculated using the following equation.

teradf ≈ 20100 ⋅ 1 1
------------------------ + 3300 ⋅ ----------------------------
fNVMOP fNVMBUS

The D-Flash sector erase time on a new device is ~5ms and can extend to 20ms as the flash is cycled.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1227



Appendix A Electrical Characteristics

A.3.1.19 Enable EEE (FCMD=0x13)
The maximum time to enable EPROM emulation is given by

t = ⎛⎛
⎝((1100 ⋅ BWN + (176 ⋅ (1 + BWN) + (BWN + NSEC) ⋅ 32364))) ⋅ 1

------------------⎞
⎝ f ⎠ +

NVMOP
⎛
⎝(3050 ⋅ (1 + BWN) + (NSEC + BWN) ⋅ 1

290500) ⋅ ---------------------⎞⎞
f ⎠⎠
NVMBUS

where NSEC is the number of sectors of constant data. A constant sector is one in which all 63 records
contain the latest active data and would need to be copied. The maximum possible is 33 (2048 EEE RAM
words /63 =32.5) although this is a highly unlikely scenario. The impact of a worst case brownout recovery
scenario is denoted by BWN = 2 for non brownout situations BWN =0.

MC9S12XE-Family Reference Manual  Rev. 1.25

1228 Freescale Semiconductor



Appendix A Electrical Characteristics

A.3.1.20 Maximum CCOB Latency
The maximum time a CCOB command has to wait to be actioned due to an EEE clean up is given where
BWN = 1 if a brownout has occured otherwise BWN = 0. BWN = 1 only for the first ENEEE after reset.

≈ ⎛ 1
t ⎝32364 ⋅ 1

------------------------ + 292600 ⋅ ----------------------------⎞⎠ ⋅ (1 + BWN)
fNVMOP fNVMBUS

+ BWN ⋅ ⎛
⎝350 ⋅ 1 1100

--------------------- + ------------------------⎞
fNVMOP fNVMBUS⎠

A.3.1.21 Disable EEE (FCMD=0x14)
Maximum time to disable EPROM emulation is given by

1
t= 300 ⋅ ----------------------------

fNVMBUS

A.3.1.22 EEE Query (FCMD=0x15)
Maximum time for the EEE query command is given by

1
t= 300 ⋅ ----------------------------

fNVMBUS

A.3.1.23 Partition D-Flash (FCMD=0x20)
The maximum time for partitioning the D-flash (ERPART=16, DFPART=0) is given by

t ≈ 21800 ⋅ 1 1
------------------------ + 400000 ⋅ ----------------------------
fNVMOP fNVMBUS

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1229



Appendix A Electrical Characteristics

A.3.1.24 EEE Copy Down
The typical EEE copy down time is given by the following equation

tdfcd = (14000 + (316 ⋅ ERPART) + ( ⋅ ( ))) 1
1500 124 – DFPART × ---------------------

fNVMBUS

The maximum EEE copy down time is given by the following equation

tdfcd = (34000 + (316 ⋅ ERPART) + ( ⋅ ( 1
1500 124 – DFPART))) × ---------------------

fNVMBUS

Worst case for Enable EEPROM Emulation allows for all the EEE records to have to be copied which is a
very low probability scenario only likely in the case that the EEE is mostly full of unchanging data (the
records for which are stored in consecutive D-Flash sectors).

Table A-19. NVM Timing Characteristics

Conditions are as shown in Table A-4, with fNVMBUS = 50MHz and fNVMOP= 1MHz unless otherwise noted.

Num C Rating Symbol Min Typ Max Unit

1 D External oscillator clock fNVMOSC 2 — 50(1) MHz

2 D Bus frequency for programming or erase operations fNVMBUS 1 — 50 MHz

3 D Operating frequency fNVMOP 800 — 1050 kHz

4 D P-Flash phrase programming tbwpgm — 162 173 µs

5a D P- Flash phrase program time using D-LOAD on 4 blocks tbwpgm4 — 231 264 µs

5b D P-Flash phrase program time using D-LOAD on 3 blocks tbwpgm3 — 208 233 µs

5c D P-Flash phrase program time using D-LOAD on 2 blocks tbwpgm2 — 185 202 µs

6 P P-Flash sector erase time tera — 20 21 ms

7 P Erase All Blocks (Mass erase) time tmass — 101 102 ms

7a D Unsecure Flash tuns — 101 102 ms

8 D P-Flash erase verify (blank check) time(2) tcheck — — 335002 tcyc

9a D D-Flash word programming one word tdpgm — 88 95 µs

9b D D-Flash word programming two words tdpgm — 153 165 µs

9c D D-Flash word programming three words tdpgm — 212 230 µs

9d D D-Flash word programming four words tdpgm — 282 316 µs

9e D D-Flash word programming four words crossing row tdpgm — 298 342 µs
boundary

10 D D-Flash sector erase time teradf — 5.2(3) 21 ms

11 D D-Flash erase verify (blank check) time tcheck — — 17500 tcyc

12 D EEE copy down (mask sets 5M48H, 3M25J, 2M53J, tdfrcd — 255000 275000(4) tcyc
1M12S, 1N35H, 1N36H)

12 D EEE copy down (other mask sets) tdfrcd — 205000 225000(5) tcyc
1. Restrictions for oscillator in crystal mode apply.
2. Valid for both “Erase verify all” or “Erase verify block” on 256K block without failing locations
3. This is a typical value for a new device
4. Maximum partitioning

MC9S12XE-Family Reference Manual  Rev. 1.25

1230 Freescale Semiconductor



Appendix A Electrical Characteristics

5. Maximum partitioning

A.3.2 NVM Reliability Parameters
The reliability of the NVM blocks is guaranteed by stress test during qualification, constant process
monitors and burn-in to screen early life failures.

The data retention and program/erase cycling failure rates are specified at the operating conditions noted.
The program/erase cycle count on the sector is incremented every time a sector or mass erase event is
executed.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1231



Appendix A Electrical Characteristics

The standard shipping condition for both the D-Flash and P-Flash memory is erased with security disabled.
However it is recommended that each block or sector is erased before factory programming to ensure that
the full data retention capability is achieved. Data retention time is measured from the last erase operation.

Table A-20.  NVM Reliability Characteristics

Conditions are shown in Table A-4 unless otherwise noted

Num C Rating Symbol Min Typ Max Unit

P-Flash Arrays

1 C Data retention at an average junction temperature of TJavg = tPNVMRET 15 100(2) — Years
85°C(1) after up to 10,000 program/erase cycles

2 C Data retention at an average junction temperature of TJavg = tPNVMRET 20 1002 — Years
85°C(3) after less than 100 program/erase cycles

3 C P-Flash number of program/erase cycles nPFLPE 10K 100K3 — Cycles
(-40°C ≤ tj ≤ 150°C)

D-Flash Array

4 C Data retention at an average junction temperature of TJavg = tDNVMRET 5 1002 — Years
85°C3 after up to 50,000 program/erase cycles

5 C Data retention at an average junction temperature of TJavg = tDNVMRET 10 1002 — Years
85°C3 after less than 10,000 program/erase cycles

6 C Data retention at an average junction temperature of TJavg = tDNVMRET 20 1002 — Years
85°C3 after less than 100 program/erase cycles

7 C D-Flash number of program/erase cycles (-40°C ≤ tj ≤ 150°C) nDFLPE 50K 500K3 — Cycles

Emulated EEPROM

8 C Data retention at an average junction temperature of TJavg = tEENVMRET 54 1002 — Years
85°C1after spec. program/erase cycles

9 C Data retention at an average junction temperature of TJavg = tEENVMRET 10 1002 — Years
85°C3 after less than 20% spec.program/erase cycles.
(e.g. after <20,000 cycles / Spec 100,000 cycles)

10 C Data retention at an average junction temperature of TJavg = tEENVMRET 20 1002 — Years
85°C3 after less than 0.2% spec. program/erase cycles
(e.g. after < 200 cycles / Spec 100,000 cycles)

11 C EEPROM number of program/erase cycles with a ratio of nEEPE 100K(4) 1M(5) — Cycles
EEE_NVM to EEE_RAM = 8 (-40°C ≤ tj ≤ 150°C)

12 C EEPROM number of program/erase cycles with a ratio of nEEPE 3M4 30M5 — Cycles
EEE_NVM to EEE_RAM = 128 (-40°C ≤ tj ≤ 150°C)

13 C EEPROM number of program/erase cycles with a ratio of nEEPE 325M4 3.2G5 — Cycles
EEE_NVM to EEE_RAM = 16384(6) (-40°C ≤ tj ≤ 150°C)

1. TJavg does not exceed 85°C in a typical temperature profile over the lifetime of a consumer, industrial or automotive
application.

2. Typical data retention values are based on intrinsic capability of the technology measured at high temperature and de-rated
to 25°C using the Arrhenius equation. For additional information on how Freescale defines Typical Data Retention, please
refer to Engineering Bulletin EB618

3. TJavg does not exceed 85°C in a typical temperature profile over the lifetime of a consumer, industrial or automotive
application.

4. This represents the number of writes of updated data words to the EEE_RAM partition. Minimum specification (endurance
and data retention) of the Emulated EEPROM array is based on the minimum specification of the D-Flash array per item 6.

MC9S12XE-Family Reference Manual  Rev. 1.25

1232 Freescale Semiconductor



Appendix A Electrical Characteristics

5. This represents the number of writes of updated data words to the EEE_RAM partition. Typical endurance performance for
the Emulated EEPROM array is based on typical endurance performance and the EEE algorithm implemented on this
product family. Spec. table quotes typical endurance evaluated at 25°C for this product family.

6. This is equivalent to using a single byte or aligned word in the EEE_RAM with 32K D-Flash allocated for EEEPROM

The number of program/erase cycles for the EEPROM/D-Flash depends upon the partitioning of D-Flash
used for EEPROM Emulation. Defining RAM size allocated for EEE as EEE-RAM and D-Flash partition
allocated to EEE as EEE_NVM, the minimum number of program/erase cycles is specified depending
upon the ratio of EEE_NVM/EEE_RAM. The minimum ratio EEE_NVM/EEE_RAM =8.

Figure A-2. Program/Erase Dependency on D-Flash Partitioning

# K Cycles
(Log)

1,000,000 Spec Cycles
5 Year Data Retention

100,000

10,000 20% Spec Cycles
10 Year Data Retention

1,000

100

10
EEE_NVM/EEE_RAM ratio

10 100 1000 10,000 100,000 (Log)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1233



Appendix A Electrical Characteristics

A.4 Voltage Regulator
Device functionality is guaranteed on power down to the LVR assert level.

Table A-21. Voltage Regulator Electrical Characteristics

Conditions are shown in Table A-4 unless otherwise noted

Num C Characteristic Symbol Min Typical Max Unit

1 P Input Voltages VVDDR,A 3.13 — 5.5 V

Output Voltage Core
Full Performance Mode V 1.72 1.84 1.98 V

2 P
Reduced Power Mode (MCU STOP mode) DD — 1.6 — V
Shutdown Mode — —(1) — V

Output Voltage Flash
 Full Performance Mode V 2.6 2.82 2.9 V

3 P
Reduced Power Mode (MCU STOP mode) DDF — 2.2 — V
Shutdown Mode — —1 — V

Output Voltage PLL
Full Performance Mode 1.84 1.98 V

4 P V 1.72
Reduced Power Mode (MCU STOP mode) DDPLL

— 1.6 — V
Shutdown Mode — —1 — V

4 4.23 4.40 V
5 P Low Voltage Interrupt Asser Level (2) VLVIA 4.0

Low Voltage InterruptDeassert Level VLVID 4.19 4.38 4.49 V

6a P VDDX Low Voltage Reset Deassert (3) VLVRXD — — 3.13 V

6b D VDDX Low Voltage Reset assert 3 VLVRXA — 3.02 — V

6c P VDDX Low Voltage Reset assert 3 VLVRXA 2.97 — — V

7 C Trimmed API internal clock(4) ∆f / fnominal dfAPI - 5% — + 5% —

The first period after enabling the counter by APIFE
8 D t 00 us

might be reduced by API start up delay sdel — — 1

9 T Temperature Sensor Slope dVTS 5.05 5.25 5.45 mV/oC

High Temperature Interrupt Assert THTIA 120 132 144 o
10 T (VREGHTTR=$88)(5)

T
High Temperature Interrupt Deassert HTID 110 122 134 C

(VREGHTTR=$88)

11 T Bandgap Reference Voltage VBG 1.13 1.21 1.32 V
1. Voltage Regulator Disabled. High Impedance Output
2. Monitors VDDA, active only in Full Performance Mode. Indicates I/O & ADC performance degradation due to low supply

voltage.
3. Monitors VDDX, active only in Full Performance Mode. MCU is monitored by the POR in RPM (see Figure A-3)
4. The API Trimming bits must be set that the minimum period equals to 0.2 ms.
5. A hysteresis is guaranteed by design

NOTE
The LVR monitors the voltages VDDF and VDDX. If the voltage drops on
these supplies to a level which could prohibit the correct function of the
microcontroller, the LVR triggers.

MC9S12XE-Family Reference Manual  Rev. 1.25

1234 Freescale Semiconductor



Appendix A Electrical Characteristics

A.5 Output Loads

A.5.1 Resistive Loads
The voltage regulator is intended to supply the internal logic and oscillator. It allows no external DC loads.

A.5.2 Capacitive Loads
The capacitive loads are specified in Table A-22. Ceramic capacitors with X7R dielectricum are required.

Table A-22.  - Required Capacitive Loads

Num Characteristic Symbol Min Recommended Max Unit

1 VDD/VDDF external capacitive load CDDext 176 220 264 nF

3 VDDPLL external capacitive load CDDPLLext 80 220 264 nF

A.5.3 Chip Power-up and Voltage Drops
LVI (low voltage interrupt), POR (power-on reset) and LVRs (low voltage reset) handle chip power-up or
drops of the supply voltage. Their function is shown in Figure A-3 .

Figure A-3. MC9S12XE-Family - Chip Power-up and Voltage Drops (not scaled)
V VDDX

VLVID
VLVIA

VLVRXD VDD
VLVRXA

VPORD

t
LVI

LVI enabled LVI disabled due to LVR
POR

LVRX

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1235



Appendix A Electrical Characteristics

Figure A-4. MC9S12XE-Family Power Sequencing

V
VDDR,
VDDX

VDDA

>= 0 t

During power sequencing VDDA can be powered up before VDDR, VDDX.
VDDR and VDDX must be powered up together adhering to the operating conditions differential.
VRH power up must follow VDDA to avoid current injection.

A.6 Reset, Oscillator and PLL
This section summarizes the electrical characteristics of the various startup scenarios for oscillator and
phase-locked loop (PLL).

A.6.1 Startup
Table A-23 summarizes several startup characteristics explained in this section. Detailed description of the
startup behavior can be found in the Clock and Reset Generator (CRG) block description

Table A-23. Startup Characteristics

Conditions are shown in Table A-4unless otherwise noted

Num C Rating Symbol Min Typ Max Unit

1 D Reset input pulse width, minimum input time PWRSTL 2 — — tosc

2 D Startup from reset tRST 192 — 4000(1) nbus

3 D Wait recovery startup time tWRS — — 14 tcyc

4 D Fast wakeup from STOP(2) tfws — 50 100 µs
1. This is the time between RESET deassertion and start of CPU code execution.
2. Including voltage regulator startup; VDD /VDDF filter capacitors 220 nF, VDD35 = 5 V, T= 25°C

A.6.1.1 POR
The release level VPORR and the assert level VPORA are derived from the VDD supply. They are also valid
if the device is powered externally. After releasing the POR reset the oscillator and the clock quality check
are started. If after a time tCQOUT no valid oscillation is detected, the MCU will start using the internal self
clock. The fastest startup time possible is given by nuposc.

MC9S12XE-Family Reference Manual  Rev. 1.25

1236 Freescale Semiconductor



Appendix A Electrical Characteristics

A.6.1.2 SRAM Data Retention
Provided an appropriate external reset signal is applied to the MCU, preventing the CPU from executing
code when VDD35 is out of specification limits, the SRAM contents integrity is guaranteed if after the reset
the PORF bit in the CRG flags register has not been set.

A.6.1.3 External Reset
When external reset is asserted for a time greater than PWRSTL the CRG module generates an internal
reset, and the CPU starts fetching the reset vector without doing a clock quality check, if there was an
oscillation before reset.

A.6.1.4 Stop Recovery
Out of stop the controller can be woken up by an external interrupt. A clock quality check as after POR is
performed before releasing the clocks to the system.

If the MCU is woken-up by an interrupt and the fast wake-up feature is enabled (FSTWKP = 1 and
SCME = 1), the system will resume operation in self-clock mode after tfws.

A.6.1.5 Pseudo Stop and Wait Recovery
The recovery from pseudo stop and wait is essentially the same since the oscillator is not stopped in both
modes. The controller can be woken up by internal or external interrupts. After twrs the CPU starts fetching
the interrupt vector.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1237



Appendix A Electrical Characteristics

A.6.2 Oscillator
Table A-24. Oscillator Characteristics

Conditions are shown in Table A-4. unless otherwise noted

Num C Rating Symbol Min Typ Max Unit

1a C Crystal oscillator range (loop controlled Pierce) fOSC 4.0 — 16 MHz

1b C Crystal oscillator range (full swing Pierce) (1),(2) fOSC 2.0 — 40 MHz

2 P Startup Current iOSC 100 — — µA

3a C Oscillator start-up time (LCP, 4MHz)(3) tUPOSC — 2 10 ms

3b C Oscillator start-up time (LCP, 8MHz)3 tUPOSC — 1.6 8 ms

3c C Oscillator start-up time (LCP, 16MHz)3 tUPOSC — 1 5 ms

4a C Oscillator start-up time (full swing Pierce, 2MHz)3 tUPOSC — 8 40 ms

4b C Oscillator start-up time (full swing Pierce, 4MHz)3 tUPOSC — 4 20 ms

4c C Oscillator start-up time (full swing Pierce, 8MHz)3 tUPOSC — 2 10 ms

4d C Oscillator start-up time (full swing Pierce, 16MHz)3 tUPOSC — 1 5 ms

4e C Oscillator start-up time (full swing Pierce, 40MHz)3 tUPOSC — 0.8 4 ms

5 D Clock Quality check time-out tCQOUT 0.45 — 2.5 s

6 P Clock Monitor Failure Assert Frequency fCMFA 200 400 1000 KHz

7 P External square wave input frequency fEXT 2.0 — 50 MHz

8 D External square wave pulse width low tEXTL 9.5 — — ns

9 D External square wave pulse width high tEXTH 9.5 — — ns

10 D External square wave rise time tEXTR — — 1 ns

11 D External square wave fall time tEXTF — — 1 ns

12 D Input Capacitance (EXTAL, XTAL pins) CIN — 7 — pF

13 P EXTAL Pin Input High Voltage VIH,EXTAL 0.75*VDDPLL — — V

T EXTAL Pin Input High Voltage,(4) VIH,EXTAL — — VDDPLL + 0.3 V

14 P EXTAL Pin Input Low Voltage VIL,EXTAL — — 0.25*VDDPLL V

— —
T EXTAL Pin Input Low Voltage,4

VIL,EXTAL VSSPLL - 0.3 V

15 C EXTAL Pin Input Hysteresis VHYS,EXTAL — 180 — mV

EXTAL Pin oscillation amplitude (loop controlled
16 C V — —

Pierce) PP,EXTAL 0.9 V

1. Depending on the crystal a damping series resistor might be necessary
2. Only valid if full swing Pierce oscillator/external clock mode is selected
3. These values apply for carefully designed PCB layouts with capacitors that match the crystal/resonator requirements..
4. Only applies if EXTAL is externally driven

MC9S12XE-Family Reference Manual  Rev. 1.25

1238 Freescale Semiconductor



Appendix A Electrical Characteristics

A.6.3 Phase Locked Loop

A.6.3.1 Jitter Information
With each transition of the clock fcmp, the deviation from the reference clock fref is measured and input
voltage to the VCO is adjusted accordingly.The adjustment is done continuously with no abrupt changes
in the clock output frequency. Noise, voltage, temperature and other factors cause slight variations in the
control loop resulting in a clock jitter. This jitter affects the real minimum and maximum clock periods as
illustrated in Figure A-5.

0 1 2 3 N-1 N

tmin1

tnom

tmax1

tminN

tmaxN

Figure A-5. Jitter Definitions

The relative deviation of tnom is at its maximum for one clock period, and decreases towards zero for larger
number of clock periods (N).

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1239



Appendix A Electrical Characteristics

Defining the jitter as:

⎛ t (N) tmin(N) ⎞
J(N) = max⎜ 1 – max

---------------------- , 1 – -------
N ⋅--------------- ⎟

⎝ N ⋅ tnom tnom ⎠

The following equation is a good fit for the maximum jitter:

j
J(N) = ----1--- + j

N 2

J(N)

1 5 10 20 N

Figure A-6. Maximum bus clock jitter approximation

MC9S12XE-Family Reference Manual  Rev. 1.25

1240 Freescale Semiconductor



Appendix A Electrical Characteristics

This is important to note with respect to timers, serial modules where a prescaler will eliminate the effect
of the jitter to a large extent.

Table A-25. IPLL Characteristics

Conditions are shown in Table A-4 unless otherwise noted

Num C Rating Symbol Min Typ Max Unit

1 P Self Clock Mode frequency(1) fSCM 1 — 4 MHz

2 C VCO locking range fVCO 32 — 120 MHz

3 C Reference Clock fREF 1 — 40 MHz

4 D Lock Detection |∆Lock| 0 — 1.5 %(2)

5 D Un-Lock Detection |∆unl| 0.5 — 2.5 %2

— 150 +
7 C Time to lock tlock 214 µs

256/fREF

8 C Jitter fit parameter 1(3) j1 — — 1.2 %

9 C Jitter fit parameter 23 j2 — — 0 %

10 D Bus Frequency for FM1=1, FM0=1 (frequency fbus — — 48 MHz
modulation in PLLCTL register of s12xe_crg)

11 D Bus Frequency for FM1=1, FM0=0 (frequency fbus — — 49 MHz
modulation in PLLCTL register of s12xe_crg)

12 D Bus Frequency for FM1=0, FM0=1 (frequency fbus — — 49 MHz
modulation in PLLCTL register of s12xe_crg)

1. Bus frequency is equivalent to fSCM/2
2. % deviation from target frequency
3. fOSC = 4MHz, fBUS = 50MHz equivalent fPLL = 100MHz: REFDIV=$01, REFRQ=01, SYNDIV=$18, VCOFRQ=11, POSTDIV=$

00.

A.7 External Interface Timing

A.7.1 MSCAN
Table A-26.  MSCAN Wake-up Pulse Characteristics

Conditions are shown in Table A-4 unless otherwise noted

Num C Rating Symbol Min Typ Max Unit

1 P MSCAN wakeup dominant pulse filtered tWUP — — 1.5 µs
2 P MSCAN wakeup dominant pulse pass tWUP 5 — — µs

A.7.2 SPI Timing
This section provides electrical parametrics and ratings for the SPI. In Table A-27 the measurement
conditions are listed.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1241



Appendix A Electrical Characteristics

Table A-27. Measurement Conditions

Description Value Unit

Drive mode Full drive mode —
Load capacitance C (1)

LOAD , on all outputs 50 pF
Thresholds for delay measurement points (20% / 80%) VDDX V

1. Timing specified for equal load on all SPI output pins. Avoid asymmetric load.

A.7.2.1 Master Mode
In Figure A-7 the timing diagram for master mode with transmission format CPHA = 0 is depicted.

SS1
(Output)

2 1 12 13 3
SCK 4

(CPOL = 0)
(Output) 4

12 13
SCK

(CPOL = 1)
(Output)

5 6

MISO
(Input) MSB IN2 Bit MSB-1. . . 1 LSB IN

10 9 11

MOSI
(Output) MSB OUT2 Bit MSB-1. . . 1 LSB OUT

1. If configured as an output.
2. LSBF = 0. For LSBF = 1, bit order is LSB, bit 1, bit 2... MSB.

Figure A-7. SPI Master Timing (CPHA = 0)

MC9S12XE-Family Reference Manual  Rev. 1.25

1242 Freescale Semiconductor



Appendix A Electrical Characteristics

In Figure A-8 the timing diagram for master mode with transmission format CPHA=1 is depicted.

SS1
(Output)

1
2 12 13 3

SCK
(CPOL = 0)

(Output)
4 4 12 13

SCK
(CPOL = 1)

(Output)
5 6

MISO
(Input) MSB IN2 Bit MSB-1. . . 1 LSB IN

9 11
MOSI

(Output) Port Data Master MSB OUT2 Bit MSB-1. . . 1 Master LSB OUT Port Data

1.If configured as output
2. LSBF = 0. For LSBF = 1, bit order is LSB, bit 1,bit 2... MSB.

Figure A-8. SPI Master Timing (CPHA = 1)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1243



Appendix A Electrical Characteristics

In Table A-28 the timing characteristics for master mode are listed.
Table A-28. SPI Master Mode Timing Characteristics

Num C Characteristic Symbol Min Typ Max Unit
1 D SCK frequency fsck 1/2048 — 1/2(1) fbus
1 D SCK period tsck 21 — 2048 tbus
2 D Enable lead time tlead — 1/2 — tsck
3 D Enable lag time tlag — 1/2 — tsck
4 D Clock (SCK) high or low time twsck — 1/2 — tsck
5 D Data setup time (inputs) tsu 8 — — ns
6 D Data hold time (inputs) thi 8 — — ns
9 D Data valid after SCK edge tvsck — — 15 ns
10 D Data valid after SS fall (CPHA = 0) tvss — — 15 ns
11 D Data hold time (outputs) tho 0 — — ns
12 D Rise and fall time inputs trfi — — 8 ns
13 D Rise and fall time outputs trfo — — 8 ns

1. See Figure A-9.

fSCK/fbus

1/2

1/4

5 10 15 20 25 30 35 40 fbus [MHz]
Figure A-9. Derating of maximum fSCK to fbus ratio in Master Mode

MC9S12XE-Family Reference Manual  Rev. 1.25

1244 Freescale Semiconductor



Appendix A Electrical Characteristics

A.7.2.2 Slave Mode
In Figure A-10 the timing diagram for slave mode with transmission format CPHA = 0 is depicted.

SS
(Input)

1 12 13 3
SCK

(CPOL = 0)
(Input)

2 4 4 12 13
SCK

(CPOL = 1)
(Input) 10 8

7 9 11 11

MISO See
(Output) Slave MSB Bit MSB-1 . . . 1 Slave LSB OUT See

Note Note

5 6
MOSI

(Input) MSB IN Bit MSB-1. . . 1 LSB IN

NOTE: Not defined
Figure A-10. SPI Slave Timing (CPHA = 0)

In Figure A-11 the timing diagram for slave mode with transmission format CPHA = 1 is depicted.

SS
(Input)

1 3
2 12 13

SCK
(CPOL = 0)

(Input)
4 4 12 13

SCK
(CPOL = 1)

(Input)
9 11 8

MISO See
(Output) Note Slave MSB OUT Bit MSB-1 . . . 1 Slave LSB OUT

7 5 6
MOSI

(Input) MSB IN Bit MSB-1 . . . 1 LSB IN

NOTE: Not defined
Figure A-11. SPI Slave Timing (CPHA = 1)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1245



Appendix A Electrical Characteristics

In Table A-29 the timing characteristics for slave mode are listed.
Table A-29. SPI Slave Mode Timing Characteristics

Num C Characteristic Symbol Min Typ Max Unit

1 D SCK frequency fsck DC — 1/4 fbus

1 D SCK period tsck 4 — ∞ tbus

2 D Enable lead time tlead 4 — — tbus

3 D Enable lag time tlag 4 — — tbus

4 D Clock (SCK) high or low time twsck 4 — — tbus

5 D Data setup time (inputs) tsu 8 — — ns

6 D Data hold time (inputs) thi 8 — — ns

7 D Slave access time (time to data active) ta — — 20 ns

8 D Slave MISO disable time tdis — — 22 ns

9 D Data valid after SCK edge tvsck — — 28 + 0.5 ⋅ t (1)
bus ns

10 D Data valid after SS fall tvss — — 28 + 0.5 ⋅ t 1
bus ns

11 D Data hold time (outputs) tho 20 — — ns

12 D Rise and fall time inputs trfi — — 8 ns

13 D Rise and fall time outputs trfo — — 8 ns
1. 0.5 tbus added due to internal synchronization delay

MC9S12XE-Family Reference Manual  Rev. 1.25

1246 Freescale Semiconductor



Appendix A Electrical Characteristics

A.7.3 External Bus Timing
The following conditions are assumed for all following external bus timing values:

• Crystal input within 45% to 55% duty
• Equal 25 pF load on all pins
• Pad full drive (reduced drive must be off)

A.7.3.1 Normal Expanded Mode (External Wait Feature Disabled)

1 1

CSx

ADDRx ADDR1 ADDR2

2 3

RE

4 5

WE

8 6
7 11

10

DATAx (Read) DATA1 (Write) DATA2

9

EWAIT

UDS, LDS

Figure A-12. Example 1a: Normal Expanded Mode — Read Followed by Write

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1247



Appendix A Electrical Characteristics

Table A-30. Example 1a: Normal Expanded Mode Timing 50 MHz bus (EWAIT disabled)

VDD5=5.0V VDD5=3.3V
No. Characteristic Symbol Unit

C Min Max C Min Max
- Frequency of internal bus fi - D.C. 50.0 - D.C. 25.0 MHz
- Internal cycle time tcyc - 20 ∞ - 40 ∞ ns
- Frequency of external bus fo - D.C. 25.0 - D.C. 12.5 MHz
1 External cycle time (selected by EXSTR) tcyce - 40 ∞ - 80 ∞ ns
2 Address (1) valid to RE fall tADRE D 4 - D 13 - ns
3 Pulse width, RE PWRE D 28 - D 58 - ns
4 Address valid to WE fall tADWE D 4 - D 15 - ns
5 Pulse width, WE PWWE D 18 - D 38 - ns
6 Read data setup time (if ITHRS = 0) tDSR D 19 - D 38 - ns

Read data setup time (if ITHRS = 1) tDSR D 23 - D N/A ns
7 Read data hold time tDHR D 0 - D 0 - ns
8 Read enable access time tACCR D 4 - D 4 - ns
9 Write data valid to WE fall tWDWE D 5 - D 5 - ns
10 Write data setup time tDSW D 23 - D 43 - ns
11 Write data hold time tDHW D 6 - D 4 - ns

1. Includes the following signals: ADDRx, UDS, LDS, and CSx.

MC9S12XE-Family Reference Manual  Rev. 1.25

1248 Freescale Semiconductor



Appendix A Electrical Characteristics

A.7.3.2 Normal Expanded Mode (External Wait Feature Enabled)

1

CSx

ADDRx ADDR1 ADDR2

2 3

RE

WE

8 6
7

DATAx (Read) DATA1

12
13

EWAIT

UDS, LDS

Figure A-13. Example 1b: Normal Expanded Mode — Stretched Read Access

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1249



Appendix A Electrical Characteristics

1

CSx

ADDRx ADDR1 ADDR2

RE

4 5

WE

9
10 11

DATAx (Write) DATA1

12
13

EWAIT

UDS, LDS

Figure A-14. Example 1b: Normal Expanded Mode — Stretched Write Access

Table A-31. Example 1b: Normal Expanded Mode Timing at 50MHz bus (EWAIT enabled)

VDD5 = 5.0V VDD5 = 3.3V

2 stretch 3 stretch 2 stretch 3 stretch
No. Characteristic Symbol Unit

C cycles cycles C cycles cycles

Min Max Min Max Min Max Min Max
- Frequency of internal bus fi - D.C. 50.0 D.C. 50.0 - D.C. 25.0 D.C. 25.0 MHz
- Internal cycle time tcyc - 20 ∞ 20 ∞ - 20 ∞ 20 ∞ ns
- Frequency of external bus fo - D.C. 16.7 D.C. 12.5 - D.C. 8.33 D.C. 6.25 MHz

MC9S12XE-Family Reference Manual  Rev. 1.25

1250 Freescale Semiconductor



Appendix A Electrical Characteristics

Table A-31. Example 1b: Normal Expanded Mode Timing at 50MHz bus (EWAIT enabled)

VDD5 = 5.0V VDD5 = 3.3V

2 stretch 3 stretch 2 stretch 3 stretch
No. Characteristic Symbol Unit

C cycles cycles C cycles cycles

Min Max Min Max Min Max Min Max
- External cycle time (selected tcyce - 60 ∞ 80 ∞ - 120 ∞ 160 ∞ ns

by EXSTR)
1 External cycle time tcycew - 80 ∞ 100 ∞ - 160 ∞ 200 ∞ ns

(EXSTR+1EWAIT)
2 Address (1) valid to RE fall tADRE D 4 - 4 - D 13 - 13 - ns
3 Pulse width, RE (2) PWRE D 68 - 88 - D 138 - 178 - ns
4 Address  valid to WE fall tADWE D 4 - 4 - D 15 - 15 - ns
5 Pulse width, WE PWWE D 58 - 78 - D 118 - 158 - ns
6 Read data setup time tDSR D 19 - 19 - D 38 - 38 - ns

(if ITHRS = 0)
Read data setup time tDSR D 23 - 23 - D N/A ns
(if ITHRS = 1)

7 Read data hold time tDHR D 0 - 0 - D 0 - 0 - ns
8 Read enable access time tACCR D 49 - 69 - D 65 - 105 - ns
9 Write data valid to WE fall tWDWE D 5 - 5 - D 5 - 5 - ns
10 Write data setup time tDSW D 63 - 93 - D 123 - 163 - ns
11 Write data hold time tDHW D 6 - 6 - D 4 - 4 - ns
12 Address to EWAIT fall tADWF D 0 16 0 36 D 0 20 0 - ns
13 Address to EWAIT rise tADWR D 30 39 50 58 D 50 61 90 101 ns

1. Includes the following signals: ADDRx, UDS, LDS, and CSx.
2. Affected by EWAIT.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1251



Appendix A Electrical Characteristics

A.7.3.3 Emulation Single-Chip Mode (Without Wait States)

1 1

2 3

ECLK2X

ECLK

4 5 6 7

ADDR
[22:20]/ addr1 acc1 addr2 acc2 addr3
ACC
[2:0]
ADDR
[19:16]/
IQSTAT addr1 iqstat0 addr2 iqstat1 addr3
[3:0]

ADDR
[15:0]/
IVD addr1 ivd0 addr2 ivd1 addr3
[15:0]

8
9

DATAx data0 (read) data1 (write) data2

10 11
12 12

R/W

LSTRB

Figure A-15. Example 2a: Emulation Single-Chip Mode — Read Followed by Write

MC9S12XE-Family Reference Manual  Rev. 1.25

1252 Freescale Semiconductor



Appendix A Electrical Characteristics

Table A-32. Example 2a: Emulation Single-Chip Mode Timing 50 Mhz bus, VDD5=5.0V (EWAIT disabled)

No. C Characteristic (1) Symbol Min Max Unit
- - Frequency of internal bus fi D.C. 50.0 MHz
1 - Cycle time tcyc 20 ∞ ns
2 D Pulse width, E high PWEH 9 - ns
3 D Pulse width, E low PWEL 9 - ns
4 D Address delay time tAD - 5 ns
5 D Address hold time tAH 0 - ns
6 D IVDx delay time (2) tIVDD - 4.5 ns
7 D IVDx hold time tIVDH 0 - ns
8 D Read data setup time (ITHRS = 1 only) tDSR 15 - ns
9 D Read data hold time tDHR 0 - ns
10 D Write data delay time tDDW - 5 ns
11 D Write data hold time tDHW 0 - ns
12 D Read/write data delay time (3) tRWD -1 5 ns

1. Typical Supply and Silicon, Room Temperature Only
2. Includes also ACCx, IQSTATx
3. Includes LSTRB

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1253



Appendix A Electrical Characteristics

A.7.3.4 Emulation Expanded Mode (With Optional Access Stretching)

1

2 3

ECLK2X

ECLK

4 5 7
6

ADDR
[22:20]/

ACC ADDR1 ACC1 ADDR1 000 ADDR2
[2:0]

ADDR
[19:16]/
IQSTAT ADDR1 IQSTAT0 ADDR1 IQSTAT1 ADDR2

[3:0]

ADDR
[15:0]/ ADDR1 ? ADDR1 IVD1 ADDR2

IVD
[15:0]

8
9

DATAx DATA0 (Read) DATA1

12 12

R/W

LSTRB

Figure A-16. Example 2b: Emulation Expanded Mode — Read with 1 Stretch Cycle

MC9S12XE-Family Reference Manual  Rev. 1.25

1254 Freescale Semiconductor



Appendix A Electrical Characteristics

1

2 3

ECLK2X

ECLK

4 5 7
6

ADDR
[22:20]/ ADDR1 ACC1 ADDR1 000 ADDR2

ACC
[2:0]

ADDR
[19:16]/
IQSTAT ADDR1 IQSTAT0 ADDR1 IQSTAT1 ADDR2

[3:0]

ADDR
[15:0]/

IVD ADDR1 ? ADDR1 x ADDR2
[15:0]

10 11

DATAx (write) data1

12 12

R/W

LSTRB

Figure A-17. Example 2b: Emulation Expanded Mode Ò Write with 1 Stretch Cycle

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1255



Appendix A Electrical Characteristics

Table A-33. Example 2b: Emulation Expanded Mode Timing 50 MHz bus, VDD5=5.0V (EWAIT disabled)

1 stretch 2 stretch 3 stretch
No. C Characteristic (1) Symbol cycle cycles cycles Unit

Min Max Min Max Min Max
- - Internal cycle time tcyc 20 ∞ 20 ∞ 20 ∞ ns
1 - Cycle time tcyce 40 ∞ 60 ∞ 80 ∞ ns
2 D Pulse width, E high PWEH 9 11 9 11 9 11 ns
3 D E falling to sampling E rising tEFSR 28 32 48 52 68 72 ns
4 D Address delay time tAD refer to table refer to table refer to table ns
5 D Address hold time t Table A-32 Table A-32 Table A-32

AH ns
6 D IVD delay time (2) tIVDD ns
7 D IVD hold time tIVDH ns
8 D Read data setup time tDSR ns
9 D Read data hold time tDHR ns
10 D Write data delay time tDDW ns
11 D Write data hold time tDHW ns
12 D Read/write data delay time (3) tRWD ns

1. Typical Supply and Silicon, Room Temperature Only
2. Includes also ACCx, IQSTATx
3. Includes LSTRB

MC9S12XE-Family Reference Manual  Rev. 1.25

1256 Freescale Semiconductor



Appendix A Electrical Characteristics

A.7.3.5 External Tag Trigger Timing

1

ECLK

ADDR ADDR

DATAx

     DATA

R/W

2
TAGHI/TAGLO

3

Figure A-18. External Trigger Timing

Table A-34. External Tag Trigger Timing VDD35 = 5.0 V

No. C Characteristic (1) Symbol Min Max Unit

- D Frequency of internal bus fi D.C. 50.0 MHz

1 D Cycle time tcyc 20 ∞ ns

2 D TAGHI/TAGLO setup time tTS 10 — ns

3 D TAGHI/TAGLO hold time tTH 0 — ns
1. Typical supply and silicon, room temperature only

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1257