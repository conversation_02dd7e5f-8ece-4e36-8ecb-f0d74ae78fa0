﻿Appendix F Ordering Information

Appendix F
Ordering Information
Customers can choose between ordering either the mask-specific partnumber or the generic / mask-
independent partnumber. Ordering the mask-specific partnumber enables the customer to specify which
particular maskset they receive whereas ordering the generic maskset means that the currently preferred
maskset (which may change over time) is shipped. In either case, the marking on the device will always
show the generic / mask-independent partnumber and the mask set number.

NOTE
The  mask identifier suffix and the Tape & Reel suffix are always both omitted from the

partnumber which is actually marked on the device.
For specific partnumbers to order, please contact your local sales office. The below figure illustrates the
structure of a typical mask-specific ordering number for the MC9S12XE-Family devices
S 9 12X EP100 J1 C AG R

Tape & Reel R = Tape & Reel
No R = No Tape & Reel

AA = 80  QFP
Package Option AL = 112 LQFP

AG = 144 LQFP
VL = 208 MAPBGA

Temperature Option C = -40˚C to 85˚C
V = -40˚C to 105˚C
M = -40˚C to 125˚C

Maskset identifier Suffix
First digit references fab
J=TSMC3, F=ATMC,
W=TSMC11, Blank= flexible fab
Second digit refers to mask or firmware revision
Numeric second digit = mask rev. (e.g.1=1M48H)
A=firmware revA, version ID=0xFFFF
B=firmware revB, version ID=0x0004
(This suffix is omitted in generic partnumbers)
Device Title
Controller Family
Main Memory Type:
9 = Flash
3 = ROM (if available)
Status / Partnumber type:
S or SC = Maskset specific partnumber
MC = Generic / mask-independent partnumber
P or PC = prototype status (pre qualification)

Figure F-1. Order Part Number Example

MC9S12XE-Family Reference Manual  Rev. 1.25

1322 Freescale Semiconductor






How to Reach Us:

Home Page:
www.freescale.com

USA/Europe or Locations Not Listed:
Freescale Semiconductor
Technical Information Center, CH370
1300 N. Alma School Road
Chandler, Arizona 85224
1-800-521-6274 or 480-768-2130

Europe, Middle East, and Africa:
+44 1296 380 456 (English)
+46 8 52200080 (English)
+49 89 92103 559 (German)
+33 1 69 35 48 48 (French)

Japan:
Freescale Semiconductor Japan Ltd.
Technical Information Center
3-20-1, Minami-Azabu, Minato-ku
Tokyo 106-0047, Japan
0120-191014 or +81-3-3440-3569

Asia/Pacific:
Freescale Semiconductor Hong Kong Ltd.
Technical Information Center
2 Dai King Street Information in this document is provided solely to enable system and

software implementers to use Freescale Semiconductor products. There are
Tai Po Industrial Estate no express or implied copyright licenses granted hereunder to design or
Tai Po, N.T., Hong Kong fabricate any integrated circuits or integrated circuits based on the
852-26668334 information in this document.

For Literature Requests Only: Freescale Semiconductor reserves the right to make changes without further
Freescale Semiconductor Literature Distribution Center notice to any products herein. Freescale Semiconductor makes no warranty,
P.O. Box 5405 representation or guarantee regarding the suitability of its products for any
Denver, Colorado 80217 particular purpose, nor does Freescale Semiconductor assume any liability
1-800-441-2447 or 303-675-2140 arising out of the application or use of any product or circuit, and specifically
Fax: 303-675-2150 disclaims any and all liability, including without limitation consequential or

incidental damages. “Typical” parameters that may be provided in Freescale
Semiconductor data sheets and/or specifications can and do vary in different
applications and actual performance may vary over time. All operating
parameters, including “Typicals”, must be validated for each customer
application by customer’s technical experts. Freescale Semiconductor does
not convey any license under its patent rights nor the rights of others.
Freescale Semiconductor products are not designed, intended, or authorized
for use as components in systems intended for surgical implant into the body,
or other applications intended to support or sustain life, or for any other
application in which the failure of the Freescale Semiconductor product could
create a situation where personal injury or death may occur. Should Buyer
purchase or use Freescale Semiconductor products for any such unintended
or unauthorized application, Buyer shall indemnify and hold Freescale
Semiconductor and its officers, employees, subsidiaries, affiliates, and
distributors harmless against all claims, costs, damages, and expenses, and
reasonable attorney fees arising out of, directly or indirectly, any claim of
personal injury or death associated with such unintended or unauthorized
use, even if such claim alleges that Freescale Semiconductor was negligent
regarding the design or manufacture of the part.

Freescale™ and the
Freescale logo are trademarks of Freescale Semiconductor, Inc. All other
product or service names are the property
of their respective owners.
© Freescale Semiconductor, Inc. 2005-2013. All rights reserved.
MC9S12XEP100RMV1
Rev. 1.25
02/2013



Mouser Electronics
  
Authorized Distributor
 
  
Click to View Pricing, Inventory, Delivery & Lifecycle Information:
 
 
 
 NXP:  
  MC9S12XEP100CAG  MC9S12XEP100CAL  MC9S12XEP100MAG  MC9S12XEP100MAL  MC9S12XEP768CAG 
MC9S12XEP768CAL  MC9S12XEP768MAG  MC9S12XEP768MAL  S912XEG128J2MAA  S912XEP100J5MAL 
S912XEP768J5MAG  S912XEQ384J3VAG  S912XEQ384J3VAL  S912XEQ512J3CAGR  S912XEQ512J3MAA 
S912XEQ512J3MAL  S912XET256J2CAL  S912XET256J2MAA  S912XET256J2MAG  S912XET256J2MAL 
S912XET256J2VAGR  MC9S12XEQ384VAG  MC9S12XEQ512CAA  MC9S12XEQ512MAA  MC9S12XEQ512VAA 
S912XEQ384J3CAL  S912XEP100J5MAGR  S912XET512J3VALR  MC9S12XEG128MAA  MC9S12XEG128MAL 
MC9S12XEQ384CAG  MC9S12XEQ384CAL  MC9S12XEQ384MAG  MC9S12XEQ384MAL  MC9S12XEQ512CAG 
MC9S12XEQ512CAL  MC9S12XEQ512MAG  MC9S12XEQ512MAL  MC9S12XET256MAA  MC9S12XET256MAG 
MC9S12XET256MAL  S912XEP100J5MAG  MC9S12XET256CAG  S912XEQ512J3MAG  S912XEQ512J3VAG 
S912XEQ512J3CAL  MC9S12XEG128CAL  MC9S12XEG128CALR  MC9S12XEP100CVL  MC9S12XEP100MVL 
MC9S12XEQ512CAGR  S912XEP100J5VAGR  S912XEP768J5MAGR  S912XEG128J2MAL  S912XEP100J5CVL 
S912XEQ512F1MAA  S912XEA128J2CAA  S912XEQ384BVAL  S912XEQ384BVALR  S912XEG128J2MAAR 
S912XEP768J4MAG  S912XET256J2CALR  S912XET256J2MALR  S912XET256J2VAG  S912XET256J2CAGR 
S912XEP100J5MALR  S912XEP768J4MAGR  S912XEP768J5CAGR  S912XEQ384F1VALR  S912XET256J2CAA 
S912XET256J2CAAR  S912XET256J2MAAR  S912XEQ512BVAGR  S912XEQ512BCAG  S912XEQ512BCAGR 
S912XEQ512BVAG  S912XEQ384F1VAL  S912XEQ512J3CAA  S912XEP100BMAG  S912XEA128J2MAA 
S912XET256W1MAG  S912XEG128W1MAL  S912XET256BMALR  S912XEG128W1MAA  S912XEP100BCAG 
S912XET256W0MAA  S912XET256BMAA  S912XET256W1MAL  S912XET256BMAL  S912XET256BMAAR 
S912XEP100BCAGR  S912XET256W1MAA  S912XEP100BMAL  S912XEQ384F1MAG  S912XEP100W1MAL 
S912XEQ384F1CAG  S912XET256BVAG  S912XET256BMAG  S912XET256BCAL  S912XEP100AVAG