using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace VolvoFlashWR.UI.Converters
{
    /// <summary>
    /// Converts a boolean value to a Visibility value
    /// </summary>
    public class BooleanToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// Converts a boolean value to a Visibility value
        /// </summary>
        /// <param name="value">The boolean value</param>
        /// <param name="targetType">The target type</param>
        /// <param name="parameter">Optional parameter to invert the conversion</param>
        /// <param name="culture">The culture</param>
        /// <returns>Visibility.Visible if true, Visibility.Collapsed if false</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool invert = parameter != null && parameter.ToString().ToLower() == "invert";
            bool boolValue = value != null && (bool)value;

            if (invert)
            {
                boolValue = !boolValue;
            }

            return boolValue ? Visibility.Visible : Visibility.Collapsed;
        }

        /// <summary>
        /// Converts a Visibility value to a boolean value
        /// </summary>
        /// <param name="value">The Visibility value</param>
        /// <param name="targetType">The target type</param>
        /// <param name="parameter">Optional parameter to invert the conversion</param>
        /// <param name="culture">The culture</param>
        /// <returns>True if Visibility.Visible, false otherwise</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool invert = parameter != null && parameter.ToString().ToLower() == "invert";
            bool boolValue = value != null && (Visibility)value == Visibility.Visible;

            if (invert)
            {
                boolValue = !boolValue;
            }

            return boolValue;
        }
    }
}
