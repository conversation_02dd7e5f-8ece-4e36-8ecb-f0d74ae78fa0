using System;

namespace VolvoFlashWR.UI.Models
{
    /// <summary>
    /// Represents a log entry in the UI
    /// </summary>
    public class LogEntry
    {
        /// <summary>
        /// Gets or sets the timestamp of the log entry
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the type of the log entry (Info, Warning, Error)
        /// </summary>
        public string Type { get; set; } = "Info";

        /// <summary>
        /// Gets or sets the message of the log entry
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Creates a new log entry with the current timestamp
        /// </summary>
        /// <param name="type">The type of the log entry</param>
        /// <param name="message">The message of the log entry</param>
        /// <returns>A new log entry</returns>
        public static LogEntry Create(string type, string message)
        {
            return new LogEntry
            {
                Timestamp = DateTime.Now,
                Type = type,
                Message = message
            };
        }

        /// <summary>
        /// Creates a new information log entry
        /// </summary>
        /// <param name="message">The message of the log entry</param>
        /// <returns>A new information log entry</returns>
        public static LogEntry Info(string message)
        {
            return Create("Info", message);
        }

        /// <summary>
        /// Creates a new warning log entry
        /// </summary>
        /// <param name="message">The message of the log entry</param>
        /// <returns>A new warning log entry</returns>
        public static LogEntry Warning(string message)
        {
            return Create("Warning", message);
        }

        /// <summary>
        /// Creates a new error log entry
        /// </summary>
        /// <param name="message">The message of the log entry</param>
        /// <returns>A new error log entry</returns>
        public static LogEntry Error(string message)
        {
            return Create("Error", message);
        }
    }
}
