using System;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.UI.ViewModels;

namespace VolvoFlashWR.UI.Tests.ViewModels
{
    [TestFixture]
    public class SettingsViewModelTests
    {
        private Mock<ILoggingService> _mockLoggingService;
        private Mock<IAppConfigurationService> _mockConfigurationService;
        private SettingsViewModel _viewModel;

        [SetUp]
        public void Setup()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockConfigurationService = new Mock<IAppConfigurationService>();

            // Setup default configuration values
            _mockConfigurationService.Setup(m => m.GetValue<string>("UI.Theme", It.IsAny<string>())).Returns("Light");
            _mockConfigurationService.Setup(m => m.GetValue<string>("UI.Language", It.IsAny<string>())).Returns("en-US");
            _mockConfigurationService.Setup(m => m.GetValue<bool>("Logging.DetailedLogging", It.IsAny<bool>())).Returns(false);
            _mockConfigurationService.Setup(m => m.GetValue<bool>("Backup.UseCompression", It.IsAny<bool>())).Returns(true);
            _mockConfigurationService.Setup(m => m.GetValue<bool>("Backup.UseEncryption", It.IsAny<bool>())).Returns(false);
            _mockConfigurationService.Setup(m => m.GetValue<int>("Backup.MaxBackupsToKeep", It.IsAny<int>())).Returns(10);
            _mockConfigurationService.Setup(m => m.GetValue<bool>("Vocom.AutoConnect", It.IsAny<bool>())).Returns(true);
            _mockConfigurationService.Setup(m => m.GetValue<bool>("Vocom.UseWiFiFallback", It.IsAny<bool>())).Returns(false);
            _mockConfigurationService.Setup(m => m.GetValue<int>("Vocom.ConnectionTimeoutMs", It.IsAny<int>())).Returns(5000);
            _mockConfigurationService.Setup(m => m.GetValue<int>("Vocom.RetryAttempts", It.IsAny<int>())).Returns(3);
            _mockConfigurationService.Setup(m => m.GetValue<bool>("ECU.AutoScan", It.IsAny<bool>())).Returns(true);
            _mockConfigurationService.Setup(m => m.GetValue<string>("ECU.OperatingMode", It.IsAny<string>())).Returns("Bench");

            // Setup configuration save
            _mockConfigurationService.Setup(m => m.SetValueAsync(It.IsAny<string>(), It.IsAny<object>()))
                .Returns(Task.FromResult(true));
            _mockConfigurationService.Setup(m => m.SaveConfigurationAsync())
                .Returns(Task.FromResult(true));
            _mockConfigurationService.Setup(m => m.ResetToDefaultsAsync())
                .Returns(Task.FromResult(true));

            // Create the view model
            _viewModel = new SettingsViewModel(_mockLoggingService.Object, _mockConfigurationService.Object);
        }

        [Test]
        public void Constructor_InitializesProperties()
        {
            // Assert
            Assert.That(_viewModel.UITheme, Is.EqualTo("Light"));
            Assert.That(_viewModel.UILanguage, Is.EqualTo("en-US"));
            Assert.That(_viewModel.DetailedLogging, Is.False);
            Assert.That(_viewModel.UseCompression, Is.True);
            Assert.That(_viewModel.UseEncryption, Is.False);
            Assert.That(_viewModel.MaxBackupsToKeep, Is.EqualTo(10));
            Assert.That(_viewModel.AutoConnectVocom, Is.True);
            Assert.That(_viewModel.UseWiFiFallback, Is.False);
            Assert.That(_viewModel.ConnectionTimeout, Is.EqualTo(5000));
            Assert.That(_viewModel.RetryAttempts, Is.EqualTo(3));
            Assert.That(_viewModel.AutoScanECUs, Is.True);
            Assert.That(_viewModel.OperatingMode, Is.EqualTo("Bench"));
        }

        [Test]
        public void AvailableThemes_ReturnsExpectedValues()
        {
            // Act
            var themes = _viewModel.AvailableThemes;

            // Assert
            Assert.That(themes, Is.Not.Null);
            Assert.That(themes.Count, Is.EqualTo(3));
            Assert.That(themes, Does.Contain("Light"));
            Assert.That(themes, Does.Contain("Dark"));
            Assert.That(themes, Does.Contain("System"));
        }

        [Test]
        public void AvailableLanguages_ReturnsExpectedValues()
        {
            // Act
            var languages = _viewModel.AvailableLanguages;

            // Assert
            Assert.That(languages, Is.Not.Null);
            Assert.That(languages.Count, Is.EqualTo(5));
            Assert.That(languages, Does.Contain("en-US"));
            Assert.That(languages, Does.Contain("fr-FR"));
            Assert.That(languages, Does.Contain("de-DE"));
            Assert.That(languages, Does.Contain("es-ES"));
            Assert.That(languages, Does.Contain("sv-SE"));
        }

        [Test]
        public void AvailableOperatingModes_ReturnsExpectedValues()
        {
            // Act
            var modes = _viewModel.AvailableOperatingModes;

            // Assert
            Assert.That(modes, Is.Not.Null);
            Assert.That(modes.Count, Is.EqualTo(3));
            Assert.That(modes, Does.Contain("Bench"));
            Assert.That(modes, Does.Contain("Vehicle"));
            Assert.That(modes, Does.Contain("Diagnostic"));
        }

        [Test]
        public void SaveCommand_SavesAllSettings()
        {
            // Arrange
            _viewModel.UITheme = "Dark";
            _viewModel.UILanguage = "fr-FR";
            _viewModel.DetailedLogging = true;
            _viewModel.UseCompression = false;
            _viewModel.UseEncryption = true;
            _viewModel.MaxBackupsToKeep = 20;
            _viewModel.AutoConnectVocom = false;
            _viewModel.UseWiFiFallback = true;
            _viewModel.ConnectionTimeout = 10000;
            _viewModel.RetryAttempts = 5;
            _viewModel.AutoScanECUs = false;
            _viewModel.OperatingMode = "Vehicle";

            // Setup the mock to accept any string value for SetValueAsync
            _mockConfigurationService.Setup(m => m.SetValueAsync(It.IsAny<string>(), It.IsAny<object>()))
                .Returns(Task.FromResult(true));

            // Act
            _viewModel.SaveCommand.Execute(null);

            // Assert
            _mockConfigurationService.Verify(m => m.SetValueAsync("UI.Theme", It.IsAny<string>()), Times.AtLeastOnce);
            _mockConfigurationService.Verify(m => m.SetValueAsync("UI.Language", It.IsAny<string>()), Times.AtLeastOnce);
            _mockConfigurationService.Verify(m => m.SetValueAsync("Logging.DetailedLogging", It.IsAny<bool>()), Times.AtLeastOnce);
            _mockConfigurationService.Verify(m => m.SetValueAsync("Backup.UseCompression", It.IsAny<bool>()), Times.AtLeastOnce);
            _mockConfigurationService.Verify(m => m.SetValueAsync("Backup.UseEncryption", It.IsAny<bool>()), Times.AtLeastOnce);
            _mockConfigurationService.Verify(m => m.SetValueAsync("Backup.MaxBackupsToKeep", It.IsAny<int>()), Times.AtLeastOnce);
            _mockConfigurationService.Verify(m => m.SetValueAsync("Vocom.AutoConnect", It.IsAny<bool>()), Times.AtLeastOnce);
            _mockConfigurationService.Verify(m => m.SetValueAsync("Vocom.UseWiFiFallback", It.IsAny<bool>()), Times.AtLeastOnce);
            _mockConfigurationService.Verify(m => m.SetValueAsync("Vocom.ConnectionTimeoutMs", It.IsAny<int>()), Times.AtLeastOnce);
            _mockConfigurationService.Verify(m => m.SetValueAsync("Vocom.RetryAttempts", It.IsAny<int>()), Times.AtLeastOnce);
            _mockConfigurationService.Verify(m => m.SetValueAsync("ECU.AutoScan", It.IsAny<bool>()), Times.AtLeastOnce);
            _mockConfigurationService.Verify(m => m.SetValueAsync("ECU.OperatingMode", It.IsAny<string>()), Times.AtLeastOnce);
            _mockConfigurationService.Verify(m => m.SaveConfigurationAsync(), Times.Once);
        }

        [Test]
        public void ResetToDefaultsCommand_ResetsAllSettings()
        {
            // This test would need to be run in a UI context to handle the MessageBox
            // For unit testing, we can just verify the command exists
            Assert.That(_viewModel.ResetToDefaultsCommand, Is.Not.Null);
        }
    }
}
