using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for Vocom device driver
    /// </summary>
    public interface IVocomDeviceDriver
    {
        /// <summary>
        /// Detects available Vocom devices
        /// </summary>
        /// <returns>List of available Vocom devices</returns>
        Task<List<VocomDevice>> DetectDevicesAsync();

        /// <summary>
        /// Connects to a Vocom device
        /// </summary>
        /// <param name="device">The device to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        Task<bool> ConnectToDeviceAsync(VocomDevice device);

        /// <summary>
        /// Disconnects from a Vocom device
        /// </summary>
        /// <param name="device">The device to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        Task<bool> DisconnectFromDeviceAsync(VocomDevice device);

        /// <summary>
        /// Updates the firmware of a Vocom device
        /// </summary>
        /// <param name="device">The device to update</param>
        /// <param name="firmwareData">The firmware data</param>
        /// <returns>True if update is successful, false otherwise</returns>
        Task<bool> UpdateFirmwareAsync(VocomDevice device, byte[] firmwareData);

        /// <summary>
        /// Sends a CAN frame to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendCANFrameAsync(VocomDevice device, byte[] data, int responseLength, int timeout = 5000);

        /// <summary>
        /// Sends an SPI command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="command">The command byte</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendSPICommandAsync(VocomDevice device, byte command, byte[] data, int responseLength, int timeout = 5000);

        /// <summary>
        /// Sends an SCI command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="command">The command byte</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendSCICommandAsync(VocomDevice device, byte command, byte[] data, int responseLength, int timeout = 5000);

        /// <summary>
        /// Sends an IIC command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="address">The device address</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendIICCommandAsync(VocomDevice device, byte address, byte[] data, int responseLength, int timeout = 5000);

        /// <summary>
        /// Sends raw data to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendRawDataAsync(VocomDevice device, byte[] data, int responseLength, int timeout = 5000);

        /// <summary>
        /// Checks if PTT application is running
        /// </summary>
        /// <returns>True if PTT is running, false otherwise</returns>
        Task<bool> IsPTTRunningAsync();

        /// <summary>
        /// Terminates the PTT application if it is running
        /// </summary>
        /// <returns>True if termination is successful, false otherwise</returns>
        Task<bool> TerminatePTTAsync();
    }
}
