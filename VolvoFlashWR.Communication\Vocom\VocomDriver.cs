using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Driver for Vocom hardware communication
    /// </summary>
    public class VocomDriver : IVocomDriver, IVocomDeviceDriver
    {
        private readonly ILoggingService _logger;
        private readonly VocomNativeInterop _nativeInterop;
        private bool _isInitialized = false;

        /// <summary>
        /// Initializes a new instance of the VocomDriver class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="configService">The configuration service</param>
        public VocomDriver(ILoggingService logger, IAppConfigurationService configService = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _nativeInterop = new VocomNativeInterop(logger, configService);
        }

        /// <summary>
        /// Gets a value indicating whether the driver is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Initializes the Vocom driver
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing Vocom driver", "VocomDriver");

                if (_isInitialized)
                {
                    _logger.LogWarning("Vocom driver already initialized", "VocomDriver");
                    return true;
                }

                bool initialized = await _nativeInterop.InitializeAsync();
                if (!initialized)
                {
                    _logger.LogError("Failed to initialize Vocom native interop", "VocomDriver");
                    return false;
                }

                _isInitialized = true;
                _logger.LogInformation("Vocom driver initialized successfully", "VocomDriver");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error initializing Vocom driver", "VocomDriver", ex);
                return false;
            }
        }

        /// <summary>
        /// Shuts down the Vocom driver
        /// </summary>
        /// <returns>True if shutdown is successful, false otherwise</returns>
        public async Task<bool> ShutdownAsync()
        {
            try
            {
                _logger.LogInformation("Shutting down Vocom driver", "VocomDriver");

                if (!_isInitialized)
                {
                    _logger.LogWarning("Vocom driver not initialized, nothing to shut down", "VocomDriver");
                    return true;
                }

                bool shutdown = await _nativeInterop.ShutdownAsync();
                if (!shutdown)
                {
                    _logger.LogError("Failed to shut down Vocom native interop", "VocomDriver");
                    return false;
                }

                _isInitialized = false;
                _logger.LogInformation("Vocom driver shut down successfully", "VocomDriver");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error shutting down Vocom driver", "VocomDriver", ex);
                return false;
            }
        }

        /// <summary>
        /// Detects available Vocom devices
        /// </summary>
        /// <returns>Array of detected Vocom devices</returns>
        public async Task<VocomDevice[]> DetectDevicesAsync()
        {
            try
            {
                _logger.LogInformation("Detecting Vocom devices", "VocomDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomDriver");
                    return Array.Empty<VocomDevice>();
                }

                VocomDevice[] devices = await _nativeInterop.DetectDevicesAsync();
                _logger.LogInformation($"Detected {devices.Length} Vocom devices", "VocomDriver");
                return devices;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error detecting Vocom devices", "VocomDriver", ex);
                return Array.Empty<VocomDevice>();
            }
        }

        /// <summary>
        /// Detects available Vocom devices (IVocomDeviceDriver implementation)
        /// </summary>
        /// <returns>List of available Vocom devices</returns>
        async Task<List<VocomDevice>> IVocomDeviceDriver.DetectDevicesAsync()
        {
            try
            {
                _logger.LogInformation("Detecting Vocom devices (IVocomDeviceDriver)", "VocomDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomDriver");
                    return new List<VocomDevice>();
                }

                VocomDevice[] devices = await _nativeInterop.DetectDevicesAsync();
                _logger.LogInformation($"Detected {devices.Length} Vocom devices", "VocomDriver");
                return devices.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError("Error detecting Vocom devices", "VocomDriver", ex);
                return new List<VocomDevice>();
            }
        }

        /// <summary>
        /// Connects to a Vocom device
        /// </summary>
        /// <param name="device">The device to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        public async Task<bool> ConnectToDeviceAsync(VocomDevice device)
        {
            try
            {
                _logger.LogInformation($"Connecting to Vocom device {device?.SerialNumber}", "VocomDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomDriver");
                    return false;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot connect to null device", "VocomDriver");
                    return false;
                }

                bool connected = await _nativeInterop.ConnectDeviceAsync(device);
                if (connected)
                {
                    _logger.LogInformation($"Connected to Vocom device {device.SerialNumber}", "VocomDriver");
                    device.ConnectionStatus = VocomConnectionStatus.Connected;
                    device.LastConnectionTime = DateTime.Now;
                }
                else
                {
                    _logger.LogError($"Failed to connect to Vocom device {device.SerialNumber}", "VocomDriver");
                }

                return connected;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error connecting to Vocom device {device?.SerialNumber}", "VocomDriver", ex);
                return false;
            }
        }

        /// <summary>
        /// Disconnects from a Vocom device
        /// </summary>
        /// <param name="device">The device to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public async Task<bool> DisconnectFromDeviceAsync(VocomDevice device)
        {
            try
            {
                _logger.LogInformation($"Disconnecting from Vocom device {device?.SerialNumber}", "VocomDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomDriver");
                    return false;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot disconnect from null device", "VocomDriver");
                    return false;
                }

                bool disconnected = await _nativeInterop.DisconnectDeviceAsync(device);
                if (disconnected)
                {
                    _logger.LogInformation($"Disconnected from Vocom device {device.SerialNumber}", "VocomDriver");
                    device.ConnectionStatus = VocomConnectionStatus.Disconnected;
                }
                else
                {
                    _logger.LogError($"Failed to disconnect from Vocom device {device.SerialNumber}", "VocomDriver");
                }

                return disconnected;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error disconnecting from Vocom device {device?.SerialNumber}", "VocomDriver", ex);
                return false;
            }
        }

        /// <summary>
        /// Sends a CAN frame to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendCANFrameAsync(VocomDevice device, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending CAN frame to Vocom device {device?.SerialNumber}", "VocomDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomDriver");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send CAN frame to null device", "VocomDriver");
                    return null;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("CAN frame data is null or empty", "VocomDriver");
                    return null;
                }

                byte[] response = await _nativeInterop.SendCANFrameAsync(device, data, responseLength, timeout);
                if (response == null || response.Length == 0)
                {
                    _logger.LogError($"Failed to send CAN frame to Vocom device {device.SerialNumber}", "VocomDriver");
                }
                else
                {
                    _logger.LogInformation($"Successfully sent CAN frame to Vocom device {device.SerialNumber}", "VocomDriver");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending CAN frame to Vocom device {device?.SerialNumber}", "VocomDriver", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends an SPI command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="command">The command byte</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendSPICommandAsync(VocomDevice device, byte command, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending SPI command to Vocom device {device?.SerialNumber}", "VocomDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomDriver");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send SPI command to null device", "VocomDriver");
                    return null;
                }

                byte[] response = await _nativeInterop.SendSPICommandAsync(device, command, data, responseLength, timeout);
                if (response == null || response.Length == 0)
                {
                    _logger.LogError($"Failed to send SPI command to Vocom device {device.SerialNumber}", "VocomDriver");
                }
                else
                {
                    _logger.LogInformation($"Successfully sent SPI command to Vocom device {device.SerialNumber}", "VocomDriver");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending SPI command to Vocom device {device?.SerialNumber}", "VocomDriver", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends an SCI command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="command">The command byte</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendSCICommandAsync(VocomDevice device, byte command, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending SCI command to Vocom device {device?.SerialNumber}", "VocomDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomDriver");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send SCI command to null device", "VocomDriver");
                    return null;
                }

                byte[] response = await _nativeInterop.SendSCICommandAsync(device, command, data, responseLength, timeout);
                if (response == null || response.Length == 0)
                {
                    _logger.LogError($"Failed to send SCI command to Vocom device {device.SerialNumber}", "VocomDriver");
                }
                else
                {
                    _logger.LogInformation($"Successfully sent SCI command to Vocom device {device.SerialNumber}", "VocomDriver");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending SCI command to Vocom device {device?.SerialNumber}", "VocomDriver", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends an IIC command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="address">The IIC device address</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendIICCommandAsync(VocomDevice device, byte address, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending IIC command to Vocom device {device?.SerialNumber}", "VocomDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomDriver");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send IIC command to null device", "VocomDriver");
                    return null;
                }

                byte[] response = await _nativeInterop.SendIICCommandAsync(device, address, data, responseLength, timeout);
                if (response == null || response.Length == 0)
                {
                    _logger.LogError($"Failed to send IIC command to Vocom device {device.SerialNumber}", "VocomDriver");
                }
                else
                {
                    _logger.LogInformation($"Successfully sent IIC command to Vocom device {device.SerialNumber}", "VocomDriver");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending IIC command to Vocom device {device?.SerialNumber}", "VocomDriver", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends raw data to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendRawDataAsync(VocomDevice device, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending raw data to Vocom device {device?.SerialNumber}", "VocomDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomDriver");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send raw data to null device", "VocomDriver");
                    return null;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("Raw data is null or empty", "VocomDriver");
                    return null;
                }

                // Determine the appropriate protocol based on the data
                byte protocolByte = data.Length > 0 ? data[0] : (byte)0;

                // Send the data using the appropriate protocol based on the first byte
                byte[] response;

                // Check the protocol identifier in the first byte
                switch (protocolByte & 0xF0)
                {
                    case 0x10: // CAN protocol
                        _logger.LogInformation("Detected CAN protocol in raw data", "VocomDriver");
                        response = await _nativeInterop.SendCANFrameAsync(device, data, responseLength, timeout);
                        break;

                    case 0x20: // SPI protocol
                        _logger.LogInformation("Detected SPI protocol in raw data", "VocomDriver");
                        byte spiCommand = data.Length > 1 ? data[1] : (byte)0;
                        byte[] spiData = data.Length > 2 ? data.Skip(2).ToArray() : Array.Empty<byte>();
                        response = await _nativeInterop.SendSPICommandAsync(device, spiCommand, spiData, responseLength, timeout);
                        break;

                    case 0x30: // SCI protocol
                        _logger.LogInformation("Detected SCI protocol in raw data", "VocomDriver");
                        byte sciCommand = data.Length > 1 ? data[1] : (byte)0;
                        byte[] sciData = data.Length > 2 ? data.Skip(2).ToArray() : Array.Empty<byte>();
                        response = await _nativeInterop.SendSCICommandAsync(device, sciCommand, sciData, responseLength, timeout);
                        break;

                    case 0x40: // IIC protocol
                        _logger.LogInformation("Detected IIC protocol in raw data", "VocomDriver");
                        byte iicAddress = data.Length > 1 ? data[1] : (byte)0;
                        byte[] iicData = data.Length > 2 ? data.Skip(2).ToArray() : Array.Empty<byte>();
                        response = await _nativeInterop.SendIICCommandAsync(device, iicAddress, iicData, responseLength, timeout);
                        break;

                    default: // Unknown protocol, use CAN as default
                        _logger.LogWarning($"Unknown protocol identifier in raw data: 0x{protocolByte:X2}, using CAN as default", "VocomDriver");
                        response = await _nativeInterop.SendCANFrameAsync(device, data, responseLength, timeout);
                        break;
                }

                if (response == null || response.Length == 0)
                {
                    _logger.LogError($"Failed to send raw data to Vocom device {device.SerialNumber}", "VocomDriver");
                }
                else
                {
                    _logger.LogInformation($"Successfully sent raw data to Vocom device {device.SerialNumber}", "VocomDriver");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending raw data to Vocom device {device?.SerialNumber}", "VocomDriver", ex);
                return null;
            }
        }



        /// <summary>
        /// Checks if PTT application is running
        /// </summary>
        /// <returns>True if PTT is running, false otherwise</returns>
        public async Task<bool> IsPTTRunningAsync()
        {
            try
            {
                _logger.LogInformation("Checking if PTT application is running", "VocomDriver");

                if (!_isInitialized)
                {
                    _logger.LogWarning("Vocom driver not initialized", "VocomDriver");
                }

                return await _nativeInterop.IsPTTRunningAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError("Error checking if PTT application is running", "VocomDriver", ex);
                return false;
            }
        }

        /// <summary>
        /// Terminates the PTT application if it is running
        /// </summary>
        /// <returns>True if termination is successful, false otherwise</returns>
        public async Task<bool> TerminatePTTAsync()
        {
            try
            {
                _logger.LogInformation("Terminating PTT application", "VocomDriver");

                if (!_isInitialized)
                {
                    _logger.LogWarning("Vocom driver not initialized", "VocomDriver");
                }

                return await _nativeInterop.TerminatePTTAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError("Error terminating PTT application", "VocomDriver", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets the native interop instance
        /// </summary>
        /// <returns>The VocomNativeInterop instance</returns>
        public VocomNativeInterop GetNativeInterop()
        {
            return _nativeInterop;
        }

        /// <summary>
        /// Updates the firmware of a Vocom device
        /// </summary>
        /// <param name="device">The device to update</param>
        /// <param name="firmwareData">The firmware data</param>
        /// <returns>True if update is successful, false otherwise</returns>
        public async Task<bool> UpdateFirmwareAsync(VocomDevice device, byte[] firmwareData)
        {
            try
            {
                _logger.LogInformation($"Updating firmware for Vocom device {device?.SerialNumber}", "VocomDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomDriver");
                    return false;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot update firmware for null device", "VocomDriver");
                    return false;
                }

                if (firmwareData == null || firmwareData.Length == 0)
                {
                    _logger.LogError("Firmware data is null or empty", "VocomDriver");
                    return false;
                }

                // In a real implementation, this would use the Vocom driver to update the firmware
                // For now, we'll just simulate a firmware update

                // Simulate firmware update process
                _logger.LogInformation($"Starting firmware update for Vocom device {device.SerialNumber}", "VocomDriver");

                // Simulate firmware update steps
                await Task.Delay(1000); // Simulate preparation
                _logger.LogInformation("Firmware update: Preparing device...", "VocomDriver");

                await Task.Delay(2000); // Simulate erasing flash
                _logger.LogInformation("Firmware update: Erasing flash memory...", "VocomDriver");

                await Task.Delay(3000); // Simulate writing firmware
                _logger.LogInformation("Firmware update: Writing new firmware...", "VocomDriver");

                await Task.Delay(1000); // Simulate verification
                _logger.LogInformation("Firmware update: Verifying firmware...", "VocomDriver");

                await Task.Delay(1000); // Simulate reset
                _logger.LogInformation("Firmware update: Resetting device...", "VocomDriver");

                _logger.LogInformation($"Firmware update completed successfully for Vocom device {device.SerialNumber}", "VocomDriver");

                // Update device firmware version
                device.FirmwareVersion = $"{DateTime.Now:yyyyMMdd}-1";

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error updating firmware for Vocom device {device?.SerialNumber}", "VocomDriver", ex);
                return false;
            }
        }
    }
}
