using System.Windows.Controls;
using VolvoFlashWR.Core.Diagnostics;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.UI.ViewModels;

namespace VolvoFlashWR.UI.Views
{
    /// <summary>
    /// Interaction logic for DiagnosticsExportView.xaml
    /// </summary>
    public partial class DiagnosticsExportView : UserControl
    {
        /// <summary>
        /// Initializes a new instance of the DiagnosticsExportView class
        /// </summary>
        public DiagnosticsExportView()
        {
            InitializeComponent();
        }

        /// <summary>
        /// Initializes a new instance of the DiagnosticsExportView class with dependencies
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="flashDiagnostics">The flash diagnostics</param>
        public DiagnosticsExportView(ILoggingService logger, FlashDiagnostics flashDiagnostics)
        {
            InitializeComponent();
            DataContext = new DiagnosticsExportViewModel(logger, flashDiagnostics);
        }
    }
}
