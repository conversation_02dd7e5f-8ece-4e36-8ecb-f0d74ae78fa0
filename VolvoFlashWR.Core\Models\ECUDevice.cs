using System;
using System.Collections.Generic;
using VolvoFlashWR.Core.Enums;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents an Electronic Control Unit (ECU) in a Volvo truck
    /// </summary>
    public class ECUDevice
    {
        /// <summary>
        /// Unique identifier for the ECU
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Name/Type of the ECU (e.g., EMS, APM)
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Manufacturer of the ECU
        /// </summary>
        public string Manufacturer { get; set; }

        /// <summary>
        /// Serial number of the ECU
        /// </summary>
        public string SerialNumber { get; set; }

        /// <summary>
        /// Hardware version of the ECU
        /// </summary>
        public string HardwareVersion { get; set; }

        /// <summary>
        /// Software version of the ECU
        /// </summary>
        public string SoftwareVersion { get; set; }

        /// <summary>
        /// Current status of the ECU connection
        /// </summary>
        public ECUConnectionStatus ConnectionStatus { get; set; }

        /// <summary>
        /// List of active faults in the ECU
        /// </summary>
        public List<ECUFault> ActiveFaults { get; set; } = new List<ECUFault>();

        /// <summary>
        /// List of inactive faults in the ECU
        /// </summary>
        public List<ECUFault> InactiveFaults { get; set; } = new List<ECUFault>();

        /// <summary>
        /// Parameters read from the ECU
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Timestamp of the last successful communication
        /// </summary>
        public DateTime LastCommunicationTime { get; set; }

        /// <summary>
        /// Indicates if the ECU supports high-speed communication
        /// </summary>
        public bool SupportsHighSpeedCommunication { get; set; }

        /// <summary>
        /// Indicates if the ECU supports low-speed communication
        /// </summary>
        public bool SupportsLowSpeedCommunication { get; set; }

        /// <summary>
        /// Current communication speed mode (High or Low)
        /// </summary>
        public CommunicationSpeedMode CurrentCommunicationSpeedMode { get; set; } = CommunicationSpeedMode.Low;

        /// <summary>
        /// Microcontroller type used in the ECU
        /// </summary>
        public string MicrocontrollerType { get; set; }

        /// <summary>
        /// EEPROM size in bytes
        /// </summary>
        public int EEPROMSize { get; set; }

        /// <summary>
        /// Flash memory size in bytes
        /// </summary>
        public int FlashSize { get; set; }

        /// <summary>
        /// RAM size in bytes
        /// </summary>
        public int RAMSize { get; set; }

        /// <summary>
        /// EEPROM sector size in bytes
        /// </summary>
        public int EEPROMSectorSize { get; set; } = 1024; // Default sector size for MC9S12XEP100

        /// <summary>
        /// Flash sector size in bytes
        /// </summary>
        public int FlashSectorSize { get; set; } = 1024; // Default sector size for MC9S12XEP100

        /// <summary>
        /// Flash phrase size in bytes (for ECC operations)
        /// </summary>
        public int FlashPhraseSize { get; set; } = 8; // Default phrase size for MC9S12XEP100

        /// <summary>
        /// Indicates if the ECU supports Error Correction Code (ECC) for Flash memory
        /// </summary>
        public bool SupportsECC { get; set; } = true; // MC9S12XEP100 supports ECC

        /// <summary>
        /// Protocol type used for communication with the ECU
        /// </summary>
        public ECUProtocolType ProtocolType { get; set; }

        /// <summary>
        /// Communication protocol used for the ECU
        /// </summary>
        public CommunicationProtocol CommunicationProtocol { get; set; }


        /// <summary>
        /// CAN ID for the ECU (if using CAN protocol)
        /// </summary>
        public string CANId { get; set; }

        /// <summary>
        /// Additional properties for the ECU
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Represents the connection status of an ECU
    /// </summary>
    public enum ECUConnectionStatus
    {
        Disconnected,
        Connecting,
        Connected,
        Error
    }

    /// <summary>
    /// Represents a fault detected in an ECU
    /// </summary>
    public class ECUFault
    {
        /// <summary>
        /// Fault code
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Description of the fault
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Severity level of the fault
        /// </summary>
        public FaultSeverity Severity { get; set; }

        /// <summary>
        /// Timestamp when the fault was detected
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Indicates if the fault is currently active
        /// </summary>
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// Represents the severity level of a fault
    /// </summary>
    public enum FaultSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// Represents the communication speed mode for an ECU
    /// </summary>
    public enum CommunicationSpeedMode
    {
        /// <summary>
        /// Low-speed communication mode
        /// </summary>
        Low,

        /// <summary>
        /// High-speed communication mode
        /// </summary>
        High,

        /// <summary>
        /// Normal-speed communication mode (medium speed)
        /// </summary>
        Normal
    }
}
