﻿Chapter 2
Port Integration Module (S12XEPIMV1)

Table 2-1. Revision History

Revision Sections
Revision Date Description of Changes

Number Affected

V01.17 02 Apr 2008  • Corrected reduced drive strength to 1/5
 • Separated PE1,0 bit descriptions from other PE GPIO

V01.18 25 Nov 2008 2.3.19/120  • Corrected alternative functions on Port K (ACC[2:0])
*******/181  • Corrected functions on PE[5] (MODB) and PE[2] (WE)

V01.19 18 Dec 2009  • Added function independency to reduced drive and wired-or bit
descriptions

 • Minor corrections

2.1 Introduction

2.1.1 Overview
The S12XE Family Port Integration Module establishes the interface between the peripheral modules
including the non-multiplexed External Bus Interface module (S12X_EBI) and the I/O pins for all ports.
It controls the electrical pin properties as well as the signal prioritization and multiplexing on shared pins.

This document covers:
• Port A and B used as address output of the S12X_EBI
• Port C and D used as data I/O of the S12X_EBI
• Port E associated with the S12X_EBI control signals and the IRQ, XIRQ interrupt inputs
• Port K associated with address output and control signals of the S12X_EBI
• Port T associated with 1 ECT module
• Port S associated with 2 SCI and 1 SPI modules
• Port M associated with 4 MSCAN and 1 SCI module
• Port P connected to the PWM and 2 SPI modules - inputs can be used as an external interrupt source
• Port H associated with 4 SCI modules - inputs can be used as an external interrupt source
• Port J associated with 1 MSCAN, 1 SCI, 2 IIC modules and chip select outputs - inputs can be used

as an external interrupt source
• Port AD0 and AD1 associated with two 16-channel ATD modules
• Port R associated with 1 standard timer (TIM) module
• Port L associated with 4 SCI modules

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 89



Chapter 2 Port Integration Module (S12XEPIMV1)

• Port F associated with IIC, SCI and chip select outputs

Most I/O pins can be configured by register bits to select data direction and drive strength, to enable and
select pull-up or pull-down devices.

NOTE
This document assumes the availability of all features (208-pin package
option). Some functions are not available on lower pin count package
options. Refer to the pin-out summary in the SOC Guide.

2.1.2 Features
The Port Integration Module includes these distinctive registers:

• Data and data direction registers for Ports A, B, C, D, E, K, T, S, M, P, H, J, AD0, AD1, R, L, and
F when used as general-purpose I/O

• Control registers to enable/disable pull-device and select pull-ups/pull-downs on Ports T, S, M, P,
H, J, R, L, and F on per-pin basis

• Control registers to enable/disable pull-up devices on Ports AD0 and AD1 on per-pin basis
• Single control register to enable/disable pull-ups on Ports A, B, C, D, E, and K on per-port basis

and on BKGD pin
• Control registers to enable/disable reduced output drive on Ports T, S, M, P, H, J, AD0, AD1, R, L,

and F on per-pin basis
• Single control register to enable/disable reduced output drive on Ports A, B, C, D, E, and K on per-

port basis
• Control registers to enable/disable open-drain (wired-or) mode on Ports S, M, and L
• Interrupt flag register for pin interrupts on Ports P, H, and J
• Control register to configure IRQ pin operation
• Free-running clock outputs

A standard port pin has the following minimum features:
• Input/output selection
• 5V output drive with two selectable drive strengths
• 5V digital and analog input
• Input with selectable pull-up or pull-down device

Optional features supported on dedicated pins:
• Open drain for wired-or connections
• Interrupt inputs with glitch filtering
• Reduced input threshold to support low voltage applications

2.2 External Signal Description
This section lists and describes the signals that do connect off-chip.

MC9S12XE-Family Reference Manual  Rev. 1.25

90 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-2 shows all the pins and their functions that are controlled by the Port Integration Module. Refer
to the SOC Guide for the availability of the individual pins in the different package options.

NOTE
If there is more than one function associated with a pin, the priority is
indicated by the position in the table from top (highest priority) to bottom
(lowest priority).

Table 2-2. Pin Functions and Priorities

Pin Function Pin Function
Port  Pin Name

& Priority(1) I/O Description
after Reset

- BKGD MODC (2) I MODC input during RESET BKGD
BKGD I/O S12X_BDM communication pin

A PA[7:0] ADDR[15:8] O High-order external bus address output Mode
mux (multiplexed with IVIS data) dependent (4)

IVD[15:8] (3)

GPIO I/O General-purpose I/O
B PB[7:1] ADDR[7:1] O Low-order external bus address output Mode

mux (multiplexed with IVIS data) dependent 4

IVD[7:1] 3

GPIO I/O General-purpose I/O
PB[0] ADDR[0] O Low-order external bus address output

mux (multiplexed with IVIS data)
IVD0 3

UDS O Upper data strobe
GPIO I/O General-purpose I/O

C PC[7:0] DATA[15:8] I/O High-order bidirectional data input/output Mode
Configurable for reduced input threshold dependent 4

GPIO I/O General-purpose I/O
D PD[7:0] DATA[7:0] I/O Low-order bidirectional data input/output Mode

Configurable for reduced input threshold dependent 4

GPIO I/O General-purpose I/O

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 91



Chapter 2 Port Integration Module (S12XEPIMV1)

Pin Function Pin Function
Port  Pin Name

& Priority(1) I/O Description
after Reset

E PE[7] XCLKS 2 I External clock selection input during RESET Mode
dependent 4

ECLKX2 I Free-running clock output at Core Clock rate (ECLK x 2)
GPIO I/O General-purpose I/O

PE[6] MODB 2 I MODB input during RESET
TAGHI I Instruction tagging low pin

Configurable for reduced input threshold
GPIO I/O General-purpose I/O

PE[5] MODA 2 I MODA input during RESET
RE O Read enable signal

TAGLO I Instruction tagging low pin
Configurable for reduced input threshold

GPIO I/O General-purpose I/O
PE[4] ECLK O Free-running clock output at the Bus Clock rate or programmable

divided in normal modes
GPIO I/O General-purpose I/O

PE[3] EROMCTL 2 I EROMON bit control input during RESET
LSTRB O Low strobe bar output

LDS O Lower data strobe
GPIO I/O General-purpose I/O

PE[2] RW O Read/write output for external bus
WE O Write enable signal

GPIO I/O General-purpose I/O
PE[1] IRQ I Maskable level- or falling edge-sensitive interrupt input

GPI I General-purpose input
PE[0] XIRQ I Non-maskable level-sensitive interrupt input

GPI I General-purpose input
K PK[7] ROMCTL 2 I ROMON bit control input during RESET Mode

dependent 3
EWAIT I External Wait signal

Configurable for reduced input threshold
GPIO I/O General-purpose I/O

PK[6:4] ADDR[22:20] O Extended external bus address output
mux (multiplexed with access master output)

ACC[2:0] 3

GPIO I/O General-purpose I/O
PK[3:0] ADDR[19:16] O Extended external bus address output

mux (multiplexed with instruction pipe status bits)
IQSTAT[3:0] 3

GPIO I/O General-purpose I/O

MC9S12XE-Family Reference Manual  Rev. 1.25

92 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Pin Function Pin Function
Port  Pin Name

& Priority(1) I/O Description
after Reset

T PT[7] IOC[7] I/O Enhanced Capture Timer Channels 7 input/output GPIO
GPIO I/O General-purpose I/O
IOC[5] I/O Enhanced Capture Timer Channel 5 input/output

PT[5] VREG_API O VREG Autonomous Periodical Interrupt output
GPIO I/O General-purpose I/O

PT[4:0] IOC[4:0] I/O Enhanced Capture Timer Channels 4 - 0 input/output
GPIO I/O General-purpose I/O

S PS7 SS0 I/O Serial Peripheral Interface 0 slave select output in master mode, GPIO
input in slave mode or master mode.

GPIO I/O General-purpose I/O
PS6 SCK0 I/O Serial Peripheral Interface 0 serial clock pin

GPIO I/O General-purpose I/O
PS5 MOSI0 I/O Serial Peripheral Interface 0 master out/slave in pin

GPIO I/O General-purpose I/O
PS4 MISO0 I/O Serial Peripheral Interface 0 master in/slave out pin

GPIO I/O General-purpose I/O
PS3 TXD1 O Serial Communication Interface 1 transmit pin

GPIO I/O General-purpose I/O
PS2 RXD1 I Serial Communication Interface 1 receive pin

GPIO I/O General-purpose I/O
PS1 TXD0 O Serial Communication Interface 0 transmit pin

GPIO I/O General-purpose I/O
PS0 RXD0 I Serial Communication Interface 0 receive pin

GPIO I/O General-purpose I/O

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 93



Chapter 2 Port Integration Module (S12XEPIMV1)

Pin Function Pin Function
Port  Pin Name

& Priority(1) I/O Description
after Reset

M PM7 TXCAN3 O MSCAN3 transmit pin GPIO
(TXCAN4) O MSCAN4 transmit pin

TXD3 O Serial Communication Interface 3 transmit pin
GPIO I/O General-purpose I/O

PM6 RXCAN3 I MSCAN3 receive pin
(RXCAN4) I MSCAN4 receive pin

RXD3 I Serial Communication Interface 3 receive pin
GPIO I/O General-purpose I/O

PM5 TXCAN2 O MSCAN2 transmit pin
(TXCAN0) O MSCAN0 transmit pin
(TXCAN4) O MSCAN4 transmit pin

(SCK0) I/O Serial Peripheral Interface 0 serial clock pin
If CAN0 is routed to PM[3:2] the SPI0 can still be used in
bidirectional master mode.

GPIO I/O General-purpose I/O
PM4 RXCAN2 I MSCAN2 receive pin

(RXCAN0) I MSCAN0 receive pin
(RXCAN4) I MSCAN4 receive pin
(MOSI0) I/O Serial Peripheral Interface 0 master out/slave in pin

If CAN0 is routed to PM[3:2] the SPI0 can still be used in
bidirectional master mode.

GPIO I/O General-purpose I/O
PM3 TXCAN1 O MSCAN1 transmit pin

(TXCAN0) O MSCAN0 transmit pin
(SS0) I/O Serial Peripheral Interface 0 slave select output in master mode,

input for slave mode or master mode.
GPIO I/O General-purpose I/O

PM2 RXCAN1 I MSCAN1 receive pin
(RXCAN0) I MSCAN0 receive pin
(MISO0) I/O Serial Peripheral Interface 0 master in/slave out pin

GPIO I/O General-purpose I/O
PM1 TXCAN0 O MSCAN0 transmit pin

GPIO I/O General-purpose I/O
PM0 RXCAN0 I MSCAN0 receive pin

GPIO I/O General-purpose I/O

MC9S12XE-Family Reference Manual  Rev. 1.25

94 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Pin Function Pin Function
Port  Pin Name I/O Description

& Priority(1) after Reset

P PP7 PWM7 I/O Pulse Width Modulator input/output channel 7 GPIO
SCK2 I/O Serial Peripheral Interface 2 serial clock pin

(TIMIOC7) I/O Timer Channel 7 input/output
GPIO/KWP7 I/O General-purpose I/O with interrupt

PP6 PWM6 O Pulse Width Modulator output channel 6
SS2 I/O Serial Peripheral Interface 2 slave select output in master mode,

input for slave mode or master mode.
(TIMIOC6) I/O Timer Channel 6 input/output

GPIO/KWP6 I/O General-purpose I/O with interrupt
PP5 PWM5 O Pulse Width Modulator output channel 5

MOSI2 I/O Serial Peripheral Interface 2 master out/slave in pin
(TIMIOC5) I/O Timer Channel 5 input/output

GPIO/KWP5 I/O General-purpose I/O with interrupt
PP4 PWM4 O Pulse Width Modulator output channel 4

MISO2 I/O Serial Peripheral Interface 2 master in/slave out pin
(TIMIOC4) I/O Timer Channel 4 input/output

GPIO/KWP4 I/O General-purpose I/O with interrupt
PP3 PWM3 O Pulse Width Modulator output channel 3

SS1 I/O Serial Peripheral Interface 1 slave select output in master mode,
input for slave mode or master mode.

(TIMIOC3) I/O Timer Channel 3 input/output
GPIO/KWP3 I/O General-purpose I/O with interrupt

PP2 PWM2 O Pulse Width Modulator output channel 2
SCK1 I/O Serial Peripheral Interface 1 serial clock pin

(TIMIOC2) I/O Timer Channel 2 input/output
GPIO/KWP2 I/O General-purpose I/O with interrupt

PP1 PWM1 O Pulse Width Modulator output channel 1
MOSI1 I/O Serial Peripheral Interface 1 master out/slave in pin

(TIMIOC1) I/O Timer Channel 1 input/output
GPIO/KWP1 I/O General-purpose I/O with interrupt

PP0 PWM0 O Pulse Width Modulator output channel 0
MISO1 I/O Serial Peripheral Interface 1 master in/slave out pin

(TIMIOC0) I/O Timer Channel 0 input/output
GPIO/KWP0 I/O General-purpose I/O with interrupt

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 95



Chapter 2 Port Integration Module (S12XEPIMV1)

Pin Function Pin Function
Port  Pin Name

& Priority(1) I/O Description
after Reset

H PH7 (SS2) I/O Serial Peripheral Interface 2 slave select output in master mode, GPIO
input for slave mode or master mode

TXD5 O Serial Communication Interface 5 transmit pin
GPIO/KWH7 I/O General-purpose I/O with interrupt

PH6 (SCK2) I/O Serial Peripheral Interface 2 serial clock pin
RXD5 I Serial Communication Interface 5 receive pin

GPIO/KWH6 I/O General-purpose I/O with interrupt
PH5 (MOSI2) I/O Serial Peripheral Interface 2 master out/slave in pin

TXD4 O Serial Communication Interface 4 transmit pin
GPIO/KWH5 I/O General-purpose I/O with interrupt

PH4 (MISO2) I/O Serial Peripheral Interface 2 master in/slave out pin
RXD4 I Serial Communication Interface 4 receive pin

GPIO/KWH4 I/O General-purpose I/O with interrupt
PH3 (SS1) I/O Serial Peripheral Interface 1 slave select output in master mode,

input for slave mode or master mode.
TXD7 O Serial Communication Interface 7 transmit pin

GPIO/KWH3 I/O General-purpose I/O with interrupt
PH2 (SCK1) I/O Serial Peripheral Interface 1 serial clock pin

RXD7 I Serial Communication Interface 7 receive pin
GPIO/KWH2 I/O General-purpose I/O with interrupt

PH1 (MOSI1) I/O Serial Peripheral Interface 1 master out/slave in pin
TXD6 O Serial Communication Interface 6 transmit pin

GPIO/KWH1 I/O General-purpose I/O with interrupt
PH0 (MISO1) I/O Serial Peripheral Interface 1 master in/slave out pin

TXD6 O Serial Communication Interface 6 transmit pin
GPIO/KWH0 I/O General-purpose I/O with interrupt

MC9S12XE-Family Reference Manual  Rev. 1.25

96 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Pin Function Pin Function
Port  Pin Name

& Priority(1) I/O Description
after Reset

J PJ7 TXCAN4 O MSCAN4 transmit pin GPIO
SCL0 O Inter Integrated Circuit 0 serial clock line

(TXCAN0) O MSCAN0 transmit pin
GPIO/KWJ7 I/O General-purpose I/O with interrupt

PJ6 RXCAN4 I MSCAN4 receive pin
SDA0 I/O Inter Integrated Circuit 0 serial data line

(RXCAN0) I MSCAN0 receive pin
GPIO/KWJ6 I/O General-purpose I/O with interrupt

PJ5 SCL1 O Inter Integrated Circuit 1 serial clock line
CS2 O Chip select 2

GPIO/KWJ5 I/O General-purpose I/O with interrupt
PJ4 SDA1 I/O Inter Integrated Circuit 1 serial data line

CS0 O Chip select 0
GPIO/KWJ4 I/O General-purpose I/O with interrupt

PJ3 GPIO/KWJ3 I/O General-purpose I/O with interrupt
PJ2 CS1 O Chip select 1

GPIO/KWJ2 I/O General-purpose I/O with interrupt
PJ1 TXD2 O Serial Communication Interface 2 transmit pin

GPIO/KWJ1 I/O General-purpose I/O with interrupt
PJ0 RXD2 I Serial Communication Interface 2 receive pin

CS3 O Chip select 3
GPIO/KWJ0 I/O General-purpose I/O with interrupt

AD0 PAD[15:0] GPIO I/O General-purpose I/O GPIO
AN[15:0] I ATD0 analog inputs

AD1 PAD[31:16] GPIO I/O General-purpose I/O GPIO
AN[15:0] I ATD1 analog inputs

R PR[7:0] TIMIOC[7:0] I/O Timer Channels 7- 0 input/output GPIO
GPIO I/O General-purpose I/O

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 97



Chapter 2 Port Integration Module (S12XEPIMV1)

Pin Function Pin Function
Port  Pin Name O Description

& Priority(1) I/
after Reset

L PL7 (TXD7) O Serial Communication Interface 7 transmit pin GPIO
GPIO I/O General-purpose I/O

PL6 (RXD7) I Serial Communication Interface 7 receive pin
GPIO I/O General-purpose I/O

PL5 (TXD6) O Serial Communication Interface 6 transmit pin
GPIO I/O General-purpose I/O

PL4 (RXD6) I Serial Communication Interface 6 receive pin
GPIO I/O General-purpose I/O

PL3 (TXD5) O Serial Communication Interface 5 transmit pin
GPIO I/O General-purpose I/O

PL2 (RXD5) I Serial Communication Interface 5 receive pin
GPIO I/O General-purpose I/O

PL1 (TXD4) O Serial Communication Interface 4 transmit pin
GPIO I/O General-purpose I/O

PL0 (RXD4) I Serial Communication Interface 4 receive pin
GPIO I/O General-purpose I/O

F PF7 (TXD3) O Serial Communication Interface 3 transmit pin GPIO
GPIO I/O General-purpose I/O

PF6 (RXD3) I Serial Communication Interface 3 receive pin
GPIO I/O General-purpose I/O

PF5 (SCL0) O Inter Integrated Circuit 0 serial clock line
GPIO I/O General-purpose I/O

PF4 (SDA0) I/O Inter Integrated Circuit 0 serial data line
GPIO I/O General-purpose I/O

PF3 (CS3) O Chip select 3
GPIO I/O General-purpose I/O

PF2 (CS2) O Chip select 2
GPIO I/O General-purpose I/O

PF1 (CS1) O Chip select 1
GPIO I/O General-purpose I/O

PF0 (CS0) O Chip select 0
GPIO I/O General-purpose I/O

1. Signals in brackets denote alternative module routing pins.
2. Function active when RESET asserted.
3. Only available in emulation modes or in Special Test Mode with IVIS on.
4. Refer to S12X_EBI section.

2.3 Memory Map and Register Definition
This section provides a detailed description of all Port Integration Module registers.

MC9S12XE-Family Reference Manual  Rev. 1.25

98 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.1 Memory Map

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0000 R
PORTA PA7 PA6 PA5 PA4 PA3 PA2 PA1 PA0

W

0x0001 R
PORTB PB7 PB6 PB5 PB4 PB3 PB2 PB1 PB0

W

0x0002 R
DDRA DDRA7 DDRA6 DDRA5 DDRA4 DDRA3 DDRA2 DDRA1 DDRA0

W

0x0003 R
DDRB DDRB7 DDRB6 DDRB5 DDRB4 DDRB3 DDRB2 DDRB1 DDRB0

W

0x0004 R
PORTC PC7 PC6 PC5 PC4 PC3 PC2 PC1 PC0

W

0x0005 R
PORTD PD7 PD6 PD5 PD4 PD3 PD2 PD1 PD0

W

0x0006 R
DDRC DDRC7 DDRC6 DDRC5 DDRC4 DDRC3 DDRC2 DDRC1 DDRC0

W

0x0007 R
DDRD DDRD7 DDRD6 DDRD5 DDRD4 DDRD3 DDRD2 DDRD1 DDRD0

W

0x0008 R PE1 PE0
PORTE PE7 PE6 PE5 PE4 PE3 PE2

W

0x0009 R 0 0
DDRE DDRE7 DDRE6 DDRE5 DDRE4 DDRE3 DDRE2

W

0x000A R
0x000B W
Non-PIM Non-PIM Address Range
Address
Range

0x000C R 0
PUCR PUPKE BKPUE PUPEE PUPDE PUPCE PUPBE PUPAE

W

0x000D R 0 0
RDRIV RDPK RDPE RDPD RDPC RDPB RDPA

W

= Unimplemented or Reserved

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 99



Chapter 2 Port Integration Module (S12XEPIMV1)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x000E– R
0x001B W
Non-PIM Non-PIM Address Range
Address
Range

0x001C R
ECLKCTL NECLK NCLKX2 DIV16 EDIV4 EDIV3 EDIV2 EDIV1 EDIV0

W

0x001D R 0 0 0 0 0 0 0 0
Reserved W

0x001E R 0 0 0 0 0 0
IRQCR IRQE IRQEN

W

0x001F R 0 0 0 0 0 0 0 0
W

Reserved

0x0020– R
0x0031 W

Non-PIM Non-PIM Address Range
Address
Range

0x0032 R
PORTK PK7 PK6 PK5 PK4 PK3 PK2 PK1 PK0

W

0x0033 R
DDRK DDRK7 DDRK6 DDRK5 DDRK4 DDRK3 DDRK2 DDRK1 DDRK0

W

0x0034– R
0x023F W

Non-PIM Non-PIM Address Range
Address
Range

0x0240 R
PTT PTT7 PTT6 PTT5 PTT4 PTT3 PTT2 PTT1 PTT0

W

0x0241 R PTIT7 PTIT6 PTIT5 PTIT4 PTIT3 PTIT2 PTIT1 PTIT0
PTIT W

0x0242 R
DDRT DDRT7 DDRT6 DDRT5 DDRT4 DDRT3 DDRT2 DDRT1 DDRT0

W

0x0243 R
RDRT RDRT7 RDRT6 RDRT5 RDRT4 RDRT3 RDRT2 RDRT1 RDRT0

W

= Unimplemented or Reserved

MC9S12XE-Family Reference Manual  Rev. 1.25

100 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0244 R
PERT PERT7 PERT6 PERT5 PERT4 PERT3 PERT2 PERT1 PERT0

W

0x0245 R
PPST PPST7 PPST6 PPST5 PPST4 PPST3 PPST2 PPST1 PPST0

W

0x0246 R 0 0 0 0 0 0 0 0
Reserved W

0x0247 R 0 0 0 0 0 0 0 0
Reserved W

0x0248 R
PTS PTS7 PTS6 PTS5 PTS4 PTS3 PTS2 PTS1 PTS0

W

0x0249 R PTIS7 PTIS6 PTIS5 PTIS4 PTIS3 PTIS2 PTIS1 PTIS0
PTIS W

0x024A R
DDRS DDRS7 DDRS6 DDRS5 DDRS4 DDRS3 DDRS2 DDRS1 DDRS0

W

0x024B R
RDRS RDRS7 RDRS6 RDRS5 RDRS4 RDRS3 RDRS2 RDRS1 RDRS0

W

0x024C R
PERS PERS7 PERS6 PERS5 PERS4 PERS3 PERS2 PERS1 PERS0

W

0x024D R
PPSS PPSS7 PPSS6 PPSS5 PPSS4 PPSS3 PPSS2 PPSS1 PPSS0

W

0x024E R
WOMS WOMS7 WOMS6 WOMS5 WOMS4 WOMS3 WOMS2 WOMS1 WOMS0

W

0x024F R 0 0 0 0 0 0 0 0
Reserved W

0x0250 R
PTM PTM7 PTM6 PTM5 PTM4 PTM3 PTM2 PTM1 PTM0

W

0x0251 R PTIM7 PTIM6 PTIM5 PTIM4 PTIM3 PTIM2 PTIM1 PTIM0
PTIM W

0x0252 R
DDRM DDRM7 DDRM6 DDRM5 DDRM4 DDRM3 DDRM2 DDRM1 DDRM0

W

= Unimplemented or Reserved

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 101



Chapter 2 Port Integration Module (S12XEPIMV1)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0253 R
RDRM RDRM7 RDRM6 RDRM5 RDRM4 RDRM3 RDRM2 RDRM1 RDRM0

W

0x0254 R
PERM PERM7 PERM6 PERM5 PERM4 PERM3 PERM2 PERM1 PERM0

W

0x0255 R
PPSM PPSM7 PPSM6 PPSM5 PPSM4 PPSM3 PPSM2 PPSM1 PPSM0

W

0x0256 R
WOMM WOMM7 WOMM6 WOMM5 WOMM4 WOMM3 WOMM2 WOMM1 WOMM0

W

0x0257 R 0
MODRR MODRR6 MODRR5 MODRR4 MODRR3 MODRR2 MODRR1 MODRR0

W

0x0258 R
PTP PTP7 PTP6 PTP5 PTP4 PTP3 PTP2 PTP1 PTP0

W

0x0259 R PTIP7 PTIP6 PTIP5 PTIP4 PTIP3 PTIP2 PTIP1 PTIP0
PTIP W

0x025A R
DDRP DDRP7 DDRP6 DDRP5 DDRP4 DDRP3 DDRP2 DDRP1 DDRP0

W

0x025B R
RDRP RDRP7 RDRP6 RDRP5 RDRP4 RDRP3 RDRP2 RDRP1 RDRP0

W

0x025C R
PERP PERP7 PERP6 PERP5 PERP4 PERP3 PERP2 PERP1 PERP0

W

0x025D R
PPSP PPSP7 PPSP6 PPSP5 PPSP4 PPSP3 PPSP2 PPSP1 PPSP0

W

0x025E R
PIEP PIEP7 PIEP6 PIEP5 PIEP4 PIEP3 PIEP2 PIEP1 PIEP0

W

0x025F R
PIFP PIFP7 PIFP6 PIFP5 PIFP4 PIFP3 PIFP2 PIFP1 PIFP0

W

0x0260 R
PTH PTH7 PTH6 PTH5 PTH4 PTH3 PTH2 PTH1 PTH0

W

0x0261 R PTIH7 PTIH6 PTIH5 PTIH4 PTIH3 PTIH2 PTIH1 PTIH0
PTIH W

0x0262 R
DDRH DDRH7 DDRH6 DDRH5 DDRH4 DDRH3 DDRH2 DDRH1 DDRH0

W
= Unimplemented or Reserved

MC9S12XE-Family Reference Manual  Rev. 1.25

102 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0263 R
RDRH RDRH7 RDRH6 RDRH5 RDRH4 RDRH3 RDRH2 RDRH1 RDRH0

W

0x0264 R
PERH PERH7 PERH6 PERH5 PERH4 PERH3 PERH2 PERH1 PERH0

W

0x0265 R
PPSH PPSH7 PPSH6 PPSH5 PPSH4 PPSH3 PPSH2 PPSH1 PPSH0

W

0x0266 R
PIEH PIEH7 PIEH6 PIEH5 PIEH4 PIEH3 PIEH2 PIEH1 PIEH0

W

0x0267 R
PIFH PIFH7 PIFH6 PIFH5 PIFH4 PIFH3 PIFH2 PIFH1 PIFH0

W

0x0268 R
PTJ PTJ7 PTJ6 PTJ5 PTJ4 PTJ3 PTJ2 PTJ1 PTJ0

W

0x0269 R PTIJ7 PTIJ6 PTIJ5 PTIJ4 PTIJ3 PTIJ2 PTIJ1 PTIJ0
PTIJ W

0x026A R
DDRJ DDRJ7 DDRJ6 DDRJ5 DDRJ4 DDRJ3 DDRJ2 DDRJ1 DDRJ0

W

0x026B R
RDRJ RDRJ7 RDRJ6 RDRJ5 RDRJ4 RDRJ3 RDRJ2 RDRJ1 RDRJ0

W

0x026C R
PERJ PERJ7 PERJ6 PERJ5 PERJ4 PERJ3 PERJ2 PERJ1 PERJ0

W

0x026D R
PPSJ PPSJ7 PPSJ6 PPSJ5 PPSJ4 PPSJ3 PPSJ2 PPSJ1 PPSJ0

W

0x026E R
PIEJ PIEJ7 PIEJ6 PIEJ5 PIEJ4 PIEJ3 PIEJ2 PIEJ1 PIEJ0

W

0x026F R
PIFJ PIFJ7 PIFJ6 PIFJ5 PIFJ4 PIFJ3 PIFJ2 PIFJ1 PIFJ0

W

0x0270 R
PT0AD0 PT0AD07 PT0AD06 PT0AD05 PT0AD04 PT0AD03 PT0AD02 PT0AD01 PT0AD00

W

0x0271 R
PT1AD0 PT1AD07 PT1AD06 PT1AD05 PT1AD04 PT1AD03 PT1AD02 PT1AD01 PT1AD00

W

= Unimplemented or Reserved

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 103



Chapter 2 Port Integration Module (S12XEPIMV1)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0272 R
DDR0AD0 DDR0AD07 DDR0AD06 DDR0AD05 DDR0AD04 DDR0AD03 DDR0AD02 DDR0AD01 DDR0AD00

W

0x0273 R
DDR1AD0 DDR1AD07 DDR1AD06 DDR1AD05 DDR1AD04 DDR1AD03 DDR1AD02 DDR1AD01 DDR1AD00

W

0x0274 R
RDR0AD0 RDR0AD07 RDR0AD06 RDR0AD05 RDR0AD04 RDR0AD03 RDR0AD02 RDR0AD01 RDR0AD00

W

0x0275 R
RDR1AD0 RDR1AD07 RDR1AD06 RDR1AD05 RDR1AD04 RDR1AD03 RDR1AD02 RDR1AD01 RDR1AD00

W

0x0276 R
PER0AD0 PER0AD07 PER0AD06 PER0AD05 PER0AD04 PER0AD03 PER0AD02 PER0AD01 PER0AD00

W

0x0277 R
PER1AD0 PER1AD07 PER1AD06 PER1AD05 PER1AD04 PER1AD03 PER1AD02 PER1AD01 PER1AD00

W

0x0278 R
PT0AD1 PT0AD17 PT0AD16 PT0AD15 PT0AD14 PT0AD13 PT0AD12 PT0AD11 PT0AD10

W

0x0279 R
PT1AD1 PT1AD17 PT1AD16 PT1AD15 PT1AD14 PT1AD13 PT1AD12 PT1AD11 PT1AD10

W

0x027A R
DDR0AD1 DDR0AD17 DDR0AD16 DDR0AD15 DDR0AD14 DDR0AD13 DDR0AD12 DDR0AD11 DDR0AD10

W

0x027B R
DDR1AD1 DDR1AD17 DDR1AD16 DDR1AD15 DDR1AD14 DDR1AD13 DDR1AD12 DDR1AD11 DDR1AD10

W

0x027C R
RDR0AD1 RDR0AD17 RDR0AD16 RDR0AD15 RDR0AD14 RDR0AD13 RDR0AD12 RDR0AD11 RDR0AD10

W

0x027D R
RDR1AD1 RDR1AD17 RDR1AD16 RDR1AD15 RDR1AD14 RDR1AD13 RDR1AD12 RDR1AD11 RDR1AD10

W

0x027E R
PER0AD1 PER0AD17 PER0AD16 PER0AD15 PER0AD14 PER0AD13 PER0AD12 PER0AD1‘ PER0AD10

W

0x027F R
PER1AD1 PER1AD17 PER1AD16 PER1AD15 PER1AD14 PER1AD13 PER1AD12 PER1AD11 PER1AD10

W

0x0280– R
0x0267 W

Non-PIM Non-PIM Address Range
Address
Range

= Unimplemented or Reserved

MC9S12XE-Family Reference Manual  Rev. 1.25

104 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0368 R
PTR PTR7 PTR6 PTR5 PTR4 PTR3 PTR2 PTR1 PTR0

W

0x0369 R PTIR7 PTIR6 PTIR5 PTIR4 PTIR3 PTIR2 PTIR1 PTIR0
PTIR W

0x036A R
DDRR DDRR7 DDRR6 DDRR5 DDRR4 DDRR3 DDRR2 DDRR1 DDRR0

W

0x036B R
RDRR RDRR7 RDRR6 RDRR5 RDRR4 RDRR3 RDRR2 RDRR1 RDRR0

W

0x036C R
PERR PERR7 PERR6 PERR5 PERR4 PERR3 PERR2 PERR1 PERR0

W

0x036D R
PPSR PPSR7 PPSR6 PPSR5 PPSR4 PPSR3 PPSR2 PPSR1 PPSR0

W

0x036E R 0 0 0 0 0 0 0 0
Reserved W

0x036F R
PTRRR PTRRR7 PTRRR6 PTRRR5 PTRRR4 PTRRR3 PTRRR2 PTRRR1 PTRRR0

W

0x0370 R
PTL PTL7 PTL6 PTL5 PTL4 PTL3 PTL2 PTL1 PTL0

W

0x0371 R PTIL7 PTIL6 PTIL5 PTIL4 PTIL3 PTIL2 PTIL1 PTIL0
PTIL W

0x0372 R
DDRL DDRL7 DDRL6 DDRL5 DDRL4 DDRL3 DDRL2 DDRL1 DDRL0

W

0x0373 R
RDRL RDRL7 RDRL6 RDRL5 RDRL4 RDRL3 RDRL2 RDRL1 RDRL0

W

0x0374 R
PERL PERL7 PERL6 PERL5 PERL4 PERL3 PERL2 PERL1 PERL0

W

0x0375 R
PPSL PPSL7 PPSL6 PPSL5 PPSL4 PPSL3 PPSL2 PPSL1 PPSL0

W

0x0376 R
WOML WOML7 WOML6 WOML5 WOML4 WOML3 WOML2 WOML1 WOML0

W

0x0377 R 0 0 0 0
PTLRR PTLRR7 PTLRR6 PTLRR5 PTLRR4

W
= Unimplemented or Reserved

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 105



Chapter 2 Port Integration Module (S12XEPIMV1)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0378 R
PTF PTF7 PTF6 PTF5 PTF4 PTF3 PTF2 PTF1 PTF0

W

0x0379 R PTIF7 PTIF6 PTIF5 PTIF4 PTIF3 PTIF2 PTIF1 PTIF0
PTIF W

0x037A R
DDRF DDRF7 DDRF6 DDRF5 DDRF4 DDRF3 DDRF2 DDRF1 DDRF0

W

0x037B R
RDRF RDRF7 RDRF6 RDRF5 RDRF4 RDRF3 RDRF2 RDRF1 RDRF0

W

0x037C R
PERF PERF7 PERF6 PERF5 PERF4 PERF3 PERF2 PERF1 PERF0

W

0x037D R
PPSF PPSF7 PPSF6 PPSF5 PPSF4 PPSF3 PPSF2 PPSF1 PPSF0

W

0x037E R 0 0 0 0 0 0 0 0
Reserved W

0x037F R 0 0
PTFRR PTFRR5 PTFRR4 PTFRR3 PTFRR2 PTFRR1 PTFRR0

W

= Unimplemented or Reserved

2.3.2 Register Descriptions
The following table summarizes the effect of the various configuration bits, i.e. data direction (DDR),
output level (IO), reduced drive (RDR), pull enable (PE), pull select (PS) on the pin function and pull
device activity.

The configuration bit PS is used for two purposes:
1. Configure the sensitive interrupt edge (rising or falling), if interrupt is enabled.
2. Select either a pull-up or pull-down device if PE is active.

MC9S12XE-Family Reference Manual  Rev. 1.25

106 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-3. Pin Configuration Summary

DDR IO RDR PE PS(1) IE(2) Function Pull Device Interrupt

0 x x 0 x 0 Input Disabled Disabled

0 x x 1 0 0 Input Pull Up Disabled

0 x x 1 1 0 Input Pull Down Disabled

0 x x 0 0 1 Input Disabled Falling edge

0 x x 0 1 1 Input Disabled Rising edge

0 x x 1 0 1 Input Pull Up Falling edge

0 x x 1 1 1 Input Pull Down Rising edge

1 0 0 x x 0 Output, full drive to 0 Disabled Disabled

1 1 0 x x 0 Output, full drive to 1 Disabled Disabled

1 0 1 x x 0 Output, reduced drive to 0 Disabled Disabled

1 1 1 x x 0 Output, reduced drive to 1 Disabled Disabled

1 0 0 x 0 1 Output, full drive to 0 Disabled Falling edge

1 1 0 x 1 1 Output, full drive to 1 Disabled Rising edge

1 0 1 x 0 1 Output, reduced drive to 0 Disabled Falling edge

1 1 1 x 1 1 Output, reduced drive to 1 Disabled Rising edge
1. Always “0” on Port A, B, C, D, E, K, AD0, and AD1.
2. Applicable only on Port P, H, and J.

NOTE
All register bits in this module are completely synchronous to internal
clocks during a register read.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 107



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.3 Port A Data Register (PORTA)

 Address 0x0000 (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PA7 PA6 PA5 PA4 PA3 PA2 PA1 PA0

W

Altern. ADDR15 ADDR14 ADDR13 ADDR12 ADDR11 ADDR10 ADDR9 ADDR8
Function mux mux mux mux mux mux mux mux

IVD15 IVD14 IVD13 IVD12 IVD11 IVD10 IVD9 IVD8

Reset 0 0 0 0 0 0 0 0

Figure 2-1. Port A Data Register (PORTA)
1. Read: Anytime. In emulation modes, read operations will return the data from the external bus, in all other modes the data source

is depending on the data direction value.
Write: Anytime. In emulation modes, write operations will also be directed to the external bus.

Table 2-4. PORTA Register Field Descriptions

Field Description

7-0 Port A general purpose input/output data—Data Register
PA Port A pins 7 through 0 are associated with address outputs ADDR[15:8] respectively in expanded modes. In

emulation modes the address is multiplexed with IVD[15:8].
When not used with the alternative function, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

2.3.4 Port B Data Register (PORTB)

 Address 0x0001 (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PB7 PB6 PB5 PB4 PB3 PB2 PB1 PB0

W

Altern. ADDR0
Function ADDR7 ADDR6 ADDR5 ADDR4 ADDR3 ADDR2 ADDR1 mux

mux mux mux mux mux mux mux IVD0
IVD7 IVD6 IVD5 IVD4 IVD3 IVD2 IVD1 or

UDS

Reset 0 0 0 0 0 0 0 0

Figure 2-2. Port B Data Register (PORTB)
1. Read: Anytime. In emulation modes, read operations will return the data from the external bus, in all other modes the data source

is depending on the data direction value.
Write: Anytime. In emulation modes, write operations will also be directed to the external bus.

MC9S12XE-Family Reference Manual  Rev. 1.25

108 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-5. PORTB Register Field Descriptions

Field Description

7-0 Port B general purpose input/output data—Data Register
PB Port B pins 7 through 0 are associated with address outputs ADDR[7:0] respectively in expanded modes. In

emulation modes the address is multiplexed with IVD[7:0]. In normal expanded mode pin 0 is related to the UDS
input.
When not used with the alternative function, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

2.3.5 Port A Data Direction Register (DDRA)

 Address 0x0002 (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDRA7 DDRA6 DDRA5 DDRA4 DDRA3 DDRA2 DDRA1 DDRA0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-3. Port A Data Direction Register (DDRA)
1. Read: Anytime. In emulation modes, read operations will return the data from the external bus, in all other modes the data source

is depending on the data direction value.
Write: Anytime. In emulation modes, write operations will also be directed to the external bus.

Table 2-6. DDRA Register Field Descriptions

Field Description

7-0 Port A Data Direction—
DDRA This register controls the data direction of pins 7 through 0.

The external bus function forces the I/O state to be outputs for all associated pins. In this case the data direction bits
will not change.
When operating a pin as a general purpose I/O, the associated data direction bit determines whether it is an input
or output.
1 Associated pin is configured as output.
0 Associated pin is configured as high-impedance input.

2.3.6 Port B Data Direction Register (DDRB)

 Address 0x0003 (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDRB7 DDRB6 DDRB5 DDRB4 DDRB3 DDRB2 DDRB1 DDRB0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-4. Port B Data Direction Register (DDRB)
1. Read: Anytime. In emulation modes, read operations will return the data from the external bus, in all other modes the data source

is depending on the data direction value.
Write: Anytime. In emulation modes, write operations will also be directed to the external bus.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 109



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-7. DDRB Register Field Descriptions

Field Description

7-0 Port B Data Direction—
DDRB This register controls the data direction of pins 7 through 0.

The external bus function forces the I/O state to be outputs for all associated pins. In this case the data direction bits
will not change.
When operating a pin as a general purpose I/O, the associated data direction bit determines whether it is an input
or output.
1 Associated pin is configured as output.
0 Associated pin is configured as high-impedance input.

2.3.7 Port C Data Register (PORTC)

 Address 0x0004 (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PC7 PC6 PC5 PC4 PC3 PC2 PC1 PC0

W

Altern.
Function DATA15 DATA14 DATA13 DATA12 DATA11 DATA10 DATA9 DATA8

Reset 0 0 0 0 0 0 0 0

Figure 2-5. Port C Data Register (PORTC)
1. Read: Anytime. In emulation modes, read operations will return the data from the external bus, in all other modes the data source

is depending on the data direction value.
Write: Anytime. In emulation modes, write operations will also be directed to the external bus.

Table 2-8. PORTC Register Field Descriptions

Field Description

7-0 Port C general purpose input/output data—Data Register
PC Port C pins 7 through 0 are associated with data I/O lines DATA[15:8] respectively in expanded modes.

When not used with the alternative function, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

MC9S12XE-Family Reference Manual  Rev. 1.25

110 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.8 Port D Data Register (PORTD)

 Address 0x0005 (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PD7 PD6 PD5 PD4 PD3 PD2 PD1 PD0

W

Altern.
Function DATA7 DATA6 DATA5 DATA4 DATA3 DATA2 DATA1 DATA0

Reset 0 0 0 0 0 0 0 0

Figure 2-6. Port D Data Register (PORTD)
1. Read: Anytime. In emulation modes, read operations will return the data from the external bus, in all other modes the data source

is depending on the data direction value.
Write: Anytime. In emulation modes, write operations will also be directed to the external bus.

Table 2-9. PORTD Register Field Descriptions

Field Description

7-0 Port D general purpose input/output data—Data Register
PD Port D pins 7 through 0 are associated with data I/O lines DATA[7:0] respectively in expanded modes.

When not used with the alternative function, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

2.3.9 Port C Data Direction Register (DDRC)

 Address 0x0006 (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDRC7 DDRC6 DDRC5 DDRC4 DDRC3 DDRC2 DDRC1 DDRC0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-7. Port C Data Direction Register (DDRC)
1. Read: Anytime. In emulation modes, read operations will return the data from the external bus, in all other modes the data source

is depending on the data direction value.
Write: Anytime. In emulation modes, write operations will also be directed to the external bus.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 111



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-10. DDRC Register Field Descriptions

Field Description

7-0 Port C Data Direction—
DDRC This register controls the data direction of pins 7 through 0.

The external bus function controls the data direction for the associated pins. In this case the data direction bits will
not change.
When operating a pin as a general purpose I/O, the associated data direction bit determines whether it is an input
or output.
1 Associated pin is configured as output.
0 Associated pin is configured as high-impedance input.

2.3.10 Port D Data Direction Register (DDRD)

 Address 0x0007 (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDRD7 DDRD6 DDRD5 DDRD4 DDRD3 DDRD2 DDRD1 DDRD0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-8. Port D Data Direction Register (DDRD)
1. Read: Anytime. In emulation modes, read operations will return the data from the external bus, in all other modes the data source

is depending on the data direction value.
Write: Anytime. In emulation modes, write operations will also be directed to the external bus.

Table 2-11. DDRD Register Field Descriptions

Field Description

7-0 Port D Data Direction—
DDRD This register controls the data direction of pins 7 through 0.

When used with the external bus this function controls the data direction for the associated pins. In this case the data
direction bits will not change.
When operating a pin as a general purpose I/O, the associated data direction bit determines whether it is an input
or output.
1 Associated pin is configured as output.
0 Associated pin is configured as high-impedance input.

MC9S12XE-Family Reference Manual  Rev. 1.25

112 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.11 Port E Data Register (PORTE)

 Address 0x0008 (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R PE1 PE0
PE7 PE6 PE5 PE4 PE3 PE2

W

Altern. MODA EROMCTL
Function XCLKS MODB or or RW

or or RE ECLK LSTRB or IRQ XIRQ
ECLKX2 TAGHI or or WE

TAGLO LDS

Reset 0 0 0 0 0 0 —(2) —2

= Unimplemented or Reserved

Figure 2-9. Port E Data Register (PORTE)
1. Read: Anytime. In emulation modes, read operations will return the data from the external bus, in all other modes the data source

is depending on the data direction value.
Write: Anytime. In emulation modes, write operations will also be directed to the external bus.

2. These registers are reset to zero. Two bus clock cycles after reset release the register values are updated with the associated
pin values.

Table 2-12. PORTE Register Field Descriptions

Field Description

7-2 Port E general purpose input/output data—Data Register
PE Port E bits 7 through 0 are associated with external bus control signals and interrupt inputs. These include mode

select (MODB, MODA), E clock, double frequency E clock, Instruction Tagging High and Low (TAGHI, TAGLO),
Read/Write (RW), Read Enable and Write Enable (RE, WE), Lower Data Select (LDS).
When not used with the alternative functions, Port E pins 7-2 can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.
Pins 6 and 5 are inputs with enabled pull-down devices while RESET pin is low.
Pins 7 and 3 are inputs with enabled pull-up devices while RESET pin is low.

1 Port E general purpose input data and interrupt—Data Register, IRQ input.
PE This pin can be used as general purpose and IRQ input.

0 Port E general purpose input data and interrupt—Data Register, XIRQ input.
PE This pin can be used as general purpose and XIRQ input.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 113



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.12 Port E Data Direction Register (DDRE)

 Address 0x0009 (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0 0
DDRE7 DDRE6 DDRE5 DDRE4 DDRE3 DDRE2

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 2-10. Port E Data Direction Register (DDRE)
1. Read: Anytime. In emulation modes, read operations will return the data from the external bus, in all other modes the data source

is depending on the data direction value.
Write: Anytime. In emulation modes, write operations will also be directed to the external bus.

Table 2-13. DDRE Register Field Descriptions

Field Description

7-2 Port E Data Direction—
DDRE This register controls the data direction of pins 7 through 2.

The external bus function controls the data direction for the associated pins. In this case the data direction bits will
not change.
When operating a pin as a general purpose I/O, the associated data direction bit determines whether it is an input
or output.
1 Associated pin is configured as output.
0 Associated pin is configured as high-impedance input.

1-0 Reserved—
Port E bit 1 (associated with IRQ) and bit 0 (associated with XIRQ) cannot be configured as outputs. Port E, bits 1
and 0, can be read regardless of whether the alternate interrupt function is enabled.

2.3.13 S12X_EBI ports, BKGD pin Pull-up Control Register (PUCR)

 Address 0x000C (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0
PUPKE BKPUE PUPEE PUPDE PUPCE PUPBE PUPAE

W

Reset 1 1 0 1 0 0 0 0

= Unimplemented or Reserved

Figure 2-11. S12X_EBI ports, BKGD pin Pull-up Control Register (PUCR)
1. Read:Anytime in single-chip modes.

Write:Anytime, except BKPUE which is writable in Special Test Mode only.

MC9S12XE-Family Reference Manual  Rev. 1.25

114 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-14. PUCR Register Field Descriptions

Field Description

7 Pull-up Port K Enable—Enable pull-up devices on all Port K input pins
PUPKE This bit configures whether pull-up devices are activated, if the pins are used as inputs. This bit has no effect if the

pins are used as outputs. Out of reset the pull-up devices are enabled.
1 Pull-up devices enabled.
0 Pull-up devices disabled.

6 BKGD pin pull-up Enable—Enable pull-up devices on BKGD pin
BKPUE This bit configures whether a pull-up device is activated, if the pin is used as input. This bit has no effect if the pin is

used as outputs. Out of reset the pull-up device is enabled.
1 Pull-up device enabled.
0 Pull-up device disabled.

5 Reserved—

4 Pull-up Port E Enable—Enable pull-up devices on all Port E input pins except on pins 5 and 6 which have pull-down
PUPEE devices only enabled during reset. This bit has no effect on these pins.

This bit configures whether pull-up devices are activated, if the pins are used as inputs. This bit has no effect if the
pins are used as outputs. Out of reset the pull-up devices are enabled.
1 Pull-up devices enabled.
0 Pull-up devices disabled.

3 Pull-up Port D Enable—Enable pull-up devices on all Port D input pins
PUPDE This bit configures whether pull-up devices are activated, if the pins are used as inputs. This bit has no effect if the

pins are used as outputs. Out of reset the pull-up devices are disabled.
1 Pull-up devices enabled.
0 Pull-up devices disabled.

2 Pull-up Port C Enable—Enable pull-up devices on all Port C input pins
PUPCE This bit configures whether pull-up devices are activated, if the pins are used as inputs. This bit has no effect if the

pins are used as outputs. Out of reset the pull-up devices are disabled.
1 Pull-up devices enabled.
0 Pull-up devices disabled.

1 Pull-up Port B Enable—Enable pull-up devices on all Port B input pins
PUPBE This bit configures whether pull-up devices are activated, if the pins are used as inputs. This bit has no effect if the

pins are used as outputs. Out of reset the pull-up devices are disabled.
1 Pull-up devices enabled.
0 Pull-up devices disabled.

0 Pull-up Port A Enable—Enable pull-up devices on all Port A input pins
PUPAE This bit configures whether pull-up devices are activated, if the pins are used as inputs. This bit has no effect if the

pins are used as outputs. Out of reset the pull-up devices are disabled.
1 Pull-up devices enabled.
0 Pull-up devices disabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 115



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.14 S12X_EBI ports Reduced Drive Register (RDRIV)

 Address 0x000D (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0 0
RDPK RDPE RDPD RDPC RDPB RDPA

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 2-12. S12X_EBI ports Reduced Drive Register (RDRIV)
1. Read: Anytime. In emulation modes, read operations will return the data from the external bus, in all other modes the data source

is depending on the data direction value.
Write: Anytime. In emulation modes, write operations will also be directed to the external bus.

This register is used to select reduced drive for the pins associated with the S12X_EBI ports A, B, C, D,
E, and K. If enabled, the pins drive at approx. 1/5 of the full drive strength.

The reduced drive functionality does not take effect on the pins in emulation modes.
Table 2-15. RDRIV Register Field Descriptions

Field Description

7 Port K reduced drive—Select reduced drive for outputs
RDPK This bit configures the drive strength of all Port K output pins as either full or reduced independent of the function

used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

6-5 Reserved—

4 Port E reduced drive—Select reduced drive for outputs
RDPE This bit configures the drive strength of all Port E output pins as either full or reduced independent of the function

used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

3 Port D reduced drive—Select reduced drive for outputs
RDPD This bit configures the drive strength of all output pins as either full or reduced independent of the function used on

the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

2 Port C reduced drive—Select reduced drive for outputs
RDPC This bit configures the drive strength of all output pins as either full or reduced independent of the function used on

the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

116 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-15. RDRIV Register Field Descriptions (continued)

Field Description

1 Port B reduced drive—Select reduced drive for outputs
RDPB This bit configures the drive strength of all output pins as either full or reduced independent of the function used on

the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

0 Port A reduced drive—Select reduced drive for outputs
RDPA This bit configures the drive strength of all output pins as either full or reduced independent of the function used on

the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

2.3.15 ECLK Control Register (ECLKCTL)

 Address 0x001C (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R
NECLK NCLKX2 DIV16 EDIV4 EDIV3 EDIV2 EDIV1 EDIV0

W

Mode
Reset(2): Depen- 1 0 0 0 0 0 0

dent

SS 0 1 0 0 0 0 0 0

ES 1 1 0 0 0 0 0 0

ST 0 1 0 0 0 0 0 0

EX 0 1 0 0 0 0 0 0

NS 1 1 0 0 0 0 0 0

NX 0 1 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 2-13. ECLK Control Register (ECLKCTL)
1. Read: Anytime. In emulation modes, read operations will return the data from the external bus, in all other modes the data source

is depending on the data direction value.
Write: Anytime. In emulation modes, write operations will also be directed to the external bus.

2. Reset values in emulation modes are identical to those of the target mode.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 117



Chapter 2 Port Integration Module (S12XEPIMV1)

The ECLKCTL register is used to control the availability of the free-running clocks and the free-running
clock divider.

Table 2-16. ECLKCTL Register Field Descriptions

Field Description

7 No ECLK—Disable ECLK output
NECLK This bit controls the availability of a free-running clock on the ECLK pin.

Clock output is always active in emulation modes and if enabled in all other operating modes.
1 ECLK disabled
0 ECLK enabled

6 No ECLKX2—Disable ECLKX2 output
NCLKX2 This bit controls the availability of a free-running clock on the ECLKX2 pin. This clock has a fixed rate of twice the

internal Bus Clock.
Clock output is always active in emulation modes and if enabled in all other operating modes.
1 ECLKX2 disabled
0 ECLKX2 enabled

5 Free-running ECLK predivider—Divide by 16
DIV16 This bit enables a divide-by-16 stage on the selected EDIV rate.

1 Divider enabled: ECLK rate = EDIV rate divided by 16
0 Divider disabled: ECLK rate = EDIV rate

4-0 Free-running ECLK Divider—Configure ECLK rate
EDIV These bits determine the rate of the free-running clock on the ECLK pin. Divider is always disabled in emulation

modes and active as programmed in all other operating modes.
00000 ECLK rate = Bus Clock rate
00001 ECLK rate = Bus Clock rate divided by 2
00010 ECLK rate = Bus Clock rate divided by 3, ...
11111 ECLK rate = Bus Clock rate divided by 32

2.3.16 PIM Reserved Register

 Address 0x001D (PRR) Access: User read(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 2-14. PIM Reserved Register
1. Read: Always reads 0x00

Write: Unimplemented

MC9S12XE-Family Reference Manual  Rev. 1.25

118 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.17 IRQ Control Register (IRQCR)

 Address 0x001E Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0
IRQE IRQEN

W

Reset 0 1 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 2-15. IRQ Control Register (IRQCR)
1. Read: See individual bit descriptions below.

Write: See individual bit descriptions below.

Table 2-17. IRQCR Register Field Descriptions

Field Description

7 IRQ select edge sensitive only—
IRQE Special modes: Read or write anytime.

Normal & emulation modes: Read anytime, write once.
1 IRQ configured to respond only to falling edges. Falling edges on the IRQ pin will be detected anytime IRQE = 1

and will be cleared only upon a reset or the servicing of the IRQ interrupt.
0 IRQ configured for low level recognition.

6 External IRQ enable—
IRQEN Read or write anytime.

1 External IRQ pin is connected to interrupt logic.
0 External IRQ pin is disconnected from interrupt logic.

5-0 Reserved—

2.3.18 PIM Reserved Register
This register is reserved for factory testing of the PIM module and is not available in normal operation.

 Address 0x001F Access: User read(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 2-16. PIM Reserved Register
1. Read: Always reads 0x00

Write: Unimplemented

NOTE
Writing to this register when in special modes can alter the pin functionality.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 119



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.19 Port K Data Register (PORTK)

 Address 0x0032 (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PK7 PK6 PK5 PK4 PK3 PK2 PK1 PK0

W

Altern. ROMCTL ADDR22 ADDR21 ADDR20 ADDR19 ADDR18 ADDR17 ADDR16
Function or mux mux mux mux mux mux mux

EWAIT ACC2 ACC1 ACC0 IQSTAT3 IQSTAT2 IQSTAT1 IQSTAT0

Reset 0 0 0 0 0 0 0 0

Figure 2-17. Port K Data Register (PORTK)
1. Read: Anytime. In emulation modes, read operations will return the data from the external bus, in all other modes the data source

is depending on the data direction value.
Write: Anytime. In emulation modes, write operations will also be directed to the external bus.

Table 2-18. PORTK Register Field Descriptions

Field Description

7-0 Port K general purpose input/output data—Data Register
PK Port K pins 7 through 0 are associated with external bus control signals and internal memory expansion emulation

pins. These include ADDR[22:16], Access Source (ACC[2:0]), External Wait (EWAIT) and instruction pipe signals
IQSTAT[3:0]. Bits 6-0 carry the external addresses in all expanded modes. In emulation modes the address is
multiplexed with the alternate functions ACC and IQSTAT on the respective pins.
When not used with the alternative function, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

2.3.20 Port K Data Direction Register (DDRK)

 Address 0x0033 (PRR) Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDRK7 DDRK6 DDRK5 DDRK4 DDRK3 DDRK2 DDRK1 DDRK0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-18. Port K Data Direction Register (DDRK)
1. Read: Anytime. In emulation modes, read operations will return the data from the external bus, in all other modes the data source

is depending on the data direction value.
Write: Anytime. In emulation modes, write operations will also be directed to the external bus.

MC9S12XE-Family Reference Manual  Rev. 1.25

120 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-19. DDRK Register Field Descriptions

Field Description

7-0 Port K Data Direction—
DDRK This register controls the data direction of pins 7 through 0.

The external bus function controls the data direction for the associated pins. In this case the data direction bits will
not change.
When operating a pin as a general purpose I/O, the associated data direction bit determines whether it is an input
or output.
1 Associated pin is configured as output.
0 Associated pin is configured as high-impedance input.

2.3.21 Port T Data Register (PTT)

 Address 0x0240 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PTT7 PTT6 PTT5 PTT4 PTT3 PTT2 PTT1 PTT0

W

Altern.
Function IOC7 IOC6 IOC5 IOC4 IOC3 IOC2 IOC1 IOC0

— — VREG_API — — — — —

Reset 0 0 0 0 0 0 0 0

Figure 2-19. Port T Data Register (PTT)
1. Read: Anytime.

Write: Anytime.

Table 2-20. PTT Register Field Descriptions

Field Description

7-6 Port T general purpose input/output data—Data Register
PTT Port T pins 7 through 0 are associated with ECT channels IOC7 and IOC6.

When not used with the alternative function, these pins can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

5 Port T general purpose input/output data—Data Register
PTT Port T pins 5 is associated with ECT channel IOC5 and the VREG_API output.

The ECT function takes precedence over the VREG_API and the general purpose I/O function if the related channel
is enabled.
When not used with the alternative function, these pins can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

4-0 Port T general purpose input/output data—Data Register
PTT Port T pins 4 through 0 are associated with ECT channels IOC4 through IOC0.

When not used with the alternative function, these pins can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 121



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.22 Port T Input Register (PTIT)

 Address 0x0241 Access: User read(1)

7 6 5 4 3 2 1 0

R PTIT7 PTIT6 PTIT5 PTIT4 PTIT3 PTIT2 PTIT1 PTIT0

W

Reset u u u u u u u u

= Unimplemented or Reserved u = Unaffected by reset

Figure 2-20. Port T Input Register (PTIT)
1. Read: Anytime.

Write:Never, writes to this register have no effect.

Table 2-21. PTIT Register Field Descriptions

Field Description

7-0 Port T input data—
PTIT This register always reads back the buffered state of the associated pins. This can also be used to detect overload

or short circuit conditions on output pins.

2.3.23 Port T Data Direction Register (DDRT)

 Address 0x0242 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDRT7 DDRT6 DDRT5 DDRT4 DDRT3 DDRT2 DDRT1 DDRT0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-21. Port T Data Direction Register (DDRT)
1. Read: Anytime.

Write: Anytime.

Table 2-22. DDRT Register Field Descriptions

Field Description

7-0 Port T data direction—
DDRT This register controls the data direction of pins 7 through 0.

The ECT forces the I/O state to be an output for each timer port associated with an enabled output compare. In this
case the data direction bits will not change.
The data direction bits revert to controlling the I/O direction of a pin when the associated timer output compare is
disabled.
The timer Input Capture always monitors the state of the pin.
1 Associated pin is configured as output.
0 Associated pin is configured as high-impedance input.

MC9S12XE-Family Reference Manual  Rev. 1.25

122 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

NOTE
Due to internal synchronization circuits, it can take up to 2 bus clock cycles
until the correct value is read on PTT or PTIT registers, when changing the
DDRT register.

2.3.24 Port T Reduced Drive Register (RDRT)

 Address 0x0243 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
RDRT7 RDRT6 RDRT5 RDRT4 RDRT3 RDRT2 RDRT1 RDRT0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-22. Port T Reduced Drive Register (RDRT)
1. Read: Anytime.

Write: Anytime.

Table 2-23. RDRT Register Field Descriptions

Field Description

7-0 Port T reduced drive—Select reduced drive for outputs
RDRT This register configures the drive strength of output pins 7 through 0 as either full or reduced independent of the

function used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

2.3.25 Port T Pull Device Enable Register (PERT)

 Address 0x0244 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PERT7 PERT6 PERT5 PERT4 PERT3 PERT2 PERT1 PERT0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-23. Port T Pull Device Enable Register (PERT)
1. Read: Anytime.

Write: Anytime.

Table 2-24. PERT Register Field Descriptions

Field Description

7-0 Port T pull device enable—Enable pull devices on input pins
PERT These bits configure whether a pull device is activated, if the associated pin is used as an input. This bit has no effect

if the pin is used as an output. Out of reset no pull device is enabled.
1 Pull device enabled.
0 Pull device disabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 123



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.26 Port T Polarity Select Register (PPST)

 Address 0x0245 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PPST7 PPST6 PPST5 PPST4 PPST3 PPST2 PPST1 PPST0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-24. Port T Polarity Select Register (PPST)
1. Read: Anytime.

Write: Anytime.

Table 2-25. PPST Register Field Descriptions

Field Description

7-0 Port T pull device select—Determine pull device polarity on input pins
PPST This register selects whether a pull-down or a pull-up device is connected to the pin.

1 A pull-down device is connected to the associated pin, if enabled and if the pin is used as input.
0 A pull-up device is connected to the associated pin, if enabled and if the pin is used as input.

2.3.27 PIM Reserved Register

 Address 0x0246 Access: User read(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 2-25. PIM Reserved Register
1. Read: Always reads 0x00

Write: Unimplemented

2.3.28 PIM Reserved Register

 Address 0x0247 Access: User read(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 2-26. PIM Reserved Register
1. Read: Always reads 0x00

Write: Unimplemented

MC9S12XE-Family Reference Manual  Rev. 1.25

124 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.29 Port S Data Register (PTS)

 Address 0x0248 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PTS7 PTST6 PTS5 PTS4 PTS3 PTS2 PTS1 PTS0

W

Altern.
SS0 SCK0 MOSI0 MISO0 TXD1 RXD1 TXD0 RXD0

Function

Reset 0 0 0 0 0 0 0 0

Figure 2-27. Port S Data Register (PTS)
1. Read: Anytime.

Write: Anytime.

Table 2-26. PTS Register Field Descriptions

Field Description

7 Port S general purpose input/output data—Data Register
PTS Port S pin 7 is associated with the SS signal of the SPI0 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

6 Port S general purpose input/output data—Data Register
PTS Port S pin 6 is associated with the SCK signal of the SPI0 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

5 Port S general purpose input/output data—Data Register
PTS Port S pin 5 is associated with the MOSI signal of the SPI0 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

4 Port S general purpose input/output data—Data Register
PTS Port S pin 4 is associated with the MISO signal of the SPI0 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

3 Port S general purpose input/output data—Data Register
PTS Port S pin 3 is associated with the TXD signal of the SCI1 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

2 Port S general purpose input/output data—Data Register
PTS Port S bits 2 is associated with the RXD signal of the SCI1 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 125



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-26. PTS Register Field Descriptions (continued)

Field Description

1 Port S general purpose input/output data—Data Register
PTS Port S pin 3 is associated with the TXD signal of the SCI0 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

0 Port S general purpose input/output data—Data Register
PTS Port S bits 2 is associated with the RXD signal of the SCI0 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

2.3.30 Port S Input Register (PTIS)

 Address 0x0249 Access: User read(1)

7 6 5 4 3 2 1 0

R PTIS7 PTIS6 PTIS5 PTIS4 PTIS3 PTIS2 PTIS1 PTIS0

W

Reset u u u u u u u u

= Unimplemented or Reserved u = Unaffected by reset

Figure 2-28. Port S Input Register (PTIS)
1. Read: Anytime.

Write:Never, writes to this register have no effect.

Table 2-27. PTIS Register Field Descriptions

Field Description

7-0 Port S input data—
PTIS This register always reads back the buffered state of the associated pins. This can also be used to detect overload

or short circuit conditions on output pins.

2.3.31 Port S Data Direction Register (DDRS)

 Address 0x024A Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDRS7 DDRS6 DDRS5 DDRS4 DDRS3 DDRS2 DDRS1 DDRS0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-29. Port S Data Direction Register (DDRS)
1. Read: Anytime.

Write: Anytime.

MC9S12XE-Family Reference Manual  Rev. 1.25

126 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-28. DDRS Register Field Descriptions

Field Description

7-0 Port S data direction—
DDRS This register controls the data direction of pins 7 through 0.This register configures each Port S pin as either input

or output.
If SPI0 is enabled, the SPI0 determines the pin direction. Refer to SPI section for details.
If the associated SCI transmit or receive channel is enabled this register has no effect on the pins. The pin is forced
to be an output if a SCI transmit channel is enabled, it is forced to be an input if the SCI receive channel is enabled.
The data direction bits revert to controlling the I/O direction of a pin when the associated channel is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

NOTE
Due to internal synchronization circuits, it can take up to 2 bus clock cycles
until the correct value is read on PTS or PTIS registers, when changing the
DDRS register.

2.3.32 Port S Reduced Drive Register (RDRS)

 Address 0x024B Access: User read/write(1)

7 6 5 4 3 2 1 0

R
RDRS7 RDRS6 RDRS5 RDRS4 RDRS3 RDRS2 RDRS1 RDRS0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-30. Port S Reduced Drive Register (RDRS)
1. Read: Anytime.

Write: Anytime.

Table 2-29. RDRS Register Field Descriptions

Field Description

7-0 Port S reduced drive—Select reduced drive for outputs
RDRS This register configures the drive strength of output pins 7 through 0 as either full or reduced independent of the

function used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 127



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.33 Port S Pull Device Enable Register (PERS)

 Address 0x024C Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PERS7 PERS6 PERS5 PERS4 PERS3 PERS2 PERS1 PERS0

W

Reset 1 1 1 1 1 1 1 1

Figure 2-31. Port S Pull Device Enable Register (PERS)
1. Read: Anytime.

Write: Anytime.

Table 2-30. PERS Register Field Descriptions

Field Description

7-0 Port S pull device enable—Enable pull devices on input pins
PERS These bits configure whether a pull device is activated, if the associated pin is used as an input. This bit has no effect

if the pin is used as an output. Out of reset all pull devices are enabled.
1 Pull device enabled.
0 Pull device disabled.

2.3.34 Port S Polarity Select Register (PPSS)

 Address 0x024D Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PPSS7 PPSS6 PPSS5 PPSS4 PPSS3 PPSS2 PPSS1 PPSS0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-32. Port S Polarity Select Register (PPSS)
1. Read: Anytime.

Write: Anytime.

Table 2-31. PPSS Register Field Descriptions

Field Description

7-0 Port S pull device select—Determine pull device polarity on input pins
PPSS This register selects whether a pull-down or a pull-up device is connected to the pin.

1 A pull-down device is connected to the associated pin, if enabled and if the pin is used as input.
0 A pull-up device is connected to the associated pin, if enabled and if the pin is used as input.

MC9S12XE-Family Reference Manual  Rev. 1.25

128 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.35 Port S Wired-Or Mode Register (WOMS)

 Address 0x024E Access: User read/write(1)

7 6 5 4 3 2 1 0

R
WOMS7 WOMS6 WOMS5 WOMS4 WOMS3 WOMS2 WOMS1 WOMS0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-33. Port S Wired-Or Mode Register (WOMS)
1. Read: Anytime.

Write: Anytime.

Table 2-32. WOMS Register Field Descriptions

Field Description

7-0 Port S wired-or mode—Enable wired-or functionality
WOMS This register configures the output pins as wired-or independent of the function used on the pins. If enabled the

output is driven active low only (open-drain). A logic level of “1” is not driven.This allows a multipoint connection of
several serial modules. These bits have no influence on pins used as inputs.
1 Output buffers operate as open-drain outputs.
0 Output buffers operate as push-pull outputs.

2.3.36 PIM Reserved Register

 Address 0x024F Access: User read(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved u = Unaffected by reset

Figure 2-34. PIM Reserved Register
1. Read: Always reads 0x00

Write: Unimplemented

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 129



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.37 Port M Data Register (PTM)

 Address 0x0250 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PTM7 PTM6 PTM5 PTM4 PTM3 PTM2 PTM1 PTM0

W

Altern. TXCAN3 RXCAN3 TXCAN2 RXCAN2 TXCAN1 RXCAN1 TXCAN0 RXCAN0
Function

— — (TXCAN0) (RXCAN0) (TXCAN0) (RXCAN0) — —

(TXCAN4) (RXCAN4) (TXCAN4) (RXCAN4) — — — —

— — (SCK0) (MOSI0) (SS0) (MISO0) — —

TXD3 RXD3 — — — — — —

Reset 0 0 0 0 0 0 0 0

Figure 2-35. Port M Data Register (PTM)
1. Read: Anytime.

Write: Anytime.

Table 2-33. PTM Register Field Descriptions

Field Description

7-6 Port M general purpose input/output data—Data Register
PTM Port M pins 7 and 6 are associated with TXCAN and RXCAN signals of CAN3 and the routed CAN4, as well as with

TXD and RXD signals of SCI3, respectively.
The CAN3 function takes precedence over the CAN4, SCI3 and the general purpose I/O function if the CAN3 module
is enabled. The CAN4 function takes precedence over the SCI3 and the general purpose I/O function if the CAN4
module is enabled. The SCI3 function takes precedence over the general purpose I/O function if the SCI3 module
is enabled.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

5 Port M general purpose input/output data—Data Register
PTM Port M pin 5 is associated with the TXCAN signal of CAN2 and the routed CAN4 and CAN0, as well as with SCK

signals of SPI0.
The CAN2 function takes precedence over the routed CAN0, routed CAN4, the routed SPI0 and the general purpose
I/O function if the CAN2 module is enabled. The routed CAN0 function takes precedence over the routed CAN4, the
routed SPI0 and the general purpose I/O function if the routed CAN0 module is enabled. The routed CAN4 function
takes precedence over the routed SPI0 and general purpose I/O function if the routed CAN4 module is enabled. The
routed SPI0 function takes precedence of the general purpose I/O function if the routed SPI0 is enabled.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

MC9S12XE-Family Reference Manual  Rev. 1.25

130 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-33. PTM Register Field Descriptions (continued)

Field Description

4 Port M general purpose input/output data—Data Register
PTM Port M pin 4 is associated with the RXCAN signal of CAN2 and the routed CAN4 and CAN0, as well as with MOSI

signals of SPI0.
The CAN2 function takes precedence over the routed CAN0, routed CAN4, the routed SPI0 and the general purpose
I/O function if the CAN2 module is enabled. The routed CAN0 function takes precedence over the routed CAN4, the
routed SPI0 and the general purpose I/O function if the routed CAN0 module is enabled. The routed CAN4 function
takes precedence over the routed SPI0 and general purpose I/O function if the routed CAN4 module is enabled. The
routed SPI0 function takes precedence of the general purpose I/O function if the routed SPI0 is enabled.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

3 Port M general purpose input/output data—Data Register
PTM Port M pin 5 is associated with the TXCAN signal of CAN1 and the routed CAN0, as well as with SS0 signals of SPI0.

The CAN1 function takes precedence over the routed CAN0, the routed SPI0 and the general purpose I/O function
if the CAN1 module is enabled. The routed CAN0 function takes precedence over the routed SPI0 and the general
purpose I/O function if the routed CAN0 module is enabled. The routed SPI0 function takes precedence of the
general purpose I/O function if the routed SPI0 is enabled.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

2 Port M general purpose input/output data—Data Register
PTM Port M pin 4 is associated with the RXCAN signal of CAN1 and the routed CAN0, as well as with MISO signals of

SPI0.
The CAN1 function takes precedence over the routed CAN0, the routed SPI0 and the general purpose I/O function
if the CAN1 module is enabled. The routed CAN0 function takes precedence over the routed SPI0 and the general
purpose I/O function if the routed CAN0 module is enabled. The routed SPI0 function takes precedence of the
general purpose I/O function if the routed SPI0 is enabled.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

1-0 Port M general purpose input/output data—Data Register
PTM Port M pins 1 and 0 are associated with TXCAN and RXCAN signals of CAN0, respectively.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

2.3.38 Port M Input Register (PTIM)

 Address 0x0251 Access: User read(1)

7 6 5 4 3 2 1 0

R PTIM7 PTIM6 PTIM5 PTIM4 PTIM3 PTIM2 PTIM1 PTIM0

W

Reset u u u u u u u u

= Unimplemented or Reserved u = Unaffected by reset

Figure 2-36. Port M Input Register (PTIM)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 131



Chapter 2 Port Integration Module (S12XEPIMV1)

1. Read: Anytime.
Write:Never, writes to this register have no effect.

Table 2-34. PTIM Register Field Descriptions

Field Description

7-0 Port M input data—
PTIM This register always reads back the buffered state of the associated pins. This can also be used to detect overload

or short circuit conditions on output pins.

2.3.39 Port M Data Direction Register (DDRM)

 Address 0x0252 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDRM7 DDRM6 DDRM5 DDRM4 DDRM3 DDRM2 DDRM1 DDRM0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-37. Port M Data Direction Register (DDRM)
1. Read: Anytime.

Write: Anytime.

Table 2-35. DDRM Register Field Descriptions

Field Description

7 Port M data direction—
DDRM This register controls the data direction of pin 7.

The enabled CAN3, routed CAN4, or routed SCI3 forces the I/O state to be an output. In those cases the data
direction bits will not change. The DDRM bits revert to controlling the I/O direction of a pin when the associated
peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

6 Port M data direction—
DDRM This register controls the data direction of pin 6.

The enabled CAN3, routed CAN4, or routed SCI3 forces the I/O state to be an input. In those cases the data direction
bits will not change. The DDRM bits revert to controlling the I/O direction of a pin when the associated peripheral
module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

5 Port M data direction—
DDRM This register controls the data direction of pin 5.

The enabled CAN2, routed CAN0, or routed CAN4 forces the I/O state to be an output. Depending on the
configuration of the enabled routed SPI0 this pin will be forced to be input or output. In those cases the data direction
bits will not change. The DDRM bits revert to controlling the I/O direction of a pin when the associated peripheral
module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

MC9S12XE-Family Reference Manual  Rev. 1.25

132 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-35. DDRM Register Field Descriptions (continued)

Field Description

4 Port M data direction—
DDRM This register controls the data direction of pin 4.

The enabled CAN2, routed CAN0, or routed CAN4 forces the I/O state to be an input. Depending on the configuration
of the enabled routed SPI0 this pin will be forced to be input or output.In those cases the data direction bits will not
change. The DDRM bits revert to controlling the I/O direction of a pin when the associated peripheral module is
disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

3 Port M data direction—
DDRM This register controls the data direction of pin 3.

The enabled CAN1 or routed CAN0 forces the I/O state to be an output. Depending on the configuration of the
enabled routed SPI0 this pin will be forced to be input or output. In those cases the data direction bits will not change.
The DDRM bits revert to controlling the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

2 Port M data direction—
DDRM This register controls the data direction of pin 2.

The enabled CAN1 or routed CAN0 forces the I/O state to be an input. Depending on the configuration of the enabled
routed SPI0 this pin will be forced to be input or output.In those cases the data direction bits will not change. The
DDRM bits revert to controlling the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

1 Port M data direction—
DDRM This register controls the data direction of pin 1.

The enabled CAN0 forces the I/O state to be an output. In those cases the data direction bits will not change. The
DDRM bits revert to controlling the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

0 Port M data direction—
DDRM This register controls the data direction of pin 0.

The enabled CAN0 forces the I/O state to be an input. In those cases the data direction bits will not change. The
DDRM bits revert to controlling the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

NOTE
Due to internal synchronization circuits, it can take up to 2 bus clock cycles
until the correct value is read on PTM or PTIM registers, when changing the
DDRM register.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 133



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.40 Port M Reduced Drive Register (RDRM)

 Address 0x0253 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
RDRM7 RDRM6 RDRM5 RDRM4 RDRM3 RDRM2 RDRM1 RDRM0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-38. Port M Reduced Drive Register (RDRM)
1. Read: Anytime.

Write: Anytime.

Table 2-36. RDRM Register Field Descriptions

Field Description

7-0 Port M reduced drive—Select reduced drive for outputs
RDRM This register configures the drive strength of Port M output pins 7 through 0 as either full or reduced independent of

the function used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

2.3.41 Port M Pull Device Enable Register (PERM)

 Address 0x0254 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PERM7 PERM6 PERM5 PERM4 PERM3 PERM2 PERM1 PERM0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-39. Port M Pull Device Enable Register (PERM)
1. Read: Anytime.

Write: Anytime.

Table 2-37. PERM Register Field Descriptions

Field Description

7-0 Port M pull device enable—Enable pull-up devices on input pins
PERM These bits configure whether a pull device is activated, if the associated pin is used as an input or wired-or output.

This bit has no effect if the pin is used as push-pull output. Out of reset no pull device is enabled.
1 Pull device enabled.
0 Pull device disabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

134 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.42 Port M Polarity Select Register (PPSM)

 Address 0x0255 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PPSM7 PPSM6 PPSM5 PPSM4 PPSM3 PPSM2 PPSM1 PPSM0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-40. Port M Polarity Select Register (PPSM)
1. Read: Anytime.

Write: Anytime.

Table 2-38. PPSM Register Field Descriptions

Field Description

7-0 Port M pull device select—Determine pull device polarity on input pins
PPSM This register selects whether a pull-down or a pull-up device is connected to the pin. If CAN is active a pull-up device

can be activated on the RXCAN[3:0] inputs, but not a pull-down.
1 A pull-down device is connected to the associated Port M pin, if enabled by the associated bit in register PERM

and if the port is used as a general purpose but not as RXCAN.
0 A pull-up device is connected to the associated Port M pin, if enabled by the associated bit in register PERM and

if the port is used as general purpose or RXCAN input.

2.3.43 Port M Wired-Or Mode Register (WOMM)

 Address 0x0256 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
WOMM7 WOMM6 WOMM5 WOMM4 WOMM3 WOMM2 WOMM1 WOMM0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-41. Port M Wired-Or Mode Register (WOMM)
1. Read: Anytime.

Write: Anytime.

Table 2-39. WOMM Register Field Descriptions

Field Description

7-0 Port M wired-or mode—Enable wired-or functionality
WOMM This register configures the output pins as wired-or independent of the function used on the pins. If enabled the

output is driven active low only (open-drain). A logic level of “1” is not driven.This allows a multipoint connection of
several serial modules. These bits have no influence on pins used as inputs.
1 Output buffers operate as open-drain outputs.
0 Output buffers operate as push-pull outputs.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 135



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.44 Module Routing Register (MODRR)

 Address 0x0257 Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0
MODRR6 MODRR5 MODRR4 MODRR3 MODRR2 MODRR1 MODRR0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 2-42. Module Routing Register (MODRR)
1. Read: Anytime.

Write: Anytime.

This register configures the re-routing of CAN0, CAN4, SPI0, SPI1, and SPI2 on alternative ports.

Table 2-40. Module Routing Summary

Module MODRR Related Pins
6 5 4 3 2 1 0

RXCAN TXCAN
CAN0 x x x x x 0 0 PM0 PM1

x x x x x 0 1 PM2 PM3
x x x x x 1 0 PM4 PM5
x x x x x 1 1 PJ6 PJ7

CAN4 x x x 0 0 x x PJ6 PJ7
x x x 0 1 x x PM4 PM5
x x x 1 0 x x PM6 PM7
x x x 1 1 x x Reserved

MISO MOSI SCK SS
SPI0 x x 0 x x x x PS4 PS5 PS6 PS7

x x 1 x x x x PM2 PM4 PM5 PM3
SPI1 x 0 x x x x x PP0 PP1 PP2 PP3

x 1 x x x x x PH0 PH1 PH2 PH3
SPI2 0 x x x x x x PP4 PP5 PP7 PP6

1 x x x x x x PH4 PH5 PH6 PH7

MC9S12XE-Family Reference Manual  Rev. 1.25

136 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.45 Port P Data Register (PTP)

 Address 0x0258 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PTP7 PTP6 PTP5 PTP4 PTP3 PTP2 PTP1 PTP0

W

Altern. PWM7 PWM6 PWM5 PWM4 PWM3 PWM2 PWM1 PWM0
Function

SCK2 SS2 MOSI2 MISO2 SS1 SCK1 MOSI1 MISO1

Reset 0 0 0 0 0 0 0 0

Figure 2-43. Port P Data Register (PTP)
1. Read: Anytime.

Write: Anytime.

Table 2-41. PTP Register Field Descriptions

Field Description

7 Port P general purpose input/output data—Data Register
PTP Port P pin 6 is associated with the PWM output channel 7 and the SCK signal of SPI2.

The PWM function takes precedence over the SPI2 and the general purpose I/O function if the PWM channel 7 is
enabled. The SPI2 function takes precedence of the general purpose I/O function if the routed SPI2 is enabled.
When not used with the alternative functions, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

6 Port P general purpose input/output data—Data Register
PTP Port P pin 6 is associated with the PWM output channel 6 and the SS signal of SPI2.

The PWM function takes precedence over the SPI2 and the general purpose I/O function if the PWM channel 6 is
enabled. The SPI2 function takes precedence of the general purpose I/O function if the routed SPI2 is enabled.
When not used with the alternative functions, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

5 Port P general purpose input/output data—Data Register
PTP Port P pin 5 is associated with the PWM output channel 5 and the MOSI signal of SPI2.

The PWM function takes precedence over the SPI2 and the general purpose I/O function if the PWM channel 5 is
enabled. The SPI2 function takes precedence of the general purpose I/O function if the routed SPI2 is enabled.
When not used with the alternative functions, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

4 Port P general purpose input/output data—Data Register
PTP Port P pin 4 is associated with the PWM output channel 4 and the MISO signal of SPI2.

The PWM function takes precedence over the SPI2 and the general purpose I/O function if the PWM channel 4 is
enabled. The SPI2 function takes precedence of the general purpose I/O function if the routed SPI2 is enabled.
When not used with the alternative functions, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 137



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-41. PTP Register Field Descriptions (continued)

Field Description

3 Port P general purpose input/output data—Data Register
PTP Port P pin 3 is associated with the PWM output channel 3 and the SS signal of SPI1.

The PWM function takes precedence over the SPI1 and the general purpose I/O function if the PWM channel 3 is
enabled. The SPI1 function takes precedence of the general purpose I/O function if the routed SPI1 is enabled.
When not used with the alternative functions, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

2 Port P general purpose input/output data—Data Register
PTP Port P pin 2 is associated with the PWM output channel 2 and the SCK signal of SPI1.

The PWM function takes precedence over the SPI1 and the general purpose I/O function if the PWM channel 2 is
enabled. The SPI1 function takes precedence of the general purpose I/O function if the routed SPI1 is enabled.
When not used with the alternative functions, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

1 Port P general purpose input/output data—Data Register
PTP Port P pin 1 is associated with the PWM output channel 1 and the MOSI signal of SPI1.

The PWM function takes precedence over the SPI1 and the general purpose I/O function if the PWM channel 1 is
enabled. The SPI1 function takes precedence of the general purpose I/O function if the routed SPI1 is enabled.
When not used with the alternative functions, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

0 Port P general purpose input/output data—Data Register
PTP Port P pin 0 is associated with the PWM output channel 0 and the MISO signal of SPI1.

The PWM function takes precedence over the SPI1 and the general purpose I/O function if the PWM channel 0 is
enabled. The SPI1 function takes precedence of the general purpose I/O function if the routed SPI1 is enabled.
When not used with the alternative functions, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

2.3.46 Port P Input Register (PTIP)

 Address 0x0259 Access: User read(1)

7 6 5 4 3 2 1 0

R PTIP7 PTIP6 PTIP5 PTIP4 PTIP3 PTIP2 PTIP1 PTIP0

W

Reset u u u u u u u u

= Unimplemented or Reserved u = Unaffected by reset

Figure 2-44. Port P Input Register (PTIP)
1. Read: Anytime.

Write:Never, writes to this register have no effect.

MC9S12XE-Family Reference Manual  Rev. 1.25

138 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-42. PTIP Register Field Descriptions

Field Description

7-0 Port P input data—
PTIP This register always reads back the buffered state of the associated pins. This can also be used to detect overload

or short circuit conditions on output pins.

2.3.47 Port P Data Direction Register (DDRP)

 Address 0x025A Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDRP7 DDRP6 DDRP5 DDRP4 DDRP3 DDRP2 DDRP1 DDRP0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-45. Port P Data Direction Register (DDRP)
1. Read: Anytime.

Write: Anytime.

Table 2-43. DDRP Register Field Descriptions

Field Description

7 Port P data direction—
DDRP This register controls the data direction of pin 7.

The enabled PWM channel 7 forces the I/O state to be an output. If the PWM shutdown feature is enabled this pin
is forced to be an input. In these cases the data direction bit will not change.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

6-0 Port P data direction—
DDRP The PWM forces the I/O state to be an output for each port line associated with an enabled PWM6-0 channel. In this

case the data direction bit will not change.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

NOTE
Due to internal synchronization circuits, it can take up to 2 bus clock cycles
until the correct value is read on PTP or PTIP registers, when changing the
DDRP register.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 139



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.48 Port P Reduced Drive Register (RDRP)

 Address 0x025B Access: User read/write(1)

7 6 5 4 3 2 1 0

R
RDRP7 RDRP6 RDRP5 RDRP4 RDRP3 RDRP2 RDRP1 RDRP0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-46. Port P Reduced Drive Register (RDRP)
1. Read: Anytime.

Write: Anytime.

Table 2-44. RDRP Register Field Descriptions

Field Description

7-0 Port P reduced drive—Select reduced drive for outputs
RDRP This register configures the drive strength of output pins 7 through 0 as either full or reduced independent of the

function used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

2.3.49 Port P Pull Device Enable Register (PERP)

 Address 0x025C Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PPSP7 PPSP6 PPSP5 PPSP4 PPSP3 PPSP2 PPSP1 PPSP0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-47. Port P Pull Device Enable Register (PERP)
1. Read: Anytime.

Write: Anytime.

Table 2-45. PERP Register Field Descriptions

Field Description

7-0 Port P pull device enable—Enable pull devices on input pins
PERP These bits configure whether a pull device is activated, if the associated pin is used as an input. This bit has no effect

if the pin is used as an output. Out of reset no pull device is enabled.
1 Pull device enabled.
0 Pull device disabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

140 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.50 Port P Polarity Select Register (PPSP)

 Address 0x025D Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PPSP7 PPSP6 PPSP5 PPSP4 PPSP3 PPSP2 PPSP1 PPSP0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-48. Port P Polarity Select Register (PPSP)
1. Read: Anytime.

Write: Anytime.

Table 2-46. PPSP Register Field Descriptions

Field Description

7-0 Port P pull device select—Determine pull device polarity on input pins
PPSP This register serves a dual purpose by selecting the polarity of the active interrupt edge as well as selecting a pull-

up or pull-down device if enabled.
1 A rising edge on the associated Port P pin sets the associated flag bit in the PIFP register. A pull-down device is

connected to the associated Port P pin, if enabled by the associated bit in register PERP and if the port is used
as input.

0 A falling edge on the associated Port P pin sets the associated flag bit in the PIFP register.A pull-up device is
connected to the associated Port P pin, if enabled by the associated bit in register PERP and if the port is used
as input.

2.3.51 Port P Interrupt Enable Register (PIEP)
Read: Anytime.

 Address 0x025E Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PIEP7 PIEP6 PIEP5 PIEP4 PIEP3 PIEP2 PIEP1 PIEP0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-49. Port P Interrupt Enable Register (PIEP)
1. Read: Anytime.

Write: Anytime.

Table 2-47. PPSP Register Field Descriptions

Field Description

7-0 Port P interrupt enable—
PIEP This register disables or enables on a per-pin basis the edge sensitive external interrupt associated with Port P.

1 Interrupt is enabled.
0 Interrupt is disabled (interrupt flag masked).

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 141



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.52 Port P Interrupt Flag Register (PIFP)

 Address 0x025F Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PIFP7 PIFP6 PIFP5 PIFP4 PIFP3 PIFP2 PIFP1 PIFP0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-50. Port P Interrupt Flag Register (PIFP)
1. Read: Anytime.

Write: Anytime.

Table 2-48. PPSP Register Field Descriptions

Field Description

7-0 Port P interrupt flag—
PIFP Each flag is set by an active edge on the associated input pin. This could be a rising or a falling edge based on the

state of the PPSP register. To clear this flag, write logic level 1 to the corresponding bit in the PIFP register. Writing
a 0 has no effect.
1 Active edge on the associated bit has occurred (an interrupt will occur if the associated enable bit is set).
0 No active edge pending.

2.3.53 Port H Data Register (PTH)

 Address 0x0260 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PTH7 PTH6 PTH5 PTH4 PTH3 PTH2 PTH1 PTH0

W

Altern. SS2 SCK2 MOSI2 MISO2 SS1 SCK1 MOSI1 MISO1
Function

TXD5 RXD5 TXD4 RXD4 TXD7 RXD7 TXD6 RXD6

Reset 0 0 0 0 0 0 0 0

Figure 2-51. Port H Data Register (PTH)
1. Read: Anytime.

Write: Anytime.

MC9S12XE-Family Reference Manual  Rev. 1.25

142 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-49. PTH Register Field Descriptions

Field Description

7 Port H general purpose input/output data—Data Register
PTH Port H pin 7 is associated with the TXD signal of the SCI5 module and the SS signal of the routed SPI2.

The routed SPI2 function takes precedence over the SCI5 and the general purpose I/O function if the routed SPI2
module is enabled. The SCI5 function takes precedence over the general purpose I/O function if the SCI5 is enabled.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

6 Port H general purpose input/output data—Data Register
PTH Port H pin 6 is associated with the RXD signal of the SCI5 module and the SCK signal of the routed SPI2.

The routed SPI2 function takes precedence over the SCI5 and the general purpose I/O function if the routed SPI2
module is enabled. The SCI5 function takes precedence over the general purpose I/O function if the SCI5 is enabled.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

5 Port H general purpose input/output data—Data Register
PTH Port H pin 5 is associated with the TXD signal of the SCI4 module and the MOSI signal of the routed SPI2.

The routed SPI2 function takes precedence over the SCI4 and the general purpose I/O function if the routed SPI2
module is enabled. The SCI4 function takes precedence over the general purpose I/O function if the SCI4 is enabled.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

4 Port H general purpose input/output data—Data Register
PTH Port H pin 4 is associated with the RXD signal of the SCI4 module and the MISO signal of the routed SPI2.

The routed SPI2 function takes precedence over the SCI4 and the general purpose I/O function if the routed SPI2
module is enabled. The SCI4 function takes precedence over the general purpose I/O function if the SCI4 is enabled.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

3 Port H general purpose input/output data—Data Register
PTH Port H pin 3 is associated with the TXD signal of the SCI7 module and the SS signal of the routed SPI1.

The routed SPI1 function takes precedence over the SCI7 and the general purpose I/O function if the routed SPI1
module is enabled. The SCI7 function takes precedence over the general purpose I/O function if the SCI7 is enabled.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

2 Port H general purpose input/output data—Data Register
PTH Port H pin 2 is associated with the RXD signal of the SCI7 module and the SCK signal of the routed SPI1.

The routed SPI1 function takes precedence over the SCI7 and the general purpose I/O function if the routed SPI1
module is enabled. The SCI7 function takes precedence over the general purpose I/O function if the SCI7 is enabled.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 143



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-49. PTH Register Field Descriptions (continued)

Field Description

1 Port H general purpose input/output data—Data Register
PTH Port H pin 1 is associated with the TXD signal of the SCI6 module and the MOSI signal of the routed SPI1.

The routed SPI1 function takes precedence over the SCI6 and the general purpose I/O function if the routed SPI1
module is enabled. The SCI6 function takes precedence over the general purpose I/O function if the SCI6 is enabled.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

0 Port H general purpose input/output data—Data Register
PTH Port H pin 0 is associated with the RXD signal of the SCI6 module and the MISO signal of the routed SPI1.

The routed SPI1 function takes precedence over the SCI6 and the general purpose I/O function if the routed SPI1
module is enabled. The SCI6 function takes precedence over the general purpose I/O function if the SCI6 is enabled.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

2.3.54 Port H Input Register (PTIH)

 Address 0x0261 Access: User read(1)

7 6 5 4 3 2 1 0

R PTIH7 PTIH6 PTIH5 PTIH4 PTIH3 PTIH2 PTIH1 PTIH0

W

Reset u u u u u u u u

= Unimplemented or Reserved u = Unaffected by reset

Figure 2-52. Port H Input Register (PTIH)
1. Read: Anytime.

Write:Never, writes to this register have no effect.

Table 2-50. PTIH Register Field Descriptions

Field Description

7-0 Port H input data—
PTIH This register always reads back the buffered state of the associated pins. This can also be used to detect overload

or short circuit conditions on output pins.

2.3.55 Port H Data Direction Register (DDRH)

 Address 0x0262 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDRH7 DDRH6 DDRH5 DDRH4 DDRH3 DDRH2 DDRH1 DDRH0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-53. Port H Data Direction Register (DDRH)

MC9S12XE-Family Reference Manual  Rev. 1.25

144 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

1. Read: Anytime.
Write: Anytime.

Table 2-51. DDRH Register Field Descriptions

Field Description

7 Port H data direction—
DDRH This register controls the data direction of pin 7.

The enabled SCI5 forces the I/O state to be an output. Depending on the configuration of the enabled routed SPI2
this pin will be forced to be input or output. In those cases the data direction bits will not change. The DDRM bits
revert to controlling the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

6 Port H data direction—
DDRH This register controls the data direction of pin 6.

The enabled SCI5 forces the I/O state to be an input. Depending on the configuration of the enabled routed SPI2
this pin will be forced to be input or output. In those cases the data direction bits will not change. The DDRM bits
revert to controlling the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

5 Port H data direction—
DDRH This register controls the data direction of pin 5.

The enabled SCI4 forces the I/O state to be an output. Depending on the configuration of the enabled routed SPI2
this pin will be forced to be input or output. In those cases the data direction bits will not change. The DDRM bits
revert to controlling the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

4 Port H data direction—
DDRH This register controls the data direction of pin 4.

The enabled SCI4 forces the I/O state to be an input. Depending on the configuration of the enabled routed SPI2
this pin will be forced to be input or output. In those cases the data direction bits will not change. The DDRM bits
revert to controlling the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

3 Port H data direction—
DDRH This register controls the data direction of pin 3.

The enabled SCI7 forces the I/O state to be an output. Depending on the configuration of the enabled routed SPI1
this pin will be forced to be input or output. In those cases the data direction bits will not change. The DDRM bits
revert to controlling the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

2 Port H data direction—
DDRH This register controls the data direction of pin 2.

The enabled SCI7 forces the I/O state to be an input. Depending on the configuration of the enabled routed SPI1
this pin will be forced to be input or output. In those cases the data direction bits will not change. The DDRM bits
revert to controlling the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 145



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-51. DDRH Register Field Descriptions (continued)

Field Description

1 Port H data direction—
DDRH This register controls the data direction of pin 1.

The enabled SCI6 forces the I/O state to be an output. Depending on the configuration of the enabled routed SPI1
this pin will be forced to be input or output. In those cases the data direction bits will not change. The DDRM bits
revert to controlling the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

0 Port H data direction—
DDRH This register controls the data direction of pin 0.

The enabled SCI6 forces the I/O state to be an input. Depending on the configuration of the enabled routed SPI1
this pin will be forced to be input or output. In those cases the data direction bits will not change. The DDRM bits
revert to controlling the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

NOTE
Due to internal synchronization circuits, it can take up to 2 bus clock cycles
until the correct value is read on PTH or PTIH registers, when changing the
DDRH register.

2.3.56 Port H Reduced Drive Register (RDRH)

 Address 0x0263 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
RDRH7 RDRH6 RDRH5 RDRH4 RDRH3 RDRH2 RDRH1 RDRH0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-54. Port H Reduced Drive Register (RDRH)
1. Read: Anytime.

Write: Anytime.

Table 2-52. RDRH Register Field Descriptions

Field Description

7-0 Port H reduced drive—Select reduced drive for outputs
RDRH This register configures the drive strength of output pins 7 through 0 as either full or reduced independent of the

function used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

146 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.57 Port H Pull Device Enable Register (PERH)

 Address 0x0264 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PERH7 PERH6 PERH5 PERH4 PERH3 PERH2 PERH1 PERH0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-55. Port H Pull Device Enable Register (PERH)
1. Read: Anytime.

Write: Anytime.

Table 2-53. PERH Register Field Descriptions

Field Description

7-0 Port H pull device enable—Enable pull devices on input pins
PERH These bits configure whether a pull device is activated, if the associated pin is used as an input. This bit has no effect

if the pin is used as an output. Out of reset no pull device is enabled.
1 Pull device enabled.
0 Pull device disabled.

2.3.58 Port H Polarity Select Register (PPSH)

 Address 0x0265 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PPSH7 PPSH6 PPSH5 PPSH4 PPSH3 PPSH2 PPSH1 PPSH0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-56. Port H Polarity Select Register (PPSH)
1. Read: Anytime.

Write: Anytime.

Table 2-54. PPSH Register Field Descriptions

Field Description

7-0 Port H pull device select—Determine pull device polarity on input pins
PPSH This register serves a dual purpose by selecting the polarity of the active interrupt edge as well as selecting a pull-

up or pull-down device if enabled.
1 A rising edge on the associated Port H pin sets the associated flag bit in the PIFH register. A pull-down device is

connected to the associated Port H pin, if enabled by the associated bit in register PERH and if the port is used
as input.

0 A falling edge on the associated Port H pin sets the associated flag bit in the PIFH register.A pull-up device is
connected to the associated Port H pin, if enabled by the associated bit in register PERH and if the port is used
as input.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 147



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.59 Port H Interrupt Enable Register (PIEH)
Read: Anytime.

 Address 0x0266 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PIEH7 PIEH6 PIEH5 PIEH4 PIEH3 PIEH2 PIEH1 PIEH0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-57. Port H Interrupt Enable Register (PIEH)
1. Read: Anytime.

Write: Anytime.

Table 2-55. PPSP Register Field Descriptions

Field Description

7-0 Port H interrupt enable—
PIEH This register disables or enables on a per-pin basis the edge sensitive external interrupt associated with Port H.

1 Interrupt is enabled.
0 Interrupt is disabled (interrupt flag masked).

2.3.60 Port H Interrupt Flag Register (PIFH)

 Address 0x0267 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PIFH7 PIFH6 PIFH5 PIFH4 PIFH3 PIFH2 PIFH1 PIFH0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-58. Port H Interrupt Flag Register (PIFH)
1. Read: Anytime.

Write: Anytime.

Table 2-56. PPSP Register Field Descriptions

Field Description

7-0 Port H interrupt flag—
PIFH Each flag is set by an active edge on the associated input pin. This could be a rising or a falling edge based on the

state of the PPSH register. To clear this flag, write logic level 1 to the corresponding bit in the PIFH register. Writing
a 0 has no effect.
1 Active edge on the associated bit has occurred (an interrupt will occur if the associated enable bit is set).
0 No active edge pending.

MC9S12XE-Family Reference Manual  Rev. 1.25

148 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.61 Port J Data Register (PTJ)

 Address 0x0268 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PTJ7 PTJ6 PTJ5 PTJ4 PTJ3 PTJ2 PTJ1 PTJ0

W

Altern. TXCAN4 RXCAN4 — — — — TXD2 RXD2
Function

SCL0 SDA0 SCL1 SDA1 — — — —

(TXCAN0) (RXCAN0) CS2 CS0 — CS1 — CS3

Reset 0 0 0 0 0 0 0 0

Figure 2-59. Port J Data Register (PTJ)
1. Read: Anytime.

Write: Anytime.

Table 2-57. PTJ Register Field Descriptions

Field Description

7-6 Port J general purpose input/output data—Data Register
PTJ Port J pins 7 and 6 are associated with TXCAN and RXCAN signals of CAN4 and the routed CAN0, as well as with

SCL and SDA signals of IIC0, respectively.
The CAN4 function takes precedence over the IIC0, the routed CAN0 and the general purpose I/O function if the
CAN4 module is enabled. The IIC0 function takes precedence over the routed CAN0 and the general purpose I/O
function if the IIC0 is enabled. If the IIC0 module takes precedence the SDA0 and SCL0 outputs are configured as
open drain outputs. The routed CAN0 function takes precedence over the general purpose I/O function if the routed
CAN0 module is enabled.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

5-4 Port J general purpose input/output data—Data Register
PTJ This pin is associated with the SCL and SDA signals of IIC1, and with chip select outputs CS2 and CS0, respectively.

The IIC1 function takes precedence over the chip select and general purpose I/O function if the IIC1 is enabled. The
chip selects take precedence over the general purpose I/O. If the IIC1 module takes precedence the SDA1 and SCL1
outputs are configured as open drain outputs. Refer to IIC section for details.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

3 Port J general purpose input/output data—Data Register
PTJ This pin can be used as general purpose I/O.

If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

2 Port J general purpose input/output data—Data Register
PTJ This pin is associated with the chip select output signal CS2.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 149



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-57. PTJ Register Field Descriptions (continued)

Field Description

1 Port J general purpose input/output data—Data Register
PTJ This pin is associated with the TXD signal of SCI2.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

0 Port J general purpose input/output data—Data Register
PTJ This pin is associated with the TXD signal of SCI2 and chip select output CS3. The SCI function takes precedence

over the chip select and general purpose I/O function if the SCI2 is enabled. The chip select takes precedence over
the general purpose I/O.
When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

2.3.62 Port J Input Register (PTIJ)

 Address 0x0269 Access: User read(1)

7 6 5 4 3 2 1 0

R PTIJ7 PTIJ6 PTIJ5 PTIJ4 PTIJ3 PTIJ2 PTIJ1 PTIJ0

W

Reset u u u u u u u u

= Unimplemented or Reserved u = Unaffected by reset

Figure 2-60. Port J Input Register (PTIJ)
1. Read: Anytime.

Write:Never, writes to this register have no effect.

Table 2-58. PTIJ Register Field Descriptions

Field Description

7-0 Port J input data—
PTIJ This register always reads back the buffered state of the associated pins. This can also be used to detect overload

or short circuit conditions on output pins.

2.3.63 Port J Data Direction Register (DDRJ)

 Address 0x026A Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDRJ7 DDRJ6 DDRJ5 DDRJ4 DDRJ3 DDRJ2 DDRJ1 DDRJ0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-61. Port J Data Direction Register (DDRJ)

MC9S12XE-Family Reference Manual  Rev. 1.25

150 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

1. Read: Anytime.
Write: Anytime.

Table 2-59. DDRJ Register Field Descriptions

Field Description

7 Port J data direction—
DDRJ This register controls the data direction of pin 7.

The enabled CAN4 or routed CAN0 forces the I/O state to be an output. The enabled IIC0 module forces this pin to
be a open drain output. In those cases the data direction bits will not change. The DDRM bits revert to controlling
the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

6 Port J data direction—
DDRJ This register controls the data direction of pin 6.

The enabled CAN4 or routed CAN0 forces the I/O state to be an input. The enabled IIC0 module forces this pin to
be a open drain output. In those cases the data direction bits will not change. The DDRM bits revert to controlling
the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

5 Port J data direction—
DDRJ This register controls the data direction of pin 5.

The enabled CS2 signal forces the I/O state to be an output. The enabled IIC1 module forces this pin to be a open
drain output. In those cases the data direction bits will not change. The DDRM bits revert to controlling the I/O
direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

4 Port J data direction—
DDRJ This register controls the data direction of pin 4.

The enabled CS0 signal forces the I/O state to be an output. The enabled IIC1 module forces this pin to be a open
drain output. In those cases the data direction bits will not change. The DDRM bits revert to controlling the I/O
direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

3 Port J data direction—
DDRJ This register controls the data direction of pin 3.

1 Associated pin is configured as output.
0 Associated pin is configured as input.

2 Port J data direction—
DDRJ This register controls the data direction of pin 2.

The enabled CS1 signal forces the I/O state to be an output. In those cases the data direction bits will not change.
The DDRM bits revert to controlling the I/O direction of a pin when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 151



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-59. DDRJ Register Field Descriptions (continued)

Field Description

1 Port J data direction—
DDRJ This register controls the data direction of pin 1.

The enabled SCI2 forces the I/O state to be an output. The DDRM bits revert to controlling the I/O direction of a pin
when the associated peripheral module is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

0 Port J data direction—
DDRJ This register controls the data direction of pin 0.

The enabled SCI3 or CS3 signal forces the I/O state to be an output. In those cases the data direction bits will not
change. The DDRM bits revert to controlling the I/O direction of a pin when the associated peripheral module is
disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

NOTE
Due to internal synchronization circuits, it can take up to 2 bus clock cycles
until the correct value is read on PTH or PTIH registers, when changing the
DDRH register.

2.3.64 Port J Reduced Drive Register (RDRJ)

 Address 0x026B Access: User read/write(1)

7 6 5 4 3 2 1 0

R
RDRJ7 RDRJ6 RDRJ5 RDRJ4 RDRJ3 RDRJ2 RDRJ1 RDRJ0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-62. Port J Reduced Drive Register (RDRJ)
1. Read: Anytime.

Write: Anytime.

Table 2-60. RDRJ Register Field Descriptions

Field Description

7-0 Port J reduced drive—Select reduced drive for outputs
RDRJ This register configures the drive strength of output pins 7 through 0 as either full or reduced independent of the

function used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

152 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.65 Port J Pull Device Enable Register (PERJ)

 Address 0x026C Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PERJ7 PERJ6 PERJ5 PERJ4 PERJ3 PERJ2 PERJ1 PERJ0

W

Reset 1 1 1 1 1 1 1 1

Figure 2-63. Port J Pull Device Enable Register (PERJ)
1. Read: Anytime.

Write: Anytime.

Table 2-61. PERJ Register Field Descriptions

Field Description

7-0 Port J pull device enable—Enable pull devices on input pins
PERJ These bits configure whether a pull device is activated, if the associated pin is used as an input. This bit has no effect

if the pin is used as an output. Out of reset all pull device are enabled.
1 Pull device enabled.
0 Pull device disabled.

2.3.66 Port J Polarity Select Register (PPSJ)

 Address 0x026D Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PPSJ7 PPSJ6 PPSJ5 PPSJ4 PPSJ3 PPSJ2 PPSJ1 PPSJ0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-64. Port J Polarity Select Register (PPSJ)
1. Read: Anytime.

Write: Anytime.

Table 2-62. PPSJ Register Field Descriptions

Field Description

7-0 Port J pull device select—Determine pull device polarity on input pins
PPSJ This register serves a dual purpose by selecting the polarity of the active interrupt edge as well as selecting a pull-

up or pull-down device if enabled.
1 A rising edge on the associated Port J pin sets the associated flag bit in the PIFJ register. A pull-down device is

connected to the associated Port J pin, if enabled by the associated bit in register PERJ and if the port is used as
input.

0 A falling edge on the associated Port J pin sets the associated flag bit in the PIFJ register.A pull-up device is
connected to the associated Port J pin, if enabled by the associated bit in register PERJ and if the port is used as
input.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 153



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.67 Port J Interrupt Enable Register (PIEJ)
Read: Anytime.

 Address 0x026E Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PIEJ7 PIEJ6 PIEJ5 PIEJ4 PIEJ3 PIEJ2 PIEJ1 PIEJ0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-65. Port J Interrupt Enable Register (PIEJ)
1. Read: Anytime.

Write: Anytime.

Table 2-63. PPSP Register Field Descriptions

Field Description

7-0 Port J interrupt enable—
PIEJ This register disables or enables on a per-pin basis the edge sensitive external interrupt associated with Port J.

1 Interrupt is enabled.
0 Interrupt is disabled (interrupt flag masked).

2.3.68 Port J Interrupt Flag Register (PIFJ)

 Address 0x026F Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PIFJ7 PIFJ6 PIFJ5 PIFJ4 PIFJ3 PIFJ2 PIFJ1 PIFJ0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-66. Port J Interrupt Flag Register (PIFJ)
1. Read: Anytime.

Write: Anytime.

Table 2-64. PPSP Register Field Descriptions

Field Description

7-0 Port J interrupt flag—
PIFJ Each flag is set by an active edge on the associated input pin. This could be a rising or a falling edge based on the

state of the PPSJ register. To clear this flag, write logic level 1 to the corresponding bit in the PIFJ register. Writing
a 0 has no effect.
1 Active edge on the associated bit has occurred (an interrupt will occur if the associated enable bit is set).
0 No active edge pending.

MC9S12XE-Family Reference Manual  Rev. 1.25

154 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.69 Port AD0 Data Register 0 (PT0AD0)

 Address 0x0270 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PT0AD07 PT0AD06 PT0AD05 PT0AD04 PT0AD03 PT0AD02 PT0AD01 PT0AD00

W

Altern.
AN15 AN14 AN13 AN12 AN11 AN10 AN9 AN8

Function

Reset 0 0 0 0 0 0 0 0

Figure 2-67. Port AD0 Data Register 0 (PT0AD0)
1. Read: Anytime.

Write: Anytime.

Table 2-65. PT0AD0 Register Field Descriptions

Field Description

7-0 Port AD0 general purpose input/output data—Data Register
PT0AD0 This register is associated with ATD0 analog inputs AN[15:8] on PAD[15:8], respectively.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

2.3.70 Port AD0 Data Register 1 (PT1AD0)

 Address 0x0271 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PT1AD07 PT1AD06 PT1AD05 PT1AD04 PT1AD03 PT1AD02 PT1AD01 PT1AD00

W

Altern.
AN7 AN6 AN5 AN4 AN3 AN2 AN1 AN0

Function

Reset 0 0 0 0 0 0 0 0

Figure 2-68. Port AD0 Data Register 1 (PT1AD0)
1. Read: Anytime.

Write: Anytime.

Table 2-66. PT1AD0 Register Field Descriptions

Field Description

7-0 Port AD0 general purpose input/output data—Data Register
PT1AD0 This register is associated with ATD0 analog inputs AN[7:0] on PAD[7:0], respectively.

When not used with the alternative function, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 155



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.71 Port AD0 Data Direction Register 0 (DDR0AD0)

 Address 0x0272 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDR0AD07 DDR0AD06 DDR0AD05 DDR0AD04 DDR0AD03 DDR0AD02 DDR0AD01 DDR0AD00

W

Reset 0 0 0 0 0 0 0 0

Figure 2-69. Port AD0 Data Direction Register 0 (DDR0AD0)
1. Read: Anytime.

Write: Anytime.

Table 2-67. DDR0AD0 Register Field Descriptions

Field Description

7-0 Port AD0 data direction—
DDR0AD0 This register controls the data direction of pins 15 through 8.

1 Associated pin is configured as output.
0 Associated pin is configured as input.

NOTE
Due to internal synchronization circuits, it can take up to 2 bus clock cycles
until the correct value is read on PT0AD0 registers, when changing the
DDR0AD0 register.

NOTE
To use the digital input function on Port AD0 the ATD Digital Input Enable
Register (ATD0DIEN1) has to be set to logic level “1”.

2.3.72 Port AD0 Data Direction Register 1 (DDR1AD0)

 Address 0x0273 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDR1AD07 DDR1AD06 DDR1AD05 DDR1AD04 DDR1AD03 DDR1AD02 DDR1AD01 DDR1AD00

W

Reset 0 0 0 0 0 0 0 0

Figure 2-70. Port AD0 Data Direction Register 1 (DDR1AD0)
1. Read: Anytime.

Write: Anytime.

MC9S12XE-Family Reference Manual  Rev. 1.25

156 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-68. DDR1AD0 Register Field Descriptions

Field Description

7-0 Port AD0 data direction—
DDR1AD0 This register controls the data direction of pins 7 through 0.

1 Associated pin is configured as output.
0 Associated pin is configured as input.

NOTE
Due to internal synchronization circuits, it can take up to 2 bus clock cycles
until the correct value is read on PT0AD0 registers, when changing the
DDR1AD0 register.

NOTE
To use the digital input function on Port AD0 the ATD Digital Input Enable
Register (ATD0DIEN1) has to be set to logic level “1”.

2.3.73 Port AD0 Reduced Drive Register 0 (RDR0AD0)

 Address 0x0274 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
RDR0AD07 RDR0AD06 RDR0AD05 RDR0AD04 RDR0AD03 RDR0AD02 RDR0AD01 RDR0AD00

W

Reset 0 0 0 0 0 0 0 0

Figure 2-71. Port AD0 Reduced Drive Register 0 (RDR0AD0)
1. Read: Anytime.

Write: Anytime.

Table 2-69. RDR0AD0 Register Field Descriptions

Field Description

7-0 Port AD0 reduced drive—Select reduced drive for Port AD0 outputs
RDR0AD0 This register configures the drive strength of Port AD0 output pins 15 through 8 as either full or reduced independent

of the function used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 157



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.74 Port AD0 Reduced Drive Register 1 (RDR1AD0)

 Address 0x0275 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
RDR1AD07 RDR1AD06 RDR1AD05 RDR1AD04 RDR1AD03 RDR1AD02 RDR1AD01 RDR1AD00

W

Reset 0 0 0 0 0 0 0 0

Figure 2-72. Port AD0 Reduced Drive Register 1 (RDR1AD0)
1. Read: Anytime.

Write: Anytime.

Table 2-70. RDR1AD0 Register Field Descriptions

Field Description

7-0 Port AD0 reduced drive—Select reduced drive for Port AD0 outputs
RDR1AD0 This register configures the drive strength of Port AD0 output pins 7 through 0 as either full or reduced independent

of the function used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

2.3.75 Port AD0 Pull Up Enable Register 0 (PER0AD0)

 Address 0x0276 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PER0AD07 PER0AD06 PER0AD05 PER0AD04 PER0AD03 PER0AD02 PER0AD01 PER0AD00

W

Reset 0 0 0 0 0 0 0 0

Figure 2-73. Port AD0 Pull Device Up Register 0 (PER0AD0)
1. Read: Anytime.

Write: Anytime.

Table 2-71. PER0AD0 Register Field Descriptions

Field Description

7-0 Port AD0 pull device enable—Enable pull devices on input pins
PER0AD0 These bits configure whether a pull device is activated, if the associated pin is used as an input. This bit has no effect

if the pin is used as an output. Out of reset no pull device is enabled.
1 Pull device enabled.
0 Pull device disabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

158 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.76 Port AD0 Pull Up Enable Register 1 (PER1AD0)

 Address 0x0277 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PER1AD07 PER1AD06 PER1AD05 PER1AD04 PER1AD03 PER1AD02 PER1AD01 PER1AD00

W

Reset 0 0 0 0 0 0 0 0

Figure 2-74. Port AD0 Pull Up Enable Register 1 (PER1AD0)
1. Read: Anytime.

Write: Anytime.

Table 2-72. PER1AD0 Register Field Descriptions

Field Description

7-0 Port AD0 pull device enable—Enable pull devices on input pins
PER1AD0 These bits configure whether a pull device is activated, if the associated pin is used as an input. This bit has no effect

if the pin is used as an output. Out of reset no pull device is enabled.
1 Pull device enabled.
0 Pull device disabled.

2.3.77 Port AD1 Data Register 0 (PT0AD1)

 Address 0x0278 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PT0AD17 PT0AD16 PT0AD15 PT0AD14 PT0AD13 PT0AD12 PT0AD11 PT0AD10

W

Altern.
AN15 AN14 AN13 AN12 AN11 AN10 AN9 AN8

Function

Reset 0 0 0 0 0 0 0 0

Figure 2-75. Port AD1 Data Register 0 (PT0AD1)
1. Read: Anytime.

Write: Anytime.

Table 2-73. PT0AD1 Register Field Descriptions

Field Description

7-0 Port AD1 general purpose input/output data—Data Register
PT0AD1 This register is associated with ATD1 analog inputs AN[15:8] on PAD[31:24], respectively.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 159



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.78 Port AD1 Data Register 1 (PT1AD1)

 Address 0x0279 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PT1AD17 PT1AD16 PT1AD15 PT1AD14 PT1AD13 PT1AD12 PT1AD11 PT1AD10

W

Altern.
AN7 AN6 AN5 AN4 AN3 AN2 AN1 AN0

Function

Reset 0 0 0 0 0 0 0 0

Figure 2-76. Port AD1 Data Register 1 (PT1AD1)
1. Read: Anytime.

Write: Anytime.

Table 2-74. PT1AD1 Register Field Descriptions

Field Description

7-0 Port AD1 general purpose input/output data—Data Register
PT1AD1 This register is associated with ATD1 analog inputs AN[7:0] on PAD[23:16], respectively.

When not used with the alternative function, these pins can be used as general purpose I/O.
If the associated data direction bits of these pins are set to 1, a read returns the value of the port register, otherwise
the buffered pin input state is read.

2.3.79 Port AD1 Data Direction Register 0 (DDR0AD1)

 Address 0x027A Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDR0AD17 DDR0AD16 DDR0AD15 DDR0AD14 DDR0AD13 DDR0AD12 DDR0AD11 DDR0AD10

W

Reset 0 0 0 0 0 0 0 0

Figure 2-77. Port AD1 Data Direction Register 0 (DDR0AD1)
1. Read: Anytime.

Write: Anytime.

Table 2-75. DDR0AD1 Register Field Descriptions

Field Description

7-0 Port AD1 data direction—
DDR0AD1 This register controls the data direction of pins 15 through 8.

1 Associated pin is configured as output.
0 Associated pin is configured as input.

MC9S12XE-Family Reference Manual  Rev. 1.25

160 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

NOTE
Due to internal synchronization circuits, it can take up to 2 bus clock cycles
until the correct value is read on PT0AD1 registers, when changing the
DDR0AD1 register.

NOTE
To use the digital input function on Port AD1 the ATD Digital Input Enable
Register (ATD1DIEN1) has to be set to logic level “1”.

2.3.80 Port AD1 Data Direction Register 1 (DDR1AD1)

 Address 0x027B Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDR1AD17 DDR1AD16 DDR1AD15 DDR1AD14 DDR1AD13 DDR1AD12 DDR1AD11 DDR1AD10

W

Reset 0 0 0 0 0 0 0 0

Figure 2-78. Port AD1 Data Direction Register 1 (DDR1AD1)
1. Read: Anytime.

Write: Anytime.

Table 2-76. DDR1AD1 Register Field Descriptions

Field Description

7-0 Port AD1 data direction—
DDR1AD1 This register controls the data direction of pins 7 through 0.

1 Associated pin is configured as output.
0 Associated pin is configured as input.

NOTE
Due to internal synchronization circuits, it can take up to 2 bus clock cycles
until the correct value is read on PT0AD1 registers, when changing the
DDR1AD1 register.

NOTE
To use the digital input function on Port AD1 the ATD Digital Input Enable
Register (ATD1DIEN1) has to be set to logic level “1”.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 161



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.81 Port AD1 Reduced Drive Register 0 (RDR0AD1)

 Address 0x027C Access: User read/write(1)

7 6 5 4 3 2 1 0

R
RDR0AD17 RDR0AD16 RDR0AD15 RDR0AD14 RDR0AD13 RDR0AD12 RDR0AD11 RDR0AD10

W

Reset 0 0 0 0 0 0 0 0

Figure 2-79. Port AD1 Reduced Drive Register 0 (RDR0AD1)
1. Read: Anytime.

Write: Anytime.

Table 2-77. RDR0AD1 Register Field Descriptions

Field Description

7-0 Port AD1 reduced drive—Select reduced drive for Port AD1 outputs
RDR0AD1 This register configures the drive strength of Port AD1 output pins 15 through 8 as either full or reduced independent

of the function used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

2.3.82 Port AD1 Reduced Drive Register 1 (RDR1AD1)

 Address 0x027D Access: User read/write(1)

7 6 5 4 3 2 1 0

R
RDR1AD17 RDR1AD16 RDR1AD15 RDR1AD14 RDR1AD13 RDR1AD12 RDR1AD11 RDR1AD10

W

Reset 0 0 0 0 0 0 0 0

Figure 2-80. Port AD1 Reduced Drive Register 1 (RDR1AD1)
1. Read: Anytime.

Write: Anytime.

Table 2-78. RDR1AD1 Register Field Descriptions

Field Description

7-0 Port AD1 reduced drive—Select reduced drive for Port AD1 outputs
RDR1AD1 This register configures the drive strength of Port AD1 output pins 7 through 0 as either full or reduced independent

of the function used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

162 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.83 Port AD1 Pull Up Enable Register 0 (PER0AD1)

 Address 0x027E Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PER0AD17 PER0AD16 PER0AD15 PER0AD14 PER0AD13 PER0AD12 PER0AD11 PER0AD10

W

Reset 0 0 0 0 0 0 0 0

Figure 2-81. Port AD1 Pull Device Up Register 0 (PER0AD1)
1. Read: Anytime.

Write: Anytime.

Table 2-79. PER0AD1 Register Field Descriptions

Field Description

7-0 Port AD1 pull device enable—Enable pull devices on input pins
PER0AD1 These bits configure whether a pull device is activated, if the associated pin is used as an input. This bit has no effect

if the pin is used as an output. Out of reset no pull device is enabled.
1 Pull device enabled.
0 Pull device disabled.

2.3.84 Port AD1 Pull Up Enable Register 1 (PER1AD1)

 Address 0x027F Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PER1AD17 PER1AD16 PER1AD15 PER1AD14 PER1AD13 PER1AD12 PER1AD11 PER1AD10

W

Reset 0 0 0 0 0 0 0 0

Figure 2-82. Port AD1 Pull Up Enable Register 1 (PER1AD1)
1. Read: Anytime.

Write: Anytime.

Table 2-80. PER1AD1 Register Field Descriptions

Field Description

7-0 Port AD1 pull device enable—Enable pull devices on input pins
PER1AD1 These bits configure whether a pull device is activated, if the associated pin is used as an input. This bit has no effect

if the pin is used as an output. Out of reset no pull device is enabled.
1 Pull device enabled.
0 Pull device disabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 163



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.85 Port R Data Register (PTR)

 Address 0x0368 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PTR7 PTR6 PTR5 PTR4 PTR3 PTR2 PTR1 PTR0

W

Altern.
Function TIMIOC7 TIMIOC6 TIMIOC5 TIMIOC4 TIMIOC3 TIMIOC2 TIMIOC1 TIMIOC0

Reset 0 0 0 0 0 0 0 0

Figure 2-83. Port R Data Register (PTR)
1. Read: Anytime.

Write: Anytime.

Table 2-81. PTR Register Field Descriptions

Field Description

7-0 Port R general purpose input/output data—Data Register
PTR Port R pins 7 through 0 are associated with TIM channels TIMIOC7 through TIMIOC0.

When not used with the alternative function, these pins can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

2.3.86 Port R Input Register (PTIR)

 Address 0x0369 Access: User read(1)

7 6 5 4 3 2 1 0

R PTIR7 PTIR6 PTIR5 PTIR4 PTIR3 PTIR2 PTIR1 PTIR0

W

Reset u u u u u u u u

= Unimplemented or Reserved u = Unaffected by reset

Figure 2-84. Port R Input Register (PTIR)
1. Read: Anytime.

Write:Never, writes to this register have no effect.

Table 2-82. PTIR Register Field Descriptions

Field Description

7-0 Port R input data—
PTIR This register always reads back the buffered state of the associated pins. This can also be used to detect overload

or short circuit conditions on output pins.

MC9S12XE-Family Reference Manual  Rev. 1.25

164 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.87 Port R Data Direction Register (DDRR)

 Address 0x036A Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDRR7 DDRR6 DDRR5 DDRR4 DDRR3 DDRR2 DDRR1 DDRR0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-85. Port R Data Direction Register (DDRR)
1. Read: Anytime.

Write: Anytime.

Table 2-83. DDRR Register Field Descriptions

Field Description

7-0 Port R data direction—
DDRR This register controls the data direction of pins 7 through 0.

The TIM forces the I/O state to be an output for each timer port associated with an enabled output compare. In this
case the data direction bits will not change.
The data direction bits revert to controlling the I/O direction of a pin when the associated timer output compare is
disabled.
The timer Input Capture always monitors the state of the pin.
1 Associated pin is configured as output.
0 Associated pin is configured as high-impedance input.

NOTE
Due to internal synchronization circuits, it can take up to 2 bus clock cycles
until the correct value is read on PTR or PTIR registers, when changing the
DDRR register.

2.3.88 Port R Reduced Drive Register (RDRR)

 Address 0x036B Access: User read/write(1)

7 6 5 4 3 2 1 0

R
RDRR7 RDRR6 RDRR5 RDRR4 RDRR3 RDRR2 RDRR1 RDRR0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-86. Port R Reduced Drive Register (RDRR)
1. Read: Anytime.

Write: Anytime.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 165



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-84. RDRR Register Field Descriptions

Field Description

7-0 Port R reduced drive—Select reduced drive for outputs
RDRR This register configures the drive strength of output pins 7 through 0 as either full or reduced independent of the

function used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

2.3.89 Port R Pull Device Enable Register (PERR)

 Address 0x036C Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PERR7 PERR6 PERR5 PERR4 PERR3 PERR2 PERR1 PERR0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-87. Port R Pull Device Enable Register (PERR)
1. Read: Anytime.

Write: Anytime.

Table 2-85. PERR Register Field Descriptions

Field Description

7-0 Port R pull device enable—Enable pull devices on input pins
PERR These bits configure whether a pull device is activated, if the associated pin is used as an input. This bit has no effect

if the pin is used as an output. Out of reset no pull device is enabled.
1 Pull device enabled.
0 Pull device disabled.

2.3.90 Port R Polarity Select Register (PPSR)

 Address 0x036D Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PPSR7 PPSR6 PPSR5 PPSR4 PPSR3 PPSR2 PPSR1 PPSR0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-88. Port R Polarity Select Register (PPSR)
1. Read: Anytime.

Write: Anytime.

MC9S12XE-Family Reference Manual  Rev. 1.25

166 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-86. PPSR Register Field Descriptions

Field Description

7-0 Port R pull device select—Determine pull device polarity on input pins
PPSR This register selects whether a pull-down or a pull-up device is connected to the pin.

1 A pull-down device is connected to the associated pin, if enabled and if the pin is used as input.
0 A pull-up device is connected to the associated pin, if enabled and if the pin is used as input.

2.3.91 PIM Reserved Register

 Address 0x036E Access: User read(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 2-89. PIM Reserved Register
1. Read: Always reads 0x00

Write: Unimplemented

2.3.92 Port R Routing Register (PTRRR)

 Address 0x036F Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PTRRR7 PTRRR6 PTRRR5 PTRRR4 PTRRR3 PTRRR2 PTRRR1 PTRRR0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 2-90. Port R Routing Register (PTRRR)
1. Read: Anytime.

Write: Anytime.

Table 2-87. PTR Routing Register Field Descriptions

Field Description

7 Port R routing—
PTRRR This register configures the re-routing of the associated TIM channel.

1 TIMIOC7 is available on PP7
0 TIMIOC7 is available on PR7

6 Port R routing—
PTRRR This register configures the re-routing of the associated TIM channel.

1 TIMIOC6 is available on PP6
0 TIMIOC6 is available on PR6

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 167



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-87. PTR Routing Register Field Descriptions (continued)

Field Description

5 Port R routing—
PTRRR This register configures the re-routing of the associated TIM channel.

1 TIMIOC5 is available on PP5
0 TIMIOC5 is available on PR5

4 Port R routing—
PTRRR This register configures the re-routing of the associated TIM channel.

1 TIMIOC4 is available on PP4
0 TIMIOC4 is available on PR4

3 Port R routing—
PTRRR This register configures the re-routing of the associated TIM channel.

1 TIMIOC3 is available on PP3
0 TIMIOC3 is available on PR3

2 Port R routing—
PTRRR This register configures the re-routing of the associated TIM channel.

1 TIMIOC2 is available on PP2
0 TIMIOC2 is available on PR2

1 Port R routing—
PTRRR This register configures the re-routing of the associated TIM channel.

1 TIMIOC1 is available on PP1
0 TIMIOC1 is available on PR1

0 Port R routing—
PTRRR This register configures the re-routing of the associated TIM channel.

1 TIMIOC0 is available on PP0
0 TIMIOC0 is available on PR0

2.3.93 Port L Data Register (PTL)

 Address 0x0370 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PTL7 PTLT6 PTL5 PTL4 PTL3 PTL2 PTL1 PTL0

W

Altern.
(TXD7) (RXD7) (TXD6) (RXD6) (TXD5) (RXD5) (TXD4) (RXD4)

Function

Reset 0 0 0 0 0 0 0 0

Figure 2-91. Port L Data Register (PTL)
1. Read: Anytime.

Write: Anytime.

MC9S12XE-Family Reference Manual  Rev. 1.25

168 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-88. PTL Register Field Descriptions

Field Description

7 Port L general purpose input/output data—Data Register
PTL Port L pin 7 is associated with the TXD signal of the SCI7 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

6 Port L general purpose input/output data—Data Register
PTL Port L pin 6 is associated with the RXD signal of the SCI7 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

5 Port L general purpose input/output data—Data Register
PTL Port L pin 5 is associated with the TXD signal of the SCI6 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

4 Port L general purpose input/output data—Data Register
PTL Port L pin 4 is associated with the RXD signal of the SCI6 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

3 Port L general purpose input/output data—Data Register
PTL Port L pin 3 is associated with the TXD signal of the SC5 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

2 Port L general purpose input/output data—Data Register
PTL Port L pin 2 is associated with the RXD signal of the SCI5 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

1 Port L general purpose input/output data—Data Register
PTL Port L pin 3 is associated with the TXD signal of the SCI4 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

0 Port L general purpose input/output data—Data Register
PTL Port L pin 2 is associated with the RXD signal of the SCI4 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 169



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.94 Port L Input Register (PTIL)

 Address 0x0371 Access: User read(1)

7 6 5 4 3 2 1 0

R PTIL7 PTIL6 PTIL5 PTIL4 PTIL3 PTIL2 PTIL1 PTIL0

W

Reset u u u u u u u u

= Unimplemented or Reserved u = Unaffected by reset

Figure 2-92. Port L Input Register (PTIL)
1. Read: Anytime.

Write:Never, writes to this register have no effect.

Table 2-89. PTIL Register Field Descriptions

Field Description

7-0 Port L input data—
PTIL This register always reads back the buffered state of the associated pins. This can also be used to detect overload

or short circuit conditions on output pins.

2.3.95 Port L Data Direction Register (DDRL)

 Address 0x0372 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDRL7 DDRL6 DDRL5 DDRL4 DDRL3 DDRL2 DDRL1 DDRL0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-93. Port L Data Direction Register (DDRL)
1. Read: Anytime.

Write: Anytime.

Table 2-90. DDRL Register Field Descriptions

Field Description

7-0 Port L data direction—
DDRL This register controls the data direction of pins 7 through 0.This register configures each Port L pin as either input

or output.
If SPI0 is enabled, the SPI0 determines the pin direction. Refer to SPI section for details.
If the associated SCI transmit or receive channel is enabled this register has no effect on the pins. The pin is forced
to be an output if a SCI transmit channel is enabled, it is forced to be an input if the SCI receive channel is enabled.
The data direction bits revert to controlling the I/O direction of a pin when the associated channel is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

MC9S12XE-Family Reference Manual  Rev. 1.25

170 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

NOTE
Due to internal synchronization circuits, it can take up to 2 bus clock cycles
until the correct value is read on PTL or PTIL registers, when changing the
DDRL register.

2.3.96 Port L Reduced Drive Register (RDRL)

 Address 0x0373 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
RDRL7 RDRL6 RDRL5 RDRL4 RDRL3 RDRL2 RDRL1 RDRL0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-94. Port L Reduced Drive Register (RDRL)
1. Read: Anytime.

Write: Anytime.

Table 2-91. RDRL Register Field Descriptions

Field Description

7-0 Port L reduced drive—Select reduced drive for outputs
RDRL This register configures the drive strength of output pins 7 through 0 as either full or reduced independent of the

function used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

2.3.97 Port L Pull Device Enable Register (PERL)

 Address 0x0374 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PERL7 PERL6 PERL5 PERL4 PERL3 PERL2 PERL1 PERL0

W

Reset 1 1 1 1 1 1 1 1

Figure 2-95. Port L Pull Device Enable Register (PERL)
1. Read: Anytime.

Write: Anytime.

Table 2-92. PERL Register Field Descriptions

Field Description

7-0 Port L pull device enable—Enable pull devices on input pins
PERL These bits configure whether a pull device is activated, if the associated pin is used as an input. This bit has no effect

if the pin is used as an output. Out of reset all pull devices are enabled.
1 Pull device enabled.
0 Pull device disabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 171



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.98 Port L Polarity Select Register (PPSL)

 Address 0x0375 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PPSL7 PPSL6 PPSL5 PPSL4 PPSL3 PPSL2 PPSL1 PPSL0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-96. Port L Polarity Select Register (PPSL)
1. Read: Anytime.

Write: Anytime.

Table 2-93. PPSL Register Field Descriptions

Field Description

7-0 Port L pull device select—Determine pull device polarity on input pins
PPSL This register selects whether a pull-down or a pull-up device is connected to the pin.

1 A pull-down device is connected to the associated pin, if enabled and if the pin is used as input.
0 A pull-up device is connected to the associated pin, if enabled and if the pin is used as input.

2.3.99 Port L Wired-Or Mode Register (WOML)

 Address 0x0376 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
WOML7 WOML6 WOML5 WOML4 WOML3 WOML2 WOML1 WOML0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-97. Port L Wired-Or Mode Register (WOML)
1. Read: Anytime.

Write: Anytime.

Table 2-94. WOML Register Field Descriptions

Field Description

7-0 Port L wired-or mode—Enable wired-or functionality
WOML This register configures the output pins as wired-or independent of the function used on the pins. If enabled the

output is driven active low only (open-drain). A logic level of “1” is not driven.This allows a multipoint connection of
several serial modules. These bits have no influence on pins used as inputs.
1 Output buffers operate as open-drain outputs.
0 Output buffers operate as push-pull outputs.

MC9S12XE-Family Reference Manual  Rev. 1.25

172 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.100 Port L Routing Register (PTLRR)

 Address 0x0377 Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0 0 0 0
PTLRR7 PTLRR6 PTLRR5 PTLRR4

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 2-98. Port L Routing Register (PTLRR)
1. Read: Anytime.

Write: Anytime.

This register configures the re-routing of SCI7, SCI6, SCI5, and SCI4 on alternative ports.

Table 2-95. Port L Routing Summary

Module PTLRR Related Pins
7 6 5 4

TXD RXD
SCI7 0 x x x PH3 PH2

1 x x x PL7 PL6
SCI6 x 0 x x PH1 PH0

x 1 x x PL5 PL4
SCI5 x x 0 x PH7 PH6

x x 1 x PL3 PL2
SCI4 x x x 0 PH5 PH4

x x x 1 PL1 PL0

2.3.101 Port F Data Register (PTF)

 Address 0x0378 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PTF7 PTFT6 PTF5 PTF4 PTF3 PTF2 PTF1 PTF0

W

Altern.
(TXD3) (RXD3) (SCL0) (SDA0) (CS3) (CS2) (CS1) (CS0)

Function

Reset 0 0 0 0 0 0 0 0

Figure 2-99. Port F Data Register (PTF)
1. Read: Anytime.

Write: Anytime.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 173



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-96. PTF Register Field Descriptions

Field Description

7 Port F general purpose input/output data—Data Register
PTF Port F pin 7 is associated with the TXD signal of the SCI3 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

6 Port F general purpose input/output data—Data Register
PTF Port F pin 6 is associated with the RXD signal of the SCI3 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

5 Port F general purpose input/output data—Data Register
PTF Port F pin 5 is associated with the TXD signal of the SCI6 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

4 Port F general purpose input/output data—Data Register
PTF Port F pin 4 is associated with the RXD signal of the SCI6 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

3 Port F general purpose input/output data—Data Register
PTF Port F pin 3 is associated with the TXD signal of the SC5 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

2 Port F general purpose input/output data—Data Register
PTF Port F pin 2 is associated with the RXD signal of the SCI5 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

1 Port F general purpose input/output data—Data Register
PTF Port F pin 3 is associated with the TXD signal of the SCI4 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

0 Port F general purpose input/output data—Data Register
PTF Port F pin 2 is associated with the RXD signal of the SCI4 module.

When not used with the alternative function, this pin can be used as general purpose I/O.
If the associated data direction bit of this pin is set to 1, a read returns the value of the port register, otherwise the
buffered pin input state is read.

MC9S12XE-Family Reference Manual  Rev. 1.25

174 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.102 Port F Input Register (PTIF)

 Address 0x0379 Access: User read(1)

7 6 5 4 3 2 1 0

R PTIF7 PTIF6 PTIF5 PTIF4 PTIF3 PTIF2 PTIF1 PTIF0

W

Reset u u u u u u u u

= Unimplemented or Reserved u = Unaffected by reset

Figure 2-100. Port F Input Register (PTIF)
1. Read: Anytime.

Write:Never, writes to this register have no effect.

Table 2-97. PTIF Register Field Descriptions

Field Description

7-0 Port F input data—
PTIF This register always reads back the buffered state of the associated pins. This can also be used to detect overload

or short circuit conditions on output pins.

2.3.103 Port F Data Direction Register (DDRF)

 Address 0x037A Access: User read/write(1)

7 6 5 4 3 2 1 0

R
DDRF7 DDRF6 DDRF5 DDRF4 DDRF3 DDRF2 DDRF1 DDRF0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-101. Port F Data Direction Register (DDRF)
1. Read: Anytime.

Write: Anytime.

Table 2-98. DDRF Register Field Descriptions

Field Description

7-0 Port F data direction—
DDRF This register controls the data direction of pins 7 through 0.This register configures each Port F pin as either input

or output.
If SPI0 is enabled, the SPI0 determines the pin direction. Refer to SPI section for details.
If the associated SCI transmit or receive channel is enabled this register has no effect on the pins. The pin is forced
to be an output if a SCI transmit channel is enabled, it is forced to be an input if the SCI receive channel is enabled.
The data direction bits revert to controlling the I/O direction of a pin when the associated channel is disabled.
1 Associated pin is configured as output.
0 Associated pin is configured as input.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 175



Chapter 2 Port Integration Module (S12XEPIMV1)

NOTE
Due to internal synchronization circuits, it can take up to 2 bus clock cycles
until the correct value is read on PTF or PTIF registers, when changing the
DDRF register.

2.3.104 Port F Reduced Drive Register (RDRF)

 Address 0x037B Access: User read/write(1)

7 6 5 4 3 2 1 0

R
RDRF7 RDRF6 RDRF5 RDRF4 RDRF3 RDRF2 RDRF1 RDRF0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-102. Port F Reduced Drive Register (RDRF)
1. Read: Anytime.

Write: Anytime.

Table 2-99. RDRF Register Field Descriptions

Field Description

7-0 Port F reduced drive—Select reduced drive for outputs
RDRF This register configures the drive strength of output pins 7 through 0 as either full or reduced independent of the

function used on the pins. If a pin is used as input this bit has no effect.
1 Reduced drive selected (approx. 1/5 of the full drive strength).
0 Full drive strength enabled.

2.3.105 Port F Pull Device Enable Register (PERF)

 Address 0x037C Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PERF7 PERF6 PERF5 PERF4 PERF3 PERF2 PERF1 PERF0

W

Reset 1 1 1 1 1 1 1 1

Figure 2-103. Port F Pull Device Enable Register (PERF)
1. Read: Anytime.

Write: Anytime.

Table 2-100. PERF Register Field Descriptions

Field Description

7-0 Port F pull device enable—Enable pull devices on input pins
PERF These bits configure whether a pull device is activated, if the associated pin is used as an input. This bit has no effect

if the pin is used as an output. Out of reset all pull devices are enabled.
1 Pull device enabled.
0 Pull device disabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

176 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.3.106 Port F Polarity Select Register (PPSF)

 Address 0x037D Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PPSF7 PPSF6 PPSF5 PPSF4 PPSF3 PPSF2 PPSF1 PPSF0

W

Reset 0 0 0 0 0 0 0 0

Figure 2-104. Port F Polarity Select Register (PPSF)
1. Read: Anytime.

Write: Anytime.

Table 2-101. PPSF Register Field Descriptions

Field Description

7-0 Port F pull device select—Determine pull device polarity on input pins
PPSF This register selects whether a pull-down or a pull-up device is connected to the pin.

1 A pull-down device is connected to the associated pin, if enabled and if the pin is used as input.
0 A pull-up device is connected to the associated pin, if enabled and if the pin is used as input.

2.3.107 PIM Reserved Register

 Address 0x037E Access: User read(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 2-105. PIM Reserved Register
1. Read: Always reads 0x00

Write: Unimplemented

2.3.108 Port F Routing Register (PTFRR)

 Address 0x037F Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0 0
PTFRR5 PTFRR4 PTFRR3 PTFRR2 PTFRR1 PTFRR0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 2-106. Port F Routing Register (PTFRR)
1. Read: Anytime.

Write: Anytime.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 177



Chapter 2 Port Integration Module (S12XEPIMV1)

This register configures the re-routing of SCI3, IIC0, CS[3:0] on alternative ports.

Table 2-102. Port F Routing Summary

Module PTFRR Related Pins
5 4 3 2 1 0

TXD RXD
SCI3 0 x x x x x PM7 PM6

1 x x x x x PF7 PF6

SCL SDA
IIC0 x 0 x x x x PJ7 PJ6

x 1 x x x x PF5 PF4

CS
CS3 x x 0 x x x PJ0

x x 1 x x x PF3
CS2 x x x 0 x x PJ5

x x x 1 x x PF2
CS1 x x x x 0 x PJ2

x x x x 1 x PF1
CS0 x x x x x 0 PJ4

x x x x x 1 PF0

2.4 Functional Description

2.4.1 General
Each pin except PE0, PE1, and BKGD can act as general purpose I/O. In addition each pin can act as an
output from the external bus interface module or a peripheral module or an input to the external bus
interface module or a peripheral module.

2.4.2 Registers
A set of configuration registers is common to all ports with exceptions in the expanded bus interface and
ATD ports (Table 2-103). All registers can be written at any time, however a specific configuration might
not become active.

Example 2-1. Selecting a pull-up device
This device does not become active while the port is used as a push-pull output.

MC9S12XE-Family Reference Manual  Rev. 1.25

178 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

Table 2-103. Register availability per port(1)

Data Reduced Pull Polarity Wired- Interrupt Interrupt
Port Data  Input Routing

Direction Drive Enable Select Or Mode Enable Flag
A yes - yes yes yes - - - - -
B yes - yes - - - - -
C yes - yes - - - - -
D yes - yes - - - - -
E yes - yes - - - - -
K yes - yes - - - - -
T yes yes yes yes yes yes - - - -
S yes yes yes yes yes yes yes - - yes
M yes yes yes yes yes yes yes - - yes
P yes yes yes yes yes yes - yes yes -
H yes yes yes yes yes yes - yes yes -
J yes yes yes yes yes yes - yes yes -

AD0 yes - yes yes yes - - - - -
AD1 yes - yes yes yes - - - - -

R yes yes yes yes yes yes - - - -
L yes yes yes yes yes yes yes - - yes
F yes yes yes yes yes yes - - - yes

1. Each cell represents one register with individual configuration bits

******* Data register (PORTx, PTx)
This register holds the value driven out to the pin if the pin is used as a general purpose I/O.

Writing to this register has only an effect on the pin if the pin is used as general purpose output. When
reading this address, the buffered state of the pin is returned if the associated data direction register bit is
set to “0”.

If the data direction register bits are set to logic level “1”, the contents of the data register is returned. This
is independent of any other configuration (Figure 2-107).

******* Input register (PTIx)
This is a read-only register and always returns the buffered state of the pin (Figure 2-107).

******* Data direction register (DDRx)
This register defines whether the pin is used as an input or an output.

If a peripheral module controls the pin the contents of the data direction register is ignored (Figure 2-107).

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 179



Chapter 2 Port Integration Module (S12XEPIMV1)

PTI

0

1

PIN
PT 0

1

DDR 0

1

data out
Module output enable

module enable

Figure 2-107. Illustration of I/O pin functionality

******* Reduced drive register (RDRx)
If the pin is used as an output this register allows the configuration of the drive strength.

******* Pull device enable register (PERx)
This register turns on a pull-up or pull-down device.

It becomes active only if the pin is used as an input or as a wired-or output.

******* Polarity select register (PPSx)
This register selects either a pull-up or pull-down device if enabled.

It becomes only active if the pin is used as an input. A pull-up device can be activated if the pin is used as
a wired-or output.

******* Wired-or mode register (WOMx)
If the pin is used as an output this register turns off the active high drive. This allows wired-or type
connections of outputs.

2.4.2.8 Interrupt enable register (PIEx)
If the pin is used as an interrupt input this register serves as a mask to the interrupt flag to enable/disable
the interrupt.

MC9S12XE-Family Reference Manual  Rev. 1.25

180 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

******* Interrupt flag register (PIFx)
If the pin is used as an interrupt input this register holds the interrupt flag after a valid pin event.

*******0 Module routing register (MODRR, PTRRR, PTLRR, PTFRR)
This register supports the re-routing of the CAN0, CAN4, SPI2-0, SCI7-3, IIC0, TIM and CS[3:0] pins to
alternative ports. This allows a software re-configuration of the pinouts of the different package options
with respect to above peripherals.

2.4.3 Pins and Ports
NOTE

Please refer to the SOC Guide to determine the pin availability in the
different package options.

******* BKGD pin
The BKGD pin is associated with the S12X_BDM and S12X_EBI modules.

During reset, the BKGD pin is used as MODC input.

******* Port A, B
Port A pins PA[7:0] and Port B pins PB[7:0] can be used for either general-purpose I/O with the external
bus interface. In this case Port A and Port B are associated with the external address bus outputs ADDR15-
ADDR8 and ADDR7-ADDR0, respectively. PB0 is the ADDR0 or UDS output.

******* Port C, D
Port C pins PC[7:0] and Port D pins PD[7:0] can be used for either general-purpose I/O with the external
bus interface. In this case Port C and Port D are associated with the external data bus inputs/outputs
DATA15-DATA8 and DATA7-DATA0, respectively.

These pins are configured for reduced input threshold in certain operating modes (refer to S12X_EBI
section).

******* Port E
Port E is associated with the external bus control outputs RW, LSTRB, LDS and RE, the free-running clock
outputs ECLK and ECLK2X, as well as with the TAGHI, TAGLO, MODA and MODB and interrupt inputs
IRQ and XIRQ.

Port E pins PE[7:2] can be used for either general-purpose I/O or with the alternative functions.

Port E pin PE[7] can be used for either general-purpose I/O or as the free-running clock ECLKX2 output
running at the Core Clock rate. The clock output is always enabled in emulation modes.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 181



Chapter 2 Port Integration Module (S12XEPIMV1)

Port E pin PE[6] can be used for either general-purpose I/O, as TAGHI input or as MODB input during
reset.

Port E pin PE[5] can be used for either general-purpose I/O, as TAGLO input, RE output or as MODA
input during reset.

Port E pin PE[4] can be used for either general-purpose I/O or as the free-running clock ECLK output
running at the Bus Clock rate or at the programmed divided clock rate. The clock output is always enabled
in emulation modes.

Port E pin PE[3] can be used for either general-purpose I/O, as LSTRB or LDS output, or as EROMCTL
input during reset.

Port E pin PE[2] can be used for either general-purpose I/O, or as RW or WE output.

Port E pin PE[1] can be used for either general-purpose input or as the level- or falling edge-sensitive IRQ
interrupt input. IRQ will be enabled by setting the IRQEN configuration bit (2.3.17/119) and clearing the
I-bit in the CPU condition code register. It is inhibited at reset so this pin is initially configured as a simple
input with a pull-up.

Port E pin PE[0] can be used for either general-purpose input or as the level-sensitive XIRQ interrupt input.
XIRQ can be enabled by clearing the X-bit in the CPU condition code register. It is inhibited at reset so
this pin is initially configured as a high-impedance input with a pull-up.

Port E pins PE[5] and PE[6] are configured for reduced input threshold in certain modes (refer to
S12X_EBI section).

******* Port K
Port K pins PK[7:0] can be used for either general-purpose I/O, or with the external bus interface. In this
case Port K pins PK[6:0] are associated with the external address bus outputs ADDR22-ADDR16 and PK7
is associated to the EWAIT input.

Port K pin PE[7] is configured for reduced input threshold in certain modes (refer to S12X_EBI section).

******* Port T
This port is associated with the ECT module.

Port T pins PT[7:0] can be used for either general-purpose I/O, or with the channels of the Enhanced
Capture Timer.

******* Port S
This port is associated with SCI0, SCI1 and SPI0.

Port S pins PS[7:4] can be used either for general-purpose I/O, or with the SPI0 subsystem.

Port S pins PS[3:2] can be used either for general-purpose I/O, or with the SCI1 subsystem.

Port S pins PS[1:0] can be used either for general-purpose I/O, or with the SCI0 subsystem.

MC9S12XE-Family Reference Manual  Rev. 1.25

182 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

The SPI0 pins can be re-routed.

******* Port M
This port is associated with the SCI3 CAN4-0 and SPI0.

Port M pins PM[7:6] can be used for either general purpose I/O, or with the CAN3 subsystem.

Port M pins PM[5:4] can be used for either general purpose I/O, or with the CAN2 subsystem.

Port M pins PM[3:2] can be used for either general purpose I/O, or with the CAN1 subsystem.

Port M pins PM[1:0] can be used for either general purpose I/O, or with the CAN0 subsystem.

Port M pins PM[5:2] can be used for either general purpose I/O, or with the SPI0 subsystem.

The CAN0, CAN4 and SPI0 pins can be re-routed.

2.4.3.9 Port P
This port is associated with the PWM, SPI1, SPI2 and TIM.

Port P pins PP[7:0] can be used for either general purpose I/O, or with the PWM or with the channels of
the standard Timer.subsystem.

Port P pins PP[7:4] can be used for either general purpose I/O, or with the SPI2 subsystem.

Port P pins PP[3:0] can be used for either general purpose I/O, or with the SPI1 subsystem.

*******0 Port H
This port is associated with the SPI1, SPI2, and SCI7-4.

Port H pins PH[7:4] can be used for either general purpose I/O, or with the SPI2 subsystem.

Port H pins PH[3:0] can be used for either general purpose I/O, or with the SPI1 subsystem.

Port H pins PH[7:6] can be used for either general purpose I/O, or with the SCI5 subsystem.

Port H pins PH[5:4] can be used for either general purpose I/O, or with the SCI4 subsystem.

Port H pins PH[3:2] can be used for either general purpose I/O, or with the SCI7 subsystem.

Port H pins PH[1:0] can be used for either general purpose I/O, or with the SCI6 subsystem.

*******1 Port J
This port is associated with the chip selects CS[3:0] as well as with CAN4, CAN0, IIC1, IIC0, and SCI2.

Port J pins PJ[7:6] can be used for either general purpose I/O, or with the CAN4, IIC0 or CAN0
subsystems.

Port J pins PJ[5:4] can be used for either general purpose I/O, or with the IIC1 subsystem or as chip select
outputs.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 183



Chapter 2 Port Integration Module (S12XEPIMV1)

Port J pin PJ[3] can be used for general purpose I/O.

Port J pin PJ[2] can be used for either general purpose I/O or as chip select output.

Port J pin PJ[1] can be used for either general purpose I/O, or with the SCI2 subsystem.

Port J pin PJ[0] can be used for either general purpose I/O, or with the SCI2 subsystem or as chip select
output.

*******2 Port AD0
This port is associated with the ATD0.

Port AD0 pins PAD[15:0] can be used for either general purpose I/O, or with the ATD0 subsystem.

*******3 Port AD1
This port is associated with the ATD1.

Port AD1 pins PAD[31:16] can be used for either general purpose I/O, or with the ATD1 subsystem.

*******4 Port R
This port is associated with the TIM module.

Port R pins PR[7:0] can be used for either general-purpose I/O, or with the channels of the standard Timer.

The TIM channels can be re-routed.

*******5 Port L
This port is associated with SCI7-4.

Port L pins PL[7:6] can be used for either general purpose I/O, or with SCI7 subsystem.

Port L pins PL[5:4] can be used for either general purpose I/O, or with SCI6 subsystem.

Port L pins PL[3:2] can be used for either general purpose I/O, or with SCI5 subsystem.

Port L pins PL[1:0] can be used for either general purpose I/O, or with SCI4 subsystem.

*******6 Port F
This port is associated with SCI3, IIC0 and chip selects.

Port L pins PL[7:6] can be used for either general purpose I/O, or with SCI3 subsystem.

Port L pins PL[5:4] can be used for either general purpose I/O, or with IIC0 subsystem.

Port L pins PL[3:0] can be used for either general purpose I/O, or with chip selects.

MC9S12XE-Family Reference Manual  Rev. 1.25

184 Freescale Semiconductor



Chapter 2 Port Integration Module (S12XEPIMV1)

2.4.4 Pin interrupts
Ports P, H and J offer pin interrupt capability. The interrupt enable as well as the sensitivity to rising or
falling edges can be individually configured on per-pin basis. All bits/pins in a port share the same interrupt
vector. Interrupts can be used with the pins configured as inputs or outputs.

An interrupt is generated when a bit in the port interrupt flag register and its corresponding port interrupt
enable bit are both set. The pin interrupt feature is also capable to wake up the CPU when it is in STOP or
WAIT mode.

A digital filter on each pin prevents pulses (Figure 2-109) shorter than a specified time from generating an
interrupt. The minimum time varies over process conditions, temperature and voltage (Figure 2-108 and
Table 2-104).

Glitch, filtered out, no interrupt flag set

Valid pulse, interrupt flag set uncertain

tpign

tpval

Figure 2-108. Interrupt Glitch Filter on Port P, H and J (PPS=0)

Table 2-104. Pulse Detection Criteria

Mode

Pulse STOP STOP(1)

Unit

Ignored tpulse ≤ 3 bus clocks tpulse ≤ tpign

Uncertain 3 < tpulse < 4 bus clocks tpign < tpulse < tpval

Valid tpulse ≥ 4 bus clocks tpulse ≥ tpval
1. These values include the spread of the oscillator frequency over temper-

ature, voltage and process.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 185



Chapter 2 Port Integration Module (S12XEPIMV1)

tpulse

Figure 2-109. Pulse Illustration

A valid edge on an input is detected if 4 consecutive samples of a passive level are followed by 4
consecutive samples of an active level directly or indirectly.

The filters are continuously clocked by the bus clock in RUN and WAIT mode. In STOP mode the clock
is generated by an RC-oscillator in the Port Integration Module. To maximize current saving the RC
oscillator runs only if the following condition is true on any pin individually:

Sample count <= 4 and interrupt enabled (PIE=1) and interrupt flag not set (PIF=0)

2.5 Initialization Information

2.5.1 Port Data and Data Direction Register writes
It is not recommended to write PORTx/PTx and DDRx in a word access. When changing the register pins
from inputs to outputs, the data may have extra transitions during the write access. Initialize the port data
register before enabling the outputs.

MC9S12XE-Family Reference Manual  Rev. 1.25

186 Freescale Semiconductor