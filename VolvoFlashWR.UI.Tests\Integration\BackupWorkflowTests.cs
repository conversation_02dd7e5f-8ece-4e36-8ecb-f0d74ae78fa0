using System;
using System.IO;
using System.Threading.Tasks;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Backup;
using VolvoFlashWR.Communication.ECU;
using VolvoFlashWR.Communication.Vocom;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Services;
using VolvoFlashWR.UI.Tests.Helpers;

namespace VolvoFlashWR.UI.Tests.Integration
{
    [TestFixture]
    public class BackupWorkflowTests
    {
        private ILoggingService _loggingService;
        private IVocomService _vocomService;
        private IECUCommunicationService _ecuCommunicationService;
        private IBackupService _backupService;
        private IBackupSchedulerService _backupSchedulerService;
        private string _testBackupDirectory;
        private string _testSchedulesDirectory;

        [SetUp]
        public async Task Setup()
        {
            // Set up the test environment
            TestHelper.SetupTestEnvironment();

            // Create test directories
            _testBackupDirectory = Path.Combine(Path.GetTempPath(), "VolvoFlashWR_Tests", "Backups", Guid.NewGuid().ToString());
            _testSchedulesDirectory = Path.Combine(Path.GetTempPath(), "VolvoFlashWR_Tests", "Schedules", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testBackupDirectory);
            Directory.CreateDirectory(_testSchedulesDirectory);

            // Create logging service
            _loggingService = new LoggingService();
            await _loggingService.InitializeAsync(Path.Combine(Path.GetTempPath(), "VolvoFlashWR_Tests", "Logs"), true);

            // Create Vocom service
            var vocomFactory = new VocomServiceFactory(_loggingService);
            _vocomService = await vocomFactory.CreateServiceAsync();

            // Create ECU communication service
            var ecuFactory = new ECUCommunicationServiceFactory(_loggingService, _vocomService);
            _ecuCommunicationService = await ecuFactory.CreateServiceAsync();

            // Create backup service
            var backupFactory = new BackupServiceFactory(_loggingService, _ecuCommunicationService);
            _backupService = await backupFactory.CreateServiceWithCustomSettingsAsync(_testBackupDirectory);

            // Make sure the ECU communication service is not null
            Assert.That(_ecuCommunicationService, Is.Not.Null, "ECU communication service is null");

            // Create backup scheduler service
            var schedulerFactory = new BackupSchedulerServiceFactory(_loggingService, _backupService, _ecuCommunicationService);
            _backupSchedulerService = await schedulerFactory.CreateServiceAsync(_testSchedulesDirectory);
        }

        [TearDown]
        public void TearDown()
        {
            // Clean up test directories
            if (Directory.Exists(_testBackupDirectory))
            {
                Directory.Delete(_testBackupDirectory, true);
            }
            if (Directory.Exists(_testSchedulesDirectory))
            {
                Directory.Delete(_testSchedulesDirectory, true);
            }

            // Clean up the test environment
            TestHelper.CleanupTestEnvironment();
        }

        [Test]
        [Ignore("Integration test requires actual hardware")]
        public async Task FullBackupWorkflow_ValidECU_CreatesAndRestoresBackup()
        {
            // Arrange - Scan for ECUs
            var ecus = await _ecuCommunicationService.ScanForECUsAsync();
            Assert.That(ecus, Has.Count.GreaterThan(0), "No ECUs found");

            // Select the first ECU
            var ecu = ecus[0];

            // Connect to the ECU
            bool connected = await _ecuCommunicationService.ConnectToECUAsync(ecu);
            Assert.That(connected, Is.True, "Failed to connect to ECU");

            // Act - Create a backup
            var backup = await _backupService.CreateBackupAsync(
                ecu,
                "Integration Test Backup",
                "Testing",
                null,
                true,
                true,
                true);

            // Assert - Verify backup was created
            Assert.That(backup, Is.Not.Null, "Backup was not created");
            Assert.That(backup.ECUId, Is.EqualTo(ecu.Id), "Backup ECU ID does not match");
            Assert.That(backup.ECUName, Is.EqualTo(ecu.Name), "Backup ECU name does not match");
            Assert.That(backup.Description, Is.EqualTo("Integration Test Backup"), "Backup description does not match");
            Assert.That(backup.Category, Is.EqualTo("Testing"), "Backup category does not match");

            // Act - Restore the backup
            bool restored = await _backupService.RestoreBackupAsync(
                backup,
                ecu,
                true,
                true,
                true);

            // Assert - Verify backup was restored
            Assert.That(restored, Is.True, "Backup was not restored");

            // Act - Create a new version of the backup
            var newVersion = await _backupService.CreateBackupVersionAsync(
                backup,
                ecu,
                "Updated version",
                true,
                true,
                true);

            // Assert - Verify new version was created
            Assert.That(newVersion, Is.Not.Null, "New version was not created");
            Assert.That(newVersion.ParentBackupId, Is.EqualTo(backup.Id), "New version parent ID does not match");
            Assert.That(newVersion.Version, Is.EqualTo(backup.Version + 1), "New version number does not match");
            Assert.That(newVersion.VersionNotes, Is.EqualTo("Updated version"), "New version notes do not match");

            // Act - Get all versions of the backup
            var versions = await _backupService.GetBackupVersionsAsync(backup.Id);

            // Assert - Verify versions were retrieved
            Assert.That(versions, Has.Count.EqualTo(2), "Incorrect number of versions");
            Assert.That(versions[0].Id, Is.EqualTo(backup.Id), "First version ID does not match");
            Assert.That(versions[1].Id, Is.EqualTo(newVersion.Id), "Second version ID does not match");

            // Disconnect from the ECU
            await _ecuCommunicationService.DisconnectFromECUAsync(ecu);
        }

        [Test]
        [Ignore("Integration test requires actual hardware")]
        public async Task BackupScheduling_ValidECU_CreatesAndExecutesSchedule()
        {
            // Arrange - Scan for ECUs
            var ecus = await _ecuCommunicationService.ScanForECUsAsync();
            Assert.That(ecus, Has.Count.GreaterThan(0), "No ECUs found");

            // Select the first ECU
            var ecu = ecus[0];

            // Act - Create a backup schedule
            var schedule = new BackupSchedule
            {
                Name = "Test Schedule",
                Description = "Integration test schedule",
                ECUId = ecu.Id,
                ECUName = ecu.Name,
                FrequencyType = BackupFrequencyType.Daily,
                Interval = 1,
                TimeOfDay = new TimeSpan(3, 0, 0), // 3:00 AM
                StartDate = DateTime.Today,
                Category = "Testing",
                IncludeEEPROM = true,
                IncludeMicrocontrollerCode = true,
                IncludeParameters = true
            };

            var createdSchedule = await _backupSchedulerService.CreateScheduleAsync(schedule);

            // Assert - Verify schedule was created
            Assert.That(createdSchedule, Is.Not.Null, "Schedule was not created");

            // Act - Get all schedules
            var schedules = await _backupSchedulerService.GetAllSchedulesAsync();

            // Assert - Verify schedules were retrieved
            Assert.That(schedules, Has.Count.EqualTo(1), "Incorrect number of schedules");
            Assert.That(schedules[0].Name, Is.EqualTo("Test Schedule"), "Schedule name does not match");
            Assert.That(schedules[0].ECUId, Is.EqualTo(ecu.Id), "Schedule ECU ID does not match");

            // Act - Execute the schedule immediately
            var backup = await _backupSchedulerService.ExecuteScheduleNowAsync(schedules[0].Id);

            // Assert - Verify backup was created
            Assert.That(backup, Is.Not.Null, "Backup was not created by schedule");
            Assert.That(backup.ECUId, Is.EqualTo(ecu.Id), "Backup ECU ID does not match");
            Assert.That(backup.Category, Is.EqualTo("Testing"), "Backup category does not match");

            // Act - Delete the schedule
            bool deleted = await _backupSchedulerService.DeleteScheduleAsync(schedules[0].Id);

            // Assert - Verify schedule was deleted
            Assert.That(deleted, Is.True, "Schedule was not deleted");
        }

        [Test]
        [Ignore("Integration test requires actual hardware")]
        public async Task BackupRetentionPolicy_ValidSchedule_AppliesCorrectPolicy()
        {
            // Arrange - Scan for ECUs
            var ecus = await _ecuCommunicationService.ScanForECUsAsync();
            Assert.That(ecus, Has.Count.GreaterThan(0), "No ECUs found");

            // Select the first ECU
            var ecu = ecus[0];

            // Create multiple backups for the ECU
            for (int i = 0; i < 5; i++)
            {
                await _backupService.CreateBackupAsync(
                    ecu,
                    $"Test Backup {i + 1}",
                    "Testing",
                    null,
                    true,
                    true,
                    true);

                // Add a small delay to ensure different timestamps
                await Task.Delay(100);
            }

            // Create a schedule with a retention policy
            var schedule = new BackupSchedule
            {
                Name = "Retention Test Schedule",
                Description = "Test retention policy",
                ECUId = ecu.Id,
                ECUName = ecu.Name,
                FrequencyType = BackupFrequencyType.Daily,
                Interval = 1,
                TimeOfDay = new TimeSpan(3, 0, 0),
                StartDate = DateTime.Today,
                Category = "Testing",
                MaxBackupsToKeep = 3, // Only keep 3 backups
                IncludeEEPROM = true,
                IncludeMicrocontrollerCode = true,
                IncludeParameters = true
            };

            var createdSchedule = await _backupSchedulerService.CreateScheduleAsync(schedule);

            // Get all backups before applying retention policy
            var backupsBefore = await _backupService.GetAllBackupsAsync();
            int countBefore = backupsBefore.Count(b => b.ECUId == ecu.Id);

            // Execute the schedule to trigger the retention policy
            await _backupSchedulerService.ExecuteScheduleNowAsync(createdSchedule.Id);

            // Get all backups after applying retention policy
            var backupsAfter = await _backupService.GetAllBackupsAsync();
            int countAfter = backupsAfter.Count(b => b.ECUId == ecu.Id);

            // Assert - Verify retention policy was applied
            Assert.That(countAfter, Is.LessThanOrEqualTo(4), "Retention policy was not applied correctly"); // 3 kept + 1 new = 4 max
            Assert.That(countAfter, Is.LessThan(countBefore + 1), "No backups were deleted by retention policy");
        }

        [Test]
        [Ignore("Integration test requires actual hardware")]
        public async Task BackupVersioning_CreateMultipleVersions_BuildsCorrectVersionChain()
        {
            // Arrange - Scan for ECUs
            var ecus = await _ecuCommunicationService.ScanForECUsAsync();
            Assert.That(ecus, Has.Count.GreaterThan(0), "No ECUs found");

            // Select the first ECU
            var ecu = ecus[0];

            // Connect to the ECU
            bool connected = await _ecuCommunicationService.ConnectToECUAsync(ecu);
            Assert.That(connected, Is.True, "Failed to connect to ECU");

            // Create the original backup
            var originalBackup = await _backupService.CreateBackupAsync(
                ecu,
                "Original Backup",
                "Testing",
                null,
                true,
                true,
                true);

            // Create multiple versions
            var version2 = await _backupService.CreateBackupVersionAsync(
                originalBackup,
                ecu,
                "Version 2 Notes",
                true,
                true,
                true);

            var version3 = await _backupService.CreateBackupVersionAsync(
                version2,
                ecu,
                "Version 3 Notes",
                true,
                true,
                true);

            // Get the version tree
            var versionTree = await _backupService.GetBackupVersionTreeAsync(originalBackup.Id);

            // Assert - Verify version tree structure
            Assert.That(versionTree, Is.Not.Null, "Version tree is null");
            Assert.That(versionTree.RootBackup.Id, Is.EqualTo(originalBackup.Id), "Root backup ID does not match");
            Assert.That(versionTree.LatestVersion.Id, Is.EqualTo(version3.Id), "Latest version ID does not match");
            Assert.That(versionTree.VersionCount, Is.EqualTo(3), "Incorrect number of versions");

            // Check that all versions have the correct IsLatestVersion flag
            var allVersions = await _backupService.GetBackupVersionsAsync(originalBackup.Id);
            foreach (var version in allVersions)
            {
                if (version.Id == version3.Id)
                {
                    Assert.That(version.IsLatestVersion, Is.True, "Latest version flag is incorrect");
                }
                else
                {
                    Assert.That(version.IsLatestVersion, Is.False, "Non-latest version flag is incorrect");
                }
            }

            // Verify backup integrity
            bool isValid = await _backupService.VerifyBackupIntegrityAsync(version3);
            Assert.That(isValid, Is.True, "Backup integrity check failed");
        }
    }
}

