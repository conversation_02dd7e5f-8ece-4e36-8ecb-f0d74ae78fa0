using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Protocols
{
    /// <summary>
    /// Base class for ECU protocol handlers
    /// </summary>
    public abstract class BaseECUProtocolHandler : IECUProtocolHandler
    {
        #region Protected Fields

        protected readonly ILoggingService _logger;
        protected readonly IVocomService _vocomService;
        protected OperatingMode _currentOperatingMode;
        protected bool _isInitialized;

        // MC9S12XEP100 specific constants based on datasheet in MC9S12XEP100RMV1-1358561 folder
        protected const int EEPROM_SIZE = 4 * 1024; // 4KB EEPROM
        protected const int FLASH_SIZE = 768 * 1024; // 768KB Flash (from 0028 768 KByte Flash Module file)
        protected const int RAM_SIZE = 48 * 1024; // 48KB Data RAM
        protected const int MAX_CLOCK_FREQUENCY = 50 * 1000 * 1000; // 50 MHz maximum clock frequency

        // Communication interface constants
        protected const int CAN_BAUD_RATE_HIGH = 500000; // 500 kbps for high-speed CAN
        protected const int CAN_BAUD_RATE_LOW = 125000; // 125 kbps for low-speed CAN
        protected const int SPI_CLOCK_RATE = 12500000; // 12.5 MHz SPI clock rate
        protected const int SCI_BAUD_RATE_HIGH = 115200; // 115.2 kbps for high-speed SCI
        protected const int SCI_BAUD_RATE_LOW = 9600; // 9.6 kbps for low-speed SCI
        protected const int IIC_CLOCK_RATE = 400000; // 400 kHz I2C clock rate

        #endregion

        #region Properties

        /// <summary>
        /// Gets the protocol type
        /// </summary>
        public abstract ECUProtocolType ProtocolType { get; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the BaseECUProtocolHandler class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="vocomService">The Vocom service</param>
        protected BaseECUProtocolHandler(ILoggingService logger, IVocomService vocomService)
        {
            _logger = logger;
            _vocomService = vocomService ?? throw new ArgumentNullException(nameof(vocomService));
            _currentOperatingMode = OperatingMode.Bench; // Default to Bench mode
            _isInitialized = false;
        }

        #endregion

        #region IECUProtocolHandler Implementation

        /// <summary>
        /// Initializes the protocol handler
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public virtual async Task<bool> InitializeAsync()
        {
            try
            {
                _logger?.LogInformation($"Initializing {ProtocolType} protocol handler", "BaseECUProtocolHandler");

                if (_vocomService == null)
                {
                    _logger?.LogError("Vocom service is null", "BaseECUProtocolHandler");
                    return false;
                }

                if (_vocomService.CurrentDevice == null ||
                    _vocomService.CurrentDevice.ConnectionStatus != VocomConnectionStatus.Connected)
                {
                    _logger?.LogError("Vocom device is not connected", "BaseECUProtocolHandler");
                    return false;
                }

                // Set default operating mode
                _currentOperatingMode = OperatingMode.Bench;

                // Add a small delay to simulate initialization
                await Task.Delay(10);

                _isInitialized = true;
                _logger?.LogInformation($"{ProtocolType} protocol handler initialized successfully", "BaseECUProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to initialize {ProtocolType} protocol handler", "BaseECUProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Connects to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        public abstract Task<bool> ConnectAsync(ECUDevice ecu);

        /// <summary>
        /// Disconnects from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public abstract Task<bool> DisconnectAsync(ECUDevice ecu);

        /// <summary>
        /// Sets the operating mode
        /// </summary>
        /// <param name="mode">The operating mode to set</param>
        /// <returns>True if mode change is successful, false otherwise</returns>
        public virtual async Task<bool> SetOperatingModeAsync(OperatingMode mode)
        {
            try
            {
                _logger?.LogInformation($"Setting operating mode to {mode}", "BaseECUProtocolHandler");

                if (!ValidateInitialization())
                {
                    return false;
                }

                // Add a small delay to simulate mode change operation
                await Task.Delay(5);

                // Implement mode change logic specific to the protocol
                _currentOperatingMode = mode;

                _logger?.LogInformation($"Operating mode set to {mode}", "BaseECUProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to set operating mode to {mode}", "BaseECUProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Sets the communication speed mode (High or Low)
        /// </summary>
        /// <param name="ecu">The ECU to set the speed mode for</param>
        /// <param name="speedMode">The speed mode to set</param>
        /// <returns>True if speed mode change is successful, false otherwise</returns>
        public virtual async Task<bool> SetCommunicationSpeedModeAsync(ECUDevice ecu, CommunicationSpeedMode speedMode)
        {
            try
            {
                _logger?.LogInformation($"Setting communication speed mode to {speedMode} for ECU {ecu?.Name}");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                // Check if the ECU supports the requested speed mode
                if (speedMode == CommunicationSpeedMode.High && !ecu.SupportsHighSpeedCommunication)
                {
                    _logger?.LogError($"ECU {ecu.Name} does not support high-speed communication", "ECUProtocolHandler");
                    return false;
                }
                else if (speedMode == CommunicationSpeedMode.Low && !ecu.SupportsLowSpeedCommunication)
                {
                    _logger?.LogError($"ECU {ecu.Name} does not support low-speed communication", "ECUProtocolHandler");
                    return false;
                }

                // Update the ECU's current speed mode
                ecu.CurrentCommunicationSpeedMode = speedMode;

                // Protocol-specific speed mode change logic should be implemented in derived classes
                _logger?.LogInformation($"Communication speed mode set to {speedMode} for ECU {ecu.Name}", "ECUProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to set communication speed mode to {speedMode} for ECU {ecu?.Name}", "ECUProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Reads EEPROM data from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>EEPROM data as byte array</returns>
        public abstract Task<byte[]> ReadEEPROMAsync(ECUDevice ecu);

        /// <summary>
        /// Reads a chunk of EEPROM data from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <param name="offset">The offset to start reading from</param>
        /// <param name="size">The number of bytes to read</param>
        /// <returns>EEPROM data chunk as byte array</returns>
        public virtual async Task<byte[]> ReadEEPROMChunkAsync(ECUDevice ecu, int offset, int size)
        {
            try
            {
                _logger?.LogInformation($"Reading EEPROM chunk from ECU {ecu?.Name} at offset {offset} with size {size}", "ECUProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return null;
                }

                if (offset < 0 || offset >= ecu.EEPROMSize)
                {
                    _logger?.LogError($"Invalid offset {offset} for EEPROM read", "ECUProtocolHandler");
                    return null;
                }

                if (size <= 0 || offset + size > ecu.EEPROMSize)
                {
                    _logger?.LogError($"Invalid size {size} for EEPROM read at offset {offset}", "ECUProtocolHandler");
                    return null;
                }

                // Base implementation - derived classes should override with specific implementation
                // For now, we'll just read the entire EEPROM and return the requested chunk
                byte[] fullEEPROM = await ReadEEPROMAsync(ecu);
                if (fullEEPROM == null)
                {
                    _logger?.LogError($"Failed to read EEPROM from ECU {ecu.Name}", "ECUProtocolHandler");
                    return null;
                }

                byte[] chunk = new byte[size];
                Array.Copy(fullEEPROM, offset, chunk, 0, size);

                _logger?.LogInformation($"Read {size} bytes of EEPROM data from ECU {ecu.Name} at offset {offset}", "ECUProtocolHandler");
                return chunk;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading EEPROM chunk from ECU {ecu?.Name} at offset {offset}", "ECUProtocolHandler", ex);
                return null;
            }
        }

        /// <summary>
        /// Writes EEPROM data to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="data">The data to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public abstract Task<bool> WriteEEPROMAsync(ECUDevice ecu, byte[] data);

        /// <summary>
        /// Writes a chunk of EEPROM data to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="offset">The offset to start writing at</param>
        /// <param name="data">The data chunk to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public virtual async Task<bool> WriteEEPROMChunkAsync(ECUDevice ecu, int offset, byte[] data)
        {
            try
            {
                _logger?.LogInformation($"Writing EEPROM chunk to ECU {ecu?.Name} at offset {offset} with size {data?.Length}", "ECUProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                if (data == null || data.Length == 0)
                {
                    _logger?.LogError("EEPROM data chunk is null or empty", "ECUProtocolHandler");
                    return false;
                }

                if (offset < 0 || offset >= ecu.EEPROMSize)
                {
                    _logger?.LogError($"Invalid offset {offset} for EEPROM write", "ECUProtocolHandler");
                    return false;
                }

                if (offset + data.Length > ecu.EEPROMSize)
                {
                    _logger?.LogError($"Data chunk size ({data.Length} bytes) exceeds available EEPROM size at offset {offset}", "ECUProtocolHandler");
                    return false;
                }

                // Base implementation - derived classes should override with specific implementation
                // For now, we'll read the entire EEPROM, update the chunk, and write it back
                byte[] fullEEPROM = await ReadEEPROMAsync(ecu);
                if (fullEEPROM == null)
                {
                    _logger?.LogError($"Failed to read EEPROM from ECU {ecu.Name} for chunk write", "ECUProtocolHandler");
                    return false;
                }

                // Update the chunk in the full EEPROM data
                Array.Copy(data, 0, fullEEPROM, offset, data.Length);

                // Write the updated EEPROM data back to the ECU
                bool success = await WriteEEPROMAsync(ecu, fullEEPROM);
                if (success)
                {
                    _logger?.LogInformation($"Wrote {data.Length} bytes of EEPROM data to ECU {ecu.Name} at offset {offset}", "ECUProtocolHandler");
                }
                else
                {
                    _logger?.LogError($"Failed to write EEPROM chunk to ECU {ecu.Name} at offset {offset}", "ECUProtocolHandler");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing EEPROM chunk to ECU {ecu?.Name} at offset {offset}", "ECUProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Reads microcontroller code from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Microcontroller code as byte array</returns>
        public abstract Task<byte[]> ReadMicrocontrollerCodeAsync(ECUDevice ecu);

        /// <summary>
        /// Reads a chunk of microcontroller code from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <param name="offset">The offset to start reading from</param>
        /// <param name="size">The number of bytes to read</param>
        /// <returns>Microcontroller code chunk as byte array</returns>
        public virtual async Task<byte[]> ReadMicrocontrollerCodeChunkAsync(ECUDevice ecu, int offset, int size)
        {
            try
            {
                _logger?.LogInformation($"Reading microcontroller code chunk from ECU {ecu?.Name} at offset {offset} with size {size}", "ECUProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return null;
                }

                if (offset < 0 || offset >= ecu.FlashSize)
                {
                    _logger?.LogError($"Invalid offset {offset} for microcontroller code read", "ECUProtocolHandler");
                    return null;
                }

                if (size <= 0 || offset + size > ecu.FlashSize)
                {
                    _logger?.LogError($"Invalid size {size} for microcontroller code read at offset {offset}", "ECUProtocolHandler");
                    return null;
                }

                // Base implementation - derived classes should override with specific implementation
                // For now, we'll just read the entire microcontroller code and return the requested chunk
                byte[] fullCode = await ReadMicrocontrollerCodeAsync(ecu);
                if (fullCode == null)
                {
                    _logger?.LogError($"Failed to read microcontroller code from ECU {ecu.Name}", "ECUProtocolHandler");
                    return null;
                }

                byte[] chunk = new byte[size];
                Array.Copy(fullCode, offset, chunk, 0, size);

                _logger?.LogInformation($"Read {size} bytes of microcontroller code from ECU {ecu.Name} at offset {offset}", "ECUProtocolHandler");
                return chunk;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading microcontroller code chunk from ECU {ecu?.Name} at offset {offset}", "ECUProtocolHandler", ex);
                return null;
            }
        }

        /// <summary>
        /// Writes microcontroller code to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="code">The code to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public abstract Task<bool> WriteMicrocontrollerCodeAsync(ECUDevice ecu, byte[] code);

        /// <summary>
        /// Writes a chunk of microcontroller code to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="offset">The offset to start writing at</param>
        /// <param name="code">The code chunk to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public virtual async Task<bool> WriteMicrocontrollerCodeChunkAsync(ECUDevice ecu, int offset, byte[] code)
        {
            try
            {
                _logger?.LogInformation($"Writing microcontroller code chunk to ECU {ecu?.Name} at offset {offset} with size {code?.Length}", "ECUProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                if (code == null || code.Length == 0)
                {
                    _logger?.LogError("Microcontroller code chunk is null or empty", "ECUProtocolHandler");
                    return false;
                }

                if (offset < 0 || offset >= ecu.FlashSize)
                {
                    _logger?.LogError($"Invalid offset {offset} for microcontroller code write", "ECUProtocolHandler");
                    return false;
                }

                if (offset + code.Length > ecu.FlashSize)
                {
                    _logger?.LogError($"Code chunk size ({code.Length} bytes) exceeds available flash size at offset {offset}", "ECUProtocolHandler");
                    return false;
                }

                // Base implementation - derived classes should override with specific implementation
                // For now, we'll read the entire microcontroller code, update the chunk, and write it back
                byte[] fullCode = await ReadMicrocontrollerCodeAsync(ecu);
                if (fullCode == null)
                {
                    _logger?.LogError($"Failed to read microcontroller code from ECU {ecu.Name} for chunk write", "ECUProtocolHandler");
                    return false;
                }

                // Update the chunk in the full microcontroller code
                Array.Copy(code, 0, fullCode, offset, code.Length);

                // Write the updated microcontroller code back to the ECU
                bool success = await WriteMicrocontrollerCodeAsync(ecu, fullCode);
                if (success)
                {
                    _logger?.LogInformation($"Wrote {code.Length} bytes of microcontroller code to ECU {ecu.Name} at offset {offset}", "ECUProtocolHandler");
                }
                else
                {
                    _logger?.LogError($"Failed to write microcontroller code chunk to ECU {ecu.Name} at offset {offset}", "ECUProtocolHandler");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing microcontroller code chunk to ECU {ecu?.Name} at offset {offset}", "ECUProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Reads active faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>List of active faults</returns>
        public abstract Task<List<ECUFault>> ReadActiveFaultsAsync(ECUDevice ecu);

        /// <summary>
        /// Reads inactive faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>List of inactive faults</returns>
        public abstract Task<List<ECUFault>> ReadInactiveFaultsAsync(ECUDevice ecu);

        /// <summary>
        /// Clears faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <returns>True if clearing is successful, false otherwise</returns>
        public abstract Task<bool> ClearFaultsAsync(ECUDevice ecu);

        /// <summary>
        /// Clears all faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <returns>True if clearing is successful, false otherwise</returns>
        public virtual async Task<bool> ClearAllFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Clearing all faults from ECU {ecu?.Name}", "ECUProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                // Base implementation - use the ClearFaultsAsync method
                bool success = await ClearFaultsAsync(ecu);
                if (success)
                {
                    _logger?.LogInformation($"Cleared all faults from ECU {ecu.Name}", "ECUProtocolHandler");
                }
                else
                {
                    _logger?.LogError($"Failed to clear all faults from ECU {ecu.Name}", "ECUProtocolHandler");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error clearing all faults from ECU {ecu?.Name}", "ECUProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Clears specific faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <param name="faultCodes">The specific fault codes to clear</param>
        /// <returns>True if clearing is successful, false otherwise</returns>
        public virtual async Task<bool> ClearSpecificFaultsAsync(ECUDevice ecu, List<string> faultCodes)
        {
            try
            {
                _logger?.LogInformation($"Clearing specific faults from ECU {ecu?.Name}", "ECUProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                if (faultCodes == null || faultCodes.Count == 0)
                {
                    _logger?.LogError("Fault codes list is null or empty", "ECUProtocolHandler");
                    return false;
                }

                // Base implementation - just clear all faults
                // Derived classes should override with specific implementation for clearing individual faults
                _logger?.LogWarning($"Clearing specific faults not supported by base implementation, clearing all faults instead", "ECUProtocolHandler");
                bool success = await ClearAllFaultsAsync(ecu);
                if (success)
                {
                    _logger?.LogInformation($"Cleared all faults from ECU {ecu.Name} (specific fault clearing not supported)", "ECUProtocolHandler");
                }
                else
                {
                    _logger?.LogError($"Failed to clear faults from ECU {ecu.Name}", "ECUProtocolHandler");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error clearing specific faults from ECU {ecu?.Name}", "ECUProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Reads parameters from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Dictionary of parameter names and values</returns>
        public abstract Task<Dictionary<string, object>> ReadParametersAsync(ECUDevice ecu);

        /// <summary>
        /// Writes parameters to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="parameters">The parameters to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public abstract Task<bool> WriteParametersAsync(ECUDevice ecu, Dictionary<string, object> parameters);

        /// <summary>
        /// Writes a single parameter to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="parameterName">The name of the parameter to write</param>
        /// <param name="parameterValue">The value to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public virtual async Task<bool> WriteParameterAsync(ECUDevice ecu, string parameterName, object parameterValue)
        {
            try
            {
                _logger?.LogInformation($"Writing parameter {parameterName} to ECU {ecu?.Name}", "ECUProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                if (string.IsNullOrEmpty(parameterName))
                {
                    _logger?.LogError("Parameter name is null or empty", "ECUProtocolHandler");
                    return false;
                }

                if (parameterValue == null)
                {
                    _logger?.LogError("Parameter value is null", "ECUProtocolHandler");
                    return false;
                }

                // Base implementation - create a dictionary with the single parameter and use WriteParametersAsync
                Dictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { parameterName, parameterValue }
                };

                bool success = await WriteParametersAsync(ecu, parameters);
                if (success)
                {
                    _logger?.LogInformation($"Wrote parameter {parameterName} to ECU {ecu.Name}", "ECUProtocolHandler");
                }
                else
                {
                    _logger?.LogError($"Failed to write parameter {parameterName} to ECU {ecu.Name}", "ECUProtocolHandler");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing parameter {parameterName} to ECU {ecu?.Name}", "ECUProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Performs a diagnostic session on an ECU
        /// </summary>
        /// <param name="ecu">The ECU to diagnose</param>
        /// <returns>Diagnostic data</returns>
        public abstract Task<DiagnosticData> PerformDiagnosticSessionAsync(ECUDevice ecu);

        /// <summary>
        /// Cancels the current operation
        /// </summary>
        /// <returns>True if cancellation is successful, false otherwise</returns>
        public virtual async Task<bool> CancelOperationAsync()
        {
            try
            {
                _logger?.LogInformation($"Cancelling current operation for {ProtocolType} protocol handler", "ECUProtocolHandler");

                if (!ValidateInitialization())
                {
                    return false;
                }

                // Base implementation - derived classes should override with specific cancellation logic
                _logger?.LogInformation($"Operation cancelled for {ProtocolType} protocol handler", "ECUProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to cancel operation for {ProtocolType} protocol handler", "ECUProtocolHandler", ex);
                return false;
            }
        }

        #endregion

        #region Data Communication Methods

        /// <summary>
        /// Reads data from an ECU using the specified command
        /// </summary>
        /// <param name="device">The Vocom device to use</param>
        /// <param name="ecu">The ECU to read from</param>
        /// <param name="command">The command to send</param>
        /// <param name="dataSize">The size of data to read</param>
        /// <returns>The data read from the ECU</returns>
        public virtual async Task<byte[]> ReadDataAsync(VocomDevice device, ECUDevice ecu, byte command, int dataSize)
        {
            try
            {
                _logger?.LogInformation($"Reading data from ECU {ecu?.Name} with command 0x{command:X2}", GetType().Name);

                if (device == null)
                {
                    _logger?.LogError("Vocom device is null", GetType().Name);
                    return null;
                }

                if (ecu == null)
                {
                    _logger?.LogError("ECU is null", GetType().Name);
                    return null;
                }

                // This is a base implementation that should be overridden by derived classes
                _logger?.LogWarning("ReadDataAsync called on base class, this should be overridden", GetType().Name);
                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading data from ECU {ecu?.Name}", GetType().Name, ex);
                return null;
            }
        }

        /// <summary>
        /// Writes data to an ECU using the specified command
        /// </summary>
        /// <param name="device">The Vocom device to use</param>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="command">The command to send</param>
        /// <param name="data">The data to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public virtual async Task<bool> WriteDataAsync(VocomDevice device, ECUDevice ecu, byte command, byte[] data)
        {
            try
            {
                _logger?.LogInformation($"Writing data to ECU {ecu?.Name} with command 0x{command:X2}", GetType().Name);

                if (device == null)
                {
                    _logger?.LogError("Vocom device is null", GetType().Name);
                    return false;
                }

                if (ecu == null)
                {
                    _logger?.LogError("ECU is null", GetType().Name);
                    return false;
                }

                if (data == null)
                {
                    _logger?.LogError("Data is null", GetType().Name);
                    return false;
                }

                // This is a base implementation that should be overridden by derived classes
                _logger?.LogWarning("WriteDataAsync called on base class, this should be overridden", GetType().Name);
                return false;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing data to ECU {ecu?.Name}", GetType().Name, ex);
                return false;
            }
        }

        #endregion

        #region Protected Methods

        /// <summary>
        /// Validates that the protocol handler is initialized
        /// </summary>
        /// <returns>True if initialized, false otherwise</returns>
        protected bool ValidateInitialization()
        {
            if (!_isInitialized)
            {
                _logger?.LogError($"{ProtocolType} protocol handler is not initialized", "ECUProtocolHandler");
                return false;
            }

            if (_vocomService == null || _vocomService.CurrentDevice == null ||
                _vocomService.CurrentDevice.ConnectionStatus != VocomConnectionStatus.Connected)
            {
                _logger?.LogError("Vocom device is not connected", "ECUProtocolHandler");
                return false;
            }

            return true;
        }

        /// <summary>
        /// Validates that the ECU is valid
        /// </summary>
        /// <param name="ecu">The ECU to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        protected bool ValidateECU(ECUDevice? ecu)
        {
            if (ecu == null)
            {
                _logger?.LogError("ECU is null", "ECUProtocolHandler");
                return false;
            }

            return true;
        }

        #endregion
    }
}
