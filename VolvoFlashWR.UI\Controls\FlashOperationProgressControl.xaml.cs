using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using System.Windows.Threading;
using VolvoFlashWR.Core.Diagnostics;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.UI.Controls
{
    /// <summary>
    /// Interaction logic for FlashOperationProgressControl.xaml
    /// </summary>
    public partial class FlashOperationProgressControl : UserControl, INotifyPropertyChanged, IDisposable
    {
        private readonly FlashOperationMonitor _monitor;
        private readonly DispatcherTimer _refreshTimer;
        private readonly ObservableCollection<FlashOperation> _operations = new ObservableCollection<FlashOperation>();
        
        /// <summary>
        /// Gets the collection of flash operations
        /// </summary>
        public ObservableCollection<FlashOperation> Operations => _operations;
        
        /// <summary>
        /// Event raised when a property changes
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;
        
        /// <summary>
        /// Initializes a new instance of the FlashOperationProgressControl class
        /// </summary>
        /// <param name="monitor">The flash operation monitor</param>
        public FlashOperationProgressControl(FlashOperationMonitor monitor)
        {
            InitializeComponent();
            
            _monitor = monitor ?? throw new ArgumentNullException(nameof(monitor));
            
            // Set up the data context
            DataContext = this;
            lvOperations.ItemsSource = _operations;
            
            // Set up the refresh timer
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _refreshTimer.Tick += RefreshTimer_Tick;
            _refreshTimer.Start();
            
            // Subscribe to monitor events
            _monitor.OperationStarted += Monitor_OperationStarted;
            _monitor.OperationUpdated += Monitor_OperationUpdated;
            _monitor.OperationCompleted += Monitor_OperationCompleted;
            _monitor.OperationFailed += Monitor_OperationFailed;
            
            // Initial refresh
            RefreshOperations();
        }
        
        /// <summary>
        /// Refreshes the operations list
        /// </summary>
        private void RefreshOperations()
        {
            // Get all operations from the monitor
            var operations = _monitor.Operations.ToList();
            
            // Update the UI on the dispatcher thread
            Dispatcher.Invoke(() =>
            {
                // Clear the current operations
                _operations.Clear();
                
                // Add the operations
                foreach (var operation in operations)
                {
                    _operations.Add(operation);
                }
                
                // Notify property changed
                OnPropertyChanged(nameof(Operations));
            });
        }
        
        /// <summary>
        /// Handles the Tick event of the refresh timer
        /// </summary>
        private void RefreshTimer_Tick(object sender, EventArgs e)
        {
            // Refresh the operations list
            RefreshOperations();
        }
        
        /// <summary>
        /// Handles the OperationStarted event of the monitor
        /// </summary>
        private void Monitor_OperationStarted(object sender, FlashOperationEventArgs e)
        {
            // Add the operation to the list
            Dispatcher.Invoke(() =>
            {
                _operations.Add(e.Operation);
                OnPropertyChanged(nameof(Operations));
            });
        }
        
        /// <summary>
        /// Handles the OperationUpdated event of the monitor
        /// </summary>
        private void Monitor_OperationUpdated(object sender, FlashOperationEventArgs e)
        {
            // Find the operation in the list and update it
            Dispatcher.Invoke(() =>
            {
                var operation = _operations.FirstOrDefault(o => o.Id == e.Operation.Id);
                if (operation != null)
                {
                    // Update the operation properties
                    operation.BytesProcessed = e.Operation.BytesProcessed;
                    operation.Status = e.Operation.Status;
                    operation.LastUpdated = e.Operation.LastUpdated;
                    operation.AdditionalInfo = e.Operation.AdditionalInfo;
                    
                    // Notify property changed
                    OnPropertyChanged(nameof(Operations));
                }
            });
        }
        
        /// <summary>
        /// Handles the OperationCompleted event of the monitor
        /// </summary>
        private void Monitor_OperationCompleted(object sender, FlashOperationEventArgs e)
        {
            // Find the operation in the list and update it
            Dispatcher.Invoke(() =>
            {
                var operation = _operations.FirstOrDefault(o => o.Id == e.Operation.Id);
                if (operation != null)
                {
                    // Update the operation properties
                    operation.BytesProcessed = e.Operation.BytesProcessed;
                    operation.Status = e.Operation.Status;
                    operation.EndTime = e.Operation.EndTime;
                    operation.LastUpdated = e.Operation.LastUpdated;
                    operation.AdditionalInfo = e.Operation.AdditionalInfo;
                    
                    // Notify property changed
                    OnPropertyChanged(nameof(Operations));
                }
            });
        }
        
        /// <summary>
        /// Handles the OperationFailed event of the monitor
        /// </summary>
        private void Monitor_OperationFailed(object sender, FlashOperationEventArgs e)
        {
            // Find the operation in the list and update it
            Dispatcher.Invoke(() =>
            {
                var operation = _operations.FirstOrDefault(o => o.Id == e.Operation.Id);
                if (operation != null)
                {
                    // Update the operation properties
                    operation.BytesProcessed = e.Operation.BytesProcessed;
                    operation.Status = e.Operation.Status;
                    operation.EndTime = e.Operation.EndTime;
                    operation.LastUpdated = e.Operation.LastUpdated;
                    operation.AdditionalInfo = e.Operation.AdditionalInfo;
                    operation.ErrorMessage = e.Operation.ErrorMessage;
                    
                    // Notify property changed
                    OnPropertyChanged(nameof(Operations));
                }
            });
        }
        
        /// <summary>
        /// Handles the Click event of the Clear Completed button
        /// </summary>
        private void BtnClearCompleted_Click(object sender, RoutedEventArgs e)
        {
            // Clear completed operations from the monitor
            _monitor.ClearCompletedOperations();
            
            // Refresh the operations list
            RefreshOperations();
        }
        
        /// <summary>
        /// Handles the Click event of the Refresh button
        /// </summary>
        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            // Refresh the operations list
            RefreshOperations();
        }
        
        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">The name of the property that changed</param>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        
        /// <summary>
        /// Disposes the control
        /// </summary>
        public void Dispose()
        {
            // Stop the refresh timer
            _refreshTimer.Stop();
            
            // Unsubscribe from monitor events
            _monitor.OperationStarted -= Monitor_OperationStarted;
            _monitor.OperationUpdated -= Monitor_OperationUpdated;
            _monitor.OperationCompleted -= Monitor_OperationCompleted;
            _monitor.OperationFailed -= Monitor_OperationFailed;
        }
    }
    
    /// <summary>
    /// Converts a FlashOperationStatus to a brush color
    /// </summary>
    public class StatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is FlashOperationStatus status)
            {
                switch (status)
                {
                    case FlashOperationStatus.Pending:
                        return new SolidColorBrush(Colors.Gray);
                    case FlashOperationStatus.InProgress:
                        return new SolidColorBrush(Colors.Blue);
                    case FlashOperationStatus.Paused:
                        return new SolidColorBrush(Colors.Orange);
                    case FlashOperationStatus.Stalled:
                        return new SolidColorBrush(Colors.Red);
                    case FlashOperationStatus.Completed:
                        return new SolidColorBrush(Colors.Green);
                    case FlashOperationStatus.Failed:
                        return new SolidColorBrush(Colors.DarkRed);
                    default:
                        return new SolidColorBrush(Colors.Gray);
                }
            }
            
            return new SolidColorBrush(Colors.Gray);
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
    
    /// <summary>
    /// Converts seconds to a TimeSpan
    /// </summary>
    public class SecondsToTimeSpanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is double seconds)
            {
                return TimeSpan.FromSeconds(seconds);
            }
            
            return TimeSpan.Zero;
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}

