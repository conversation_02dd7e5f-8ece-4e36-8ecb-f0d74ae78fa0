using System;
using System.Globalization;
using System.Windows.Data;

namespace VolvoFlashWR.UI.Converters
{
    /// <summary>
    /// Converts a DateTime to a human-readable age string (e.g., "2 days ago", "3 hours ago")
    /// </summary>
    public class DateTimeToAgeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is DateTime dateTime)
            {
                TimeSpan timeSpan = DateTime.Now - dateTime;

                if (timeSpan.TotalDays > 365)
                {
                    int years = (int)(timeSpan.TotalDays / 365);
                    return $"{years} {(years == 1 ? "year" : "years")}";
                }
                else if (timeSpan.TotalDays > 30)
                {
                    int months = (int)(timeSpan.TotalDays / 30);
                    return $"{months} {(months == 1 ? "month" : "months")}";
                }
                else if (timeSpan.TotalDays > 1)
                {
                    int days = (int)timeSpan.TotalDays;
                    return $"{days} {(days == 1 ? "day" : "days")}";
                }
                else if (timeSpan.TotalHours > 1)
                {
                    int hours = (int)timeSpan.TotalHours;
                    return $"{hours} {(hours == 1 ? "hour" : "hours")}";
                }
                else if (timeSpan.TotalMinutes > 1)
                {
                    int minutes = (int)timeSpan.TotalMinutes;
                    return $"{minutes} {(minutes == 1 ? "min" : "mins")}";
                }
                else
                {
                    return "Just now";
                }
            }

            return "Unknown";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
