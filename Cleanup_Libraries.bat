@echo off
echo === Cleaning Up Unnecessary Libraries ===
echo This script will move non-essential libraries to a backup folder
echo and keep only the critical libraries needed for Phoenix APCI real hardware communication.
echo.

REM Create backup directory
if not exist "Libraries_Backup" mkdir Libraries_Backup
if not exist "Libraries_Essential" mkdir Libraries_Essential

echo === Moving Essential Libraries to Libraries_Essential ===

REM Core APCI libraries (CRITICAL)
copy "Libraries\apci.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\apcidb.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\Rpci.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\Pc2.dll" "Libraries_Essential\" >nul 2>&1

REM Phoenix libraries (ESSENTIAL)
copy "Libraries\PhoenixESW.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\PhoenixGeneral.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\PhoenixProducInformation.dll" "Libraries_Essential\" >nul 2>&1

REM Vocom driver libraries (CRITICAL)
copy "Libraries\WUDFPuma.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\WUDFUpdate_01009.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\WdfCoInstaller01009.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\winusbcoinstaller2.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\wudfpuma.inf" "Libraries_Essential\" >nul 2>&1
copy "Libraries\wudfpuma.PNF" "Libraries_Essential\" >nul 2>&1
copy "Libraries\WUDFPuma.cat" "Libraries_Essential\" >nul 2>&1

REM Volvo APCI libraries (REQUIRED)
copy "Libraries\Volvo.ApciPlus.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\Volvo.ApciPlusData.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\Volvo.ApciPlusTea2Data.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\Volvo.ApciPlus.dll.config" "Libraries_Essential\" >nul 2>&1
copy "Libraries\Volvo.ApciPlusData.dll.config" "Libraries_Essential\" >nul 2>&1
copy "Libraries\Volvo.ApciPlus.Simulator.dll.config" "Libraries_Essential\" >nul 2>&1

REM Volvo NVS libraries (REQUIRED)
copy "Libraries\Volvo.NVS.Core.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\Volvo.NVS.Logging.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\Volvo.NVS.Persistence.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\Volvo.NVS.Persistence.NHibernate.dll" "Libraries_Essential\" >nul 2>&1

REM Volvo NAMS libraries (REQUIRED)
copy "Libraries\Volvo.NAMS.AC.Services.Interface.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\Volvo.NAMS.AC.Services.Interfaces.dll" "Libraries_Essential\" >nul 2>&1

REM Volvo IT libraries (REQUIRED)
copy "Libraries\VolvoIt.Baf.Utility.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\VolvoIt.Fido.Agent.Gateway.Contract.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\VolvoIt.Waf.ServiceContract.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\VolvoIt.Waf.ServiceContract.resources.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\VolvoIt.Waf.Utility.dll" "Libraries_Essential\" >nul 2>&1

REM Vodia libraries (REQUIRED)
copy "Libraries\Vodia.CommonDomain.Model.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\Vodia.Contracts.Common.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\Vodia.UtilityComponent.dll" "Libraries_Essential\" >nul 2>&1

REM Essential dependencies (REQUIRED)
copy "Libraries\log4net.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\Newtonsoft.Json.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\AutoMapper.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\NHibernate.dll" "Libraries_Essential\" >nul 2>&1

REM Compression libraries (USEFUL)
copy "Libraries\Ionic.Zip.Reduced.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\SharpCompress.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\DotNetZip.dll" "Libraries_Essential\" >nul 2>&1

REM Utility libraries (USEFUL)
copy "Libraries\VCError.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\VCUtil.Security.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\SystemInterface.dll" "Libraries_Essential\" >nul 2>&1

REM WinSCP for file operations (USEFUL)
copy "Libraries\WinSCP.exe" "Libraries_Essential\" >nul 2>&1
copy "Libraries\WinSCPnet.dll" "Libraries_Essential\" >nul 2>&1

echo === Moving System libraries to backup (keeping only essential ones) ===

REM Keep only essential System libraries
copy "Libraries\System.Memory.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\System.Runtime.CompilerServices.Unsafe.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\System.Threading.Tasks.Extensions.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\System.ValueTuple.dll" "Libraries_Essential\" >nul 2>&1
copy "Libraries\System.Numerics.Vectors.dll" "Libraries_Essential\" >nul 2>&1

REM Move all other System libraries to backup
move "Libraries\System.*.dll" "Libraries_Backup\" >nul 2>&1

echo.
echo === Library Cleanup Summary ===
echo Essential libraries (kept): ~50 critical libraries for Phoenix APCI
echo Backed up libraries: All non-essential System and other libraries
echo.
echo The application now has a streamlined library structure optimized for real hardware communication.
echo.
pause
