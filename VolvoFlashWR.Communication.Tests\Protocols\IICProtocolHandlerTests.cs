using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;

using VolvoFlashWR.Communication.Protocols;
using VolvoFlashWR.Communication.Vocom;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Protocols
{
    [TestFixture]
    public class IICProtocolHandlerTests
    {
        private Mock<ILoggingService> _mockLogger;
        private Mock<IVocomService> _mockVocomService;
        private IICProtocolHandler _iicProtocolHandler;
        private VocomDevice _mockVocomDevice;
        private ECUDevice _mockECU;

        [SetUp]
        public void Setup()
        {
            // Create mock logger
            _mockLogger = new Mock<ILoggingService>();

            // Create mock Vocom service
            _mockVocomService = new Mock<IVocomService>();

            // Create mock Vocom device
            _mockVocomDevice = new VocomDevice
            {
                Id = "test-device-id",
                SerialNumber = "88890300",
                ConnectionType = VocomConnectionType.USB,
                ConnectionStatus = VocomConnectionStatus.Connected
            };

            // Configure mock Vocom service
            _mockVocomService.Setup(s => s.CurrentDevice).Returns(_mockVocomDevice);

            // Create mock ECU
            _mockECU = new ECUDevice
            {
                Id = "test-ecu-id",
                Name = "Test ECU",
                SerialNumber = "TEST-123",
                ConnectionStatus = ECUConnectionStatus.Disconnected,
                ProtocolType = ECUProtocolType.IIC,
                Properties = new Dictionary<string, object>
                {
                    { "SupportFastModeIIC", true },
                    { "IICAddress", 42 },
                    { "IICSpeed", "Fast" }
                }
            };

            // Create IIC protocol handler
            _iicProtocolHandler = new IICProtocolHandler(_mockLogger.Object, _mockVocomService.Object);
        }

        [Test]
        public async Task InitializeAsync_ShouldReturnTrue_WhenVocomDeviceIsConnected()
        {
            // Act
            bool result = await _iicProtocolHandler.InitializeAsync();

            // Assert
            Assert.That(result, Is.True, "IIC protocol handler initialization should succeed when Vocom device is connected");
        }

        [Test]
        public async Task InitializeAsync_ShouldReturnFalse_WhenVocomDeviceIsNotConnected()
        {
            // Arrange
            _mockVocomDevice.ConnectionStatus = VocomConnectionStatus.Disconnected;

            // Act
            bool result = await _iicProtocolHandler.InitializeAsync();

            // Assert
            ClassicAssert.That(result, Is.False);
        }

        [Test]
        public async Task ConnectAsync_ShouldReturnTrue_WhenECUIsValid()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();

            // Act
            bool result = await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.True);
        }

        [Test]
        public async Task ConnectAsync_ShouldReturnFalse_WhenECUIsNull()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();

            // Act
            bool result = await _iicProtocolHandler.ConnectAsync(null);

            // Assert
            ClassicAssert.That(result, Is.False);
        }

        [Test]
        public async Task ConnectAsync_ShouldReturnFalse_WhenECUProtocolTypeIsNotIIC()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            _mockECU.ProtocolType = ECUProtocolType.CAN;

            // Act
            bool result = await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.False);
        }

        [Test]
        public async Task DisconnectAsync_ShouldReturnTrue_WhenECUIsValid()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Act
            bool result = await _iicProtocolHandler.DisconnectAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.True);
        }

        [Test]
        public async Task DisconnectAsync_ShouldReturnFalse_WhenECUIsNull()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();

            // Act
            bool result = await _iicProtocolHandler.DisconnectAsync(null);

            // Assert
            ClassicAssert.That(result, Is.False);
        }

        [Test]
        public async Task SetOperatingModeAsync_ShouldReturnTrue_WhenInitialized()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();

            // Act
            bool result = await _iicProtocolHandler.SetOperatingModeAsync(OperatingMode.Bench);

            // Assert
            ClassicAssert.That(result, Is.True);
        }

        [Test]
        public async Task SetOperatingModeAsync_ShouldReturnFalse_WhenNotInitialized()
        {
            // Act
            bool result = await _iicProtocolHandler.SetOperatingModeAsync(OperatingMode.Bench);

            // Assert
            ClassicAssert.That(result, Is.False);
        }

        [Test]
        public async Task ReadEEPROMAsync_ShouldReturnData_WhenECUIsValid()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Act
            byte[] result = await _iicProtocolHandler.ReadEEPROMAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.Not.Null);
            ClassicAssert.Greater(result.Length, 0, "EEPROM data should have a length greater than 0");
        }

        [Test]
        public async Task ReadEEPROMAsync_ShouldReturnNull_WhenECUIsNull()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();

            // Act
            byte[] result = await _iicProtocolHandler.ReadEEPROMAsync(null);

            // Assert
            ClassicAssert.That(result, Is.Null);
        }

        [Test]
        public async Task WriteEEPROMAsync_ShouldReturnTrue_WhenDataIsValid()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);
            byte[] data = new byte[1024]; // 1KB of data

            // Act
            bool result = await _iicProtocolHandler.WriteEEPROMAsync(_mockECU, data);

            // Assert
            ClassicAssert.That(result, Is.True);
        }

        [Test]
        public async Task WriteEEPROMAsync_ShouldReturnFalse_WhenDataIsNull()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Act
            bool result = await _iicProtocolHandler.WriteEEPROMAsync(_mockECU, null);

            // Assert
            ClassicAssert.That(result, Is.False);
        }

        [Test]
        public async Task ReadMicrocontrollerCodeAsync_ShouldReturnData_WhenECUIsValid()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Act
            byte[] result = await _iicProtocolHandler.ReadMicrocontrollerCodeAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.Not.Null);
            ClassicAssert.Greater(result.Length, 0, "Microcontroller code should have a length greater than 0");
        }

        [Test]
        public async Task WriteMicrocontrollerCodeAsync_ShouldReturnTrue_WhenDataIsValid()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);
            byte[] code = new byte[1024]; // 1KB of code

            // Act
            bool result = await _iicProtocolHandler.WriteMicrocontrollerCodeAsync(_mockECU, code);

            // Assert
            ClassicAssert.That(result, Is.True);
        }

        [Test]
        public async Task ReadActiveFaultsAsync_ShouldReturnFaults_WhenECUIsValid()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Act
            List<ECUFault> result = await _iicProtocolHandler.ReadActiveFaultsAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.Not.Null);
        }

        [Test]
        public async Task ReadInactiveFaultsAsync_ShouldReturnFaults_WhenECUIsValid()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Act
            List<ECUFault> result = await _iicProtocolHandler.ReadInactiveFaultsAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.Not.Null);
        }

        [Test]
        public async Task ClearFaultsAsync_ShouldReturnTrue_WhenECUIsValid()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Act
            bool result = await _iicProtocolHandler.ClearFaultsAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.True);
        }

        [Test]
        public async Task ReadParametersAsync_ShouldReturnParameters_WhenECUIsValid()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Act
            Dictionary<string, object> result = await _iicProtocolHandler.ReadParametersAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.Not.Null);
            ClassicAssert.Greater(result.Count, 0, "Parameters should have a count greater than 0");
        }

        [Test]
        public async Task WriteParametersAsync_ShouldReturnTrue_WhenParametersAreValid()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);
            Dictionary<string, object> parameters = new Dictionary<string, object>
            {
                { "Parameter1", 123 },
                { "Parameter2", "Value" }
            };

            // Act
            bool result = await _iicProtocolHandler.WriteParametersAsync(_mockECU, parameters);

            // Assert
            ClassicAssert.That(result, Is.True);
        }

        [Test]
        public async Task PerformDiagnosticSessionAsync_ShouldReturnDiagnosticData_WhenECUIsValid()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Act
            DiagnosticData result = await _iicProtocolHandler.PerformDiagnosticSessionAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.Not.Null);
            ClassicAssert.That(result.IsValid, Is.True);
            ClassicAssert.That(result.ECUId, Is.EqualTo(_mockECU.Id));
            ClassicAssert.That(result.Timestamp, Is.Not.EqualTo(default(DateTime)));
        }

        [Test]
        public async Task ConnectAsync_ShouldSetECUConnectionStatus_WhenSuccessful()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            _mockECU.ConnectionStatus = ECUConnectionStatus.Disconnected;

            // Act
            bool result = await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.True);
            ClassicAssert.That(_mockECU.ConnectionStatus, Is.EqualTo(ECUConnectionStatus.Connected));
        }

        [Test]
        public async Task ConnectAsync_ShouldUpdateECUProperties_WhenSuccessful()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            _mockECU.Properties.Clear(); // Clear existing properties

            // Act
            bool result = await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.True);
            ClassicAssert.That(_mockECU.Properties.ContainsKey("IICSpeed"), Is.True, "ECU properties should contain IICSpeed");
        }

        [Test]
        public async Task ReadEEPROMAsync_ShouldReturnNull_WhenECUIsNotConnected()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            _mockECU.ConnectionStatus = ECUConnectionStatus.Disconnected;

            // Act
            byte[] result = await _iicProtocolHandler.ReadEEPROMAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.Null);
        }

        [Test]
        public async Task WriteEEPROMAsync_ShouldReturnFalse_WhenECUIsNotConnected()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            _mockECU.ConnectionStatus = ECUConnectionStatus.Disconnected;
            byte[] data = new byte[1024]; // 1KB of data

            // Act
            bool result = await _iicProtocolHandler.WriteEEPROMAsync(_mockECU, data);

            // Assert
            ClassicAssert.That(result, Is.False);
        }

        [Test]
        public async Task WriteEEPROMAsync_ShouldReturnFalse_WhenDataExceedsMaximumSize()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Create data that exceeds the maximum EEPROM size
            // The BaseECUProtocolHandler defines EEPROM_SIZE as 65536 (64KB)
            byte[] data = new byte[70000]; // 70KB of data

            // Act
            bool result = await _iicProtocolHandler.WriteEEPROMAsync(_mockECU, data);

            // Assert
            ClassicAssert.That(result, Is.False);
        }

        [Test]
        public async Task ReadEEPROMAsync_ShouldReadInBlocks_WhenECUIsValid()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);

            // Act
            byte[] result = await _iicProtocolHandler.ReadEEPROMAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.Not.Null);
            ClassicAssert.That(result.Length, Is.GreaterThan(0));
        }

        [Test]
        public async Task WriteEEPROMAsync_ShouldWriteInBlocks_WhenECUIsValid()
        {
            // Arrange
            await _iicProtocolHandler.InitializeAsync();
            await _iicProtocolHandler.ConnectAsync(_mockECU);
            byte[] data = new byte[4096]; // 4KB of data

            // Act
            bool result = await _iicProtocolHandler.WriteEEPROMAsync(_mockECU, data);

            // Assert
            ClassicAssert.That(result, Is.True);
        }
    }
}


