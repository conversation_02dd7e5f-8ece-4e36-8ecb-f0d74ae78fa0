<UserControl x:Class="VolvoFlashWR.UI.Views.BackupVersionsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:VolvoFlashWR.UI.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2" 
                   Text="Backup Version History" 
                   FontSize="20" FontWeight="Bold" Margin="10"/>
        
        <!-- Version Tree -->
        <GroupBox Grid.Row="1" Grid.Column="0" Header="Version Tree" Margin="5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="{Binding SelectedBackup.ECUName}" 
                           FontWeight="Bold" Margin="5"/>
                
                <TreeView Grid.Row="1" ItemsSource="{Binding VersionTree.VersionNodes}" 
                          SelectedItemChanged="VersionTree_SelectedItemChanged">
                    <TreeView.ItemTemplate>
                        <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{Binding Backup.Version, StringFormat='v{0}'}" FontWeight="Bold"/>
                                <TextBlock Text=" - " />
                                <TextBlock Text="{Binding Backup.VersionCreationTime, StringFormat='{}{0:yyyy-MM-dd HH:mm}'}" />
                                <TextBlock Text=" " />
                                <TextBlock Text="(Latest)" Foreground="Green" 
                                           Visibility="{Binding Backup.IsLatestVersion, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </StackPanel>
                        </HierarchicalDataTemplate>
                    </TreeView.ItemTemplate>
                </TreeView>
            </Grid>
        </GroupBox>
        
        <!-- Version Details -->
        <GroupBox Grid.Row="1" Grid.Column="1" Header="Version Details" Margin="5">
            <ScrollViewer>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Version Info -->
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Version:" Margin="5" FontWeight="Bold"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding SelectedVersion.Version}" Margin="5"/>
                    
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Created:" Margin="5" FontWeight="Bold"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SelectedVersion.VersionCreationTime, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" Margin="5"/>
                    
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Created By:" Margin="5" FontWeight="Bold"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding SelectedVersion.CreatedBy}" Margin="5"/>
                    
                    <TextBlock Grid.Row="3" Grid.Column="0" Text="Description:" Margin="5" FontWeight="Bold"/>
                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding SelectedVersion.Description}" Margin="5" TextWrapping="Wrap"/>
                    
                    <TextBlock Grid.Row="4" Grid.Column="0" Text="Version Notes:" Margin="5" FontWeight="Bold"/>
                    <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding SelectedVersion.VersionNotes}" Margin="5" TextWrapping="Wrap"/>
                    
                    <TextBlock Grid.Row="5" Grid.Column="0" Text="Category:" Margin="5" FontWeight="Bold"/>
                    <TextBlock Grid.Row="5" Grid.Column="1" Text="{Binding SelectedVersion.Category}" Margin="5"/>
                    
                    <TextBlock Grid.Row="6" Grid.Column="0" Text="Tags:" Margin="5" FontWeight="Bold"/>
                    <ItemsControl Grid.Row="6" Grid.Column="1" ItemsSource="{Binding SelectedVersion.Tags}" Margin="5">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#FFE0E0E0" CornerRadius="3" Margin="2">
                                    <TextBlock Text="{Binding}" Margin="5,2"/>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                    
                    <!-- Data Summary -->
                    <GroupBox Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="2" Header="Data Summary" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="EEPROM Data:" Margin="5" FontWeight="Bold"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" 
                                       Text="{Binding SelectedVersion.EEPROMData.Length, StringFormat='{}{0:N0} bytes'}" 
                                       Margin="5"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Microcontroller Code:" Margin="5" FontWeight="Bold"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" 
                                       Text="{Binding SelectedVersion.MicrocontrollerCode.Length, StringFormat='{}{0:N0} bytes'}" 
                                       Margin="5"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Parameters:" Margin="5" FontWeight="Bold"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" 
                                       Text="{Binding ParameterCount, StringFormat='{}{0:N0} parameters'}" 
                                       Margin="5"/>
                            
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="Total Size:" Margin="5" FontWeight="Bold"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" 
                                       Text="{Binding SelectedVersion.SizeInBytes, StringFormat='{}{0:N0} bytes'}" 
                                       Margin="5"/>
                        </Grid>
                    </GroupBox>
                </Grid>
            </ScrollViewer>
        </GroupBox>
        
        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" 
                    Orientation="Horizontal" HorizontalAlignment="Right" Margin="10">
            <Button Content="Create New Version" Command="{Binding CreateNewVersionCommand}" 
                    Margin="5" Padding="10,5"/>
            <Button Content="Compare Versions" Command="{Binding CompareVersionsCommand}" 
                    Margin="5" Padding="10,5"/>
            <Button Content="Merge Versions" Command="{Binding MergeVersionsCommand}" 
                    Margin="5" Padding="10,5"/>
            <Button Content="Restore This Version" Command="{Binding RestoreVersionCommand}" 
                    Margin="5" Padding="10,5"/>
            <Button Content="Close" Command="{Binding CloseCommand}" 
                    Margin="5" Padding="10,5"/>
        </StackPanel>
    </Grid>
</UserControl>
