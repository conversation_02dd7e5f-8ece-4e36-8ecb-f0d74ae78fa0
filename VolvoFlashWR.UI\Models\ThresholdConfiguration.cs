namespace VolvoFlashWR.UI.Models
{
    /// <summary>
    /// Represents a threshold configuration for a parameter
    /// </summary>
    public class ThresholdConfiguration
    {
        /// <summary>
        /// The parameter name this threshold applies to
        /// </summary>
        public string ParameterName { get; set; } = string.Empty;

        /// <summary>
        /// The minimum normal value
        /// </summary>
        public double MinNormal { get; set; } = double.MinValue;

        /// <summary>
        /// The maximum normal value
        /// </summary>
        public double MaxNormal { get; set; } = double.MaxValue;

        /// <summary>
        /// The minimum warning value
        /// </summary>
        public double MinWarning { get; set; } = double.MinValue;

        /// <summary>
        /// The maximum warning value
        /// </summary>
        public double MaxWarning { get; set; } = double.MaxValue;

        /// <summary>
        /// The minimum error value
        /// </summary>
        public double MinError { get; set; } = double.MinValue;

        /// <summary>
        /// The maximum error value
        /// </summary>
        public double MaxError { get; set; } = double.MaxValue;

        /// <summary>
        /// Default constructor
        /// </summary>
        public ThresholdConfiguration()
        {
        }

        /// <summary>
        /// Constructor with parameter name
        /// </summary>
        /// <param name="parameterName">The name of the parameter</param>
        public ThresholdConfiguration(string parameterName)
        {
            ParameterName = parameterName;
        }

        /// <summary>
        /// Determines the status of a value based on the thresholds
        /// </summary>
        /// <param name="value">The value to check</param>
        /// <returns>The status as a string (Normal, Warning, Error)</returns>
        public string GetStatus(double value)
        {
            // First check if the value is within the error range
            if (value >= MinError && value <= MaxError)
            {
                // If the value is outside the warning range, it's an error
                if (value < MinWarning || value > MaxWarning)
                {
                    return "Error";
                }
            }

            // Next check if the value is within the warning range
            if (value >= MinWarning && value <= MaxWarning)
            {
                // If the value is outside the normal range, it's a warning
                if (value < MinNormal || value > MaxNormal)
                {
                    return "Warning";
                }
            }

            // If the value is within the normal range, it's normal
            if (value >= MinNormal && value <= MaxNormal)
            {
                return "Normal";
            }

            // If we get here, the value doesn't fit into any of the defined ranges
            return "Unknown";
        }

        /// <summary>
        /// Sets the normal range
        /// </summary>
        /// <param name="min">Minimum normal value</param>
        /// <param name="max">Maximum normal value</param>
        public void SetNormalRange(double min, double max)
        {
            MinNormal = min;
            MaxNormal = max;
        }

        /// <summary>
        /// Sets the warning range
        /// </summary>
        /// <param name="min">Minimum warning value</param>
        /// <param name="max">Maximum warning value</param>
        public void SetWarningRange(double min, double max)
        {
            MinWarning = min;
            MaxWarning = max;
        }

        /// <summary>
        /// Sets the error range
        /// </summary>
        /// <param name="min">Minimum error value</param>
        /// <param name="max">Maximum error value</param>
        public void SetErrorRange(double min, double max)
        {
            MinError = min;
            MaxError = max;
        }
    }
}
