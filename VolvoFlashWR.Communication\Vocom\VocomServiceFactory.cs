using System;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using System.Linq;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Factory for creating and initializing Vocom services
    /// </summary>
    public class VocomServiceFactory
    {
        private readonly ILoggingService _logger;

        /// <summary>
        /// Initializes a new instance of the VocomServiceFactory class
        /// </summary>
        /// <param name="logger">The logging service</param>
        public VocomServiceFactory(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Creates and initializes a new Vocom service with default settings
        /// </summary>
        /// <returns>The initialized Vocom service</returns>
        public async Task<IVocomService> CreateServiceAsync()
        {
            try
            {
                _logger.LogInformation("Creating Vocom service with default settings", "VocomServiceFactory");

                // Check if we should use dummy implementations
                string dummyEnv = Environment.GetEnvironmentVariable("USE_DUMMY_IMPLEMENTATIONS");
                bool useDummyImplementations = !string.IsNullOrEmpty(dummyEnv) && dummyEnv.ToLower() != "false";

                if (useDummyImplementations)
                {
                    _logger.LogInformation("Using dummy Vocom service implementation based on environment variable", "VocomServiceFactory");
                    var dummyService = new DummyVocomService(_logger);
                    await dummyService.InitializeAsync();
                    return dummyService;
                }

                // Check if Phoenix Vocom adapter is enabled
                string phoenixEnabledEnv = Environment.GetEnvironmentVariable("PHOENIX_VOCOM_ENABLED");
                bool usePhoenixAdapter = !string.IsNullOrEmpty(phoenixEnabledEnv) && phoenixEnabledEnv.ToLower() != "false";

                PhoenixVocomAdapter phoenixAdapter = null;
                bool phoenixInitialized = false;

                if (usePhoenixAdapter)
                {
                    // Try to create a Phoenix Vocom adapter first
                    _logger.LogInformation("Phoenix Vocom adapter enabled, attempting to create it", "VocomServiceFactory");
                    phoenixAdapter = new PhoenixVocomAdapter(_logger);
                    phoenixInitialized = await phoenixAdapter.InitializeAsync();
                }
                else
                {
                    _logger.LogInformation("Phoenix Vocom adapter not enabled, skipping", "VocomServiceFactory");
                }

                if (phoenixInitialized)
                {
                    _logger.LogInformation("Phoenix Vocom adapter initialized successfully", "VocomServiceFactory");

                    // Create WiFi and Bluetooth services
                    var wifiService = new WiFiCommunicationService(_logger);
                    var bluetoothService = new BluetoothCommunicationService(_logger);

                    // Create the service with default settings
                    var service = new VocomService(_logger);

                    // Initialize the service with dependencies
                    var usbService = new USBCommunicationService(_logger);
                    bool initialized = await service.InitializeAsync(phoenixAdapter, usbService, wifiService, bluetoothService);
                    if (!initialized)
                    {
                        _logger.LogError("Failed to initialize Vocom service with Phoenix adapter", "VocomServiceFactory");
                        return null;
                    }

                    _logger.LogInformation("Vocom service created and initialized successfully with Phoenix adapter", "VocomServiceFactory");
                    return service;
                }

                // If Phoenix adapter fails, try the standard Vocom driver
                _logger.LogInformation("Phoenix adapter initialization failed, attempting to create standard Vocom driver", "VocomServiceFactory");
                var vocomDriver = new VocomDriver(_logger);
                bool driverInitialized = await vocomDriver.InitializeAsync();

                if (!driverInitialized)
                {
                    _logger.LogWarning("Failed to initialize standard Vocom driver, falling back to device driver", "VocomServiceFactory");

                    // Try the device driver as fallback
                    var deviceDriver = new VocomDeviceDriver(_logger);
                    bool deviceDriverInitialized = await deviceDriver.InitializeAsync();

                    if (!deviceDriverInitialized)
                    {
                        _logger.LogError("Failed to initialize Vocom device driver", "VocomServiceFactory");
                        return null;
                    }

                    // Create WiFi and Bluetooth services
                    var wifiService = new WiFiCommunicationService(_logger);
                    var bluetoothService = new BluetoothCommunicationService(_logger);

                    // Create the service with default settings
                    var service = new VocomService(_logger);

                    // Initialize the service with dependencies
                    bool initialized = await service.InitializeAsync(deviceDriver, new USBCommunicationService(_logger), wifiService, bluetoothService);
                    if (!initialized)
                    {
                        _logger.LogError("Failed to initialize Vocom service", "VocomServiceFactory");
                        return null;
                    }

                    _logger.LogInformation("Vocom service created and initialized successfully with device driver", "VocomServiceFactory");
                    return service;
                }
                else
                {
                    // Create WiFi and Bluetooth services
                    var wifiService = new WiFiCommunicationService(_logger);
                    var bluetoothService = new BluetoothCommunicationService(_logger);

                    // Create the service with default settings
                    var service = new VocomService(_logger);

                    // Initialize the service with dependencies using modern USB service
                    var usbService = new ModernUSBCommunicationService(_logger);
                    bool initialized = await service.InitializeAsync(vocomDriver as IVocomDeviceDriver, usbService, wifiService, bluetoothService);
                    if (!initialized)
                    {
                        _logger.LogError("Failed to initialize Vocom service", "VocomServiceFactory");
                        return null;
                    }

                    _logger.LogInformation("Vocom service created and initialized successfully with real driver", "VocomServiceFactory");
                    return service;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error creating Vocom service", "VocomServiceFactory", ex);
                return null;
            }
        }

        /// <summary>
        /// Creates and initializes a new Vocom service with custom connection settings
        /// </summary>
        /// <param name="connectionSettings">Custom connection settings</param>
        /// <returns>The initialized Vocom service</returns>
        public async Task<IVocomService> CreateServiceWithSettingsAsync(ConnectionSettings connectionSettings)
        {
            try
            {
                _logger.LogInformation("Creating Vocom service with custom settings", "VocomServiceFactory");

                // Check if Phoenix Vocom adapter is enabled
                string phoenixEnabledEnv = Environment.GetEnvironmentVariable("PHOENIX_VOCOM_ENABLED");
                bool usePhoenixAdapter = !string.IsNullOrEmpty(phoenixEnabledEnv) && phoenixEnabledEnv.ToLower() != "false";

                PhoenixVocomAdapter phoenixAdapter = null;
                bool phoenixInitialized = false;

                if (usePhoenixAdapter)
                {
                    // Try to create a Phoenix Vocom adapter first
                    _logger.LogInformation("Phoenix Vocom adapter enabled, attempting to create it with custom settings", "VocomServiceFactory");
                    phoenixAdapter = new PhoenixVocomAdapter(_logger);
                    phoenixInitialized = await phoenixAdapter.InitializeAsync();
                }
                else
                {
                    _logger.LogInformation("Phoenix Vocom adapter not enabled, skipping", "VocomServiceFactory");
                }

                if (phoenixInitialized)
                {
                    _logger.LogInformation("Phoenix Vocom adapter initialized successfully", "VocomServiceFactory");

                        // Create WiFi and Bluetooth services
                    var phoenixWifiService = new WiFiCommunicationService(_logger);
                    var phoenixBluetoothService = new BluetoothCommunicationService(_logger);

                    // Create the service with default settings
                    var phoenixService = new VocomService(_logger);

                    // Initialize the service with dependencies
                    var phoenixUsbService = new USBCommunicationService(_logger);
                    bool phoenixServiceInitialized = await phoenixService.InitializeAsync(phoenixAdapter, phoenixUsbService, phoenixWifiService, phoenixBluetoothService);
                    if (!phoenixServiceInitialized)
                    {
                        _logger.LogError("Failed to initialize Vocom service with Phoenix adapter", "VocomServiceFactory");
                        return null;
                    }

                    // Update connection settings
                    phoenixService.UpdateConnectionSettings(connectionSettings);

                    _logger.LogInformation("Vocom service created and initialized successfully with Phoenix adapter and custom settings", "VocomServiceFactory");
                    return phoenixService;
                }

                // If Phoenix adapter fails, fall back to the device driver
                _logger.LogWarning("Phoenix adapter initialization failed, falling back to device driver", "VocomServiceFactory");
                var vocomDriver = new VocomDeviceDriver(_logger);
                bool driverInitialized = await vocomDriver.InitializeAsync();
                if (!driverInitialized)
                {
                    _logger.LogError("Failed to initialize Vocom device driver", "VocomServiceFactory");
                    return null;
                }

                // Create WiFi and Bluetooth services (placeholder implementations)
                var wifiService = new WiFiCommunicationService(_logger);
                var bluetoothService = new BluetoothCommunicationService(_logger);

                // Create the service
                var service = new VocomService(_logger);

                // Initialize the service with dependencies
                var usbService = new USBCommunicationService(_logger);
                bool initialized = await service.InitializeAsync(vocomDriver, usbService, wifiService, bluetoothService);
                if (!initialized)
                {
                    _logger.LogError("Failed to initialize Vocom service with custom settings", "VocomServiceFactory");
                    return null;
                }

                // Update connection settings
                service.UpdateConnectionSettings(connectionSettings);

                _logger.LogInformation("Vocom service created and initialized successfully with custom settings", "VocomServiceFactory");
                return service;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error creating Vocom service with custom settings", "VocomServiceFactory", ex);
                return null;
            }
        }
    }
}
