using System;
using MsLogLevel = Microsoft.Extensions.Logging.LogLevel;
using MsILogger = Microsoft.Extensions.Logging.ILogger;
using MsEventId = Microsoft.Extensions.Logging.EventId;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Utilities
{
    /// <summary>
    /// Adapter class to convert ILoggingService to Microsoft.Extensions.Logging.ILogger
    /// </summary>
    public class LoggingServiceAdapter : MsILogger
    {
        private readonly ILoggingService _loggingService;
        private readonly string _categoryName;

        /// <summary>
        /// Initializes a new instance of the LoggingServiceAdapter class
        /// </summary>
        /// <param name="loggingService">The logging service to adapt</param>
        /// <param name="categoryName">The category name for the logger</param>
        public LoggingServiceAdapter(ILoggingService loggingService, string categoryName)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _categoryName = categoryName ?? throw new ArgumentNullException(nameof(categoryName));
        }

        /// <summary>
        /// Begins a logical operation scope
        /// </summary>
        /// <typeparam name="TState">The type of the state to begin scope for</typeparam>
        /// <param name="state">The identifier for the scope</param>
        /// <returns>A disposable object that ends the logical operation scope on dispose</returns>
        public IDisposable BeginScope<TState>(TState state)
        {
            return new NoopDisposable();
        }

        /// <summary>
        /// Checks if the given logLevel is enabled
        /// </summary>
        /// <param name="logLevel">Level to be checked</param>
        /// <returns>True if enabled, false otherwise</returns>
        public bool IsEnabled(MsLogLevel logLevel)
        {
            return true; // Always enabled for now
        }

        /// <summary>
        /// Writes a log entry
        /// </summary>
        /// <typeparam name="TState">The type of the object to be written</typeparam>
        /// <param name="logLevel">Entry will be written on this level</param>
        /// <param name="eventId">Id of the event</param>
        /// <param name="state">The entry to be written</param>
        /// <param name="exception">The exception related to this entry</param>
        /// <param name="formatter">Function to create a string message of the state and exception</param>
        public void Log<TState>(MsLogLevel logLevel, MsEventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
        {
            if (!IsEnabled(logLevel))
            {
                return;
            }

            string message = formatter(state, exception);

            switch (logLevel)
            {
                case MsLogLevel.Trace:
                case MsLogLevel.Debug:
                    _loggingService.LogDebug(message, _categoryName);
                    break;
                case MsLogLevel.Information:
                    _loggingService.LogInformation(message, _categoryName);
                    break;
                case MsLogLevel.Warning:
                    _loggingService.LogWarning(message, _categoryName);
                    break;
                case MsLogLevel.Error:
                case MsLogLevel.Critical:
                    _loggingService.LogError(message, _categoryName, exception);
                    break;
                default:
                    _loggingService.LogInformation(message, _categoryName);
                    break;
            }
        }

        /// <summary>
        /// A no-op disposable for the BeginScope method
        /// </summary>
        private class NoopDisposable : IDisposable
        {
            public void Dispose()
            {
                // No-op
            }
        }
    }

    /// <summary>
    /// Factory for creating LoggingServiceAdapter instances
    /// </summary>
    public class LoggingServiceAdapterFactory
    {
        private readonly ILoggingService _loggingService;

        /// <summary>
        /// Initializes a new instance of the LoggingServiceAdapterFactory class
        /// </summary>
        /// <param name="loggingService">The logging service to adapt</param>
        public LoggingServiceAdapterFactory(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        /// <summary>
        /// Creates a new LoggingServiceAdapter instance
        /// </summary>
        /// <param name="categoryName">The category name for the logger</param>
        /// <returns>A new LoggingServiceAdapter instance</returns>
        public MsILogger CreateLogger(string categoryName)
        {
            return new LoggingServiceAdapter(_loggingService, categoryName);
        }
    }
}
