using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.UI.Tests.Helpers;
using VolvoFlashWR.UI.ViewModels;

namespace VolvoFlashWR.UI.Tests.ViewModels
{
    [TestFixture]
    public class MainViewModelConfigurationTests
    {
        private Mock<ILoggingService> _mockLoggingService;
        private Mock<IAppConfigurationService> _mockConfigurationService;
        private Mock<IVocomService> _mockVocomService;
        private Mock<IECUCommunicationService> _mockEcuCommunicationService;
        private Mock<IBackupService> _mockBackupService;
        private Mock<IBackupSchedulerService> _mockBackupSchedulerService;
        private MainViewModel _viewModel;

        [SetUp]
        public void Setup()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockConfigurationService = new Mock<IAppConfigurationService>();
            _mockVocomService = new Mock<IVocomService>();
            _mockEcuCommunicationService = new Mock<IECUCommunicationService>();
            _mockBackupService = new Mock<IBackupService>();
            _mockBackupSchedulerService = new Mock<IBackupSchedulerService>();

            // Setup default configuration values
            _mockConfigurationService.Setup(m => m.GetValue<string>("UI.Theme", It.IsAny<string>())).Returns("Light");
            _mockConfigurationService.Setup(m => m.GetValue<string>("UI.Language", It.IsAny<string>())).Returns("en-US");
            _mockConfigurationService.Setup(m => m.GetValue<bool>("Logging.DetailedLogging", It.IsAny<bool>())).Returns(false);
            _mockConfigurationService.Setup(m => m.GetValue<bool>("Backup.UseCompression", It.IsAny<bool>())).Returns(true);
            _mockConfigurationService.Setup(m => m.GetValue<bool>("Backup.UseEncryption", It.IsAny<bool>())).Returns(false);
            _mockConfigurationService.Setup(m => m.GetValue<int>("Backup.MaxBackupsToKeep", It.IsAny<int>())).Returns(10);
            _mockConfigurationService.Setup(m => m.GetValue<bool>("Vocom.AutoConnect", It.IsAny<bool>())).Returns(true);
            _mockConfigurationService.Setup(m => m.GetValue<bool>("Vocom.UseWiFiFallback", It.IsAny<bool>())).Returns(false);

            // Setup configuration save
            _mockConfigurationService.Setup(m => m.SetValueAsync(It.IsAny<string>(), It.IsAny<object>()))
                .Returns(Task.FromResult(true));
            _mockConfigurationService.Setup(m => m.SaveConfigurationAsync())
                .Returns(Task.FromResult(true));
            _mockConfigurationService.Setup(m => m.ResetToDefaultsAsync())
                .Returns(Task.FromResult(true));

            // Setup backup service
            _mockBackupService.Setup(m => m.GetPredefinedCategoriesAsync())
                .Returns(Task.FromResult(new List<string> { "Category1", "Category2" }));

            // Create mock flash operation monitor service
            var mockFlashOperationMonitorService = new Mock<IFlashOperationMonitorService>();

            // Create the view model
            _viewModel = new MainViewModel(
                _mockLoggingService.Object,
                _mockConfigurationService.Object,
                _mockVocomService.Object,
                _mockEcuCommunicationService.Object,
                _mockBackupService.Object,
                _mockBackupSchedulerService.Object,
                mockFlashOperationMonitorService.Object);
        }

        [Test]
        public void Constructor_InitializesConfigurationProperties()
        {
            // Assert
            Assert.That(_viewModel.UITheme, Is.EqualTo("Light"));
            Assert.That(_viewModel.UILanguage, Is.EqualTo("en-US"));
            Assert.That(_viewModel.DetailedLogging, Is.False);
            Assert.That(_viewModel.UseCompression, Is.True);
            Assert.That(_viewModel.UseEncryption, Is.False);
            Assert.That(_viewModel.MaxBackupsToKeep, Is.EqualTo(10));
            Assert.That(_viewModel.AutoCheckBluetooth, Is.True);
            Assert.That(_viewModel.UseWiFiFallback, Is.False);
        }

        [Test]
        public void UITheme_WhenChanged_UpdatesConfiguration()
        {
            // Act
            _viewModel.UITheme = "Dark";

            // Assert
            _mockConfigurationService.Verify(m => m.SetValueAsync("UI.Theme", "Dark"), Times.Once);
        }

        [Test]
        public void UILanguage_WhenChanged_UpdatesConfiguration()
        {
            // Act
            _viewModel.UILanguage = "fr-FR";

            // Assert
            _mockConfigurationService.Verify(m => m.SetValueAsync("UI.Language", "fr-FR"), Times.Once);
        }

        [Test]
        public void DetailedLogging_WhenChanged_UpdatesConfiguration()
        {
            // Act
            _viewModel.DetailedLogging = true;

            // Assert
            _mockConfigurationService.Verify(m => m.SetValueAsync("Logging.DetailedLogging", true), Times.Once);
        }

        [Test]
        public void UseCompression_WhenChanged_UpdatesConfiguration()
        {
            // Act
            _viewModel.UseCompression = false;

            // Assert
            _mockConfigurationService.Verify(m => m.SetValueAsync("Backup.UseCompression", false), Times.Once);
        }

        [Test]
        public void UseEncryption_WhenChanged_UpdatesConfiguration()
        {
            // Act
            _viewModel.UseEncryption = true;

            // Assert
            _mockConfigurationService.Verify(m => m.SetValueAsync("Backup.UseEncryption", true), Times.Once);
        }

        [Test]
        public void MaxBackupsToKeep_WhenChanged_UpdatesConfiguration()
        {
            // Act
            _viewModel.MaxBackupsToKeep = 20;

            // Assert
            _mockConfigurationService.Verify(m => m.SetValueAsync("Backup.MaxBackupsToKeep", 20), Times.Once);
        }

        [Test]
        public void AutoCheckBluetooth_WhenChanged_UpdatesConfiguration()
        {
            // Act
            _viewModel.AutoCheckBluetooth = false;

            // Assert
            _mockConfigurationService.Verify(m => m.SetValueAsync("Vocom.AutoConnect", false), Times.Once);
        }

        [Test]
        public void UseWiFiFallback_WhenChanged_UpdatesConfiguration()
        {
            // Act
            _viewModel.UseWiFiFallback = true;

            // Assert
            _mockConfigurationService.Verify(m => m.SetValueAsync("Vocom.UseWiFiFallback", true), Times.Once);
        }

        [Test]
        public void OnConfigurationChanged_UpdatesProperties()
        {
            // Arrange
            _mockConfigurationService.Setup(m => m.GetValue<string>("UI.Theme", It.IsAny<string>())).Returns("Dark");

            // Act - simulate configuration change event
            _mockConfigurationService.Raise(m => m.ConfigurationChanged += null, new object(), "UI.Theme");

            // Assert
            Assert.That(_viewModel.UITheme, Is.EqualTo("Dark"));
        }

        [Test]
        public void SaveConfigurationCommand_SavesAllSettings()
        {
            // Act
            _viewModel.SaveConfigurationCommand.Execute(null);

            // Assert
            _mockConfigurationService.Verify(m => m.SaveConfigurationAsync(), Times.Once);
        }

        [Test]
        public void ResetConfigurationCommand_ResetsToDefaults()
        {
            // This test would need to be run in a UI context to handle the MessageBox
            // For unit testing, we can just verify the command exists
            Assert.That(_viewModel.ResetConfigurationCommand, Is.Not.Null);
        }
    }
}

