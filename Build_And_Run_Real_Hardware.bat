@echo off
title VolvoFlashWR - Build and Run Real Hardware Mode
echo === VolvoFlashWR Build and Run Real Hardware Mode ===
echo.

echo Checking for Vocom adapter...
echo.

REM Check if Vocom driver is installed
if not exist "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll" (
    echo ! WARNING: Vocom driver not found in system!
    echo Please install the Vocom driver first.
    echo.
)

REM Check if libraries are present
if not exist "Libraries\WUDFPuma.dll" (
    echo X Critical library missing: WUDFPuma.dll
    echo Please run Configure_Real_Hardware_Libraries.ps1 first.
    pause
    exit /b 1
)

echo + Libraries check passed
echo.

echo Building the application...
echo.

REM Build the application in Release mode
dotnet build --configuration Release --framework net8.0-windows

if %ERRORLEVEL% NEQ 0 (
    echo X Build failed!
    echo Please check for compilation errors.
    pause
    exit /b 1
)

echo + Build successful
echo.

echo Starting VolvoFlashWR in Real Hardware Mode...
echo.

REM Set environment variables for library loading
set PATH=%PATH%;%CD%\Libraries;%CD%\Drivers\Vocom

REM Copy libraries to the build output directory for easier access
if not exist "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries" (
    mkdir "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries"
)

echo Copying critical libraries to build directory...
copy "Libraries\WUDFPuma.dll" "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\" >nul 2>&1
copy "Libraries\apci.dll" "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\" >nul 2>&1
copy "Libraries\Volvo.ApciPlus.dll" "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\" >nul 2>&1

REM Start the application
cd "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64"
start "" "VolvoFlashWR.Launcher.exe" --mode=normal --hardware=real

echo Application started. Check the application window for connection status.
echo.
echo Press any key to return to main directory...
pause >nul

cd ..\..\..\..\..
