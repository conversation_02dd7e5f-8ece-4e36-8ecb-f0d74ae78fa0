using System;

namespace VolvoFlashWR.UI.Models
{
    /// <summary>
    /// Represents an entry in the backup schedule history
    /// </summary>
    public class ScheduleHistoryEntry
    {
        /// <summary>
        /// The name of the schedule
        /// </summary>
        public string ScheduleName { get; set; } = string.Empty;

        /// <summary>
        /// The name of the ECU
        /// </summary>
        public string ECUName { get; set; } = string.Empty;

        /// <summary>
        /// The time when the schedule was executed
        /// </summary>
        public DateTime ExecutionTime { get; set; } = DateTime.Now;

        /// <summary>
        /// The status of the execution (Success, Failed, Skipped)
        /// </summary>
        public string Status { get; set; } = "Unknown";

        /// <summary>
        /// The ID of the created backup (if successful)
        /// </summary>
        public string BackupId { get; set; } = string.Empty;

        /// <summary>
        /// Additional details about the execution
        /// </summary>
        public string Details { get; set; } = string.Empty;

        /// <summary>
        /// Default constructor
        /// </summary>
        public ScheduleHistoryEntry()
        {
            // Properties already initialized with default values
        }

        /// <summary>
        /// Constructor with schedule name and ECU name
        /// </summary>
        /// <param name="scheduleName">The name of the schedule</param>
        /// <param name="ecuName">The name of the ECU</param>
        public ScheduleHistoryEntry(string scheduleName, string ecuName)
        {
            ScheduleName = scheduleName;
            ECUName = ecuName;
            ExecutionTime = DateTime.Now;
        }

        /// <summary>
        /// Constructor with schedule name, ECU name, and status
        /// </summary>
        /// <param name="scheduleName">The name of the schedule</param>
        /// <param name="ecuName">The name of the ECU</param>
        /// <param name="status">The status of the execution</param>
        public ScheduleHistoryEntry(string scheduleName, string ecuName, string status)
        {
            ScheduleName = scheduleName;
            ECUName = ecuName;
            ExecutionTime = DateTime.Now;
            Status = status;
        }
    }
}
