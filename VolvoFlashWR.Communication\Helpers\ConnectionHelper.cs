using System;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Communication.Helpers
{
    /// <summary>
    /// Helper class for connection operations
    /// </summary>
    public static class ConnectionHelper
    {
        /// <summary>
        /// Executes an operation with retry logic
        /// </summary>
        /// <param name="operation">The operation to execute</param>
        /// <param name="retryCount">The number of retry attempts</param>
        /// <param name="retryDelayMs">The delay between retry attempts in milliseconds</param>
        /// <param name="logger">The logging service</param>
        /// <returns>The result of the operation</returns>
        public static async Task<bool> ExecuteWithRetryAsync(Func<Task<bool>> operation, int retryCount, int retryDelayMs, ILoggingService logger = null)
        {
            int attempt = 0;
            bool success = false;

            while (attempt < retryCount && !success)
            {
                try
                {
                    attempt++;
                    logger?.LogInformation($"Executing operation, attempt {attempt}/{retryCount}", "ConnectionHelper");
                    success = await operation();

                    if (success)
                    {
                        logger?.LogInformation($"Operation succeeded on attempt {attempt}/{retryCount}", "ConnectionHelper");
                        return true;
                    }
                    else
                    {
                        logger?.LogWarning($"Operation failed on attempt {attempt}/{retryCount}, retrying...", "ConnectionHelper");
                    }
                }
                catch (Exception ex)
                {
                    logger?.LogError($"Exception during operation on attempt {attempt}/{retryCount}: {ex.Message}", "ConnectionHelper", ex);
                }

                if (!success && attempt < retryCount)
                {
                    logger?.LogInformation($"Waiting {retryDelayMs}ms before retry", "ConnectionHelper");
                    await Task.Delay(retryDelayMs);
                }
            }

            if (!success)
            {
                logger?.LogError($"Operation failed after {retryCount} attempts", "ConnectionHelper");
            }

            return success;
        }

        /// <summary>
        /// Checks if a USB device is connected
        /// </summary>
        /// <param name="deviceName">The name of the device to check</param>
        /// <returns>True if the device is connected, false otherwise</returns>
        public static bool IsUSBDeviceConnected(string deviceName)
        {
            // In a real implementation, this would check if the USB device is connected
            // For now, we'll just simulate this
            return true;
        }

        /// <summary>
        /// Enables Bluetooth
        /// </summary>
        /// <returns>True if Bluetooth is successfully enabled, false otherwise</returns>
        public static bool EnableBluetooth()
        {
            // In a real implementation, this would enable Bluetooth
            // For now, we'll just simulate this
            return true;
        }
    }
}
