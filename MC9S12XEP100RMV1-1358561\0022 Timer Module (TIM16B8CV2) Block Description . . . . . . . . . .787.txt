﻿Chapter 22
Timer Module (TIM16B8CV2) Block Description

Table 22-1. Revision History

Revision Sections
Revision Date Description of Changes

Number Affected

V02.05 9 Jul 2009 *********/22- - Revised flag clearing procedure, whereby TEN or PAEN bit must be set
803 when clearing flags.

*********/22- - Add fomula to describe prescaler
803

*********/22-
805

*********/22-
806

*********/22-
808

22.4.2/22-811
22.4.3/22-811

V02.06 26 Aug 2009 22.1.2/22-788 - Correct typo: TSCR ->TSCR1
*********/22- - Correct reference: Figure 1-25 -> Figure 1-31

805 - Add description, “a counter overflow when TTOV[7] is set”, to be the
********/22-794 condition of channel 7 override event.
********/22-795 - Phrase the description of OC7M to make it more explicit
********/22-796
22.4.3/22-811

V02.07 04 May 2010 ********/22-799 - Add Table 22-10
*********/22- - in TCRE bit description part,add Note

802 - Add Figure 22-31
22.4.3/22-811

22.1 Introduction
The basic timer consists of a 16-bit, software-programmable counter driven by a enhanced programmable
prescaler.

This timer can be used for many purposes, including input waveform measurements while simultaneously
generating an output waveform. Pulse widths can vary from microseconds to many seconds.

This timer contains 8 complete input capture/output compare channels and one pulse accumulator. The
input capture function is used to detect a selected transition edge and record the time. The output compare
function is used for generating output signals or for timer software delays. The 16-bit pulse accumulator
is used to operate as a simple event counter or a gated time accumulator. The pulse accumulator shares
timer channel 7 when in event mode.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 787



Chapter 22 Timer Module (TIM16B8CV2) Block Description

A full access for the counter registers or the input capture/output compare registers should take place in
one clock cycle. Accessing high byte and low byte separately for all of these registers may not yield the
same result as accessing them in one word.

22.1.1 Features
The TIM16B8CV2 includes these distinctive features:

• Eight input capture/output compare channels.
• Clock prescaling.
• 16-bit counter.
• 16-bit pulse accumulator.

22.1.2 Modes of Operation
Stop: Timer is off because clocks are stopped.

Freeze: Timer counter keep on running, unless TSFRZ in TSCR1 (0x0006) is set to 1.

Wait: Counters keep on running, unless TSWAI in TSCR1 (0x0006) is set to 1.

Normal: Timer counter keep on running, unless TEN in TSCR1 (0x0006) is cleared to 0.

MC9S12XE-Family Reference Manual  Rev. 1.25

788 Freescale Semiconductor



Chapter 22 Timer Module (TIM16B8CV2) Block Description

22.1.3 Block Diagrams

Channel 0

Bus clock Prescaler Input capture
Output compare IOC0

Channel 1

16-bit Counter Input capture
Output compare IOC1

Channel 2
Timer overflow Input capture
interrupt Output compare IOC2

Timer channel 0 Channel 3
interrupt Input capture IOC3

Output compare
Registers Channel 4

Input capture
Output compare IOC4

Channel 5
Input capture IOC5

Output compare
Timer channel 7
interrupt Channel 6

Input capture
Output compare IOC6

PA overflow Channel 7
interrupt

16-bit Input capture IOC7
PA input Pulse accumulator Output compare
interrupt

Figure 22-1. TIM16B8CV2 Block Diagram

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 789



Chapter 22 Timer Module (TIM16B8CV2) Block Description

TIMCLK (Timer clock)

CLK1
CLK0 4:1 MUX

Prescaled clock Clock select
(PCLK) (PAMOD) Edge detector PT7

Interrupt

PACNT MUX

Divide by 64 M clock

Figure 22-2. 16-Bit Pulse Accumulator Block Diagram

16-bit Main Timer

PTn Edge detector

Set CnF Interrupt
TCn Input Capture Reg.

Figure 22-3. Interrupt Flag Setting

MC9S12XE-Family Reference Manual  Rev. 1.25

790 Freescale Semiconductor

Intermodule Bus

PACLK / 65536

PACLK / 256

PACLK



Chapter 22 Timer Module (TIM16B8CV2) Block Description

PULSE
ACCUMULATOR PAD

CHANNEL 7 OUTPUT COMPARE

OCPD

TEN
TIOS7

Figure 22-4. Channel 7 Output Compare/Pulse Accumulator Logic

22.2 External Signal Description
The TIM16B8CV2 module has a total of eight external pins.

22.2.1 IOC7 — Input Capture and Output Compare Channel 7 Pin
This pin serves as input capture or output compare for channel 7. This can also be configured as pulse
accumulator input.

22.2.2 IOC6 — Input Capture and Output Compare Channel 6 Pin
This pin serves as input capture or output compare for channel 6.

22.2.3 IOC5 — Input Capture and Output Compare Channel 5 Pin
This pin serves as input capture or output compare for channel 5.

22.2.4 IOC4 — Input Capture and Output Compare Channel 4 Pin
This pin serves as input capture or output compare for channel 4. Pin

22.2.5 IOC3 — Input Capture and Output Compare Channel 3 Pin
This pin serves as input capture or output compare for channel 3.

22.2.6 IOC2 — Input Capture and Output Compare Channel 2 Pin
This pin serves as input capture or output compare for channel 2.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 791



Chapter 22 Timer Module (TIM16B8CV2) Block Description

22.2.7 IOC1 — Input Capture and Output Compare Channel 1 Pin
This pin serves as input capture or output compare for channel 1.

22.2.8 IOC0 — Input Capture and Output Compare Channel 0 Pin
This pin serves as input capture or output compare for channel 0.

NOTE
 For the description of interrupts see Section 22.6, “Interrupts”.

22.3 Memory Map and Register Definition
This section provides a detailed description of all memory and registers.

22.3.1 Module Memory Map
The memory map for the TIM16B8CV2 module is given below in Figure 22-5. The address listed for each
register is the address offset. The total address for each register is the sum of the base address for the
TIM16B8CV2 module and the address offset for each register.

22.3.2 Register Descriptions
This section consists of register descriptions in address order. Each description includes a standard
register diagram with an associated figure number. Details of register bit and field function follow the
register diagrams, in bit order.

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0000 R
TIOS IOS7 IOS6 IOS5 IOS4 IOS3 IOS2 IOS1 IOS0

W

0x0001 R 0 0 0 0 0 0 0 0
CFORC W FOC7 FOC6 FOC5 FOC4 FOC3 FOC2 FOC1 FOC0

0x0002 R
OC7M OC7M7 OC7M6 OC7M5 OC7M4 OC7M3 OC7M2 OC7M1 OC7M0

W

0x0003 R
OC7D OC7D7 OC7D6 OC7D5 OC7D4 OC7D3 OC7D2 OC7D1 OC7D0

W

0x0004 R
TCNTH TCNT15 TCNT14 TCNT13 TCNT12 TCNT11 TCNT10 TCNT9 TCNT8

W

0x0005 R
TCNTL TCNT7 TCNT6 TCNT5 TCNT4 TCNT3 TCNT2 TCNT1 TCNT0

W

= Unimplemented or Reserved

Figure 22-5. TIM16B8CV2 Register Summary (Sheet 1 of 3)

MC9S12XE-Family Reference Manual  Rev. 1.25

792 Freescale Semiconductor



Chapter 22 Timer Module (TIM16B8CV2) Block Description

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name
0x0006 R 0 0 0
TSCR1 TEN TSWAI TSFRZ TFFCA PRNT

W

0x0007 R
TTOV TOV7 TOV6 TOV5 TOV4 TOV3 TOV2 TOV1 TOV0

W

0x0008 R
TCTL1 OM7 OL7 OM6 OL6 OM5 OL5 OM4 OL4

W

0x0009 R
TCTL2 OM3 OL3 OM2 OL2 OM1 OL1 OM0 OL0

W

0x000A R
TCTL3 EDG7B EDG7A EDG6B EDG6A EDG5B EDG5A EDG4B EDG4A

W

0x000B R
TCTL4 EDG3B EDG3A EDG2B EDG2A EDG1B EDG1A EDG0B EDG0A

W

0x000C R
TIE C7I C6I C5I C4I C3I C2I C1I C0I

W

0x000D R 0 0 0
TSCR2 TOI TCRE PR2 PR1 PR0

W

0x000E R
TFLG1 C7F C6F C5F C4F C3F C2F C1F C0F

W

0x000F R 0 0 0 0 0 0 0
TFLG2 TOF

W

R
Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8

0x0010–0x001F W
TCxH–TCxL R

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

0x0020 R 0
PACTL PAEN PAMOD PEDGE CLK1 CLK0 PAOVI PAI

W

0x0021 R 0 0 0 0 0 0
PAFLG PAOVF PAIF

W

0x0022 R
PACNTH PACNT15 PACNT14 PACNT13 PACNT12 PACNT11 PACNT10 PACNT9 PACNT8

W

0x0023 R
PACNTL PACNT7 PACNT6 PACNT5 PACNT4 PACNT3 PACNT2 PACNT1 PACNT0

W

0x0024–0x002B R
Reserved W

= Unimplemented or Reserved

Figure 22-5. TIM16B8CV2 Register Summary (Sheet 2 of 3)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 793



Chapter 22 Timer Module (TIM16B8CV2) Block Description

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name
0x002C R
OCPD OCPD7 OCPD6 OCPD5 OCPD4 OCPD3 OCPD2 OCPD1 OCPD0

W

0x002D R

0x002E R
PTPSR PTPS7 PTPS6 PTPS5 PTPS4 PTPS3 PTPS2 PTPS1 PTPS0

W

0x002F R
Reserved W

= Unimplemented or Reserved

Figure 22-5. TIM16B8CV2 Register Summary (Sheet 3 of 3)

******** Timer Input Capture/Output Compare Select (TIOS)
Module Base + 0x0000

7 6 5 4 3 2 1 0

R
IOS7 IOS6 IOS5 IOS4 IOS3 IOS2 IOS1 IOS0

W

Reset 0 0 0 0 0 0 0 0

Figure 22-6. Timer Input Capture/Output Compare Select (TIOS)

Read: Anytime

Write: Anytime

Table 22-2. TIOS Field Descriptions

Field Description

7:0 Input Capture or Output Compare Channel Configuration
IOS[7:0] 0 The corresponding channel acts as an input capture.

1 The corresponding channel acts as an output compare.

******** Timer Compare Force Register (CFORC)
Module Base + 0x0001

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W FOC7 FOC6 FOC5 FOC4 FOC3 FOC2 FOC1 FOC0

Reset 0 0 0 0 0 0 0 0

Figure 22-7. Timer Compare Force Register (CFORC)

MC9S12XE-Family Reference Manual  Rev. 1.25

794 Freescale Semiconductor



Chapter 22 Timer Module (TIM16B8CV2) Block Description

Read: Anytime but will always return 0x0000 (1 state is transient)

Write: Anytime

Table 22-3. CFORC Field Descriptions

Field Description

7:0 Force Output Compare Action for Channel 7:0 — A write to this register with the corresponding data bit(s) set
FOC[7:0] causes the action which is programmed for output compare “x” to occur immediately. The action taken is the

same as if a successful comparison had just taken place with the TCx register except the interrupt flag does not
get set.
Note: A channel 7 event, which can be a counter overflow when TTOV[7] is set or a successful output compare

on channel 7, overrides any channel 6:0 compares. If forced output compare on any channel occurs at the
same time as the successful output compare then forced output compare action will take precedence and
interrupt flag won’t get set.

******** Output Compare 7 Mask Register (OC7M)
Module Base + 0x0002

7 6 5 4 3 2 1 0

R
OC7M7 OC7M6 OC7M5 OC7M4 OC7M3 OC7M2 OC7M1 OC7M0

W

Reset 0 0 0 0 0 0 0 0

Figure 22-8. Output Compare 7 Mask Register (OC7M)

Read: Anytime

Write: Anytime

Table 22-4. OC7M Field Descriptions

Field Description

7:0 Output Compare 7 Mask — A channel 7 event, which can be a counter overflow when TTOV[7] is set or a
OC7M[7:0] successful output compare on channel 7, overrides any channel 6:0 compares. For each OC7M bit that is set,

the output compare action reflects the corresponding OC7D bit.
0 The corresponding OC7Dx bit in the output compare 7 data register will not be transferred to the timer port on

a channel 7 event, even if the corresponding pin is setup for output compare.
1 The corresponding OC7Dx bit in the output compare 7 data register will be transferred to the timer port on a

channel 7 event.
Note: The corresponding channel must also be setup for output compare (IOSx = 1 and OCPDx = 0) for data to

be transferred from the output compare 7 data register to the timer port.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 795



Chapter 22 Timer Module (TIM16B8CV2) Block Description

******** Output Compare 7 Data Register (OC7D)

Module Base + 0x0003

7 6 5 4 3 2 1 0

R
OC7D7 OC7D6 OC7D5 OC7D4 OC7D3 OC7D2 OC7D1 OC7D0

W

Reset 0 0 0 0 0 0 0 0

Figure 22-9. Output Compare 7 Data Register (OC7D)

Read: Anytime

Write: Anytime

Table 22-5. OC7D Field Descriptions

Field Description

7:0 Output Compare 7 Data — A channel 7 event, which can be a counter overflow when TTOV[7] is set or a
OC7D[7:0] successful output compare on channel 7, can cause bits in the output compare 7 data register to transfer to the

timer port data register depending on the output compare 7 mask register.

******** Timer Count Register (TCNT)

Module Base + 0x0004

15 14 13 12 11 10 9 9

R
TCNT15 TCNT14 TCNT13 TCNT12 TCNT11 TCNT10 TCNT9 TCNT8

W

Reset 0 0 0 0 0 0 0 0

Figure 22-10. Timer Count Register High (TCNTH)

Module Base + 0x0005

7 6 5 4 3 2 1 0

R
TCNT7 TCNT6 TCNT5 TCNT4 TCNT3 TCNT2 TCNT1 TCNT0

W

Reset 0 0 0 0 0 0 0 0

Figure 22-11. Timer Count Register Low (TCNTL)

The 16-bit main timer is an up counter.

A full access for the counter register should take place in one clock cycle. A separate read/write for high
byte and low byte will give a different result than accessing them as a word.

Read: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

796 Freescale Semiconductor



Chapter 22 Timer Module (TIM16B8CV2) Block Description

Write: Has no meaning or effect in the normal mode; only writable in special modes (test_mode = 1).

The period of the first count after a write to the TCNT registers may be a different size because the write
is not synchronized with the prescaler clock.

******** Timer System Control Register 1 (TSCR1)

Module Base + 0x0006

7 6 5 4 3 2 1 0

R 0 0 0
TEN TSWAI TSFRZ TFFCA PRNT

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 22-12. Timer System Control Register 1 (TSCR1)

Read: Anytime

Write: Anytime

Table 22-6. TSCR1 Field Descriptions

Field Description

7 Timer Enable
TEN 0 Disables the main timer, including the counter. Can be used for reducing power consumption.

1 Allows the timer to function normally.
If for any reason the timer is not active, there is no ÷64 clock for the pulse accumulator because the ÷64 is
generated by the timer prescaler.

6 Timer Module Stops While in Wait
TSWAI 0 Allows the timer module to continue running during wait.

1 Disables the timer module when the MCU is in the wait mode. Timer interrupts cannot be used to get the MCU
out of wait.

TSWAI also affects pulse accumulator.

5 Timer Stops While in Freeze Mode
TSFRZ 0 Allows the timer counter to continue running while in freeze mode.

1 Disables the timer counter whenever the MCU is in freeze mode. This is useful for emulation.
TSFRZ does not stop the pulse accumulator.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 797



Chapter 22 Timer Module (TIM16B8CV2) Block Description

Table 22-6. TSCR1 Field Descriptions (continued)

Field Description

4 Timer Fast Flag Clear All
TFFCA 0 Allows the timer flag clearing to function normally.

1 For TFLG1(0x000E), a read from an input capture or a write to the output compare channel (0x0010–0x001F)
causes the corresponding channel flag, CnF, to be cleared. For TFLG2 (0x000F), any access to the TCNT
register (0x0004, 0x0005) clears the TOF flag. Any access to the PACNT registers (0x0022, 0x0023) clears
the PAOVF and PAIF flags in the PAFLG register (0x0021). This has the advantage of eliminating software
overhead in a separate clear sequence. Extra care is required to avoid accidental flag clearing due to
unintended accesses.

3 Precision Timer
PRNT 0 Enables legacy timer. PR0, PR1, and PR2 bits of the TSCR2 register are used for timer counter prescaler

selection.
1 Enables precision timer. All bits of the PTPSR register are used for Precision Timer Prescaler Selection, and

all bits.
This bit is writable only once out of reset.

******** Timer Toggle On Overflow Register 1 (TTOV)

Module Base + 0x0007

7 6 5 4 3 2 1 0

R
TOV7 TOV6 TOV5 TOV4 TOV3 TOV2 TOV1 TOV0

W

Reset 0 0 0 0 0 0 0 0

Figure 22-13. Timer Toggle On Overflow Register 1 (TTOV)

Read: Anytime

Write: Anytime

Table 22-7. TTOV Field Descriptions

Field Description

7:0 Toggle On Overflow Bits — TOVx toggles output compare pin on overflow. This feature only takes effect when
TOV[7:0] in output compare mode. When set, it takes precedence over forced output compare but not channel 7 override

events.
0 Toggle output compare pin on overflow feature disabled.
1 Toggle output compare pin on overflow feature enabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

798 Freescale Semiconductor



Chapter 22 Timer Module (TIM16B8CV2) Block Description

******** Timer Control Register 1/Timer Control Register 2 (TCTL1/TCTL2)

Module Base + 0x0008

7 6 5 4 3 2 1 0

R
OM7 OL7 OM6 OL6 OM5 OL5 OM4 OL4

W

Reset 0 0 0 0 0 0 0 0

Figure 22-14. Timer Control Register 1 (TCTL1)

Module Base + 0x0009

7 6 5 4 3 2 1 0

R
OM3 OL3 OM2 OL2 OM1 OL1 OM0 OL0

W

Reset 0 0 0 0 0 0 0 0

Figure 22-15. Timer Control Register 2 (TCTL2)

Read: Anytime

Write: Anytime

Table 22-8. TCTL1/TCTL2 Field Descriptions

Field Description

7:0 Output Mode — These eight pairs of control bits are encoded to specify the output action to be taken as a result
OMx of a successful OCx compare. When either OMx or OLx is 1, the pin associated with OCx becomes an output

tied to OCx.
Note: To enable output action by OMx bits on timer port, the corresponding bit in OC7M should be cleared. For

an output line to be driven by an OCx the OCPDx must be cleared.

7:0 Output Level — These eight pairs of control bits are encoded to specify the output action to be taken as a result
OLx of a successful OCx compare. When either OMx or OLx is 1, the pin associated with OCx becomes an output

tied to OCx.
Note: To enable output action by OLx bits on timer port, the corresponding bit in OC7M should be cleared. For

an output line to be driven by an OCx the OCPDx must be cleared.

Table 22-9. Compare Result Output Action

OMx OLx Action

0 0 No output compare
action on the timer output signal

0 1 Toggle OCx output line
1 0 Clear OCx output line to zero
1 1 Set OCx output line to one

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 799



Chapter 22 Timer Module (TIM16B8CV2) Block Description

To operate the 16-bit pulse accumulator independently of input capture or output compare 7 and 0
respectively the user must set the corresponding bits IOSx = 1, OMx = 0 and OLx = 0. OC7M7 in the
OC7M register must also be cleared.

To enable output action using the OM7 and OL7 bits on the timer port,the corresponding bit OC7M7 in
the OC7M register must also be cleared. The settings for these bits can be seen in Table 22-10

Table 22-10. The OC7 and OCx event priority

OC7M7=0 OC7M7=1

OC7Mx=1 OC7Mx=0 OC7Mx=1 OC7Mx=0
TC7=TCx TC7>TCx TC7=TCx TC7>TCx TC7=TCx TC7>TCx TC7=TCx TC7>TCx

IOCx=OC7Dx IOCx=OC7Dx IOCx=OMx/OLx IOCx=OC7Dx IOCx=OC7Dx IOCx=OMx/OLx
IOC7=OM7/O +OMx/OLx IOC7=OM7/OL7 IOC7=OC7D7 +OMx/OLx IOC7=OC7D7

L7 IOC7=OM7/O IOC7=OC7D7
L7

Note: in Table 22-10, the IOS7 and IOSx should be set to 1

IOSx is the register TIOS bit x,

OC7Mx is the register OC7M bit x,

TCx is timer Input Capture/Output Compare register,

IOCx is channel x,

OMx/OLx is the register TCTL1/TCTL2,

OC7Dx is the register OC7D bit x.

IOCx = OC7Dx+ OMx/OLx, means that both OC7 event and OCx event will change channel x value.

22.3.2.9 Timer Control Register 3/Timer Control Register 4 (TCTL3 and TCTL4)
Module Base + 0x000A

7 6 5 4 3 2 1 0

R
EDG7B EDG7A EDG6B EDG6A EDG5B EDG5A EDG4B EDG4A

W

Reset 0 0 0 0 0 0 0 0

Figure 22-16. Timer Control Register 3 (TCTL3)

Module Base + 0x000B

7 6 5 4 3 2 1 0

R
EDG3B EDG3A EDG2B EDG2A EDG1B EDG1A EDG0B EDG0A

W

Reset 0 0 0 0 0 0 0 0

Figure 22-17. Timer Control Register 4 (TCTL4)

MC9S12XE-Family Reference Manual  Rev. 1.25

800 Freescale Semiconductor



Chapter 22 Timer Module (TIM16B8CV2) Block Description

Read: Anytime

Write: Anytime.

Table 22-11. TCTL3/TCTL4 Field Descriptions

Field Description

7:0 Input Capture Edge Control — These eight pairs of control bits configure the input capture edge detector
EDGnB circuits.
EDGnA

Table 22-12. Edge Detector Circuit Configuration

EDGnB EDGnA Configuration

0 0 Capture disabled
0 1 Capture on rising edges only
1 0 Capture on falling edges only
1 1 Capture on any edge (rising or falling)

********* Timer Interrupt Enable Register (TIE)

Module Base + 0x000C

7 6 5 4 3 2 1 0

R
C7I C6I C5I C4I C3I C2I C1I C0I

W

Reset 0 0 0 0 0 0 0 0

Figure 22-18. Timer Interrupt Enable Register (TIE)

Read: Anytime

Write: Anytime.

Table 22-13. TIE Field Descriptions

Field Description

7:0 Input Capture/Output Compare “x” Interrupt Enable — The bits in TIE correspond bit-for-bit with the bits in
C7I:C0I the TFLG1 status register. If cleared, the corresponding flag is disabled from causing a hardware interrupt. If set,

the corresponding flag is enabled to cause a interrupt.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 801



Chapter 22 Timer Module (TIM16B8CV2) Block Description

********* Timer System Control Register 2 (TSCR2)

Module Base + 0x000D

7 6 5 4 3 2 1 0

R 0 0 0
TOI TCRE PR2 PR1 PR0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 22-19. Timer System Control Register 2 (TSCR2)

Read: Anytime

Write: Anytime.

Table 22-14. TSCR2 Field Descriptions

Field Description

7 Timer Overflow Interrupt Enable
TOI 0 Interrupt inhibited.

1 Hardware interrupt requested when TOF flag set.

3 Timer Counter Reset Enable — This bit allows the timer counter to be reset by a successful output compare 7
TCRE event. This mode of operation is similar to an up-counting modulus counter.

0 Counter reset inhibited and counter free runs.
1 Counter reset by a successful output compare 7.
Note: If TC7 = 0x0000 and TCRE = 1, TCNT will stay at 0x0000 continuously. If TC7 = 0xFFFF and TCRE = 1,

TOF will never be set when TCNT is reset from 0xFFFF to 0x0000.
Note: TCRE=1 and TC7!=0, the TCNT cycle period will be TC7 x "prescaler counter width" + "1 Bus Clock", for

a more detail explanation please refer to Section 22.4.3, “Output Compare

2 Timer Prescaler Select — These three bits select the frequency of the timer prescaler clock derived from the
PR[2:0] Bus Clock as shown in Table 22-15.

Table 22-15. Timer Clock Selection

PR2 PR1 PR0 Timer Clock

0 0 0 Bus Clock / 1
0 0 1 Bus Clock / 2
0 1 0 Bus Clock / 4
0 1 1 Bus Clock / 8
1 0 0 Bus Clock / 16
1 0 1 Bus Clock / 32
1 1 0 Bus Clock / 64
1 1 1 Bus Clock / 128

MC9S12XE-Family Reference Manual  Rev. 1.25

802 Freescale Semiconductor



Chapter 22 Timer Module (TIM16B8CV2) Block Description

NOTE
The newly selected prescale factor will not take effect until the next
synchronized edge where all prescale counter stages equal zero.

********* Main Timer Interrupt Flag 1 (TFLG1)
Module Base + 0x000E

7 6 5 4 3 2 1 0

R
C7F C6F C5F C4F C3F C2F C1F C0F

W

Reset 0 0 0 0 0 0 0 0

Figure 22-20. Main Timer Interrupt Flag 1 (TFLG1)

Read: Anytime

Write: Used in the clearing mechanism (set bits cause corresponding bits to be cleared). Writing a zero
will not affect current status of the bit.

Table 22-16. TRLG1 Field Descriptions

Field Description

7:0 Input Capture/Output Compare Channel “x” Flag — These flags are set when an input capture or output
C[7:0]F compare event occurs. Clearing requires writing a one to the corresponding flag bit while TEN or PAEN is set to

one.

When TFFCA bit in TSCR register is set, a read from an input capture or a write into an output compare channel
(0x0010–0x001F) will cause the corresponding channel flag CxF to be cleared.

********* Main Timer Interrupt Flag 2 (TFLG2)
Module Base + 0x000F

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0
TOF

W

Reset 0 0 0 0 0 0 0 0

Unimplemented or Reserved

Figure 22-21. Main Timer Interrupt Flag 2 (TFLG2)

TFLG2 indicates when interrupt conditions have occurred. To clear a bit in the flag register, write the bit
to one while TEN bit of TSCR1 or PAEN bit of PACTL is set to one.

Read: Anytime

Write: Used in clearing mechanism (set bits cause corresponding bits to be cleared).

Any access to TCNT will clear TFLG2 register if the TFFCA bit in TSCR register is set.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 803



Chapter 22 Timer Module (TIM16B8CV2) Block Description

Table 22-17. TRLG2 Field Descriptions

Field Description

7 Timer Overflow Flag — Set when 16-bit free-running timer overflows from 0xFFFF to 0x0000. Clearing this bit
TOF requires writing a one to bit 7 of TFLG2 register while the TEN bit of TSCR1 or PAEN bit of PACTL is set to one

(See also TCRE control bit explanation.)

********4 Timer Input Capture/Output Compare Registers High and Low 0–7
(TCxH and TCxL)

Module Base + 0x0010 = TC0H 0x0018 = TC4H
0x0012 = TC1H 0x001A = TC5H
0x0014 = TC2H 0x001C = TC6H
0x0016 = TC3H 0x001E = TC7H

15 14 13 12 11 10 9 0

R
Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8

W

Reset 0 0 0 0 0 0 0 0

Figure 22-22. Timer Input Capture/Output Compare Register x High (TCxH)

Module Base + 0x0011 = TC0L 0x0019 = TC4L
0x0013 = TC1L 0x001B = TC5L
0x0015 = TC2L 0x001D = TC6L
0x0017 = TC3L 0x001F = TC7L

7 6 5 4 3 2 1 0

R
Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

W

Reset 0 0 0 0 0 0 0 0

Figure 22-23. Timer Input Capture/Output Compare Register x Low (TCxL)

Depending on the TIOS bit for the corresponding channel, these registers are used to latch the value of the
free-running counter when a defined transition is sensed by the corresponding input capture edge detector
or to trigger an output action for output compare.

Read: Anytime

Write: Anytime for output compare function.Writes to these registers have no meaning or effect during
input capture. All timer input capture/output compare registers are reset to 0x0000.

NOTE
Read/Write access in byte mode for high byte should takes place before low
byte otherwise it will give a different result.

MC9S12XE-Family Reference Manual  Rev. 1.25

804 Freescale Semiconductor



Chapter 22 Timer Module (TIM16B8CV2) Block Description

********* 16-Bit Pulse Accumulator Control Register (PACTL)
Module Base + 0x0020

7 6 5 4 3 2 1 0

R 0
PAEN PAMOD PEDGE CLK1 CLK0 PAOVI PAI

W

Reset 0 0 0 0 0 0 0 0

Unimplemented or Reserved

Figure 22-24. 16-Bit Pulse Accumulator Control Register (PACTL)

When PAEN is set, the PACT is enabled.The PACT shares the input pin with IOC7.

Read: Any time

Write: Any time

Table 22-18. PACTL Field Descriptions

Field Description

6 Pulse Accumulator System Enable — PAEN is independent from TEN. With timer disabled, the pulse
PAEN accumulator can function unless pulse accumulator is disabled.

0 16-Bit Pulse Accumulator system disabled.
1 Pulse Accumulator system enabled.

5 Pulse Accumulator Mode — This bit is active only when the Pulse Accumulator is enabled (PAEN = 1). See
PAMOD Table 22-19.

0 Event counter mode.
1 Gated time accumulation mode.

4 Pulse Accumulator Edge Control — This bit is active only when the Pulse Accumulator is enabled (PAEN = 1).
PEDGE For PAMOD bit = 0 (event counter mode). See Table 22-19.

0 Falling edges on IOC7 pin cause the count to be incremented.
1 Rising edges on IOC7 pin cause the count to be incremented.
For PAMOD bit = 1 (gated time accumulation mode).
0 IOC7 input pin high enables M (bus clock) divided by 64 clock to Pulse Accumulator and the trailing falling

edge on IOC7 sets the PAIF flag.
1 IOC7 input pin low enables M (bus clock) divided by 64 clock to Pulse Accumulator and the trailing rising edge

on IOC7 sets the PAIF flag.

3:2 Clock Select Bits — Refer to Table 22-20.
CLK[1:0]

1 Pulse Accumulator Overflow Interrupt Enable
PAOVI 0 Interrupt inhibited.

1 Interrupt requested if PAOVF is set.

0 Pulse Accumulator Input Interrupt Enable
PAI 0 Interrupt inhibited.

1 Interrupt requested if PAIF is set.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 805



Chapter 22 Timer Module (TIM16B8CV2) Block Description

Table 22-19. Pin Action

PAMOD PEDGE Pin Action

0 0 Falling edge

0 1 Rising edge

1 0 Div. by 64 clock enabled with pin high level

1 1 Div. by 64 clock enabled with pin low level

NOTE
If the timer is not active (TEN = 0 in TSCR), there is no divide-by-64
because the ÷64 clock is generated by the timer prescaler.

Table 22-20. Timer Clock Selection

CLK1 CLK0 Timer Clock

0 0 Use timer prescaler clock as timer counter clock

0 1 Use PACLK as input to timer counter clock

1 0 Use PACLK/256 as timer counter clock frequency

1 1 Use PACLK/65536 as timer counter clock frequency

For the description of PACLK please refer Figure 22-30.

If the pulse accumulator is disabled (PAEN = 0), the prescaler clock from the timer is always used as an
input clock to the timer counter. The change from one selected clock to the other happens immediately
after these bits are written.

********* Pulse Accumulator Flag Register (PAFLG)

Module Base + 0x0021

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0
PAOVF PAIF

W

Reset 0 0 0 0 0 0 0 0

Unimplemented or Reserved

Figure 22-25. Pulse Accumulator Flag Register (PAFLG)

Read: Anytime

Write: Anytime

When the TFFCA bit in the TSCR register is set, any access to the PACNT register will clear all the flags
in the PAFLG register. Timer module or Pulse Accumulator must stay enabled (TEN=1 or PAEN=1) while
clearing these bits.

MC9S12XE-Family Reference Manual  Rev. 1.25

806 Freescale Semiconductor



Chapter 22 Timer Module (TIM16B8CV2) Block Description

Table 22-21. PAFLG Field Descriptions

Field Description

1 Pulse Accumulator Overflow Flag — Set when the 16-bit pulse accumulator overflows from 0xFFFF to 0x0000.
PAOVF Clearing this bit requires writing a one to this bit in the PAFLG register while TEN bit of TSCR1 or PAEN bit of

PACTL register is set to one.

0 Pulse Accumulator Input edge Flag — Set when the selected edge is detected at the IOC7 input pin.In event
PAIF mode the event edge triggers PAIF and in gated time accumulation mode the trailing edge of the gate signal at

the IOC7 input pin triggers PAIF.
Clearing this bit requires writing a one to this bit in the PAFLG register while TEN bit of TSCR1 or PAEN bit of
PACTL register is set to one. Any access to the PACNT register will clear all the flags in this register when TFFCA
bit in register TSCR(0x0006) is set.

********7 Pulse Accumulators Count Registers (PACNT)

Module Base + 0x0022

15 14 13 12 11 10 9 0

R
PACNT15 PACNT14 PACNT13 PACNT12 PACNT11 PACNT10 PACNT9 PACNT8

W

Reset 0 0 0 0 0 0 0 0

Figure 22-26. Pulse Accumulator Count Register High (PACNTH)

Module Base + 0x0023

7 6 5 4 3 2 1 0

R
PACNT7 PACNT6 PACNT5 PACNT4 PACNT3 PACNT2 PACNT1 PACNT0

W

Reset 0 0 0 0 0 0 0 0

Figure 22-27. Pulse Accumulator Count Register Low (PACNTL)

Read: Anytime

Write: Anytime

These registers contain the number of active input edges on its input pin since the last reset.

When PACNT overflows from 0xFFFF to 0x0000, the Interrupt flag PAOVF in PAFLG (0x0021) is set.

Full count register access should take place in one clock cycle. A separate read/write for high byte and low
byte will give a different result than accessing them as a word.

NOTE
Reading the pulse accumulator counter registers immediately after an active
edge on the pulse accumulator input pin may miss the last count because the
input has to be synchronized with the bus clock first.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 807



Chapter 22 Timer Module (TIM16B8CV2) Block Description

********8 Output Compare Pin Disconnect Register(OCPD)

Module Base + 0x002C

7 6 5 4 3 2 1 0

R
OCPD7 OCPD6 OCPD5 OCPD4 OCPD3 OCPD2 OCPD1 OCPD0

W

Reset 0 0 0 0 0 0 0 0

Figure 22-28. Ouput Compare Pin Disconnect Register (OCPD)

Read: Anytime

Write: Anytime

All bits reset to zero.

Table 22-22. OCPD Field Description

Field Description

Output Compare Pin Disconnect Bits
OCPD[7:0} 0 Enables the timer channel port. Ouptut Compare action will occur on the channel pin. These bits do not affect

the input capture or pulse accumulator functions
1 Disables the timer channel port. Output Compare action will not occur on the channel pin, but the output

compare flag still become set .

********* Precision Timer Prescaler Select Register (PTPSR)

Module Base + 0x002E

7 6 5 4 3 2 1 0

R
PTPS7 PTPS6 PTPS5 PTPS4 PTPS3 PTPS2 PTPS1 PTPS0

W

Reset 0 0 0 0 0 0 0 0

Figure 22-29. Precision Timer Prescaler Select Register (PTPSR)

Read: Anytime

Write: Anytime

All bits reset to zero.

MC9S12XE-Family Reference Manual  Rev. 1.25

808 Freescale Semiconductor



Chapter 22 Timer Module (TIM16B8CV2) Block Description

Table 22-23. PTPSR Field Descriptions

Field Description

7:0 Precision Timer Prescaler Select Bits — These eight bits specify the division rate of the main Timer prescaler.
PTPS[7:0] These are effective only when the PRNT bit of TSCR1 is set to 1. Table 22-24 shows some selection examples

in this case.
The newly selected prescale factor will not take effect until the next synchronized edge where all prescale counter
stages equal zero.

The Prescaler can be calculated as follows depending on logical value of the PTPS[7:0] and PRNT bit:

PRNT = 1 : Prescaler = PTPS[7:0] + 1

Table 22-24. Precision Timer Prescaler Selection Examples when PRNT = 1

Prescale
PTPS7 PTPS6 PTPS5 PTPS4 PTPS3 PTPS2 PTPS1 PTPS0

Factor

0 0 0 0 0 0 0 0 1
0 0 0 0 0 0 0 1 2
0 0 0 0 0 0 1 0 3
0 0 0 0 0 0 1 1 4
0 0 0 0 0 1 0 0 5
0 0 0 0 0 1 0 1 6
0 0 0 0 0 1 1 0 7
0 0 0 0 0 1 1 1 8
0 0 0 0 1 1 1 1 16
0 0 0 1 1 1 1 1 32
0 0 1 1 1 1 1 1 64
0 1 1 1 1 1 1 1 128
1 1 1 1 1 1 1 1 256

22.4 Functional Description
This section provides a complete functional description of the timer TIM16B8CV2 block. Please refer to
the detailed timer block diagram in Figure 22-30 as necessary.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 809



Chapter 22 Timer Module (TIM16B8CV2) Block Description

Bus Clock

CLK[1:0]
PR[2:1:0] PACLK channel 7 output

compare
PACLK/256

PACLK/65536 MUX
PRESCALER TCRE

CxI
TCNT(hi):TCNT(lo)

CxF
CLEAR COUNTER

16-BIT COUNTER TOF INTERRUPT
TOI LOGIC TOF

TE

CHANNEL 0

16-BIT COMPARATOR C0F C0F CH. 0 CAPTURE
OM:OL0

TC0 IOC0 PIN
TOV0 LOGIC IOC0 PIN

EDGE        CH. 0COMPARE
EDG0A EDG0B DETECT IOC0

CHANNEL 1

16-BIT COMPARATOR C1F C1F CH. 1 CAPTURE
TC1 OM:OL1 IOC1 PIN

TOV1 LOGIC IOC1 PIN
EDG1A EDG1B EDGE       CH. 1 COMPARE

DETECT
CHANNEL2 IOC1

CHANNEL7

16-BIT COMPARATOR C7F C7F    CH.7 CAPTURE
TC7 OM:OL7 IOC7 PIN PA INPUT

LOGIC IOC7 PIN
EDG7A TOV7     CH. 7 COMPARE

EDGE
EDG7B DETECT

IOC7

PAOVF PACNT(hi):PACNT(lo)
PEDGE EDGE

PAE DETECT
PACLK/65536 16-BIT COUNTER

PACLK/256 PACLK
TEN

INTERRUPT INTERRUPT PAIF
REQUEST LOGIC

DIVIDE-BY-64 Bus Clock
PAOVI PAI

PAOVF PAIF

PAOVF
PAOVI

Figure 22-30. Detailed Timer Block Diagram

22.4.1 Prescaler
The prescaler divides the bus clock by 1,2,4,8,16,32,64 or 128. The prescaler select bits, PR[2:0], select
the prescaler divisor. PR[2:0] are in timer system control register 2 (TSCR2).

MC9S12XE-Family Reference Manual  Rev. 1.25

810 Freescale Semiconductor



Chapter 22 Timer Module (TIM16B8CV2) Block Description

The prescaler divides the bus clock by a prescalar value. Prescaler select bits PR[2:0] of in timer system
control register 2 (TSCR2) are set to define a prescalar value that generates a divide by 1, 2, 4, 8, 16, 32,
64 and 128 when the PRNT bit in TSCR1 is disabled.

By enabling the PRNT bit of the TSCR1 register, the performance of the timer can be enhanced. In this
case, it is possible to set additional prescaler settings for the main timer counter  in the present timer by
using PTPSR[7:0] bits of PTPSR register.

22.4.2 Input Capture
Clearing the I/O (input/output) select bit, IOSx, configures channel x as an input capture channel. The
input capture function captures the time at which an external event occurs. When an active edge occurs on
the pin of an input capture channel, the timer transfers the value in the timer counter into the timer channel
registers, TCx.

The minimum pulse width for the input capture input is greater than two bus clocks.

An input capture on channel x sets the CxF flag. The CxI bit enables the CxF flag to generate interrupt
requests. Timer module or Pulse Accumulator must stay enabled (TEN bit of TSCR1 or PAEN bit of
PACTL regsiter must be set to one) while clearing CxF (writing one to CxF).

22.4.3 Output Compare
Setting the I/O select bit, IOSx, configures channel x as an output compare channel. The output compare
function can generate a periodic pulse with a programmable polarity, duration, and frequency. When the
timer counter reaches the value in the channel registers of an output compare channel, the timer can set,
clear, or toggle the channel pin if the corresponding OCPDx bit is set to zero. An output compare on
channel x sets the CxF flag. The CxI bit enables the CxF flag to generate interrupt requests. Timer module
or Pulse Accumulator must stay enabled (TEN bit of TSCR1 or PAEN bit of PACTL regsiter must be set
to one) while clearing CxF (writing one to CxF).

The output mode and level bits, OMx and OLx, select set, clear, toggle on output compare. Clearing both
OMx and OLx results in no output compare action on the output compare channel pin.

Setting a force output compare bit, FOCx, causes an output compare on channel x. A forced output
compare does not set the channel flag.

A channel 7 event, which can be a counter overflow when TTOV[7] is set or a successful output compare
on channel 7, overrides output compares on all other output compare channels. The output compare 7 mask
register masks the bits in the output compare 7 data register. The timer counter reset enable bit, TCRE,
enables channel 7 output compares to reset the timer counter. A channel 7 output compare can reset the
timer counter even if the IOC7 pin is being used as the pulse accumulator input.

Writing to the timer port bit of an output compare pin does not affect the pin state. The value written is
stored in an internal latch. When the pin becomes available for general-purpose output, the last value
written to the bit appears at the pin.

When TCRE is set and TC7 is not equal to 0, then TCNT will cycle from 0 to TC7. When TCNT reaches
TC7 value, it lasts only one bus cycle then resets to 0.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 811



Chapter 22 Timer Module (TIM16B8CV2) Block Description

Note: in Figure 22-31, if PR[2:0] is equal to 0, one prescaler counter is equal to one bus clock
Figure 22-31. The TCNT cycle diagram under TCRE=1 condition

prescaler
1 bus

counter
clock

TC7 0 1 ----- TC7-1 TC7 0

TC7 event TC7 event

******** OC Channel Initialization
Internal register whose output drives OCx can be programmed before timer drives OCx. The desired state
can be programmed to this Internal register by writing a one to CFORCx bit with TIOSx, OCPDx and TEN
bits set to one. Setting OCPDx to zero allows Interal register to drive the programmed state to OCx. This
allows a glitch free switch over of port from general purpose I/O to timer output once the OCPDx bit is set
to zero.

22.4.4 Pulse Accumulator
The pulse accumulator (PACNT) is a 16-bit counter that can operate in two modes:

Event counter mode — Counting edges of selected polarity on the pulse accumulator input pin, PAI.

Gated time accumulation mode — Counting pulses from a divide-by-64 clock. The PAMOD bit selects the
mode of operation.

The minimum pulse width for the PAI input is greater than two bus clocks.

22.4.5 Event Counter Mode
Clearing the PAMOD bit configures the PACNT for event counter operation. An active edge on the IOC7
pin increments the pulse accumulator counter. The PEDGE bit selects falling edges or rising edges to
increment the count.

NOTE
The PACNT input and timer channel 7 use the same pin IOC7. To use the
IOC7, disconnect it from the output logic by clearing the channel 7 output
mode and output level bits, OM7 and OL7. Also clear the channel 7 output
compare 7 mask bit, OC7M7.

The Pulse Accumulator counter register reflect the number of active input edges on the PACNT input pin
since the last reset.

The PAOVF bit is set when the accumulator rolls over from 0xFFFF to 0x0000. The pulse accumulator
overflow interrupt enable bit, PAOVI, enables the PAOVF flag to generate interrupt requests.

MC9S12XE-Family Reference Manual  Rev. 1.25

812 Freescale Semiconductor



Chapter 22 Timer Module (TIM16B8CV2) Block Description

NOTE
The pulse accumulator counter can operate in event counter mode even
when the timer enable bit, TEN, is clear.

22.4.6 Gated Time Accumulation Mode
Setting the PAMOD bit configures the pulse accumulator for gated time accumulation operation. An active
level on the PACNT input pin enables a divided-by-64 clock to drive the pulse accumulator. The PEDGE
bit selects low levels or high levels to enable the divided-by-64 clock.

The trailing edge of the active level at the IOC7 pin sets the PAIF. The PAI bit enables the PAIF flag to
generate interrupt requests.

The pulse accumulator counter register reflect the number of pulses from the divided-by-64 clock since the
last reset.

NOTE
The timer prescaler generates the divided-by-64 clock. If the timer is not
active, there is no divided-by-64 clock.

22.5 Resets
The reset state of each individual bit is listed within Section 22.3, “Memory Map and Register Definition”
which details the registers and their bit fields.

22.6 Interrupts
This section describes interrupts originated by the TIM16B8CV2 block. Table 22-25 lists the interrupts
generated by the TIM16B8CV2 to communicate with the MCU.

Table 22-25. TIM16B8CV1 Interrupts

Offset
Interrupt (1) Vector1 Priority1 Source Description

C[7:0]F — — — Timer Channel 7–0 Active high timer channel interrupts 7–0

PAOVI — — — Pulse Accumulator Active high pulse accumulator input interrupt
Input

PAOVF — — — Pulse Accumulator Pulse accumulator overflow interrupt
Overflow

TOF — — — Timer Overflow Timer Overflow interrupt
1. Chip Dependent.

The TIM16B8CV2 uses a total of 11 interrupt vectors. The interrupt vector offsets and interrupt numbers
are chip dependent.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 813



Chapter 22 Timer Module (TIM16B8CV2) Block Description

22.6.1 Channel [7:0] Interrupt (C[7:0]F)
This active high outputs will be asserted by the module to request a timer channel 7 – 0 interrupt to be
serviced by the system controller.

22.6.2 Pulse Accumulator Input Interrupt (PAOVI)
This active high output will be asserted by the module to request a timer pulse accumulator input interrupt
to be serviced by the system controller.

22.6.3 Pulse Accumulator Overflow Interrupt (PAOVF)
This active high output will be asserted by the module to request a timer pulse accumulator overflow
interrupt to be serviced by the system controller.

22.6.4 Timer Overflow Interrupt (TOF)
This active high output will be asserted by the module to request a timer overflow interrupt to be serviced
by the system controller.

MC9S12XE-Family Reference Manual  Rev. 1.25

814 Freescale Semiconductor