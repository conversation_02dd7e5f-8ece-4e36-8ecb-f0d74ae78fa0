using System;
using System.Collections.Generic;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents microcontroller code from an ECU
    /// </summary>
    public class MicrocontrollerCode
    {
        /// <summary>
        /// The unique identifier for this microcontroller code
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// The ECU ID this microcontroller code belongs to
        /// </summary>
        public string ECUId { get; set; } = string.Empty;

        /// <summary>
        /// The ECU name this microcontroller code belongs to
        /// </summary>
        public string ECUName { get; set; } = string.Empty;

        /// <summary>
        /// The timestamp when this microcontroller code was read
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// The raw microcontroller code bytes
        /// </summary>
        public byte[] Code { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// The size of the microcontroller code in bytes
        /// </summary>
        public int Size => Code?.Length ?? 0;

        /// <summary>
        /// The checksum of the microcontroller code
        /// </summary>
        public string Checksum { get; set; } = string.Empty;

        /// <summary>
        /// The version of the microcontroller code
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// The microcontroller type (e.g., MC9S12XEP768MAL)
        /// </summary>
        public string MicrocontrollerType { get; set; } = string.Empty;

        /// <summary>
        /// Memory segments in the microcontroller code
        /// </summary>
        public List<MemorySegment> MemorySegments { get; set; } = new List<MemorySegment>();

        /// <summary>
        /// Additional metadata about the microcontroller code
        /// </summary>
        public string Metadata { get; set; } = string.Empty;

        /// <summary>
        /// Calculates the checksum of the microcontroller code
        /// </summary>
        /// <returns>The calculated checksum</returns>
        public string CalculateChecksum()
        {
            if (Code == null || Code.Length == 0)
                return string.Empty;

            // Simple checksum calculation (CRC32 would be better in a real implementation)
            uint checksum = 0;
            foreach (byte b in Code)
            {
                checksum += b;
            }

            return checksum.ToString("X8");
        }

        /// <summary>
        /// Validates the microcontroller code against the stored checksum
        /// </summary>
        /// <returns>True if the code is valid, false otherwise</returns>
        public bool Validate()
        {
            if (string.IsNullOrEmpty(Checksum) || Code == null || Code.Length == 0)
                return false;

            string calculatedChecksum = CalculateChecksum();
            return string.Equals(calculatedChecksum, Checksum, StringComparison.OrdinalIgnoreCase);
        }
    }

    /// <summary>
    /// Represents a memory segment in microcontroller code
    /// </summary>
    public class MemorySegment
    {
        /// <summary>
        /// The starting address of the memory segment
        /// </summary>
        public uint StartAddress { get; set; }

        /// <summary>
        /// The ending address of the memory segment
        /// </summary>
        public uint EndAddress { get; set; }

        /// <summary>
        /// The data in the memory segment
        /// </summary>
        public byte[] Data { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// The type of memory segment (e.g., Flash, RAM, EEPROM)
        /// </summary>
        public string SegmentType { get; set; } = string.Empty;

        /// <summary>
        /// Whether the segment is read-only
        /// </summary>
        public bool IsReadOnly { get; set; }

        /// <summary>
        /// The size of the memory segment in bytes
        /// </summary>
        public int Size => Data?.Length ?? 0;

        /// <summary>
        /// Gets the start address as a hexadecimal string
        /// </summary>
        public string StartAddressHex => $"0x{StartAddress:X8}";

        /// <summary>
        /// Gets the end address as a hexadecimal string
        /// </summary>
        public string EndAddressHex => $"0x{EndAddress:X8}";

        /// <summary>
        /// Gets the size as a formatted string
        /// </summary>
        public string SizeFormatted
        {
            get
            {
                string[] sizes = { "B", "KB", "MB", "GB" };
                double len = Size;
                int order = 0;

                while (len >= 1024 && order < sizes.Length - 1)
                {
                    order++;
                    len = len / 1024;
                }

                return $"{len:0.##} {sizes[order]}";
            }
        }
    }
}
