using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Runtime.Versioning;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Utilities;

#if WINDOWS
using System.Management;
#endif

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Implementation of the USB communication service for Vocom devices
    /// </summary>
    public class USBCommunicationService : IUSBCommunicationService
    {
        private readonly ILoggingService _logger;
        private bool _isInitialized = false;
        private Dictionary<string, SerialPort> _connectedPorts = new Dictionary<string, SerialPort>();
        private readonly string[] VOCOM_DEVICE_IDS = new[] { "Vocom - 88890300", "Vocom", "88890300", "Volvo Communication Unit" };
        private const int DEFAULT_TIMEOUT_MS = 5000;
        private const int DEFAULT_RETRY_ATTEMPTS = 3;
        private const int DEFAULT_RETRY_DELAY_MS = 1000;

        /// <summary>
        /// Event triggered when a USB connection is established
        /// </summary>
        public event EventHandler<string> USBConnected;

        /// <summary>
        /// Event triggered when a USB connection is lost
        /// </summary>
        public event EventHandler<string> USBDisconnected;

        /// <summary>
        /// Event triggered when an error occurs during USB communication
        /// </summary>
        public event EventHandler<string> USBError;

        /// <summary>
        /// Initializes a new instance of the USBCommunicationService class
        /// </summary>
        /// <param name="logger">The logging service</param>
        public USBCommunicationService(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Initializes the USB communication service
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing USB communication service", "USBCommunicationService");

                // Check if USB is available
                bool isAvailable = await IsUSBAvailableAsync();
                if (!isAvailable)
                {
                    _logger.LogError("USB is not available", "USBCommunicationService");
                    USBError?.Invoke(this, "USB is not available");
                    return false;
                }

                _isInitialized = true;
                _logger.LogInformation("USB communication service initialized successfully", "USBCommunicationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to initialize USB communication service", "USBCommunicationService", ex);
                USBError?.Invoke(this, $"Initialization error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks if USB is available
        /// </summary>
        /// <returns>True if USB is available, false otherwise</returns>
        public Task<bool> IsUSBAvailableAsync()
        {
            try
            {
                // Check if any USB ports are available
                string[] ports = SerialPort.GetPortNames();
                bool available = ports.Length > 0;

                _logger.LogInformation($"USB availability check: {available} ({ports.Length} ports available)", "USBCommunicationService");
                return Task.FromResult(available);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error checking USB availability", "USBCommunicationService", ex);
                USBError?.Invoke(this, $"USB availability check error: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Detects Vocom devices connected via USB
        /// </summary>
        /// <returns>List of port names for connected Vocom devices</returns>
        public async Task<List<string>> DetectVocomDevicesAsync()
        {
            try
            {
                _logger.LogInformation("Detecting Vocom devices connected via USB", "USBCommunicationService");

                if (!_isInitialized)
                {
                    _logger.LogError("USB communication service not initialized", "USBCommunicationService");
                    USBError?.Invoke(this, "USB communication service not initialized");
                    return new List<string>();
                }

                List<string> vocomPorts = new List<string>();

#if WINDOWS
                // Use WMI to get detailed information about COM ports (Windows-specific)
                [SupportedOSPlatform("windows")]
                void ScanForVocomDevicesOnWindows()
                {
                    // First try to find Vocom devices using the VID/PID approach
                    try
                    {
                        using (ManagementObjectSearcher searcher = new ManagementObjectSearcher(
                            "SELECT * FROM Win32_PnPEntity WHERE (PNPDeviceID LIKE '%VID_1A12%' AND PNPDeviceID LIKE '%PID_0001%')"))
                        {
                            foreach (ManagementObject queryObj in searcher.Get().Cast<ManagementObject>())
                            {
                                if (queryObj["Caption"] != null)
                                {
                                    string caption = queryObj["Caption"].ToString();
                                    string portName = ExtractCOMPort(caption);

                                    if (!string.IsNullOrEmpty(portName))
                                    {
                                        if (!vocomPorts.Contains(portName))
                                        {
                                            vocomPorts.Add(portName);
                                            _logger.LogInformation($"Found Vocom device by VID/PID on port {portName}", "USBCommunicationService");
                                        }
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Error searching for Vocom devices by VID/PID: {ex.Message}", "USBCommunicationService");
                    }

                    // Then try to find Vocom devices by name
                    try
                    {
                        using (ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PnPEntity WHERE (Caption LIKE '%COM%')"))
                        {
                            foreach (ManagementObject queryObj in searcher.Get().Cast<ManagementObject>())
                            {
                                if (queryObj["Caption"] != null)
                                {
                                    string caption = queryObj["Caption"].ToString();

                                    // Check if the caption contains any of the Vocom device IDs
                                    bool isVocomDevice = false;
                                    foreach (string deviceId in VOCOM_DEVICE_IDS)
                                    {
                                        if (caption.Contains(deviceId, StringComparison.OrdinalIgnoreCase))
                                        {
                                            isVocomDevice = true;
                                            break;
                                        }
                                    }

                                    if (isVocomDevice)
                                    {
                                        // Extract the COM port from the caption
                                        string portName = ExtractCOMPort(caption);
                                        if (!string.IsNullOrEmpty(portName) && !vocomPorts.Contains(portName))
                                        {
                                            vocomPorts.Add(portName);
                                            _logger.LogInformation($"Found Vocom device by name on port {portName}", "USBCommunicationService");
                                        }
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Error searching for Vocom devices by name: {ex.Message}", "USBCommunicationService");
                    }

                    // If no Vocom devices were found, check if the driver is installed and add all COM ports as potential devices
                    if (vocomPorts.Count == 0)
                    {
                        try
                        {
                            string vocomDriverPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86),
                                "88890020 Adapter", "UMDF", "WUDFPuma.dll");

                            if (File.Exists(vocomDriverPath))
                            {
                                _logger.LogInformation("Vocom driver found but no devices detected, adding all COM ports as potential devices", "USBCommunicationService");

                                string[] ports = SerialPort.GetPortNames();
                                foreach (string port in ports)
                                {
                                    if (!vocomPorts.Contains(port))
                                    {
                                        vocomPorts.Add(port);
                                        _logger.LogInformation($"Added potential Vocom device on port {port}", "USBCommunicationService");
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"Error checking for Vocom driver: {ex.Message}", "USBCommunicationService");
                        }
                    }
                }

                if (OperatingSystem.IsWindows())
                {
                    ScanForVocomDevicesOnWindows();
                }
#else
                // Fallback for non-Windows platforms
                string[] ports = SerialPort.GetPortNames();
                foreach (string port in ports)
                {
                    // Add a simple detection mechanism for non-Windows platforms
                    // In a real implementation, this would use platform-specific APIs
                    vocomPorts.Add(port);
                    _logger.LogInformation($"Found potential Vocom device on port {port}", "USBCommunicationService");
                }
#endif

                if (vocomPorts.Count == 0)
                {
                    _logger.LogInformation("No Vocom devices found connected via USB", "USBCommunicationService");
                }
                else
                {
                    _logger.LogInformation($"Found {vocomPorts.Count} Vocom devices connected via USB", "USBCommunicationService");
                }

                return vocomPorts;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error detecting Vocom devices", "USBCommunicationService", ex);
                USBError?.Invoke(this, $"Device detection error: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// Connects to a device via USB
        /// </summary>
        /// <param name="portName">The port name to connect to</param>
        /// <param name="baudRate">The baud rate to use</param>
        /// <param name="timeout">The connection timeout in milliseconds</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        public async Task<bool> ConnectToDeviceAsync(string portName, int baudRate = 115200, int timeout = DEFAULT_TIMEOUT_MS)
        {
            try
            {
                _logger.LogInformation($"Connecting to device on port {portName} with baud rate {baudRate}", "USBCommunicationService");

                if (!_isInitialized)
                {
                    _logger.LogError("USB communication service not initialized", "USBCommunicationService");
                    USBError?.Invoke(this, "USB communication service not initialized");
                    return false;
                }

                // Check if the port is already connected
                if (_connectedPorts.ContainsKey(portName))
                {
                    _logger.LogWarning($"Port {portName} is already connected", "USBCommunicationService");
                    return true;
                }

                // Check if the port exists
                string[] availablePorts = SerialPort.GetPortNames();
                if (!availablePorts.Contains(portName))
                {
                    _logger.LogError($"Port {portName} does not exist", "USBCommunicationService");
                    USBError?.Invoke(this, $"Port {portName} does not exist");
                    return false;
                }

                // Create and configure the serial port
                SerialPort serialPort = new SerialPort(portName, baudRate)
                {
                    ReadTimeout = timeout,
                    WriteTimeout = timeout,
                    DtrEnable = true,
                    RtsEnable = true,
                    Handshake = Handshake.None
                };

                // Connect with retry logic
                bool connected = await ConnectionHelper.ExecuteWithRetryAsync(async () =>
                {
                    try
                    {
                        // Open the port
                        serialPort.Open();

                        // Verify the connection by sending a test command
                        byte[] testCommand = new byte[] { 0x01, 0x00, 0x00 }; // Example test command
                        serialPort.Write(testCommand, 0, testCommand.Length);

                        // Wait for a response
                        await Task.Delay(100);

                        // If we get here without an exception, the connection is successful
                        return true;
                    }
                    catch
                    {
                        // Close the port if it's open
                        if (serialPort.IsOpen)
                        {
                            serialPort.Close();
                        }
                        throw;
                    }
                },
                DEFAULT_RETRY_ATTEMPTS,
                DEFAULT_RETRY_DELAY_MS,
                _logger);

                if (connected)
                {
                    // Add the port to the connected ports dictionary
                    _connectedPorts[portName] = serialPort;

                    _logger.LogInformation($"Connected to device on port {portName}", "USBCommunicationService");
                    USBConnected?.Invoke(this, portName);
                    return true;
                }
                else
                {
                    _logger.LogError($"Failed to connect to device on port {portName}", "USBCommunicationService");
                    USBError?.Invoke(this, $"Failed to connect to device on port {portName}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error connecting to device on port {portName}", "USBCommunicationService", ex);
                USBError?.Invoke(this, $"Connection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disconnects from a device
        /// </summary>
        /// <param name="portName">The port name to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public async Task<bool> DisconnectFromDeviceAsync(string portName)
        {
            try
            {
                _logger.LogInformation($"Disconnecting from device on port {portName}", "USBCommunicationService");

                if (!_isInitialized)
                {
                    _logger.LogError("USB communication service not initialized", "USBCommunicationService");
                    USBError?.Invoke(this, "USB communication service not initialized");
                    return false;
                }

                // Check if the port is connected
                if (!_connectedPorts.TryGetValue(portName, out SerialPort serialPort))
                {
                    _logger.LogWarning($"Port {portName} is not connected", "USBCommunicationService");
                    return true;
                }

                // Close the port
                if (serialPort.IsOpen)
                {
                    serialPort.Close();
                }

                // Remove the port from the connected ports dictionary
                _connectedPorts.Remove(portName);

                _logger.LogInformation($"Disconnected from device on port {portName}", "USBCommunicationService");
                USBDisconnected?.Invoke(this, portName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error disconnecting from device on port {portName}", "USBCommunicationService", ex);
                USBError?.Invoke(this, $"Disconnection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Sends data to a device
        /// </summary>
        /// <param name="portName">The port name to send data to</param>
        /// <param name="data">The data to send</param>
        /// <returns>True if data is sent successfully, false otherwise</returns>
        public async Task<bool> SendDataAsync(string portName, byte[] data)
        {
            try
            {
                _logger.LogInformation($"Sending {data?.Length ?? 0} bytes to device on port {portName}", "USBCommunicationService");

                if (!_isInitialized)
                {
                    _logger.LogError("USB communication service not initialized", "USBCommunicationService");
                    USBError?.Invoke(this, "USB communication service not initialized");
                    return false;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("Data is null or empty", "USBCommunicationService");
                    USBError?.Invoke(this, "Data is null or empty");
                    return false;
                }

                // Check if the port is connected
                if (!_connectedPorts.TryGetValue(portName, out SerialPort serialPort))
                {
                    _logger.LogError($"Port {portName} is not connected", "USBCommunicationService");
                    USBError?.Invoke(this, $"Port {portName} is not connected");
                    return false;
                }

                // Check if the port is open
                if (!serialPort.IsOpen)
                {
                    _logger.LogError($"Port {portName} is not open", "USBCommunicationService");
                    USBError?.Invoke(this, $"Port {portName} is not open");
                    return false;
                }

                // Send the data
                serialPort.Write(data, 0, data.Length);

                _logger.LogInformation($"Sent {data.Length} bytes to device on port {portName}", "USBCommunicationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending data to device on port {portName}", "USBCommunicationService", ex);
                USBError?.Invoke(this, $"Send data error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Receives data from a device
        /// </summary>
        /// <param name="portName">The port name to receive data from</param>
        /// <param name="timeout">The timeout in milliseconds</param>
        /// <returns>The received data</returns>
        public async Task<byte[]> ReceiveDataAsync(string portName, int timeout = DEFAULT_TIMEOUT_MS)
        {
            try
            {
                _logger.LogInformation($"Receiving data from device on port {portName}", "USBCommunicationService");

                if (!_isInitialized)
                {
                    _logger.LogError("USB communication service not initialized", "USBCommunicationService");
                    USBError?.Invoke(this, "USB communication service not initialized");
                    return null;
                }

                // Check if the port is connected
                if (!_connectedPorts.TryGetValue(portName, out SerialPort serialPort))
                {
                    _logger.LogError($"Port {portName} is not connected", "USBCommunicationService");
                    USBError?.Invoke(this, $"Port {portName} is not connected");
                    return null;
                }

                // Check if the port is open
                if (!serialPort.IsOpen)
                {
                    _logger.LogError($"Port {portName} is not open", "USBCommunicationService");
                    USBError?.Invoke(this, $"Port {portName} is not open");
                    return null;
                }

                // Set the read timeout
                serialPort.ReadTimeout = timeout;

                // Create a buffer to hold the received data
                byte[] buffer = new byte[4096];
                int bytesRead = 0;

                // Read the data with timeout
                using (var cancellationTokenSource = new System.Threading.CancellationTokenSource(timeout))
                {
                    try
                    {
                        // Read available data
                        bytesRead = await Task.Run(() =>
                        {
                            int totalBytesRead = 0;
                            int bytesAvailable;

                            // Wait for data to be available
                            DateTime startTime = DateTime.Now;
                            while ((bytesAvailable = serialPort.BytesToRead) == 0)
                            {
                                if ((DateTime.Now - startTime).TotalMilliseconds > timeout)
                                {
                                    throw new TimeoutException("Timeout waiting for data");
                                }
                                Task.Delay(10).Wait();
                            }

                            // Read the data
                            totalBytesRead = serialPort.Read(buffer, 0, Math.Min(bytesAvailable, buffer.Length));
                            return totalBytesRead;
                        }, cancellationTokenSource.Token);
                    }
                    catch (TaskCanceledException)
                    {
                        _logger.LogWarning($"Timeout receiving data from device on port {portName}", "USBCommunicationService");
                        USBError?.Invoke(this, $"Timeout receiving data from device on port {portName}");
                        return null;
                    }
                }

                if (bytesRead > 0)
                {
                    // Resize the buffer to the actual number of bytes read
                    byte[] result = new byte[bytesRead];
                    Array.Copy(buffer, result, bytesRead);

                    _logger.LogInformation($"Received {bytesRead} bytes from device on port {portName}", "USBCommunicationService");
                    return result;
                }
                else
                {
                    _logger.LogWarning($"No data received from device on port {portName}", "USBCommunicationService");
                    return new byte[0];
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error receiving data from device on port {portName}", "USBCommunicationService", ex);
                USBError?.Invoke(this, $"Receive data error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Extracts the COM port from a device caption
        /// </summary>
        /// <param name="caption">The device caption</param>
        /// <returns>The COM port name</returns>
        private string ExtractCOMPort(string caption)
        {
            try
            {
                if (string.IsNullOrEmpty(caption))
                {
                    return null;
                }

                // Extract the COM port from the caption (e.g., "Vocom - 88890300 (COM3)")
                int comIndex = caption.IndexOf("(COM");
                if (comIndex >= 0)
                {
                    int endIndex = caption.IndexOf(")", comIndex);
                    if (endIndex > comIndex)
                    {
                        string comPort = caption.Substring(comIndex + 1, endIndex - comIndex - 1);
                        return comPort;
                    }
                }

                // Try alternative format (e.g., "COM3 - Vocom")
                comIndex = caption.IndexOf("COM");
                if (comIndex >= 0)
                {
                    // Extract the COM port (COM followed by numbers)
                    string remaining = caption.Substring(comIndex);
                    string comPort = "COM";

                    for (int i = 3; i < remaining.Length; i++)
                    {
                        if (char.IsDigit(remaining[i]))
                        {
                            comPort += remaining[i];
                        }
                        else
                        {
                            break;
                        }
                    }

                    if (comPort.Length > 3) // More than just "COM"
                    {
                        return comPort;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error extracting COM port from caption: {ex.Message}", "USBCommunicationService");
                return null;
            }
        }
    }
}
