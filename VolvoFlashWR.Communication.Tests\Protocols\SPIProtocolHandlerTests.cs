using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;

using VolvoFlashWR.Communication.Protocols;
using VolvoFlashWR.Communication.Vocom;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Protocols
{
    [TestFixture]
    public class SPIProtocolHandlerTests
    {
        private Mock<ILoggingService> _mockLogger;
        private Mock<IVocomService> _mockVocomService;
        private SPIProtocolHandler _spiProtocolHandler;
        private VocomDevice _mockVocomDevice;
        private ECUDevice _mockECU;

        [SetUp]
        public void Setup()
        {
            // Create mock logger
            _mockLogger = new Mock<ILoggingService>();

            // Create mock Vocom service
            _mockVocomService = new Mock<IVocomService>();

            // Create mock Vocom device
            _mockVocomDevice = new VocomDevice
            {
                Id = "test-device-id",
                SerialNumber = "88890300",
                ConnectionType = VocomConnectionType.USB,
                ConnectionStatus = VocomConnectionStatus.Connected
            };

            // Configure mock Vocom service
            _mockVocomService.Setup(s => s.CurrentDevice).Returns(_mockVocomDevice);

            // Create mock ECU
            _mockECU = new ECUDevice
            {
                Id = "test-ecu-id",
                Name = "Test ECU",
                SerialNumber = "TEST-123",
                ConnectionStatus = ECUConnectionStatus.Disconnected,
                ProtocolType = ECUProtocolType.SPI
            };

            // Create SPI protocol handler
            _spiProtocolHandler = new SPIProtocolHandler(_mockLogger.Object, _mockVocomService.Object);
        }

        [Test]
        public async Task InitializeAsync_ShouldReturnTrue_WhenVocomDeviceIsConnected()
        {
            // Act
            bool result = await _spiProtocolHandler.InitializeAsync();

            // Assert
            ClassicAssert.That(result, Is.True);
        }

        [Test]
        public async Task InitializeAsync_ShouldReturnFalse_WhenVocomDeviceIsNotConnected()
        {
            // Arrange
            _mockVocomDevice.ConnectionStatus = VocomConnectionStatus.Disconnected;

            // Act
            bool result = await _spiProtocolHandler.InitializeAsync();

            // Assert
            ClassicAssert.That(result, Is.False);
        }

        [Test]
        public async Task ConnectAsync_ShouldReturnTrue_WhenECUIsValid()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();

            // Act
            bool result = await _spiProtocolHandler.ConnectAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.True);
        }

        [Test]
        public async Task ConnectAsync_ShouldReturnFalse_WhenECUIsNull()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();

            // Act
            bool result = await _spiProtocolHandler.ConnectAsync(null);

            // Assert
            ClassicAssert.That(result, Is.False);
        }

        [Test]
        public async Task ConnectAsync_ShouldReturnFalse_WhenECUProtocolTypeIsNotSPI()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();
            _mockECU.ProtocolType = ECUProtocolType.CAN;

            // Act
            bool result = await _spiProtocolHandler.ConnectAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.False);
        }

        [Test]
        public async Task DisconnectAsync_ShouldReturnTrue_WhenECUIsValid()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();
            await _spiProtocolHandler.ConnectAsync(_mockECU);

            // Act
            bool result = await _spiProtocolHandler.DisconnectAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.True);
        }

        [Test]
        public async Task DisconnectAsync_ShouldReturnFalse_WhenECUIsNull()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();

            // Act
            bool result = await _spiProtocolHandler.DisconnectAsync(null);

            // Assert
            ClassicAssert.That(result, Is.False);
        }

        [Test]
        public async Task SetOperatingModeAsync_ShouldReturnTrue_WhenModeIsValid()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();

            // Act
            bool result = await _spiProtocolHandler.SetOperatingModeAsync(OperatingMode.Bench);

            // Assert
            ClassicAssert.That(result, Is.True);
        }

        [Test]
        public async Task ReadEEPROMAsync_ShouldReturnData_WhenECUIsValid()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();
            await _spiProtocolHandler.ConnectAsync(_mockECU);

            // Act
            byte[] result = await _spiProtocolHandler.ReadEEPROMAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.Not.Null);
            ClassicAssert.Greater(result.Length, 0, "EEPROM data should have a length greater than 0");
        }

        [Test]
        public async Task ReadEEPROMAsync_ShouldReturnNull_WhenECUIsNull()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();

            // Act
            byte[] result = await _spiProtocolHandler.ReadEEPROMAsync(null);

            // Assert
            ClassicAssert.That(result, Is.Null);
        }

        [Test]
        public async Task WriteEEPROMAsync_ShouldReturnTrue_WhenDataIsValid()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();
            await _spiProtocolHandler.ConnectAsync(_mockECU);
            byte[] data = new byte[1024]; // 1KB of data

            // Act
            bool result = await _spiProtocolHandler.WriteEEPROMAsync(_mockECU, data);

            // Assert
            ClassicAssert.That(result, Is.True);
        }

        [Test]
        public async Task WriteEEPROMAsync_ShouldReturnFalse_WhenDataIsNull()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();
            await _spiProtocolHandler.ConnectAsync(_mockECU);

            // Act
            bool result = await _spiProtocolHandler.WriteEEPROMAsync(_mockECU, null);

            // Assert
            ClassicAssert.That(result, Is.False);
        }

        [Test]
        public async Task ReadMicrocontrollerCodeAsync_ShouldReturnData_WhenECUIsValid()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();
            await _spiProtocolHandler.ConnectAsync(_mockECU);

            // Act
            byte[] result = await _spiProtocolHandler.ReadMicrocontrollerCodeAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.Not.Null);
            ClassicAssert.Greater(result.Length, 0, "Microcontroller code should have a length greater than 0");
        }

        [Test]
        public async Task WriteMicrocontrollerCodeAsync_ShouldReturnTrue_WhenDataIsValid()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();
            await _spiProtocolHandler.ConnectAsync(_mockECU);
            byte[] code = new byte[1024]; // 1KB of code

            // Act
            bool result = await _spiProtocolHandler.WriteMicrocontrollerCodeAsync(_mockECU, code);

            // Assert
            ClassicAssert.That(result, Is.True);
        }

        [Test]
        public async Task ReadParametersAsync_ShouldReturnParameters_WhenECUIsValid()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();
            await _spiProtocolHandler.ConnectAsync(_mockECU);

            // Act
            Dictionary<string, object> result = await _spiProtocolHandler.ReadParametersAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.Not.Null);
            ClassicAssert.Greater(result.Count, 0, "Parameters should have a count greater than 0");
        }

        [Test]
        public async Task WriteParametersAsync_ShouldReturnTrue_WhenParametersAreValid()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();
            await _spiProtocolHandler.ConnectAsync(_mockECU);
            Dictionary<string, object> parameters = new Dictionary<string, object>
            {
                { "Parameter1", 123 },
                { "Parameter2", "Value" }
            };

            // Act
            bool result = await _spiProtocolHandler.WriteParametersAsync(_mockECU, parameters);

            // Assert
            ClassicAssert.That(result, Is.True);
        }

        [Test]
        public async Task PerformDiagnosticSessionAsync_ShouldReturnDiagnosticData_WhenECUIsValid()
        {
            // Arrange
            await _spiProtocolHandler.InitializeAsync();
            await _spiProtocolHandler.ConnectAsync(_mockECU);

            // Act
            DiagnosticData result = await _spiProtocolHandler.PerformDiagnosticSessionAsync(_mockECU);

            // Assert
            ClassicAssert.That(result, Is.Not.Null);
            ClassicAssert.That(result.IsValid, Is.True);
            ClassicAssert.That(result.ECUId, Is.EqualTo(_mockECU.Id));
            ClassicAssert.That(result.Timestamp, Is.Not.EqualTo(default(DateTime)));
        }
    }
}


