using System;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Settings for Vocom device connection
    /// </summary>
    public class VocomConnectionSettings
    {
        /// <summary>
        /// Gets or sets the connection timeout in milliseconds
        /// </summary>
        public int ConnectionTimeoutMs { get; set; } = 5000;

        /// <summary>
        /// Gets or sets whether to automatically check Bluetooth status
        /// </summary>
        public bool AutoCheckBluetooth { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to use WiFi as a fallback connection method
        /// </summary>
        public bool UseWiFiFallback { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to automatically disconnect PTT application
        /// </summary>
        public bool AutoDisconnectPTT { get; set; } = true;

        /// <summary>
        /// Gets or sets the preferred connection type
        /// </summary>
        public VocomConnectionType PreferredConnectionType { get; set; } = VocomConnectionType.USB;

        /// <summary>
        /// Gets or sets the number of connection retry attempts
        /// </summary>
        public int ConnectionRetryAttempts { get; set; } = 3;

        /// <summary>
        /// Gets or sets the delay between connection retry attempts in milliseconds
        /// </summary>
        public int ConnectionRetryDelayMs { get; set; } = 1000;

        /// <summary>
        /// Gets or sets whether to automatically reconnect on connection loss
        /// </summary>
        public bool AutoReconnect { get; set; } = true;

        /// <summary>
        /// Gets or sets the maximum number of auto-reconnect attempts
        /// </summary>
        public int MaxAutoReconnectAttempts { get; set; } = 5;
    }
}
