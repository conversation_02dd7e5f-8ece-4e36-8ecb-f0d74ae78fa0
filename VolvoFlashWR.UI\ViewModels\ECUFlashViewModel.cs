using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Microsoft.Win32;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.UI.Commands;

namespace VolvoFlashWR.UI.ViewModels
{
    /// <summary>
    /// ViewModel for the ECU Flash operations view
    /// </summary>
    public class ECUFlashViewModel : INotifyPropertyChanged
    {
        #region Private Fields

        private IECUCommunicationService _ecuCommunicationService;
        private ILoggingService _loggingService;

        private ObservableCollection<ECUDevice> _connectedECUs;
        private ECUDevice _selectedECU;

        private string _statusMessage;
        private double _progressValue;
        private Visibility _progressVisibility;

        // EEPROM-related fields
        private EEPROMData _eepromData;
        private string _eepromDataPreview;
        private string _eepromChecksum;
        private string _eepromStatus;
        private DateTime? _lastEEPROMReadTime;
        private DateTime? _lastEEPROMWriteTime;

        // MCU Code-related fields
        private MicrocontrollerCode _mcuCode;
        private string _mcuCodeChecksum;
        private string _mcuCodeStatus;
        private DateTime? _lastMCUCodeReadTime;
        private DateTime? _lastMCUCodeWriteTime;
        private ObservableCollection<MemorySegment> _memorySegments;
        private CommunicationSpeedMode _selectedCommunicationSpeedMode = CommunicationSpeedMode.Low;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ECUFlashViewModel class
        /// </summary>
        public ECUFlashViewModel()
        {
            // Initialize collections
            ConnectedECUs = new ObservableCollection<ECUDevice>();
            MemorySegments = new ObservableCollection<MemorySegment>();

            // Initialize default values
            StatusMessage = "Ready";
            ProgressValue = 0;
            ProgressVisibility = Visibility.Collapsed;

            // Initialize commands
            ReadEEPROMCommand = new RelayCommand(o => ReadEEPROM(), o => CanReadEEPROM());
            WriteEEPROMCommand = new RelayCommand(o => WriteEEPROM(), o => CanWriteEEPROM());
            SaveEEPROMCommand = new RelayCommand(o => SaveEEPROM(), o => CanSaveEEPROM());
            LoadEEPROMCommand = new RelayCommand(o => LoadEEPROM(), o => CanLoadEEPROM());

            ReadMCUCodeCommand = new RelayCommand(o => ReadMCUCode(), o => CanReadMCUCode());
            WriteMCUCodeCommand = new RelayCommand(o => WriteMCUCode(), o => CanWriteMCUCode());
            SaveMCUCodeCommand = new RelayCommand(o => SaveMCUCode(), o => CanSaveMCUCode());
            LoadMCUCodeCommand = new RelayCommand(o => LoadMCUCode(), o => CanLoadMCUCode());

            SetCommunicationSpeedModeCommand = new RelayCommand(o => SetCommunicationSpeedMode(), o => CanSetCommunicationSpeedMode());

            // Get services from App
            _ecuCommunicationService = (App.Current as App)?.GetService<IECUCommunicationService>();
            _loggingService = (App.Current as App)?.GetService<ILoggingService>();

            // Load connected ECUs
            LoadConnectedECUs();
        }

        #endregion

        #region Properties

        #region General Properties

        /// <summary>
        /// Gets or sets the collection of connected ECUs
        /// </summary>
        public ObservableCollection<ECUDevice> ConnectedECUs
        {
            get => _connectedECUs;
            set
            {
                _connectedECUs = value;
                OnPropertyChanged(nameof(ConnectedECUs));
            }
        }

        /// <summary>
        /// Gets or sets the selected ECU
        /// </summary>
        public ECUDevice SelectedECU
        {
            get => _selectedECU;
            set
            {
                _selectedECU = value;
                OnPropertyChanged(nameof(SelectedECU));
                OnPropertyChanged(nameof(EEPROMSizeFormatted));
                OnPropertyChanged(nameof(FlashSizeFormatted));
                OnPropertyChanged(nameof(MicrocontrollerType));

                // Update selected communication speed mode based on the ECU
                if (_selectedECU != null)
                {
                    SelectedCommunicationSpeedMode = _selectedECU.CurrentCommunicationSpeedMode;
                }

                // Clear data when ECU changes
                EEPROMData = null;
                MCUCode = null;
                MemorySegments.Clear();

                // Update command states
                UpdateCommandStates();
            }
        }

        /// <summary>
        /// Gets the available communication speed modes
        /// </summary>
        public Array CommunicationSpeedModes => Enum.GetValues(typeof(CommunicationSpeedMode));

        /// <summary>
        /// Gets or sets the selected communication speed mode
        /// </summary>
        public CommunicationSpeedMode SelectedCommunicationSpeedMode
        {
            get => _selectedCommunicationSpeedMode;
            set
            {
                _selectedCommunicationSpeedMode = value;
                OnPropertyChanged(nameof(SelectedCommunicationSpeedMode));

                // Update command states
                UpdateCommandStates();
            }
        }

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged(nameof(StatusMessage));
            }
        }

        /// <summary>
        /// Gets or sets the progress value
        /// </summary>
        public double ProgressValue
        {
            get => _progressValue;
            set
            {
                _progressValue = value;
                OnPropertyChanged(nameof(ProgressValue));
            }
        }

        /// <summary>
        /// Gets or sets the progress visibility
        /// </summary>
        public Visibility ProgressVisibility
        {
            get => _progressVisibility;
            set
            {
                _progressVisibility = value;
                OnPropertyChanged(nameof(ProgressVisibility));
            }
        }

        #endregion

        #region EEPROM Properties

        /// <summary>
        /// Gets or sets the EEPROM data
        /// </summary>
        public EEPROMData EEPROMData
        {
            get => _eepromData;
            set
            {
                _eepromData = value;
                OnPropertyChanged(nameof(EEPROMData));

                // Update related properties
                if (value != null)
                {
                    EEPROMDataPreview = GenerateEEPROMDataPreview(value.Data);
                    EEPROMChecksum = value.Checksum;
                    EEPROMStatus = "Valid";
                }
                else
                {
                    EEPROMDataPreview = "No EEPROM data available";
                    EEPROMChecksum = "N/A";
                    EEPROMStatus = "Not Available";
                }

                // Update command states
                UpdateCommandStates();
            }
        }

        /// <summary>
        /// Gets or sets the EEPROM data preview
        /// </summary>
        public string EEPROMDataPreview
        {
            get => _eepromDataPreview;
            set
            {
                _eepromDataPreview = value;
                OnPropertyChanged(nameof(EEPROMDataPreview));
            }
        }

        /// <summary>
        /// Gets or sets the EEPROM checksum
        /// </summary>
        public string EEPROMChecksum
        {
            get => _eepromChecksum;
            set
            {
                _eepromChecksum = value;
                OnPropertyChanged(nameof(EEPROMChecksum));
            }
        }

        /// <summary>
        /// Gets or sets the EEPROM status
        /// </summary>
        public string EEPROMStatus
        {
            get => _eepromStatus;
            set
            {
                _eepromStatus = value;
                OnPropertyChanged(nameof(EEPROMStatus));
            }
        }

        /// <summary>
        /// Gets or sets the last EEPROM read time
        /// </summary>
        public DateTime? LastEEPROMReadTime
        {
            get => _lastEEPROMReadTime;
            set
            {
                _lastEEPROMReadTime = value;
                OnPropertyChanged(nameof(LastEEPROMReadTime));
            }
        }

        /// <summary>
        /// Gets or sets the last EEPROM write time
        /// </summary>
        public DateTime? LastEEPROMWriteTime
        {
            get => _lastEEPROMWriteTime;
            set
            {
                _lastEEPROMWriteTime = value;
                OnPropertyChanged(nameof(LastEEPROMWriteTime));
            }
        }

        /// <summary>
        /// Gets the formatted EEPROM size
        /// </summary>
        public string EEPROMSizeFormatted
        {
            get
            {
                if (SelectedECU == null)
                    return "N/A";

                return FormatByteSize(SelectedECU.EEPROMSize);
            }
        }

        #endregion

        #region MCU Code Properties

        /// <summary>
        /// Gets or sets the microcontroller code
        /// </summary>
        public MicrocontrollerCode MCUCode
        {
            get => _mcuCode;
            set
            {
                _mcuCode = value;
                OnPropertyChanged(nameof(MCUCode));

                // Update related properties
                if (value != null)
                {
                    MCUCodeChecksum = value.Checksum;
                    MCUCodeStatus = "Valid";

                    // Update memory segments
                    MemorySegments.Clear();
                    foreach (var segment in value.MemorySegments)
                    {
                        MemorySegments.Add(segment);
                    }
                }
                else
                {
                    MCUCodeChecksum = "N/A";
                    MCUCodeStatus = "Not Available";
                    MemorySegments.Clear();
                }

                // Update command states
                UpdateCommandStates();
            }
        }

        /// <summary>
        /// Gets or sets the microcontroller code checksum
        /// </summary>
        public string MCUCodeChecksum
        {
            get => _mcuCodeChecksum;
            set
            {
                _mcuCodeChecksum = value;
                OnPropertyChanged(nameof(MCUCodeChecksum));
            }
        }

        /// <summary>
        /// Gets or sets the microcontroller code status
        /// </summary>
        public string MCUCodeStatus
        {
            get => _mcuCodeStatus;
            set
            {
                _mcuCodeStatus = value;
                OnPropertyChanged(nameof(MCUCodeStatus));
            }
        }

        /// <summary>
        /// Gets or sets the last microcontroller code read time
        /// </summary>
        public DateTime? LastMCUCodeReadTime
        {
            get => _lastMCUCodeReadTime;
            set
            {
                _lastMCUCodeReadTime = value;
                OnPropertyChanged(nameof(LastMCUCodeReadTime));
            }
        }

        /// <summary>
        /// Gets or sets the last microcontroller code write time
        /// </summary>
        public DateTime? LastMCUCodeWriteTime
        {
            get => _lastMCUCodeWriteTime;
            set
            {
                _lastMCUCodeWriteTime = value;
                OnPropertyChanged(nameof(LastMCUCodeWriteTime));
            }
        }

        /// <summary>
        /// Gets or sets the memory segments
        /// </summary>
        public ObservableCollection<MemorySegment> MemorySegments
        {
            get => _memorySegments;
            set
            {
                _memorySegments = value;
                OnPropertyChanged(nameof(MemorySegments));
            }
        }

        /// <summary>
        /// Gets the formatted flash size
        /// </summary>
        public string FlashSizeFormatted
        {
            get
            {
                if (SelectedECU == null)
                    return "N/A";

                return FormatByteSize(SelectedECU.FlashSize);
            }
        }

        /// <summary>
        /// Gets the microcontroller type
        /// </summary>
        public string MicrocontrollerType
        {
            get
            {
                if (SelectedECU == null)
                    return "N/A";

                return SelectedECU.MicrocontrollerType;
            }
        }

        #endregion

        #region Commands

        /// <summary>
        /// Gets the command to read EEPROM data
        /// </summary>
        public ICommand ReadEEPROMCommand { get; private set; }

        /// <summary>
        /// Gets the command to write EEPROM data
        /// </summary>
        public ICommand WriteEEPROMCommand { get; private set; }

        /// <summary>
        /// Gets the command to save EEPROM data
        /// </summary>
        public ICommand SaveEEPROMCommand { get; private set; }

        /// <summary>
        /// Gets the command to load EEPROM data
        /// </summary>
        public ICommand LoadEEPROMCommand { get; private set; }

        /// <summary>
        /// Gets the command to read microcontroller code
        /// </summary>
        public ICommand ReadMCUCodeCommand { get; private set; }

        /// <summary>
        /// Gets the command to write microcontroller code
        /// </summary>
        public ICommand WriteMCUCodeCommand { get; private set; }

        /// <summary>
        /// Gets the command to save microcontroller code
        /// </summary>
        public ICommand SaveMCUCodeCommand { get; private set; }

        /// <summary>
        /// Gets the command to load microcontroller code
        /// </summary>
        public ICommand LoadMCUCodeCommand { get; private set; }

        /// <summary>
        /// Gets the command to set the communication speed mode
        /// </summary>
        public ICommand SetCommunicationSpeedModeCommand { get; private set; }

        #endregion

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Event for property change notification
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">The name of the property that changed</param>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Loads the connected ECUs
        /// </summary>
        private void LoadConnectedECUs()
        {
            try
            {
                if (_ecuCommunicationService == null)
                {
                    StatusMessage = "ECU Communication Service not available";
                    return;
                }

                ConnectedECUs.Clear();

                foreach (var ecu in _ecuCommunicationService.ConnectedECUs)
                {
                    ConnectedECUs.Add(ecu);
                }

                if (ConnectedECUs.Count > 0)
                {
                    SelectedECU = ConnectedECUs[0];
                    StatusMessage = $"Loaded {ConnectedECUs.Count} connected ECUs";
                }
                else
                {
                    StatusMessage = "No connected ECUs found";
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("Error loading connected ECUs", "ECUFlashViewModel", ex);
                StatusMessage = $"Error loading connected ECUs: {ex.Message}";
            }
        }

        /// <summary>
        /// Updates the command states
        /// </summary>
        private void UpdateCommandStates()
        {
            // Force command state evaluation
            (ReadEEPROMCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (WriteEEPROMCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (SaveEEPROMCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (LoadEEPROMCommand as RelayCommand)?.RaiseCanExecuteChanged();

            (ReadMCUCodeCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (WriteMCUCodeCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (SaveMCUCodeCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (LoadMCUCodeCommand as RelayCommand)?.RaiseCanExecuteChanged();

            (SetCommunicationSpeedModeCommand as RelayCommand)?.RaiseCanExecuteChanged();
        }

        /// <summary>
        /// Formats a byte size to a human-readable string
        /// </summary>
        /// <param name="bytes">The size in bytes</param>
        /// <returns>A formatted string</returns>
        private string FormatByteSize(int bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;

            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }

            return $"{len:0.##} {sizes[order]} ({bytes:N0} bytes)";
        }

        /// <summary>
        /// Generates a preview of EEPROM data
        /// </summary>
        /// <param name="data">The EEPROM data</param>
        /// <returns>A formatted preview string</returns>
        private string GenerateEEPROMDataPreview(byte[] data)
        {
            if (data == null || data.Length == 0)
                return "No data available";

            StringBuilder sb = new StringBuilder();

            // Show up to 16 bytes per line, with address at the start
            for (int i = 0; i < Math.Min(data.Length, 256); i += 16)
            {
                // Add address
                sb.AppendFormat("{0:X8}: ", i);

                // Add hex values
                for (int j = 0; j < 16; j++)
                {
                    if (i + j < data.Length)
                        sb.AppendFormat("{0:X2} ", data[i + j]);
                    else
                        sb.Append("   ");

                    // Add extra space in the middle
                    if (j == 7)
                        sb.Append(" ");
                }

                sb.Append(" | ");

                // Add ASCII representation
                for (int j = 0; j < 16; j++)
                {
                    if (i + j < data.Length)
                    {
                        char c = (char)data[i + j];
                        if (c >= 32 && c <= 126) // Printable ASCII
                            sb.Append(c);
                        else
                            sb.Append('.');
                    }
                    else
                    {
                        sb.Append(' ');
                    }
                }

                sb.AppendLine();
            }

            // Add ellipsis if there's more data
            if (data.Length > 256)
                sb.AppendLine("...");

            return sb.ToString();
        }

        #endregion

        #region Command Methods

        #region EEPROM Command Methods

        /// <summary>
        /// Determines whether the ReadEEPROM command can execute
        /// </summary>
        /// <returns>True if the command can execute, false otherwise</returns>
        private bool CanReadEEPROM()
        {
            return SelectedECU != null &&
                   _ecuCommunicationService != null &&
                   SelectedECU.ConnectionStatus == ECUConnectionStatus.Connected;
        }

        /// <summary>
        /// Executes the ReadEEPROM command
        /// </summary>
        private async void ReadEEPROM()
        {
            try
            {
                StatusMessage = $"Reading EEPROM data from {SelectedECU.Name}...";
                ProgressVisibility = Visibility.Visible;
                ProgressValue = 0;

                // Create a progress reporter
                var progress = new Progress<int>(value =>
                {
                    ProgressValue = value;
                });

                // Read EEPROM data
                EEPROMData = await _ecuCommunicationService.ReadEEPROMDataAsync(SelectedECU);

                if (EEPROMData != null)
                {
                    LastEEPROMReadTime = DateTime.Now;
                    StatusMessage = $"Successfully read EEPROM data from {SelectedECU.Name}";
                }
                else
                {
                    StatusMessage = $"Failed to read EEPROM data from {SelectedECU.Name}";
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error reading EEPROM data from {SelectedECU?.Name}", "ECUFlashViewModel", ex);
                StatusMessage = $"Error reading EEPROM data: {ex.Message}";
            }
            finally
            {
                ProgressVisibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// Determines whether the WriteEEPROM command can execute
        /// </summary>
        /// <returns>True if the command can execute, false otherwise</returns>
        private bool CanWriteEEPROM()
        {
            return SelectedECU != null &&
                   _ecuCommunicationService != null &&
                   SelectedECU.ConnectionStatus == ECUConnectionStatus.Connected &&
                   EEPROMData != null;
        }

        /// <summary>
        /// Executes the WriteEEPROM command
        /// </summary>
        private async void WriteEEPROM()
        {
            try
            {
                // Confirm with the user
                if (MessageBox.Show(
                    $"Are you sure you want to write EEPROM data to {SelectedECU.Name}?\n\nThis operation cannot be undone and may damage the ECU if the data is incorrect.",
                    "Confirm EEPROM Write",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning) != MessageBoxResult.Yes)
                {
                    return;
                }

                StatusMessage = $"Writing EEPROM data to {SelectedECU.Name}...";
                ProgressVisibility = Visibility.Visible;
                ProgressValue = 0;

                // Create a progress reporter
                var progress = new Progress<int>(value =>
                {
                    ProgressValue = value;
                });

                // Write EEPROM data
                bool success = await _ecuCommunicationService.WriteEEPROMDataAsync(SelectedECU, EEPROMData);

                if (success)
                {
                    LastEEPROMWriteTime = DateTime.Now;
                    StatusMessage = $"Successfully wrote EEPROM data to {SelectedECU.Name}";
                }
                else
                {
                    StatusMessage = $"Failed to write EEPROM data to {SelectedECU.Name}";
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error writing EEPROM data to {SelectedECU?.Name}", "ECUFlashViewModel", ex);
                StatusMessage = $"Error writing EEPROM data: {ex.Message}";
            }
            finally
            {
                ProgressVisibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// Determines whether the SaveEEPROM command can execute
        /// </summary>
        /// <returns>True if the command can execute, false otherwise</returns>
        private bool CanSaveEEPROM()
        {
            return EEPROMData != null && EEPROMData.Data != null && EEPROMData.Data.Length > 0;
        }

        /// <summary>
        /// Executes the SaveEEPROM command
        /// </summary>
        private void SaveEEPROM()
        {
            try
            {
                // Create a save file dialog
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "EEPROM Data Files (*.eeprom)|*.eeprom|Binary Files (*.bin)|*.bin|All Files (*.*)|*.*",
                    DefaultExt = ".eeprom",
                    Title = "Save EEPROM Data",
                    FileName = $"{SelectedECU.Name}_EEPROM_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                // Show the dialog
                if (saveFileDialog.ShowDialog() == true)
                {
                    // Save the file
                    File.WriteAllBytes(saveFileDialog.FileName, EEPROMData.Data);
                    StatusMessage = $"EEPROM data saved to {saveFileDialog.FileName}";
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("Error saving EEPROM data", "ECUFlashViewModel", ex);
                StatusMessage = $"Error saving EEPROM data: {ex.Message}";
            }
        }

        /// <summary>
        /// Determines whether the LoadEEPROM command can execute
        /// </summary>
        /// <returns>True if the command can execute, false otherwise</returns>
        private bool CanLoadEEPROM()
        {
            return SelectedECU != null;
        }

        /// <summary>
        /// Executes the LoadEEPROM command
        /// </summary>
        private void LoadEEPROM()
        {
            try
            {
                // Create an open file dialog
                OpenFileDialog openFileDialog = new OpenFileDialog
                {
                    Filter = "EEPROM Data Files (*.eeprom)|*.eeprom|Binary Files (*.bin)|*.bin|All Files (*.*)|*.*",
                    Title = "Load EEPROM Data"
                };

                // Show the dialog
                if (openFileDialog.ShowDialog() == true)
                {
                    // Load the file
                    byte[] data = File.ReadAllBytes(openFileDialog.FileName);

                    // Create a new EEPROM data object
                    EEPROMData = new EEPROMData
                    {
                        ECUId = SelectedECU.Id,
                        ECUName = SelectedECU.Name,
                        Timestamp = DateTime.Now,
                        Data = data,
                        Version = SelectedECU.SoftwareVersion,
                        Metadata = $"Loaded from {openFileDialog.FileName} on {DateTime.Now}"
                    };

                    // Calculate the checksum
                    EEPROMData.Checksum = EEPROMData.CalculateChecksum();

                    StatusMessage = $"EEPROM data loaded from {openFileDialog.FileName}";
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("Error loading EEPROM data", "ECUFlashViewModel", ex);
                StatusMessage = $"Error loading EEPROM data: {ex.Message}";
            }
        }

        #endregion

        #region Communication Speed Mode Command Methods

        /// <summary>
        /// Determines whether the SetCommunicationSpeedMode command can execute
        /// </summary>
        /// <returns>True if the command can execute, false otherwise</returns>
        private bool CanSetCommunicationSpeedMode()
        {
            return SelectedECU != null &&
                   _ecuCommunicationService != null &&
                   SelectedECU.ConnectionStatus == ECUConnectionStatus.Connected;
        }

        /// <summary>
        /// Executes the SetCommunicationSpeedMode command
        /// </summary>
        private async void SetCommunicationSpeedMode()
        {
            try
            {
                StatusMessage = $"Setting communication speed mode to {SelectedCommunicationSpeedMode} for {SelectedECU.Name}...";

                // Set the communication speed mode
                bool success = await _ecuCommunicationService.SetCommunicationSpeedModeAsync(SelectedECU, SelectedCommunicationSpeedMode);

                if (success)
                {
                    StatusMessage = $"Successfully set communication speed mode to {SelectedCommunicationSpeedMode} for {SelectedECU.Name}";
                }
                else
                {
                    StatusMessage = $"Failed to set communication speed mode to {SelectedCommunicationSpeedMode} for {SelectedECU.Name}";

                    // Reset the selected communication speed mode to the ECU's current mode
                    SelectedCommunicationSpeedMode = SelectedECU.CurrentCommunicationSpeedMode;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error setting communication speed mode for {SelectedECU?.Name}", "ECUFlashViewModel", ex);
                StatusMessage = $"Error setting communication speed mode: {ex.Message}";

                // Reset the selected communication speed mode to the ECU's current mode
                if (SelectedECU != null)
                {
                    SelectedCommunicationSpeedMode = SelectedECU.CurrentCommunicationSpeedMode;
                }
            }
        }

        #endregion

        #region MCU Code Command Methods

        /// <summary>
        /// Determines whether the ReadMCUCode command can execute
        /// </summary>
        /// <returns>True if the command can execute, false otherwise</returns>
        private bool CanReadMCUCode()
        {
            return SelectedECU != null &&
                   _ecuCommunicationService != null &&
                   SelectedECU.ConnectionStatus == ECUConnectionStatus.Connected;
        }

        /// <summary>
        /// Executes the ReadMCUCode command
        /// </summary>
        private async void ReadMCUCode()
        {
            try
            {
                StatusMessage = $"Reading microcontroller code from {SelectedECU.Name}...";
                ProgressVisibility = Visibility.Visible;
                ProgressValue = 0;

                // Create a progress reporter
                var progress = new Progress<int>(value =>
                {
                    ProgressValue = value;
                });

                // Read microcontroller code
                MCUCode = await _ecuCommunicationService.ReadMicrocontrollerCodeDataAsync(SelectedECU);

                if (MCUCode != null)
                {
                    LastMCUCodeReadTime = DateTime.Now;
                    StatusMessage = $"Successfully read microcontroller code from {SelectedECU.Name}";
                }
                else
                {
                    StatusMessage = $"Failed to read microcontroller code from {SelectedECU.Name}";
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error reading microcontroller code from {SelectedECU?.Name}", "ECUFlashViewModel", ex);
                StatusMessage = $"Error reading microcontroller code: {ex.Message}";
            }
            finally
            {
                ProgressVisibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// Determines whether the WriteMCUCode command can execute
        /// </summary>
        /// <returns>True if the command can execute, false otherwise</returns>
        private bool CanWriteMCUCode()
        {
            return SelectedECU != null &&
                   _ecuCommunicationService != null &&
                   SelectedECU.ConnectionStatus == ECUConnectionStatus.Connected &&
                   MCUCode != null;
        }

        /// <summary>
        /// Executes the WriteMCUCode command
        /// </summary>
        private async void WriteMCUCode()
        {
            try
            {
                // Confirm with the user
                if (MessageBox.Show(
                    $"Are you sure you want to write microcontroller code to {SelectedECU.Name}?\n\nThis operation cannot be undone and may brick the ECU if the code is incorrect.",
                    "Confirm Microcontroller Code Write",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning) != MessageBoxResult.Yes)
                {
                    return;
                }

                StatusMessage = $"Writing microcontroller code to {SelectedECU.Name}...";
                ProgressVisibility = Visibility.Visible;
                ProgressValue = 0;

                // Create a progress reporter
                var progress = new Progress<int>(value =>
                {
                    ProgressValue = value;
                });

                // Write microcontroller code
                bool success = await _ecuCommunicationService.WriteMicrocontrollerCodeDataAsync(SelectedECU, MCUCode);

                if (success)
                {
                    LastMCUCodeWriteTime = DateTime.Now;
                    StatusMessage = $"Successfully wrote microcontroller code to {SelectedECU.Name}";
                }
                else
                {
                    StatusMessage = $"Failed to write microcontroller code to {SelectedECU.Name}";
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error writing microcontroller code to {SelectedECU?.Name}", "ECUFlashViewModel", ex);
                StatusMessage = $"Error writing microcontroller code: {ex.Message}";
            }
            finally
            {
                ProgressVisibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// Determines whether the SaveMCUCode command can execute
        /// </summary>
        /// <returns>True if the command can execute, false otherwise</returns>
        private bool CanSaveMCUCode()
        {
            return MCUCode != null && MCUCode.Code != null && MCUCode.Code.Length > 0;
        }

        /// <summary>
        /// Executes the SaveMCUCode command
        /// </summary>
        private void SaveMCUCode()
        {
            try
            {
                // Create a save file dialog
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "Microcontroller Code Files (*.bin)|*.bin|All Files (*.*)|*.*",
                    DefaultExt = ".bin",
                    Title = "Save Microcontroller Code",
                    FileName = $"{SelectedECU.Name}_MCU_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                // Show the dialog
                if (saveFileDialog.ShowDialog() == true)
                {
                    // Save the file
                    File.WriteAllBytes(saveFileDialog.FileName, MCUCode.Code);
                    StatusMessage = $"Microcontroller code saved to {saveFileDialog.FileName}";
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("Error saving microcontroller code", "ECUFlashViewModel", ex);
                StatusMessage = $"Error saving microcontroller code: {ex.Message}";
            }
        }

        /// <summary>
        /// Determines whether the LoadMCUCode command can execute
        /// </summary>
        /// <returns>True if the command can execute, false otherwise</returns>
        private bool CanLoadMCUCode()
        {
            return SelectedECU != null;
        }

        /// <summary>
        /// Executes the LoadMCUCode command
        /// </summary>
        private void LoadMCUCode()
        {
            try
            {
                // Create an open file dialog
                OpenFileDialog openFileDialog = new OpenFileDialog
                {
                    Filter = "Microcontroller Code Files (*.bin)|*.bin|All Files (*.*)|*.*",
                    Title = "Load Microcontroller Code"
                };

                // Show the dialog
                if (openFileDialog.ShowDialog() == true)
                {
                    // Load the file
                    byte[] code = File.ReadAllBytes(openFileDialog.FileName);

                    // Create a new microcontroller code object
                    MCUCode = new MicrocontrollerCode
                    {
                        ECUId = SelectedECU.Id,
                        ECUName = SelectedECU.Name,
                        Timestamp = DateTime.Now,
                        Code = code,
                        Version = SelectedECU.SoftwareVersion,
                        MicrocontrollerType = SelectedECU.MicrocontrollerType,
                        Metadata = $"Loaded from {openFileDialog.FileName} on {DateTime.Now}"
                    };

                    // Calculate the checksum
                    MCUCode.Checksum = MCUCode.CalculateChecksum();

                    // Create a default memory segment
                    MCUCode.MemorySegments.Add(new MemorySegment
                    {
                        StartAddress = 0,
                        EndAddress = (uint)(code.Length - 1),
                        Data = code,
                        SegmentType = "Flash",
                        IsReadOnly = true
                    });

                    StatusMessage = $"Microcontroller code loaded from {openFileDialog.FileName}";
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("Error loading microcontroller code", "ECUFlashViewModel", ex);
                StatusMessage = $"Error loading microcontroller code: {ex.Message}";
            }
        }

        #endregion
        #endregion
        #endregion
    }
}

