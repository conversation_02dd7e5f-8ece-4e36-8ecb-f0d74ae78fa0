using System;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Microcontroller;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Communication.Tests.Microcontroller
{
    [TestFixture]
    public class FlashOperationRetryTests
    {
        private Mock<ILoggingService> _mockLogger;
        private FlashOperationRetry _flashRetry;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _flashRetry = new FlashOperationRetry(_mockLogger.Object, 3, 10);
        }

        [Test]
        public async Task ExecuteWithRetryAsync_SuccessOnFirstAttempt_ReturnsResult()
        {
            // Arrange
            uint address = 0x1000;
            int expectedResult = 42;
            int callCount = 0;

            // Act
            int result = await _flashRetry.ExecuteWithRetryAsync<int>(
                () =>
                {
                    callCount++;
                    return Task.FromResult(expectedResult);
                },
                "TestOperation",
                address);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
            Assert.That(callCount, Is.EqualTo(1));
        }

        [Test]
        public async Task ExecuteWithRetryAsync_SuccessOnSecondAttempt_ReturnsResult()
        {
            // Arrange
            uint address = 0x1000;
            int expectedResult = 42;
            int callCount = 0;

            // Act
            int result = await _flashRetry.ExecuteWithRetryAsync<int>(
                () =>
                {
                    callCount++;
                    if (callCount == 1)
                    {
                        throw new Exception("Simulated failure");
                    }
                    return Task.FromResult(expectedResult);
                },
                "TestOperation",
                address);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
            Assert.That(callCount, Is.EqualTo(2));
        }

        [Test]
        public void ExecuteWithRetryAsync_FailsAllAttempts_ThrowsException()
        {
            // Arrange
            uint address = 0x1000;
            int callCount = 0;

            // Act & Assert
            Assert.ThrowsAsync<Exception>(async () =>
            {
                await _flashRetry.ExecuteWithRetryAsync<int>(
                    () =>
                    {
                        callCount++;
                        throw new Exception("Simulated failure");
                    },
                    "TestOperation",
                    address);
            });

            Assert.That(callCount, Is.EqualTo(4)); // Initial attempt + 3 retries
        }

        [Test]
        public async Task ExecuteWithRetryAndValidationAsync_SuccessOnFirstAttempt_ReturnsResult()
        {
            // Arrange
            uint address = 0x1000;
            int expectedResult = 42;
            int callCount = 0;

            // Act
            int result = await _flashRetry.ExecuteWithRetryAndValidationAsync<int>(
                () =>
                {
                    callCount++;
                    return Task.FromResult(expectedResult);
                },
                r => r == expectedResult,
                "TestOperation",
                address);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
            Assert.That(callCount, Is.EqualTo(1));
        }

        [Test]
        public async Task ExecuteWithRetryAndValidationAsync_ValidationFailsOnceAndThenSucceeds_ReturnsResult()
        {
            // Arrange
            uint address = 0x1000;
            int expectedResult = 42;
            int callCount = 0;

            // Act
            int result = await _flashRetry.ExecuteWithRetryAndValidationAsync<int>(
                () =>
                {
                    callCount++;
                    return Task.FromResult(callCount == 1 ? 0 : expectedResult); // First attempt returns invalid result
                },
                r => r == expectedResult,
                "TestOperation",
                address);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
            Assert.That(callCount, Is.EqualTo(2));
        }

        [Test]
        public void ExecuteWithRetryAndValidationAsync_ValidationFailsAllAttempts_ThrowsException()
        {
            // Arrange
            uint address = 0x1000;
            int callCount = 0;

            // Act & Assert
            Assert.ThrowsAsync<Exception>(async () =>
            {
                await _flashRetry.ExecuteWithRetryAndValidationAsync<int>(
                    () =>
                    {
                        callCount++;
                        return Task.FromResult(0); // Always returns invalid result
                    },
                    r => r == 42, // Validation always fails
                    "TestOperation",
                    address);
            });

            Assert.That(callCount, Is.EqualTo(4)); // Initial attempt + 3 retries
        }
    }
}

