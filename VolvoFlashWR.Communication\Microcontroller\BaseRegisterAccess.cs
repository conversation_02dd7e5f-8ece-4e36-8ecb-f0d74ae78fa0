using System;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Microcontroller
{
    /// <summary>
    /// Base class for accessing microcontroller registers
    /// </summary>
    public abstract class BaseRegisterAccess : IRegisterAccess
    {
        protected readonly ILoggingService _logger;
        protected readonly IVocomService _vocomService;

        /// <summary>
        /// Initializes a new instance of the BaseRegisterAccess class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="vocomService">The Vocom service</param>
        protected BaseRegisterAccess(ILoggingService logger, IVocomService vocomService)
        {
            _logger = logger;
            _vocomService = vocomService ?? throw new ArgumentNullException(nameof(vocomService));
        }

        /// <summary>
        /// Reads a byte from a register
        /// </summary>
        /// <param name="register">The register address</param>
        /// <returns>The register value</returns>
        public abstract Task<byte> ReadRegisterByteAsync(uint register);

        /// <summary>
        /// Reads a word (2 bytes) from a register
        /// </summary>
        /// <param name="register">The register address</param>
        /// <returns>The register value</returns>
        public virtual async Task<ushort> ReadRegisterWordAsync(uint register)
        {
            byte lowByte = await ReadRegisterByteAsync(register);
            byte highByte = await ReadRegisterByteAsync(register + 1);
            return (ushort)((highByte << 8) | lowByte);
        }

        /// <summary>
        /// Reads a long word (4 bytes) from a register
        /// </summary>
        /// <param name="register">The register address</param>
        /// <returns>The register value</returns>
        public virtual async Task<uint> ReadRegisterLongAsync(uint register)
        {
            ushort lowWord = await ReadRegisterWordAsync(register);
            ushort highWord = await ReadRegisterWordAsync(register + 2);
            return ((uint)highWord << 16) | lowWord;
        }

        /// <summary>
        /// Writes a byte to a register
        /// </summary>
        /// <param name="register">The register address</param>
        /// <param name="value">The value to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public abstract Task<bool> WriteRegisterByteAsync(uint register, byte value);

        /// <summary>
        /// Writes a word (2 bytes) to a register
        /// </summary>
        /// <param name="register">The register address</param>
        /// <param name="value">The value to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public virtual async Task<bool> WriteRegisterWordAsync(uint register, ushort value)
        {
            byte lowByte = (byte)(value & 0xFF);
            byte highByte = (byte)((value >> 8) & 0xFF);

            bool lowResult = await WriteRegisterByteAsync(register, lowByte);
            bool highResult = await WriteRegisterByteAsync(register + 1, highByte);

            return lowResult && highResult;
        }

        /// <summary>
        /// Writes a long word (4 bytes) to a register
        /// </summary>
        /// <param name="register">The register address</param>
        /// <param name="value">The value to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public virtual async Task<bool> WriteRegisterLongAsync(uint register, uint value)
        {
            ushort lowWord = (ushort)(value & 0xFFFF);
            ushort highWord = (ushort)((value >> 16) & 0xFFFF);

            bool lowResult = await WriteRegisterWordAsync(register, lowWord);
            bool highResult = await WriteRegisterWordAsync(register + 2, highWord);

            return lowResult && highResult;
        }

        /// <summary>
        /// Sends a command to the Vocom device
        /// </summary>
        /// <param name="command">The command to send</param>
        /// <returns>The response from the Vocom device</returns>
        protected abstract Task<byte[]> SendCommandToVocomAsync(byte[] command);
    }
}
