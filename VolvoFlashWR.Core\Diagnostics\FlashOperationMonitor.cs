using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Utilities;

namespace VolvoFlashWR.Core.Diagnostics
{
    /// <summary>
    /// Monitors flash memory operations and provides real-time diagnostics
    /// </summary>
    public class FlashOperationMonitor : IDisposable
    {
        private readonly ILoggingService _logger;
        private readonly Timer _monitorTimer;
        private readonly object _lockObject = new object();
        private readonly List<FlashOperation> _operations = new List<FlashOperation>();
        private readonly List<FlashOperationListener> _listeners = new List<FlashOperationListener>();
        private bool _isMonitoring;
        private int _monitorIntervalMs = 1000; // Default monitoring interval: 1 second

        /// <summary>
        /// Gets the collection of flash operations being monitored
        /// </summary>
        public ReadOnlyCollection<FlashOperation> Operations => _operations.AsReadOnly();

        /// <summary>
        /// Gets or sets the monitoring interval in milliseconds
        /// </summary>
        public int MonitorIntervalMs
        {
            get => _monitorIntervalMs;
            set
            {
                if (value < 100)
                {
                    throw new ArgumentException("Monitor interval must be at least 100ms", nameof(value));
                }

                _monitorIntervalMs = value;

                // Update the timer if monitoring is active
                if (_isMonitoring)
                {
                    _monitorTimer.Change(0, _monitorIntervalMs);
                }
            }
        }

        /// <summary>
        /// Gets whether monitoring is currently active
        /// </summary>
        public bool IsMonitoring => _isMonitoring;

        /// <summary>
        /// Event raised when a flash operation is started
        /// </summary>
        public event EventHandler<FlashOperationEventArgs> OperationStarted;

        /// <summary>
        /// Event raised when a flash operation is completed
        /// </summary>
        public event EventHandler<FlashOperationEventArgs> OperationCompleted;

        /// <summary>
        /// Event raised when a flash operation is updated
        /// </summary>
        public event EventHandler<FlashOperationEventArgs> OperationUpdated;

        /// <summary>
        /// Event raised when a flash operation fails
        /// </summary>
        public event EventHandler<FlashOperationEventArgs> OperationFailed;

        /// <summary>
        /// Initializes a new instance of the FlashOperationMonitor class
        /// </summary>
        /// <param name="logger">The logger to use</param>
        public FlashOperationMonitor(ILoggingService logger)
        {
            _logger = logger;
            _monitorTimer = new Timer(MonitorCallback, null, Timeout.Infinite, Timeout.Infinite);
        }

        /// <summary>
        /// Starts monitoring flash operations
        /// </summary>
        public void StartMonitoring()
        {
            if (!_isMonitoring)
            {
                _isMonitoring = true;
                _monitorTimer.Change(0, _monitorIntervalMs);
                _logger?.LogInformation($"Flash operation monitoring started with interval {_monitorIntervalMs}ms", "FlashOperationMonitor");
            }
        }

        /// <summary>
        /// Stops monitoring flash operations
        /// </summary>
        public void StopMonitoring()
        {
            if (_isMonitoring)
            {
                _isMonitoring = false;
                _monitorTimer.Change(Timeout.Infinite, Timeout.Infinite);
                _logger?.LogInformation("Flash operation monitoring stopped", "FlashOperationMonitor");
            }
        }

        /// <summary>
        /// Registers a flash operation for monitoring
        /// </summary>
        /// <param name="operation">The flash operation to monitor</param>
        /// <returns>The operation ID</returns>
        public Guid RegisterOperation(FlashOperation operation)
        {
            if (operation == null)
            {
                throw new ArgumentNullException(nameof(operation));
            }

            lock (_lockObject)
            {
                _operations.Add(operation);
                _logger?.LogInformation($"Registered flash operation: {operation.OperationType} at address 0x{operation.Address:X8}, size: {operation.Size} bytes", "FlashOperationMonitor");

                // Raise the operation started event
                OperationStarted?.Invoke(this, new FlashOperationEventArgs(operation));

                return operation.Id;
            }
        }

        /// <summary>
        /// Updates the progress of a flash operation
        /// </summary>
        /// <param name="operationId">The operation ID</param>
        /// <param name="bytesProcessed">The number of bytes processed</param>
        /// <param name="status">The operation status</param>
        public void UpdateOperationProgress(Guid operationId, int bytesProcessed, FlashOperationStatus status)
        {
            lock (_lockObject)
            {
                var operation = _operations.FirstOrDefault(o => o.Id == operationId);
                if (operation != null)
                {
                    // Calculate performance metrics
                    int bytesDelta = bytesProcessed - operation.BytesProcessed;
                    if (bytesDelta > 0)
                    {
                        var timeDelta = (DateTime.Now - operation.LastUpdated).TotalMilliseconds;
                        if (timeDelta > 0)
                        {
                            // Add performance metric
                            operation.AddPerformanceMetric(bytesDelta, timeDelta);

                            _logger?.LogDebug($"Performance metric for operation {operationId}: {bytesDelta} bytes in {timeDelta:F1}ms ({bytesDelta / (timeDelta / 1000.0):F1} bytes/sec)", "FlashOperationMonitor");
                        }
                    }

                    // Update operation properties
                    operation.BytesProcessed = bytesProcessed;
                    operation.Status = status;
                    operation.LastUpdated = DateTime.Now;

                    _logger?.LogDebug($"Updated flash operation {operationId}: {bytesProcessed}/{operation.Size} bytes processed, status: {status}", "FlashOperationMonitor");

                    // Raise the operation updated event
                    OperationUpdated?.Invoke(this, new FlashOperationEventArgs(operation));

                    // If the operation is complete, raise the operation completed event
                    if (status == FlashOperationStatus.Completed)
                    {
                        _logger?.LogInformation($"Flash operation {operationId} completed successfully in {operation.ElapsedTime.TotalMilliseconds:F1}ms, average throughput: {operation.AverageThroughput:F1} bytes/sec, peak throughput: {operation.PeakThroughput:F1} bytes/sec", "FlashOperationMonitor");
                        OperationCompleted?.Invoke(this, new FlashOperationEventArgs(operation));
                    }
                    // If the operation failed, raise the operation failed event
                    else if (status == FlashOperationStatus.Failed)
                    {
                        _logger?.LogError($"Flash operation {operationId} failed after {operation.ElapsedTime.TotalMilliseconds:F1}ms, average throughput: {operation.AverageThroughput:F1} bytes/sec", "FlashOperationMonitor");
                        OperationFailed?.Invoke(this, new FlashOperationEventArgs(operation));
                    }
                }
            }
        }

        /// <summary>
        /// Completes a flash operation
        /// </summary>
        /// <param name="operationId">The operation ID</param>
        /// <param name="success">Whether the operation was successful</param>
        /// <param name="errorMessage">The error message if the operation failed</param>
        public void CompleteOperation(Guid operationId, bool success, string? errorMessage = null)
        {
            lock (_lockObject)
            {
                var operation = _operations.FirstOrDefault(o => o.Id == operationId);
                if (operation != null)
                {
                    operation.Status = success ? FlashOperationStatus.Completed : FlashOperationStatus.Failed;
                    operation.EndTime = DateTime.Now;
                    operation.ErrorMessage = errorMessage;

                    if (success)
                    {
                        // Ensure bytes processed is set to the full size on success
                        operation.BytesProcessed = operation.Size;
                        var elapsed = operation.EndTime.HasValue ? (operation.EndTime.Value - operation.StartTime).TotalMilliseconds : 0;
                        _logger?.LogInformation($"Flash operation {operationId} completed successfully in {elapsed:F1}ms", "FlashOperationMonitor");
                        OperationCompleted?.Invoke(this, new FlashOperationEventArgs(operation));
                    }
                    else
                    {
                        _logger?.LogError($"Flash operation {operationId} failed: {errorMessage}", "FlashOperationMonitor");
                        OperationFailed?.Invoke(this, new FlashOperationEventArgs(operation));
                    }
                }
            }
        }

        /// <summary>
        /// Gets a flash operation by ID
        /// </summary>
        /// <param name="operationId">The operation ID</param>
        /// <returns>The flash operation, or null if not found</returns>
        public FlashOperation GetOperation(Guid operationId)
        {
            lock (_lockObject)
            {
                return _operations.FirstOrDefault(o => o.Id == operationId);
            }
        }

        /// <summary>
        /// Clears completed operations from the monitor
        /// </summary>
        public void ClearCompletedOperations()
        {
            lock (_lockObject)
            {
                _operations.RemoveAll(o => o.Status == FlashOperationStatus.Completed || o.Status == FlashOperationStatus.Failed);
                _logger?.LogInformation("Cleared completed flash operations", "FlashOperationMonitor");
            }
        }

        /// <summary>
        /// Adds a listener for flash operations
        /// </summary>
        /// <param name="listener">The listener to add</param>
        public void AddListener(FlashOperationListener listener)
        {
            if (listener == null)
            {
                throw new ArgumentNullException(nameof(listener));
            }

            lock (_lockObject)
            {
                _listeners.Add(listener);
                _logger?.LogInformation($"Added flash operation listener: {listener.Name}", "FlashOperationMonitor");
            }
        }

        /// <summary>
        /// Removes a listener for flash operations
        /// </summary>
        /// <param name="listener">The listener to remove</param>
        public void RemoveListener(FlashOperationListener listener)
        {
            if (listener == null)
            {
                throw new ArgumentNullException(nameof(listener));
            }

            lock (_lockObject)
            {
                _listeners.Remove(listener);
                _logger?.LogInformation($"Removed flash operation listener: {listener.Name}", "FlashOperationMonitor");
            }
        }

        /// <summary>
        /// Callback method for the monitor timer
        /// </summary>
        private void MonitorCallback(object state)
        {
            try
            {
                // Check for stalled operations
                CheckForStalledOperations();

                // Notify listeners
                NotifyListeners();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error in flash operation monitor: {ex.Message}", "FlashOperationMonitor");
            }
        }

        /// <summary>
        /// Checks for stalled operations
        /// </summary>
        private void CheckForStalledOperations()
        {
            lock (_lockObject)
            {
                var now = DateTime.Now;
                foreach (var operation in _operations)
                {
                    // Skip operations that are already completed or failed
                    if (operation.Status == FlashOperationStatus.Completed ||
                        operation.Status == FlashOperationStatus.Failed)
                    {
                        continue;
                    }

                    // Check if the operation has been updated recently
                    var timeSinceLastUpdate = now - operation.LastUpdated;

                    // Calculate expected progress based on elapsed time and operation type
                    var elapsedTime = (now - operation.StartTime).TotalSeconds;
                    var expectedProgress = CalculateExpectedProgress(operation, elapsedTime);
                    var progressDifference = expectedProgress - ((double)operation.BytesProcessed / operation.Size);

                    // Check for stalled operations based on time since last update
                    if (timeSinceLastUpdate.TotalSeconds > 10) // Consider an operation stalled if no updates for 10 seconds
                    {
                        _logger?.LogWarning($"Flash operation {operation.Id} appears to be stalled (no updates for {timeSinceLastUpdate.TotalSeconds:F1} seconds)", "FlashOperationMonitor");

                        // Update the operation status to stalled
                        operation.Status = FlashOperationStatus.Stalled;

                        // Add stall information to additional info
                        string additionalInfo = operation.AdditionalInfo ?? "";
                        if (!additionalInfo.Contains("StallDetected:"))
                        {
                            additionalInfo = string.IsNullOrEmpty(additionalInfo) ?
                                $"StallDetected:{now:yyyy-MM-dd HH:mm:ss}" :
                                $"{additionalInfo};StallDetected:{now:yyyy-MM-dd HH:mm:ss}";
                            operation.AdditionalInfo = additionalInfo;
                        }

                        // Raise the operation updated event
                        OperationUpdated?.Invoke(this, new FlashOperationEventArgs(operation));
                    }
                    // Check for operations that are progressing much slower than expected
                    else if (progressDifference > 0.3 && elapsedTime > 5) // 30% behind expected progress and running for at least 5 seconds
                    {
                        _logger?.LogWarning($"Flash operation {operation.Id} is progressing slower than expected (expected: {expectedProgress:P0}, actual: {(double)operation.BytesProcessed / operation.Size:P0})", "FlashOperationMonitor");

                        // Add slow progress information to additional info
                        string additionalInfo = operation.AdditionalInfo ?? "";
                        if (!additionalInfo.Contains("SlowProgress:"))
                        {
                            additionalInfo = string.IsNullOrEmpty(additionalInfo) ?
                                $"SlowProgress:{progressDifference:P0}" :
                                $"{additionalInfo};SlowProgress:{progressDifference:P0}";
                            operation.AdditionalInfo = additionalInfo;
                        }

                        // Raise the operation updated event
                        OperationUpdated?.Invoke(this, new FlashOperationEventArgs(operation));
                    }
                }
            }
        }

        /// <summary>
        /// Calculates the expected progress for an operation based on elapsed time
        /// </summary>
        /// <param name="operation">The flash operation</param>
        /// <param name="elapsedSeconds">The elapsed time in seconds</param>
        /// <returns>The expected progress as a fraction (0.0 to 1.0)</returns>
        private double CalculateExpectedProgress(FlashOperation operation, double elapsedSeconds)
        {
            // Define expected rates for different operation types (bytes per second)
            double expectedRate;

            switch (operation.OperationType)
            {
                case FlashOperationType.Read:
                    expectedRate = 10240; // 10 KB/s for read operations
                    break;
                case FlashOperationType.Write:
                    expectedRate = 5120;  // 5 KB/s for write operations
                    break;
                case FlashOperationType.Erase:
                    expectedRate = 20480; // 20 KB/s for erase operations
                    break;
                case FlashOperationType.Verify:
                    expectedRate = 15360; // 15 KB/s for verify operations
                    break;
                default:
                    expectedRate = 8192;  // 8 KB/s default
                    break;
            }

            // Calculate expected bytes processed
            double expectedBytes = expectedRate * elapsedSeconds;

            // Calculate expected progress as a fraction
            double expectedProgress = Math.Min(1.0, expectedBytes / operation.Size);

            return expectedProgress;
        }

        /// <summary>
        /// Notifies listeners of current operations
        /// </summary>
        private void NotifyListeners()
        {
            lock (_lockObject)
            {
                foreach (var listener in _listeners)
                {
                    try
                    {
                        // Get active operations (not completed or failed)
                        var activeOperations = _operations.Where(o =>
                            o.Status != FlashOperationStatus.Completed &&
                            o.Status != FlashOperationStatus.Failed).ToList();

                        // Notify the listener
                        listener.OnOperationsUpdated(activeOperations);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError($"Error notifying flash operation listener {listener.Name}: {ex.Message}", "FlashOperationMonitor");
                    }
                }
            }
        }

        /// <summary>
        /// Disposes the monitor
        /// </summary>
        public void Dispose()
        {
            _monitorTimer?.Dispose();
            _logger?.LogInformation("Flash operation monitor disposed", "FlashOperationMonitor");
        }
    }

    /// <summary>
    /// Event arguments for flash operation events
    /// </summary>
    public class FlashOperationEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the flash operation
        /// </summary>
        public FlashOperation Operation { get; }

        /// <summary>
        /// Initializes a new instance of the FlashOperationEventArgs class
        /// </summary>
        /// <param name="operation">The flash operation</param>
        public FlashOperationEventArgs(FlashOperation operation)
        {
            Operation = operation;
        }
    }

    /// <summary>
    /// Base class for flash operation listeners
    /// </summary>
    public abstract class FlashOperationListener
    {
        /// <summary>
        /// Gets the name of the listener
        /// </summary>
        public string Name { get; }

        /// <summary>
        /// Initializes a new instance of the FlashOperationListener class
        /// </summary>
        /// <param name="name">The name of the listener</param>
        protected FlashOperationListener(string name)
        {
            Name = name;
        }

        /// <summary>
        /// Called when operations are updated
        /// </summary>
        /// <param name="operations">The active operations</param>
        public abstract void OnOperationsUpdated(IReadOnlyList<FlashOperation> operations);
    }
}
