using System;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Microcontroller;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Microcontroller
{
    [TestFixture]
    public class MC9S12XEP100HelperECCTests
    {
        private Mock<ILoggingService> _mockLogger;
        private Mock<IRegisterAccess> _mockRegisterAccess;
        private MC9S12XEP100Helper _helper;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _mockRegisterAccess = new Mock<IRegisterAccess>();
            _helper = new MC9S12XEP100Helper(_mockLogger.Object, _mockRegisterAccess.Object);
        }

        [Test]
        public void CalculateECC_ValidData_ReturnsCorrectECC()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };

            // Act
            byte ecc = _helper.CalculateECC(data);

            // Assert
            Assert.That(ecc, Is.Not.EqualTo(0)); // ECC should not be zero for non-zero data
        }

        [Test]
        public void VerifyECC_CorrectECC_ReturnsTrue()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            byte ecc = _helper.CalculateECC(data);

            // Act
            bool result = _helper.VerifyECC(data, ecc);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public void VerifyECC_IncorrectECC_ReturnsFalse()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            byte ecc = _helper.CalculateECC(data);
            byte incorrectEcc = (byte)(ecc ^ 0xFF); // Flip all bits to create an incorrect ECC

            // Act
            bool result = _helper.VerifyECC(data, incorrectEcc);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void CheckECCErrorsDetailed_NoError_ReturnsNoErrorStatus()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            byte ecc = _helper.CalculateECC(data);

            // Act
            MC9S12XEP100Helper.ECCErrorInfo errorInfo = _helper.CheckECCErrorsDetailed(data, ecc, out byte[] correctedData);

            // Assert
            Assert.That(errorInfo.Status, Is.EqualTo(MC9S12XEP100Helper.ECCErrorStatus.NoError));
            Assert.That(errorInfo.BytePosition, Is.EqualTo(-1));
            Assert.That(errorInfo.BitPosition, Is.EqualTo(-1));
            Assert.That(errorInfo.Syndrome, Is.EqualTo(0));
            Assert.That(correctedData, Is.EqualTo(data));
        }

        [Test]
        public void CheckECCErrorsDetailed_SingleBitError_ReturnsSingleBitErrorStatus()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            byte ecc = _helper.CalculateECC(data);

            // Introduce a single-bit error in the data
            byte[] corruptedData = new byte[data.Length];
            Array.Copy(data, corruptedData, data.Length);
            corruptedData[3] ^= 0x01; // Flip one bit in the 4th byte

            // Act
            MC9S12XEP100Helper.ECCErrorInfo errorInfo = _helper.CheckECCErrorsDetailed(corruptedData, ecc, out byte[] correctedData);

            // Assert
            Assert.That(errorInfo.Status, Is.EqualTo(MC9S12XEP100Helper.ECCErrorStatus.SingleBitError));
            Assert.That(errorInfo.BytePosition, Is.EqualTo(3));
            Assert.That(errorInfo.BitPosition, Is.GreaterThanOrEqualTo(0));
            Assert.That(errorInfo.BitPosition, Is.LessThan(8));
            Assert.That(errorInfo.Syndrome, Is.Not.EqualTo(0));
            Assert.That(correctedData, Is.EqualTo(data)); // Corrected data should match original data
        }

        [Test]
        public void CheckECCErrorsDetailed_MultiBitError_ReturnsMultiBitErrorStatus()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            byte ecc = _helper.CalculateECC(data);

            // Introduce a multi-bit error in the data
            byte[] corruptedData = new byte[data.Length];
            Array.Copy(data, corruptedData, data.Length);
            corruptedData[3] ^= 0x03; // Flip two bits in the 4th byte

            // Act
            MC9S12XEP100Helper.ECCErrorInfo errorInfo = _helper.CheckECCErrorsDetailed(corruptedData, ecc, out byte[] correctedData);

            // Assert
            Assert.That(errorInfo.Status, Is.EqualTo(MC9S12XEP100Helper.ECCErrorStatus.MultiBitError));
            Assert.That(errorInfo.Syndrome, Is.Not.EqualTo(0));
            Assert.That(correctedData, Is.EqualTo(corruptedData)); // Corrected data should be the same as corrupted data for multi-bit errors
        }

        [Test]
        public void CheckECCErrors_NoError_ReturnsNoErrorStatus()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            byte ecc = _helper.CalculateECC(data);

            // Act
            MC9S12XEP100Helper.ECCErrorStatus status = _helper.CheckECCErrors(data, ecc, out byte[] correctedData);

            // Assert
            Assert.That(status, Is.EqualTo(MC9S12XEP100Helper.ECCErrorStatus.NoError));
            Assert.That(correctedData, Is.EqualTo(data));
        }

        [Test]
        public void CheckECCErrors_SingleBitError_ReturnsSingleBitErrorStatus()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            byte ecc = _helper.CalculateECC(data);

            // Introduce a single-bit error in the data
            byte[] corruptedData = new byte[data.Length];
            Array.Copy(data, corruptedData, data.Length);
            corruptedData[3] ^= 0x01; // Flip one bit in the 4th byte

            // Act
            MC9S12XEP100Helper.ECCErrorStatus status = _helper.CheckECCErrors(corruptedData, ecc, out byte[] correctedData);

            // Assert
            Assert.That(status, Is.EqualTo(MC9S12XEP100Helper.ECCErrorStatus.SingleBitError));
            Assert.That(correctedData, Is.EqualTo(data)); // Corrected data should match original data
        }

        [Test]
        public void CheckECCErrors_MultiBitError_ReturnsMultiBitErrorStatus()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            byte ecc = _helper.CalculateECC(data);

            // Introduce a multi-bit error in the data
            byte[] corruptedData = new byte[data.Length];
            Array.Copy(data, corruptedData, data.Length);
            corruptedData[3] ^= 0x03; // Flip two bits in the 4th byte

            // Act
            MC9S12XEP100Helper.ECCErrorStatus status = _helper.CheckECCErrors(corruptedData, ecc, out byte[] correctedData);

            // Assert
            Assert.That(status, Is.EqualTo(MC9S12XEP100Helper.ECCErrorStatus.MultiBitError));
            Assert.That(correctedData, Is.EqualTo(corruptedData)); // Corrected data should be the same as corrupted data for multi-bit errors
        }

        [Test]
        public async Task ReadFlashPhraseAsync_WithECCCallback_InvokesCallback()
        {
            // Arrange
            uint address = 0x1000;
            byte[] phraseData = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            byte ecc = _helper.CalculateECC(phraseData);

            // Set up register access mock to return the phrase data and ECC
            for (int i = 0; i < phraseData.Length; i++)
            {
                _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(address + (uint)i))
                    .ReturnsAsync(phraseData[i]);
            }
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(address + (uint)MC9S12XEP100Configuration.PHRASE_SIZE))
                .ReturnsAsync(ecc);

            bool callbackInvoked = false;
            MC9S12XEP100Helper.ECCErrorInfo callbackErrorInfo = null;

            // Act
            byte[] result = await _helper.ReadFlashPhraseAsync(address, (addr, errorInfo) =>
            {
                callbackInvoked = true;
                callbackErrorInfo = errorInfo;
            });

            // Assert
            Assert.That(callbackInvoked, Is.True);
            Assert.That(callbackErrorInfo, Is.Not.Null);
            Assert.That(callbackErrorInfo.Status, Is.EqualTo(MC9S12XEP100Helper.ECCErrorStatus.NoError));
            Assert.That(result, Is.EqualTo(phraseData));
        }
    }
}

