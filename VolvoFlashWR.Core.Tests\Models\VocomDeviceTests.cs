using System;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Tests.Models
{
    [TestFixture]
    public class VocomDeviceTests
    {
        [Test]
        public void VocomDevice_DefaultValues_AreCorrect()
        {
            // Arrange & Act
            var vocomDevice = new VocomDevice();

            // Assert
            Assert.That(vocomDevice.Id, Is.Null);
            Assert.That(vocomDevice.SerialNumber, Is.Null);
            Assert.That(vocomDevice.FirmwareVersion, Is.Null);
            Assert.That(vocomDevice.ConnectionStatus, Is.EqualTo(VocomConnectionStatus.Disconnected));
            Assert.That(vocomDevice.IsInUseByOtherApplication, Is.False);
        }

        [Test]
        public void VocomDevice_CustomValues_AreCorrect()
        {
            // Arrange
            string id = "Vocom123";
            string serialNumber = "88890300";
            string firmwareVersion = "1.2.3";
            VocomConnectionStatus connectionStatus = VocomConnectionStatus.Connected;
            VocomConnectionType connectionType = VocomConnectionType.USB;
            DateTime lastConnectionTime = new DateTime(2023, 1, 1);
            bool isInUseByOtherApplication = true;
            string usingApplicationName = "PTT";
            string bluetoothAddress = "00:11:22:33:44:55";
            string wifiIPAddress = "*************";
            string usbPortInfo = "COM3";

            // Act
            var vocomDevice = new VocomDevice
            {
                Id = id,
                SerialNumber = serialNumber,
                FirmwareVersion = firmwareVersion,
                ConnectionStatus = connectionStatus,
                ConnectionType = connectionType,
                LastConnectionTime = lastConnectionTime,
                IsInUseByOtherApplication = isInUseByOtherApplication,
                UsingApplicationName = usingApplicationName,
                BluetoothAddress = bluetoothAddress,
                WiFiIPAddress = wifiIPAddress,
                USBPortInfo = usbPortInfo
            };

            // Assert
            Assert.That(vocomDevice.Id, Is.EqualTo(id));
            Assert.That(vocomDevice.SerialNumber, Is.EqualTo(serialNumber));
            Assert.That(vocomDevice.FirmwareVersion, Is.EqualTo(firmwareVersion));
            Assert.That(vocomDevice.ConnectionStatus, Is.EqualTo(connectionStatus));
            Assert.That(vocomDevice.ConnectionType, Is.EqualTo(connectionType));
            Assert.That(vocomDevice.LastConnectionTime, Is.EqualTo(lastConnectionTime));
            Assert.That(vocomDevice.IsInUseByOtherApplication, Is.EqualTo(isInUseByOtherApplication));
            Assert.That(vocomDevice.UsingApplicationName, Is.EqualTo(usingApplicationName));
            Assert.That(vocomDevice.BluetoothAddress, Is.EqualTo(bluetoothAddress));
            Assert.That(vocomDevice.WiFiIPAddress, Is.EqualTo(wifiIPAddress));
            Assert.That(vocomDevice.USBPortInfo, Is.EqualTo(usbPortInfo));
        }
    }
}

