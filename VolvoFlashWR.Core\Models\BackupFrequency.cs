namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Defines the frequency for scheduled backups
    /// </summary>
    public enum BackupFrequency
    {
        /// <summary>
        /// Backup once per hour
        /// </summary>
        Hourly,

        /// <summary>
        /// Backup once per day
        /// </summary>
        Daily,

        /// <summary>
        /// Backup once per week
        /// </summary>
        Weekly,

        /// <summary>
        /// Backup once per month
        /// </summary>
        Monthly,

        /// <summary>
        /// Custom backup schedule
        /// </summary>
        Custom
    }
}
