using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Protocols;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Protocols
{
    [TestFixture]
    public class ProtocolHandlerTests
    {
        private Mock<ILoggingService> _mockLogger;
        private Mock<IVocomDeviceDriver> _mockVocomDriver;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _mockVocomDriver = new Mock<IVocomDeviceDriver>();
        }

        [Test]
        public async Task SPIProtocolHandler_ReadData_ReturnsCorrectData()
        {
            // Arrange
            var mockVocomService = new Mock<IVocomService>();
            mockVocomService.Setup(s => s.CurrentDevice).Returns(new VocomDevice());
            var handler = new SPIProtocolHandler(_mockLogger.Object, mockVocomService.Object);
            var device = new VocomDevice
            {
                Id = "vocom1",
                Name = "Vocom 1",
                SerialNumber = "SN12345",
                FirmwareVersion = "1.0",
                ConnectionType = VocomConnectionType.USB
            };

            var ecu = new ECUDevice
            {
                Id = "ecu1",
                Name = "Test ECU",
                SerialNumber = "SN12345",
                HardwareVersion = "HW1.0",
                SoftwareVersion = "SW1.0",
                ProtocolType = ECUProtocolType.SPI,
                CommunicationProtocol = CommunicationProtocol.SPI
            };

            byte[] expectedData = new byte[] { 1, 2, 3, 4 };
            _mockVocomDriver.Setup(d => d.SendSPICommandAsync(
                It.IsAny<VocomDevice>(),
                It.IsAny<byte>(),
                It.IsAny<byte[]>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).ReturnsAsync(expectedData);

            // Initialize the handler
            await handler.InitializeAsync();

            // Act
            byte[] result = await handler.ReadDataAsync(device, ecu, 0x01, 4);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(expectedData));
            _mockVocomDriver.Verify(d => d.SendSPICommandAsync(
                device,
                It.IsAny<byte>(),
                It.IsAny<byte[]>(),
                4,
                It.IsAny<int>()
            ), Times.Once);
        }

        [Test]
        public async Task SPIProtocolHandler_WriteData_WritesCorrectData()
        {
            // Arrange
            var mockVocomService = new Mock<IVocomService>();
            mockVocomService.Setup(s => s.CurrentDevice).Returns(new VocomDevice());
            var handler = new SPIProtocolHandler(_mockLogger.Object, mockVocomService.Object);
            var device = new VocomDevice
            {
                Id = "vocom1",
                Name = "Vocom 1",
                SerialNumber = "SN12345",
                FirmwareVersion = "1.0",
                ConnectionType = VocomConnectionType.USB
            };

            var ecu = new ECUDevice
            {
                Id = "ecu1",
                Name = "Test ECU",
                SerialNumber = "SN12345",
                HardwareVersion = "HW1.0",
                SoftwareVersion = "SW1.0",
                ProtocolType = ECUProtocolType.SPI,
                CommunicationProtocol = CommunicationProtocol.SPI
            };

            byte[] dataToWrite = new byte[] { 1, 2, 3, 4 };
            _mockVocomDriver.Setup(d => d.SendSPICommandAsync(
                It.IsAny<VocomDevice>(),
                It.IsAny<byte>(),
                It.IsAny<byte[]>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).ReturnsAsync(new byte[] { 0 }); // Success response

            // Initialize the handler
            await handler.InitializeAsync();

            // Act
            bool result = await handler.WriteDataAsync(device, ecu, 0x02, dataToWrite);

            // Assert
            Assert.That(result, Is.True);
            _mockVocomDriver.Verify(d => d.SendSPICommandAsync(
                device,
                It.IsAny<byte>(),
                It.Is<byte[]>(b => b.Length == dataToWrite.Length + 1), // Command byte + data
                0,
                It.IsAny<int>()
            ), Times.Once);
        }

        [Test]
        public async Task CANProtocolHandler_ReadData_ReturnsCorrectData()
        {
            // Arrange
            var mockVocomService = new Mock<IVocomService>();
            mockVocomService.Setup(s => s.CurrentDevice).Returns(new VocomDevice());
            var handler = new CANProtocolHandler(_mockLogger.Object, mockVocomService.Object);
            var device = new VocomDevice
            {
                Id = "vocom1",
                Name = "Vocom 1",
                SerialNumber = "SN12345",
                FirmwareVersion = "1.0",
                ConnectionType = VocomConnectionType.USB
            };

            var ecu = new ECUDevice
            {
                Id = "ecu1",
                Name = "Test ECU",
                SerialNumber = "SN12345",
                HardwareVersion = "HW1.0",
                SoftwareVersion = "SW1.0",
                ProtocolType = ECUProtocolType.CAN,
                CommunicationProtocol = CommunicationProtocol.CAN,
                CANId = "0x7E0"
            };

            byte[] expectedData = new byte[] { 1, 2, 3, 4 };
            _mockVocomDriver.Setup(d => d.SendCANFrameAsync(
                It.IsAny<VocomDevice>(),
                It.IsAny<byte[]>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).ReturnsAsync(expectedData);

            // Initialize the handler
            await handler.InitializeAsync();

            // Act
            byte[] result = await handler.ReadDataAsync(device, ecu, 0x01, 4);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(expectedData));
            _mockVocomDriver.Verify(d => d.SendCANFrameAsync(
                device,
                It.IsAny<byte[]>(),
                4,
                It.IsAny<int>()
            ), Times.Once);
        }

        [Test]
        public async Task CANProtocolHandler_WriteData_WritesCorrectData()
        {
            // Arrange
            var mockVocomService = new Mock<IVocomService>();
            mockVocomService.Setup(s => s.CurrentDevice).Returns(new VocomDevice());
            var handler = new CANProtocolHandler(_mockLogger.Object, mockVocomService.Object);
            var device = new VocomDevice
            {
                Id = "vocom1",
                Name = "Vocom 1",
                SerialNumber = "SN12345",
                FirmwareVersion = "1.0",
                ConnectionType = VocomConnectionType.USB
            };

            var ecu = new ECUDevice
            {
                Id = "ecu1",
                Name = "Test ECU",
                SerialNumber = "SN12345",
                HardwareVersion = "HW1.0",
                SoftwareVersion = "SW1.0",
                ProtocolType = ECUProtocolType.CAN,
                CommunicationProtocol = CommunicationProtocol.CAN,
                CANId = "0x7E0"
            };

            byte[] dataToWrite = new byte[] { 1, 2, 3, 4 };
            _mockVocomDriver.Setup(d => d.SendCANFrameAsync(
                It.IsAny<VocomDevice>(),
                It.IsAny<byte[]>(),
                It.IsAny<int>(),
                It.IsAny<int>()
            )).ReturnsAsync(new byte[] { 0 }); // Success response

            // Initialize the handler
            await handler.InitializeAsync();

            // Act
            bool result = await handler.WriteDataAsync(device, ecu, 0x02, dataToWrite);

            // Assert
            Assert.That(result, Is.True);
            _mockVocomDriver.Verify(d => d.SendCANFrameAsync(
                device,
                It.Is<byte[]>(b => b.Length == dataToWrite.Length + 1), // Command byte + data
                0,
                It.IsAny<int>()
            ), Times.Once);
        }

        [Test]
        public async Task CANProtocolHandler_SetCommunicationSpeedModeAsync_HighSpeed_ReturnsTrue()
        {
            // Arrange
            var mockVocomService = new Mock<IVocomService>();
            mockVocomService.Setup(s => s.CurrentDevice).Returns(new VocomDevice());
            var handler = new CANProtocolHandler(_mockLogger.Object, mockVocomService.Object);

            var ecu = new ECUDevice
            {
                Id = "ecu1",
                Name = "Test ECU",
                SerialNumber = "SN12345",
                HardwareVersion = "HW1.0",
                SoftwareVersion = "SW1.0",
                ProtocolType = ECUProtocolType.CAN,
                CommunicationProtocol = CommunicationProtocol.CAN,
                CANId = "0x7E0",
                SupportsHighSpeedCommunication = true,
                SupportsLowSpeedCommunication = true,
                CurrentCommunicationSpeedMode = CommunicationSpeedMode.Low
            };

            // Initialize the handler
            await handler.InitializeAsync();

            // Act
            bool result = await handler.SetCommunicationSpeedModeAsync(ecu, CommunicationSpeedMode.High);

            // Assert
            Assert.That(result, Is.True);
            Assert.That(ecu.CurrentCommunicationSpeedMode, Is.EqualTo(CommunicationSpeedMode.High));
        }

        [Test]
        public async Task CANProtocolHandler_SetCommunicationSpeedModeAsync_UnsupportedHighSpeed_ReturnsFalse()
        {
            // Arrange
            var mockVocomService = new Mock<IVocomService>();
            mockVocomService.Setup(s => s.CurrentDevice).Returns(new VocomDevice());
            var handler = new CANProtocolHandler(_mockLogger.Object, mockVocomService.Object);

            var ecu = new ECUDevice
            {
                Id = "ecu1",
                Name = "Test ECU",
                SerialNumber = "SN12345",
                HardwareVersion = "HW1.0",
                SoftwareVersion = "SW1.0",
                ProtocolType = ECUProtocolType.CAN,
                CommunicationProtocol = CommunicationProtocol.CAN,
                CANId = "0x7E0",
                SupportsHighSpeedCommunication = false,
                SupportsLowSpeedCommunication = true,
                CurrentCommunicationSpeedMode = CommunicationSpeedMode.Low
            };

            // Initialize the handler
            await handler.InitializeAsync();

            // Act
            bool result = await handler.SetCommunicationSpeedModeAsync(ecu, CommunicationSpeedMode.High);

            // Assert
            Assert.That(result, Is.False);
            Assert.That(ecu.CurrentCommunicationSpeedMode, Is.EqualTo(CommunicationSpeedMode.Low));
        }

        [Test]
        public async Task ProtocolHandlerFactory_GetHandler_ReturnsCorrectHandler()
        {
            // Arrange
            var factory = new ProtocolHandlerFactory(_mockLogger.Object);

            // Create a mock Vocom service
            var mockVocomService = new Mock<IVocomService>();
            mockVocomService.Setup(s => s.CurrentDevice).Returns(new VocomDevice());

            // Initialize the factory with the mocked service
            await factory.InitializeAsync(mockVocomService.Object);

            // Act - Get SPI handler
            var spiEcu = new ECUDevice { ProtocolType = ECUProtocolType.SPI, CommunicationProtocol = CommunicationProtocol.SPI };
            var spiHandler = await factory.GetProtocolHandlerAsync(spiEcu);

            // Act - Get CAN handler
            var canEcu = new ECUDevice { ProtocolType = ECUProtocolType.CAN, CommunicationProtocol = CommunicationProtocol.CAN };
            var canHandler = await factory.GetProtocolHandlerAsync(canEcu);

            // Act - Get J1939 handler
            var j1939Ecu = new ECUDevice { ProtocolType = ECUProtocolType.J1939, CommunicationProtocol = CommunicationProtocol.J1939 };
            var j1939Handler = await factory.GetProtocolHandlerAsync(j1939Ecu);

            // Assert
            Assert.That(spiHandler, Is.InstanceOf<SPIProtocolHandler>());
            Assert.That(canHandler, Is.InstanceOf<CANProtocolHandler>());
            Assert.That(j1939Handler, Is.InstanceOf<J1939ProtocolHandler>());
        }
    }
}

