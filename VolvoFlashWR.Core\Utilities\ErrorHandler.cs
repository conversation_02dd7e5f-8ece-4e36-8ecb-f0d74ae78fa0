using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Utilities
{
    /// <summary>
    /// Helper class for error handling
    /// </summary>
    public static class ErrorHandler
    {
        private static readonly Dictionary<string, int> _errorCounts = new Dictionary<string, int>();
        private static readonly Dictionary<string, DateTime> _lastErrorTimes = new Dictionary<string, DateTime>();
        private static readonly Dictionary<string, List<Exception>> _recentErrors = new Dictionary<string, List<Exception>>();
        private static readonly Dictionary<string, int> _consecutiveErrorCounts = new Dictionary<string, int>();
        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        /// <summary>
        /// <PERSON>les an exception by logging it and optionally executing a fallback action
        /// </summary>
        /// <typeparam name="T">The return type of the fallback action</typeparam>
        /// <param name="exception">The exception to handle</param>
        /// <param name="source">The source of the exception</param>
        /// <param name="message">A message describing the error</param>
        /// <param name="logger">The logging service to use</param>
        /// <param name="fallbackAction">An optional fallback action to execute</param>
        /// <param name="rethrowException">Whether to rethrow the exception after handling</param>
        /// <returns>The result of the fallback action, or default(T) if no fallback action is provided</returns>
        public static T HandleException<T>(
            Exception exception,
            string source,
            string message,
            ILoggingService logger,
            Func<T> fallbackAction = null,
            bool rethrowException = false)
        {
            if (exception == null)
            {
                return fallbackAction != null ? fallbackAction() : default;
            }

            // Get detailed exception message using the extension method
            string detailedMessage = exception.GetDetailedMessage(includeStackTrace: true);

            // Log the error with the detailed message
            logger?.LogError($"{message}: {detailedMessage}", source, exception);

            // Track error frequency
            TrackError(source, exception);

            // Execute fallback action if provided
            if (fallbackAction != null)
            {
                try
                {
                    return fallbackAction();
                }
                catch (Exception fallbackEx)
                {
                    logger?.LogError($"Fallback action failed: {fallbackEx.Message}", source, fallbackEx);
                }
            }

            // Rethrow the exception if requested
            if (rethrowException)
            {
                throw exception;
            }

            return default;
        }

        /// <summary>
        /// Handles an exception asynchronously by logging it and optionally executing a fallback action
        /// </summary>
        /// <typeparam name="T">The return type of the fallback action</typeparam>
        /// <param name="exception">The exception to handle</param>
        /// <param name="source">The source of the exception</param>
        /// <param name="message">A message describing the error</param>
        /// <param name="logger">The logging service to use</param>
        /// <param name="fallbackAction">An optional fallback action to execute</param>
        /// <param name="rethrowException">Whether to rethrow the exception after handling</param>
        /// <returns>The result of the fallback action, or default(T) if no fallback action is provided</returns>
        public static async Task<T> HandleExceptionAsync<T>(
            Exception exception,
            string source,
            string message,
            ILoggingService logger,
            Func<Task<T>> fallbackAction = null,
            bool rethrowException = false)
        {
            if (exception == null)
            {
                return fallbackAction != null ? await fallbackAction() : default;
            }

            // Get detailed exception message using the extension method
            string detailedMessage = exception.GetDetailedMessage(includeStackTrace: true);

            // Log the error with the detailed message
            logger?.LogError($"{message}: {detailedMessage}", source, exception);

            // Track error frequency
            TrackError(source, exception);

            // Execute fallback action if provided
            if (fallbackAction != null)
            {
                try
                {
                    return await fallbackAction();
                }
                catch (Exception fallbackEx)
                {
                    logger?.LogError($"Fallback action failed: {fallbackEx.Message}", source, fallbackEx);
                }
            }

            // Rethrow the exception if requested
            if (rethrowException)
            {
                throw exception;
            }

            return default;
        }

        /// <summary>
        /// Tracks the frequency of errors from a specific source
        /// </summary>
        /// <param name="source">The source of the error</param>
        /// <param name="exception">The exception that occurred (optional)</param>
        private static async void TrackError(string source, Exception exception = null)
        {
            await _semaphore.WaitAsync();
            try
            {
                if (!_errorCounts.ContainsKey(source))
                {
                    _errorCounts[source] = 0;
                    _lastErrorTimes[source] = DateTime.MinValue;
                    _consecutiveErrorCounts[source] = 0;
                    _recentErrors[source] = new List<Exception>();
                }

                _errorCounts[source]++;
                _consecutiveErrorCounts[source]++;
                _lastErrorTimes[source] = DateTime.Now;

                // Store the exception in recent errors (if provided)
                if (exception != null)
                {
                    // Keep only the last 10 exceptions
                    if (_recentErrors[source].Count >= 10)
                    {
                        _recentErrors[source].RemoveAt(0);
                    }
                    _recentErrors[source].Add(exception);
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// Gets the number of errors from a specific source
        /// </summary>
        /// <param name="source">The source to check</param>
        /// <returns>The number of errors from the source</returns>
        public static int GetErrorCount(string source)
        {
            return _errorCounts.ContainsKey(source) ? _errorCounts[source] : 0;
        }

        /// <summary>
        /// Gets the time of the last error from a specific source
        /// </summary>
        /// <param name="source">The source to check</param>
        /// <returns>The time of the last error from the source</returns>
        public static DateTime GetLastErrorTime(string source)
        {
            return _lastErrorTimes.ContainsKey(source) ? _lastErrorTimes[source] : DateTime.MinValue;
        }

        /// <summary>
        /// Resets the error count for a specific source
        /// </summary>
        /// <param name="source">The source to reset</param>
        public static void ResetErrorCount(string source)
        {
            if (_errorCounts.ContainsKey(source))
            {
                _errorCounts[source] = 0;
            }
        }

        /// <summary>
        /// Checks if a source has exceeded a specified error threshold within a time window
        /// </summary>
        /// <param name="source">The source to check</param>
        /// <param name="threshold">The error threshold</param>
        /// <param name="timeWindowMinutes">The time window in minutes</param>
        /// <returns>True if the threshold has been exceeded, false otherwise</returns>
        public static bool HasExceededErrorThreshold(string source, int threshold, int timeWindowMinutes)
        {
            if (!_errorCounts.ContainsKey(source) || !_lastErrorTimes.ContainsKey(source))
                return false;

            if (_errorCounts[source] >= threshold)
            {
                // Check if the errors occurred within the time window
                TimeSpan timeSinceFirstError = DateTime.Now - _lastErrorTimes[source];
                return timeSinceFirstError.TotalMinutes <= timeWindowMinutes;
            }

            return false;
        }

        /// <summary>
        /// Gets the number of consecutive errors from a specific source
        /// </summary>
        /// <param name="source">The source to check</param>
        /// <returns>The number of consecutive errors from the source</returns>
        public static int GetConsecutiveErrorCount(string source)
        {
            return _consecutiveErrorCounts.ContainsKey(source) ? _consecutiveErrorCounts[source] : 0;
        }

        /// <summary>
        /// Resets the consecutive error count for a specific source
        /// </summary>
        /// <param name="source">The source to reset</param>
        public static void ResetConsecutiveErrorCount(string source)
        {
            if (_consecutiveErrorCounts.ContainsKey(source))
            {
                _consecutiveErrorCounts[source] = 0;
            }
        }

        /// <summary>
        /// Gets the recent exceptions from a specific source
        /// </summary>
        /// <param name="source">The source to check</param>
        /// <param name="maxCount">The maximum number of exceptions to return</param>
        /// <returns>A list of recent exceptions from the source</returns>
        public static List<Exception> GetRecentExceptions(string source, int maxCount = 10)
        {
            if (!_recentErrors.ContainsKey(source))
            {
                return new List<Exception>();
            }

            return _recentErrors[source].TakeLast(maxCount).ToList();
        }

        /// <summary>
        /// Executes an action with retry logic
        /// </summary>
        /// <typeparam name="T">The return type of the action</typeparam>
        /// <param name="action">The action to execute</param>
        /// <param name="retryCount">The number of retry attempts</param>
        /// <param name="retryDelayMs">The delay between retries in milliseconds</param>
        /// <param name="source">The source of the action</param>
        /// <param name="logger">The logging service to use</param>
        /// <returns>The result of the action</returns>
        public static async Task<T> ExecuteWithRetryAsync<T>(
            Func<Task<T>> action,
            int retryCount,
            int retryDelayMs,
            string source,
            ILoggingService logger)
        {
            int currentRetry = 0;

            while (true)
            {
                try
                {
                    return await action();
                }
                catch (Exception ex)
                {
                    currentRetry++;

                    if (currentRetry > retryCount || !ex.IsTransient())
                    {
                        // Log and rethrow if we've exhausted our retry count or if it's not a transient exception
                        logger?.LogError($"Operation failed after {currentRetry} attempts", source, ex);
                        throw;
                    }

                    // Log the retry attempt
                    logger?.LogWarning($"Retry attempt {currentRetry}/{retryCount} after error: {ex.Message}", source);

                    // Wait before retrying with exponential backoff
                    int delay = retryDelayMs * (int)Math.Pow(2, currentRetry - 1);
                    await Task.Delay(delay);
                }
            }
        }

        /// <summary>
        /// Executes an action with retry logic
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="retryCount">The number of retry attempts</param>
        /// <param name="retryDelayMs">The delay between retries in milliseconds</param>
        /// <param name="source">The source of the action</param>
        /// <param name="logger">The logging service to use</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteWithRetryAsync(
            Func<Task> action,
            int retryCount,
            int retryDelayMs,
            string source,
            ILoggingService logger)
        {
            int currentRetry = 0;

            while (true)
            {
                try
                {
                    await action();
                    return;
                }
                catch (Exception ex)
                {
                    currentRetry++;

                    if (currentRetry > retryCount || !ex.IsTransient())
                    {
                        // Log and rethrow if we've exhausted our retry count or if it's not a transient exception
                        logger?.LogError($"Operation failed after {currentRetry} attempts", source, ex);
                        throw;
                    }

                    // Log the retry attempt
                    logger?.LogWarning($"Retry attempt {currentRetry}/{retryCount} after error: {ex.Message}", source);

                    // Wait before retrying with exponential backoff
                    int delay = retryDelayMs * (int)Math.Pow(2, currentRetry - 1);
                    await Task.Delay(delay);
                }
            }
        }

        /// <summary>
        /// Executes an action with error handling
        /// </summary>
        /// <typeparam name="T">The return type of the action</typeparam>
        /// <param name="action">The action to execute</param>
        /// <param name="defaultValue">The default value to return if an exception occurs</param>
        /// <param name="source">The source of the action</param>
        /// <param name="logger">The logging service to use</param>
        /// <param name="errorMessage">An optional error message</param>
        /// <returns>The result of the action, or the default value if an exception occurred</returns>
        public static T ExecuteWithErrorHandling<T>(
            Func<T> action,
            T defaultValue,
            string source,
            ILoggingService logger,
            string errorMessage = null)
        {
            try
            {
                return action();
            }
            catch (Exception ex)
            {
                string message = errorMessage ?? "Error executing action";
                HandleException<T>(ex, source, message, logger);
                return defaultValue;
            }
        }

        /// <summary>
        /// Executes an action with error handling
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="source">The source of the action</param>
        /// <param name="logger">The logging service to use</param>
        /// <param name="errorMessage">An optional error message</param>
        /// <returns>True if the action completed successfully, false otherwise</returns>
        public static bool ExecuteWithErrorHandling(
            Action action,
            string source,
            ILoggingService logger,
            string errorMessage = null)
        {
            try
            {
                action();
                return true;
            }
            catch (Exception ex)
            {
                string message = errorMessage ?? "Error executing action";
                HandleException<object>(ex, source, message, logger);
                return false;
            }
        }

        /// <summary>
        /// Executes an action with error handling asynchronously
        /// </summary>
        /// <typeparam name="T">The return type of the action</typeparam>
        /// <param name="action">The action to execute</param>
        /// <param name="defaultValue">The default value to return if an exception occurs</param>
        /// <param name="source">The source of the action</param>
        /// <param name="logger">The logging service to use</param>
        /// <param name="errorMessage">An optional error message</param>
        /// <returns>The result of the action, or the default value if an exception occurred</returns>
        public static async Task<T> ExecuteWithErrorHandlingAsync<T>(
            Func<Task<T>> action,
            T defaultValue,
            string source,
            ILoggingService logger,
            string errorMessage = null)
        {
            try
            {
                return await action();
            }
            catch (Exception ex)
            {
                string message = errorMessage ?? "Error executing action";
                await HandleExceptionAsync<T>(ex, source, message, logger);
                return defaultValue;
            }
        }

        /// <summary>
        /// Executes an action with error handling asynchronously
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="source">The source of the action</param>
        /// <param name="logger">The logging service to use</param>
        /// <param name="errorMessage">An optional error message</param>
        /// <returns>True if the action completed successfully, false otherwise</returns>
        public static async Task<bool> ExecuteWithErrorHandlingAsync(
            Func<Task> action,
            string source,
            ILoggingService logger,
            string errorMessage = null)
        {
            try
            {
                await action();
                return true;
            }
            catch (Exception ex)
            {
                string message = errorMessage ?? "Error executing action";
                await HandleExceptionAsync<object>(ex, source, message, logger);
                return false;
            }
        }

        /// <summary>
        /// Gets a user-friendly error message for an exception
        /// </summary>
        /// <param name="exception">The exception</param>
        /// <returns>A user-friendly error message</returns>
        public static string GetUserFriendlyErrorMessage(Exception exception)
        {
            if (exception == null)
            {
                return "An unknown error occurred.";
            }

            return exception.GetUserFriendlyMessage();
        }

        /// <summary>
        /// Determines if an exception is critical and should terminate the application
        /// </summary>
        /// <param name="exception">The exception to check</param>
        /// <returns>True if the exception is critical, false otherwise</returns>
        public static bool IsCriticalException(Exception exception)
        {
            return exception != null && exception.IsCriticalException();
        }
    }
}
