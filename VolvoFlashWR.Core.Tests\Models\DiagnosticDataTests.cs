using System;
using System.Collections.Generic;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Tests.Models
{
    [TestFixture]
    public class DiagnosticDataTests
    {
        [Test]
        public void DiagnosticData_DefaultValues_AreCorrect()
        {
            // Arrange & Act
            var diagnosticData = new DiagnosticData();
            var now = DateTime.Now;

            // Assert
            Assert.That(diagnosticData.SessionId, Is.Not.Null.Or.Empty);
            Assert.That(diagnosticData.Timestamp.Date, Is.EqualTo(now.Date));
            Assert.That(diagnosticData.ActiveFaults, Is.Not.Null);
            Assert.That(diagnosticData.InactiveFaults, Is.Not.Null);
            Assert.That(diagnosticData.Parameters, Is.Not.Null);
        }

        [Test]
        public void DiagnosticData_CustomValues_AreCorrect()
        {
            // Arrange
            string sessionId = "Session123";
            DateTime timestamp = new DateTime(2023, 1, 1);
            string ecuId = "ECU123";
            string ecuName = "TestECU";
            var activeFaults = new List<ECUFault> { new ECUFault { Code = "P0123", Description = "Test Fault" } };
            var inactiveFaults = new List<ECUFault> { new ECUFault { Code = "P0456", Description = "Inactive Fault" } };
            var parameters = new Dictionary<string, object> { { "TestParam", "Value" } };
            OperatingMode operatingMode = OperatingMode.Bench;
            VocomConnectionType connectionType = VocomConnectionType.USB;

            // Act
            var diagnosticData = new DiagnosticData
            {
                SessionId = sessionId,
                Timestamp = timestamp,
                ECUId = ecuId,
                ECUName = ecuName,
                ActiveFaults = activeFaults,
                InactiveFaults = inactiveFaults,
                Parameters = parameters,
                OperatingMode = operatingMode,
                ConnectionType = connectionType
            };

            // Assert
            Assert.That(diagnosticData.SessionId, Is.EqualTo(sessionId));
            Assert.That(diagnosticData.Timestamp, Is.EqualTo(timestamp));
            Assert.That(diagnosticData.ECUId, Is.EqualTo(ecuId));
            Assert.That(diagnosticData.ECUName, Is.EqualTo(ecuName));
            Assert.That(diagnosticData.ActiveFaults, Is.EqualTo(activeFaults));
            Assert.That(diagnosticData.InactiveFaults, Is.EqualTo(inactiveFaults));
            Assert.That(diagnosticData.Parameters, Is.EqualTo(parameters));
            Assert.That(diagnosticData.OperatingMode, Is.EqualTo(operatingMode));
            Assert.That(diagnosticData.ConnectionType, Is.EqualTo(connectionType));
        }
    }
}

