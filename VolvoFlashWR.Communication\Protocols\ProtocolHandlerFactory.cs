using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Protocols
{
    /// <summary>
    /// Factory for creating protocol handlers
    /// </summary>
    public class ProtocolHandlerFactory
    {
        private readonly ILoggingService _logger;
        private IVocomService _vocomService;
        private Dictionary<ECUProtocolType, IECUProtocolHandler> _protocolHandlers;
        private Dictionary<string, JsonElement> _protocolConfigurations = new Dictionary<string, JsonElement>();
        private bool _isInitialized;

        /// <summary>
        /// Initializes a new instance of the ProtocolHandlerFactory class
        /// </summary>
        /// <param name="logger">The logging service</param>
        public ProtocolHandlerFactory(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _protocolHandlers = new Dictionary<ECUProtocolType, IECUProtocolHandler>();
        }

        /// <summary>
        /// Initializes the factory with the Vocom service
        /// </summary>
        /// <param name="vocomService">The Vocom service</param>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public async Task<bool> InitializeAsync(IVocomService vocomService)
        {
            try
            {
                _logger.LogInformation("Initializing protocol handler factory", "ProtocolHandlerFactory");

                if (vocomService == null)
                {
                    _logger.LogError("Vocom service is null", "ProtocolHandlerFactory");
                    return false;
                }

                _vocomService = vocomService;

                // Load MC9S12XEP100 configuration
                bool configLoaded = await LoadMC9S12XEP100ConfigurationAsync();
                if (!configLoaded)
                {
                    _logger.LogWarning("Failed to load MC9S12XEP100 configuration, using default values", "ProtocolHandlerFactory");
                    // Continue with default values
                }

                // Load Vocom configuration
                bool vocomConfigLoaded = await LoadVocomConfigurationAsync();
                if (!vocomConfigLoaded)
                {
                    _logger.LogWarning("Failed to load Vocom configuration, using default values", "ProtocolHandlerFactory");
                    // Continue with default values
                }

                // Create protocol handlers
                _protocolHandlers.Clear();
                _protocolHandlers.Add(ECUProtocolType.CAN, new CANProtocolHandler(_logger, _vocomService));
                _protocolHandlers.Add(ECUProtocolType.SPI, new SPIProtocolHandler(_logger, _vocomService));
                _protocolHandlers.Add(ECUProtocolType.SCI, new SCIProtocolHandler(_logger, _vocomService));
                _protocolHandlers.Add(ECUProtocolType.IIC, new IICProtocolHandler(_logger, _vocomService));
                _protocolHandlers.Add(ECUProtocolType.J1939, new J1939ProtocolHandler(_logger, _vocomService));

                // Initialize all protocol handlers
                foreach (var handler in _protocolHandlers.Values)
                {
                    await handler.InitializeAsync();
                }

                _isInitialized = true;
                _logger.LogInformation("Protocol handler factory initialized successfully", "ProtocolHandlerFactory");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error initializing protocol handler factory", "ProtocolHandlerFactory", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets a protocol handler for the specified ECU
        /// </summary>
        /// <param name="ecu">The ECU to get a handler for</param>
        /// <returns>The protocol handler</returns>
        public async Task<IECUProtocolHandler> GetProtocolHandlerAsync(ECUDevice ecu)
        {
            try
            {
                if (!_isInitialized)
                {
                    _logger.LogError("Protocol handler factory not initialized", "ProtocolHandlerFactory");
                    throw new InvalidOperationException("Protocol handler factory not initialized");
                }

                if (ecu == null)
                {
                    _logger.LogError("ECU is null", "ProtocolHandlerFactory");
                    throw new ArgumentNullException(nameof(ecu));
                }

                if (!_protocolHandlers.TryGetValue(ecu.ProtocolType, out IECUProtocolHandler handler))
                {
                    _logger.LogError($"Protocol handler for {ecu.ProtocolType} not found", "ProtocolHandlerFactory");
                    throw new InvalidOperationException($"Protocol handler for {ecu.ProtocolType} not found");
                }

                return handler;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error getting protocol handler", "ProtocolHandlerFactory", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets the configuration for a protocol
        /// </summary>
        /// <param name="protocolType">The protocol type</param>
        /// <returns>The protocol configuration</returns>
        public Dictionary<string, object> GetProtocolConfiguration(ECUProtocolType protocolType)
        {
            Dictionary<string, object> config = new Dictionary<string, object>();

            string protocolName = protocolType.ToString();
            if (_protocolConfigurations.TryGetValue(protocolName, out JsonElement protocolConfig))
            {
                if (protocolConfig.TryGetProperty("enabled", out JsonElement enabledElement) && enabledElement.GetBoolean())
                {
                    // Add protocol-specific configuration
                    if (protocolConfig.TryGetProperty("registers", out JsonElement registersElement))
                    {
                        foreach (var property in registersElement.EnumerateObject())
                        {
                            config.Add($"Register_{property.Name}", property.Value.GetString());
                        }
                    }

                    if (protocolType == ECUProtocolType.CAN || protocolType == ECUProtocolType.SCI)
                    {
                        if (protocolConfig.TryGetProperty("baud_rates", out JsonElement baudRatesElement))
                        {
                            if (baudRatesElement.TryGetProperty("high_speed", out JsonElement highSpeedElement))
                            {
                                config.Add("HighSpeedBaudRate", highSpeedElement.GetInt32());
                            }

                            if (baudRatesElement.TryGetProperty("low_speed", out JsonElement lowSpeedElement))
                            {
                                config.Add("LowSpeedBaudRate", lowSpeedElement.GetInt32());
                            }
                        }
                    }
                    else if (protocolType == ECUProtocolType.SPI || protocolType == ECUProtocolType.IIC)
                    {
                        if (protocolConfig.TryGetProperty("clock_rates", out JsonElement clockRatesElement))
                        {
                            foreach (var property in clockRatesElement.EnumerateObject())
                            {
                                config.Add($"ClockRate_{property.Name}", property.Value.GetInt32());
                            }
                        }
                    }
                }
            }

            return config;
        }

        /// <summary>
        /// Loads the MC9S12XEP100 configuration from the configuration file
        /// </summary>
        /// <returns>True if the configuration was loaded successfully, false otherwise</returns>
        private async Task<bool> LoadMC9S12XEP100ConfigurationAsync()
        {
            try
            {
                _logger.LogInformation("Loading MC9S12XEP100 configuration", "ProtocolHandlerFactory");

                // Check if the configuration file exists
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Drivers", "MC9S12XEP100", "config.json");
                if (!File.Exists(configPath))
                {
                    _logger.LogWarning($"MC9S12XEP100 configuration file not found at {configPath}", "ProtocolHandlerFactory");
                    return false;
                }

                // Read the configuration file
                string configJson = await File.ReadAllTextAsync(configPath);

                // Parse the configuration
                using (JsonDocument document = JsonDocument.Parse(configJson))
                {
                    JsonElement root = document.RootElement;

                    // Load the protocol configurations
                    if (root.TryGetProperty("protocols", out JsonElement protocolsElement))
                    {
                        foreach (var property in protocolsElement.EnumerateObject())
                        {
                            _protocolConfigurations[property.Name] = property.Value.Clone();
                        }
                    }
                }

                _logger.LogInformation("MC9S12XEP100 configuration loaded successfully", "ProtocolHandlerFactory");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to load MC9S12XEP100 configuration", "ProtocolHandlerFactory", ex);
                return false;
            }
        }

        /// <summary>
        /// Loads the Vocom configuration from the configuration file
        /// </summary>
        /// <returns>True if the configuration was loaded successfully, false otherwise</returns>
        private async Task<bool> LoadVocomConfigurationAsync()
        {
            try
            {
                _logger.LogInformation("Loading Vocom configuration", "ProtocolHandlerFactory");

                // Check if the configuration file exists
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Drivers", "Vocom", "config.json");
                if (!File.Exists(configPath))
                {
                    _logger.LogWarning($"Vocom configuration file not found at {configPath}", "ProtocolHandlerFactory");
                    return false;
                }

                // Read the configuration file
                string configJson = await File.ReadAllTextAsync(configPath);

                // Parse the configuration
                using (JsonDocument document = JsonDocument.Parse(configJson))
                {
                    JsonElement root = document.RootElement;

                    // Load the protocol configurations
                    if (root.TryGetProperty("protocols", out JsonElement protocolsElement))
                    {
                        foreach (var property in protocolsElement.EnumerateObject())
                        {
                            string protocolName = property.Name;
                            if (_protocolConfigurations.ContainsKey(protocolName))
                            {
                                // Merge with existing configuration
                                // For simplicity, we'll just add a "vocom_" prefix to avoid conflicts
                                foreach (var subProperty in property.Value.EnumerateObject())
                                {
                                    _protocolConfigurations[$"vocom_{protocolName}_{subProperty.Name}"] = subProperty.Value.Clone();
                                }
                            }
                            else
                            {
                                // Add new configuration
                                _protocolConfigurations[$"vocom_{protocolName}"] = property.Value.Clone();
                            }
                        }
                    }
                }

                _logger.LogInformation("Vocom configuration loaded successfully", "ProtocolHandlerFactory");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to load Vocom configuration", "ProtocolHandlerFactory", ex);
                return false;
            }
        }
    }
}
