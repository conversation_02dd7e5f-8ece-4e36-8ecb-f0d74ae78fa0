using System;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Adapters;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Services;
using VolvoFlashWR.Communication.Protocols;

namespace VolvoFlashWR.Communication.Microcontroller
{
    /// <summary>
    /// Integration class for MC9S12XEP100 microcontroller
    /// </summary>
    public class MC9S12XEP100Integration
    {
        private readonly ILoggingService _loggingService;
        private readonly ILoggingService _logger;
        private readonly IVocomService _vocomService;
        private readonly MC9S12XEP100Helper _helper;
        private readonly IRegisterAccess _registerAccess;

        /// <summary>
        /// Initializes a new instance of the MC9S12XEP100Integration class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="vocomService">The Vocom service</param>
        /// <param name="protocolType">The protocol type</param>
        public MC9S12XEP100Integration(ILoggingService loggingService, IVocomService vocomService, ECUProtocolType protocolType)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _logger = loggingService;
            _vocomService = vocomService ?? throw new ArgumentNullException(nameof(vocomService));

            // Create the appropriate register access interface based on the protocol type
            _registerAccess = CreateRegisterAccess(protocolType);

            // Create the helper with the register access interface
            _helper = new MC9S12XEP100Helper(_logger ?? new NullLoggingService(), _registerAccess);
        }

        /// <summary>
        /// Creates a register access interface based on the protocol type
        /// </summary>
        /// <param name="protocolType">The protocol type</param>
        /// <returns>The register access interface</returns>
        private IRegisterAccess CreateRegisterAccess(ECUProtocolType protocolType)
        {
            switch (protocolType)
            {
                case ECUProtocolType.CAN:
                    return new CANRegisterAccess(_logger ?? new NullLoggingService(), _vocomService);
                case ECUProtocolType.SPI:
                    return new SPIRegisterAccess(_logger ?? new NullLoggingService(), _vocomService);
                case ECUProtocolType.SCI:
                    return new SCIRegisterAccess(_logger ?? new NullLoggingService(), _vocomService);
                case ECUProtocolType.IIC:
                    return new IICRegisterAccess(_logger ?? new NullLoggingService(), _vocomService);
                default:
                    throw new ArgumentException($"Unsupported protocol type: {protocolType}", nameof(protocolType));
            }
        }

        /// <summary>
        /// Gets the MC9S12XEP100 helper
        /// </summary>
        public MC9S12XEP100Helper Helper => _helper;

        /// <summary>
        /// Reads EEPROM data from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>EEPROM data as byte array</returns>
        public async Task<byte[]> ReadEEPROMAsync(ECUDevice ecu)
        {
            return await ReadEEPROMAsync(ecu, null);
        }

        /// <summary>
        /// Reads EEPROM data from an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>EEPROM data as byte array</returns>
        public async Task<byte[]> ReadEEPROMAsync(ECUDevice ecu, IProgress<int>? progress)
        {
            try
            {
                _loggingService?.LogInformation($"Reading EEPROM from ECU {ecu?.Name} using MC9S12XEP100 integration", "MC9S12XEP100Integration");

                if (ecu == null)
                {
                    _loggingService?.LogError("ECU is null", "MC9S12XEP100Integration");
                    return Array.Empty<byte>();
                }

                // Report initial progress
                progress?.Report(0);

                // Check if the ECU is secured
                bool isSecured = await _helper.IsSecuredAsync();
                if (isSecured)
                {
                    _loggingService?.LogWarning($"ECU {ecu.Name} is secured, attempting security access", "MC9S12XEP100Integration");

                    // Get the backdoor key from the ECU properties
                    if (!ecu.Properties.TryGetValue("BackdoorKey", out object keyObj) || !(keyObj is byte[] key) || key.Length != 8)
                    {
                        _loggingService?.LogError($"Invalid or missing backdoor key for ECU {ecu.Name}", "MC9S12XEP100Integration");
                        return Array.Empty<byte>();
                    }

                    // Perform security access
                    bool accessGranted = await _helper.PerformSecurityAccessAsync((byte[])keyObj);
                    if (!accessGranted)
                    {
                        _loggingService?.LogError($"Security access denied for ECU {ecu.Name}", "MC9S12XEP100Integration");
                        return Array.Empty<byte>();
                    }

                    // Report progress after security access
                    progress?.Report(10);
                }
                else
                {
                    // Skip security access progress if not needed
                    progress?.Report(10);
                }

                // Read the EEPROM data
                byte[] eepromData = new byte[ecu.EEPROMSize];
                uint address = MC9S12XEP100Configuration.MemoryMap.EEPROM_START;

                _loggingService?.LogInformation($"Reading {ecu.EEPROMSize} bytes of EEPROM data from address 0x{address:X8}", "MC9S12XEP100Integration");

                // Read the EEPROM data in chunks
                const int chunkSize = 256; // 256-byte chunks for EEPROM (smaller for better progress reporting)
                int totalChunks = (int)Math.Ceiling((double)ecu.EEPROMSize / chunkSize);

                // Track ECC errors for reporting
                int singleBitErrorCount = 0;
                int multiBitErrorCount = 0;

                for (int offset = 0, chunkIndex = 0; offset < ecu.EEPROMSize; offset += chunkSize, chunkIndex++)
                {
                    int size = Math.Min(chunkSize, ecu.EEPROMSize - offset);

                    // Use a callback to track ECC errors during read
                    byte[] chunk = await _helper.ReadFlashBlockAsync(
                        address + (uint)offset,
                        size,
                        (addr, errorInfo) =>
                        {
                            if (errorInfo.Status == MC9S12XEP100Helper.ECCErrorStatus.SingleBitError)
                            {
                                singleBitErrorCount++;
                            }
                            else if (errorInfo.Status == MC9S12XEP100Helper.ECCErrorStatus.MultiBitError)
                            {
                                multiBitErrorCount++;
                            }
                        });

                    if (chunk == null)
                    {
                        _loggingService?.LogError($"Failed to read EEPROM chunk at offset {offset}", "MC9S12XEP100Integration");
                        return Array.Empty<byte>();
                    }

                    Array.Copy(chunk, 0, eepromData, offset, chunk.Length);

                    // Report progress (10% to 95%)
                    int percentComplete = 10 + (int)((chunkIndex + 1) * 85.0 / totalChunks);
                    progress?.Report(percentComplete);
                    _loggingService?.LogDebug($"EEPROM read progress: {percentComplete}%", "MC9S12XEP100Integration");
                }

                // Perform data validation (CRC check)
                _loggingService?.LogInformation("Validating EEPROM data integrity", "MC9S12XEP100Integration");

                // Check if the EEPROM data contains a CRC at the end
                if (eepromData.Length >= 2)
                {
                    // Extract the stored CRC from the last 2 bytes
                    ushort storedCrc = (ushort)((eepromData[eepromData.Length - 2] << 8) | eepromData[eepromData.Length - 1]);

                    // Calculate CRC for the data (excluding the CRC bytes)
                    byte[] dataWithoutCrc = new byte[eepromData.Length - 2];
                    Array.Copy(eepromData, dataWithoutCrc, eepromData.Length - 2);
                    ushort calculatedCrc = _helper.CalculateCRC16(dataWithoutCrc);

                    if (calculatedCrc != storedCrc)
                    {
                        _loggingService?.LogWarning(
                            $"EEPROM data CRC mismatch: calculated 0x{calculatedCrc:X4}, stored 0x{storedCrc:X4}",
                            "MC9S12XEP100Integration");

                        // Store CRC error information in the ECU properties
                        if (!ecu.Properties.ContainsKey("EEPROMValidation"))
                        {
                            ecu.Properties["EEPROMValidation"] = new Dictionary<string, object>();
                        }

                        if (ecu.Properties["EEPROMValidation"] is Dictionary<string, object> validationInfo)
                        {
                            validationInfo["LastCRCMismatch"] = true;
                            validationInfo["CalculatedCRC"] = calculatedCrc;
                            validationInfo["StoredCRC"] = storedCrc;
                            validationInfo["Timestamp"] = DateTime.Now;
                        }
                    }
                    else
                    {
                        _loggingService?.LogInformation("EEPROM data CRC validation successful", "MC9S12XEP100Integration");
                    }
                }

                // Log ECC error statistics
                if (singleBitErrorCount > 0 || multiBitErrorCount > 0)
                {
                    _loggingService?.LogWarning(
                        $"ECC errors detected during EEPROM read: {singleBitErrorCount} single-bit errors (corrected), {multiBitErrorCount} multi-bit errors (uncorrected)",
                        "MC9S12XEP100Integration");

                    // Store ECC error information in the ECU properties
                    if (!ecu.Properties.ContainsKey("EEPROMECCErrors"))
                    {
                        ecu.Properties["EEPROMECCErrors"] = new Dictionary<string, object>();
                    }

                    if (ecu.Properties["EEPROMECCErrors"] is Dictionary<string, object> eccErrors)
                    {
                        eccErrors["LastReadSingleBitErrors"] = singleBitErrorCount;
                        eccErrors["LastReadMultiBitErrors"] = multiBitErrorCount;
                        eccErrors["LastReadTimestamp"] = DateTime.Now;
                    }
                }

                // Report 100% progress
                progress?.Report(100);

                _loggingService?.LogInformation($"Read {eepromData.Length} bytes of EEPROM data from ECU {ecu.Name}", "MC9S12XEP100Integration");
                return eepromData;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error reading EEPROM from ECU {ecu?.Name}: {ex.Message}", "MC9S12XEP100Integration", ex);
                return Array.Empty<byte>();
            }
        }

        /// <summary>
        /// Writes EEPROM data to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="data">The EEPROM data to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public async Task<bool> WriteEEPROMAsync(ECUDevice ecu, byte[] data)
        {
            return await WriteEEPROMAsync(ecu, data, null);
        }

        /// <summary>
        /// Writes EEPROM data to an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="data">The EEPROM data to write</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public async Task<bool> WriteEEPROMAsync(ECUDevice ecu, byte[] data, IProgress<int>? progress)
        {
            try
            {
                _loggingService?.LogInformation($"Writing EEPROM to ECU {ecu?.Name} using MC9S12XEP100 integration", "MC9S12XEP100Integration");

                if (ecu == null)
                {
                    _loggingService?.LogError("ECU is null", "MC9S12XEP100Integration");
                    return false;
                }

                if (data == null || data.Length == 0)
                {
                    _loggingService?.LogError("EEPROM data is null or empty", "MC9S12XEP100Integration");
                    return false;
                }

                if (data.Length > ecu.EEPROMSize)
                {
                    _loggingService?.LogError($"EEPROM data size ({data.Length} bytes) exceeds ECU EEPROM size ({ecu.EEPROMSize} bytes)", "MC9S12XEP100Integration");
                    return false;
                }

                // Check if the ECU is secured
                bool isSecured = await _helper.IsSecuredAsync();
                if (isSecured)
                {
                    _loggingService?.LogWarning($"ECU {ecu.Name} is secured, attempting security access", "MC9S12XEP100Integration");

                    // Get the backdoor key from the ECU properties
                    if (!ecu.Properties.TryGetValue("BackdoorKey", out object keyObj) || !(keyObj is byte[] key) || key.Length != 8)
                    {
                        _loggingService?.LogError($"Invalid or missing backdoor key for ECU {ecu.Name}", "MC9S12XEP100Integration");
                        return false;
                    }

                    // Perform security access
                    bool accessGranted = await _helper.PerformSecurityAccessAsync((byte[])keyObj);
                    if (!accessGranted)
                    {
                        _loggingService?.LogError($"Security access denied for ECU {ecu.Name}", "MC9S12XEP100Integration");
                        return false;
                    }
                }

                // Write the EEPROM data
                uint address = MC9S12XEP100Configuration.MemoryMap.EEPROM_START;

                _loggingService?.LogInformation($"Writing {data.Length} bytes of EEPROM data to address 0x{address:X8}", "MC9S12XEP100Integration");

                // Report initial progress
                progress?.Report(0);

                // Generate a unique operation ID for tracking
                string operationId = $"EEPROM_WRITE_{DateTime.Now.Ticks}";

                // EEPROM is typically smaller, so we can use a different approach than for flash
                // First, erase the EEPROM sector(s)
                _loggingService?.LogInformation("Erasing EEPROM sectors before writing", "MC9S12XEP100Integration");

                // Calculate how many sectors we need to erase
                int sectorCount = (int)Math.Ceiling((double)data.Length / MC9S12XEP100Configuration.SECTOR_SIZE);
                for (int i = 0; i < sectorCount; i++)
                {
                    uint sectorAddress = address + (uint)(i * MC9S12XEP100Configuration.SECTOR_SIZE);
                    bool eraseSuccess = await _helper.EraseFlashSectorAsync(sectorAddress);
                    if (!eraseSuccess)
                    {
                        _loggingService?.LogError($"Failed to erase EEPROM sector at address 0x{sectorAddress:X8}", "MC9S12XEP100Integration");
                        return false;
                    }

                    // Report erase progress (up to 30%)
                    int eraseProgress = (int)((i + 1) * 30.0 / sectorCount);
                    progress?.Report(eraseProgress);
                }

                // Now write the EEPROM data in chunks
                const int chunkSize = 256; // 256-byte chunks for EEPROM
                int totalChunks = (int)Math.Ceiling((double)data.Length / chunkSize);

                for (int offset = 0, chunkIndex = 0; offset < data.Length; offset += chunkSize, chunkIndex++)
                {
                    int size = Math.Min(chunkSize, data.Length - offset);
                    byte[] chunk = new byte[size];
                    Array.Copy(data, offset, chunk, 0, size);

                    // Use secure flash programming for each chunk
                    bool success = await _helper.SecureFlashProgramAsync(address + (uint)offset, chunk);
                    if (!success)
                    {
                        _loggingService?.LogError($"Failed to write EEPROM data chunk at offset {offset}", "MC9S12XEP100Integration");
                        return false;
                    }

                    // Report write progress (30% to 90%)
                    int writeProgress = 30 + (int)((chunkIndex + 1) * 60.0 / totalChunks);
                    progress?.Report(writeProgress);
                    _loggingService?.LogDebug($"EEPROM write progress: {writeProgress}%", "MC9S12XEP100Integration");
                }

                // Verify the integrity of the written data
                _loggingService?.LogInformation("Verifying EEPROM data integrity", "MC9S12XEP100Integration");
                bool verified = await _helper.VerifyFlashIntegrityAsync(address, data.Length);
                if (!verified)
                {
                    _loggingService?.LogError($"EEPROM data integrity verification failed for ECU {ecu.Name}", "MC9S12XEP100Integration");
                    return false;
                }

                // Report 100% progress
                progress?.Report(100);

                _loggingService?.LogInformation($"Successfully wrote and verified {data.Length} bytes of EEPROM data to ECU {ecu.Name}", "MC9S12XEP100Integration");
                return true;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error writing EEPROM to ECU {ecu?.Name}: {ex.Message}", "MC9S12XEP100Integration");
                return false;
            }
        }

        /// <summary>
        /// Reads microcontroller code from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Microcontroller code as byte array</returns>
        public async Task<byte[]> ReadMicrocontrollerCodeAsync(ECUDevice ecu)
        {
            return await ReadMicrocontrollerCodeAsync(ecu, null);
        }

        /// <summary>
        /// Reads microcontroller code from an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>Microcontroller code as byte array</returns>
        public async Task<byte[]> ReadMicrocontrollerCodeAsync(ECUDevice ecu, IProgress<int>? progress)
        {
            try
            {
                _loggingService?.LogInformation($"Reading microcontroller code from ECU {ecu?.Name} using MC9S12XEP100 integration", "MC9S12XEP100Integration");

                if (ecu == null)
                {
                    _loggingService?.LogError("ECU is null", "MC9S12XEP100Integration");
                    return Array.Empty<byte>();
                }

                // Report initial progress
                progress?.Report(0);

                // Check if the ECU is secured
                bool isSecured = await _helper.IsSecuredAsync();
                if (isSecured)
                {
                    _loggingService?.LogWarning($"ECU {ecu.Name} is secured, attempting security access", "MC9S12XEP100Integration");

                    // Get the backdoor key from the ECU properties
                    if (!ecu.Properties.TryGetValue("BackdoorKey", out object keyObj) || !(keyObj is byte[] key) || key.Length != 8)
                    {
                        _loggingService?.LogError($"Invalid or missing backdoor key for ECU {ecu.Name}", "MC9S12XEP100Integration");
                        return Array.Empty<byte>();
                    }

                    // Perform security access
                    bool accessGranted = await _helper.PerformSecurityAccessAsync((byte[])keyObj);
                    if (!accessGranted)
                    {
                        _loggingService?.LogError($"Security access denied for ECU {ecu.Name}", "MC9S12XEP100Integration");
                        return Array.Empty<byte>();
                    }
                }

                // Read the microcontroller code
                byte[] mcuCode = new byte[ecu.FlashSize];
                uint address = MC9S12XEP100Configuration.MemoryMap.FLASH_START;

                _loggingService?.LogInformation($"Reading {ecu.FlashSize} bytes of microcontroller code from address 0x{address:X8}", "MC9S12XEP100Integration");

                // Read the microcontroller code in chunks
                const int chunkSize = 4096; // 4KB chunks
                int totalChunks = (int)Math.Ceiling((double)ecu.FlashSize / chunkSize);

                // Track ECC errors for reporting
                int singleBitErrorCount = 0;
                int multiBitErrorCount = 0;
                Dictionary<uint, int> errorSectors = new Dictionary<uint, int>();

                for (int offset = 0, chunkIndex = 0; offset < ecu.FlashSize; offset += chunkSize, chunkIndex++)
                {
                    int size = Math.Min(chunkSize, ecu.FlashSize - offset);

                    // Use a callback to track ECC errors during read
                    byte[] chunk = await _helper.ReadFlashBlockAsync(
                        address + (uint)offset,
                        size,
                        (addr, errorInfo) =>
                        {
                            if (errorInfo.Status == MC9S12XEP100Helper.ECCErrorStatus.SingleBitError)
                            {
                                singleBitErrorCount++;
                                uint sector = _helper.AlignToSectorAddress(addr);
                                if (!errorSectors.ContainsKey(sector))
                                {
                                    errorSectors[sector] = 0;
                                }
                                errorSectors[sector]++;
                            }
                            else if (errorInfo.Status == MC9S12XEP100Helper.ECCErrorStatus.MultiBitError)
                            {
                                multiBitErrorCount++;
                                uint sector = _helper.AlignToSectorAddress(addr);
                                if (!errorSectors.ContainsKey(sector))
                                {
                                    errorSectors[sector] = 0;
                                }
                                errorSectors[sector]++;
                            }
                        });

                    if (chunk == null)
                    {
                        _loggingService?.LogError($"Failed to read microcontroller code chunk at offset {offset}", "MC9S12XEP100Integration");
                        return Array.Empty<byte>();
                    }

                    Array.Copy(chunk, 0, mcuCode, offset, chunk.Length);

                    // Report progress
                    int percentComplete = (int)((chunkIndex + 1) * 100.0 / totalChunks);
                    progress?.Report(percentComplete);
                    _loggingService?.LogDebug($"Flash read progress: {percentComplete}%", "MC9S12XEP100Integration");
                }

                // Log ECC error statistics
                if (singleBitErrorCount > 0 || multiBitErrorCount > 0)
                {
                    _loggingService?.LogWarning(
                        $"ECC errors detected during read: {singleBitErrorCount} single-bit errors (corrected), {multiBitErrorCount} multi-bit errors (uncorrected)",
                        "MC9S12XEP100Integration");

                    // Log sectors with the most errors
                    var worstSectors = errorSectors.OrderByDescending(kv => kv.Value).Take(3);
                    foreach (var sector in worstSectors)
                    {
                        _loggingService?.LogWarning(
                            $"Sector at 0x{sector.Key:X8} has {sector.Value} ECC errors",
                            "MC9S12XEP100Integration");
                    }

                    // Store ECC error information in the ECU properties for diagnostics
                    if (!ecu.Properties.ContainsKey("ECCErrorStats"))
                    {
                        ecu.Properties["ECCErrorStats"] = new Dictionary<string, object>();
                    }

                    if (ecu.Properties["ECCErrorStats"] is Dictionary<string, object> eccStats)
                    {
                        eccStats["LastReadSingleBitErrors"] = singleBitErrorCount;
                        eccStats["LastReadMultiBitErrors"] = multiBitErrorCount;
                        eccStats["LastReadTimestamp"] = DateTime.Now;
                        eccStats["WorstSectors"] = worstSectors.Select(s => $"0x{s.Key:X8}:{s.Value}").ToArray();
                    }
                }

                _loggingService?.LogInformation($"Read {mcuCode.Length} bytes of microcontroller code from ECU {ecu.Name}", "MC9S12XEP100Integration");
                return mcuCode;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error reading microcontroller code from ECU {ecu?.Name}: {ex.Message}", "MC9S12XEP100Integration");
                return Array.Empty<byte>();
            }
        }

        /// <summary>
        /// Writes microcontroller code to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="code">The microcontroller code to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public async Task<bool> WriteMicrocontrollerCodeAsync(ECUDevice ecu, byte[] code)
        {
            return await WriteMicrocontrollerCodeAsync(ecu, code, null);
        }

        /// <summary>
        /// Writes microcontroller code to an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="code">The microcontroller code to write</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public async Task<bool> WriteMicrocontrollerCodeAsync(ECUDevice ecu, byte[] code, IProgress<int>? progress)
        {
            try
            {
                _loggingService?.LogInformation($"Writing microcontroller code to ECU {ecu?.Name} using MC9S12XEP100 integration", "MC9S12XEP100Integration");

                if (ecu == null)
                {
                    _loggingService?.LogError("ECU is null", "MC9S12XEP100Integration");
                    return false;
                }

                if (code == null || code.Length == 0)
                {
                    _loggingService?.LogError("Microcontroller code is null or empty", "MC9S12XEP100Integration");
                    return false;
                }

                if (code.Length > ecu.FlashSize)
                {
                    _loggingService?.LogError($"Microcontroller code size ({code.Length} bytes) exceeds ECU flash size ({ecu.FlashSize} bytes)", "MC9S12XEP100Integration");
                    return false;
                }

                // Check if the ECU is secured
                bool isSecured = await _helper.IsSecuredAsync();
                if (isSecured)
                {
                    _loggingService?.LogWarning($"ECU {ecu.Name} is secured, attempting security access", "MC9S12XEP100Integration");

                    // Get the backdoor key from the ECU properties
                    if (!ecu.Properties.TryGetValue("BackdoorKey", out object keyObj) || !(keyObj is byte[] key) || key.Length != 8)
                    {
                        _loggingService?.LogError($"Invalid or missing backdoor key for ECU {ecu.Name}", "MC9S12XEP100Integration");
                        return false;
                    }

                    // Perform security access
                    bool accessGranted = await _helper.PerformSecurityAccessAsync((byte[])keyObj);
                    if (!accessGranted)
                    {
                        _loggingService?.LogError($"Security access denied for ECU {ecu.Name}", "MC9S12XEP100Integration");
                        return false;
                    }
                }

                // Write the microcontroller code
                uint address = MC9S12XEP100Configuration.MemoryMap.FLASH_START;

                _loggingService?.LogInformation($"Writing {code.Length} bytes of microcontroller code to address 0x{address:X8}", "MC9S12XEP100Integration");

                // Determine if we should use burst mode for programming
                bool useBurstMode = code.Length > MC9S12XEP100Configuration.PHRASE_SIZE * 4;
                _loggingService?.LogInformation($"Using {(useBurstMode ? "burst" : "standard")} mode for flash programming", "MC9S12XEP100Integration");

                // Generate a unique operation ID for tracking
                string operationId = $"MCU_WRITE_{DateTime.Now.Ticks}";

                if (useBurstMode)
                {
                    // Calculate optimal burst size based on memory characteristics
                    int optimalBurstSize = _helper.CalculateOptimalBurstSize(code.Length, address);
                    _loggingService?.LogInformation($"Using optimal burst size of {optimalBurstSize} phrases", "MC9S12XEP100Integration");

                    // Write the microcontroller code in bursts
                    const int chunkSize = 4096; // 4KB chunks
                    int totalChunks = (int)Math.Ceiling((double)code.Length / chunkSize);

                    for (int offset = 0, chunkIndex = 0; offset < code.Length; offset += chunkSize, chunkIndex++)
                    {
                        int size = Math.Min(chunkSize, code.Length - offset);
                        byte[] chunk = new byte[size];
                        Array.Copy(code, offset, chunk, 0, size);

                        // Use burst mode flash programming for each chunk
                        bool success = await _helper.ProgramFlashBlockAsync(address + (uint)offset, chunk, true, operationId);
                        if (!success)
                        {
                            _loggingService?.LogError($"Failed to write microcontroller code chunk at offset {offset}", "MC9S12XEP100Integration");
                            return false;
                        }

                        // Report progress
                        int percentComplete = (int)((chunkIndex + 1) * 100.0 / totalChunks);
                        progress?.Report(percentComplete);
                        _loggingService?.LogDebug($"Flash programming progress: {percentComplete}%", "MC9S12XEP100Integration");
                    }
                }
                else
                {
                    // For small code sizes, use standard programming mode
                    bool success = await _helper.ProgramFlashBlockAsync(address, code, false, operationId);
                    if (!success)
                    {
                        _loggingService?.LogError($"Failed to write microcontroller code", "MC9S12XEP100Integration");
                        return false;
                    }

                    // Report 100% progress
                    progress?.Report(100);
                }

                // Verify the integrity of the written code
                _loggingService?.LogInformation($"Verifying integrity of written microcontroller code", "MC9S12XEP100Integration");
                bool verified = await _helper.VerifyFlashIntegrityAsync(address, code.Length);
                if (!verified)
                {
                    _loggingService?.LogError($"Microcontroller code integrity verification failed", "MC9S12XEP100Integration");
                    return false;
                }

                _loggingService?.LogInformation($"Wrote and verified {code.Length} bytes of microcontroller code to ECU {ecu.Name}", "MC9S12XEP100Integration");
                return true;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error writing microcontroller code to ECU {ecu?.Name}: {ex.Message}", "MC9S12XEP100Integration");
                return false;
            }
        }

        /// <summary>
        /// Generates and stores a backdoor key for an ECU
        /// </summary>
        /// <param name="ecu">The ECU to generate a backdoor key for</param>
        /// <param name="masterKey">The master key (16 bytes)</param>
        /// <returns>True if the backdoor key was generated and stored successfully, false otherwise</returns>
        public bool GenerateAndStoreBackdoorKey(ECUDevice ecu, byte[] masterKey)
        {
            try
            {
                _loggingService?.LogInformation($"Generating backdoor key for ECU {ecu?.Name}", "MC9S12XEP100Integration");

                if (ecu == null)
                {
                    _loggingService?.LogError("ECU is null", "MC9S12XEP100Integration");
                    return false;
                }

                if (string.IsNullOrEmpty(ecu.Id))
                {
                    _loggingService?.LogError("ECU ID is null or empty", "MC9S12XEP100Integration");
                    return false;
                }

                if (masterKey == null || masterKey.Length != 16)
                {
                    _loggingService?.LogError("Invalid master key (must be 16 bytes)", "MC9S12XEP100Integration");
                    return false;
                }

                // Generate the backdoor key
                byte[] backdoorKey = _helper.GenerateBackdoorKey(ecu.Id, masterKey);
                if (backdoorKey == null || backdoorKey.Length != 8)
                {
                    _loggingService?.LogError("Failed to generate backdoor key", "MC9S12XEP100Integration");
                    return false;
                }

                // Store the backdoor key in the ECU properties
                ecu.Properties["BackdoorKey"] = backdoorKey;

                _loggingService?.LogInformation($"Generated and stored backdoor key for ECU {ecu.Name}", "MC9S12XEP100Integration");
                return true;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error generating backdoor key for ECU {ecu?.Name}: {ex.Message}", "MC9S12XEP100Integration");
                return false;
            }
        }

        /// <summary>
        /// Secures an ECU by setting the security bits
        /// </summary>
        /// <param name="ecu">The ECU to secure</param>
        /// <returns>True if the ECU was secured successfully, false otherwise</returns>
        public async Task<bool> SecureECUAsync(ECUDevice ecu)
        {
            try
            {
                _loggingService?.LogInformation($"Securing ECU {ecu?.Name}", "MC9S12XEP100Integration");

                if (ecu == null)
                {
                    _loggingService?.LogError("ECU is null", "MC9S12XEP100Integration");
                    return false;
                }

                // Check if the ECU is already secured
                bool isSecured = await _helper.IsSecuredAsync();
                if (isSecured)
                {
                    _loggingService?.LogInformation($"ECU {ecu.Name} is already secured", "MC9S12XEP100Integration");
                    return true;
                }

                // Make sure we have a backdoor key before securing the ECU
                if (!ecu.Properties.TryGetValue("BackdoorKey", out object keyObj) || !(keyObj is byte[] key) || key.Length != 8)
                {
                    _loggingService?.LogError($"Cannot secure ECU {ecu.Name} without a valid backdoor key", "MC9S12XEP100Integration");
                    return false;
                }

                // Secure the ECU
                bool success = await _helper.SecureECUAsync();
                if (!success)
                {
                    _loggingService?.LogError($"Failed to secure ECU {ecu.Name}", "MC9S12XEP100Integration");
                    return false;
                }

                _loggingService?.LogInformation($"ECU {ecu.Name} secured successfully", "MC9S12XEP100Integration");
                return true;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"Error securing ECU {ecu?.Name}: {ex.Message}", "MC9S12XEP100Integration");
                return false;
            }
        }
    }
}
