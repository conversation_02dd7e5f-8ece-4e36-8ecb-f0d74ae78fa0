#define MyAppName "Volvo Flash WR"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "S.A.H Software Solutions"
#define MyAppURL "https://www.sahsoftware.com/"
#define MyAppExeName "VolvoFlashWR.Launcher.exe"

[Setup]
; NOTE: The value of AppId uniquely identifies this application.
; Do not use the same AppId value in installers for other applications.
AppId={{E8F3A9B7-C5D4-4E3F-B2A1-9D8C7D6A5E4F}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\{#MyAppName}
DefaultGroupName={#MyAppName}
OutputDir=installer
OutputBaseFilename=VolvoFlashWR_Setup
Compression=lzma
SolidCompression=yes
ArchitecturesInstallIn64BitMode=x64
PrivilegesRequired=admin

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
; Main application files
Source: "publish\VolvoFlashWR\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{group}\Key Generator"; Filename: "{app}\Tools\VolvoFlashWR.KeyGenerator.exe"
Name: "{group}\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

[Dirs]
Name: "{app}\Config"; Permissions: users-modify
Name: "{app}\Logs"; Permissions: users-modify
Name: "{app}\Backups"; Permissions: users-modify

[Code]
function InitializeSetup(): Boolean;
begin
  // Check if .NET 8.0 is installed
  // This is a simplified check - in a real installer, you would use a more robust method
  if not RegKeyExists(HKLM, 'SOFTWARE\Microsoft\NET Framework Setup\NDP\v8.0') then
  begin
    MsgBox('.NET 8.0 Runtime is required to run this application. Please install it first.', mbInformation, MB_OK);
    Result := False;
  end
  else
    Result := True;
end;
