<UserControl x:Class="VolvoFlashWR.UI.Controls.FlashOperationProgressControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:VolvoFlashWR.UI.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="800">
    <UserControl.Resources>
        <local:StatusToColorConverter x:Key="StatusToColorConverter"/>
        <local:SecondsToTimeSpanConverter x:Key="SecondsToTimeSpanConverter"/>
        <local:BytesToKBConverter x:Key="BytesToKBConverter"/>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2D2D30" Padding="10">
            <TextBlock Text="Flash Operations Monitor" Foreground="White" FontSize="16" FontWeight="Bold"/>
        </Border>
        
        <!-- Controls -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="10" HorizontalAlignment="Right">
            <Button x:Name="btnClearCompleted" Content="Clear Completed" Margin="5" Padding="8,3" Click="BtnClearCompleted_Click"/>
            <Button x:Name="btnRefresh" Content="Refresh" Margin="5" Padding="8,3" Click="BtnRefresh_Click"/>
        </StackPanel>
        
        <!-- Operations List -->
        <ListView x:Name="lvOperations" Grid.Row="2" Margin="10" BorderThickness="1" BorderBrush="#CCCCCC">
            <ListView.View>
                <GridView>
                    <GridViewColumn Header="Type" Width="80" DisplayMemberBinding="{Binding OperationType}"/>
                    <GridViewColumn Header="Address" Width="100">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Address, StringFormat=0x{0:X8}}"/>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Header="Size" Width="80" DisplayMemberBinding="{Binding Size, StringFormat={}{0:N0} bytes}"/>
                    <GridViewColumn Header="Status" Width="80" DisplayMemberBinding="{Binding Status}"/>
                    <GridViewColumn Header="Progress" Width="200">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <Grid>
                                    <ProgressBar Value="{Binding ProgressPercentage}" Height="18" Width="180" 
                                                 Foreground="{Binding Status, Converter={StaticResource StatusToColorConverter}}"/>
                                    <TextBlock Text="{Binding ProgressPercentage, StringFormat={}{0:N1}%}" 
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Grid>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Header="Time" Width="100">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock>
                                    <TextBlock.Text>
                                        <MultiBinding StringFormat="{}{0:mm\\:ss} / {1:mm\\:ss}">
                                            <Binding Path="ElapsedTime"/>
                                            <Binding Path="EstimatedTimeRemaining" Converter="{StaticResource SecondsToTimeSpanConverter}"/>
                                        </MultiBinding>
                                    </TextBlock.Text>
                                </TextBlock>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Header="Throughput" Width="120">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock>
                                    <TextBlock.Text>
                                        <MultiBinding StringFormat="{}{0:N1} KB/s">
                                            <Binding Path="CurrentThroughput" Converter="{StaticResource BytesToKBConverter}"/>
                                        </MultiBinding>
                                    </TextBlock.Text>
                                </TextBlock>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Header="Avg/Peak" Width="120">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock>
                                    <TextBlock.Text>
                                        <MultiBinding StringFormat="{}{0:N1}/{1:N1} KB/s">
                                            <Binding Path="AverageThroughput" Converter="{StaticResource BytesToKBConverter}"/>
                                            <Binding Path="PeakThroughput" Converter="{StaticResource BytesToKBConverter}"/>
                                        </MultiBinding>
                                    </TextBlock.Text>
                                </TextBlock>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Header="Retries" Width="60" DisplayMemberBinding="{Binding RetryCount}"/>
                </GridView>
            </ListView.View>
        </ListView>
    </Grid>
</UserControl>
