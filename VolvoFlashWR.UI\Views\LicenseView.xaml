<Window x:Class="VolvoFlashWR.UI.Views.LicenseView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:VolvoFlashWR.UI.Views"
        xmlns:converters="clr-namespace:VolvoFlashWR.UI.Converters"
        mc:Ignorable="d"
        Title="License Management" Height="450" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="Volvo Flash WR License Management" 
                       FontSize="20" FontWeight="Bold" Margin="0,0,0,10"/>
            <TextBlock Text="Manage your software license" 
                       FontSize="14" Foreground="#666666"/>
        </StackPanel>

        <!-- License Status -->
        <Border Grid.Row="1" BorderBrush="#CCCCCC" BorderThickness="1" Padding="15" Margin="0,0,0,20"
                Background="#F5F5F5" CornerRadius="5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="License Status:" 
                           FontWeight="Bold" Margin="0,0,10,0"/>
                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding LicenseStatusText}" 
                           FontWeight="SemiBold"/>

                <!-- Trial Period Info -->
                <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" 
                            Visibility="{Binding IsInTrial, Converter={StaticResource BooleanToVisibilityConverter}}"
                            Margin="0,10,0,0">
                    <TextBlock Text="Your trial period will expire on:" Margin="0,0,0,5"/>
                    <TextBlock Text="{Binding LicenseInfo.TrialEndDate, StringFormat='{}{0:yyyy-MM-dd}'}" 
                               FontWeight="Bold"/>
                </StackPanel>

                <!-- Licensed Info -->
                <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" 
                            Visibility="{Binding IsLicensed, Converter={StaticResource BooleanToVisibilityConverter}}"
                            Margin="0,10,0,0">
                    <TextBlock Text="Your license information:" Margin="0,0,0,5"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Activation Date:" Margin="0,0,10,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" 
                                   Text="{Binding LicenseInfo.ActivationDate, StringFormat='{}{0:yyyy-MM-dd}'}" 
                                   Margin="0,0,0,5"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Expiration Date:" Margin="0,0,10,5"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" 
                                   Text="{Binding LicenseInfo.ExpirationDate, StringFormat='{}{0:yyyy-MM-dd}'}" 
                                   Margin="0,0,0,5"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="License Key:" Margin="0,0,10,5"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" 
                                   Text="{Binding LicenseInfo.LicenseKey}" 
                                   TextWrapping="Wrap"/>
                    </Grid>
                </StackPanel>

                <!-- Expired Info -->
                <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" 
                            Visibility="{Binding IsTrialExpired, Converter={StaticResource BooleanToVisibilityConverter}}"
                            Margin="0,10,0,0">
                    <TextBlock Text="Your trial period has expired. Please activate the software to continue using it." 
                               TextWrapping="Wrap" Foreground="Red"/>
                </StackPanel>

                <!-- License Expired Info -->
                <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" 
                            Visibility="{Binding IsLicenseExpired, Converter={StaticResource BooleanToVisibilityConverter}}"
                            Margin="0,10,0,0">
                    <TextBlock Text="Your license has expired. Please renew your license to continue using the software." 
                               TextWrapping="Wrap" Foreground="Red"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Activation Section -->
        <Grid Grid.Row="2" Margin="0,0,0,20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0" Text="Activate Your License" 
                       FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10"/>

            <TextBlock Grid.Row="1" Text="Enter your activation key below to activate the software:" 
                       Margin="0,0,0,10"/>

            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox Grid.Column="0" Text="{Binding ActivationKey, UpdateSourceTrigger=PropertyChanged}" 
                         Padding="8" Margin="0,0,10,0"/>
                <Button Grid.Column="1" Content="Activate" Command="{Binding ActivateCommand}" 
                        Padding="15,8" IsEnabled="{Binding IsActivationEnabled}"/>
            </Grid>

            <Button Grid.Row="3" Content="Deactivate License" Command="{Binding DeactivateCommand}" 
                    HorizontalAlignment="Left" Margin="0,20,0,0" Padding="15,8"
                    Visibility="{Binding CanDeactivate, Converter={StaticResource BooleanToVisibilityConverter}}"/>
        </Grid>

        <!-- Status and Buttons -->
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Status Message -->
            <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" VerticalAlignment="Center" 
                       Foreground="{Binding StatusMessageColor}"/>

            <!-- Close Button -->
            <Button Grid.Column="1" Content="Close" Command="{Binding CloseCommand}" 
                    Margin="5" Padding="15,8" Width="100"/>
        </Grid>

        <!-- Busy Indicator -->
        <Grid Grid.Row="0" Grid.RowSpan="4" Background="#80000000" 
              Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <TextBlock Text="Processing..." Foreground="White" FontSize="16" Margin="0,0,0,10"/>
                <ProgressBar IsIndeterminate="True" Width="200" Height="20"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
