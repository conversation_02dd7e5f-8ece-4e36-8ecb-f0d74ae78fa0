﻿29.1 Introduction
The FTM1024K5 module implements the following:

• 1024 Kbytes of P-Flash (Program Flash) memory, consisting of 5 physical Flash blocks, intended
primarily for nonvolatile code storage

• 32 Kbytes of D-Flash (Data Flash) memory, consisting of 1 physical Flash block, that can be used
as nonvolatile storage to support the built-in hardware scheme for emulated EEPROM, as basic
Flash memory primarily intended for nonvolatile data storage, or as a combination of both

• 4 Kbytes of buffer RAM, consisting of 1 physical RAM block, that can be used as emulated
EEPROM using a built-in hardware scheme, as basic RAM, or as a combination of both

The Flash memory is ideal for single-supply applications allowing for field reprogramming without
requiring external high voltage sources for program or erase operations. The Flash module includes a
memory controller that executes commands to modify Flash memory contents or configure module
resources for emulated EEPROM operation. The user interface to the memory controller consists of the
indexed Flash Common Command Object (FCCOB) register which is written to with the command, global
address, data, and any required command parameters. The memory controller must complete the execution
of a command before the FCCOB register can be written to with a new command.

CAUTION
A Flash word or phrase must be in the erased state before being
programmed. Cumulative programming of bits within a Flash word or
phrase is not allowed.

The RAM and Flash memory may be read as bytes, aligned words, or misaligned words. Read access time
is one bus cycle for bytes and aligned words, and two bus cycles for misaligned words. For Flash memory,
an erased bit reads 1 and a programmed bit reads 0.

It is not possible to read from a Flash block while any command is executing on that specific Flash block.
It is possible to read from a Flash block while a command is executing on a different Flash block.

Both P-Flash and D-Flash memories are implemented with Error Correction Codes (ECC) that can resolve
single bit faults and detect double bit faults. For P-Flash memory, the ECC implementation requires that
programming be done on an aligned 8 byte basis (a Flash phrase). Since P-Flash memory is always read
by phrase, only one single bit fault in the phrase containing the byte or word accessed will be corrected.

29.1.1 Glossary
Buffer RAM — The buffer RAM constitutes the volatile memory store required for EEE. Memory space
in the buffer RAM not required for EEE can be partitioned to provide volatile memory space for
applications.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1140



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Command Write Sequence — An MCU instruction sequence to execute built-in algorithms (including
program and erase) on the Flash memory.

D-Flash Memory — The D-Flash memory constitutes the nonvolatile memory store required for EEE.
Memory space in the D-Flash memory not required for EEE can be partitioned to provide nonvolatile
memory space for applications.

D-Flash Sector — The D-Flash sector is the smallest portion of the D-Flash memory that can be erased.
The D-Flash sector consists of four 64 byte rows for a total of 256 bytes.

EEE (Emulated EEPROM) — A method to emulate the small sector size features and endurance
characteristics associated with an EEPROM.

EEE IFR — Nonvolatile information register located in the D-Flash block that contains data required to
partition the D-Flash memory and buffer RAM for EEE. The EEE IFR is visible in the global memory map
by setting the EEEIFRON bit in the MMCCTL1 register.

NVM Command Mode — An NVM mode using the CPU to setup the FCCOB register to pass parameters
required for Flash command execution.

Phrase — An aligned group of four 16-bit words within the P-Flash memory. Each phrase includes eight
ECC bits for single bit fault correction and double bit fault detection within the phrase.

P-Flash Memory — The P-Flash memory constitutes the main nonvolatile memory store for applications.

P-Flash Sector — The P-Flash sector is the smallest portion of the P-Flash memory that can be erased.
Each P-Flash sector contains 1024 bytes.

Program IFR — Nonvolatile information register located in the P-Flash block that contains the Device
ID, Version ID, and the Program Once field. The Program IFR is visible in the global memory map by
setting the PGMIFRON bit in the MMCCTL1 register.

29.1.2 Features

******** P-Flash Features
• 1024 Kbytes of P-Flash memory composed of three 256 Kbyte Flash blocks and two 128 Kbyte

Flash blocks. The 256 Kbyte Flash block consists of two 128 Kbyte sections each divided into
128 sectors of 1024 bytes. The 128 Kbyte Flash blocks are each divided into 128 sectors of 1024
bytes.

• Single bit fault correction and double bit fault detection within a 64-bit phrase during read
operations

• Automated program and erase algorithm with verify and generation of ECC parity bits
• Fast sector erase and phrase program operation
• Ability to program up to one phrase in each P-Flash block simultaneously
• Flexible protection scheme to prevent accidental program or erase of P-Flash memory

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1141



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

******** D-Flash Features
• Up to 32 Kbytes of D-Flash memory with 256 byte sectors for user access
• Dedicated commands to control access to the D-Flash memory over EEE operation
• Single bit fault correction and double bit fault detection within a word during read operations
• Automated program and erase algorithm with verify and generation of ECC parity bits
• Fast sector erase and word program operation
• Ability to program up to four words in a burst sequence

29.1.2.3 Emulated EEPROM Features
• Up to 4 Kbytes of emulated EEPROM (EEE) accessible as 4 Kbytes of RAM
• Flexible protection scheme to prevent accidental program or erase of data
• Automatic EEE file handling using an internal Memory Controller
• Automatic transfer of valid EEE data from D-Flash memory to buffer RAM on reset
• Ability to monitor the number of outstanding EEE related buffer RAM words left to be

programmed into D-Flash memory
• Ability to disable EEE operation and allow priority access to the D-Flash memory
• Ability to cancel all pending EEE operations and allow priority access to the D-Flash memory

29.1.2.4 User Buffer RAM Features
• Up to 4 Kbytes of RAM for user access

29.1.2.5 Other Flash Module Features
• No external high-voltage power supply required for Flash memory program and erase operations
• Interrupt generation on Flash command completion and Flash error detection
• Security mechanism to prevent unauthorized access to the Flash memory

29.1.3 Block Diagram
The block diagram of the Flash module is shown in Figure 29-1.

MC9S12XE-Family Reference Manual  Rev. 1.25

1142 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

P-Flash Block 0
32Kx72

16bit 16Kx72 16Kx72
internal sector 0 sector 0

Flash bus sector 1 sector 1
Interface

Command
Interrupt Registers sector 127 sector 127
Request P-Flash P-Flash

Block 1S Block 1N
Error

Protection 16Kx72 16Kx72
Interrupt sector 0 sector 0
Request sector 1 sector 1

Security sector 127 sector 127

P-Flash Block 2
Oscillator 32Kx72
Clock (XTAL) Clock 16Kx72 16Kx72

Divider FCLK sector 0 sector 0
XGATE sector 1 sector 1

Memory
Controller sector 127 sector 127

CPU P-Flash Block 3
D-Flash 32Kx72
16Kx22 16Kx72 16Kx72

Scratch RAM sector 0 sector 0 sector 0
512x16 sector 1 sector 1 sector 1

Buffer RAM sector 127 sector 127 sector 127
2Kx16

Tag RAM
128x16

Figure 29-1. FTM1024K5 Block Diagram

29.2 External Signal Description
The Flash module contains no signals that connect off-chip.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1143



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

29.3 Memory Map and Registers
This section describes the memory map and registers for the Flash module. Read data from unimplemented
memory space in the Flash module is undefined. Write access to unimplemented or reserved memory space
in the Flash module will be ignored by the Flash module.

29.3.1 Module Memory Map
The S12X architecture places the P-Flash memory between global addresses 0x70_0000 and 0x7F_FFFF
as shown in Table 29-2. The P-Flash memory map is shown in Figure 29-2.

Table 29-2. P-Flash Memory Addressing

Size
Global Address Description

(Bytes)

P-Flash Block 0
0x7C_0000 – 0x7F_FFFF 256 K Contains Flash Configuration Field

(see Table 29-3)

0x7A_0000 – 0x7B_FFFF 128 K P-Flash Block 1N

0x78_0000 – 0x79_FFFF 128 K P-Flash Block 1S

0x74_0000 – 0x77_FFFF 256 K P-Flash Block 2

0x70_0000 – 0x73_FFFF 256 K P-Flash Block 3

The FPROT register, described in Section ********, can be set to protect regions in the Flash memory from
accidental program or erase. Three separate memory regions, one growing upward from global address
0x7F_8000 in the Flash memory (called the lower region), one growing downward from global address
0x7F_FFFF in the Flash memory (called the higher region), and the remaining addresses in the Flash
memory, can be activated for protection. The Flash memory addresses covered by these protectable regions
are shown in the P-Flash memory map. The higher address region is mainly targeted to hold the boot loader
code since it covers the vector space. Default protection settings as well as security information that allows
the MCU to restrict access to the Flash module are stored in the Flash configuration field as described in
Table 29-3.

Table 29-3. Flash Configuration Field(1)

Size
Global Address Description

(Bytes)

Backdoor Comparison Key
0x7F_FF00 – 0x7F_FF07 8 Refer to Section ********2, “Verify Backdoor Access Key Command,” and

Section 29.5.1, “Unsecuring the MCU using Backdoor Key Access”

0x7F_FF08 – Reserved
0x7F_FF0B(2) 4

P-Flash Protection byte.
0x7F_FF0C2 1

Refer to Section ********, “P-Flash Protection Register (FPROT)”

EEE Protection byte
0x7F_FF0D2 1

Refer to Section *********, “EEE Protection Register (EPROT)”

MC9S12XE-Family Reference Manual  Rev. 1.25

1144 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-3. Flash Configuration Field(1)

Size
Global Address Description

(Bytes)

Flash Nonvolatile byte
0x7F_FF0E2 1

Refer to Section *********, “Flash Option Register (FOPT)”

Flash Security byte
0x7F_FF0F2 1

Refer to Section ********, “Flash Security Register (FSEC)”
1. Older versions may have swapped protection byte addresses
2. 0x7FF08 - 0x7F_FF0F form a Flash phrase and must be programmed in a single command write sequence. Each byte in

the 0x7F_FF08 - 0x7F_FF0B reserved field should be programmed to 0xFF.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1145



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

P-Flash START = 0x70_0000

Flash Protected/Unprotected Region
992 Kbytes

0x7F_8000
0x7F_8400
0x7F_8800
0x7F_9000 Flash Protected/Unprotected Lower Region

1, 2, 4, 8 Kbytes

0x7F_A000

Flash Protected/Unprotected Region
8 Kbytes (up to 29 Kbytes)

0x7F_C000

0x7F_E000 Flash Protected/Unprotected Higher Region
2, 4, 8, 16 Kbytes

0x7F_F000

0x7F_F800
Flash Configuration Field

P-Flash END = 0x7F_FFFF 16 bytes (0x7F_FF00 - 0x7F_FF0F)

Figure 29-2. P-Flash Memory Map

MC9S12XE-Family Reference Manual  Rev. 1.25

1146 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-4. Program IFR Fields

Global Address Size
Field Description

(PGMIFRON) (Bytes)

0x40_0000 – 0x40_0007 8 Device ID

0x40_0008 – 0x40_00E7 224 Reserved

0x40_00E8 – 0x40_00E9 2 Version ID

0x40_00EA – 0x40_00FF 22 Reserved

Program Once Field
0x40_0100 – 0x40_013F 64

Refer to Section ********, “Program Once Command”

0x40_0140 – 0x40_01FF 192 Reserved

Table 29-5. P-Flash IFR Accessibility

Global Address Size
Accessed From

(PGMIFRON) (Bytes)

0x40_0000 – 0x40_01FF 512 XBUS0 (PBLK0S)(1)

0x40_0200 – 0x40_03FF 512 Unimplemented

0x40_0400 – 0x40_05FF 512 XBUS0 (PBLK1N)

0x40_0600 – 0x40_07FF 512 XBUS1 (PBLK1S)

0x40_0800 – 0x40_09FF 512 XBUS0 (PBLK2S)

0x40_0A00 – 0x40_0BFF 512 Unimplemented

0x40_0C00 – 0x40_0DFF 512 XBUS0 (PBLK3S)

0x40_0E00 – 0x40_0FFF 512 Unimplemented
1. Refer to Table 29-4 for more details.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1147



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-6. EEE Resource Fields

Size
Global Address Description

(Bytes)

0x10_0000 – 0x10_7FFF 32,768 D-Flash Memory (User and EEE)

0x10_8000 – 0x11_FFFF 98,304 Reserved

0x12_0000 – 0x12_007F 128 EEE Nonvolatile Information Register (EEEIFRON(1) = 1)

0x12_0080 – 0x12_0FFF 3,968 Reserved

0x12_1000 – 0x12_1EFF 3,840 Reserved

0x12_1F00 – 0x12_1FFF 256 EEE Tag RAM (TMGRAMON1 = 1)

0x12_2000 – 0x12_3BFF 7,168 Reserved

0x12_3C00 – 0x12_3FFF 1,024 Memory Controller Scratch RAM (TMGRAMON1 = 1)

0x12_4000 – 0x12_DFFF 40,960 Reserved

0x12_E000 – 0x12_FFFF 8,192 Reserved

0x13_0000 – 0x13_EFFF 61,440 Reserved

0x13_F000 – 0x13_FFFF 4,096 Buffer RAM (User and EEE)
1. MMCCTL1 register bit

MC9S12XE-Family Reference Manual  Rev. 1.25

1148 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

D-Flash START = 0x10_0000

D-Flash User Partition

D-Flash Memory
32 Kbytes

D-Flash EEE Partition

D-Flash END = 0x10_7FFF

0x12_0000
EEE Nonvolatile Information Register (EEEIFRON)

0x12_1000 128 bytes

EEE Tag RAM (TMGRAMON)
0x12_2000 256 bytes

Memory Controller Scratch RAM (TMGRAMON)
0x12_4000 1024 bytes

0x12_E000

0x12_FFFF

Buffer RAM START = 0x13_F000

Buffer RAM User Partition

0x13_FE00 Buffer RAM
0x13_FE40 4 Kbytes

0x13_FE80 Buffer RAM EEE Partition
0x13_FEC0
0x13_FF00 Protectable Region (EEE only)

64, 128, 192, 256, 320, 384, 448, 512 bytes
0x13_FF40
0x13_FF80
0x13_FFC0

Buffer RAM END = 0x13_FFFF

Figure 29-3. EEE Resource Memory Map

The Full Partition D-Flash command (see Section ********5) is used to program the EEE nonvolatile
information register fields where address 0x12_0000 defines the D-Flash partition for user access and
address 0x12_0004 defines the buffer RAM partition for EEE operations.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1149



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-7. EEE Nonvolatile Information Register Fields

Global Address Size
Description

(EEEIFRON) (Bytes)

D-Flash User Partition (DFPART)
0x12_0000 – 0x12_0001 2

Refer to Section ********5, “Full Partition D-Flash Command”

0x12_0002 – 0x12_0003 2 D-Flash User Partition (duplicate(1))

Buffer RAM EEE Partition (ERPART)
0x12_0004 – 0x12_0005 2

Refer to Section ********5, “Full Partition D-Flash Command”

0x12_0006 – 0x12_0007 2 Buffer RAM EEE Partition (duplicate1)

0x12_0008 – 0x12_007F 120 Reserved
1. Duplicate value used if primary value generates a double bit fault when read during the reset sequence.

29.3.2 Register Descriptions
The Flash module contains a set of 20 control and status registers located between Flash module base +
0x0000 and 0x0013. A summary of the Flash module registers is given in Figure 29-4 with detailed
descriptions in the following subsections.

CAUTION
Writes to any Flash register must be avoided while a Flash command is
active (CCIF=0) to prevent corruption of Flash register contents and
Memory Controller behavior.

Address
7 6 5 4 3 2 1 0

& Name

0x0000 R FDIVLD
FDIV6 FDIV5 FDIV4 FDIV3 FDIV2 FDIV1 FDIV0

FCLKDIV W

0x0001 R KEYEN1 KEYEN0 RNV5 RNV4 RNV3 RNV2 SEC1 SEC0
FSEC W

0x0002 R 0 0 0 0 0
CCOBIX2 CCOBIX1 CCOBIX0

FCCOBIX W

0x0003 R 0 0 0 0 0
ECCRIX2 ECCRIX1 ECCRIX0

FECCRIX W

0x0004 R 0 0 0 0
CCIE IGNSF FDFD FSFD

FCNFG W

0x0005 R 0
ERSERIE PGMERIE EPVIOLIE ERSVIE1 ERSVIE0 DFDIE SFDIE

FERCNFG W

Figure 29-4. FTM1024K5 Register Summary

MC9S12XE-Family Reference Manual  Rev. 1.25

1150 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Address
7 6 5 4 3 2 1 0

& Name

0x0006 R 0 MGBUSY RSVD MGSTAT1 MGSTAT0
CCIF ACCERR FPVIOL

FSTAT W

0x0007 R 0
ERSERIF PGMERIF EPVIOLIF ERSVIF1 ERSVIF0 DFDIF SFDIF

FERSTAT W

0x0008 R RNV6
FPOPEN FPHDIS FPHS1 FPHS0 FPLDIS FPLS1 FPLS0

FPROT W

0x0009 R RNV6 RNV5 RNV4
EPOPEN EPDIS EPS2 EPS1 EPS0

EPROT W

0x000A R
CCOB15 CCOB14 CCOB13 CCOB12 CCOB11 CCOB10 CCOB9 CCOB8

FCCOBHI W

0x000B R
CCOB7 CCOB6 CCOB5 CCOB4 CCOB3 CCOB2 CCOB1 CCOB0

FCCOBLO W

0x000C R ETAG15 ETAG14 ETAG13 ETAG12 ETAG11 ETAG10 ETAG9 ETAG8
ETAGHI W

0x000D R ETAG7 ETAG6 ETAG5 ETAG4 ETAG3 ETAG2 ETAG1 ETAG0
ETAGLO W

0x000E R ECCR15 ECCR14 ECCR13 ECCR12 ECCR11 ECCR10 ECCR9 ECCR8
FECCRHI W

0x000F R ECCR7 ECCR6 ECCR5 ECCR4 ECCR3 ECCR2 ECCR1 ECCR0
FECCRLO W

0x0010 R NV7 NV6 NV5 NV4 NV3 NV2 NV1 NV0
FOPT W

0x0011 R 0 0 0 0 0 0 0 0
FRSV0 W

0x0012 R 0 0 0 0 0 0 0 0
FRSV1 W

0x0013 R 0 0 0 0 0 0 0 0
FRSV2 W

Figure 29-4. FTM1024K5 Register Summary (continued)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1151



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Address
7 6 5 4 3 2 1 0

& Name

= Unimplemented or Reserved

Figure 29-4. FTM1024K5 Register Summary (continued)

******** Flash Clock Divider Register (FCLKDIV)
The FCLKDIV register is used to control timed events in program and erase algorithms.

Offset Module Base + 0x0000

7 6 5 4 3 2 1 0

R FDIVLD
FDIV[6:0]

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 29-5. Flash Clock Divider Register (FCLKDIV)

All bits in the FCLKDIV register are readable, bits 6–0 are write once and bit 7 is not writable.
Table 29-8. FCLKDIV Field Descriptions

Field Description

7 Clock Divider Loaded
FDIVLD 0 FCLKDIV register has not been written

1 FCLKDIV register has been written since the last reset

6–0 Clock Divider Bits — FDIV[6:0] must be set to effectively divide OSCCLK down to generate an internal Flash
FDIV[6:0] clock, FCLK, with a target frequency of 1 MHz for use by the Flash module to control timed events during program

and erase algorithms. Table 29-9 shows recommended values for FDIV[6:0] based on OSCCLK frequency.
Please refer to Section 29.4.1, “Flash Command Operations,” for more information.

CAUTION
The FCLKDIV register should never be written while a Flash command is
executing (CCIF=0). The FCLKDIV register is writable during the Flash
reset sequence even though CCIF is clear.

MC9S12XE-Family Reference Manual  Rev. 1.25

1152 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-9. FDIV vs OSCCLK Frequency

OSCCLK Frequency OSCCLK Frequency OSCCLK Frequency
(MHz) FDIV[6:0] (MHz) FDIV[6:0] (MHz) FDIV[6:0]

MIN(1) MAX(2) MIN1 MAX2 MIN1 MAX2

33.60 34.65 0x20 67.20 68.25 0x40

1.60 2.10 0x01 34.65 35.70 0x21 68.25 69.30 0x41

2.40 3.15 0x02 35.70 36.75 0x22 69.30 70.35 0x42

3.20 4.20 0x03 36.75 37.80 0x23 70.35 71.40 0x43

4.20 5.25 0x04 37.80 38.85 0x24 71.40 72.45 0x44

5.25 6.30 0x05 38.85 39.90 0x25 72.45 73.50 0x45

6.30 7.35 0x06 39.90 40.95 0x26 73.50 74.55 0x46

7.35 8.40 0x07 40.95 42.00 0x27 74.55 75.60 0x47

8.40 9.45 0x08 42.00 43.05 0x28 75.60 76.65 0x48

9.45 10.50 0x09 43.05 44.10 0x29 76.65 77.70 0x49

10.50 11.55 0x0A 44.10 45.15 0x2A 77.70 78.75 0x4A

11.55 12.60 0x0B 45.15 46.20 0x2B 78.75 79.80 0x4B

12.60 13.65 0x0C 46.20 47.25 0x2C 79.80 80.85 0x4C

13.65 14.70 0x0D 47.25 48.30 0x2D 80.85 81.90 0x4D

14.70 15.75 0x0E 48.30 49.35 0x2E 81.90 82.95 0x4E

15.75 16.80 0x0F 49.35 50.40 0x2F 82.95 84.00 0x4F

16.80 17.85 0x10 50.40 51.45 0x30 84.00 85.05 0x50

17.85 18.90 0x11 51.45 52.50 0x31 85.05 86.10 0x51

18.90 19.95 0x12 52.50 53.55 0x32 86.10 87.15 0x52

19.95 21.00 0x13 53.55 54.60 0x33 87.15 88.20 0x53

21.00 22.05 0x14 54.60 55.65 0x34 88.20 89.25 0x54

22.05 23.10 0x15 55.65 56.70 0x35 89.25 90.30 0x55

23.10 24.15 0x16 56.70 57.75 0x36 90.30 91.35 0x56

24.15 25.20 0x17 57.75 58.80 0x37 91.35 92.40 0x57

25.20 26.25 0x18 58.80 59.85 0x38 92.40 93.45 0x58

26.25 27.30 0x19 59.85 60.90 0x39 93.45 94.50 0x59

27.30 28.35 0x1A 60.90 61.95 0x3A 94.50 95.55 0x5A

28.35 29.40 0x1B 61.95 63.00 0x3B 95.55 96.60 0x5B

29.40 30.45 0x1C 63.00 64.05 0x3C 96.60 97.65 0x5C

30.45 31.50 0x1D 64.05 65.10 0x3D 97.65 98.70 0x5D

31.50 32.55 0x1E 65.10 66.15 0x3E 98.70 99.75 0x5E

32.55 33.60 0x1F 66.15 67.20 0x3F 99.75 100.80 0x5F
1. FDIV shown generates an FCLK frequency of >0.8 MHz

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1153



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

2. FDIV shown generates an FCLK frequency of 1.05 MHz

******** Flash Security Register (FSEC)
The FSEC register holds all bits associated with the security of the MCU and Flash module.

Offset Module Base + 0x0001

7 6 5 4 3 2 1 0

R KEYEN[1:0] RNV[5:2] SEC[1:0]

W

Reset F F F F F F F F

= Unimplemented or Reserved

Figure 29-6. Flash Security Register (FSEC)

All bits in the FSEC register are readable but not writable.

During the reset sequence, the FSEC register is loaded with the contents of the Flash security byte in the
Flash configuration field at global address 0x7F_FF0F located in P-Flash memory (see Table 29-3) as
indicated by reset condition F in Figure 29-6. If a double bit fault is detected while reading the P-Flash
phrase containing the Flash security byte during the reset sequence, all bits in the FSEC register will be
set to leave the Flash module in a secured state with backdoor key access disabled.

Table 29-10. FSEC Field Descriptions

Field Description

7–6 Backdoor Key Security Enable Bits — The KEYEN[1:0] bits define the enabling of backdoor key access to the
KEYEN[1:0] Flash module as shown in Table 29-11.

5–2 Reserved Nonvolatile Bits — The RNV bits should remain in the erased state for future enhancements.
RNV[5:2}

1–0 Flash Security Bits — The SEC[1:0] bits define the security state of the MCU as shown in Table 29-12. If the
SEC[1:0] Flash module is unsecured using backdoor key access, the SEC bits are forced to 10.

Table 29-11. Flash KEYEN States

KEYEN[1:0] Status of Backdoor Key Access

00 DISABLED
01 DISABLED(1)

10 ENABLED
11 DISABLED

1. Preferred KEYEN state to disable backdoor key access.

MC9S12XE-Family Reference Manual  Rev. 1.25

1154 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-12. Flash Security States

SEC[1:0] Status of Security

00 SECURED
01 SECURED(1)

10 UNSECURED
11 SECURED

1. Preferred SEC state to set MCU to secured state.

The security function in the Flash module is described in Section 29.5.

******** Flash CCOB Index Register (FCCOBIX)
The FCCOBIX register is used to index the FCCOB register for Flash memory operations.

Offset Module Base + 0x0002

7 6 5 4 3 2 1 0

R 0 0 0 0 0
CCOBIX[2:0]

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 29-7. FCCOB Index Register (FCCOBIX)

CCOBIX bits are readable and writable while remaining bits read 0 and are not writable.
Table 29-13. FCCOBIX Field Descriptions

Field Description

2–0 Common Command Register Index— The CCOBIX bits are used to select which word of the FCCOB register
CCOBIX[1:0] array is being read or written to. See Section *********, “Flash Common Command Object Register (FCCOB),”

for more details.

******** Flash ECCR Index Register (FECCRIX)
The FECCRIX register is used to index the FECCR register for ECC fault reporting.

Offset Module Base + 0x0003

7 6 5 4 3 2 1 0

R 0 0 0 0 0
ECCRIX[2:0]

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 29-8. FECCR Index Register (FECCRIX)

ECCRIX bits are readable and writable while remaining bits read 0 and are not writable.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1155



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-14. FECCRIX Field Descriptions

Field Description

2-0 ECC Error Register Index— The ECCRIX bits are used to select which word of the FECCR register array is
ECCRIX[2:0] being read. See Section *********, “Flash ECC Error Results Register (FECCR),” for more details.

******** Flash Configuration Register (FCNFG)
The FCNFG register enables the Flash command complete interrupt and forces ECC faults on Flash array
read access from the CPU or XGATE.

Offset Module Base + 0x0004

7 6 5 4 3 2 1 0

R 0 0 0 0
CCIE IGNSF FDFD FSFD

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 29-9. Flash Configuration Register (FCNFG)

CCIE, IGNSF, FDFD, and FSFD bits are readable and writable while remaining bits read 0 and are not
writable.

Table 29-15. FCNFG Field Descriptions

Field Description

7 Command Complete Interrupt Enable — The CCIE bit controls interrupt generation when a Flash command
CCIE has completed.

0 Command complete interrupt disabled
1 An interrupt will be requested whenever the CCIF flag in the FSTAT register is set (see Section ********)

4 Ignore Single Bit Fault — The IGNSF controls single bit fault reporting in the FERSTAT register (see
IGNSF Section ********).

0 All single bit faults detected during array reads are reported
1 Single bit faults detected during array reads are not reported and the single bit fault interrupt will not be

generated

MC9S12XE-Family Reference Manual  Rev. 1.25

1156 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-15. FCNFG Field Descriptions (continued)

Field Description

1 Force Double Bit Fault Detect — The FDFD bit allows the user to simulate a double bit fault during Flash array
FDFD read operations and check the associated interrupt routine. The FDFD bit is cleared by writing a 0 to FDFD. The

FECCR registers will not be updated during the Flash array read operation with FDFD set unless an actual
double bit fault is detected.
0 Flash array read operations will set the DFDIF flag in the FERSTAT register only if a double bit fault is detected
1 Any Flash array read operation will force the DFDIF flag in the FERSTAT register to be set (see

Section ********) and an interrupt will be generated as long as the DFDIE interrupt enable in the FERCNFG
register is set (see Section ********)

0 Force Single Bit Fault Detect — The FSFD bit allows the user to simulate a single bit fault during Flash array
FSFD read operations and check the associated interrupt routine. The FSFD bit is cleared by writing a 0 to FSFD. The

FECCR registers will not be updated during the Flash array read operation with FSFD set unless an actual single
bit fault is detected.
0 Flash array read operations will set the SFDIF flag in the FERSTAT register only if a single bit fault is detected
1 Flash array read operation will force the SFDIF flag in the FERSTAT register to be set (see Section ********)

and an interrupt will be generated as long as the SFDIE interrupt enable in the FERCNFG register is set (see
Section ********)

******** Flash Error Configuration Register (FERCNFG)
The FERCNFG register enables the Flash error interrupts for the FERSTAT flags.

Offset Module Base + 0x0005

7 6 5 4 3 2 1 0

R 0
ERSERIE PGMERIE EPVIOLIE ERSVIE1 ERSVIE0 DFDIE SFDIE

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 29-10. Flash Error Configuration Register (FERCNFG)

All assigned bits in the FERCNFG register are readable and writable.
Table 29-16. FERCNFG Field Descriptions

Field Description

7 EEE Erase Error Interrupt Enable — The ERSERIE bit controls interrupt generation when a failure is detected
ERSERIE during an EEE erase operation.

0 ERSERIF interrupt disabled
1 An interrupt will be requested whenever the ERSERIF flag is set (see Section ********)

6 EEE Program Error Interrupt Enable — The PGMERIE bit controls interrupt generation when a failure is
PGMERIE detected during an EEE program operation.

0 PGMERIF interrupt disabled
1 An interrupt will be requested whenever the PGMERIF flag is set (see Section ********)

4 EEE Protection Violation Interrupt Enable — The EPVIOLIE bit controls interrupt generation when a
EPVIOLIE protection violation is detected during a write to the buffer RAM EEE partition.

0 EPVIOLIF interrupt disabled
1 An interrupt will be requested whenever the EPVIOLIF flag is set (see Section ********)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1157



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-16. FERCNFG Field Descriptions (continued)

Field Description

3 EEE Error Type 1 Interrupt Enable — The ERSVIE1 bit controls interrupt generation when a change state error
ERSVIE1 is detected during an EEE operation.

0 ERSVIF1 interrupt disabled
1 An interrupt will be requested whenever the ERSVIF1 flag is set (see Section ********)

2 EEE Error Type 0 Interrupt Enable — The ERSVIE0 bit controls interrupt generation when a sector format error
ERSVIE0 is detected during an EEE operation.

0 ERSVIF0 interrupt disabled
1 An interrupt will be requested whenever the ERSVIF0 flag is set (see Section ********)

1 Double Bit Fault Detect Interrupt Enable — The DFDIE bit controls interrupt generation when a double bit fault
DFDIE is detected during a Flash block read operation.

0 DFDIF interrupt disabled
1 An interrupt will be requested whenever the DFDIF flag is set (see Section ********)

0 Single Bit Fault Detect Interrupt Enable — The SFDIE bit controls interrupt generation when a single bit fault
SFDIE is detected during a Flash block read operation.

0 SFDIF interrupt disabled whenever the SFDIF flag is set (see Section ********)
1 An interrupt will be requested whenever the SFDIF flag is set (see Section ********)

******** Flash Status Register (FSTAT)
The FSTAT register reports the operational status of the Flash module.

Offset Module Base + 0x0006

7 6 5 4 3 2 1 0

R 0 MGBUSY RSVD MGSTAT[1:0]
CCIF ACCERR FPVIOL

W

Reset 1 0 0 0 0 0 0(1) 01

= Unimplemented or Reserved

Figure 29-11. Flash Status Register (FSTAT)
1. Reset value can deviate from the value shown if a double bit fault is detected during the reset sequence (see Section 29.6).

CCIF, ACCERR, and FPVIOL bits are readable and writable, MGBUSY and MGSTAT bits are readable
but not writable, while remaining bits read 0 and are not writable.

MC9S12XE-Family Reference Manual  Rev. 1.25

1158 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-17. FSTAT Field Descriptions

Field Description

7 Command Complete Interrupt Flag — The CCIF flag indicates that a Flash command has completed. The
CCIF CCIF flag is cleared by writing a 1 to CCIF to launch a command and CCIF will stay low until command

completion or command violation.
0 Flash command in progress
1 Flash command has completed

5 Flash Access Error Flag — The ACCERR bit indicates an illegal access has occurred to the Flash memory
ACCERR caused by either a violation of the command write sequence (see Section ********) or issuing an illegal Flash

command or when errors are encountered while initializing the EEE buffer ram during the reset sequence.
While ACCERR is set, the CCIF flag cannot be cleared to launch a command. The ACCERR bit is cleared by
writing a 1 to ACCERR. Writing a 0 to the ACCERR bit has no effect on ACCERR.
0 No access error detected
1 Access error detected

4 Flash Protection Violation Flag —The FPVIOL bit indicates an attempt was made to program or erase an
FPVIOL address in a protected area of P-Flash memory during a command write sequence. The FPVIOL bit is cleared

by writing a 1 to FPVIOL. Writing a 0 to the FPVIOL bit has no effect on FPVIOL. While FPVIOL is set, it is not
possible to launch a command or start a command write sequence.
0 No protection violation detected
1 Protection violation detected

3 Memory Controller Busy Flag — The MGBUSY flag reflects the active state of the Memory Controller.
MGBUSY 0 Memory Controller is idle

1 Memory Controller is busy executing a Flash command (CCIF = 0) or is handling internal EEE operations

2 Reserved Bit — This bit is reserved and always reads 0.
RSVD

1–0 Memory Controller Command Completion Status Flag — One or more MGSTAT flag bits are set if an error
MGSTAT[1:0] is detected during execution of a Flash command or during the Flash reset sequence. See Section 29.4.2,

“Flash Command Description,” and Section 29.6, “Initialization” for details.

******** Flash Error Status Register (FERSTAT)
The FERSTAT register reflects the error status of internal Flash operations.

Offset Module Base + 0x0007

7 6 5 4 3 2 1 0

R 0
ERSERIF PGMERIF EPVIOLIF ERSVIF1 ERSVIF0 DFDIF SFDIF

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 29-12. Flash Error Status Register (FERSTAT)

All flags in the FERSTAT register are readable and only writable to clear the flag.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1159



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-18. FERSTAT Field Descriptions

Field Description

7 EEE Erase Error Interrupt Flag — The setting of the ERSERIF flag occurs due to an error in a Flash erase
ERSERIF command that resulted in the erase operation not being successful during EEE operations. The ERSERIF flag

is cleared by writing a 1 to ERSERIF. Writing a 0 to the ERSERIF flag has no effect on ERSERIF. While
ERSERIF is set, it is possible to write to the buffer RAM EEE partition but the data written will not be transferred
to the D-Flash EEE partition.
0 Erase command successfully completed on the D-Flash EEE partition
1 Erase command failed on the D-Flash EEE partition

6 EEE Program Error Interrupt Flag — The setting of the PGMERIF flag occurs due to an error in a Flash
PGMERIF program command that resulted in the program operation not being successful during EEE operations. The

PGMERIF flag is cleared by writing a 1 to PGMERIF. Writing a 0 to the PGMERIF flag has no effect on
PGMERIF. While PGMERIF is set, it is possible to write to the buffer RAM EEE partition but the data written will
not be transferred to the D-Flash EEE partition.
0 Program command successfully completed on the D-Flash EEE partition
1 Program command failed on the D-Flash EEE partition

4 EEE Protection Violation Interrupt Flag —The setting of the EPVIOLIF flag indicates an attempt was made to
EPVIOLIF write to a protected area of the buffer RAM EEE partition. The EPVIOLIF flag is cleared by writing a 1 to

EPVIOLIF. Writing a 0 to the EPVIOLIF flag has no effect on EPVIOLIF. While EPVIOLIF is set, it is possible to
write to the buffer RAM EEE partition as long as the address written to is not in a protected area.
0 No EEE protection violation
1 EEE protection violation detected

3 EEE Error Interrupt 1 Flag —The setting of the ERSVIF1 flag indicates that the memory controller was unable
ERSVIF1 to change the state of a D-Flash EEE sector. The ERSVIF1 flag is cleared by writing a 1 to ERSVIF1. Writing a

0 to the ERSVIF1 flag has no effect on ERSVIF1. While ERSVIF1 is set, it is possible to write to the buffer RAM
EEE partition but the data written will not be transferred to the D-Flash EEE partition.
0 No EEE sector state change error detected
1 EEE sector state change error detected

2 EEE Error Interrupt 0 Flag —The setting of the ERSVIF0 flag indicates that the memory controller was unable
ERSVIF0 to format a D-Flash EEE sector for EEE use. The ERSVIF0 flag is cleared by writing a 1 to ERSVIF0. Writing a

0 to the ERSVIF0 flag has no effect on ERSVIF0. While ERSVIF0 is set, it is possible to write to the buffer RAM
EEE partition but the data written will not be transferred to the D-Flash EEE partition.
0 No EEE sector format error detected
1 EEE sector format error detected

1 Double Bit Fault Detect Interrupt Flag — The setting of the DFDIF flag indicates that a double bit fault was
DFDIF detected in the stored parity and data bits during a Flash array read operation or that a Flash array read operation

was attempted on a Flash block that was under a Flash command operation. The DFDIF flag is cleared by writing
a 1 to DFDIF. Writing a 0 to DFDIF has no effect on DFDIF.
0 No double bit fault detected
1 Double bit fault detected or an invalid Flash array read operation attempted

0 Single Bit Fault Detect Interrupt Flag — With the IGNSF bit in the FCNFG register clear, the SFDIF flag
SFDIF indicates that a single bit fault was detected in the stored parity and data bits during a Flash array read operation

or that a Flash array read operation was attempted on a Flash block that was under a Flash command operation.
The SFDIF flag is cleared by writing a 1 to SFDIF. Writing a 0 to SFDIF has no effect on SFDIF.
0 No single bit fault detected
1 Single bit fault detected and corrected or an invalid Flash array read operation attempted

******** P-Flash Protection Register (FPROT)
The FPROT register defines which P-Flash sectors are protected against program and erase operations.

MC9S12XE-Family Reference Manual  Rev. 1.25

1160 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Offset Module Base + 0x0008

7 6 5 4 3 2 1 0

R RNV6
FPOPEN FPHDIS FPHS[1:0] FPLDIS FPLS[1:0]

W

Reset F F F F F F F F

= Unimplemented or Reserved

Figure 29-13. Flash Protection Register (FPROT)

The (unreserved) bits of the FPROT register are writable with the restriction that the size of the protected
region can only be increased (see Section ********.1, “P-Flash Protection Restrictions,” and Table 29-23).

During the reset sequence, the FPROT register is loaded with the contents of the P-Flash protection byte
in the Flash configuration field at global address 0x7F_FF0C located in P-Flash memory (see Table 29-3)
as indicated by reset condition ‘F’ in Figure 29-13. To change the P-Flash protection that will be loaded
during the reset sequence, the upper sector of the P-Flash memory must be unprotected, then the P-Flash
protection byte must be reprogrammed. If a double bit fault is detected while reading the P-Flash phrase
containing the P-Flash protection byte during the reset sequence, the FPOPEN bit will be cleared and
remaining bits in the FPROT register will be set to leave the P-Flash memory fully protected.

Trying to alter data in any protected area in the P-Flash memory will result in a protection violation error
and the FPVIOL bit will be set in the FSTAT register. The block erase of a P-Flash block is not possible
if any of the P-Flash sectors contained in the same P-Flash block are protected.

Table 29-19. FPROT Field Descriptions

Field Description

7 Flash Protection Operation Enable — The FPOPEN bit determines the protection function for program or
FPOPEN erase operations as shown in Table 29-20 for the P-Flash block.

0 When FPOPEN is clear, the FPHDIS and FPLDIS bits define unprotected address ranges as specified by the
corresponding FPHS and FPLS bits

1 When FPOPEN is set, the FPHDIS and FPLDIS bits enable protection for the address range specified by the
corresponding FPHS and FPLS bits

6 Reserved Nonvolatile Bit — The RNV bit should remain in the erased state for future enhancements.
RNV[6]

5 Flash Protection Higher Address Range Disable — The FPHDIS bit determines whether there is a
FPHDIS protected/unprotected area in a specific region of the P-Flash memory ending with global address 0x7F_FFFF.

0 Protection/Unprotection enabled
1 Protection/Unprotection disabled

4–3 Flash Protection Higher Address Size — The FPHS bits determine the size of the protected/unprotected area
FPHS[1:0] in P-Flash memory as shown inTable 29-21. The FPHS bits can only be written to while the FPHDIS bit is set.

2 Flash Protection Lower Address Range Disable — The FPLDIS bit determines whether there is a
FPLDIS protected/unprotected area in a specific region of the P-Flash memory beginning with global address

0x7F_8000.
0 Protection/Unprotection enabled
1 Protection/Unprotection disabled

1–0 Flash Protection Lower Address Size — The FPLS bits determine the size of the protected/unprotected area
FPLS[1:0] in P-Flash memory as shown in Table 29-22. The FPLS bits can only be written to while the FPLDIS bit is set.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1161



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-20. P-Flash Protection Function

FPOPEN FPHDIS FPLDIS Function(1)

1 1 1 No P-Flash Protection
1 1 0 Protected Low Range
1 0 1 Protected High Range
1 0 0 Protected High and Low Ranges
0 1 1 Full P-Flash Memory Protected
0 1 0 Unprotected Low Range
0 0 1 Unprotected High Range
0 0 0 Unprotected High and Low Ranges

1. For range sizes, refer to Table 29-21 and Table 29-22.

Table 29-21. P-Flash Protection Higher Address Range

FPHS[1:0]  Global Address Range Protected Size

00 0x7F_F800–0x7F_FFFF 2 Kbytes
01 0x7F_F000–0x7F_FFFF 4 Kbytes
10 0x7F_E000–0x7F_FFFF 8 Kbytes
11 0x7F_C000–0x7F_FFFF 16 Kbytes

Table 29-22. P-Flash Protection Lower Address Range

FPLS[1:0] Global Address Range Protected Size

00 0x7F_8000–0x7F_83FF 1 Kbyte
01 0x7F_8000–0x7F_87FF 2 Kbytes
10 0x7F_8000–0x7F_8FFF 4 Kbytes
11 0x7F_8000–0x7F_9FFF 8 Kbytes

All possible P-Flash protection scenarios are shown in Figure 29-14. Although the protection scheme is
loaded from the Flash memory at global address 0x7F_FF0C during the reset sequence, it can be changed
by the user. The P-Flash protection scheme can be used by applications requiring reprogramming in single
chip mode while providing as much protection as possible if reprogramming is not required.

MC9S12XE-Family Reference Manual  Rev. 1.25

1162 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

FPHDIS = 1 FPHDIS = 1 FPHDIS = 0 FPHDIS = 0
FPLDIS = 1 FPLDIS = 0 FPLDIS = 1 FPLDIS = 0

Scenario 7 6 5 4
FLASH START

0x7F_8000

0x7F_FFFF
Scenario 3 2 1 0

FLASH START

0x7F_8000

0x7F_FFFF

Unprotected region Protected region with size
defined by FPLS

Protected region Protected region with size
not defined by FPLS, FPHS defined by FPHS

Figure 29-14. P-Flash Protection Scenarios

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1163

FPHS[1:0] FPLS[1:0] FPHS[1:0] FPLS[1:0]

FPOPEN = 0 FPOPEN = 1



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

********.1 P-Flash Protection Restrictions
The general guideline is that P-Flash protection can only be added and not removed. Table 29-23 specifies
all valid transitions between P-Flash protection scenarios. Any attempt to write an invalid scenario to the
FPROT register will be ignored. The contents of the FPROT register reflect the active protection scenario.
See the FPHS and FPLS bit descriptions for additional restrictions.

Table 29-23. P-Flash Protection Scenario Transitions

From To Protection Scenario(1)

Protection
Scenario 0 1 2 3 4 5 6 7

0 X X X X

1 X X

2 X X

3 X

4 X X

5 X X X X

6 X X X X

7 X X X X X X X X
1. Allowed transitions marked with X, see Figure 29-14 for a definition of the scenarios.

********* EEE Protection Register (EPROT)
The EPROT register defines which buffer RAM EEE partition areas are protected against writes.

Offset Module Base + 0x0009

7 6 5 4 3 2 1 0

R RNV[6:4]
EPOPEN EPDIS EPS[2:0]

W

Reset F F F F F F F F

= Unimplemented or Reserved

Figure 29-15. EEE Protection Register (EPROT)

All bits in the EPROT register are readable and writable except for RNV[6:4] which are only readable. The
EPOPEN and EPDIS bits can only be written to the protected state. The EPS bits can be written anytime
until the EPDIS bit is cleared. If the EPOPEN bit is cleared, the state of the EPDIS and EPS bits is
irrelevant.

During the reset sequence, the EPROT register is loaded from the EEE protection byte in the Flash
configuration field at global address 0x7F_FF0D located in P-Flash memory (see Table 29-3) as indicated
by reset condition F in Figure 29-15. To change the EEE protection that will be loaded during the reset
sequence, the P-Flash sector containing the EEE protection byte must be unprotected, then the EEE
protection byte must be programmed. If a double bit fault is detected while reading the P-Flash phrase

MC9S12XE-Family Reference Manual  Rev. 1.25

1164 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

containing the EEE protection byte during the reset sequence, the EPOPEN bit will be cleared and
remaining bits in the EPROT register will be set to leave the buffer RAM EEE partition fully protected.

Trying to write data to any protected area in the buffer RAM EEE partition will result in a protection
violation error and the EPVIOLIF flag will be set in the FERSTAT register. Trying to write data to any
protected area in the buffer RAM partitioned for user access will not be prevented and the EPVIOLIF flag
in the FERSTAT register will not set.

Table 29-24. EPROT Field Descriptions

Field Description

7 Enables writes to the Buffer RAM partitioned for EEE
EPOPEN 0 The entire buffer RAM EEE partition is protected from writes

1 Unprotected buffer RAM EEE partition areas are enabled for writes

6–4 Reserved Nonvolatile Bits — The RNV bits should remain in the erased state for future enhancements
RNV[6:4]

3 Buffer RAM Protection Address Range Disable — The EPDIS bit determines whether there is a protected
EPDIS area in a specific region of the buffer RAM EEE partition.

0 Protection enabled
1 Protection disabled

2–0 Buffer RAM Protection Size — The EPS[2:0] bits determine the size of the protected area in the buffer RAM
EPS[2:0] EEE partition as shown inTable 29-21. The EPS bits can only be written to while the EPDIS bit is set.

Table 29-25. Buffer RAM EEE Partition Protection Address Range

EPS[2:0]  Global Address Range Protected Size

000 0x13_FFC0 – 0x13_FFFF 64 bytes

001 0x13_FF80 – 0x13_FFFF 128 bytes

010 0x13_FF40 – 0x13_FFFF 192 bytes

011 0x13_FF00 – 0x13_FFFF 256 bytes

100 0x13_FEC0 – 0x13_FFFF 320 bytes

101 0x13_FE80 – 0x13_FFFF 384 bytes

110 0x13_FE40 – 0x13_FFFF 448 bytes

111 0x13_FE00 – 0x13_FFFF 512 bytes

********* Flash Common Command Object Register (FCCOB)
The FCCOB is an array of six words addressed via the CCOBIX index found in the FCCOBIX register.
Byte wide reads and writes are allowed to the FCCOB register.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1165



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Offset Module Base + 0x000A

7 6 5 4 3 2 1 0

R
CCOB[15:8]

W

Reset 0 0 0 0 0 0 0 0

Figure 29-16. Flash Common Command Object High Register (FCCOBHI)

Offset Module Base + 0x000B

7 6 5 4 3 2 1 0

R
CCOB[7:0]

W

Reset 0 0 0 0 0 0 0 0

Figure 29-17. Flash Common Command Object Low Register (FCCOBLO)

*********.1 FCCOB - NVM Command Mode
NVM command mode uses the indexed FCCOB register to provide a command code and its relevant
parameters to the Memory Controller. The user first sets up all required FCCOB fields and then initiates
the command’s execution by writing a 1 to the CCIF bit in the FSTAT register (a 1 written by the user
clears the CCIF command completion flag to 0). When the user clears the CCIF bit in the FSTAT register
all FCCOB parameter fields are locked and cannot be changed by the user until the command completes
(as evidenced by the Memory Controller returning CCIF to 1). Some commands return information to the
FCCOB register array.

The generic format for the FCCOB parameter fields in NVM command mode is shown in Table 29-26.
The return values are available for reading after the CCIF flag in the FSTAT register has been returned to
1 by the Memory Controller. Writes to the unimplemented parameter fields (CCOBIX = 110 and CCOBIX
= 111) are ignored with reads from these fields returning 0x0000.

Table 29-26 shows the generic Flash command format. The high byte of the first word in the CCOB array
contains the command code, followed by the parameters for this specific Flash command. For details on
the FCCOB settings required by each command, see the Flash command descriptions in Section 29.4.2.

Table 29-26. FCCOB - NVM Command Mode (Typical Usage)

CCOBIX[2:0] Byte FCCOB Parameter Fields (NVM Command Mode)

HI FCMD[7:0] defining Flash command
000

LO 0, Global address [22:16]

HI Global address [15:8]
001

LO Global address [7:0]

HI Data 0 [15:8]
010

LO Data 0 [7:0]

MC9S12XE-Family Reference Manual  Rev. 1.25

1166 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-26. FCCOB - NVM Command Mode (Typical Usage)

CCOBIX[2:0] Byte FCCOB Parameter Fields (NVM Command Mode)

HI Data 1 [15:8]
011

LO Data 1 [7:0]

HI Data 2 [15:8]
100

LO Data 2 [7:0]

HI Data 3 [15:8]
101

LO Data 3 [7:0]

********* EEE Tag Counter Register (ETAG)
The ETAG register contains the number of outstanding words in the buffer RAM EEE partition that need
to be programmed into the D-Flash EEE partition. The ETAG register is decremented prior to the related
tagged word being programmed into the D-Flash EEE partition. All tagged words have been programmed
into the D-Flash EEE partition once all bits in the ETAG register read 0 and the MGBUSY flag in the
FSTAT register reads 0.

Offset Module Base + 0x000C

7 6 5 4 3 2 1 0

R ETAG[15:8]

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 29-18. EEE Tag Counter High Register (ETAGHI)

Offset Module Base + 0x000D

7 6 5 4 3 2 1 0

R ETAG[7:0]

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 29-19. EEE Tag Counter Low Register (ETAGLO)

All ETAG bits are readable but not writable and are cleared by the Memory Controller.

********* Flash ECC Error Results Register (FECCR)
The FECCR registers contain the result of a detected ECC fault for both single bit and double bit faults.
The FECCR register provides access to several ECC related fields as defined by the ECCRIX index bits
in the FECCRIX register (see Section ********). Once ECC fault information has been stored, no other

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1167



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

fault information will be recorded until the specific ECC fault flag has been cleared. In the event of
simultaneous ECC faults, the priority for fault recording is:

1. Double bit fault over single bit fault
2. CPU over XGATE

Offset Module Base + 0x000E

7 6 5 4 3 2 1 0

R ECCR[15:8]

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 29-20. Flash ECC Error Results High Register (FECCRHI)

Offset Module Base + 0x000F

7 6 5 4 3 2 1 0

R ECCR[7:0]

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 29-21. Flash ECC Error Results Low Register (FECCRLO)

All FECCR bits are readable but not writable.
Table 29-27. FECCR Index Settings

ECCRIX[2:0] FECCR Register Content

Bits [15:8] Bit[7] Bits[6:0]

Parity bits read from CPU or XGATE Global address
000

Flash block source identity [22:16]

001 Global address [15:0]

010 Data 0 [15:0]

011 Data 1 [15:0] (P-Flash only)

100 Data 2 [15:0] (P-Flash only)

101 Data 3 [15:0] (P-Flash only)

110 Not used, returns 0x0000 when read

111 Not used, returns 0x0000 when read

MC9S12XE-Family Reference Manual  Rev. 1.25

1168 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-28. FECCR Index=000 Bit Descriptions

Field Description

15:8 ECC Parity Bits — Contains the 8 parity bits from the 72 bit wide P-Flash data word or the 6 parity bits,
PAR[7:0] allocated to PAR[5:0], from the 22 bit wide D-Flash word with PAR[7:6]=00.

7 Bus Source Identifier — The XBUS01 bit determines whether the ECC error was caused by a read access
XBUS01 from the CPU or XGATE.

0 ECC Error happened on the CPU access
1 ECC Error happened on the XGATE access

6–0 Global Address — The GADDR[22:16] field contains the upper seven bits of the global address having
GADDR[22:16] caused the error.

The P-Flash word addressed by ECCRIX = 001 contains the lower 16 bits of the global address. The
following four words addressed by ECCRIX = 010 to 101 contain the 64-bit wide data phrase. The four
data words and the parity byte are the uncorrected data read from the P-Flash block.

The D-Flash word addressed by ECCRIX = 001 contains the lower 16 bits of the global address. The
uncorrected 16-bit data word is addressed by ECCRIX = 010.

********* Flash Option Register (FOPT)
The FOPT register is the Flash option register.

Offset Module Base + 0x0010

7 6 5 4 3 2 1 0

R NV[7:0]

W

Reset F F F F F F F F

= Unimplemented or Reserved

Figure 29-22. Flash Option Register (FOPT)

All bits in the FOPT register are readable but are not writable.

During the reset sequence, the FOPT register is loaded from the Flash nonvolatile byte in the Flash
configuration field at global address 0x7F_FF0E located in P-Flash memory (see Table 29-3) as indicated
by reset condition F in Figure 29-22. If a double bit fault is detected while reading the P-Flash phrase
containing the Flash nonvolatile byte during the reset sequence, all bits in the FOPT register will be set.

Table 29-29. FOPT Field Descriptions

Field Description

7–0 Nonvolatile Bits — The NV[7:0] bits are available as nonvolatile bits. Refer to the device user guide for proper
NV[7:0] use of the NV bits.

********* Flash Reserved0 Register (FRSV0)
This Flash register is reserved for factory testing.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1169



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Offset Module Base + 0x0011

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 29-23. Flash Reserved0 Register (FRSV0)

All bits in the FRSV0 register read 0 and are not writable.

********* Flash Reserved1 Register (FRSV1)
This Flash register is reserved for factory testing.

Offset Module Base + 0x0012

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 29-24. Flash Reserved1 Register (FRSV1)

All bits in the FRSV1 register read 0 and are not writable.

********* Flash Reserved2 Register (FRSV2)
This Flash register is reserved for factory testing.

Offset Module Base + 0x0013

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 29-25. Flash Reserved2 Register (FRSV2)

All bits in the FRSV2 register read 0 and are not writable.

MC9S12XE-Family Reference Manual  Rev. 1.25

1170 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

29.4 Functional Description

29.4.1 Flash Command Operations
Flash command operations are used to modify Flash memory contents or configure module resources for
EEE operation.

The next sections describe:
• How to write the FCLKDIV register that is used to generate a time base (FCLK) derived from

OSCCLK for Flash program and erase command operations
• The command write sequence used to set Flash command parameters and launch execution
• Valid Flash commands available for execution

******** Writing the FCLKDIV Register
Prior to issuing any Flash program or erase command after a reset, the user is required to write the
FCLKDIV register to divide OSCCLK down to a target FCLK of 1 MHz. Table 29-9 shows recommended
values for the FDIV field based on OSCCLK frequency.

NOTE
Programming or erasing the Flash memory cannot be performed if the bus
clock runs at less than 1 MHz. Setting FDIV too high can destroy the Flash
memory due to overstress. Setting FDIV too low can result in incomplete
programming or erasure of the Flash memory cells.

When the FCLKDIV register is written, the FDIVLD bit is set automatically. If the FDIVLD bit is 0, the
FCLKDIV register has not been written since the last reset. If the FCLKDIV register has not been written,
any Flash program or erase command loaded during a command write sequence will not execute and the
ACCERR bit in the FSTAT register will set.

******** Command Write Sequence
The Memory Controller will launch all valid Flash commands entered using a command write sequence.

Before launching a command, the ACCERR and FPVIOL bits in the FSTAT register must be clear (see
Section ********) and the CCIF flag should be tested to determine the status of the current command write
sequence. If CCIF is 0, the previous command write sequence is still active, a new command write
sequence cannot be started, and all writes to the FCCOB register are ignored.

CAUTION
Writes to any Flash register must be avoided while a Flash command is
active (CCIF=0) to prevent corruption of Flash register contents and
Memory Controller behavior.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1171



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

********.1 Define FCCOB Contents
The FCCOB parameter fields must be loaded with all required parameters for the Flash command being
executed. Access to the FCCOB parameter fields is controlled via the CCOBIX bits in the FCCOBIX
register (see Section ********).

The contents of the FCCOB parameter fields are transferred to the Memory Controller when the user clears
the CCIF command completion flag in the FSTAT register (writing 1 clears the CCIF to 0). The CCIF flag
will remain clear until the Flash command has completed. Upon completion, the Memory Controller will
return CCIF to 1 and the FCCOB register will be used to communicate any results. The flow for a generic
command write sequence is shown in Figure 29-26.

MC9S12XE-Family Reference Manual  Rev. 1.25

1172 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

START

Read: FCLKDIV register

Clock Register
Written FDIVLD no
Check Set?

Note: FCLKDIV must be set after
yes

Write: FCLKDIV register each reset

Read: FSTAT register

FCCOB CCIF no
Availability Check Set?

yes Results from previous Command

Access Error and ACCERR/ yes
FPVIOL Write: FSTAT register

Protection Violation Clear ACCERR/FPVIOL 0x30
Check Set?

no
Write to FCCOBIX register
to identify specific command
parameter to load.

Write to FCCOB register
to load required command parameter.

More yes
Parameters?

no

Write: FSTAT register (to launch command)
Clear CCIF 0x80

Read: FSTAT register

Bit Polling for
Command Completion no

CCIF Set?
Check

yes

EXIT

Figure 29-26. Generic Flash Command Write Sequence Flowchart

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1173



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

29.4.1.3 Valid Flash Module Commands
Table 29-30. Flash Commands by Mode

Unsecured Secured
FCMD Command NS NX NX

(1) (2) SS(3) ST(4) NS
(5) (6) SS(7) ST(8)

0x01 Erase Verify All Blocks ∗ ∗ ∗ ∗ ∗ ∗ ∗ ∗
0x02 Erase Verify Block ∗ ∗ ∗ ∗ ∗ ∗ ∗ ∗
0x03 Erase Verify P-Flash Section ∗ ∗ ∗ ∗ ∗
0x04 Read Once ∗ ∗ ∗ ∗ ∗
0x05 Load Data Field ∗ ∗ ∗ ∗ ∗
0x06 Program P-Flash ∗ ∗ ∗ ∗ ∗
0x07 Program Once ∗ ∗ ∗ ∗ ∗
0x08 Erase All Blocks ∗ ∗ ∗ ∗
0x09 Erase P-Flash Block ∗ ∗ ∗ ∗ ∗
0x0A Erase P-Flash Sector ∗ ∗ ∗ ∗ ∗
0x0B Unsecure Flash ∗ ∗ ∗ ∗
0x0C Verify Backdoor Access Key ∗ ∗
0x0D Set User Margin Level ∗ ∗ ∗ ∗ ∗
0x0E Set Field Margin Level ∗ ∗
0x0F Full Partition D-Flash ∗ ∗
0x10 Erase Verify D-Flash Section ∗ ∗ ∗ ∗ ∗
0x11 Program D-Flash ∗ ∗ ∗ ∗ ∗
0x12 Erase D-Flash Sector ∗ ∗ ∗ ∗ ∗
0x13 Enable EEPROM Emulation ∗ ∗ ∗ ∗ ∗ ∗ ∗ ∗
0x14 Disable EEPROM Emulation ∗ ∗ ∗ ∗ ∗ ∗ ∗ ∗
0x15 EEPROM Emulation Query ∗ ∗ ∗ ∗ ∗ ∗ ∗ ∗
0x20 Partition D-Flash ∗ ∗ ∗ ∗ ∗ ∗ ∗ ∗

1. Unsecured Normal Single Chip mode.
2. Unsecured Normal Expanded mode.
3. Unsecured Special Single Chip mode.
4. Unsecured Special Mode.
5. Secured Normal Single Chip mode.
6. Secured Normal Expanded mode.
7. Secured Special Single Chip mode.
8. Secured Special Mode.

MC9S12XE-Family Reference Manual  Rev. 1.25

1174 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

******** P-Flash Commands
Table 29-31 summarizes the valid P-Flash commands along with the effects of the commands on the P-
Flash block and other resources within the Flash module.

Table 29-31. P-Flash Commands

FCMD Command Function on P-Flash Memory

Erase Verify All Verify that all P-Flash (and D-Flash) blocks are erased.
0x01

Blocks
0x02 Erase Verify Block Verify that a P-Flash block is erased.

Erase Verify P- Verify that a given number of words starting at the address provided are erased.
0x03

Flash Section
Read a dedicated 64 byte field in the nonvolatile information register in P-Flash block 0

0x04 Read Once
that was previously programmed using the Program Once command.

0x05 Load Data Field Load data for simultaneous multiple P-Flash block operations.
Program a phrase in a P-Flash block and any previously loaded phrases for any other P-

0x06 Program P-Flash
Flash block (see Load Data Field command).
Program a dedicated 64 byte field in the nonvolatile information register in P-Flash block

0x07 Program Once
0 that is allowed to be programmed only once.
Erase all P-Flash (and D-Flash) blocks.
An erase of all Flash blocks is only possible when the FPLDIS, FPHDIS, and FPOPEN

0x08 Erase All Blocks
bits in the FPROT register and the EPDIS and EPOPEN bits in the EPROT register are
set prior to launching the command.
Erase a single P-Flash block.

Erase P-Flash
0x09 An erase of the full P-Flash block is only possible when FPLDIS, FPHDIS and FPOPEN

Block
bits in the FPROT register are set prior to launching the command.

Erase P-Flash Erase all bytes in a P-Flash sector.
0x0A

Sector
Supports a method of releasing MCU security by erasing all P-Flash (and D-Flash) blocks

0x0B Unsecure Flash
and verifying that all P-Flash (and D-Flash) blocks are erased.

Verify Backdoor Supports a method of releasing MCU security by verifying a set of security keys.
0x0C

Access Key
Set User Margin Specifies a user margin read level for all P-Flash blocks.

0x0D
Level

Set Field Margin Specifies a field margin read level for all P-Flash blocks (special modes only).
0x0E

Level

29.4.1.5 D-Flash and EEE Commands
Table 29-32 summarizes the valid D-Flash and EEE commands along with the effects of the commands
on the D-Flash block and EEE operation.

Table 29-32. D-Flash Commands

FCMD Command Function on D-Flash Memory

Erase Verify All Verify that all D-Flash (and P-Flash) blocks are erased.
0x01

Blocks
0x02 Erase Verify Block Verify that the D-Flash block is erased.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1175



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-32. D-Flash Commands

FCMD Command Function on D-Flash Memory

Erase all D-Flash (and P-Flash) blocks.
An erase of all Flash blocks is only possible when the FPLDIS, FPHDIS, and FPOPEN

0x08 Erase All Blocks
bits in the FPROT register and the EPDIS and EPOPEN bits in the EPROT register are
set prior to launching the command.
Supports a method of releasing MCU security by erasing all D-Flash (and P-Flash) blocks

0x0B Unsecure Flash
and verifying that all D-Flash (and P-Flash) blocks are erased.

Set User Margin Specifies a user margin read level for the D-Flash block.
0x0D

Level
Set Field Margin Specifies a field margin read level for the D-Flash block (special modes only).

0x0E
Level

Full Partition D- Erase the D-Flash block and partition an area of the D-Flash block for user access.
0x0F

Flash
Erase Verify D- Verify that a given number of words starting at the address provided are erased.

0x10
Flash Section

0x11 Program D-Flash Program up to four words in the D-Flash block.
Erase D-Flash Erase all bytes in a sector of the D-Flash block.

0x12
Sector

Enable EEPROM Enable EEPROM emulation where writes to the buffer RAM EEE partition will be copied
0x13

Emulation to the D-Flash EEE partition.
Disable EEPROM Suspend all current erase and program activity related to EEPROM emulation but leave

0x14
Emulation current EEE tags set.
EEPROM Returns EEE partition and status variables.

0x15
Emulation Query

0x20 Partition D-Flash Partition an area of the D-Flash block for user access.

29.4.2 Flash Command Description
This section provides details of all available Flash commands launched by a command write sequence. The
ACCERR bit in the FSTAT register will be set during the command write sequence if any of the following
illegal steps are performed, causing the command not to be processed by the Memory Controller:

• Starting any command write sequence that programs or erases Flash memory before initializing the
FCLKDIV register

• Writing an invalid command as part of the command write sequence
• For additional possible errors, refer to the error handling table provided for each command

If a Flash block is read during execution of an algorithm (CCIF = 0) on that same block, the read operation
will return invalid data. If the SFDIF or DFDIF flags were not previously set when the invalid read
operation occurred, both the SFDIF and DFDIF flags will be set and the FECCR registers will be loaded
with the global address used in the invalid read operation with the data and parity fields set to all 0.

If the ACCERR or FPVIOL bits are set in the FSTAT register, the user must clear these bits before starting
any command write sequence (see Section ********).

MC9S12XE-Family Reference Manual  Rev. 1.25

1176 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

CAUTION
A Flash word or phrase must be in the erased state before being
programmed. Cumulative programming of bits within a Flash word or
phrase is not allowed.

******** Erase Verify All Blocks Command
The Erase Verify All Blocks command will verify that all P-Flash and D-Flash blocks have been erased.

Table 29-33. Erase Verify All Blocks Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

000 0x01 Not required

Upon clearing CCIF to launch the Erase Verify All Blocks command, the Memory Controller will verify
that the entire Flash memory space is erased. The CCIF flag will set after the Erase Verify All Blocks
operation has completed.

Table 29-34. Erase Verify All Blocks Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 000 at command launch
ACCERR

Set if a Load Data Field command sequence is currently active

FSTAT FPVIOL None

MGSTAT1 Set if any errors have been encountered during the read

MGSTAT0 Set if any non-correctable errors have been encountered during the read

FERSTAT EPVIOLIF None

******** Erase Verify Block Command
The Erase Verify Block command allows the user to verify that an entire P-Flash or D-Flash block has been
erased. The FCCOB upper global address bits determine which block must be verified.

Table 29-35. Erase Verify Block Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

Global address [22:16] of the
000 0x02

Flash block to be verified.

Upon clearing CCIF to launch the Erase Verify Block command, the Memory Controller will verify that
the selected P-Flash or D-Flash block is erased. The CCIF flag will set after the Erase Verify Block
operation has completed.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1177



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-36. Erase Verify Block Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 000 at command launch

ACCERR Set if a Load Data Field command sequence is currently active

Set if an invalid global address [22:16] is supplied
FSTAT

FPVIOL None

MGSTAT1 Set if any errors have been encountered during the read

MGSTAT0 Set if any non-correctable errors have been encountered during the read

FERSTAT EPVIOLIF None

******** Erase Verify P-Flash Section Command
The Erase Verify P-Flash Section command will verify that a section of code in the P-Flash memory is
erased. The Erase Verify P-Flash Section command defines the starting point of the code to be verified and
the number of phrases. The section to be verified cannot cross a 256 Kbyte boundary in the P-Flash
memory space.

Table 29-37. Erase Verify P-Flash Section Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

Global address [22:16] of
000 0x03

a P-Flash block

001 Global address [15:0] of the first phrase to be verified

010 Number of phrases to be verified

Upon clearing CCIF to launch the Erase Verify P-Flash Section command, the Memory Controller will
verify the selected section of Flash memory is erased. The CCIF flag will set after the Erase Verify P-Flash
Section operation has completed.

Table 29-38. Erase Verify P-Flash Section Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 010 at command launch

Set if a Load Data Field command sequence is currently active

Set if command not available in current mode (see Table 29-30)
ACCERR

Set if an invalid global address [22:0] is supplied

FSTAT Set if a misaligned phrase address is supplied (global address [2:0] != 000)

Set if the requested section crosses a 256 Kbyte boundary

FPVIOL None

MGSTAT1 Set if any errors have been encountered during the read

MGSTAT0 Set if any non-correctable errors have been encountered during the read

MC9S12XE-Family Reference Manual  Rev. 1.25

1178 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-38. Erase Verify P-Flash Section Command Error Handling

Register Error Bit Error Condition

FERSTAT EPVIOLIF None

******** Read Once Command
The Read Once command provides read access to a reserved 64 byte field (8 phrases) located in the
nonvolatile information register of P-Flash block 0. The Read Once field is programmed using the
Program Once command described in Section ********. The Read Once command must not be executed
from the Flash block containing the Program Once reserved field to avoid code runaway.

Table 29-39. Read Once Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

000 0x04 Not Required

001 Read Once phrase index (0x0000 - 0x0007)

010 Read Once word 0 value

011 Read Once word 1 value

100 Read Once word 2 value

101 Read Once word 3 value

Upon clearing CCIF to launch the Read Once command, a Read Once phrase is fetched and stored in the
FCCOB indexed register. The CCIF flag will set after the Read Once operation has completed. Valid
phrase index values for the Read Once command range from 0x0000 to 0x0007. During execution of the
Read Once command, any attempt to read addresses within P-Flash block 0 will return invalid data.

128

Table 29-40. Read Once Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 001 at command launch

Set if a Load Data Field command sequence is currently active
ACCERR

Set if command not available in current mode (see Table 29-30)

FSTAT Set if an invalid phrase index is supplied

FPVIOL None

MGSTAT1 Set if any errors have been encountered during the read

MGSTAT0 Set if any non-correctable errors have been encountered during the read

FERSTAT EPVIOLIF None

******** Load Data Field Command
The Load Data Field command is executed to provide FCCOB parameters for multiple P-Flash blocks for
a future simultaneous program operation in the P-Flash memory space.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1179



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-41. Load Data Field Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

Global address [22:16] to
000 0x05

identify P-Flash block

001 Global address [15:0] of phrase location to be programmed(1)

010 Word 0

011 Word 1

100 Word 2

101 Word 3
1. Global address [2:0] must be 000

Upon clearing CCIF to launch the Load Data Field command, the FCCOB registers will be transferred to
the Memory Controller and be programmed in the block specified at the global address given with a future
Program P-Flash command launched on a P-Flash block. The CCIF flag will set after the Load Data Field
operation has completed. Note that once a Load Data Field command sequence has been initiated, the Load
Data Field command sequence will be cancelled if any command other than Load Data Field or the future
Program P-Flash is launched. Similarly, if an error occurs after launching a Load Data Field or Program
P-Flash command, the associated Load Data Field command sequence will be cancelled.

Table 29-42. Load Data Field Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 101 at command launch

Set if command not available in current mode (see Table 29-30)

Set if an invalid global address [22:0] is supplied

Set if a misaligned phrase address is supplied (global address [2:0] != 000)
ACCERR

Set if a Load Data Field command sequence is currently active and the selected
block has previously been selected in the same command sequence

FSTAT
Set if a Load Data Field command sequence is currently active and global
address [17:0] does not match that previously supplied in the same command
sequence

FPVIOL Set if the global address [22:0] points to a protected area

MGSTAT1 None

MGSTAT0 None

FERSTAT EPVIOLIF None

******** Program P-Flash Command
The Program P-Flash operation will program a previously erased phrase in the P-Flash memory using an
embedded algorithm.

MC9S12XE-Family Reference Manual  Rev. 1.25

1180 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

CAUTION
A P-Flash phrase must be in the erased state before being programmed.
Cumulative programming of bits within a Flash phrase is not allowed.

Table 29-43. Program P-Flash Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

Global address [22:16] to
000 0x06

identify P-Flash block

001 Global address [15:0] of phrase location to be programmed(1)

010 Word 0 program value

011 Word 1 program value

100 Word 2 program value

101 Word 3 program value
1. Global address [2:0] must be 000

Upon clearing CCIF to launch the Program P-Flash command, the Memory Controller will program the
data words to the supplied global address and will then proceed to verify the data words read back as
expected. The CCIF flag will set after the Program P-Flash operation has completed.

Table 29-44. Program P-Flash Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 101 at command launch

Set if command not available in current mode (see Table 29-30)

Set if an invalid global address [22:0] is supplied

Set if a misaligned phrase address is supplied (global address [2:0] != 000)
ACCERR

Set if a Load Data Field command sequence is currently active and the selected
block has previously been selected in the same command sequence

FSTAT
Set if a Load Data Field command sequence is currently active and global
address [17:0] does not match that previously supplied in the same command
sequence

FPVIOL Set if the global address [22:0] points to a protected area

MGSTAT1 Set if any errors have been encountered during the verify operation

Set if any non-correctable errors have been encountered during the verify
MGSTAT0

operation

FERSTAT EPVIOLIF None

******** Program Once Command
The Program Once command restricts programming to a reserved 64 byte field (8 phrases) in the
nonvolatile information register located in P-Flash block 0. The Program Once reserved field can be read
using the Read Once command as described in Section ********. The Program Once command must only
be issued once since the nonvolatile information register in P-Flash block 0 cannot be erased. The Program

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1181



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Once command must not be executed from the Flash block containing the Program Once reserved field to
avoid code runaway.

Table 29-45. Program Once Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

000 0x07 Not Required

001 Program Once phrase index (0x0000 - 0x0007)

010 Program Once word 0 value

011 Program Once word 1 value

100 Program Once word 2 value

101 Program Once word 3 value

Upon clearing CCIF to launch the Program Once command, the Memory Controller first verifies that the
selected phrase is erased. If erased, then the selected phrase will be programmed and then verified with
read back. The CCIF flag will remain clear, setting only after the Program Once operation has completed.

The reserved nonvolatile information register accessed by the Program Once command cannot be erased
and any attempt to program one of these phrases a second time will not be allowed. Valid phrase index
values for the Program Once command range from 0x0000 to 0x0007. During execution of the Program
Once command, any attempt to read addresses within P-Flash block 0 will return invalid data.

Table 29-46. Program Once Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 101 at command launch

Set if a Load Data Field command sequence is currently active

ACCERR Set if command not available in current mode (see Table 29-30)

Set if an invalid phrase index is supplied
FSTAT Set if the requested phrase has already been programmed(1)

FPVIOL None

MGSTAT1 Set if any errors have been encountered during the verify operation

Set if any non-correctable errors have been encountered during the verify
MGSTAT0

operation

FERSTAT EPVIOLIF None
1. If a Program Once phrase is initially programmed to 0xFFFF_FFFF_FFFF_FFFF, the Program Once command will

be allowed to execute again on that same phrase.

******** Erase All Blocks Command
The Erase All Blocks operation will erase the entire P-Flash and D-Flash memory space including the EEE
nonvolatile information register.

MC9S12XE-Family Reference Manual  Rev. 1.25

1182 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-47. Erase All Blocks Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

000 0x08 Not required

Upon clearing CCIF to launch the Erase All Blocks command, the Memory Controller will erase the entire
Flash memory space and verify that it is erased. If the Memory Controller verifies that the entire Flash
memory space was properly erased, security will be released. During the execution of this command
(CCIF=0) the user must not write to any Flash module register. The CCIF flag will set after the Erase All
Blocks operation has completed.

Table 29-48. Erase All Blocks Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 000 at command launch

ACCERR Set if a Load Data Field command sequence is currently active

Set if command not available in current mode (see Table 29-30)
FSTAT FPVIOL Set if any area of the P-Flash memory is protected

MGSTAT1 Set if any errors have been encountered during the verify operation

Set if any non-correctable errors have been encountered during the verify
MGSTAT0

operation

FERSTAT EPVIOLIF Set if any area of the buffer RAM EEE partition is protected

******** Erase P-Flash Block Command
The Erase P-Flash Block operation will erase all addresses in a P-Flash block.

Table 29-49. Erase P-Flash Block Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

Global address [22:16] to
000 0x09

identify P-Flash block

001 Global address [15:0] in P-Flash block to be erased

Upon clearing CCIF to launch the Erase P-Flash Block command, the Memory Controller will erase the
selected P-Flash block and verify that it is erased. The CCIF flag will set after the Erase P-Flash Block
operation has completed.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1183



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-50. Erase P-Flash Block Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 001 at command launch

Set if a Load Data Field command sequence is currently active
ACCERR

Set if command not available in current mode (see Table 29-30)

Set if an invalid global address [22:16] is supplied
FSTAT

FPVIOL Set if an area of the selected P-Flash block is protected

MGSTAT1 Set if any errors have been encountered during the verify operation

Set if any non-correctable errors have been encountered during the verify
MGSTAT0

operation

FERSTAT EPVIOLIF None

********0 Erase P-Flash Sector Command
The Erase P-Flash Sector operation will erase all addresses in a P-Flash sector.

Table 29-51. Erase P-Flash Sector Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

Global address [22:16] to identify
000 0x0A

P-Flash block to be erased

Global address [15:0] anywhere within the sector to be erased.
001

Refer to Section ******** for the P-Flash sector size.

Upon clearing CCIF to launch the Erase P-Flash Sector command, the Memory Controller will erase the
selected Flash sector and then verify that it is erased. The CCIF flag will be set after the Erase P-Flash
Sector operation has completed.

Table 29-52. Erase P-Flash Sector Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 001 at command launch

Set if a Load Data Field command sequence is currently active

ACCERR Set if command not available in current mode (see Table 29-30)

Set if an invalid global address [22:16] is supplied
FSTAT Set if a misaligned phrase address is supplied (global address [2:0] != 000)

FPVIOL Set if the selected P-Flash sector is protected

MGSTAT1 Set if any errors have been encountered during the verify operation

Set if any non-correctable errors have been encountered during the verify
MGSTAT0

operation

FERSTAT EPVIOLIF None

MC9S12XE-Family Reference Manual  Rev. 1.25

1184 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

********1 Unsecure Flash Command
The Unsecure Flash command will erase the entire P-Flash and D-Flash memory space and, if the erase is
successful, will release security.

Table 29-53. Unsecure Flash Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

000 0x0B Not required

Upon clearing CCIF to launch the Unsecure Flash command, the Memory Controller will erase the entire
P-Flash and D-Flash memory space and verify that it is erased. If the Memory Controller verifies that the
entire Flash memory space was properly erased, security will be released. If the erase verify is not
successful, the Unsecure Flash operation sets MGSTAT1 and terminates without changing the security
state. During the execution of this command (CCIF=0) the user must not write to any Flash module
register. The CCIF flag is set after the Unsecure Flash operation has completed.

Table 29-54. Unsecure Flash Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 000 at command launch

ACCERR Set if a Load Data Field command sequence is currently active

Set if command not available in current mode (see Table 29-30)
FSTAT FPVIOL Set if any area of the P-Flash memory is protected

MGSTAT1 Set if any errors have been encountered during the verify operation

Set if any non-correctable errors have been encountered during the verify
MGSTAT0

operation

FERSTAT EPVIOLIF Set if any area of the buffer RAM EEE partition is protected

********2 Verify Backdoor Access Key Command
The Verify Backdoor Access Key command will only execute if it is enabled by the KEYEN bits in the
FSEC register (see Table 29-11). The Verify Backdoor Access Key command releases security if user-
supplied keys match those stored in the Flash security bytes of the Flash configuration field (see Table 29-
3). The Verify Backdoor Access Key command must not be executed from the Flash block containing the
backdoor comparison key to avoid code runaway.

Table 29-55. Verify Backdoor Access Key Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

000 0x0C Not required

001 Key 0

010 Key 1

011 Key 2

100 Key 3

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1185



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Upon clearing CCIF to launch the Verify Backdoor Access Key command, the Memory Controller will
check the FSEC KEYEN bits to verify that this command is enabled. If not enabled, the Memory
Controller sets the ACCERR bit in the FSTAT register and terminates. If the command is enabled, the
Memory Controller compares the key provided in FCCOB to the backdoor comparison key in the Flash
configuration field with Key 0 compared to 0x7F_FF00, etc. If the backdoor keys match, security will be
released. If the backdoor keys do not match, security is not released and all future attempts to execute the
Verify Backdoor Access Key command are aborted (set ACCERR) until a reset occurs. The CCIF flag is
set after the Verify Backdoor Access Key operation has completed.

Table 29-56. Verify Backdoor Access Key Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 100 at command launch

Set if a Load Data Field command sequence is currently active

Set if an incorrect backdoor key is supplied
ACCERR

Set if backdoor key access has not been enabled (KEYEN[1:0] != 10, see
FSTAT Section ********)

Set if the backdoor key has mismatched since the last reset

FPVIOL None

MGSTAT1 None

MGSTAT0 None

FERSTAT EPVIOLIF None

********3 Set User Margin Level Command
The Set User Margin Level command causes the Memory Controller to set the margin level for future read
operations of a specific P-Flash or D-Flash block.

Table 29-57. Set User Margin Level Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

Global address [22:16] to identify the
000 0x0D

Flash block

001 Margin level setting

Upon clearing CCIF to launch the Set User Margin Level command, the Memory Controller will set the
user margin level for the targeted block and then set the CCIF flag.

Valid margin level settings for the Set User Margin Level command are defined in Table 29-58.
Table 29-58. Valid Set User Margin Level Settings

CCOB
Level Description

(CCOBIX=001)

0x0000 Return to Normal Level

0x0001 User Margin-1 Level(1)

MC9S12XE-Family Reference Manual  Rev. 1.25

1186 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-58. Valid Set User Margin Level Settings

CCOB
Level Description

(CCOBIX=001)

0x0002 User Margin-0 Level(2)

1. Read margin to the erased state
2. Read margin to the programmed state

Table 29-59. Set User Margin Level Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 001 at command launch

Set if a Load Data Field command sequence is currently active

ACCERR Set if command not available in current mode (see Table 29-30)

Set if an invalid global address [22:16] is supplied
FSTAT

Set if an invalid margin level setting is supplied

FPVIOL None

MGSTAT1 None

MGSTAT0 None

FERSTAT EPVIOLIF None

NOTE
User margin levels can be used to check that Flash memory contents have
adequate margin for normal level read operations. If unexpected results are
encountered when checking Flash memory contents at user margin levels, a
potential loss of information has been detected.

********4 Set Field Margin Level Command
The Set Field Margin Level command, valid in special modes only, causes the Memory Controller to set
the margin level specified for future read operations of a specific P-Flash or D-Flash block.

Table 29-60.  Set Field Margin Level Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

Global address [22:16] to identify the Flash
000 0x0E

block

001 Margin level setting

Upon clearing CCIF to launch the Set Field Margin Level command, the Memory Controller will set the
field margin level for the targeted block and then set the CCIF flag.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1187



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Valid margin level settings for the Set Field Margin Level command are defined in Table 29-61.
Table 29-61. Valid Set Field Margin Level Settings

CCOB
Level Description

(CCOBIX=001)

0x0000 Return to Normal Level

0x0001 User Margin-1 Level(1)

0x0002 User Margin-0 Level(2)

0x0003 Field Margin-1 Level1

0x0004 Field Margin-0 Level2
1. Read margin to the erased state
2. Read margin to the programmed state

Table 29-62. Set Field Margin Level Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 001 at command launch

Set if a Load Data Field command sequence is currently active

ACCERR Set if command not available in current mode (see Table 29-30)

Set if an invalid global address [22:16] is supplied
FSTAT

Set if an invalid margin level setting is supplied

FPVIOL None

MGSTAT1 None

MGSTAT0 None

FERSTAT EPVIOLIF None

CAUTION
Field margin levels must only be used during verify of the initial factory
programming.

NOTE
Field margin levels can be used to check that Flash memory contents have
adequate margin for data retention at the normal level setting. If unexpected
results are encountered when checking Flash memory contents at field
margin levels, the Flash memory contents should be erased and
reprogrammed.

********5 Full Partition D-Flash Command
The Full Partition D-Flash command allows the user to allocate sectors within the D-Flash block for
applications and a partition within the buffer RAM for EEPROM access. The D-Flash block consists of
128 sectors with 256 bytes per sector.

MC9S12XE-Family Reference Manual  Rev. 1.25

1188 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-63. Full Partition D-Flash Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

000 0x0F Not required

001 Number of 256 byte sectors for the D-Flash user partition (DFPART)

010 Number of 256 byte sectors for buffer RAM EEE partition (ERPART)

Upon clearing CCIF to launch the Full Partition D-Flash command, the following actions are taken to
define a partition within the D-Flash block for direct access (DFPART) and a partition within the buffer
RAM for EEE use (ERPART):

• Validate the DFPART and ERPART values provided:
— DFPART <= 128 (maximum number of 256 byte sectors in D-Flash block)
— ERPART <= 16 (maximum number of 256 byte sectors in buffer RAM)
— If ERPART > 0, 128 - DFPART >= 12 (minimum number of 256 byte sectors in the D-Flash

block required to support EEE)
— If ERPART > 0, ((128-DFPART)/ERPART) >= 8 (minimum ratio of D-Flash EEE space to

buffer RAM EEE space to support EEE)
• Erase the D-Flash block and the EEE nonvolatile information register
• Program DFPART to the EEE nonvolatile information register at global address 0x12_0000 (see

Table 29-7)
• Program a duplicate DFPART to the EEE nonvolatile information register at global address

0x12_0002 (see Table 29-7)
• Program ERPART to the EEE nonvolatile information register at global address 0x12_0004 (see

Table 29-7)
• Program a duplicate ERPART to the EEE nonvolatile information register at global address

0x12_0006 (see Table 29-7)

The D-Flash user partition will start at global address 0x10_0000. The buffer RAM EEE partition will end
at global address 0x13_FFFF. After the Full Partition D-Flash operation has completed, the CCIF flag will
set.

Running the Full Partition D-Flash command a second time will result in the previous partition values and
the entire D-Flash memory being erased. The data value written corresponds to the number of 256 byte
sectors allocated for either direct D-Flash access (DFPART) or buffer RAM EEE access (ERPART).

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1189



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-64. Full Partition D-Flash Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 010 at command launch

Set if a Load Data Field command sequence is currently active
ACCERR

Set if command not available in current mode (see Table 29-30)

FSTAT Set if an invalid DFPART or ERPART selection is supplied

FPVIOL None

MGSTAT1 Set if any errors have been encountered during the read

MGSTAT0 Set if any non-correctable errors have been encountered during the read

FERSTAT EPVIOLIF None

********6 Erase Verify D-Flash Section Command
The Erase Verify D-Flash Section command will verify that a section of code in the D-Flash user partition
is erased. The Erase Verify D-Flash Section command defines the starting point of the data to be verified
and the number of words.

Table 29-65. Erase Verify D-Flash Section Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

Global address [22:16] to
000 0x10

identify the D-Flash block

001 Global address [15:0] of the first word to be verified

010 Number of words to be verified

Upon clearing CCIF to launch the Erase Verify D-Flash Section command, the Memory Controller will
verify the selected section of D-Flash memory is erased. The CCIF flag will set after the Erase Verify D-
Flash Section operation has completed.

MC9S12XE-Family Reference Manual  Rev. 1.25

1190 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-66. Erase Verify D-Flash Section Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 010 at command launch

Set if a Load Data Field command sequence is currently active

Set if command not available in current mode (see Table 29-30)

Set if an invalid global address [22:0] is supplied
ACCERR

Set if a misaligned word address is supplied (global address [0] != 0)
FSTAT Set if the global address [22:0] points to an area of the D-Flash EEE partition

Set if the requested section breaches the end of the D-Flash block or goes into
the D-Flash EEE partition

FPVIOL None

MGSTAT1 Set if any errors have been encountered during the read

MGSTAT0 Set if any non-correctable errors have been encountered during the read

FERSTAT EPVIOLIF None

********7 Program D-Flash Command
The Program D-Flash operation programs one to four previously erased words in the D-Flash user
partition. The Program D-Flash operation will confirm that the targeted location(s) were successfully
programmed upon completion.

CAUTION
A Flash word must be in the erased state before being programmed.
Cumulative programming of bits within a Flash word is not allowed.

Table 29-67. Program D-Flash Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

Global address [22:16] to
000 0x11

identify the D-Flash block

001 Global address [15:0] of word to be programmed

010 Word 0 program value

011 Word 1 program value, if desired

100 Word 2 program value, if desired

101 Word 3 program value, if desired

Upon clearing CCIF to launch the Program D-Flash command, the user-supplied words will be transferred
to the Memory Controller and be programmed. The CCOBIX index value at Program D-Flash command
launch determines how many words will be programmed in the D-Flash block. No protection checks are
made in the Program D-Flash operation on the D-Flash block, only access error checks. The CCIF flag is
set when the operation has completed.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1191



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-68. Program D-Flash Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] < 010 at command launch

Set if CCOBIX[2:0] > 101 at command launch

Set if a Load Data Field command sequence is currently active

Set if command not available in current mode (see Table 29-30)
ACCERR Set if an invalid global address [22:0] is supplied

Set if a misaligned word address is supplied (global address [0] != 0)
FSTAT

Set if the global address [22:0] points to an area in the D-Flash EEE partition

Set if the requested group of words breaches the end of the D-Flash block or goes
into the D-Flash EEE partition

FPVIOL None

MGSTAT1 Set if any errors have been encountered during the verify operation

Set if any non-correctable errors have been encountered during the verify
MGSTAT0

operation

FERSTAT EPVIOLIF None

********8 Erase D-Flash Sector Command
The Erase D-Flash Sector operation will erase all addresses in a sector of the D-Flash user partition.

Table 29-69. Erase D-Flash Sector Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

Global address [22:16] to identify
000 0x12

D-Flash block

Global address [15:0] anywhere within the sector to be erased.
001

See Section ******** for D-Flash sector size.

Upon clearing CCIF to launch the Erase D-Flash Sector command, the Memory Controller will erase the
selected Flash sector and verify that it is erased. The CCIF flag will set after the Erase D-Flash Sector
operation has completed.

MC9S12XE-Family Reference Manual  Rev. 1.25

1192 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-70. Erase D-Flash Sector Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 001 at command launch

Set if a Load Data Field command sequence is currently active

Set if command not available in current mode (see Table 29-30)
ACCERR

Set if an invalid global address [22:0] is supplied

Set if a misaligned word address is supplied (global address [0] != 0)
FSTAT

Set if the global address [22:0] points to the D-Flash EEE partition

FPVIOL None

MGSTAT1 Set if any errors have been encountered during the verify operation

Set if any non-correctable errors have been encountered during the verify
MGSTAT0

operation

FERSTAT EPVIOLIF None

********9 Enable EEPROM Emulation Command
The Enable EEPROM Emulation command causes the Memory Controller to enable EEE activity. EEE
activity is disabled after any reset.

Table 29-71. Enable EEPROM Emulation Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

000 0x13 Not required

Upon clearing CCIF to launch the Enable EEPROM Emulation command, the CCIF flag will set after the
Memory Controller enables EEE operations using the contents of the EEE tag RAM and tag counter. The
Full Partition D-Flash or the Partition D-Flash command must be run prior to launching the Enable
EEPROM Emulation command.

Table 29-72. Enable EEPROM Emulation Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 000 at command launch

ACCERR Set if a Load Data Field command sequence is currently active

Set if Full Partition D-Flash or Partition D-Flash command not previously run
FSTAT

FPVIOL None

MGSTAT1 None

MGSTAT0 None

FERSTAT EPVIOLIF None

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1193



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

********0 Disable EEPROM Emulation Command
The Disable EEPROM Emulation command causes the Memory Controller to suspend current EEE
activity.

Table 29-73. Disable EEPROM Emulation Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

000 0x14 Not required

Upon clearing CCIF to launch the Disable EEPROM Emulation command, the Memory Controller will
halt EEE operations at the next convenient point without clearing the EEE tag RAM or tag counter before
setting the CCIF flag.

Table 29-74. Disable EEPROM Emulation Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 000 at command launch

ACCERR Set if a Load Data Field command sequence is currently active

Set if Full Partition D-Flash or Partition D-Flash command not previously run
FSTAT

FPVIOL None

MGSTAT1 None

MGSTAT0 None

FERSTAT EPVIOLIF None

********1 EEPROM Emulation Query Command
The EEPROM Emulation Query command returns EEE partition and status variables.

Table 29-75. EEPROM Emulation Query Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

000 0x15 Not required

001 Return DFPART

010 Return ERPART

011 Return ECOUNT(1)

100 Return Dead Sector Count Return Ready Sector Count
1. Indicates sector erase count

Upon clearing CCIF to launch the EEPROM Emulation Query command, the CCIF flag will set after the
EEE partition and status variables are stored in the FCCOBIX register.If the Emulation Query command
is executed prior to partitioning (Partition D-Flash Command Section ********5), the following reset
values are returned: DFPART = 0x_FFFF, ERPART = 0x_FFFF, ECOUNT = 0x_FFFF, Dead Sector
Count = 0x_00, Ready Sector Count = 0x_00.

MC9S12XE-Family Reference Manual  Rev. 1.25

1194 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

Table 29-76. EEPROM Emulation Query Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 000 at command launch

ACCERR Set if a Load Data Field command sequence is currently active

Set if command not available in current mode (see Table 29-30)
FSTAT

FPVIOL None

MGSTAT1 None

MGSTAT0 None

FERSTAT EPVIOLIF None

********2 Partition D-Flash Command
The Partition D-Flash command allows the user to allocate sectors within the D-Flash block for
applications and a partition within the buffer RAM for EEPROM access. The D-Flash block consists of
128 sectors with 256 bytes per sector. The Erase All Blocks command must be run prior to launching the
Partition D-Flash command.

Table 29-77. Partition D-Flash Command FCCOB Requirements

CCOBIX[2:0] FCCOB Parameters

000 0x20 Not required

001 Number of 256 byte sectors for the D-Flash user partition (DFPART)

010 Number of 256 byte sectors for buffer RAM EEE partition (ERPART)

Upon clearing CCIF to launch the Partition D-Flash command, the following actions are taken to define a
partition within the D-Flash block for direct access (DFPART) and a partition within the buffer RAM for
EEE use (ERPART):

• Validate the DFPART and ERPART values provided:
— DFPART <= 128 (maximum number of 256 byte sectors in D-Flash block)
— ERPART <= 16 (maximum number of 256 byte sectors in buffer RAM)
— If ERPART > 0, 128 - DFPART >= 12 (minimum number of 256 byte sectors in the D-Flash

block required to support EEE)
— If ERPART > 0, ((128-DFPART)/ERPART) >= 8 (minimum ratio of D-Flash EEE space to

buffer RAM EEE space to support EEE)
• Erase verify the D-Flash block and the EEE nonvolatile information register
• Program DFPART to the EEE nonvolatile information register at global address 0x12_0000 (see

Table 29-7)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1195



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

• Program a duplicate DFPART to the EEE nonvolatile information register at global address
0x12_0002 (see Table 29-7)

• Program ERPART to the EEE nonvolatile information register at global address 0x12_0004 (see
Table 29-7)

• Program a duplicate ERPART to the EEE nonvolatile information register at global address
0x12_0006 (see Table 29-7)

The D-Flash user partition will start at global address 0x10_0000. The buffer RAM EEE partition will end
at global address 0x13_FFFF. After the Partition D-Flash operation has completed, the CCIF flag will set.

Running the Partition D-Flash command a second time will result in the ACCERR bit within the FSTAT
register being set. The data value written corresponds to the number of 256 byte sectors allocated for either
direct D-Flash access (DFPART) or buffer RAM EEE access (ERPART).

Table 29-78. Partition D-Flash Command Error Handling

Register Error Bit Error Condition

Set if CCOBIX[2:0] != 010 at command launch

Set if a Load Data Field command sequence is currently active

ACCERR Set if command not available in current mode (see Table 29-30)

Set if partitions have already been defined
FSTAT

Set if an invalid DFPART or ERPART selection is supplied

FPVIOL None

MGSTAT1 Set if any errors have been encountered during the read

MGSTAT0 Set if any non-correctable errors have been encountered during the read

FERSTAT EPVIOLIF None

MC9S12XE-Family Reference Manual  Rev. 1.25

1196 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

29.4.3 Interrupts
The Flash module can generate an interrupt when a Flash command operation has completed or when a
Flash command operation has detected an EEE error or an ECC fault.

Table 29-79. Flash Interrupt Sources

Global (CCR)
Interrupt Source Interrupt Flag Local Enable

Mask

Flash Command Complete CCIF CCIE I Bit
(FSTAT register) (FCNFG register)

Flash EEE Erase Error ERSERIF ERSERIE I Bit
(FERSTAT register) (FERCNFG register)

Flash EEE Program Error PGMERIF PGMERIE I Bit
(FERSTAT register) (FERCNFG register)

Flash EEE Protection Violation EPVIOLIF EPVIOLIE I Bit
(FERSTAT register) (FERCNFG register)

Flash EEE Error Type 1 Violation ERSVIF1 ERSVIE1 I Bit
(FERSTAT register) (FERCNFG register)

Flash EEE Error Type 0 Violation ERSVIF0 ERSVIE0 I Bit
(FERSTAT register) (FERCNFG register)

ECC Double Bit Fault on Flash Read DFDIF DFDIE I Bit
(FERSTAT register) (FERCNFG register)

ECC Single Bit Fault on Flash Read SFDIF SFDIE I Bit
(FERSTAT register) (FERCNFG register)

NOTE
Vector addresses and their relative interrupt priority are determined at the
MCU level.

******** Description of Flash Interrupt Operation
The Flash module uses the CCIF flag in combination with the CCIE interrupt enable bit to generate the
Flash command interrupt request. The Flash module uses the ERSEIF, PGMEIF, EPVIOLIF, ERSVIF1,
ERSVIF0, DFDIF and SFDIF flags in combination with the ERSEIE, PGMEIE, EPVIOLIE, ERSVIE1,
ERSVIE0, DFDIE and SFDIE interrupt enable bits to generate the Flash error interrupt request. For a
detailed description of the register bits involved, refer to Section ********, “Flash Configuration Register
(FCNFG)”, Section ********, “Flash Error Configuration Register (FERCNFG)”, Section ********, “Flash
Status Register (FSTAT)”, and Section ********, “Flash Error Status Register (FERSTAT)”.

The logic used for generating the Flash module interrupts is shown in Figure 29-27.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1197



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

CCIE Flash Command Interrupt Request
CCIF

ERSERIE
ERSERIF

PGMERIE
PGMERIF

EPVIOLIE
EPVIOLIF

Flash Error Interrupt Request

ERSVIE1
ERSVIF1

ERSVIE0
ERSVIF0

DFDIE
DFDIF

SFDIE
SFDIF

Figure 29-27. Flash Module Interrupts Implementation

29.4.4 Wait Mode
The Flash module is not affected if the MCU enters wait mode. The Flash module can recover the MCU
from wait via the CCIF interrupt (see Section 29.4.3, “Interrupts”).

29.4.5 Stop Mode
If a Flash command is active (CCIF = 0) or an EE-Emulation operation is pending when the MCU requests
stop mode, the current Flash operation will be completed before the CPU is allowed to enter stop mode.

29.5 Security
The Flash module provides security information to the MCU. The Flash security state is defined by the
SEC bits of the FSEC register (see Table 29-12). During reset, the Flash module initializes the FSEC
register using data read from the security byte of the Flash configuration field at global address
0x7F_FF0F.

MC9S12XE-Family Reference Manual  Rev. 1.25

1198 Freescale Semiconductor



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

The security state out of reset can be permanently changed by programming the security byte of the Flash
configuration field. This assumes that you are starting from a mode where the necessary P-Flash erase and
program commands are available and that the upper region of the P-Flash is unprotected. If the Flash
security byte is successfully programmed, its new value will take affect after the next MCU reset.

The following subsections describe these security-related subjects:
• Unsecuring the MCU using Backdoor Key Access
• Unsecuring the MCU in Special Single Chip Mode using BDM
• Mode and Security Effects on Flash Command Availability

29.5.1 Unsecuring the MCU using Backdoor Key Access
The MCU may be unsecured by using the backdoor key access feature which requires knowledge of the
contents of the backdoor keys (four 16-bit words programmed at addresses 0x7F_FF00–0x7F_FF07). If
the KEYEN[1:0] bits are in the enabled state (see Section ********), the Verify Backdoor Access Key
command (see Section ********2) allows the user to present four prospective keys for comparison to the
keys stored in the Flash memory via the Memory Controller. If the keys presented in the Verify Backdoor
Access Key command match the backdoor keys stored in the Flash memory, the SEC bits in the FSEC
register (see Table 29-12) will be changed to unsecure the MCU. Key values of 0x0000 and 0xFFFF are
not permitted as backdoor keys. While the Verify Backdoor Access Key command is active, P-Flash block
0 will not be available for read access and will return invalid data.

The user code stored in the P-Flash memory must have a method of receiving the backdoor keys from an
external stimulus. This external stimulus would typically be through one of the on-chip serial ports.

If the KEYEN[1:0] bits are in the enabled state (see Section ********), the MCU can be unsecured by the
backdoor key access sequence described below:

1. Follow the command sequence for the Verify Backdoor Access Key command as explained in
Section ********2

2. If the Verify Backdoor Access Key command is successful, the MCU is unsecured and the
SEC[1:0] bits in the FSEC register are forced to the unsecure state of 10

The Verify Backdoor Access Key command is monitored by the Memory Controller and an illegal key will
prohibit future use of the Verify Backdoor Access Key command. A reset of the MCU is the only method
to re-enable the Verify Backdoor Access Key command.

After the backdoor keys have been correctly matched, the MCU will be unsecured. After the MCU is
unsecured, the sector containing the Flash security byte can be erased and the Flash security byte can be
reprogrammed to the unsecure state, if desired.

In the unsecure state, the user has full control of the contents of the backdoor keys by programming
addresses 0x7F_FF00–0x7F_FF07 in the Flash configuration field.

The security as defined in the Flash security byte (0x7F_FF0F) is not changed by using the Verify
Backdoor Access Key command sequence. The backdoor keys stored in addresses
0x7F_FF00–0x7F_FF07 are unaffected by the Verify Backdoor Access Key command sequence. After the
next reset of the MCU, the security state of the Flash module is determined by the Flash security byte

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1199



Chapter 29 1024 KByte Flash Module (S12XFTM1024K5V2)

(0x7F_FF0F). The Verify Backdoor Access Key command sequence has no effect on the program and
erase protections defined in the Flash protection register, FPROT.

29.5.2 Unsecuring the MCU in Special Single Chip Mode using BDM
The MCU can be unsecured in special single chip mode by erasing the P-Flash and D-Flash memory by
one of the following methods:

• Reset the MCU into special single chip mode, delay while the erase test is performed by the BDM,
send BDM commands to disable protection in the P-Flash and D-Flash memory, and execute the
Erase All Blocks command write sequence to erase the P-Flash and D-Flash memory.

• Reset the MCU into special expanded wide mode, disable protection in the P-Flash and D-Flash
memory and run code from external memory to execute the Erase All Blocks command write
sequence to erase the P-Flash and D-Flash memory.

After the CCIF flag sets to indicate that the Erase All Blocks operation has completed, reset the MCU into
special single chip mode. The BDM will execute the Erase Verify All Blocks command write sequence to
verify that the P-Flash and D-Flash memory is erased. If the P-Flash and D-Flash memory are verified as
erased the MCU will be unsecured. All BDM commands will be enabled and the Flash security byte may
be programmed to the unsecure state by the following method:

• Send BDM commands to execute a ‘Program P-Flash’ command sequence to program the Flash
security byte to the unsecured state and reset the MCU.

29.5.3 Mode and Security Effects on Flash Command Availability
The availability of Flash module commands depends on the MCU operating mode and security state as
shown in Table 29-30.

29.6 Initialization
On each system reset the Flash module executes a reset sequence which establishes initial values for the
Flash Block Configuration Parameters, the FPROT and DFPROT protection registers, and the FOPT and
FSEC registers. The Flash module reverts to built-in default values that leave the module in a fully
protected and secured state if errors are encountered during execution of the reset sequence. If a double bit
fault is detected during the reset sequence, both MGSTAT bits in the FSTAT register will be set. The
ACCERR bit in the FSTAT register is set if errors are encountered while initializing the EEE buffer ram
during the reset sequence.

CCIF remains clear throughout the reset sequence. The Flash module holds off all CPU access for the
initial portion of the reset sequence. While Flash reads are possible when the hold is removed, writes to
the FCCOBIX, FCCOBHI, and FCCOBLO registers are ignored to prevent command activity while the
Memory Controller remains busy. Completion of the reset sequence is marked by setting CCIF high which
enables writes to the FCCOBIX, FCCOBHI, and FCCOBLO registers to launch any available Flash
command.

If a reset occurs while any Flash command is in progress, that command will be immediately aborted. The
state of the word being programmed or the sector/block being erased is not guaranteed.

MC9S12XE-Family Reference Manual  Rev. 1.25

1200 Freescale Semiconductor