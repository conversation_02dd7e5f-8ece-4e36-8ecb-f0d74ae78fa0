<UserControl x:Class="VolvoFlashWR.UI.Views.EnhancedDiagnosticsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:VolvoFlashWR.UI.Views"
             xmlns:viewmodels="clr-namespace:VolvoFlashWR.UI.ViewModels"
             xmlns:lvc="clr-namespace:LiveChartsCore.SkiaSharpView.WPF;assembly=LiveChartsCore.SkiaSharpView.WPF"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <UserControl.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="120"/>
        </Style>

        <Style x:Key="InfoLabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,2"/>
        </Style>

        <Style x:Key="InfoValueStyle" TargetType="TextBlock">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,2"/>
        </Style>

        <Style x:Key="ProgressBarStyle" TargetType="ProgressBar">
            <Setter Property="Height" Value="15"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Grid Grid.Row="0" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- ECU Selection and Connection Status -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="Enhanced ECU Diagnostics" FontSize="20" FontWeight="Bold" Margin="0,0,0,10"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="Selected ECU:" Style="{StaticResource InfoLabelStyle}"/>
                        <ComboBox Grid.Column="1" ItemsSource="{Binding ConnectedECUs}"
                                  SelectedItem="{Binding SelectedECUForDiagnostics}"
                                  DisplayMemberPath="Name" Margin="5,2"/>

                        <TextBlock Grid.Column="2" Text="Status:" Style="{StaticResource InfoLabelStyle}" Margin="15,2,5,2"/>
                        <Border Grid.Column="3" CornerRadius="3" Padding="5,2" Margin="5,2"
                                Background="{Binding SelectedECUForDiagnostics.ConnectionStatus, Converter={StaticResource ConnectionStatusToBrushConverter}}">
                            <TextBlock Text="{Binding SelectedECUForDiagnostics.ConnectionStatus}" Foreground="White"/>
                        </Border>
                    </Grid>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Vertical" Margin="10,0,0,0">
                    <Button Content="Refresh ECU List" Command="{Binding RefreshECUCommand}" Style="{StaticResource ActionButtonStyle}"/>
                    <Button Content="Connect" Command="{Binding ConnectToECUCommand}" Style="{StaticResource ActionButtonStyle}"/>
                    <Button Content="Disconnect" Command="{Binding DisconnectECUCommand}" Style="{StaticResource ActionButtonStyle}"/>
                </StackPanel>
            </Grid>

            <!-- ECU Details -->
            <Expander Grid.Row="1" Header="ECU Details" IsExpanded="True" Margin="0,10,0,0">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Grid Grid.Column="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="ECU Name:" Style="{StaticResource InfoLabelStyle}"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding SelectedECUForDiagnostics.Name}" Style="{StaticResource InfoValueStyle}"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Serial Number:" Style="{StaticResource InfoLabelStyle}"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SelectedECUForDiagnostics.SerialNumber}" Style="{StaticResource InfoValueStyle}"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Hardware Version:" Style="{StaticResource InfoLabelStyle}"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding SelectedECUForDiagnostics.HardwareVersion}" Style="{StaticResource InfoValueStyle}"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Software Version:" Style="{StaticResource InfoLabelStyle}"/>
                        <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding SelectedECUForDiagnostics.SoftwareVersion}" Style="{StaticResource InfoValueStyle}"/>
                    </Grid>

                    <Grid Grid.Column="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Protocol Type:" Style="{StaticResource InfoLabelStyle}"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding SelectedECUForDiagnostics.ProtocolType}" Style="{StaticResource InfoValueStyle}"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Last Connected:" Style="{StaticResource InfoLabelStyle}"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SelectedECUForDiagnostics.LastCommunicationTime, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" Style="{StaticResource InfoValueStyle}"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Diagnostic Mode:" Style="{StaticResource InfoLabelStyle}"/>
                        <ComboBox Grid.Row="2" Grid.Column="1" ItemsSource="{Binding DiagnosticModes}"
                                  SelectedItem="{Binding SelectedDiagnosticMode}" Margin="5,2"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Operating Mode:" Style="{StaticResource InfoLabelStyle}"/>
                        <ComboBox Grid.Row="3" Grid.Column="1" ItemsSource="{Binding OperatingModes}"
                                  SelectedItem="{Binding SelectedOperatingMode}" Margin="5,2"/>
                    </Grid>
                </Grid>
            </Expander>

            <!-- Action Buttons -->
            <Grid Grid.Row="2" Margin="0,10,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Button Grid.Column="0" Content="Read Faults" Command="{Binding ReadFaultsCommand}" Style="{StaticResource ActionButtonStyle}"/>
                <Button Grid.Column="1" Content="Clear Faults" Command="{Binding ClearFaultsCommand}" Style="{StaticResource ActionButtonStyle}"/>
                <Button Grid.Column="2" Content="Read Parameters" Command="{Binding ReadParametersCommand}" Style="{StaticResource ActionButtonStyle}"/>
                <Button Grid.Column="3" Content="Create Backup" Command="{Binding CreateBackupCommand}" Style="{StaticResource ActionButtonStyle}"/>
            </Grid>
        </Grid>

        <!-- Diagnostic Results -->
        <TabControl Grid.Row="1" Margin="10">
            <!-- Active Faults Tab -->
            <TabItem Header="Active Faults">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <DataGrid Grid.Row="0" ItemsSource="{Binding ActiveFaults}"
                              AutoGenerateColumns="False" IsReadOnly="True"
                              AlternatingRowBackground="#F5F5F5">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Code" Binding="{Binding Code}" Width="100"/>
                            <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="*"/>
                            <DataGridTextColumn Header="Severity" Binding="{Binding Severity}" Width="100"/>
                            <DataGridTextColumn Header="Timestamp" Binding="{Binding Timestamp, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" Width="150"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <TextBlock Grid.Row="1" Text="{Binding ActiveFaults.Count, StringFormat='Total Active Faults: {0}'}"
                               FontWeight="Bold" Margin="5"/>
                </Grid>
            </TabItem>

            <!-- Inactive Faults Tab -->
            <TabItem Header="Inactive Faults">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <DataGrid Grid.Row="0" ItemsSource="{Binding InactiveFaults}"
                              AutoGenerateColumns="False" IsReadOnly="True"
                              AlternatingRowBackground="#F5F5F5">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Code" Binding="{Binding Code}" Width="100"/>
                            <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="*"/>
                            <DataGridTextColumn Header="Severity" Binding="{Binding Severity}" Width="100"/>
                            <DataGridTextColumn Header="Timestamp" Binding="{Binding Timestamp, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" Width="150"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <TextBlock Grid.Row="1" Text="{Binding InactiveFaults.Count, StringFormat='Total Inactive Faults: {0}'}"
                               FontWeight="Bold" Margin="5"/>
                </Grid>
            </TabItem>

            <!-- Parameters Tab -->
            <TabItem Header="Parameters">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <DataGrid Grid.Row="0" ItemsSource="{Binding Parameters}"
                              AutoGenerateColumns="False"
                              AlternatingRowBackground="#F5F5F5">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Parameter" Binding="{Binding Key}" Width="200" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Value" Binding="{Binding Value}" Width="150"/>
                            <DataGridTextColumn Header="Unit" Binding="{Binding Unit}" Width="100" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="*" IsReadOnly="True"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right" Margin="5">
                        <Button Content="Refresh" Command="{Binding RefreshParametersCommand}" Style="{StaticResource ActionButtonStyle}"/>
                        <Button Content="Write Parameters" Command="{Binding WriteParametersCommand}" Style="{StaticResource ActionButtonStyle}"/>
                        <Button Content="Export..." Command="{Binding ExportParametersCommand}" Style="{StaticResource ActionButtonStyle}"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <!-- Diagnostic Results Tab -->
            <TabItem Header="Diagnostic Results">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Diagnostic Summary -->
                    <GroupBox Grid.Row="0" Header="Diagnostic Summary" Margin="5">
                        <Grid Margin="5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Grid Grid.Column="0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="ECU Name:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding DiagnosticData.ECUName}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="Diagnostic Time:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding DiagnosticData.Timestamp, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="Session Duration:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding DiagnosticData.SessionDurationMs, StringFormat='{}{0:N0} ms'}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="Status:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding DiagnosticData.IsSuccessful, Converter={StaticResource BoolToSuccessConverter}}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="Diagnostic Mode:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding DiagnosticData.DiagnosticMode}" Style="{StaticResource InfoValueStyle}"/>
                            </Grid>

                            <Grid Grid.Column="1">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="Active Faults:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding DiagnosticData.ActiveFaults.Count}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="Inactive Faults:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding DiagnosticData.InactiveFaults.Count}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="Operating Mode:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding DiagnosticData.OperatingMode}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="Connection Type:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding DiagnosticData.ConnectionType}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="Protocol:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding DiagnosticData.ProtocolType}" Style="{StaticResource InfoValueStyle}"/>
                            </Grid>
                        </Grid>
                    </GroupBox>

                    <!-- Detailed Diagnostic Results -->
                    <DataGrid Grid.Row="1" ItemsSource="{Binding DiagnosticData.Parameters}"
                              AutoGenerateColumns="False" IsReadOnly="True"
                              AlternatingRowBackground="#F5F5F5" Margin="5">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Parameter" Binding="{Binding Key}" Width="200"/>
                            <DataGridTextColumn Header="Value" Binding="{Binding Value}" Width="150"/>
                            <DataGridTextColumn Header="Unit" Binding="{Binding Unit}" Width="80"/>
                            <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="*"/>
                            <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="5">
                        <Button Content="Export Results" Command="{Binding ExportDiagnosticResultsCommand}" Style="{StaticResource ActionButtonStyle}"/>
                        <Button Content="Print Report" Command="{Binding PrintDiagnosticReportCommand}" Style="{StaticResource ActionButtonStyle}"/>
                        <Button Content="Save Snapshot" Command="{Binding SaveDiagnosticSnapshotCommand}" Style="{StaticResource ActionButtonStyle}"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <!-- Real-time Monitoring Tab -->
            <TabItem Header="Monitoring">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Monitoring Controls -->
                    <Grid Grid.Row="0" Margin="5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Real-time Parameter Monitoring" Style="{StaticResource SectionHeaderStyle}"/>
                            <TextBlock Text="Monitor ECU parameters in real-time. Select parameters to monitor and set the refresh interval."
                                       TextWrapping="Wrap" Margin="0,0,0,10"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="Refresh Interval:" Style="{StaticResource InfoLabelStyle}"/>
                                <ComboBox Grid.Column="1" ItemsSource="{Binding RefreshIntervals}"
                                          SelectedItem="{Binding SelectedRefreshInterval}" Margin="5,2"/>

                                <TextBlock Grid.Column="2" Text="Max Points:" Style="{StaticResource InfoLabelStyle}" Margin="15,2,5,2"/>
                                <ComboBox Grid.Column="3" ItemsSource="{Binding MaxDataPoints}"
                                          SelectedItem="{Binding SelectedMaxDataPoints}" Margin="5,2"/>
                            </Grid>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Vertical" Margin="10,0,0,0">
                            <Button Content="Start Monitoring" Command="{Binding StartMonitoringCommand}" Style="{StaticResource ActionButtonStyle}"/>
                            <Button Content="Stop Monitoring" Command="{Binding StopMonitoringCommand}" Style="{StaticResource ActionButtonStyle}"/>
                            <Button Content="Clear Data" Command="{Binding ClearMonitoringDataCommand}" Style="{StaticResource ActionButtonStyle}"/>
                        </StackPanel>
                    </Grid>

                    <!-- Monitoring Charts -->
                    <TabControl Grid.Row="1" Margin="5">
                        <TabItem Header="Chart View">
                            <Border BorderBrush="#DDDDDD" BorderThickness="1" Margin="5">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                                        <TextBlock Text="Select Parameters:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                        <ComboBox ItemsSource="{Binding MonitorableParameters}"
                                                  SelectedItem="{Binding SelectedParameterForChart}"
                                                  Width="200" Margin="5,2"/>
                                        <Button Content="Add to Chart" Command="{Binding AddParameterToChartCommand}"
                                                Padding="10,2" Margin="5,2"/>
                                        <Button Content="Remove from Chart" Command="{Binding RemoveParameterFromChartCommand}"
                                                Padding="10,2" Margin="5,2"/>
                                    </StackPanel>

                                    <!-- LiveCharts control -->
                                    <Border Grid.Row="1" Background="#F9F9F9" Margin="5">
                                        <Grid>
                                            <lvc:CartesianChart
                                                Series="{Binding ChartSeries}"
                                                XAxes="{Binding XAxes}"
                                                YAxes="{Binding YAxes}"
                                                TooltipPosition="Top"
                                                ZoomMode="X"
                                                Margin="5">
                                                <lvc:CartesianChart.LegendPosition>Top</lvc:CartesianChart.LegendPosition>
                                            </lvc:CartesianChart>
                                        </Grid>
                                    </Border>
                                </Grid>
                            </Border>
                        </TabItem>

                        <TabItem Header="Data View">
                            <DataGrid ItemsSource="{Binding MonitoringData}"
                                      AutoGenerateColumns="False" IsReadOnly="True"
                                      AlternatingRowBackground="#F5F5F5" Margin="5">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="Timestamp" Binding="{Binding Timestamp, StringFormat='{}{0:HH:mm:ss.fff}'}" Width="120"/>
                                    <DataGridTextColumn Header="Parameter" Binding="{Binding ParameterName}" Width="150"/>
                                    <DataGridTextColumn Header="Value" Binding="{Binding Value}" Width="100"/>
                                    <DataGridTextColumn Header="Unit" Binding="{Binding Unit}" Width="80"/>
                                    <DataGridTextColumn Header="Min" Binding="{Binding MinValue}" Width="80"/>
                                    <DataGridTextColumn Header="Max" Binding="{Binding MaxValue}" Width="80"/>
                                    <DataGridTextColumn Header="Average" Binding="{Binding AverageValue}" Width="80"/>
                                    <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="*"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </TabItem>
                    </TabControl>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="5">
                        <Button Content="Export Data" Command="{Binding ExportMonitoringDataCommand}" Style="{StaticResource ActionButtonStyle}"/>
                        <Button Content="Save Configuration" Command="{Binding SaveMonitoringConfigCommand}" Style="{StaticResource ActionButtonStyle}"/>
                        <Button Content="Load Configuration" Command="{Binding LoadMonitoringConfigCommand}" Style="{StaticResource ActionButtonStyle}"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <!-- Backups Tab -->
            <TabItem Header="Backups">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Backup Controls -->
                    <Grid Grid.Row="0" Margin="5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Vertical">
                            <TextBlock Text="ECU Backups" FontSize="16" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="Manage backups for the selected ECU. Create new backups, restore existing ones, or schedule automatic backups."
                                       TextWrapping="Wrap" Margin="0,0,0,10"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="Filter:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBox Grid.Column="1" Text="{Binding BackupFilter, UpdateSourceTrigger=PropertyChanged}" Margin="5,2"/>
                            </Grid>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Vertical" Margin="10,0,0,0">
                            <Button Content="Create Backup" Command="{Binding CreateBackupCommand}" Style="{StaticResource ActionButtonStyle}"/>
                            <Button Content="Restore Backup" Command="{Binding RestoreBackupCommand}" Style="{StaticResource ActionButtonStyle}"/>
                            <Button Content="Manage Schedules" Command="{Binding ManageSchedulesCommand}" Style="{StaticResource ActionButtonStyle}"/>
                        </StackPanel>
                    </Grid>

                    <!-- Backup List -->
                    <DataGrid Grid.Row="1" ItemsSource="{Binding Backups}"
                              SelectedItem="{Binding SelectedBackup}"
                              AutoGenerateColumns="False"
                              AlternatingRowBackground="#F5F5F5" Margin="5">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Name" Binding="{Binding ECUName}" Width="150"/>
                            <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="200"/>
                            <DataGridTextColumn Header="Created" Binding="{Binding CreationTime, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" Width="150"/>
                            <DataGridTextColumn Header="Version" Binding="{Binding Version}" Width="80"/>
                            <DataGridTextColumn Header="Size" Binding="{Binding TotalSizeBytes, StringFormat='{}{0:N0} bytes'}" Width="100"/>
                            <DataGridTextColumn Header="Category" Binding="{Binding Category}" Width="100"/>
                            <DataGridCheckBoxColumn Header="Latest" Binding="{Binding IsLatestVersion}" Width="60" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Created By" Binding="{Binding CreatedBy}" Width="120"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Backup Details -->
                    <Expander Grid.Row="2" Header="Backup Details" IsExpanded="False" Margin="5">
                        <Grid Margin="5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Grid Grid.Column="0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="Backup ID:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding SelectedBackup.Id}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="ECU Serial:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SelectedBackup.ECUSerialNumber}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="Hardware Version:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding SelectedBackup.ECUHardwareVersion}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="Software Version:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding SelectedBackup.ECUSoftwareVersion}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="Version Notes:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding SelectedBackup.VersionNotes}" Style="{StaticResource InfoValueStyle}" TextWrapping="Wrap"/>
                            </Grid>

                            <Grid Grid.Column="1">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="EEPROM Size:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding SelectedBackup.EEPROMData.Length, StringFormat='{}{0:N0} bytes'}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="MCU Code Size:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SelectedBackup.MicrocontrollerCode.Length, StringFormat='{}{0:N0} bytes'}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="Parameters Count:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding SelectedBackup.Parameters.Count}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="Last Modified:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding SelectedBackup.LastModifiedTime, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" Style="{StaticResource InfoValueStyle}"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="Modified By:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding SelectedBackup.LastModifiedBy}" Style="{StaticResource InfoValueStyle}"/>
                            </Grid>
                        </Grid>
                    </Expander>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
