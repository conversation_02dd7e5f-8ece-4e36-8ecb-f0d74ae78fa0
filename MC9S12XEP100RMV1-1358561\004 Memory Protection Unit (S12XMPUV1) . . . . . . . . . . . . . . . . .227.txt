﻿Chapter 4
Memory Protection Unit (S12XMPUV1)

Table 4-1. Revision History

Revision Sections
Revision Date Description of Changes

Number Affected
- Added note to only use the CPU to clear the AE flag.

*******/4-231
V01.04 14 Sep 2005 - Added disclaimer to avoid changing descriptors while they are in use

4.4.1/4-237
because of other bus-masters doing accesses.

*******/4-231 - Clarified that interrupt generation is independent of AEF bit state.
V01.05 14 Mar 2006

4.4/4-237 - Corrected preliminary statement about execution of violating accesses.
V01.06 09 Oct 2006 - Made Revision History entries public.

4.1 Introduction
The MPU module provides basic functionality required to protect memory mapped resources from
undesired accesses. Multiple address range comparators compare memory accesses against eight memory
protection descriptors located in the MPU module to determine if each access is valid or not. The
comparison is sensitive to which bus master generates the access and the type of the access.

The MPU module can be used to isolate memory ranges accessible by different bus masters. It can be also
be used by an operating system or software kernel to isolate the regions of memory “legally” available to
specific software tasks, with the kernel re-configuring the task specific memory protection descriptors in
supervisor state during task-switching.

4.1.1 Preface
The following terms and abbreviations are used in the document.

Table 4-2. Terminology

Term Meaning
MCU Micro-Controller Unit
MPU Memory Protection Unit
CPU S12X Central Processing Unit (see S12XCPU Reference Manual)

XGATE XGATE Co-processor (see XGATE chapter)
supervisor state refers to the supervisor state of the S12XCPU (see S12XCPU Reference Manual)

user state refers to the user state of the S12XCPU (see S12XCPU Reference Manual)

4.1.2 Overview
The MPU module monitors the bus activity of each bus master. The data describing each access is fed into
multiple address range comparators. The output of the comparators is used to determine if a particular

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 227



Chapter 4 Memory Protection Unit (S12XMPUV1)

access is allowed or represents an access violation. If an access violation caused by the S12X CPU is
detected, the MPU module raises an access violation interrupt. If the MPU module detects an access
violation caused by a bus master other than the S12X CPU, it flags an access error condition to the
respective master. In addition to the restrictions defined for memory ranges in the MPU descriptors,
accesses to memory not covered by any MPU descriptor (even read accesses!) are considered access
violations.

Figure 4-1 shows a block diagram of the MPU module.

MPU
Data Access MPU Monitoring

Access Validation

Op-code Fetch

CPU

Data Access MPU Monitoring
Access Validation

Op-code Fetch

XGATE

MPU Monitoring
Access Validation

Data Access

Status
Registers

“Master3” MMC

Access Violation
Interrupt

Figure 4-1. Block Diagram

4.1.3 Features
• Protects memory from undesired accesses coming from up to 3 bus masters1

• Eight memory protection descriptors
— each descriptor can cover the full global memory map (8 MBytes)
— each descriptor has a granularity of 8 Bytes

1. Master 3 can be implemented or left out depending the chip configuration. Please refer to the Device Reference Manual for
information about the availability and function of Master 3.

MC9S12XE-Family Reference Manual  Rev. 1.25

228 Freescale Semiconductor

Bus Interface Bus Interface Bus Interface

Bus Interface Bus Interface Bus Interface

Comparators Comparators Comparators

Protection Protection
Descriptors Descriptors



Chapter 4 Memory Protection Unit (S12XMPUV1)

• Each descriptor can be configured to allow one of four types of access privilege for the defined
memory region
— Bus master has full access (read, write and execute enabled)
— Bus master can read and execute (write illegal)
— Bus master can read and write (execution illegal)
— Bus master can only read (write and execution illegal)

• Accesses to memory not covered by any protection descriptor will cause an access violation

4.1.4 Modes of Operation
The MPU module can be used in all MCU modes.

4.2 External Signal Description
The MPU module has no external signals.

4.3 Memory Map and Register Definition
This section provides a detailed description of address space and registers used by the MPU module.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 229



Chapter 4 Memory Protection Unit (S12XMPUV1)

4.3.1 Register Descriptions
This section describes in address order all the MPU module registers and their individual bits.

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0000 R WPF NEXF 0 0 0 0 SVSF
MPUFLG AEF

W

0x0001 R 0 ADDR[22:16]
MPUASTAT0 W

0x0002 R ADDR[15:8]
MPUASTAT1 W

0x0003 R ADDR[7:0]
MPUASTAT2 W

0x0004 R 0 0 0 0 0 0 0 0
Reserved W

0x0005 R 0 0 0 0
MPUSEL SVSEN SEL[2:0]

W

0x0006 R
MPUDESC0(1) MSTR0 MSTR1 MSTR2 MSTR3 LOW_ADDR[22:19]

W

0x0007 R
MPUDESC11 LOW_ADDR[18:11]

W

0x0008 R
MPUDESC21 LOW_ADDR[10:3]

W

0x0009 R 0 0
MPUDESC31 WP NEX HIGH_ADDR[22:19]

W

0x000A R
MPUDESC41 HIGH_ADDR[18:11]

W

0x000B R
MPUDESC51 HIGH_ADDR[10:3]

W

= Unimplemented or Reserved
1. The module addresses 0x0006−0x000B represent a window in the register map through which different descriptor registers

are visible.

Figure 4-2. MPU Register Summary

MC9S12XE-Family Reference Manual  Rev. 1.25

230 Freescale Semiconductor



Chapter 4 Memory Protection Unit (S12XMPUV1)

******* MPU Flag Register (MPUFLG)
Address: Module Base + 0x0000

7 6 5 4 3 2 1 0
R WPF NEXF 0 0 0 0 SVSF

AEF
W

Reset 0 0 0 0 0 0 0 0

Figure 4-3. MPU Flag Register (MPUFLG)

Read: Anytime

Write: Write of 1 clears flag, write of 0 ignored
Table 4-3. MPUFLG Field Descriptions

Field Description

7 Access Error Flag — This bit is the CPU access error interrupt flag. It is set if a CPU access violation has
AEF occurred. At the same time this bit is set, all the other status flags in this register and the access violation

address bits in the MPUASTATn registers are captured. Clear this flag by writing a one.
Note: If a CPU access error is flagged and both the WPF bit and the NEXF bit are zero, the access violation

was caused by an access to memory not covered by the MPU descriptors.
Note: While this bit is set, the CPU in supervisor state (“Master 0”) can read from and write to the peripheral

register space even if there is no memory protection descriptor explicitly allowing this. This is to prevent
the case that the CPU cannot clear the AEF bit if the registers are write protected for the CPU in
supervisor state.

Note: This bit should only be cleared by an access from the S12X CPU. Otherwise, when using one of the
other masters (such as the XGATE) to clear this bit, the status flags and the address status registers
may not get updated correctly if a CPU access causes a violation in the same bus cycle.

6 Write-Protect Violation Flag — This flag is set if the current CPU access violation has occurred because of
WPF an attempt to write to memory configured as read-only. The WPF bit is read-only; it will be automatically

updated when the next access violation is flagged with the AEF bit.

5 No-Execute Violation Flag — This bit is set if the current CPU access violation has occurred because of an
NEXF attempt to fetch code from memory configured as No-Execute. The NEXF bit is read-only; it will be

automatically updated when the next access violation is flagged with the AEF bit.

0 Supervisor State Flag — This bit is set if the current CPU access violation occurred while the CPU was in
SVSF supervisor state. This bit is cleared if the current CPU access violation occurred while the CPU was in user

state. The supervisor state flag is read-only; it will be automatically updated when the next CPU access
violation is flagged with the AEF bit.

If the AEF bit is set further violations are not captured into the MPU status registers. The status of the AEF
bit has no effect on the access restrictions, i.e. access restrictions for all masters are still enforced if the
AEF bit is set. Also, the non-maskable hardware interrupt for violating accesses coming from the S12X
CPU is generated regardless of the state of the AEF bit.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 231



Chapter 4 Memory Protection Unit (S12XMPUV1)

******* MPU Address Status Register 0 (MPUASTAT0)
Address: Module Base + 0x0001

7 6 5 4 3 2 1 0
R 0 ADDR[22:16]
W

Reset 0 0 0 0 0 0 0 0

Figure 4-4. MPU Address Status Register 0 (MPUASTAT0)

Read: Anytime

Write: Never
Table 4-4. MPUASTAT0 Field Descriptions

Field Description

6–0 Access violation address bits — The ADDR[22:16] bits contain bits [22:16] of the global address which
ADDR[22:16] caused the current access violation interrupt. These bits are undefined if the access error flag bit (AEF) in the

MPUFLG register is not set.

******* MPU Address Status Register 1 (MPUASTAT1)
Address: Module Base + 0x0002

7 6 5 4 3 2 1 0
R ADDR[15:8]
W

Reset 0 0 0 0 0 0 0 0

Figure 4-5. MPU Address Status Register 1 (MPUASTAT1)

Read: Anytime

Write: Never
Table 4-5. MPUASTAT1 Field Descriptions

Field Description

7–0 Access violation address bits — The ADDR[15:8] bits contain bits [15:8] of the global address which caused
ADDR[15:8] the current access violation interrupt. These bits are undefined if the access error flag bit (AEF) in the MPUFLG

register is not set.

MC9S12XE-Family Reference Manual  Rev. 1.25

232 Freescale Semiconductor



Chapter 4 Memory Protection Unit (S12XMPUV1)

******* MPU Address Status Register 2 (MPUASTAT2)
Address: Module Base + 0x0003

7 6 5 4 3 2 1 0
R ADDR[7:0]
W

Reset 0 0 0 0 0 0 0 0

Figure 4-6. MPU Address Status Register (MPUASTAT2)

Read: Anytime

Write: Never
Table 4-6. MPUASTAT2 Field Descriptions

Field Description

7–0 Access violation address bits — The ADDR[7:0] bits contain bits [7:0] of the global address which caused
ADDR[7:0] the current access violation interrupt. These bits are undefined if the access error flag bit (AEF) in the MPUFLG

register is not set.

******* MPU Descriptor Select Register (MPUSEL)
Address: Module Base + 0x0005

7 6 5 4 3 2 1 0
R 0 0 0 0

SVSEN SEL[2:0]
W

Reset 0 0 0 0 0 0 0 0

Figure 4-7. MPU Descriptor Select Register (MPUSEL)

Read: Anytime

Write: Anytime
Table 4-7. MPUSEL Field Descriptions

Field Description

7 MPU supervisor state enable bit — This bit enables the memory protection for the CPU in supervisor state.
SVSEN If this bit is cleared, the MPU does not affect any accesses coming from the CPU in supervisor state. This is to

prevent the CPU from locking out itself while configuring the protection descriptors (during initialization after a
system reset and during the update of the protection descriptors for a task switch). The memory protection
functionality for the other bus-masters is unaffected by this bit.
0 MPU is disabled for the CPU in supervisor state
1 MPU is enabled for the CPU in supervisor state

2–0 Descriptor select bits — The SEL[2:0] bits select which descriptor is visible in the MPU Descriptor Register
SEL[2:0] window (MPUDESC0—MPUDESC5).

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 233



Chapter 4 Memory Protection Unit (S12XMPUV1)

******* MPU Descriptor Register 0 (MPUDESC0)
Address: Module Base + 0x0006

7 6 5 4 3 2 1 0
R

MSTR0 MSTR1 MSTR2 MSTR3 LOW_ADDR[22:19]
W

Reset 1(1) 11 11 1(2) 0 0 0 0
1. initialized as set for descriptor 0 only, cleared for all others
2. initialized as set for descriptor 0 only, if MSTR3 is implemented on the device

Figure 4-8. MPU Descriptor Register 0 (MPUDESC0)

Read: Anytime

Write: Anytime
Table 4-8. MPUDESC0 Field Descriptions

Field Description

7 Master 0 select bit — If this bit is set the descriptor is valid for bus master 0 (CPU in supervisor state).
MSTR0

6 Master 1 select bit — If this bit is set the descriptor is valid for bus master 1 (CPU in user state).
MSTR1

5 Master 2 select bit — If this bit is set the descriptor is valid for bus master 2 (XGATE).
MSTR2

4 Master 3 select bit — If this bit is set the descriptor is valid for bus master 3.
MSTR3

3–0 Memory range lower boundary address bits — The LOW_ADDR[22:19] bits represent bits [22:19] of the
LOW_ADDR[ global memory address that is used as the lower boundary for the described memory range.

22:19]

A descriptor can be configured as valid for more than one bus-master at the same time by setting multiple
Master select bits to one. Setting all Master select bits of a descriptor to zero disables the descriptor.

******* MPU Descriptor Register 1 (MPUDESC1)
Address: Module Base + 0x0007

7 6 5 4 3 2 1 0
R

LOW_ADDR[18:11]
W

Reset 0 0 0 0 0 0 0 0

Figure 4-9. MPU Descriptor Register 1 (MPUDESC1)

Read: Anytime

Write: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

234 Freescale Semiconductor



Chapter 4 Memory Protection Unit (S12XMPUV1)

Table 4-9. MPUDESC1 Field Descriptions

Field Description

7–0 Memory range lower boundary address bits — The LOW_ADDR[18:11] bits represent bits [18:11] of the
LOW_ADDR[ global memory address that is used as the lower boundary for the described memory range.

18:11]

******* MPU Descriptor Register 2 (MPUDESC2)
Address: Module Base + 0x0008

7 6 5 4 3 2 1 0
R

LOW_ADDR[10:3]
W

Reset 0 0 0 0 0 0 0 0

Figure 4-10. MPU Descriptor Register 2 (MPUDESC2)

Read: Anytime

Write: Anytime
Table 4-10. MPUDESC2 Field Descriptions

Field Description

7–0 Memory range lower boundary address bits — The LOW_ADDR[10:3] bits represent bits [10:3] of the global
LOW_ADDR[ memory address that is used as the lower boundary for the described memory range.

10:3]

******* MPU Descriptor Register 3 (MPUDESC3)
Address: Module Base + 0x0009

7 6 5 4 3 2 1 0
R 0 0

WP NEX HIGH_ADDR[22:19]
W

Reset 0 0 0 0 1 1 1 1

Figure 4-11. MPU Descriptor Register 3 (MPUDESC3)

Read: Anytime

Write: Anytime
Table 4-11. MPUDESC3 Field Descriptions

Field Description

7 Write-Protect bit — The WP bit causes the described memory range to be treated as write-protected. If this
WP bit is set every attempt to write in the described memory range causes an access violation.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 235



Chapter 4 Memory Protection Unit (S12XMPUV1)

Field Description

6 No-Execute bit — The NEX bit prevents the described memory range from being used as code memory. If this
NEX bit is set every Op-code fetch in this memory range causes an access violation.

3–0 Memory range upper boundary address bits — The HIGH_ADDR[22:19] bits represent bits [22:19] of the
HIGH_ADDR[ global memory address that is used as the upper boundary for the described memory range.

22:19]

*******0 MPU Descriptor Register 4 (MPUDESC4)
Address: Module Base + 0x000A

7 6 5 4 3 2 1 0
R

HIGH_ADDR[18:11]
W

Reset 1 1 1 1 1 1 1 1

Figure 4-12. MPU Descriptor Register 4 (MPUDESC4)

Read: Anytime

Write: Anytime
Table 4-12. MPUDESC4 Field Descriptions

Field Description

7–0 Memory range upper boundary address bits — The HIGH_ADDR[18:11] bits represent bits [18:11] of the
HIGH_ADDR[ global memory address that is used as the upper boundary for the described memory range.

18:11]

*******1 MPU Descriptor Register 5 (MPUDESC5)
Address: Module Base + 0x000B

7 6 5 4 3 2 1 0
R

HIGH_ADDR[10:3]
W

Reset 1 1 1 1 1 1 1 1

Figure 4-13. MPU Descriptor Register 5 (MPUDESC5)

Read: Anytime

Write: Anytime
Table 4-13. MPUDESC5 Field Descriptions

Field Description

7–0 Memory range upper boundary address bits — The HIGH_ADDR[10:3] bits represent bits [10:3] of the
HIGH_ADDR[ global memory address that is used as the upper boundary for the described memory range.

10:3]

MC9S12XE-Family Reference Manual  Rev. 1.25

236 Freescale Semiconductor



Chapter 4 Memory Protection Unit (S12XMPUV1)

4.4 Functional Description
The MPU module provides memory protection for accesses coming from multiple masters in the system.
This is done by monitoring bus traffic of each master and compare this with the configuration information
from a set of eight programmable descriptors located in the MPU module. If the MPU module detects an
access violation caused by the S12X CPU, it will assert the CPU access violation interrupt signal. If the
MPU module detects an access violation caused by a bus master other than the S12X CPU, it raises an
access error signal. Please refer to the documentation chapter of the individual master modules (i.e.
XGATE, etc.) for more information about the access error condition.

Violating accesses are not executed. The return value of a violating read access is undefined for both 8 bit
and 16 bit accesses.

NOTE
Accesses from BDM are not restricted. BDM hardware accesses always
bypass the MPU module. During execution of BDM firmware code S12X
CPU accesses are masked from the MPU module as well.

4.4.1 Protection Descriptors
Each of the eight protection descriptors can be used to restrict the allowed types of memory accesses for
a given memory range. Each of these memory ranges can cover up the entire 23 bits global memory range
(8 MBytes).

The descriptors are banked in the MPU module register map.

Each descriptor can be selected for modifying using the SEL bits in the MPU Descriptor Select (MPUSEL)
register.

Table 4-14 gives an overview of the types of accesses that can be configured using the protection
descriptors.

Table 4-14. Access Types

WP NEX Meaning

0 0 read, write and execute
0 1 read, write
1 0 read and execute
1 1 read only

The granularity of each descriptor is 8 bytes. This means the protection comparators in the MPU module
cover only address bits [22:3] of each access. The lower address bits [2:0] are ignored.

NOTE
A mis-aligned word access to the upper boundary address of a descriptor is
always flagged as an access violation.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 237



Chapter 4 Memory Protection Unit (S12XMPUV1)

NOTE
Configuring the lower boundary address of a descriptor to be higher than the
upper boundary address of a descriptor causes this descriptor to be ignored
by the comparator block. This effectively disables the descriptor.

NOTE
Avoid changing descriptors while they are in active use to validate accesses
from bus-masters. This can be done by temporarily disabling the affected
master during the update (XGATE, Master 3, switch S12X CPU states).
Otherwise accesses from bus-masters affected by a descriptor which is
updated concurrently could yield undefined results.

******* Overlapping Descriptors
If the memory ranges of two protection descriptors defined for the same bus-master overlap, the access
restrictions for the overlapped memory range are accumulated. For example:

• a memory protection descriptor defines memory range 0x40_0000−0x41_FFFF as WP=1, NEX=0
(read and execute)

• another descriptor defines memory range 0x41_0000−0x43_FFFF as WP=0, NEX=1 (read and
write)

• the resulting access rights for the overlapping range 0x41_0000−0x41_FFFF are WP=1, NEX=1
(read only)

******* Implicitly defined memory descriptors
As mentioned in the bit description of the Access Error Flag (AEF) in the MPUFLG register (Table 4-3),
there is an additional memory range implicitly defined only while the AEF bit is set: The CPU in
supervisor state can read from and write to the peripheral register space even if there is no memory
protection descriptor explicitly allowing this. This is to prevent the case that the CPU cannot clear the AEF
bit if the registers are write protected for the CPU in supervisor state.

The register address space containing the PAGE registers (EPAGE, RPAGE, GPAGE, PPAGE) at 0x0010−
0x0017 gets special treatment. It is defined like this:

• The S12X CPU can always read and write these registers, regardless of the configuration in the
descriptors.

• XGATE or Master3 (if available) are never allowed to read or write these registers, even if the
descriptor configuration allows accesses for other masters than the S12X CPU.

******* Op-code pre-fetch cycles and the NEX bit
Some bus-masters (CPU, XGATE) do a pre-fetch of program-code past the current instruction. The
S12XCPU pre-fetches two words past the current instruction, the XGATE pre-fetches one word, even if
the pre-fetched code is not executed. The MPU module has no way of knowing this at the time when the
pre-fetch cycles occur. Therefore this will result in an access violation if the op-code pre-fetch accesses a
memory range marked as “No-Execute” (NEX=1). This must be taken into account when defining memory

MC9S12XE-Family Reference Manual  Rev. 1.25

238 Freescale Semiconductor



Chapter 4 Memory Protection Unit (S12XMPUV1)

ranges with the NEX bit set adjacent to memory used for program code. The best way to do this would be
to leave some fill-bytes between the memory ranges in this case, i.e. do not set the upper memory boundary
to the address of the last op-code but to a following address which is at least two words (four bytes) away.

4.4.2 Interrupts
This section describes all interrupts originated by the MPU module.

******* Description of Interrupt Operation
The MPU module generates one interrupt request. It cannot be masked locally in the MPU module and is
meant to be used as the source of a non-maskable hardware interrupt request for the S12X CPU

Table 4-15. Interrupt vectors

Interrupt Source CCR Mask Local Enable

S12X CPU access error interrupt (AEF) − −

******* CPU Access Error Interrupt
An S12X CPU access error interrupt request is generated if the MPU module has detected an illegal
memory access originating from the S12X CPU. This is a non-maskable hardware interrupt. Due to the
non-maskable nature of this interrupt, the de-assertion of this interrupt request is coupled to the S12X CPU
interrupt vector fetch instead of the local access error flag (AEF). This means leaving the access error flag
(AEF) in the MPUFLG register set will not cause the same interrupt to be serviced again after leaving the
interrupt service routine with “RTI”. Instead, the interrupt request will be asserted again only when the
next illegal S12X CPU access is detected.

4.5 Initialization/Application Information

4.5.1 Initialization
After reset the MPU module is in an unconfigured state, with all eight protection descriptors covering the
whole memory map. The master bits are all set for descriptor “0” and cleared for all other descriptors. The
S12XCPU in supervisor state can access everything because the SVSEN bit in the MPUSEL register is
cleared by a system reset. After system reset every master has full access to the memory map because of
descriptor “0”.

In order to use the MPU module to protect memory ranges from undesired accesses, software needs to:
• Initialize the protection descriptors.
• Make sure there are meaningful interrupt service routines defined for the Access Violation

interrupts because these are non-maskable (See S12XINT chapter for details).
• Initialize peripherals and other masters for use (i.e. set-up XGATE, Master3 if applicable).
• Enable the MPU protection for the S12X CPU in supervisor state, if desired.
• Switch the S12X CPU to user state, if desired.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 239



Chapter 4 Memory Protection Unit (S12XMPUV1)

MC9S12XE-Family Reference Manual  Rev. 1.25

240 Freescale Semiconductor