using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Communication.Backup;

namespace VolvoFlashWR.UI.ViewModels
{
    public class BackupSchedulerViewModel : INotifyPropertyChanged
    {
        #region Private Fields

        private readonly ILoggingService _logger;
        private readonly IBackupService _backupService;
        private readonly IBackupSchedulerService _backupSchedulerService;
        private readonly IECUCommunicationService _ecuService;
        private ObservableCollection<BackupSchedule> _scheduledBackups;
        private ObservableCollection<ECUDevice> _availableECUs;
        private ObservableCollection<string> _backupCategories;
        private BackupSchedule _selectedSchedule;
        private BackupSchedule _currentSchedule;
        private bool _isEditMode;
        private bool _isNewSchedule;

        #endregion

        #region Properties

        public ObservableCollection<BackupSchedule> ScheduledBackups
        {
            get => _scheduledBackups;
            set
            {
                _scheduledBackups = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<ECUDevice> AvailableECUs
        {
            get => _availableECUs;
            set
            {
                _availableECUs = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<string> BackupCategories
        {
            get => _backupCategories;
            set
            {
                _backupCategories = value;
                OnPropertyChanged();
            }
        }

        public BackupSchedule SelectedSchedule
        {
            get => _selectedSchedule;
            set
            {
                _selectedSchedule = value;
                OnPropertyChanged();
                (EditScheduleCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (DeleteScheduleCommand as RelayCommand)?.RaiseCanExecuteChanged();
                (RunScheduleNowCommand as RelayCommand)?.RaiseCanExecuteChanged();
            }
        }

        public BackupSchedule CurrentSchedule
        {
            get => _currentSchedule;
            set
            {
                _currentSchedule = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsCustomFrequency));
            }
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                _isEditMode = value;
                OnPropertyChanged();
            }
        }

        public bool IsCustomFrequency => CurrentSchedule != null && CurrentSchedule.Frequency == (Core.Models.BackupFrequency)Core.Enums.BackupFrequency.Custom;

        public List<Core.Enums.BackupFrequency> ScheduleFrequencies { get; } = Enum.GetValues(typeof(Core.Enums.BackupFrequency))
                                                                        .Cast<Core.Enums.BackupFrequency>()
                                                                        .ToList();

        public List<string> TimeUnits { get; } = new List<string> { "Minutes", "Hours", "Days", "Weeks" };

        public List<int> Hours { get; } = Enumerable.Range(0, 24).ToList();

        public List<int> Minutes { get; } = Enumerable.Range(0, 60).ToList();

        public List<ScheduleType> ScheduleTypes { get; } = Enum.GetValues(typeof(ScheduleType))
                                                              .Cast<ScheduleType>()
                                                              .ToList();

        #endregion

        #region Commands

        public ICommand AddScheduleCommand { get; private set; }
        public ICommand EditScheduleCommand { get; private set; }
        public ICommand DeleteScheduleCommand { get; private set; }
        public ICommand SaveScheduleCommand { get; private set; }
        public ICommand CancelEditCommand { get; private set; }
        public ICommand RunScheduleNowCommand { get; private set; }
        public ICommand CloseCommand { get; private set; }

        #endregion

        #region Events

        public event EventHandler CloseRequested;
        public event PropertyChangedEventHandler? PropertyChanged;

        #endregion

        #region Constructor

        public BackupSchedulerViewModel(
            ILoggingService logger,
            IBackupService backupService,
            IBackupSchedulerService backupSchedulerService,
            IECUCommunicationService ecuService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));
            _backupSchedulerService = backupSchedulerService ?? throw new ArgumentNullException(nameof(backupSchedulerService));
            _ecuService = ecuService ?? throw new ArgumentNullException(nameof(ecuService));

            // Initialize collections
            _scheduledBackups = new ObservableCollection<BackupSchedule>();
            _availableECUs = new ObservableCollection<ECUDevice>();
            _backupCategories = new ObservableCollection<string>();

            // Initialize commands
            AddScheduleCommand = new RelayCommand(
                _ => AddSchedule(),
                _ => true);
            EditScheduleCommand = new RelayCommand(
                _ => EditSchedule(),
                _ => CanEditSchedule());
            DeleteScheduleCommand = new RelayCommand(
                _ => DeleteSchedule(),
                _ => CanDeleteSchedule());
            SaveScheduleCommand = new RelayCommand(
                _ => SaveSchedule(),
                _ => CanSaveSchedule());
            CancelEditCommand = new RelayCommand(
                _ => CancelEdit(),
                _ => true);
            RunScheduleNowCommand = new RelayCommand(
                _ => RunScheduleNow(),
                _ => CanRunScheduleNow());
            CloseCommand = new RelayCommand(
                _ => Close(),
                _ => true);

            // Load data
            LoadDataAsync();
        }

        #endregion

        #region Methods

        private async void LoadDataAsync()
        {
            try
            {
                // Load scheduled backups
                var schedules = await _backupSchedulerService.GetAllSchedulesAsync();
                ScheduledBackups.Clear();
                foreach (var schedule in schedules)
                {
                    ScheduledBackups.Add(schedule);
                }

                // Load available ECUs
                var ecus = await _ecuService.ScanForECUsAsync();
                AvailableECUs.Clear();
                foreach (var ecu in ecus)
                {
                    AvailableECUs.Add(ecu);
                }

                // Load backup categories
                var categories = await _backupService.GetPredefinedCategoriesAsync();
                BackupCategories.Clear();
                foreach (var category in categories)
                {
                    BackupCategories.Add(category);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error loading backup scheduler data", "BackupSchedulerViewModel", ex);
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddSchedule()
        {
            IsEditMode = true;
            _isNewSchedule = true;

            // Create a new schedule
            CurrentSchedule = new BackupSchedule
            {
                Id = Guid.NewGuid().ToString(),
                Name = "New Backup Schedule",
                Frequency = (Core.Models.BackupFrequency)Core.Enums.BackupFrequency.Daily,
                FrequencyType = BackupFrequencyType.Daily,
                StartDate = DateTime.Today,
                StartHour = DateTime.Now.Hour,
                StartMinute = 0,
                IncludeEEPROM = true,
                IncludeMicrocontrollerCode = true,
                IncludeParameters = true,
                IsEnabled = true,
                MaxBackupsToKeep = 7, // Default to keeping a week's worth of backups
                MaxBackupAge = 30 // Default to creating a new full backup after 30 days
            };

            // Set default ECU if available
            if (AvailableECUs.Count > 0)
            {
                CurrentSchedule.SelectedECU = AvailableECUs[0];
                CurrentSchedule.ECUId = AvailableECUs[0].Id;
                CurrentSchedule.ECUName = AvailableECUs[0].Name;
            }
        }

        private void EditSchedule()
        {
            if (SelectedSchedule == null)
                return;

            IsEditMode = true;
            _isNewSchedule = false;

            // Clone the selected schedule for editing
            CurrentSchedule = SelectedSchedule.Clone();

            // Set the selected ECU
            if (CurrentSchedule.ECUId != null)
            {
                CurrentSchedule.SelectedECU = AvailableECUs.FirstOrDefault(e => e.Id == CurrentSchedule.ECUId);
            }
        }

        private bool CanEditSchedule()
        {
            return SelectedSchedule != null;
        }

        private async void DeleteSchedule()
        {
            if (SelectedSchedule == null)
                return;

            var result = MessageBox.Show(
                $"Are you sure you want to delete the schedule '{SelectedSchedule.Name}'?",
                "Confirm Delete",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _backupSchedulerService.DeleteScheduleAsync(SelectedSchedule.Id);
                    ScheduledBackups.Remove(SelectedSchedule);
                    SelectedSchedule = null;
                }
                catch (Exception ex)
                {
                    _logger.LogError("Error deleting backup schedule", "BackupSchedulerViewModel", ex);
                    MessageBox.Show($"Error deleting schedule: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private bool CanDeleteSchedule()
        {
            return SelectedSchedule != null;
        }

        private async void SaveSchedule()
        {
            if (CurrentSchedule == null)
                return;

            // Validate schedule
            if (string.IsNullOrWhiteSpace(CurrentSchedule.Name))
            {
                MessageBox.Show("Schedule name cannot be empty.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (CurrentSchedule.SelectedECU == null)
            {
                MessageBox.Show("Please select an ECU for the backup schedule.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Update ECU information
            CurrentSchedule.ECUId = CurrentSchedule.SelectedECU.Id;
            CurrentSchedule.ECUName = CurrentSchedule.SelectedECU.Name;

            try
            {
                // Save the schedule
                if (_isNewSchedule)
                {
                    await _backupSchedulerService.AddScheduleAsync(CurrentSchedule);
                    ScheduledBackups.Add(CurrentSchedule);
                }
                else
                {
                    await _backupSchedulerService.UpdateScheduleAsync(CurrentSchedule);

                    // Update the item in the collection
                    var index = ScheduledBackups.IndexOf(ScheduledBackups.FirstOrDefault(s => s.Id == CurrentSchedule.Id));
                    if (index >= 0)
                    {
                        ScheduledBackups[index] = CurrentSchedule;
                    }
                }

                // Exit edit mode
                IsEditMode = false;
                SelectedSchedule = CurrentSchedule;
                CurrentSchedule = null;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error saving backup schedule", "BackupSchedulerViewModel", ex);
                MessageBox.Show($"Error saving schedule: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool CanSaveSchedule()
        {
            return CurrentSchedule != null &&
                   !string.IsNullOrWhiteSpace(CurrentSchedule.Name) &&
                   CurrentSchedule.SelectedECU != null;
        }

        private void CancelEdit()
        {
            IsEditMode = false;
            CurrentSchedule = null;
        }

        private async void RunScheduleNow()
        {
            if (SelectedSchedule == null)
                return;

            try
            {
                var result = await _backupSchedulerService.ExecuteScheduleAsync(SelectedSchedule);
                if (result != null)
                {
                    MessageBox.Show(
                        $"Backup created successfully for ECU {result.ECUName}.",
                        "Backup Created",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show(
                        "Failed to create backup. Check the logs for more information.",
                        "Backup Failed",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error executing backup schedule", "BackupSchedulerViewModel", ex);
                MessageBox.Show($"Error executing schedule: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool CanRunScheduleNow()
        {
            return SelectedSchedule != null;
        }

        private void Close()
        {
            CloseRequested?.Invoke(this, EventArgs.Empty);
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}

