@echo off
title VolvoFlashWR - Real Hardware Mode Setup
color 0A

echo ===============================================================================
echo                    VolvoFlashWR - Real Hardware Mode Setup
echo ===============================================================================
echo.
echo This script will:
echo   1. Enable Phoenix APCI real hardware communication
echo   2. Copy essential libraries for Vocom 1 adapter
echo   3. Set up environment for real hardware testing
echo   4. Launch the application in real hardware mode
echo.
echo ===============================================================================

REM Set critical environment variables for Phoenix APCI
set PHOENIX_VOCOM_ENABLED=true
set PHOENIX_DIAG_PATH=C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
set USE_DUMMY_IMPLEMENTATIONS=false

echo === Environment Configuration ===
echo PHOENIX_VOCOM_ENABLED=%PHOENIX_VOCOM_ENABLED%
echo PHOENIX_DIAG_PATH=%PHOENIX_DIAG_PATH%
echo USE_DUMMY_IMPLEMENTATIONS=%USE_DUMMY_IMPLEMENTATIONS%
echo.

echo === Copying Critical Phoenix APCI Libraries ===

REM Core APCI communication libraries (CRITICAL for real hardware)
echo [CRITICAL] Core APCI Libraries:
copy "Libraries\apci.dll" "." >nul 2>&1
if exist "apci.dll" (echo   ✓ apci.dll) else (echo   ✗ apci.dll - MISSING!)
copy "Libraries\apcidb.dll" "." >nul 2>&1
if exist "apcidb.dll" (echo   ✓ apcidb.dll) else (echo   ✗ apcidb.dll - MISSING!)
copy "Libraries\Rpci.dll" "." >nul 2>&1
if exist "Rpci.dll" (echo   ✓ Rpci.dll) else (echo   ✗ Rpci.dll - MISSING!)
copy "Libraries\Pc2.dll" "." >nul 2>&1
if exist "Pc2.dll" (echo   ✓ Pc2.dll) else (echo   ✗ Pc2.dll - MISSING!)

REM Vocom 1 adapter driver (CRITICAL for Vocom hardware)
echo.
echo [CRITICAL] Vocom 1 Adapter Driver:
copy "Libraries\WUDFPuma.dll" "." >nul 2>&1
if exist "WUDFPuma.dll" (echo   ✓ WUDFPuma.dll) else (echo   ✗ WUDFPuma.dll - MISSING!)
copy "Libraries\WUDFUpdate_01009.dll" "." >nul 2>&1
if exist "WUDFUpdate_01009.dll" (echo   ✓ WUDFUpdate_01009.dll) else (echo   ✗ WUDFUpdate_01009.dll - MISSING!)
copy "Libraries\WdfCoInstaller01009.dll" "." >nul 2>&1
if exist "WdfCoInstaller01009.dll" (echo   ✓ WdfCoInstaller01009.dll) else (echo   ✗ WdfCoInstaller01009.dll - MISSING!)

REM Phoenix integration libraries (ESSENTIAL for Phoenix APCI)
echo.
echo [ESSENTIAL] Phoenix Integration:
copy "Libraries\PhoenixESW.dll" "." >nul 2>&1
if exist "PhoenixESW.dll" (echo   ✓ PhoenixESW.dll) else (echo   ✗ PhoenixESW.dll - MISSING!)
copy "Libraries\PhoenixGeneral.dll" "." >nul 2>&1
if exist "PhoenixGeneral.dll" (echo   ✓ PhoenixGeneral.dll) else (echo   ✗ PhoenixGeneral.dll - MISSING!)
copy "Libraries\PhoenixProducInformation.dll" "." >nul 2>&1
if exist "PhoenixProducInformation.dll" (echo   ✓ PhoenixProducInformation.dll) else (echo   ✗ PhoenixProducInformation.dll - MISSING!)

REM Volvo APCI libraries (REQUIRED for Volvo-specific communication)
echo.
echo [REQUIRED] Volvo APCI Communication:
copy "Libraries\Volvo.ApciPlus.dll" "." >nul 2>&1
if exist "Volvo.ApciPlus.dll" (echo   ✓ Volvo.ApciPlus.dll) else (echo   ✗ Volvo.ApciPlus.dll - MISSING!)
copy "Libraries\Volvo.ApciPlusData.dll" "." >nul 2>&1
if exist "Volvo.ApciPlusData.dll" (echo   ✓ Volvo.ApciPlusData.dll) else (echo   ✗ Volvo.ApciPlusData.dll - MISSING!)
copy "Libraries\Volvo.ApciPlusTea2Data.dll" "." >nul 2>&1
if exist "Volvo.ApciPlusTea2Data.dll" (echo   ✓ Volvo.ApciPlusTea2Data.dll) else (echo   ✗ Volvo.ApciPlusTea2Data.dll - MISSING!)

REM Volvo NVS and NAMS libraries (REQUIRED for Volvo protocols)
echo.
echo [REQUIRED] Volvo Protocol Libraries:
copy "Libraries\Volvo.NVS.Core.dll" "." >nul 2>&1
if exist "Volvo.NVS.Core.dll" (echo   ✓ Volvo.NVS.Core.dll) else (echo   ✗ Volvo.NVS.Core.dll - MISSING!)
copy "Libraries\Volvo.NAMS.AC.Services.Interface.dll" "." >nul 2>&1
if exist "Volvo.NAMS.AC.Services.Interface.dll" (echo   ✓ Volvo.NAMS.AC.Services.Interface.dll) else (echo   ✗ Volvo.NAMS.AC.Services.Interface.dll - MISSING!)

REM Vodia communication libraries (REQUIRED for communication protocols)
echo.
echo [REQUIRED] Communication Protocol Libraries:
copy "Libraries\Vodia.CommonDomain.Model.dll" "." >nul 2>&1
if exist "Vodia.CommonDomain.Model.dll" (echo   ✓ Vodia.CommonDomain.Model.dll) else (echo   ✗ Vodia.CommonDomain.Model.dll - MISSING!)
copy "Libraries\Vodia.Contracts.Common.dll" "." >nul 2>&1
if exist "Vodia.Contracts.Common.dll" (echo   ✓ Vodia.Contracts.Common.dll) else (echo   ✗ Vodia.Contracts.Common.dll - MISSING!)

REM Essential dependencies
echo.
echo [ESSENTIAL] Core Dependencies:
copy "Libraries\log4net.dll" "." >nul 2>&1
if exist "log4net.dll" (echo   ✓ log4net.dll) else (echo   ✗ log4net.dll - MISSING!)
copy "Libraries\Newtonsoft.Json.dll" "." >nul 2>&1
if exist "Newtonsoft.Json.dll" (echo   ✓ Newtonsoft.Json.dll) else (echo   ✗ Newtonsoft.Json.dll - MISSING!)

echo.
echo ===============================================================================
echo                           Real Hardware Mode Ready!
echo ===============================================================================
echo.
echo The application is now configured for real Vocom 1 adapter communication using:
echo   • Phoenix APCI libraries for hardware integration
echo   • Vocom 1 adapter drivers (WUDFPuma.dll)
echo   • Volvo-specific communication protocols
echo   • Essential dependencies for operation
echo.
echo ===============================================================================

REM Launch application in real hardware mode
echo === Launching VolvoFlashWR in Real Hardware Mode ===
echo.
echo Starting application with Phoenix APCI enabled...
start "" "VolvoFlashWR.Launcher.exe"

echo.
echo *** Application launched in Real Hardware Mode! ***
echo.
echo The application will now attempt to:
echo   1. Initialize Phoenix APCI libraries
echo   2. Detect connected Vocom 1 adapters
echo   3. Establish real hardware communication
echo   4. Enable ECU flash programming capabilities
echo.
echo Check the application logs for connection status and diagnostics.
echo.
pause
