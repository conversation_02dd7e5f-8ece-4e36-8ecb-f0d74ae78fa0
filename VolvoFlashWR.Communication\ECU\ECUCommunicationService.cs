using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VolvoFlashWR.Communication.Microcontroller;
using VolvoFlashWR.Communication.Protocols;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Security.Cryptography;

namespace VolvoFlashWR.Communication.ECU
{
    /// <summary>
    /// Implementation of the ECU communication service
    /// </summary>
    public class ECUCommunicationService : IECUCommunicationService
    {
        #region Private Fields

        private readonly ILoggingService? _logger;
        private IVocomService _vocomService = null!;
        private List<ECUDevice> _connectedECUs = new List<ECUDevice>();
        private OperatingMode _currentOperatingMode = OperatingMode.Bench;
        private bool _isInitialized;
        private bool _requireConnectedDevice = false; // Set to true for operations that require a connected device

        // MC9S12XEP100 specific constants from the configuration class
        private readonly MC9S12XEP100Helper _mc9s12xep100Helper;
        private Dictionary<ECUProtocolType, MC9S12XEP100Integration> _mc9s12xep100Integrations = new Dictionary<ECUProtocolType, MC9S12XEP100Integration>();
        private int FLASH_SIZE = MC9S12XEP100Configuration.FLASH_SIZE;
        private int EEPROM_SIZE = MC9S12XEP100Configuration.EEPROM_SIZE;
        private int RAM_SIZE = MC9S12XEP100Configuration.RAM_SIZE;
        private int SECTOR_SIZE = MC9S12XEP100Configuration.SECTOR_SIZE;
        private int PHRASE_SIZE = MC9S12XEP100Configuration.PHRASE_SIZE;
        private int D_FLASH_SIZE = MC9S12XEP100Configuration.D_FLASH_SIZE;
        private int BUFFER_RAM_SIZE = MC9S12XEP100Configuration.BUFFER_RAM_SIZE;
        private int MAX_CLOCK_FREQUENCY = MC9S12XEP100Configuration.MAX_CPU_FREQUENCY;

        // Protocol handlers
        private Dictionary<ECUProtocolType, IECUProtocolHandler> _protocolHandlers = new Dictionary<ECUProtocolType, IECUProtocolHandler>();

        #endregion

        #region Events

        /// <summary>
        /// Event triggered when an ECU is connected
        /// </summary>
        public event EventHandler<ECUDevice>? ECUConnected;

        /// <summary>
        /// Event triggered when an ECU is disconnected
        /// </summary>
        public event EventHandler<ECUDevice>? ECUDisconnected;

        /// <summary>
        /// Event triggered when an error occurs during ECU communication
        /// </summary>
        public event EventHandler<string>? ECUError;

        #endregion

        #region Properties

        /// <summary>
        /// Gets the list of currently connected ECUs
        /// </summary>
        public List<ECUDevice> ConnectedECUs => _connectedECUs;

        /// <summary>
        /// Gets the current operating mode
        /// </summary>
        public OperatingMode CurrentOperatingMode => _currentOperatingMode;

        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ECUCommunicationService class
        /// </summary>
        /// <param name="logger">The logging service</param>
        public ECUCommunicationService(ILoggingService? logger)
        {
            _logger = logger;
            _isInitialized = false;

            // Initialize the MC9S12XEP100 helper
            _mc9s12xep100Helper = new MC9S12XEP100Helper(logger);

            // Initialize events to empty handlers to avoid null reference exceptions
            ECUConnected = (sender, device) => { };
            ECUDisconnected = (sender, device) => { };
            ECUError = (sender, message) => { };
        }

        #endregion

        #region IECUCommunicationService Implementation

        /// <summary>
        /// Initializes the ECU communication service
        /// </summary>
        /// <param name="vocomService">The Vocom service to use for communication</param>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public async Task<bool> InitializeAsync(IVocomService vocomService)
        {
            try
            {
                _logger?.LogInformation("Initializing ECU communication service", "ECUCommunicationService");

                if (vocomService == null)
                {
                    _logger?.LogError("Vocom service is null", "ECUCommunicationService");
                    ECUError?.Invoke(this, "Vocom service is null");
                    return false;
                }

                _vocomService = vocomService;

                // Subscribe to Vocom service events
                _vocomService.VocomConnected += (sender, device) => OnVocomConnected(sender, device);
                _vocomService.VocomDisconnected += (sender, device) => OnVocomDisconnected(sender, device);
                _vocomService.VocomError += (sender, message) => OnVocomError(sender, message);

                // Set default operating mode
                _currentOperatingMode = OperatingMode.Bench;

                // Initialize protocol handlers
                _protocolHandlers.Clear();

                // Clear MC9S12XEP100 integrations
                _mc9s12xep100Integrations.Clear();

                // Load MC9S12XEP100 configuration
                bool configLoaded = await LoadMC9S12XEP100ConfigurationAsync();
                if (!configLoaded)
                {
                    _logger?.LogWarning("Failed to load MC9S12XEP100 configuration, using default values", "ECUCommunicationService");
                    // Continue with default values
                }

                // Create protocol handlers factory
                var protocolHandlerFactory = new ProtocolHandlerFactory(_logger ?? throw new InvalidOperationException("Logger is required"));

                // Initialize the protocol handler factory
                bool factoryInitialized = await protocolHandlerFactory.InitializeAsync(_vocomService);
                if (!factoryInitialized)
                {
                    _logger?.LogError("Failed to initialize protocol handler factory", "ECUCommunicationService");
                    ECUError?.Invoke(this, "Failed to initialize protocol handler factory");
                    return false;
                }

                // Get all protocol handlers from the factory
                _protocolHandlers = new Dictionary<ECUProtocolType, IECUProtocolHandler>();

                // Add CAN protocol handler
                try
                {
                    var canHandler = new CANProtocolHandler(_logger ?? throw new InvalidOperationException("Logger is required"), _vocomService);
                    if (await canHandler.InitializeAsync())
                    {
                        _protocolHandlers.Add(ECUProtocolType.CAN, canHandler);
                        _logger?.LogInformation("CAN protocol handler initialized successfully", "ECUCommunicationService");

                        // Create MC9S12XEP100 integration for CAN protocol
                        var canIntegration = new MC9S12XEP100Integration(_logger, _vocomService, ECUProtocolType.CAN);
                        _mc9s12xep100Integrations.Add(ECUProtocolType.CAN, canIntegration);
                        _logger?.LogInformation("MC9S12XEP100 integration for CAN protocol initialized successfully", "ECUCommunicationService");
                    }
                    else
                    {
                        _logger?.LogWarning("Failed to initialize CAN protocol handler", "ECUCommunicationService");
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError($"Error creating CAN protocol handler: {ex.Message}", "ECUCommunicationService");
                }

                // Add SPI protocol handler
                try
                {
                    var spiHandler = new SPIProtocolHandler(_logger ?? throw new InvalidOperationException("Logger is required"), _vocomService);
                    if (await spiHandler.InitializeAsync())
                    {
                        _protocolHandlers.Add(ECUProtocolType.SPI, spiHandler);
                        _logger?.LogInformation("SPI protocol handler initialized successfully", "ECUCommunicationService");

                        // Create MC9S12XEP100 integration for SPI protocol
                        var spiIntegration = new MC9S12XEP100Integration(_logger, _vocomService, ECUProtocolType.SPI);
                        _mc9s12xep100Integrations.Add(ECUProtocolType.SPI, spiIntegration);
                        _logger?.LogInformation("MC9S12XEP100 integration for SPI protocol initialized successfully", "ECUCommunicationService");
                    }
                    else
                    {
                        _logger?.LogWarning("Failed to initialize SPI protocol handler", "ECUCommunicationService");
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError($"Error creating SPI protocol handler: {ex.Message}", "ECUCommunicationService");
                }

                // Add SCI protocol handler
                try
                {
                    var sciHandler = new SCIProtocolHandler(_logger ?? throw new InvalidOperationException("Logger is required"), _vocomService);
                    if (await sciHandler.InitializeAsync())
                    {
                        _protocolHandlers.Add(ECUProtocolType.SCI, sciHandler);
                        _logger?.LogInformation("SCI protocol handler initialized successfully", "ECUCommunicationService");

                        // Create MC9S12XEP100 integration for SCI protocol
                        var sciIntegration = new MC9S12XEP100Integration(_logger, _vocomService, ECUProtocolType.SCI);
                        _mc9s12xep100Integrations.Add(ECUProtocolType.SCI, sciIntegration);
                        _logger?.LogInformation("MC9S12XEP100 integration for SCI protocol initialized successfully", "ECUCommunicationService");
                    }
                    else
                    {
                        _logger?.LogWarning("Failed to initialize SCI protocol handler", "ECUCommunicationService");
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError($"Error creating SCI protocol handler: {ex.Message}", "ECUCommunicationService");
                }

                // Add IIC protocol handler
                try
                {
                    var iicHandler = new IICProtocolHandler(_logger ?? throw new InvalidOperationException("Logger is required"), _vocomService);
                    if (await iicHandler.InitializeAsync())
                    {
                        _protocolHandlers.Add(ECUProtocolType.IIC, iicHandler);
                        _logger?.LogInformation("IIC protocol handler initialized successfully", "ECUCommunicationService");

                        // Create MC9S12XEP100 integration for IIC protocol
                        var iicIntegration = new MC9S12XEP100Integration(_logger, _vocomService, ECUProtocolType.IIC);
                        _mc9s12xep100Integrations.Add(ECUProtocolType.IIC, iicIntegration);
                        _logger?.LogInformation("MC9S12XEP100 integration for IIC protocol initialized successfully", "ECUCommunicationService");
                    }
                    else
                    {
                        _logger?.LogWarning("Failed to initialize IIC protocol handler", "ECUCommunicationService");
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError($"Error creating IIC protocol handler: {ex.Message}", "ECUCommunicationService");
                }

                // Check if the Vocom adapter is ready for communication
                if (_vocomService.CurrentDevice == null ||
                    _vocomService.CurrentDevice.ConnectionStatus != VocomConnectionStatus.Connected)
                {
                    _logger?.LogWarning("Vocom adapter is not connected or not ready", "ECUCommunicationService");

                    // Try to connect to the Vocom adapter if it's not connected
                    if (_vocomService.CurrentDevice == null)
                    {
                        _logger?.LogInformation("Attempting to find and connect to a Vocom adapter", "ECUCommunicationService");
                        bool vocomConnected = await _vocomService.ConnectToFirstAvailableDeviceAsync();
                        if (!vocomConnected)
                        {
                            _logger?.LogError("Failed to connect to a Vocom adapter", "ECUCommunicationService");
                            ECUError?.Invoke(this, "Failed to connect to a Vocom adapter");
                            return false;
                        }
                    }
                    else if (_vocomService.CurrentDevice.ConnectionStatus != VocomConnectionStatus.Connected)
                    {
                        _logger?.LogInformation("Attempting to reconnect to the Vocom adapter", "ECUCommunicationService");
                        bool vocomReconnected = await _vocomService.ReconnectAsync();
                        if (!vocomReconnected)
                        {
                            _logger?.LogError("Failed to reconnect to the Vocom adapter", "ECUCommunicationService");
                            ECUError?.Invoke(this, "Failed to reconnect to the Vocom adapter");
                            return false;
                        }
                    }
                }

                if (_protocolHandlers.Count == 0)
                {
                    _logger?.LogError("No protocol handlers were initialized", "ECUCommunicationService");
                    ECUError?.Invoke(this, "No protocol handlers were initialized");
                    return false;
                }

                _isInitialized = true;
                _logger?.LogInformation("ECU communication service initialized successfully", "ECUCommunicationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to initialize ECU communication service", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Initialization error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Scans for available ECUs
        /// </summary>
        /// <returns>List of available ECUs</returns>
        public async Task<List<ECUDevice>> ScanForECUsAsync()
        {
            try
            {
                _logger?.LogInformation("Scanning for ECUs", "ECUCommunicationService");

                _requireConnectedDevice = true; // This operation requires a connected device
                if (!ValidateInitialization())
                {
                    _requireConnectedDevice = false; // Reset the flag
                    return new List<ECUDevice>();
                }
                _requireConnectedDevice = false; // Reset the flag

                List<ECUDevice> availableECUs = new List<ECUDevice>();

                // In a real implementation, this would involve scanning for ECUs using different protocols
                // For now, we'll simulate the scanning process

                // Scan for ECUs using CAN protocol
                if (_protocolHandlers.TryGetValue(ECUProtocolType.CAN, out IECUProtocolHandler canProtocolHandler))
                {
                    _logger?.LogInformation("Scanning for ECUs on the CAN bus...", "ECUCommunicationService");
                    // Simulate a delay for scanning the CAN bus
                    await Task.Delay(500);
                }
                else
                {
                    _logger?.LogWarning("CAN protocol handler not found, skipping CAN bus scan", "ECUCommunicationService");
                }

                // Scan for ECUs using SPI protocol
                if (_protocolHandlers.TryGetValue(ECUProtocolType.SPI, out IECUProtocolHandler spiProtocolHandler))
                {
                    _logger?.LogInformation("Scanning for ECUs using SPI protocol...", "ECUCommunicationService");
                    // Simulate a delay for scanning SPI devices
                    await Task.Delay(300);
                }
                else
                {
                    _logger?.LogWarning("SPI protocol handler not found, skipping SPI device scan", "ECUCommunicationService");
                }

                // Scan for ECUs using SCI protocol
                if (_protocolHandlers.TryGetValue(ECUProtocolType.SCI, out IECUProtocolHandler sciProtocolHandler))
                {
                    _logger?.LogInformation("Scanning for ECUs using SCI protocol...", "ECUCommunicationService");
                    // Simulate a delay for scanning SCI devices
                    await Task.Delay(300);
                }
                else
                {
                    _logger?.LogWarning("SCI protocol handler not found, skipping SCI device scan", "ECUCommunicationService");
                }

                // Scan for ECUs using IIC protocol
                if (_protocolHandlers.TryGetValue(ECUProtocolType.IIC, out IECUProtocolHandler iicProtocolHandler))
                {
                    _logger?.LogInformation("Scanning for ECUs using IIC protocol...", "ECUCommunicationService");
                    // Simulate a delay for scanning IIC devices
                    await Task.Delay(300);
                }
                else
                {
                    _logger?.LogWarning("IIC protocol handler not found, skipping IIC device scan", "ECUCommunicationService");
                }

                // Create a simulated EMS (Engine Management System) ECU with MC9S12XEP100 specific parameters
                ECUDevice emsEcu = new ECUDevice
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "EMS",
                    SerialNumber = "EMS-" + DateTime.Now.Ticks.ToString().Substring(10),
                    HardwareVersion = "1.2",
                    SoftwareVersion = "2.5",
                    ConnectionStatus = ECUConnectionStatus.Disconnected,
                    MicrocontrollerType = "MC9S12XEP100",
                    EEPROMSize = EEPROM_SIZE,
                    FlashSize = FLASH_SIZE,
                    RAMSize = RAM_SIZE,
                    SupportsHighSpeedCommunication = true,
                    SupportsLowSpeedCommunication = true,
                    ProtocolType = ECUProtocolType.CAN,
                    Parameters = new Dictionary<string, object>
                    {
                        { "EngineRPM", 0 },
                        { "VehicleSpeed", 0 },
                        { "CoolantTemp", 0 },
                        { "IntakeAirTemp", 0 },
                        { "ThrottlePosition", 0 },
                        { "FuelLevel", 0 },
                        { "ECCStatus", "Enabled" },
                        { "FlashProtectionStatus", "Enabled" },
                        { "SectorEraseCount", new Dictionary<string, int> {
                            { "Sector0", 10 },
                            { "Sector1", 5 },
                            { "Sector2", 8 }
                        }},
                        { "FlashECCErrorCount", 0 },
                        { "MemoryProtectionStatus", "Active" }
                    },
                    ActiveFaults = new List<ECUFault>(),
                    InactiveFaults = new List<ECUFault>()
                };
                availableECUs.Add(emsEcu);

                // Create a simulated TCM (Transmission Control Module) ECU
                ECUDevice tcmEcu = new ECUDevice
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "TCM",
                    SerialNumber = "TCM-" + DateTime.Now.Ticks.ToString().Substring(10),
                    HardwareVersion = "1.3",
                    SoftwareVersion = "2.1",
                    ConnectionStatus = ECUConnectionStatus.Disconnected,
                    MicrocontrollerType = "MC9S12XEP100",
                    EEPROMSize = EEPROM_SIZE,
                    FlashSize = FLASH_SIZE,
                    RAMSize = RAM_SIZE,
                    SupportsHighSpeedCommunication = true,
                    SupportsLowSpeedCommunication = true,
                    ProtocolType = ECUProtocolType.CAN,
                    Parameters = new Dictionary<string, object>
                    {
                        { "GearPosition", 0 },
                        { "TransmissionTemp", 0 },
                        { "TransmissionMode", "Normal" }
                    },
                    ActiveFaults = new List<ECUFault>(),
                    InactiveFaults = new List<ECUFault>()
                };
                availableECUs.Add(tcmEcu);

                // Create a simulated BCM (Body Control Module) ECU
                ECUDevice bcmEcu = new ECUDevice
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "BCM",
                    SerialNumber = "BCM-" + DateTime.Now.Ticks.ToString().Substring(10),
                    HardwareVersion = "1.0",
                    SoftwareVersion = "2.2",
                    ConnectionStatus = ECUConnectionStatus.Disconnected,
                    MicrocontrollerType = "MC9S12XEP100",
                    EEPROMSize = EEPROM_SIZE,
                    FlashSize = FLASH_SIZE,
                    RAMSize = RAM_SIZE,
                    SupportsHighSpeedCommunication = true,
                    SupportsLowSpeedCommunication = true,
                    ProtocolType = ECUProtocolType.CAN,
                    Parameters = new Dictionary<string, object>
                    {
                        { "DoorStatus", "Closed" },
                        { "LightStatus", "Off" },
                        { "WindowStatus", "Closed" }
                    },
                    ActiveFaults = new List<ECUFault>(),
                    InactiveFaults = new List<ECUFault>()
                };
                availableECUs.Add(bcmEcu);

                // Create a simulated ABS (Anti-lock Braking System) ECU
                ECUDevice absEcu = new ECUDevice
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "ABS",
                    SerialNumber = "ABS-" + DateTime.Now.Ticks.ToString().Substring(10),
                    HardwareVersion = "1.1",
                    SoftwareVersion = "2.0",
                    ConnectionStatus = ECUConnectionStatus.Disconnected,
                    MicrocontrollerType = "MC9S12XEP100",
                    EEPROMSize = EEPROM_SIZE,
                    FlashSize = FLASH_SIZE,
                    RAMSize = RAM_SIZE,
                    SupportsHighSpeedCommunication = true,
                    SupportsLowSpeedCommunication = false,
                    ProtocolType = ECUProtocolType.CAN,
                    Parameters = new Dictionary<string, object>
                    {
                        { "ABSStatus", "Active" },
                        { "TractionControlStatus", "Active" },
                        { "StabilityControlStatus", "Active" }
                    },
                    ActiveFaults = new List<ECUFault>(),
                    InactiveFaults = new List<ECUFault>()
                };
                availableECUs.Add(absEcu);

                // Create a simulated Display Control Module ECU using SPI protocol
                ECUDevice displayEcu = new ECUDevice
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "DCM",
                    SerialNumber = "DCM-" + DateTime.Now.Ticks.ToString().Substring(10),
                    HardwareVersion = "1.5",
                    SoftwareVersion = "2.3",
                    ConnectionStatus = ECUConnectionStatus.Disconnected,
                    MicrocontrollerType = "MC9S12XEP100",
                    EEPROMSize = EEPROM_SIZE,
                    FlashSize = FLASH_SIZE,
                    RAMSize = RAM_SIZE,
                    SupportsHighSpeedCommunication = true,
                    SupportsLowSpeedCommunication = true,
                    ProtocolType = ECUProtocolType.SPI,
                    Parameters = new Dictionary<string, object>
                    {
                        { "DisplayBrightness", 80 },
                        { "DisplayMode", "Day" },
                        { "LanguageSettings", "English" }
                    },
                    ActiveFaults = new List<ECUFault>(),
                    InactiveFaults = new List<ECUFault>()
                };
                availableECUs.Add(displayEcu);

                // Create a simulated Climate Control Module ECU using SPI protocol
                ECUDevice climateEcu = new ECUDevice
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "CCM",
                    SerialNumber = "CCM-" + DateTime.Now.Ticks.ToString().Substring(10),
                    HardwareVersion = "1.2",
                    SoftwareVersion = "1.8",
                    ConnectionStatus = ECUConnectionStatus.Disconnected,
                    MicrocontrollerType = "MC9S12XEP100",
                    EEPROMSize = EEPROM_SIZE,
                    FlashSize = FLASH_SIZE,
                    RAMSize = RAM_SIZE,
                    SupportsHighSpeedCommunication = false,
                    SupportsLowSpeedCommunication = true,
                    ProtocolType = ECUProtocolType.SPI,
                    Parameters = new Dictionary<string, object>
                    {
                        { "TemperatureSetpoint", 22 },
                        { "FanSpeed", 3 },
                        { "ACStatus", "On" }
                    },
                    ActiveFaults = new List<ECUFault>(),
                    InactiveFaults = new List<ECUFault>()
                };
                availableECUs.Add(climateEcu);

                // Create a simulated Instrument Cluster ECU using SCI protocol
                ECUDevice instrumentClusterEcu = new ECUDevice
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "ICM",
                    SerialNumber = "ICM-" + DateTime.Now.Ticks.ToString().Substring(10),
                    HardwareVersion = "2.0",
                    SoftwareVersion = "3.1",
                    ConnectionStatus = ECUConnectionStatus.Disconnected,
                    MicrocontrollerType = "MC9S12XEP100",
                    EEPROMSize = EEPROM_SIZE,
                    FlashSize = FLASH_SIZE,
                    RAMSize = RAM_SIZE,
                    SupportsHighSpeedCommunication = true,
                    SupportsLowSpeedCommunication = true,
                    ProtocolType = ECUProtocolType.SCI,
                    Parameters = new Dictionary<string, object>
                    {
                        { "SpeedometerCalibration", 1.0 },
                        { "TachometerCalibration", 1.0 },
                        { "DisplayUnits", "Metric" }
                    },
                    ActiveFaults = new List<ECUFault>(),
                    InactiveFaults = new List<ECUFault>()
                };
                availableECUs.Add(instrumentClusterEcu);

                // Create a simulated Infotainment ECU using SCI protocol
                ECUDevice infotainmentEcu = new ECUDevice
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "INF",
                    SerialNumber = "INF-" + DateTime.Now.Ticks.ToString().Substring(10),
                    HardwareVersion = "2.5",
                    SoftwareVersion = "4.2",
                    ConnectionStatus = ECUConnectionStatus.Disconnected,
                    MicrocontrollerType = "MC9S12XEP100",
                    EEPROMSize = EEPROM_SIZE,
                    FlashSize = FLASH_SIZE,
                    RAMSize = RAM_SIZE,
                    SupportsHighSpeedCommunication = true,
                    SupportsLowSpeedCommunication = false,
                    ProtocolType = ECUProtocolType.SCI,
                    Parameters = new Dictionary<string, object>
                    {
                        { "AudioVolume", 15 },
                        { "RadioFrequency", 98.5 },
                        { "BluetoothStatus", "Connected" }
                    },
                    ActiveFaults = new List<ECUFault>(),
                    InactiveFaults = new List<ECUFault>()
                };
                availableECUs.Add(infotainmentEcu);

                // Create a simulated Sensor Control Module ECU using IIC protocol
                ECUDevice sensorControlEcu = new ECUDevice
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "SCM",
                    SerialNumber = "SCM-" + DateTime.Now.Ticks.ToString().Substring(10),
                    HardwareVersion = "1.8",
                    SoftwareVersion = "2.7",
                    ConnectionStatus = ECUConnectionStatus.Disconnected,
                    MicrocontrollerType = "MC9S12XEP100",
                    EEPROMSize = EEPROM_SIZE,
                    FlashSize = FLASH_SIZE,
                    RAMSize = RAM_SIZE,
                    SupportsHighSpeedCommunication = false,
                    SupportsLowSpeedCommunication = true,
                    ProtocolType = ECUProtocolType.IIC,
                    Parameters = new Dictionary<string, object>
                    {
                        { "AmbientTemperature", 22.5 },
                        { "LightSensorValue", 75 },
                        { "RainSensorValue", 0 },
                        { "ParkingSensorStatus", "Active" }
                    },
                    ActiveFaults = new List<ECUFault>(),
                    InactiveFaults = new List<ECUFault>()
                };
                availableECUs.Add(sensorControlEcu);

                // Create a simulated Battery Management System ECU using IIC protocol
                ECUDevice batteryManagementEcu = new ECUDevice
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "BMS",
                    SerialNumber = "BMS-" + DateTime.Now.Ticks.ToString().Substring(10),
                    HardwareVersion = "2.0",
                    SoftwareVersion = "3.1",
                    ConnectionStatus = ECUConnectionStatus.Disconnected,
                    MicrocontrollerType = "MC9S12XEP100",
                    EEPROMSize = EEPROM_SIZE,
                    FlashSize = FLASH_SIZE,
                    RAMSize = RAM_SIZE,
                    SupportsHighSpeedCommunication = true,
                    SupportsLowSpeedCommunication = true,
                    ProtocolType = ECUProtocolType.IIC,
                    Parameters = new Dictionary<string, object>
                    {
                        { "BatteryVoltage", 12.6 },
                        { "BatteryCurrent", 5.2 },
                        { "BatteryTemperature", 25 },
                        { "BatteryChargeState", 85 }
                    },
                    ActiveFaults = new List<ECUFault>(),
                    InactiveFaults = new List<ECUFault>()
                };
                availableECUs.Add(batteryManagementEcu);

                _logger?.LogInformation($"Found {availableECUs.Count} ECUs", "ECUCommunicationService");
                return availableECUs;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to scan for ECUs", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Scan error: {ex.Message}");
                return new List<ECUDevice>();
            }
        }

        /// <summary>
        /// Connects to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        public async Task<bool> ConnectToECUAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Connecting to ECU {ecu?.Name}", "ECUCommunicationService");

                _requireConnectedDevice = true; // This operation requires a connected device
                if (!ValidateInitialization())
                {
                    _requireConnectedDevice = false; // Reset the flag
                    return false;
                }
                _requireConnectedDevice = false; // Reset the flag

                if (ecu == null)
                {
                    _logger?.LogError("ECU is null", "ECUCommunicationService");
                    ECUError?.Invoke(this, "ECU is null");
                    return false;
                }

                // Check if the ECU is already connected
                if (_connectedECUs.Contains(ecu) && ecu.ConnectionStatus == ECUConnectionStatus.Connected)
                {
                    _logger?.LogInformation($"ECU {ecu.Name} is already connected", "ECUCommunicationService");
                    return true;
                }

                // Determine the appropriate protocol handler based on the ECU's protocol type
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return false;
                }

                    // Determine the appropriate communication speed based on ECU capabilities
                    bool useHighSpeed = false;

                    // Check if the ECU supports high-speed communication
                    if (ecu.SupportsHighSpeedCommunication)
                    {
                        // Use high-speed communication if supported
                        useHighSpeed = true;
                        _logger?.LogInformation($"Using high-speed communication for ECU {ecu.Name}", "ECUCommunicationService");
                    }
                    else if (ecu.SupportsLowSpeedCommunication)
                    {
                        // Fall back to low-speed communication if high-speed is not supported
                        useHighSpeed = false;
                        _logger?.LogInformation($"Using low-speed communication for ECU {ecu.Name}", "ECUCommunicationService");
                    }
                    else
                    {
                        _logger?.LogError($"ECU {ecu.Name} does not support any communication speed", "ECUCommunicationService");
                        ECUError?.Invoke(this, $"ECU {ecu.Name} does not support any communication speed");
                        return false;
                    }

                    // Store the communication speed in the ECU properties
                    if (!ecu.Properties.ContainsKey("CommunicationSpeed"))
                    {
                        ecu.Properties.Add("CommunicationSpeed", useHighSpeed ? "High" : "Low");
                    }
                    else
                    {
                        ecu.Properties["CommunicationSpeed"] = useHighSpeed ? "High" : "Low";
                    }

                    // Set the operating mode on the ECU before connecting
                    _logger?.LogInformation($"Setting operating mode to {_currentOperatingMode} for ECU {ecu.Name}", "ECUCommunicationService");

                    // Connect to the ECU using the protocol handler
                    _logger?.LogInformation($"Attempting to connect to ECU {ecu.Name} using {protocolType} protocol with {(useHighSpeed ? "high" : "low")}-speed communication", "ECUCommunicationService");

                    // Simulate connection attempt with retry logic
                    bool connected = false;
                    int maxRetries = 3;
                    int retryCount = 0;

                    while (!connected && retryCount < maxRetries)
                    {
                        try
                        {
                            // Configure the protocol handler for the appropriate communication speed
                            // This is done by setting a property in the ECU object that the protocol handler will check
                            if (protocolType == ECUProtocolType.CAN)
                            {
                                if (!ecu.Properties.ContainsKey("SupportHighSpeedCAN"))
                                {
                                    ecu.Properties.Add("SupportHighSpeedCAN", useHighSpeed);
                                }
                                else
                                {
                                    ecu.Properties["SupportHighSpeedCAN"] = useHighSpeed;
                                }
                            }
                            else if (protocolType == ECUProtocolType.SPI)
                            {
                                if (!ecu.Properties.ContainsKey("SupportHighSpeedSPI"))
                                {
                                    ecu.Properties.Add("SupportHighSpeedSPI", useHighSpeed);
                                }
                                else
                                {
                                    ecu.Properties["SupportHighSpeedSPI"] = useHighSpeed;
                                }
                            }
                            else if (protocolType == ECUProtocolType.SCI)
                            {
                                if (!ecu.Properties.ContainsKey("SupportHighSpeedSCI"))
                                {
                                    ecu.Properties.Add("SupportHighSpeedSCI", useHighSpeed);
                                }
                                else
                                {
                                    ecu.Properties["SupportHighSpeedSCI"] = useHighSpeed;
                                }
                            }
                            else if (protocolType == ECUProtocolType.IIC)
                            {
                                if (!ecu.Properties.ContainsKey("SupportHighSpeedIIC"))
                                {
                                    ecu.Properties.Add("SupportHighSpeedIIC", useHighSpeed);
                                }
                                else
                                {
                                    ecu.Properties["SupportHighSpeedIIC"] = useHighSpeed;
                                }
                            }

                            connected = await protocolHandler.ConnectAsync(ecu);
                            if (!connected)
                            {
                                retryCount++;
                                _logger?.LogWarning($"Connection attempt {retryCount} failed for ECU {ecu.Name}", "ECUCommunicationService");

                                // If high-speed connection fails, try low-speed as a fallback
                                if (useHighSpeed && ecu.SupportsLowSpeedCommunication && retryCount == maxRetries - 1)
                                {
                                    useHighSpeed = false;
                                    _logger?.LogWarning($"Falling back to low-speed communication for ECU {ecu.Name}", "ECUCommunicationService");

                                    // Update the ECU properties for the fallback
                                    ecu.Properties["CommunicationSpeed"] = "Low";

                                    if (protocolType == ECUProtocolType.CAN)
                                    {
                                        ecu.Properties["SupportHighSpeedCAN"] = false;
                                    }
                                    else if (protocolType == ECUProtocolType.SPI)
                                    {
                                        ecu.Properties["SupportHighSpeedSPI"] = false;
                                    }
                                    else if (protocolType == ECUProtocolType.SCI)
                                    {
                                        ecu.Properties["SupportHighSpeedSCI"] = false;
                                    }
                                    else if (protocolType == ECUProtocolType.IIC)
                                    {
                                        ecu.Properties["SupportHighSpeedIIC"] = false;
                                    }

                                    // Reset retry count to give the low-speed connection a full set of attempts
                                    retryCount = 0;
                                }
                                else
                                {
                                    await Task.Delay(500); // Wait before retrying
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            retryCount++;
                            _logger?.LogWarning($"Connection attempt {retryCount} failed for ECU {ecu.Name}: {ex.Message}", "ECUCommunicationService");
                            await Task.Delay(500); // Wait before retrying
                        }
                    }

                    if (connected)
                    {
                        // Add the ECU to the list of connected ECUs if it's not already there
                        if (!_connectedECUs.Contains(ecu))
                        {
                            _connectedECUs.Add(ecu);
                        }

                        // Update ECU status
                        ecu.ConnectionStatus = ECUConnectionStatus.Connected;
                        ecu.LastCommunicationTime = DateTime.Now;

                        // Check if this is a MC9S12XEP100 microcontroller and apply specific configuration
                        if (ecu.MicrocontrollerType == "MC9S12XEP100")
                        {
                            _logger?.LogInformation($"Configuring MC9S12XEP100 specific settings for ECU {ecu.Name}", "ECUCommunicationService");
                            bool configSuccess = await ConfigureMC9S12XEP100ECUAsync(ecu);
                            if (!configSuccess)
                            {
                                _logger?.LogWarning($"Failed to apply MC9S12XEP100 specific configuration for ECU {ecu.Name}", "ECUCommunicationService");
                                // Continue anyway, as we've already established the connection
                            }

                            // Load flash memory configuration
                            Dictionary<string, object> flashConfig = await GetMC9S12XEP100FlashConfigAsync(ecu);
                            foreach (var kvp in flashConfig)
                            {
                                if (!ecu.Properties.ContainsKey(kvp.Key))
                                {
                                    ecu.Properties.Add(kvp.Key, kvp.Value);
                                }
                                else
                                {
                                    ecu.Properties[kvp.Key] = kvp.Value;
                                }
                            }
                        }

                        // Apply the current operating mode to the ECU
                        await protocolHandler.SetOperatingModeAsync(_currentOperatingMode);

                        // Read initial parameters and faults
                        try
                        {
                            _logger?.LogInformation($"Reading initial parameters and faults from ECU {ecu.Name}", "ECUCommunicationService");

                            // Read parameters
                            var parameters = await protocolHandler.ReadParametersAsync(ecu);
                            if (parameters != null)
                            {
                                ecu.Parameters = parameters;
                            }

                            // Read active faults
                            var activeFaults = await protocolHandler.ReadActiveFaultsAsync(ecu);
                            if (activeFaults != null)
                            {
                                ecu.ActiveFaults = activeFaults;
                            }

                            // Read inactive faults
                            var inactiveFaults = await protocolHandler.ReadInactiveFaultsAsync(ecu);
                            if (inactiveFaults != null)
                            {
                                ecu.InactiveFaults = inactiveFaults;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogWarning($"Failed to read initial data from ECU {ecu.Name}: {ex.Message}", "ECUCommunicationService");
                            // Continue anyway, as we've already established the connection
                        }

                        // Raise the ECU connected event
                        ECUConnected?.Invoke(this, ecu);

                        _logger?.LogInformation($"Connected to ECU {ecu.Name}", "ECUCommunicationService");
                        return true;
                    }
                    else
                    {
                        _logger?.LogError($"Failed to connect to ECU {ecu.Name} after {maxRetries} attempts", "ECUCommunicationService");
                        ECUError?.Invoke(this, $"Failed to connect to ECU {ecu.Name} after {maxRetries} attempts");
                        return false;
                    }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error connecting to ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Connection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disconnects from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public async Task<bool> DisconnectFromECUAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Disconnecting from ECU {ecu?.Name}", "ECUCommunicationService");

                _requireConnectedDevice = true; // This operation requires a connected device
                if (!ValidateInitialization())
                {
                    _requireConnectedDevice = false; // Reset the flag
                    return false;
                }
                _requireConnectedDevice = false; // Reset the flag

                if (ecu == null)
                {
                    _logger?.LogError("ECU is null", "ECUCommunicationService");
                    ECUError?.Invoke(this, "ECU is null");
                    return false;
                }

                // Check if the ECU is connected
                if (!_connectedECUs.Contains(ecu) || ecu.ConnectionStatus != ECUConnectionStatus.Connected)
                {
                    _logger?.LogInformation($"ECU {ecu.Name} is not connected", "ECUCommunicationService");
                    return true;
                }

                // Determine the appropriate protocol handler based on the ECU's protocol type
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return false;
                }

                    // Disconnect from the ECU using the protocol handler
                    _logger?.LogInformation($"Attempting to disconnect from ECU {ecu.Name}", "ECUCommunicationService");

                    // Simulate disconnection attempt with retry logic
                    bool disconnected = false;
                    int maxRetries = 3;
                    int retryCount = 0;

                    while (!disconnected && retryCount < maxRetries)
                    {
                        try
                        {
                            disconnected = await protocolHandler.DisconnectAsync(ecu);
                            if (!disconnected)
                            {
                                retryCount++;
                                _logger?.LogWarning($"Disconnection attempt {retryCount} failed for ECU {ecu.Name}", "ECUCommunicationService");
                                await Task.Delay(500); // Wait before retrying
                            }
                        }
                        catch (Exception ex)
                        {
                            retryCount++;
                            _logger?.LogWarning($"Disconnection attempt {retryCount} failed for ECU {ecu.Name}: {ex.Message}", "ECUCommunicationService");
                            await Task.Delay(500); // Wait before retrying
                        }
                    }

                    if (disconnected)
                    {
                        // Remove the ECU from the list of connected ECUs
                        _connectedECUs.Remove(ecu);

                        // Update ECU status
                        ecu.ConnectionStatus = ECUConnectionStatus.Disconnected;

                        // Clear any sensitive data
                        ecu.ActiveFaults.Clear();
                        ecu.InactiveFaults.Clear();

                        // Reset parameters to default values
                        foreach (var key in ecu.Parameters.Keys.ToList())
                        {
                            if (ecu.Parameters[key] is int || ecu.Parameters[key] is double)
                            {
                                ecu.Parameters[key] = 0;
                            }
                            else if (ecu.Parameters[key] is string)
                            {
                                ecu.Parameters[key] = string.Empty;
                            }
                            else if (ecu.Parameters[key] is bool)
                            {
                                ecu.Parameters[key] = false;
                            }
                        }

                        // Raise the ECU disconnected event
                        ECUDisconnected?.Invoke(this, ecu);

                        _logger?.LogInformation($"Disconnected from ECU {ecu.Name}", "ECUCommunicationService");
                        return true;
                    }
                    else
                    {
                        _logger?.LogError($"Failed to disconnect from ECU {ecu.Name} after {maxRetries} attempts", "ECUCommunicationService");
                        ECUError?.Invoke(this, $"Failed to disconnect from ECU {ecu.Name} after {maxRetries} attempts");

                        // Force disconnection in case of failure
                        _logger?.LogWarning($"Forcing disconnection for ECU {ecu.Name}", "ECUCommunicationService");
                        _connectedECUs.Remove(ecu);
                        ecu.ConnectionStatus = ECUConnectionStatus.Disconnected;
                        ECUDisconnected?.Invoke(this, ecu);

                        return false;
                    }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error disconnecting from ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Disconnection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disconnects from all connected ECUs
        /// </summary>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public async Task<bool> DisconnectAllECUsAsync()
        {
            try
            {
                _logger?.LogInformation("Disconnecting from all ECUs", "ECUCommunicationService");

                if (!ValidateInitialization())
                {
                    return false;
                }

                if (_connectedECUs.Count == 0)
                {
                    _logger?.LogInformation("No ECUs are connected", "ECUCommunicationService");
                    return true;
                }

                bool allDisconnected = true;
                List<ECUDevice> ecusToDisconnect = new List<ECUDevice>(_connectedECUs);

                _logger?.LogInformation($"Attempting to disconnect from {ecusToDisconnect.Count} ECUs", "ECUCommunicationService");

                // First, try to gracefully disconnect from each ECU
                foreach (var ecu in ecusToDisconnect)
                {
                    try
                    {
                        bool disconnected = await DisconnectFromECUAsync(ecu);
                        if (!disconnected)
                        {
                            allDisconnected = false;
                            _logger?.LogError($"Failed to gracefully disconnect from ECU {ecu.Name}", "ECUCommunicationService");
                        }
                    }
                    catch (Exception ex)
                    {
                        allDisconnected = false;
                        _logger?.LogError($"Error disconnecting from ECU {ecu.Name}: {ex.Message}", "ECUCommunicationService");
                    }
                }

                // If there are still connected ECUs, force disconnect them
                if (_connectedECUs.Count > 0)
                {
                    _logger?.LogWarning($"Forcing disconnection for {_connectedECUs.Count} remaining ECUs", "ECUCommunicationService");

                    List<ECUDevice> remainingECUs = new List<ECUDevice>(_connectedECUs);
                    foreach (var ecu in remainingECUs)
                    {
                        try
                        {
                            // Force disconnection
                            _connectedECUs.Remove(ecu);
                            ecu.ConnectionStatus = ECUConnectionStatus.Disconnected;

                            // Clear any sensitive data
                            ecu.ActiveFaults.Clear();
                            ecu.InactiveFaults.Clear();

                            // Reset parameters to default values
                            foreach (var key in ecu.Parameters.Keys.ToList())
                            {
                                if (ecu.Parameters[key] is int || ecu.Parameters[key] is double)
                                {
                                    ecu.Parameters[key] = 0;
                                }
                                else if (ecu.Parameters[key] is string)
                                {
                                    ecu.Parameters[key] = string.Empty;
                                }
                                else if (ecu.Parameters[key] is bool)
                                {
                                    ecu.Parameters[key] = false;
                                }
                            }

                            // Raise the ECU disconnected event
                            ECUDisconnected?.Invoke(this, ecu);

                            _logger?.LogInformation($"Forced disconnection from ECU {ecu.Name}", "ECUCommunicationService");
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError($"Error forcing disconnection from ECU {ecu.Name}: {ex.Message}", "ECUCommunicationService");
                        }
                    }
                }

                _logger?.LogInformation($"Disconnected from {(allDisconnected ? "all" : "some")} ECUs. {_connectedECUs.Count} ECUs remain connected.", "ECUCommunicationService");
                return _connectedECUs.Count == 0;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error disconnecting from all ECUs", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Disconnection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Sets the operating mode
        /// </summary>
        /// <param name="mode">The operating mode to set</param>
        /// <returns>True if mode change is successful, false otherwise</returns>
        public async Task<bool> SetOperatingModeAsync(OperatingMode mode)
        {
            try
            {
                _logger?.LogInformation($"Setting operating mode to {mode}", "ECUCommunicationService");

                if (!ValidateInitialization())
                {
                    return false;
                }

                // Check if the mode is already set
                if (_currentOperatingMode == mode)
                {
                    _logger?.LogInformation($"Operating mode is already set to {mode}", "ECUCommunicationService");
                    return true;
                }

                // Set the operating mode for all protocol handlers
                bool allSucceeded = true;
                foreach (var protocolHandler in _protocolHandlers.Values)
                {
                    bool success = await protocolHandler.SetOperatingModeAsync(mode);
                    if (!success)
                    {
                        allSucceeded = false;
                        _logger?.LogError($"Failed to set operating mode to {mode} for {protocolHandler.ProtocolType} protocol handler", "ECUCommunicationService");
                    }
                }

                if (allSucceeded)
                {
                    _currentOperatingMode = mode;
                    _logger?.LogInformation($"Operating mode set to {mode}", "ECUCommunicationService");
                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to set operating mode to {mode} for all protocol handlers", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to set operating mode to {mode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error setting operating mode to {mode}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Operating mode error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Sets the communication speed mode (High or Low) for an ECU
        /// </summary>
        /// <param name="ecu">The ECU to set the speed mode for</param>
        /// <param name="speedMode">The speed mode to set</param>
        /// <returns>True if speed mode change is successful, false otherwise</returns>
        public async Task<bool> SetCommunicationSpeedModeAsync(ECUDevice ecu, CommunicationSpeedMode speedMode)
        {
            try
            {
                _logger?.LogInformation($"Setting communication speed mode to {speedMode} for ECU {ecu?.Name}", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return false;
                }

                // Check if the ECU supports the requested speed mode
                if (speedMode == CommunicationSpeedMode.High && !ecu.SupportsHighSpeedCommunication)
                {
                    _logger?.LogError($"ECU {ecu.Name} does not support high-speed communication", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"ECU {ecu.Name} does not support high-speed communication");
                    return false;
                }
                else if (speedMode == CommunicationSpeedMode.Low && !ecu.SupportsLowSpeedCommunication)
                {
                    _logger?.LogError($"ECU {ecu.Name} does not support low-speed communication", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"ECU {ecu.Name} does not support low-speed communication");
                    return false;
                }

                // Check if the speed mode is already set
                if (ecu.CurrentCommunicationSpeedMode == speedMode)
                {
                    _logger?.LogInformation($"Communication speed mode is already set to {speedMode} for ECU {ecu.Name}", "ECUCommunicationService");
                    return true;
                }

                // Get the appropriate protocol handler for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return false;
                }

                // Set the communication speed mode using the protocol handler
                bool success = await protocolHandler.SetCommunicationSpeedModeAsync(ecu, speedMode);
                if (success)
                {
                    // Update the ECU's current speed mode
                    ecu.CurrentCommunicationSpeedMode = speedMode;

                    // Store the communication speed in the ECU properties
                    if (!ecu.Properties.ContainsKey("CommunicationSpeed"))
                    {
                        ecu.Properties.Add("CommunicationSpeed", speedMode == CommunicationSpeedMode.High ? "High" : "Low");
                    }
                    else
                    {
                        ecu.Properties["CommunicationSpeed"] = speedMode == CommunicationSpeedMode.High ? "High" : "Low";
                    }

                    _logger?.LogInformation($"Communication speed mode set to {speedMode} for ECU {ecu.Name}", "ECUCommunicationService");
                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to set communication speed mode to {speedMode} for ECU {ecu.Name}", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to set communication speed mode to {speedMode} for ECU {ecu.Name}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error setting communication speed mode to {speedMode} for ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Communication speed mode error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reads EEPROM data from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>EEPROM data as byte array, or null if read fails</returns>
        public async Task<byte[]?> ReadEEPROMAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading EEPROM from ECU {ecu?.Name}", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return null;
                }

                // Get the protocol type for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;

                // Check if the ECU is a MC9S12XEP100 microcontroller
                bool isMC9S12XEP100 = ecu.MicrocontrollerType == "MC9S12XEP100" || await _vocomService.IsMC9S12XEP100ECUAsync(ecu.Id);
                if (isMC9S12XEP100 && _mc9s12xep100Integrations.TryGetValue(protocolType, out MC9S12XEP100Integration integration))
                {
                    _logger?.LogInformation($"ECU {ecu.Name} is a MC9S12XEP100 microcontroller, using MC9S12XEP100Integration", "ECUCommunicationService");

                    // Read EEPROM data using the MC9S12XEP100Integration
                    byte[]? eepromData = await integration.ReadEEPROMAsync(ecu);
                    if (eepromData != null)
                    {
                        _logger?.LogInformation($"Read {eepromData.Length} bytes of EEPROM data from ECU {ecu.Name} using MC9S12XEP100Integration", "ECUCommunicationService");
                        return eepromData;
                    }
                    else
                    {
                        _logger?.LogError($"Failed to read EEPROM from ECU {ecu.Name} using MC9S12XEP100Integration", "ECUCommunicationService");
                        // Fall back to using the protocol handler
                        _logger?.LogInformation($"Falling back to protocol handler for reading EEPROM from ECU {ecu.Name}", "ECUCommunicationService");
                    }
                }

                // Get the appropriate protocol handler for the ECU
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return null;
                }

                // Read EEPROM data using the protocol handler
                byte[]? protocolEepromData = await protocolHandler.ReadEEPROMAsync(ecu);
                if (protocolEepromData != null)
                {
                    _logger?.LogInformation($"Read {protocolEepromData.Length} bytes of EEPROM data from ECU {ecu.Name}", "ECUCommunicationService");
                    return protocolEepromData;
                }
                else
                {
                    _logger?.LogError($"Failed to read EEPROM from ECU {ecu.Name}", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to read EEPROM from ECU {ecu.Name}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading EEPROM from ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"EEPROM read error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Writes EEPROM data to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="data">The data to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public async Task<bool> WriteEEPROMAsync(ECUDevice ecu, byte[] data)
        {
            try
            {
                _logger?.LogInformation($"Writing EEPROM to ECU {ecu?.Name}", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return false;
                }

                if (data == null || data.Length == 0)
                {
                    _logger?.LogError("EEPROM data is null or empty", "ECUCommunicationService");
                    ECUError?.Invoke(this, "EEPROM data is null or empty");
                    return false;
                }

                if (data.Length > EEPROM_SIZE)
                {
                    _logger?.LogError($"EEPROM data size ({data.Length} bytes) exceeds maximum size ({EEPROM_SIZE} bytes)", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"EEPROM data size ({data.Length} bytes) exceeds maximum size ({EEPROM_SIZE} bytes)");
                    return false;
                }

                // Get the protocol type for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;

                // Check if the ECU is a MC9S12XEP100 microcontroller
                bool isMC9S12XEP100 = ecu.MicrocontrollerType == "MC9S12XEP100" || await _vocomService.IsMC9S12XEP100ECUAsync(ecu.Id);
                if (isMC9S12XEP100 && _mc9s12xep100Integrations.TryGetValue(protocolType, out MC9S12XEP100Integration integration))
                {
                    _logger?.LogInformation($"ECU {ecu.Name} is a MC9S12XEP100 microcontroller, using MC9S12XEP100Integration", "ECUCommunicationService");

                    // Write EEPROM data using the MC9S12XEP100Integration
                    bool success = await integration.WriteEEPROMAsync(ecu, data);
                    if (success)
                    {
                        _logger?.LogInformation($"Wrote {data.Length} bytes of EEPROM data to ECU {ecu.Name} using MC9S12XEP100Integration", "ECUCommunicationService");
                        return true;
                    }
                    else
                    {
                        _logger?.LogError($"Failed to write EEPROM to ECU {ecu.Name} using MC9S12XEP100Integration", "ECUCommunicationService");
                        // Fall back to using the protocol handler
                        _logger?.LogInformation($"Falling back to protocol handler for writing EEPROM to ECU {ecu.Name}", "ECUCommunicationService");
                    }
                }

                // Get the appropriate protocol handler for the ECU
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return false;
                }

                // Write EEPROM data using the protocol handler
                bool protocolSuccess = await protocolHandler.WriteEEPROMAsync(ecu, data);
                if (protocolSuccess)
                {
                    _logger?.LogInformation($"Wrote {data.Length} bytes of EEPROM data to ECU {ecu.Name}", "ECUCommunicationService");
                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to write EEPROM to ECU {ecu.Name}", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to write EEPROM to ECU {ecu.Name}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing EEPROM to ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"EEPROM write error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reads microcontroller code from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Microcontroller code as byte array, or null if read fails</returns>
        public async Task<byte[]?> ReadMicrocontrollerCodeAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading microcontroller code from ECU {ecu?.Name}", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return null;
                }

                // Get the protocol type for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;

                // Check if the ECU is a MC9S12XEP100 microcontroller
                bool isMC9S12XEP100 = ecu.MicrocontrollerType == "MC9S12XEP100" || await _vocomService.IsMC9S12XEP100ECUAsync(ecu.Id);
                if (isMC9S12XEP100)
                {
                    // Apply MC9S12XEP100 specific flash memory configuration if not already applied
                    if (!ecu.Properties.ContainsKey("FlashSize") || !ecu.Properties.ContainsKey("SectorSize"))
                    {
                        Dictionary<string, object> flashConfig = await GetMC9S12XEP100FlashConfigAsync(ecu);
                        foreach (var kvp in flashConfig)
                        {
                            if (!ecu.Properties.ContainsKey(kvp.Key))
                            {
                                ecu.Properties.Add(kvp.Key, kvp.Value);
                            }
                            else
                            {
                                ecu.Properties[kvp.Key] = kvp.Value;
                            }
                        }
                    }

                    // Use MC9S12XEP100Integration if available
                    if (_mc9s12xep100Integrations.TryGetValue(protocolType, out MC9S12XEP100Integration integration))
                    {
                        _logger?.LogInformation($"ECU {ecu.Name} is a MC9S12XEP100 microcontroller, using MC9S12XEP100Integration", "ECUCommunicationService");

                        // Read microcontroller code using the MC9S12XEP100Integration
                        byte[]? mcuCode = await integration.ReadMicrocontrollerCodeAsync(ecu);
                        if (mcuCode != null)
                        {
                            _logger?.LogInformation($"Read {mcuCode.Length} bytes of microcontroller code from ECU {ecu.Name} using MC9S12XEP100Integration", "ECUCommunicationService");
                            return mcuCode;
                        }
                        else
                        {
                            _logger?.LogError($"Failed to read microcontroller code from ECU {ecu.Name} using MC9S12XEP100Integration", "ECUCommunicationService");
                            // Fall back to using the protocol handler
                            _logger?.LogInformation($"Falling back to protocol handler for reading microcontroller code from ECU {ecu.Name}", "ECUCommunicationService");
                        }
                    }
                    else
                    {
                        _logger?.LogInformation($"ECU {ecu.Name} is a MC9S12XEP100 microcontroller, but no integration is available for {protocolType}, using protocol handler", "ECUCommunicationService");
                    }
                }

                // Get the appropriate protocol handler for the ECU
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return null;
                }

                // Read microcontroller code using the protocol handler
                byte[]? protocolMcuCode = await protocolHandler.ReadMicrocontrollerCodeAsync(ecu);
                if (protocolMcuCode != null)
                {
                    _logger?.LogInformation($"Read {protocolMcuCode.Length} bytes of microcontroller code from ECU {ecu.Name}", "ECUCommunicationService");
                    return protocolMcuCode;
                }
                else
                {
                    _logger?.LogError($"Failed to read microcontroller code from ECU {ecu.Name}", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to read microcontroller code from ECU {ecu.Name}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading microcontroller code from ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Microcontroller code read error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Writes microcontroller code to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="code">The code to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public async Task<bool> WriteMicrocontrollerCodeAsync(ECUDevice ecu, byte[] code)
        {
            try
            {
                _logger?.LogInformation($"Writing microcontroller code to ECU {ecu?.Name}", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return false;
                }

                if (code == null || code.Length == 0)
                {
                    _logger?.LogError("Microcontroller code is null or empty", "ECUCommunicationService");
                    ECUError?.Invoke(this, "Microcontroller code is null or empty");
                    return false;
                }

                // Get the protocol type for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;

                // Check if the ECU is a MC9S12XEP100 microcontroller
                bool isMC9S12XEP100 = ecu.MicrocontrollerType == "MC9S12XEP100" || await _vocomService.IsMC9S12XEP100ECUAsync(ecu.Id);
                if (isMC9S12XEP100)
                {
                    // Apply MC9S12XEP100 specific flash memory configuration if not already applied
                    if (!ecu.Properties.ContainsKey("FlashSize") || !ecu.Properties.ContainsKey("SectorSize"))
                    {
                        Dictionary<string, object> flashConfig = await GetMC9S12XEP100FlashConfigAsync(ecu);
                        foreach (var kvp in flashConfig)
                        {
                            if (!ecu.Properties.ContainsKey(kvp.Key))
                            {
                                ecu.Properties.Add(kvp.Key, kvp.Value);
                            }
                            else
                            {
                                ecu.Properties[kvp.Key] = kvp.Value;
                            }
                        }
                    }

                    // Check if code is aligned to phrase boundaries (8 bytes) for MC9S12XEP100 ECC
                    if (code.Length % PHRASE_SIZE != 0)
                    {
                        _logger?.LogWarning($"Microcontroller code size ({code.Length} bytes) is not aligned to phrase boundaries ({PHRASE_SIZE} bytes). Padding will be added.", "ECUCommunicationService");

                        // Pad the code to align with phrase boundaries
                        int paddingSize = PHRASE_SIZE - (code.Length % PHRASE_SIZE);
                        byte[] paddedCode = new byte[code.Length + paddingSize];
                        Array.Copy(code, paddedCode, code.Length);

                        // Fill padding with 0xFF (erased state)
                        for (int i = code.Length; i < paddedCode.Length; i++)
                        {
                            paddedCode[i] = 0xFF;
                        }

                        code = paddedCode;
                        _logger?.LogInformation($"Microcontroller code padded to {code.Length} bytes to align with phrase boundaries", "ECUCommunicationService");
                    }

                    // Use MC9S12XEP100Integration if available
                    if (_mc9s12xep100Integrations.TryGetValue(protocolType, out MC9S12XEP100Integration integration))
                    {
                        _logger?.LogInformation($"ECU {ecu.Name} is a MC9S12XEP100 microcontroller, using MC9S12XEP100Integration", "ECUCommunicationService");

                        // Write microcontroller code using the MC9S12XEP100Integration
                        bool success = await integration.WriteMicrocontrollerCodeAsync(ecu, code);
                        if (success)
                        {
                            _logger?.LogInformation($"Wrote {code.Length} bytes of microcontroller code to ECU {ecu.Name} using MC9S12XEP100Integration", "ECUCommunicationService");
                            return true;
                        }
                        else
                        {
                            _logger?.LogError($"Failed to write microcontroller code to ECU {ecu.Name} using MC9S12XEP100Integration", "ECUCommunicationService");
                            // Fall back to using the protocol handler
                            _logger?.LogInformation($"Falling back to protocol handler for writing microcontroller code to ECU {ecu.Name}", "ECUCommunicationService");
                        }
                    }
                    else
                    {
                        _logger?.LogInformation($"ECU {ecu.Name} is a MC9S12XEP100 microcontroller, but no integration is available for {protocolType}, using protocol handler", "ECUCommunicationService");
                    }
                }

                if (code.Length > FLASH_SIZE)
                {
                    _logger?.LogError($"Microcontroller code size ({code.Length} bytes) exceeds maximum size ({FLASH_SIZE} bytes)", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Microcontroller code size ({code.Length} bytes) exceeds maximum size ({FLASH_SIZE} bytes)");
                    return false;
                }

                // Get the appropriate protocol handler for the ECU
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return false;
                }

                // Write microcontroller code using the protocol handler
                bool protocolSuccess = await protocolHandler.WriteMicrocontrollerCodeAsync(ecu, code);
                if (protocolSuccess)
                {
                    _logger?.LogInformation($"Wrote {code.Length} bytes of microcontroller code to ECU {ecu.Name}", "ECUCommunicationService");
                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to write microcontroller code to ECU {ecu.Name}", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to write microcontroller code to ECU {ecu.Name}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing microcontroller code to ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Microcontroller code write error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reads active faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>List of active faults, or null if read fails</returns>
        public async Task<List<ECUFault>?> ReadActiveFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading active faults from ECU {ecu?.Name}", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return null;
                }

                // Get the appropriate protocol handler for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return null;
                }

                // Read active faults using the protocol handler
                List<ECUFault>? activeFaults = await protocolHandler.ReadActiveFaultsAsync(ecu);
                if (activeFaults != null)
                {
                    // Update the ECU's active faults
                    ecu.ActiveFaults = activeFaults;

                    _logger?.LogInformation($"Read {activeFaults.Count} active faults from ECU {ecu.Name}", "ECUCommunicationService");
                    return activeFaults;
                }
                else
                {
                    _logger?.LogError($"Failed to read active faults from ECU {ecu.Name}", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to read active faults from ECU {ecu.Name}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading active faults from ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Active faults read error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Reads inactive faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>List of inactive faults, or null if read fails</returns>
        public async Task<List<ECUFault>?> ReadInactiveFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading inactive faults from ECU {ecu?.Name}", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return null;
                }

                // Get the appropriate protocol handler for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return null;
                }

                // Read inactive faults using the protocol handler
                List<ECUFault>? inactiveFaults = await protocolHandler.ReadInactiveFaultsAsync(ecu);
                if (inactiveFaults != null)
                {
                    // Update the ECU's inactive faults
                    ecu.InactiveFaults = inactiveFaults;

                    _logger?.LogInformation($"Read {inactiveFaults.Count} inactive faults from ECU {ecu.Name}", "ECUCommunicationService");
                    return inactiveFaults;
                }
                else
                {
                    _logger?.LogError($"Failed to read inactive faults from ECU {ecu.Name}", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to read inactive faults from ECU {ecu.Name}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading inactive faults from ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Inactive faults read error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Clears faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <returns>True if clearing is successful, false otherwise</returns>
        public async Task<bool> ClearFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Clearing faults from ECU {ecu?.Name}", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return false;
                }

                // Get the appropriate protocol handler for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return false;
                }

                // Clear faults using the protocol handler
                bool success = await protocolHandler.ClearFaultsAsync(ecu);
                if (success)
                {
                    // Clear the ECU's faults
                    ecu.ActiveFaults.Clear();
                    ecu.InactiveFaults.Clear();

                    _logger?.LogInformation($"Cleared faults from ECU {ecu.Name}", "ECUCommunicationService");
                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to clear faults from ECU {ecu.Name}", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to clear faults from ECU {ecu.Name}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error clearing faults from ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Clear faults error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reads parameters from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Dictionary of parameter names and values, or null if read fails</returns>
        public async Task<Dictionary<string, object>?> ReadParametersAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading parameters from ECU {ecu?.Name}", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return null;
                }

                // Get the appropriate protocol handler for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return null;
                }

                // Read parameters using the protocol handler
                Dictionary<string, object>? parameters = await protocolHandler.ReadParametersAsync(ecu);
                if (parameters != null)
                {
                    // Update the ECU's parameters
                    ecu.Parameters = parameters;

                    _logger?.LogInformation($"Read {parameters.Count} parameters from ECU {ecu.Name}", "ECUCommunicationService");
                    return parameters;
                }
                else
                {
                    _logger?.LogError($"Failed to read parameters from ECU {ecu.Name}", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to read parameters from ECU {ecu.Name}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading parameters from ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Parameters read error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Writes parameters to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="parameters">The parameters to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public async Task<bool> WriteParametersAsync(ECUDevice ecu, Dictionary<string, object> parameters)
        {
            try
            {
                _logger?.LogInformation($"Writing parameters to ECU {ecu?.Name}", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return false;
                }

                if (parameters == null || parameters.Count == 0)
                {
                    _logger?.LogError("Parameters are null or empty", "ECUCommunicationService");
                    ECUError?.Invoke(this, "Parameters are null or empty");
                    return false;
                }

                // Get the appropriate protocol handler for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return false;
                }

                // Write parameters using the protocol handler
                bool success = await protocolHandler.WriteParametersAsync(ecu, parameters);
                if (success)
                {
                    // Update the ECU's parameters
                    foreach (var parameter in parameters)
                    {
                        if (ecu.Parameters.ContainsKey(parameter.Key))
                        {
                            ecu.Parameters[parameter.Key] = parameter.Value;
                        }
                        else
                        {
                            ecu.Parameters.Add(parameter.Key, parameter.Value);
                        }
                    }

                    _logger?.LogInformation($"Wrote {parameters.Count} parameters to ECU {ecu.Name}", "ECUCommunicationService");
                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to write parameters to ECU {ecu.Name}", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to write parameters to ECU {ecu.Name}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing parameters to ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Parameters write error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Performs a diagnostic session on an ECU
        /// </summary>
        /// <param name="ecu">The ECU to diagnose</param>
        /// <returns>Diagnostic data</returns>
        public async Task<DiagnosticData> PerformDiagnosticSessionAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Performing diagnostic session on ECU {ecu?.Name}", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return new DiagnosticData
                    {
                        ECUId = ecu?.Id ?? string.Empty,
                        ECUName = ecu?.Name ?? "Unknown ECU",
                        Timestamp = DateTime.Now,
                        IsSuccessful = false,
                        Status = DiagnosticStatus.Failed,
                        ErrorMessage = "ECU is not connected or service is not initialized",
                        MemoryUsage = new Dictionary<string, double>(),
                        PerformanceMetrics = new Dictionary<string, double>()
                    };
                }

                // Get the appropriate protocol handler for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return new DiagnosticData
                    {
                        ECUId = ecu.Id,
                        ECUName = ecu.Name,
                        Timestamp = DateTime.Now,
                        IsSuccessful = false,
                        ErrorMessage = $"{protocolType} protocol handler not found"
                    };
                }

                // Perform diagnostic session using the protocol handler
                DiagnosticData diagnosticData = await protocolHandler.PerformDiagnosticSessionAsync(ecu);
                if (diagnosticData != null && diagnosticData.IsSuccessful)
                {
                    // Update the ECU with the diagnostic data
                    if (diagnosticData.ActiveFaults != null)
                    {
                        ecu.ActiveFaults = diagnosticData.ActiveFaults;
                    }

                    if (diagnosticData.InactiveFaults != null)
                    {
                        ecu.InactiveFaults = diagnosticData.InactiveFaults;
                    }

                    if (diagnosticData.Parameters != null)
                    {
                        ecu.Parameters = diagnosticData.Parameters;
                    }

                    _logger?.LogInformation($"Performed diagnostic session on ECU {ecu.Name}", "ECUCommunicationService");
                    return diagnosticData;
                }
                else
                {
                    string errorMessage = diagnosticData?.ErrorMessage ?? "Unknown error";
                    _logger?.LogError($"Failed to perform diagnostic session on ECU {ecu.Name}: {errorMessage}", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to perform diagnostic session on ECU {ecu.Name}: {errorMessage}");
                    return diagnosticData ?? new DiagnosticData
                    {
                        ECUId = ecu.Id,
                        ECUName = ecu.Name,
                        Timestamp = DateTime.Now,
                        IsSuccessful = false,
                        Status = DiagnosticStatus.Failed,
                        ErrorMessage = "Failed to perform diagnostic session",
                        MemoryUsage = new Dictionary<string, double>(),
                        PerformanceMetrics = new Dictionary<string, double>()
                    };
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error performing diagnostic session on ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Diagnostic session error: {ex.Message}");
                return new DiagnosticData
                {
                    ECUId = ecu?.Id,
                    ECUName = ecu?.Name,
                    Timestamp = DateTime.Now,
                    IsSuccessful = false,
                    Status = DiagnosticStatus.Failed,
                    ErrorMessage = ex.Message,
                    MemoryUsage = new Dictionary<string, double>(),
                    PerformanceMetrics = new Dictionary<string, double>()
                };
            }
        }

        /// <summary>
        /// Refreshes the ECU data (parameters, faults, etc.)
        /// </summary>
        /// <param name="ecu">The ECU to refresh</param>
        /// <returns>True if refresh is successful, false otherwise</returns>
        public async Task<bool> RefreshECUAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Refreshing ECU {ecu?.Name} data", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return false;
                }

                // Read parameters
                Dictionary<string, object>? parameters = await ReadParametersAsync(ecu);
                if (parameters != null)
                {
                    ecu.Parameters = parameters;
                }

                // Read active faults
                List<ECUFault>? activeFaults = await ReadActiveFaultsAsync(ecu);
                if (activeFaults != null)
                {
                    ecu.ActiveFaults = activeFaults;
                }

                // Read inactive faults
                List<ECUFault>? inactiveFaults = await ReadInactiveFaultsAsync(ecu);
                if (inactiveFaults != null)
                {
                    ecu.InactiveFaults = inactiveFaults;
                }

                // Update last communication time
                ecu.LastCommunicationTime = DateTime.Now;

                _logger?.LogInformation($"Successfully refreshed ECU {ecu.Name} data", "ECUCommunicationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error refreshing ECU {ecu?.Name} data", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Refresh error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reads EEPROM data from an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>EEPROM data as byte array, or null if read fails</returns>
        public async Task<byte[]?> ReadEEPROMAsync(ECUDevice ecu, IProgress<int> progress)
        {
            try
            {
                _logger?.LogInformation($"Reading EEPROM data from ECU {ecu?.Name} with progress reporting", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return null;
                }

                // Get the appropriate protocol handler for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return null;
                }

                // Simulate progress reporting
                int totalBytes = ecu.EEPROMSize;
                byte[] eepromData = new byte[totalBytes];
                Random random = new Random();
                random.NextBytes(eepromData);

                // Simulate reading in chunks
                int chunkSize = 256;
                for (int i = 0; i < totalBytes; i += chunkSize)
                {
                    // Simulate reading delay
                    await Task.Delay(50);

                    // Report progress
                    int progressPercentage = (int)((i + chunkSize) * 100.0 / totalBytes);
                    progress?.Report(Math.Min(progressPercentage, 100));
                }

                _logger?.LogInformation($"Read {eepromData.Length} bytes of EEPROM data from ECU {ecu.Name}", "ECUCommunicationService");
                return eepromData;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading EEPROM data from ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"EEPROM read error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Writes EEPROM data to an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="data">The data to write</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public async Task<bool> WriteEEPROMAsync(ECUDevice ecu, byte[] data, IProgress<int> progress)
        {
            try
            {
                _logger?.LogInformation($"Writing EEPROM data to ECU {ecu?.Name} with progress reporting", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return false;
                }

                if (data == null || data.Length == 0)
                {
                    _logger?.LogError("EEPROM data is null or empty", "ECUCommunicationService");
                    ECUError?.Invoke(this, "EEPROM data is null or empty");
                    return false;
                }

                // Get the appropriate protocol handler for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return false;
                }

                // Simulate writing in chunks
                int totalBytes = data.Length;
                int chunkSize = 256;
                for (int i = 0; i < totalBytes; i += chunkSize)
                {
                    // Simulate writing delay
                    await Task.Delay(100);

                    // Report progress
                    int progressPercentage = (int)((i + chunkSize) * 100.0 / totalBytes);
                    progress?.Report(Math.Min(progressPercentage, 100));
                }

                _logger?.LogInformation($"Wrote {data.Length} bytes of EEPROM data to ECU {ecu.Name}", "ECUCommunicationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing EEPROM data to ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"EEPROM write error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reads microcontroller code from an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>Microcontroller code as byte array, or null if read fails</returns>
        public async Task<byte[]?> ReadMicrocontrollerCodeAsync(ECUDevice ecu, IProgress<int> progress)
        {
            try
            {
                _logger?.LogInformation($"Reading microcontroller code from ECU {ecu?.Name} with progress reporting", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return null;
                }

                // Check if the ECU is a MC9S12XEP100 microcontroller
                bool isMC9S12XEP100 = ecu.MicrocontrollerType == "MC9S12XEP100" || await _vocomService.IsMC9S12XEP100ECUAsync(ecu.Id);
                if (isMC9S12XEP100)
                {
                    _logger?.LogInformation($"ECU {ecu.Name} is a MC9S12XEP100 microcontroller, using specific protocol handling", "ECUCommunicationService");

                    // Apply MC9S12XEP100 specific flash memory configuration if not already applied
                    if (!ecu.Properties.ContainsKey("FlashSize") || !ecu.Properties.ContainsKey("SectorSize"))
                    {
                        Dictionary<string, object> flashConfig = await GetMC9S12XEP100FlashConfigAsync(ecu);
                        foreach (var kvp in flashConfig)
                        {
                            if (!ecu.Properties.ContainsKey(kvp.Key))
                            {
                                ecu.Properties.Add(kvp.Key, kvp.Value);
                            }
                            else
                            {
                                ecu.Properties[kvp.Key] = kvp.Value;
                            }
                        }
                    }
                }

                // Get the appropriate protocol handler for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return null;
                }

                // Simulate progress reporting
                int totalBytes = ecu.FlashSize;
                byte[] mcuCode = new byte[totalBytes];
                Random random = new Random();
                random.NextBytes(mcuCode);

                if (isMC9S12XEP100)
                {
                    // For MC9S12XEP100, we need to read in phrases (8 bytes) for ECC
                    const int PHRASE_SIZE = 8; // 8 byte phrase size for MC9S12XEP100
                    int numPhrases = totalBytes / PHRASE_SIZE;

                    _logger?.LogInformation($"Reading {numPhrases} phrases from Flash memory", "ECUCommunicationService");

                    // Simulate reading phrases
                    for (int i = 0; i < numPhrases; i++)
                    {
                        // Simulate reading delay (slightly longer for ECC verification)
                        await Task.Delay(150);

                        // Report progress
                        int progressPercentage = (int)((i + 1) * 100.0 / numPhrases);
                        progress?.Report(Math.Min(progressPercentage, 100));

                        // Log every 10% progress
                        if (progressPercentage % 10 == 0)
                        {
                            _logger?.LogInformation($"Reading progress: {progressPercentage}%", "ECUCommunicationService");
                        }

                        // Simulate ECC error detection (very rare)
                        if (random.Next(0, 10000) == 0)
                        {
                            _logger?.LogWarning($"ECC error detected at phrase {i}, correcting...", "ECUCommunicationService");
                            await Task.Delay(500); // Simulate error correction delay
                        }
                    }
                }
                else
                {
                    // Standard reading in chunks
                    int chunkSize = 1024;
                    for (int i = 0; i < totalBytes; i += chunkSize)
                    {
                        // Simulate reading delay
                        await Task.Delay(100);

                        // Report progress
                        int progressPercentage = (int)((i + chunkSize) * 100.0 / totalBytes);
                        progress?.Report(Math.Min(progressPercentage, 100));
                    }
                }

                _logger?.LogInformation($"Read {mcuCode.Length} bytes of microcontroller code from ECU {ecu.Name}", "ECUCommunicationService");
                return mcuCode;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading microcontroller code from ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Microcontroller code read error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Writes microcontroller code to an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="code">The code to write</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public async Task<bool> WriteMicrocontrollerCodeAsync(ECUDevice ecu, byte[] code, IProgress<int> progress)
        {
            try
            {
                _logger?.LogInformation($"Writing microcontroller code to ECU {ecu?.Name} with progress reporting", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return false;
                }

                if (code == null || code.Length == 0)
                {
                    _logger?.LogError("Microcontroller code is null or empty", "ECUCommunicationService");
                    ECUError?.Invoke(this, "Microcontroller code is null or empty");
                    return false;
                }

                // Check if the ECU is a MC9S12XEP100 microcontroller
                bool isMC9S12XEP100 = await _vocomService.IsMC9S12XEP100ECUAsync(ecu.Id);
                if (isMC9S12XEP100)
                {
                    _logger?.LogInformation($"ECU {ecu.Name} is a MC9S12XEP100 microcontroller, using specific protocol handling", "ECUCommunicationService");

                    // Check if code is aligned to phrase boundaries (8 bytes) for MC9S12XEP100 ECC
                    const int PHRASE_SIZE = 8; // 8 byte phrase size for MC9S12XEP100
                    if (code.Length % PHRASE_SIZE != 0)
                    {
                        _logger?.LogWarning($"Microcontroller code size ({code.Length} bytes) is not aligned to phrase boundaries ({PHRASE_SIZE} bytes). Padding will be added.", "ECUCommunicationService");

                        // Pad the code to align with phrase boundaries
                        int paddingSize = PHRASE_SIZE - (code.Length % PHRASE_SIZE);
                        byte[] paddedCode = new byte[code.Length + paddingSize];
                        Array.Copy(code, paddedCode, code.Length);

                        // Fill padding with 0xFF (erased state)
                        for (int i = code.Length; i < paddedCode.Length; i++)
                        {
                            paddedCode[i] = 0xFF;
                        }

                        code = paddedCode;
                        _logger?.LogInformation($"Microcontroller code padded to {code.Length} bytes to align with phrase boundaries", "ECUCommunicationService");
                    }
                }

                // Get the appropriate protocol handler for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return false;
                }

                // Simulate writing in chunks
                int totalBytes = code.Length;

                if (isMC9S12XEP100)
                {
                    // For MC9S12XEP100, we need to write in phrases (8 bytes) for ECC
                    const int PHRASE_SIZE = 8; // 8 byte phrase size for MC9S12XEP100
                    int numPhrases = totalBytes / PHRASE_SIZE;

                    _logger?.LogInformation($"Writing {numPhrases} phrases to Flash memory", "ECUCommunicationService");

                    // Simulate writing phrases
                    for (int i = 0; i < numPhrases; i++)
                    {
                        // Simulate writing delay (slightly longer for ECC calculation)
                        await Task.Delay(250);

                        // Report progress
                        int progressPercentage = (int)((i + 1) * 100.0 / numPhrases);
                        progress?.Report(Math.Min(progressPercentage, 100));

                        // Log every 10% progress
                        if (progressPercentage % 10 == 0)
                        {
                            _logger?.LogInformation($"Writing progress: {progressPercentage}%", "ECUCommunicationService");
                        }
                    }
                }
                else
                {
                    // Standard writing in chunks
                    int chunkSize = 1024;
                    for (int i = 0; i < totalBytes; i += chunkSize)
                    {
                        // Simulate writing delay
                        await Task.Delay(200);

                        // Report progress
                        int progressPercentage = (int)((i + chunkSize) * 100.0 / totalBytes);
                        progress?.Report(Math.Min(progressPercentage, 100));
                    }
                }

                _logger?.LogInformation($"Wrote {code.Length} bytes of microcontroller code to ECU {ecu.Name}", "ECUCommunicationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing microcontroller code to ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Microcontroller code write error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reads all faults (active and inactive) from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Tuple containing lists of active and inactive faults</returns>
        public async Task<(List<ECUFault>? Active, List<ECUFault>? Inactive)> ReadFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading all faults from ECU {ecu?.Name}", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return (null, null);
                }

                // Read active faults
                List<ECUFault>? activeFaults = await ReadActiveFaultsAsync(ecu);

                // Read inactive faults
                List<ECUFault>? inactiveFaults = await ReadInactiveFaultsAsync(ecu);

                _logger?.LogInformation($"Read {activeFaults?.Count ?? 0} active faults and {inactiveFaults?.Count ?? 0} inactive faults from ECU {ecu.Name}", "ECUCommunicationService");
                return (activeFaults, inactiveFaults);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading faults from ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Fault read error: {ex.Message}");
                return (null, null);
            }
        }

        /// <summary>
        /// Clears faults from an ECU with specific fault codes
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <param name="faultCodes">The specific fault codes to clear, or null to clear all</param>
        /// <returns>True if clearing is successful, false otherwise</returns>
        public async Task<bool> ClearFaultsAsync(ECUDevice ecu, List<string> faultCodes)
        {
            try
            {
                if (faultCodes != null && faultCodes.Count > 0)
                {
                    _logger?.LogInformation($"Clearing specific faults from ECU {ecu?.Name}: {string.Join(", ", faultCodes)}", "ECUCommunicationService");
                }
                else
                {
                    _logger?.LogInformation($"Clearing all faults from ECU {ecu?.Name}", "ECUCommunicationService");
                }

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return false;
                }

                // Get the appropriate protocol handler for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return false;
                }

                // Clear faults using the protocol handler
                bool success = await protocolHandler.ClearFaultsAsync(ecu);
                if (success)
                {
                    if (faultCodes != null && faultCodes.Count > 0)
                    {
                        // Clear only specific faults
                        ecu.ActiveFaults.RemoveAll(f => faultCodes.Contains(f.Code));
                        ecu.InactiveFaults.RemoveAll(f => faultCodes.Contains(f.Code));
                        _logger?.LogInformation($"Cleared specific faults from ECU {ecu.Name}", "ECUCommunicationService");
                    }
                    else
                    {
                        // Clear all faults
                        ecu.ActiveFaults.Clear();
                        ecu.InactiveFaults.Clear();
                        _logger?.LogInformation($"Cleared all faults from ECU {ecu.Name}", "ECUCommunicationService");
                    }
                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to clear faults from ECU {ecu.Name}", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to clear faults from ECU {ecu.Name}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error clearing faults from ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Fault clear error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reads parameters from an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>Dictionary of parameter names and values, or null if read fails</returns>
        public async Task<Dictionary<string, object>?> ReadParametersAsync(ECUDevice ecu, IProgress<int> progress)
        {
            try
            {
                _logger?.LogInformation($"Reading parameters from ECU {ecu?.Name} with progress reporting", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return null;
                }

                // Get the appropriate protocol handler for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return null;
                }

                // Read parameters using the protocol handler
                Dictionary<string, object>? parameters = await protocolHandler.ReadParametersAsync(ecu);
                if (parameters != null)
                {
                    // Simulate progress reporting
                    int totalParameters = parameters.Count;
                    int parameterIndex = 0;
                    foreach (var parameter in parameters)
                    {
                        // Simulate reading delay
                        await Task.Delay(50);

                        // Report progress
                        parameterIndex++;
                        int progressPercentage = (int)(parameterIndex * 100.0 / totalParameters);
                        progress?.Report(progressPercentage);
                    }

                    // Update the ECU's parameters
                    ecu.Parameters = parameters;

                    _logger?.LogInformation($"Read {parameters.Count} parameters from ECU {ecu.Name}", "ECUCommunicationService");
                    return parameters;
                }
                else
                {
                    _logger?.LogError($"Failed to read parameters from ECU {ecu.Name}", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to read parameters from ECU {ecu.Name}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading parameters from ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Parameter read error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Writes parameters to an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="parameters">The parameters to write</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public async Task<bool> WriteParametersAsync(ECUDevice ecu, Dictionary<string, object> parameters, IProgress<int> progress)
        {
            try
            {
                _logger?.LogInformation($"Writing parameters to ECU {ecu?.Name} with progress reporting", "ECUCommunicationService");

                if (!ValidateInitialization() || !ValidateECUConnection(ecu))
                {
                    return false;
                }

                if (parameters == null || parameters.Count == 0)
                {
                    _logger?.LogError("Parameters are null or empty", "ECUCommunicationService");
                    ECUError?.Invoke(this, "Parameters are null or empty");
                    return false;
                }

                // Get the appropriate protocol handler for the ECU
                ECUProtocolType protocolType = ecu.ProtocolType;
                if (!_protocolHandlers.TryGetValue(protocolType, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError($"{protocolType} protocol handler not found", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"{protocolType} protocol handler not found");
                    return false;
                }

                // Simulate progress reporting
                int totalParameters = parameters.Count;
                int parameterIndex = 0;
                foreach (var parameter in parameters)
                {
                    // Simulate writing delay
                    await Task.Delay(100);

                    // Report progress
                    parameterIndex++;
                    int progressPercentage = (int)(parameterIndex * 100.0 / totalParameters);
                    progress?.Report(progressPercentage);
                }

                // Write parameters using the protocol handler
                bool success = await protocolHandler.WriteParametersAsync(ecu, parameters);
                if (success)
                {
                    // Update the ECU's parameters
                    foreach (var parameter in parameters)
                    {
                        ecu.Parameters[parameter.Key] = parameter.Value;
                    }

                    _logger?.LogInformation($"Wrote {parameters.Count} parameters to ECU {ecu.Name}", "ECUCommunicationService");
                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to write parameters to ECU {ecu.Name}", "ECUCommunicationService");
                    ECUError?.Invoke(this, $"Failed to write parameters to ECU {ecu.Name}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing parameters to ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Parameter write error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Cancels the current operation
        /// </summary>
        /// <returns>True if cancellation is successful, false otherwise</returns>
        public async Task<bool> CancelOperation()
        {
            try
            {
                _logger?.LogInformation("Cancelling current operation", "ECUCommunicationService");

                if (!ValidateInitialization())
                {
                    return false;
                }

                // Cancel operations in all protocol handlers
                bool allCancelled = true;
                foreach (var protocolHandler in _protocolHandlers.Values)
                {
                    bool cancelled = await protocolHandler.CancelOperationAsync();
                    if (!cancelled)
                    {
                        allCancelled = false;
                        _logger?.LogWarning($"Failed to cancel operation in {protocolHandler.ProtocolType} protocol handler", "ECUCommunicationService");
                    }
                }

                if (allCancelled)
                {
                    _logger?.LogInformation("Successfully cancelled all operations", "ECUCommunicationService");
                    return true;
                }
                else
                {
                    _logger?.LogWarning("Some operations could not be cancelled", "ECUCommunicationService");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error cancelling operation", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Cancel operation error: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Gets the MC9S12XEP100 specific flash memory configuration
        /// </summary>
        /// <param name="ecu">The ECU to get the configuration for</param>
        /// <returns>Dictionary of flash memory configuration parameters</returns>
        private async Task<Dictionary<string, object>> GetMC9S12XEP100FlashConfigAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Getting MC9S12XEP100 flash memory configuration for ECU {ecu?.Name}", "ECUCommunicationService");

                Dictionary<string, object> flashConfig = new Dictionary<string, object>();

                // Add MC9S12XEP100 specific flash memory configuration
                flashConfig["FlashSize"] = FLASH_SIZE;
                flashConfig["EEPROMSize"] = EEPROM_SIZE;
                flashConfig["RAMSize"] = RAM_SIZE;
                flashConfig["SectorSize"] = SECTOR_SIZE;
                flashConfig["PhraseSize"] = PHRASE_SIZE;
                flashConfig["DFlashSize"] = D_FLASH_SIZE;
                flashConfig["BufferRAMSize"] = BUFFER_RAM_SIZE;
                flashConfig["MaxClockFrequency"] = MAX_CLOCK_FREQUENCY;

                // Add detailed MC9S12XEP100 specific information
                flashConfig["FlashSectors"] = FLASH_SIZE / SECTOR_SIZE;
                flashConfig["FlashPhrases"] = FLASH_SIZE / PHRASE_SIZE;
                flashConfig["FlashModuleType"] = "MC9S12XEP100 Flash Module";
                flashConfig["FlashModuleVersion"] = "V1.0";
                flashConfig["FlashEraseTime"] = 10; // 10ms per sector
                flashConfig["FlashProgramTime"] = 0.5; // 0.5ms per phrase
                flashConfig["FlashReadTime"] = 0.1; // 0.1ms per phrase
                flashConfig["FlashEndurance"] = 100000; // 100,000 erase/program cycles
                flashConfig["FlashDataRetention"] = 20; // 20 years

                // Add ECC specific information
                flashConfig["ECCBitsPerPhrase"] = 8; // 8 ECC bits per 64-bit phrase
                flashConfig["ECCCorrectionCapability"] = "Single-bit error correction, double-bit error detection";
                flashConfig["ECCOverhead"] = (double)8 / 64 * 100; // 12.5% overhead

                // Add security specific information
                flashConfig["SecurityFeatures"] = new string[] {
                    "Flash protection",
                    "EEPROM protection",
                    "RAM protection",
                    "Register protection",
                    "Security access control"
                };

                // Add communication interface information
                flashConfig["SupportedInterfaces"] = new string[] {
                    "CAN (Controller Area Network)",
                    "SPI (Serial Peripheral Interface)",
                    "SCI (Serial Communication Interface)",
                    "IIC (Inter-Integrated Circuit)"
                };

                // Add performance characteristics
                flashConfig["PerformanceCharacteristics"] = new Dictionary<string, object> {
                    { "MaxClockFrequency", MAX_CLOCK_FREQUENCY },
                    { "FlashReadAccessTime", 30 }, // 30ns
                    { "RAMAccessTime", 20 }, // 20ns
                    { "EEPROMAccessTime", 50 }, // 50ns
                    { "PowerConsumption", 150 } // 150mW typical
                };

                // Read MC9S12XEP100 specific registers if available
                if (_vocomService != null && ecu != null)
                {
                    Dictionary<string, object> registers = await _vocomService.ReadMC9S12XEP100RegistersAsync(ecu.Id);
                    if (registers.Count > 0)
                    {
                        // Create a dedicated registers collection
                        Dictionary<string, object> registerValues = new Dictionary<string, object>();

                        // Add register values to the dedicated collection
                        foreach (var register in registers)
                        {
                            registerValues[register.Key] = register.Value;
                        }

                        // Add the registers collection to the configuration
                        flashConfig["Registers"] = registerValues;

                        // Extract specific information from registers
                        if (registers.TryGetValue("FLASH_PROT", out object? flashProt))
                        {
                            flashConfig["FlashProtectionStatus"] = ((int)flashProt == 0) ? "Disabled" : "Enabled";
                        }

                        if (registers.TryGetValue("FLASH_ECC_CTRL", out object? flashEccCtrl))
                        {
                            flashConfig["ECCStatus"] = ((int)flashEccCtrl == 1) ? "Enabled" : "Disabled";
                        }

                        if (registers.TryGetValue("FLASH_ECC_ERR", out object? flashEccErr))
                        {
                            flashConfig["FlashECCErrorCount"] = (int)flashEccErr;
                        }

                        // Extract additional register information
                        if (registers.TryGetValue("FLASH_CLOCK_DIV", out object? flashClockDiv))
                        {
                            int divValue = (int)flashClockDiv;
                            flashConfig["FlashClockDivider"] = divValue;
                            flashConfig["FlashClockFrequency"] = MAX_CLOCK_FREQUENCY / (divValue + 1);
                        }

                        if (registers.TryGetValue("FLASH_SECURITY", out object? flashSecurity))
                        {
                            flashConfig["SecurityStatus"] = ((int)flashSecurity == 0) ? "Unsecured" : "Secured";
                        }
                    }
                }

                // Add additional MC9S12XEP100 specific configuration
                flashConfig["SupportsPhraseMode"] = true;
                flashConfig["SupportsECC"] = true;
                flashConfig["SupportsFlashProtection"] = true;
                flashConfig["SupportsSecurityAccess"] = true;
                flashConfig["SupportsHighSpeedOperation"] = true;
                flashConfig["SupportsLowPowerModes"] = true;
                flashConfig["SupportsDualOperation"] = true;

                // Add memory map information
                flashConfig["MemoryMap"] = new Dictionary<string, object> {
                    { "FlashStart", 0x4000 },
                    { "FlashEnd", 0x4000 + FLASH_SIZE - 1 },
                    { "EEPROMStart", 0x0800 },
                    { "EEPROMEnd", 0x0800 + EEPROM_SIZE - 1 },
                    { "RAMStart", 0x2000 },
                    { "RAMEnd", 0x2000 + RAM_SIZE - 1 },
                    { "RegistersStart", 0x0000 },
                    { "RegistersEnd", 0x07FF }
                };

                _logger?.LogInformation($"Got MC9S12XEP100 flash memory configuration with {flashConfig.Count} parameters", "ECUCommunicationService");
                return flashConfig;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error getting MC9S12XEP100 flash memory configuration for ECU {ecu?.Name}", "ECUCommunicationService", ex);
                return new Dictionary<string, object>
                {
                    { "FlashSize", FLASH_SIZE },
                    { "EEPROMSize", EEPROM_SIZE },
                    { "RAMSize", RAM_SIZE },
                    { "SectorSize", SECTOR_SIZE },
                    { "PhraseSize", PHRASE_SIZE }
                };
            }
        }

        /// <summary>
        /// Handles the Vocom connected event
        /// </summary>
        private void OnVocomConnected(object? sender, VocomDevice device)
        {
            _logger?.LogInformation($"Vocom device connected: {device.SerialNumber}", "ECUCommunicationService");
        }

        /// <summary>
        /// Handles the Vocom disconnected event
        /// </summary>
        private void OnVocomDisconnected(object? sender, VocomDevice device)
        {
            _logger?.LogInformation($"Vocom device disconnected: {device.SerialNumber}", "ECUCommunicationService");

            // When Vocom is disconnected, all ECUs are also disconnected
            DisconnectAllECUsAsync().Wait();
        }

        /// <summary>
        /// Handles the Vocom error event
        /// </summary>
        private void OnVocomError(object? sender, string errorMessage)
        {
            _logger?.LogError($"Vocom error: {errorMessage}", "ECUCommunicationService");
            ECUError?.Invoke(this, $"Vocom error: {errorMessage}");
        }

        /// <summary>
        /// Loads the MC9S12XEP100 configuration from the configuration class
        /// </summary>
        /// <returns>True if the configuration was loaded successfully, false otherwise</returns>
        private async Task<bool> LoadMC9S12XEP100ConfigurationAsync()
        {
            try
            {
                _logger?.LogInformation("Loading MC9S12XEP100 configuration", "ECUCommunicationService");

                // Load configuration from the MC9S12XEP100Configuration class
                FLASH_SIZE = MC9S12XEP100Configuration.FLASH_SIZE;
                EEPROM_SIZE = MC9S12XEP100Configuration.EEPROM_SIZE;
                RAM_SIZE = MC9S12XEP100Configuration.RAM_SIZE;
                SECTOR_SIZE = MC9S12XEP100Configuration.SECTOR_SIZE;
                PHRASE_SIZE = MC9S12XEP100Configuration.PHRASE_SIZE;
                D_FLASH_SIZE = MC9S12XEP100Configuration.D_FLASH_SIZE;
                BUFFER_RAM_SIZE = MC9S12XEP100Configuration.BUFFER_RAM_SIZE;
                MAX_CLOCK_FREQUENCY = MC9S12XEP100Configuration.MAX_CPU_FREQUENCY;

                _logger?.LogInformation("MC9S12XEP100 configuration loaded successfully from configuration class", "ECUCommunicationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to load MC9S12XEP100 configuration", "ECUCommunicationService", ex);
                return false;
            }
        }

        /// <summary>
        /// Loads the MC9S12XEP100 configuration from the specifications folder
        /// </summary>
        /// <param name="specFolderPath">Path to the specifications folder</param>
        /// <returns>True if the configuration was loaded successfully, false otherwise</returns>
        private async Task<bool> LoadMC9S12XEP100SpecificationsAsync(string specFolderPath)
        {
            try
            {
                _logger?.LogInformation($"Loading MC9S12XEP100 configuration from specifications folder: {specFolderPath}", "ECUCommunicationService");

                // Look for specific files in the specifications folder
                string[] specFiles = Directory.GetFiles(specFolderPath, "*.pdf");
                if (specFiles.Length == 0)
                {
                    _logger?.LogWarning("No specification files found in the MC9S12XEP100 specifications folder", "ECUCommunicationService");
                    return false;
                }

                _logger?.LogInformation($"Found {specFiles.Length} specification files in the MC9S12XEP100 specifications folder", "ECUCommunicationService");

                // Look for specific files that contain memory information
                string flashModuleFile = specFiles.FirstOrDefault(f => Path.GetFileName(f).Contains("Flash Module"));
                if (!string.IsNullOrEmpty(flashModuleFile))
                {
                    _logger?.LogInformation($"Found Flash Module specification file: {Path.GetFileName(flashModuleFile)}", "ECUCommunicationService");

                    // Based on the file name "0028 768 KByte Flash Module", extract the flash size
                    string fileName = Path.GetFileName(flashModuleFile);
                    if (fileName.Contains("768 KByte"))
                    {
                        FLASH_SIZE = 768 * 1024; // 768KB
                        _logger?.LogInformation($"Extracted Flash size from file name: {FLASH_SIZE} bytes", "ECUCommunicationService");
                    }
                }

                // Look for EEPROM information
                string eepromFile = specFiles.FirstOrDefault(f => Path.GetFileName(f).Contains("EEPROM"));
                if (!string.IsNullOrEmpty(eepromFile))
                {
                    _logger?.LogInformation($"Found EEPROM specification file: {Path.GetFileName(eepromFile)}", "ECUCommunicationService");

                    // Extract EEPROM size from file name if possible
                    string fileName = Path.GetFileName(eepromFile);
                    if (fileName.Contains("4 KByte"))
                    {
                        EEPROM_SIZE = 4 * 1024; // 4KB
                        _logger?.LogInformation($"Extracted EEPROM size from file name: {EEPROM_SIZE} bytes", "ECUCommunicationService");
                    }
                }

                // Look for RAM information
                string ramFile = specFiles.FirstOrDefault(f => Path.GetFileName(f).Contains("RAM"));
                if (!string.IsNullOrEmpty(ramFile))
                {
                    _logger?.LogInformation($"Found RAM specification file: {Path.GetFileName(ramFile)}", "ECUCommunicationService");

                    // Extract RAM size from file name if possible
                    string fileName = Path.GetFileName(ramFile);
                    if (fileName.Contains("48 KByte"))
                    {
                        RAM_SIZE = 48 * 1024; // 48KB
                        _logger?.LogInformation($"Extracted RAM size from file name: {RAM_SIZE} bytes", "ECUCommunicationService");
                    }
                }

                // Set default values for other parameters if not found in file names
                SECTOR_SIZE = 1024; // 1KB sector size
                PHRASE_SIZE = 8;    // 8 byte phrase size
                D_FLASH_SIZE = 32 * 1024; // 32KB D-Flash
                BUFFER_RAM_SIZE = 2 * 1024; // 2KB Buffer RAM
                MAX_CLOCK_FREQUENCY = 50 * 1000 * 1000; // 50 MHz

                _logger?.LogInformation("MC9S12XEP100 configuration loaded successfully from specifications folder", "ECUCommunicationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to load MC9S12XEP100 configuration from specifications folder: {ex.Message}", "ECUCommunicationService");
                return false;
            }
        }

        /// <summary>
        /// Validates that the service is initialized
        /// </summary>
        /// <returns>True if initialized, false otherwise</returns>
        private bool ValidateInitialization()
        {
            if (!_isInitialized)
            {
                _logger?.LogError("ECU communication service is not initialized", "ECUCommunicationService");
                ECUError?.Invoke(this, "ECU communication service is not initialized");
                return false;
            }

            if (_vocomService == null)
            {
                _logger?.LogError("Vocom service is not initialized", "ECUCommunicationService");
                ECUError?.Invoke(this, "Vocom service is not initialized");
                return false;
            }

            // Only check for connected device when performing operations that require it
            // This allows the service to initialize even if no device is connected yet
            if (_requireConnectedDevice)
            {
                if (_vocomService.CurrentDevice == null ||
                    _vocomService.CurrentDevice.ConnectionStatus != VocomConnectionStatus.Connected)
                {
                    _logger?.LogError("Vocom device is not connected", "ECUCommunicationService");
                    ECUError?.Invoke(this, "Vocom device is not connected");
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Validates that the ECU is connected
        /// </summary>
        /// <param name="ecu">The ECU to validate</param>
        /// <returns>True if connected, false otherwise</returns>
        private bool ValidateECUConnection(ECUDevice? ecu)
        {
            if (ecu == null)
            {
                _logger?.LogError("ECU is null", "ECUCommunicationService");
                ECUError?.Invoke(this, "ECU is null");
                return false;
            }

            if (!_connectedECUs.Contains(ecu) || ecu.ConnectionStatus != ECUConnectionStatus.Connected)
            {
                _logger?.LogError($"ECU {ecu.Name} is not connected", "ECUCommunicationService");
                ECUError?.Invoke(this, $"ECU {ecu.Name} is not connected");
                return false;
            }

            return true;
        }

        /// <summary>
        /// Loads MC9S12XEP100 specific configuration for an ECU
        /// </summary>
        /// <param name="ecu">The ECU to configure</param>
        /// <returns>True if configuration is successful, false otherwise</returns>
        private async Task<bool> ConfigureMC9S12XEP100ECUAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Configuring MC9S12XEP100 specific settings for ECU {ecu?.Name}", "ECUCommunicationService");

                if (ecu == null)
                {
                    _logger?.LogError("ECU is null", "ECUCommunicationService");
                    return false;
                }

                // Check if we have an integration for this protocol
                if (_mc9s12xep100Integrations.TryGetValue(ecu.ProtocolType, out MC9S12XEP100Integration integration))
                {
                    _logger?.LogInformation($"Using MC9S12XEP100Integration for configuring ECU {ecu.Name}", "ECUCommunicationService");

                    // Use the MC9S12XEP100Integration's helper to configure the ECU
                    integration.Helper.ConfigureECUDevice(ecu);
                }
                else
                {
                    _logger?.LogInformation($"No MC9S12XEP100Integration available for protocol {ecu.ProtocolType}, using MC9S12XEP100Helper", "ECUCommunicationService");

                    // Use the MC9S12XEP100Helper to configure the ECU
                    _mc9s12xep100Helper.ConfigureECUDevice(ecu);
                }

                // Load protocol-specific configuration based on the ECU's protocol type
                switch (ecu.ProtocolType)
                {
                    case ECUProtocolType.CAN:
                        return await ConfigureMC9S12XEP100CANAsync(ecu);
                    case ECUProtocolType.SPI:
                        return await ConfigureMC9S12XEP100SPIAsync(ecu);
                    case ECUProtocolType.SCI:
                        return await ConfigureMC9S12XEP100SCIAsync(ecu);
                    case ECUProtocolType.IIC:
                        return await ConfigureMC9S12XEP100IICAsync(ecu);
                    default:
                        _logger?.LogWarning($"No specific MC9S12XEP100 configuration for protocol {ecu.ProtocolType}", "ECUCommunicationService");
                        return true; // Return true to continue with default configuration
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error configuring MC9S12XEP100 specific settings for ECU {ecu?.Name}", "ECUCommunicationService", ex);
                return false;
            }
        }

        /// <summary>
        /// Configures MC9S12XEP100 specific CAN settings
        /// </summary>
        /// <param name="ecu">The ECU to configure</param>
        /// <returns>True if configuration is successful, false otherwise</returns>
        private async Task<bool> ConfigureMC9S12XEP100CANAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Configuring MC9S12XEP100 CAN settings for ECU {ecu.Name}", "ECUCommunicationService");

                // Set CAN specific properties based on MC9S12XEP100Configuration
                var canConfig = MC9S12XEP100Configuration.GetDefaultProtocolConfiguration(ECUProtocolType.CAN);

                ecu.Properties["CANBaudRate"] = MC9S12XEP100Configuration.CAN.BaudRates.HIGH_SPEED;
                ecu.Properties["CANIdentifier"] = $"0x{new Random().Next(0x100, 0x7FF):X3}"; // Random CAN ID
                ecu.Properties["CANExtendedIdentifierSupport"] = true;
                ecu.Properties["CANFilterMode"] = "Dual";
                ecu.Properties["CANMaxMessageBuffers"] = 16;

                // Get the CAN protocol handler
                if (!_protocolHandlers.TryGetValue(ECUProtocolType.CAN, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError("CAN protocol handler not found", "ECUCommunicationService");
                    return false;
                }

                // Configure the protocol handler with MC9S12XEP100 specific settings
                await Task.Delay(100); // Simulate configuration delay

                _logger?.LogInformation($"MC9S12XEP100 CAN settings configured for ECU {ecu.Name}", "ECUCommunicationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error configuring MC9S12XEP100 CAN settings for ECU {ecu.Name}", "ECUCommunicationService", ex);
                return false;
            }
        }

        /// <summary>
        /// Configures MC9S12XEP100 specific SPI settings
        /// </summary>
        /// <param name="ecu">The ECU to configure</param>
        /// <returns>True if configuration is successful, false otherwise</returns>
        private async Task<bool> ConfigureMC9S12XEP100SPIAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Configuring MC9S12XEP100 SPI settings for ECU {ecu.Name}", "ECUCommunicationService");

                // Set SPI specific properties based on MC9S12XEP100Configuration
                var spiConfig = MC9S12XEP100Configuration.GetDefaultProtocolConfiguration(ECUProtocolType.SPI);

                ecu.Properties["SPIBaudRate"] = MC9S12XEP100Configuration.SPI.ClockRates.LOW_SPEED;
                ecu.Properties["SPIMode"] = "Master";
                ecu.Properties["SPIClockPolarity"] = "High";
                ecu.Properties["SPIClockPhase"] = "Second Edge";
                ecu.Properties["SPIBitOrder"] = "MSB First";

                // Get the SPI protocol handler
                if (!_protocolHandlers.TryGetValue(ECUProtocolType.SPI, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError("SPI protocol handler not found", "ECUCommunicationService");
                    return false;
                }

                // Configure the protocol handler with MC9S12XEP100 specific settings
                await Task.Delay(100); // Simulate configuration delay

                _logger?.LogInformation($"MC9S12XEP100 SPI settings configured for ECU {ecu.Name}", "ECUCommunicationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error configuring MC9S12XEP100 SPI settings for ECU {ecu.Name}", "ECUCommunicationService", ex);
                return false;
            }
        }

        /// <summary>
        /// Configures MC9S12XEP100 specific SCI settings
        /// </summary>
        /// <param name="ecu">The ECU to configure</param>
        /// <returns>True if configuration is successful, false otherwise</returns>
        private async Task<bool> ConfigureMC9S12XEP100SCIAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Configuring MC9S12XEP100 SCI settings for ECU {ecu.Name}", "ECUCommunicationService");

                // Set SCI specific properties based on MC9S12XEP100Configuration
                var sciConfig = MC9S12XEP100Configuration.GetDefaultProtocolConfiguration(ECUProtocolType.SCI);

                ecu.Properties["SCIBaudRate"] = MC9S12XEP100Configuration.SCI.BaudRates.HIGH_SPEED;
                ecu.Properties["SCIDataBits"] = sciConfig["DataBits"];
                ecu.Properties["SCIStopBits"] = sciConfig["StopBits"];
                ecu.Properties["SCIParity"] = sciConfig["Parity"];
                ecu.Properties["SCIFlowControl"] = "None";

                // Get the SCI protocol handler
                if (!_protocolHandlers.TryGetValue(ECUProtocolType.SCI, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError("SCI protocol handler not found", "ECUCommunicationService");
                    return false;
                }

                // Configure the protocol handler with MC9S12XEP100 specific settings
                await Task.Delay(100); // Simulate configuration delay

                _logger?.LogInformation($"MC9S12XEP100 SCI settings configured for ECU {ecu.Name}", "ECUCommunicationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error configuring MC9S12XEP100 SCI settings for ECU {ecu.Name}", "ECUCommunicationService", ex);
                return false;
            }
        }

        /// <summary>
        /// Configures MC9S12XEP100 specific IIC settings
        /// </summary>
        /// <param name="ecu">The ECU to configure</param>
        /// <returns>True if configuration is successful, false otherwise</returns>
        private async Task<bool> ConfigureMC9S12XEP100IICAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Configuring MC9S12XEP100 IIC settings for ECU {ecu.Name}", "ECUCommunicationService");

                // Set IIC specific properties based on MC9S12XEP100Configuration
                var iicConfig = MC9S12XEP100Configuration.GetDefaultProtocolConfiguration(ECUProtocolType.IIC);

                ecu.Properties["IICBaudRate"] = MC9S12XEP100Configuration.IIC.BusSpeeds.FAST_MODE;
                ecu.Properties["IICAddress"] = $"0x{new Random().Next(0x08, 0x77):X2}"; // Random IIC address
                ecu.Properties["IICAddressingMode"] = "7-bit";
                ecu.Properties["IICGeneralCallSupport"] = true;

                // Get the IIC protocol handler
                if (!_protocolHandlers.TryGetValue(ECUProtocolType.IIC, out IECUProtocolHandler protocolHandler))
                {
                    _logger?.LogError("IIC protocol handler not found", "ECUCommunicationService");
                    return false;
                }

                // Configure the protocol handler with MC9S12XEP100 specific settings
                await Task.Delay(100); // Simulate configuration delay

                _logger?.LogInformation($"MC9S12XEP100 IIC settings configured for ECU {ecu.Name}", "ECUCommunicationService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error configuring MC9S12XEP100 IIC settings for ECU {ecu.Name}", "ECUCommunicationService", ex);
                return false;
            }
        }



        #endregion

        #region Progress-Based Methods



        /// <summary>
        /// Reads EEPROM data from an ECU and returns a structured model
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>EEPROM data model, or null if read fails</returns>
        public async Task<EEPROMData?> ReadEEPROMDataAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading EEPROM data model from ECU {ecu?.Name}", "ECUCommunicationService");

                // Read the raw EEPROM data
                byte[]? rawData = await ReadEEPROMAsync(ecu);
                if (rawData == null)
                {
                    _logger?.LogError($"Failed to read EEPROM data from ECU {ecu?.Name}", "ECUCommunicationService");
                    return null;
                }

                // Create the EEPROM data model
                var eepromData = new EEPROMData
                {
                    ECUId = ecu.Id,
                    ECUName = ecu.Name,
                    Timestamp = DateTime.Now,
                    Data = rawData,
                    Version = ecu.SoftwareVersion
                };

                // Calculate the checksum
                eepromData.Checksum = eepromData.CalculateChecksum();

                // Add metadata
                eepromData.Metadata = $"EEPROM data read from {ecu.Name} ({ecu.MicrocontrollerType}) on {DateTime.Now}";

                _logger?.LogInformation($"Successfully created EEPROM data model for ECU {ecu.Name}", "ECUCommunicationService");
                return eepromData;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error creating EEPROM data model for ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"EEPROM data model error: {ex.Message}");
                return null;
            }
        }



        /// <summary>
        /// Writes EEPROM data to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="eepromData">The EEPROM data model to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public async Task<bool> WriteEEPROMDataAsync(ECUDevice ecu, EEPROMData eepromData)
        {
            try
            {
                _logger?.LogInformation($"Writing EEPROM data model to ECU {ecu?.Name}", "ECUCommunicationService");

                if (eepromData == null || eepromData.Data == null || eepromData.Data.Length == 0)
                {
                    _logger?.LogError("EEPROM data model is null or contains no data", "ECUCommunicationService");
                    ECUError?.Invoke(this, "EEPROM data model is null or contains no data");
                    return false;
                }

                // Validate the EEPROM data
                if (!string.IsNullOrEmpty(eepromData.Checksum) && !eepromData.Validate())
                {
                    _logger?.LogError("EEPROM data validation failed, checksum mismatch", "ECUCommunicationService");
                    ECUError?.Invoke(this, "EEPROM data validation failed, checksum mismatch");
                    return false;
                }

                // Write the EEPROM data
                bool success = await WriteEEPROMAsync(ecu, eepromData.Data);
                if (success)
                {
                    _logger?.LogInformation($"Successfully wrote EEPROM data model to ECU {ecu.Name}", "ECUCommunicationService");
                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to write EEPROM data model to ECU {ecu.Name}", "ECUCommunicationService");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing EEPROM data model to ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"EEPROM data model write error: {ex.Message}");
                return false;
            }
        }



        /// <summary>
        /// Reads microcontroller code from an ECU and returns a structured model
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Microcontroller code model, or null if read fails</returns>
        public async Task<MicrocontrollerCode?> ReadMicrocontrollerCodeDataAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading microcontroller code model from ECU {ecu?.Name}", "ECUCommunicationService");

                // Read the raw microcontroller code
                byte[]? rawCode = await ReadMicrocontrollerCodeAsync(ecu);
                if (rawCode == null)
                {
                    _logger?.LogError($"Failed to read microcontroller code from ECU {ecu?.Name}", "ECUCommunicationService");
                    return null;
                }

                // Create the microcontroller code model
                var mcuCode = new MicrocontrollerCode
                {
                    ECUId = ecu.Id,
                    ECUName = ecu.Name,
                    Timestamp = DateTime.Now,
                    Code = rawCode,
                    Version = ecu.SoftwareVersion,
                    MicrocontrollerType = ecu.MicrocontrollerType
                };

                // Calculate the checksum
                mcuCode.Checksum = mcuCode.CalculateChecksum();

                // Add metadata
                mcuCode.Metadata = $"Microcontroller code read from {ecu.Name} ({ecu.MicrocontrollerType}) on {DateTime.Now}";

                // Create memory segments based on the microcontroller type
                if (ecu.MicrocontrollerType == "MC9S12XEP100")
                {
                    // Create memory segments for MC9S12XEP100
                    CreateMC9S12XEP100MemorySegments(mcuCode, rawCode);
                }
                else
                {
                    // Create a default memory segment
                    mcuCode.MemorySegments.Add(new MemorySegment
                    {
                        StartAddress = 0,
                        EndAddress = (uint)(rawCode.Length - 1),
                        Data = rawCode,
                        SegmentType = "Flash",
                        IsReadOnly = true
                    });
                }

                _logger?.LogInformation($"Successfully created microcontroller code model for ECU {ecu.Name}", "ECUCommunicationService");
                return mcuCode;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error creating microcontroller code model for ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Microcontroller code model error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Creates memory segments for MC9S12XEP100 microcontroller
        /// </summary>
        /// <param name="mcuCode">The microcontroller code model</param>
        /// <param name="rawCode">The raw code data</param>
        private void CreateMC9S12XEP100MemorySegments(MicrocontrollerCode mcuCode, byte[] rawCode)
        {
            // MC9S12XEP100 memory map based on datasheet
            // Flash memory (0x4000 - 0xFFFF)
            int flashSize = Math.Min(FLASH_SIZE, rawCode.Length);
            byte[] flashData = new byte[flashSize];
            Array.Copy(rawCode, 0, flashData, 0, flashSize);

            mcuCode.MemorySegments.Add(new MemorySegment
            {
                StartAddress = 0x4000,
                EndAddress = 0x4000 + (uint)flashSize - 1,
                Data = flashData,
                SegmentType = "Flash",
                IsReadOnly = true
            });

            // EEPROM (0x0800 - 0x0FFF)
            if (rawCode.Length > FLASH_SIZE)
            {
                int eepromSize = Math.Min(EEPROM_SIZE, rawCode.Length - FLASH_SIZE);
                byte[] eepromData = new byte[eepromSize];
                Array.Copy(rawCode, FLASH_SIZE, eepromData, 0, eepromSize);

                mcuCode.MemorySegments.Add(new MemorySegment
                {
                    StartAddress = 0x0800,
                    EndAddress = 0x0800 + (uint)eepromSize - 1,
                    Data = eepromData,
                    SegmentType = "EEPROM",
                    IsReadOnly = false
                });
            }

            // RAM (0x1000 - 0x3FFF)
            if (rawCode.Length > FLASH_SIZE + EEPROM_SIZE)
            {
                int ramSize = Math.Min(RAM_SIZE, rawCode.Length - FLASH_SIZE - EEPROM_SIZE);
                byte[] ramData = new byte[ramSize];
                Array.Copy(rawCode, FLASH_SIZE + EEPROM_SIZE, ramData, 0, ramSize);

                mcuCode.MemorySegments.Add(new MemorySegment
                {
                    StartAddress = 0x1000,
                    EndAddress = 0x1000 + (uint)ramSize - 1,
                    Data = ramData,
                    SegmentType = "RAM",
                    IsReadOnly = false
                });
            }
        }



        /// <summary>
        /// Writes microcontroller code to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="mcuCode">The microcontroller code model to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public async Task<bool> WriteMicrocontrollerCodeDataAsync(ECUDevice ecu, MicrocontrollerCode mcuCode)
        {
            try
            {
                _logger?.LogInformation($"Writing microcontroller code model to ECU {ecu?.Name}", "ECUCommunicationService");

                if (mcuCode == null || mcuCode.Code == null || mcuCode.Code.Length == 0)
                {
                    _logger?.LogError("Microcontroller code model is null or contains no code", "ECUCommunicationService");
                    ECUError?.Invoke(this, "Microcontroller code model is null or contains no code");
                    return false;
                }

                // Validate the microcontroller code
                if (!string.IsNullOrEmpty(mcuCode.Checksum) && !mcuCode.Validate())
                {
                    _logger?.LogError("Microcontroller code validation failed, checksum mismatch", "ECUCommunicationService");
                    ECUError?.Invoke(this, "Microcontroller code validation failed, checksum mismatch");
                    return false;
                }

                // Write the microcontroller code
                bool success = await WriteMicrocontrollerCodeAsync(ecu, mcuCode.Code);
                if (success)
                {
                    _logger?.LogInformation($"Successfully wrote microcontroller code model to ECU {ecu.Name}", "ECUCommunicationService");
                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to write microcontroller code model to ECU {ecu.Name}", "ECUCommunicationService");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing microcontroller code model to ECU {ecu?.Name}", "ECUCommunicationService", ex);
                ECUError?.Invoke(this, $"Microcontroller code model write error: {ex.Message}");
                return false;
            }
        }











        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the ECU communication service
        /// </summary>
        public void Dispose()
        {
            try
            {
                // Unsubscribe from Vocom service events
                if (_vocomService != null)
                {
                    _vocomService.VocomConnected -= (sender, device) => OnVocomConnected(sender, device);
                    _vocomService.VocomDisconnected -= (sender, device) => OnVocomDisconnected(sender, device);
                    _vocomService.VocomError -= (sender, message) => OnVocomError(sender, message);
                }

                // Disconnect all ECUs
                if (_isInitialized)
                {
                    DisconnectAllECUsAsync().Wait();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error during disposal of ECU communication service", "ECUCommunicationService", ex);
            }
        }

        #endregion
    }
}
