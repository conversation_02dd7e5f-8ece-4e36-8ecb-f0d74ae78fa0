﻿Chapter 9
Security (S12XE9SECV2)

Table 9-1. Revision History

Revision Sections
Revision Date Description of Changes

Number Affected
V02.00 27 Aug 2004 - Reviewed and updated for S12XD architecture
V02.01 21 Feb 2007 - Added S12XE, S12XF and S12XS architectures
V02.02 19 Apr 2007 - Corrected statement about Backdoor key access via BDM on XE, XF, XS

9.1 Introduction
This specification describes the function of the security mechanism in the S12XE chip family (9SEC).

NOTE
No security feature is absolutely secure. However, Freescale’s strategy is to
make reading or copying the FLASH and/or EEPROM difficult for
unauthorized users.

9.1.1 Features
The user must be reminded that part of the security must lie with the application code. An extreme example
would be application code that dumps the contents of the internal memory. This would defeat the purpose
of security. At the same time, the user may also wish to put a backdoor in the application program. An
example of this is the user downloads a security key through the SCI, which allows access to a
programming routine that updates parameters stored in another section of the Flash memory.

The security features of the S12XE chip family (in secure mode) are:
• Protect the content of non-volatile memories (Flash, EEPROM)
• Execution of NVM commands is restricted
• Disable access to internal memory via background debug module (BDM)
• Disable access to internal Flash/EEPROM in expanded modes
• Disable debugging features for the CPU and XGATE

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 347



Chapter 9 Security (S12XE9SECV2)

9.1.2 Modes of Operation
Table 9-2 gives an overview over availability of security relevant features in unsecure and secure modes.

Table 9-2. Feature Availability in Unsecure and Secure Modes on S12XE

Unsecure Mode Secure Mode

NS SS NX ES EX ST NS SS NX ES EX ST

Flash Array Access ✔ ✔ ✔(1) ✔1 ✔1 ✔1 ✔ ✔ — — — —

EEPROM Array Access ✔ ✔ ✔ ✔ ✔ ✔ ✔ ✔ — — — —

NVM Commands ✔(2) ✔ ✔2 ✔2 ✔2 ✔ ✔2 ✔2 ✔2 ✔2 ✔2 ✔2

BDM ✔ ✔ ✔ ✔ ✔ ✔ — ✔(3) — — — —

DBG Module Trace ✔ ✔ ✔ ✔ ✔ ✔ — — — — — —

XGATE Debugging ✔ ✔ ✔ ✔ ✔ ✔ — — — — — —

External Bus Interface — — ✔ ✔ ✔ ✔ — — ✔ ✔ ✔ ✔

Internal status visible — — — ✔ ✔ — — — — ✔ ✔ —
multiplexed on
external bus

Internal accesses visible — — — — — ✔ — — — — — ✔
on external bus

1. Availability of Flash arrays in the memory map depends on ROMCTL/EROMCTL pins and/or the state of the
ROMON/EROMON bits in the MMCCTL1 register. Please refer to the S12X_MMC block guide for detailed
information.

2. Restricted NVM command set only. Please refer to the NVM wrapper block guides for detailed information.
3. BDM hardware commands restricted to peripheral registers only.

9.1.3 Securing the Microcontroller
Once the user has programmed the Flash and EEPROM, the chip can be secured by programming the
security bits located in the options/security byte in the Flash memory array. These non-volatile bits will
keep the device secured through reset and power-down.

The options/security byte is located at address 0xFF0F (= global address 0x7F_FF0F) in the Flash memory
array. This byte can be erased and programmed like any other Flash location. Two bits of this byte are used
for security (SEC[1:0]). On devices which have a memory page window, the Flash options/security byte
is also available at address 0xBF0F by selecting page 0x3F with the PPAGE register. The contents of this
byte are copied into the Flash security register (FSEC) during a reset sequence.

7 6 5 4 3 2 1 0

0xFF0F KEYEN1 KEYEN0 NV5 NV4 NV3 NV2 SEC1 SEC0

Figure 9-1. Flash Options/Security Byte

The meaning of the bits KEYEN[1:0] is shown in Table 9-3. Please refer to Section *******, “Unsecuring
the MCU Using the Backdoor Key Access” for more information.

MC9S12XE-Family Reference Manual  Rev. 1.25

348 Freescale Semiconductor



Chapter 9 Security (S12XE9SECV2)

Table 9-3. Backdoor Key Access Enable Bits

Backdoor Key
KEYEN[1:0]

Access Enabled

00 0 (disabled)
01 0 (disabled)
10 1 (enabled)
11 0 (disabled)

The meaning of the security bits SEC[1:0] is shown in Table 9-4. For security reasons, the state of device
security is controlled by two bits. To put the device in unsecured mode, these bits must be programmed to
SEC[1:0] = ‘10’. All other combinations put the device in a secured mode. The recommended value to put
the device in secured state is the inverse of the unsecured state, i.e. SEC[1:0] = ‘01’.

Table 9-4. Security Bits

SEC[1:0] Security State

00 1 (secured)
01 1 (secured)
10 0 (unsecured)
11 1 (secured)

NOTE
Please refer to the Flash block guide for actual security configuration (in
section “Flash Module Security”).

9.1.4 Operation of the Secured Microcontroller
By securing the device, unauthorized access to the EEPROM and Flash memory contents can be prevented.
However, it must be understood that the security of the EEPROM and Flash memory contents also depends
on the design of the application program. For example, if the application has the capability of downloading
code through a serial port and then executing that code (e.g. an application containing bootloader code),
then this capability could potentially be used to read the EEPROM and Flash memory contents even when
the microcontroller is in the secure state. In this example, the security of the application could be enhanced
by requiring a challenge/response authentication before any code can be downloaded.

Secured operation has the following effects on the microcontroller:

******* Normal Single Chip Mode (NS)
• Background debug module (BDM) operation is completely disabled.
• Execution of Flash and EEPROM commands is restricted. Please refer to the NVM block guide for

details.
• Tracing code execution using the DBG module is disabled.
• Debugging XGATE code (breakpoints, single-stepping) is disabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 349



Chapter 9 Security (S12XE9SECV2)

******* Special Single Chip Mode (SS)
• BDM firmware commands are disabled.
• BDM hardware commands are restricted to the register space.
• Execution of Flash and EEPROM commands is restricted. Please refer to the NVM block guide for

details.
• Tracing code execution using the DBG module is disabled.
• Debugging XGATE code (breakpoints, single-stepping) is disabled.

Special single chip mode means BDM is active after reset. The availability of BDM firmware commands
depends on the security state of the device. The BDM secure firmware first performs a blank check of both
the Flash memory and the EEPROM. If the blank check succeeds, security will be temporarily turned off
and the state of the security bits in the appropriate Flash memory location can be changed If the blank
check fails, security will remain active, only the BDM hardware commands will be enabled, and the
accessible memory space is restricted to the peripheral register area. This will allow the BDM to be used
to erase the EEPROM and Flash memory without giving access to their contents. After erasing both Flash
memory and EEPROM, another reset into special single chip mode will cause the blank check to succeed
and the options/security byte can be programmed to “unsecured” state via BDM.

While the BDM is executing the blank check, the BDM interface is completely blocked, which means that
all BDM commands are temporarily blocked.

******* Expanded Modes (NX, ES, EX, and ST)
• BDM operation is completely disabled.
• Internal Flash memory and EEPROM are disabled.
• Execution of Flash and EEPROM commands is restricted. Please refer to the FTM block guide for

details.
• Tracing code execution using the DBG module is disabled.
• Debugging XGATE code (breakpoints, single-stepping) is disabled

9.1.5 Unsecuring the Microcontroller
Unsecuring the microcontroller can be done by three different methods:

1. Backdoor key access
2. Reprogramming the security bits
3. Complete memory erase (special modes)

******* Unsecuring the MCU Using the Backdoor Key Access
In normal modes (single chip and expanded), security can be temporarily disabled using the backdoor key
access method. This method requires that:

• The backdoor key at 0xFF00–0xFF07 (= global addresses 0x7F_FF00–0x7F_FF07) has been
programmed to a valid value.

MC9S12XE-Family Reference Manual  Rev. 1.25

350 Freescale Semiconductor



Chapter 9 Security (S12XE9SECV2)

• The KEYEN[1:0] bits within the Flash options/security byte select ‘enabled’.
• In single chip mode, the application program programmed into the microcontroller must be

designed to have the capability to write to the backdoor key locations.

The backdoor key values themselves would not normally be stored within the application data, which
means the application program would have to be designed to receive the backdoor key values from an
external source (e.g. through a serial port).

The backdoor key access method allows debugging of a secured microcontroller without having to erase
the Flash. This is particularly useful for failure analysis.

NOTE
No word of the backdoor key is allowed to have the value 0x0000 or
0xFFFF.

9.1.6 Reprogramming the Security Bits
In normal single chip mode (NS), security can also be disabled by erasing and reprogramming the security
bits within Flash options/security byte to the unsecured value. Because the erase operation will erase the
entire sector from 0xFE00–0xFFFF (0x7F_FE00–0x7F_FFFF), the backdoor key and the interrupt vectors
will also be erased; this method is not recommended for normal single chip mode. The application
software can only erase and program the Flash options/security byte if the Flash sector containing the Flash
options/security byte is not protected (see Flash protection). Thus Flash protection is a useful means of
preventing this method. The microcontroller will enter the unsecured state after the next reset following
the programming of the security bits to the unsecured value.

This method requires that:
• The application software previously programmed into the microcontroller has been designed to

have the capability to erase and program the Flash options/security byte, or security is first disabled
using the backdoor key method, allowing BDM to be used to issue commands to erase and program
the Flash options/security byte.

• The Flash sector containing the Flash options/security byte is not protected.

9.1.7 Complete Memory Erase (Special Modes)
The microcontroller can be unsecured in special modes by erasing the entire EEPROM and Flash memory
contents.

When a secure microcontroller is reset into special single chip mode (SS), the BDM firmware verifies
whether the EEPROM and Flash memory are erased. If any EEPROM or Flash memory address is not
erased, only BDM hardware commands are enabled. BDM hardware commands can then be used to write
to the EEPROM and Flash registers to mass erase the EEPROM and all Flash memory blocks.

When next reset into special single chip mode, the BDM firmware will again verify whether all EEPROM
and Flash memory are erased, and this being the case, will enable all BDM commands, allowing the Flash
options/security byte to be programmed to the unsecured value. The security bits SEC[1:0] in the Flash
security register will indicate the unsecure state following the next reset.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 351



Chapter 9 Security (S12XE9SECV2)

MC9S12XE-Family Reference Manual  Rev. 1.25

352 Freescale Semiconductor