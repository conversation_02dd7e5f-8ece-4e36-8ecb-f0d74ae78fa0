using System.Collections.Generic;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Options for merging backup versions
    /// </summary>
    public class BackupMergeOptions
    {
        /// <summary>
        /// Whether to merge EEPROM data
        /// </summary>
        public bool MergeEEPROM { get; set; } = true;

        /// <summary>
        /// Whether to merge microcontroller code
        /// </summary>
        public bool MergeMicrocontrollerCode { get; set; } = true;

        /// <summary>
        /// Whether to merge parameters
        /// </summary>
        public bool MergeParameters { get; set; } = true;

        /// <summary>
        /// Whether to merge tags
        /// </summary>
        public bool MergeTags { get; set; } = true;

        /// <summary>
        /// Whether to create a new version for the merged result
        /// </summary>
        public bool CreateNewVersion { get; set; } = true;

        /// <summary>
        /// Description for the merged version
        /// </summary>
        public string MergeDescription { get; set; } = "Merged version";

        /// <summary>
        /// Notes for the merged version
        /// </summary>
        public string MergeNotes { get; set; } = "Created by merging versions";

        /// <summary>
        /// Specific parameters to merge (if empty, all parameters are merged)
        /// </summary>
        public List<string> ParametersToMerge { get; set; } = new List<string>();

        /// <summary>
        /// Conflict resolution strategy
        /// </summary>
        public MergeConflictResolution ConflictResolution { get; set; } = MergeConflictResolution.PreferSource;
    }

    /// <summary>
    /// Strategy for resolving conflicts during merge
    /// </summary>
    public enum MergeConflictResolution
    {
        /// <summary>
        /// Prefer the source version in case of conflict
        /// </summary>
        PreferSource,

        /// <summary>
        /// Prefer the target version in case of conflict
        /// </summary>
        PreferTarget,

        /// <summary>
        /// Prompt the user to resolve conflicts
        /// </summary>
        PromptUser
    }
}
