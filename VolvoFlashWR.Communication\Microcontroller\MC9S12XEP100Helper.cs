using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Diagnostics;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Services;
using System.Linq;
using System.IO;
using System.Security.Cryptography;

namespace VolvoFlashWR.Communication.Microcontroller
{
    /// <summary>
    /// Helper class for working with the MC9S12XEP100 microcontroller
    /// </summary>
    public class MC9S12XEP100Helper
    {
        private readonly ILoggingService? _logger;
        private readonly IRegisterAccess? _registerAccess;
        private readonly FlashOperationRetry _flashRetry;
        private FlashHealthStatus _flashHealthStatus = new FlashHealthStatus();
        private readonly FlashOperationMonitor? _operationMonitor;
        private readonly MC9S12XEP100Security _security;

        /// <summary>
        /// Initializes a new instance of the MC9S12XEP100Helper class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="registerAccess">The register access interface</param>
        /// <param name="operationMonitor">The flash operation monitor for diagnostics</param>
        /// <param name="maxRetries">The maximum number of retries for flash operations</param>
        /// <param name="retryDelayMs">The delay between retries in milliseconds</param>
        public MC9S12XEP100Helper(
            ILoggingService? logger = null,
            IRegisterAccess? registerAccess = null,
            FlashOperationMonitor? operationMonitor = null,
            int maxRetries = 3,
            int retryDelayMs = 100)
        {
            _logger = logger;
            _registerAccess = registerAccess;
            _operationMonitor = operationMonitor;
            _flashRetry = new FlashOperationRetry(logger ?? new NullLoggingService(), maxRetries, retryDelayMs);
            _flashHealthStatus = new FlashHealthStatus();
            _security = new MC9S12XEP100Security(logger, registerAccess);

            // Try to load flash health status from file if it exists
            LoadFlashHealthStatus();

            // Start monitoring if a monitor is provided
            _operationMonitor?.StartMonitoring();
        }

        /// <summary>
        /// Calculates the CRC-16 checksum for the specified data
        /// </summary>
        /// <param name="data">The data to calculate the checksum for</param>
        /// <returns>The CRC-16 checksum</returns>
        public ushort CalculateCRC16(byte[] data)
        {
            if (data == null || data.Length == 0)
            {
                return 0;
            }

            const ushort polynomial = 0x1021; // CRC-16-CCITT polynomial
            ushort crc = 0xFFFF; // Initial value

            foreach (byte b in data)
            {
                crc ^= (ushort)(b << 8);
                for (int i = 0; i < 8; i++)
                {
                    if ((crc & 0x8000) != 0)
                    {
                        crc = (ushort)((crc << 1) ^ polynomial);
                    }
                    else
                    {
                        crc <<= 1;
                    }
                }
            }

            return crc;
        }

        /// <summary>
        /// Calculates the ECC (Error Correction Code) for the specified data
        /// </summary>
        /// <param name="data">The data to calculate the ECC for (must be 8 bytes)</param>
        /// <returns>The ECC byte</returns>
        public byte CalculateECC(byte[] data)
        {
            if (data == null || data.Length != 8)
            {
                throw new ArgumentException("Data must be exactly 8 bytes for ECC calculation", nameof(data));
            }

            // MC9S12XEP100 uses a Hamming code for ECC
            // This implementation follows the algorithm described in the MC9S12XEP100 reference manual
            byte ecc = 0;

            // Calculate parity bits
            for (int i = 0; i < 8; i++)
            {
                byte b = data[i];
                for (int j = 0; j < 8; j++)
                {
                    if ((b & (1 << j)) != 0)
                    {
                        ecc ^= (byte)(1 << (i ^ j));
                    }
                }
            }

            return ecc;
        }

        /// <summary>
        /// ECC error status
        /// </summary>
        public enum ECCErrorStatus
        {
            /// <summary>
            /// No error detected
            /// </summary>
            NoError,

            /// <summary>
            /// Single-bit error detected and corrected
            /// </summary>
            SingleBitError,

            /// <summary>
            /// Multi-bit error detected (cannot be corrected)
            /// </summary>
            MultiBitError
        }

        /// <summary>
        /// Detailed ECC error information
        /// </summary>
        public class ECCErrorInfo
        {
            /// <summary>
            /// Gets or sets the error status
            /// </summary>
            public ECCErrorStatus Status { get; set; }

            /// <summary>
            /// Gets or sets the byte position of the error (for single-bit errors)
            /// </summary>
            public int BytePosition { get; set; } = -1;

            /// <summary>
            /// Gets or sets the bit position of the error (for single-bit errors)
            /// </summary>
            public int BitPosition { get; set; } = -1;

            /// <summary>
            /// Gets or sets the syndrome value
            /// </summary>
            public byte Syndrome { get; set; }

            /// <summary>
            /// Gets or sets the calculated ECC
            /// </summary>
            public byte CalculatedECC { get; set; }

            /// <summary>
            /// Gets or sets the stored ECC
            /// </summary>
            public byte StoredECC { get; set; }

            /// <summary>
            /// Gets or sets the number of bits set in the syndrome
            /// </summary>
            public int SyndromeBitCount { get; set; }

            /// <summary>
            /// Gets a description of the error
            /// </summary>
            public string Description
            {
                get
                {
                    switch (Status)
                    {
                        case ECCErrorStatus.NoError:
                            return "No ECC errors detected";
                        case ECCErrorStatus.SingleBitError:
                            return $"Single-bit error detected and corrected at byte {BytePosition}, bit {BitPosition}";
                        case ECCErrorStatus.MultiBitError:
                            return $"Multi-bit error detected (cannot be corrected), syndrome: 0x{Syndrome:X2}";
                        default:
                            return "Unknown ECC error status";
                    }
                }
            }
        }

        /// <summary>
        /// Counts the number of bits set in a byte
        /// </summary>
        /// <param name="value">The byte to count bits in</param>
        /// <returns>The number of bits set</returns>
        private int CountSetBits(byte value)
        {
            int count = 0;
            while (value != 0)
            {
                count += (value & 1);
                value >>= 1;
            }
            return count;
        }

        /// <summary>
        /// Aligns the specified address to a phrase boundary
        /// </summary>
        /// <param name="address">The address to align</param>
        /// <returns>The aligned address</returns>
        public uint AlignToPhraseAddress(uint address)
        {
            // Phrase size is 8 bytes, so align to 8-byte boundary
            return address & ~(uint)(MC9S12XEP100Configuration.PHRASE_SIZE - 1);
        }

        /// <summary>
        /// Aligns the specified address to a sector boundary
        /// </summary>
        /// <param name="address">The address to align</param>
        /// <returns>The aligned address</returns>
        public uint AlignToSectorAddress(uint address)
        {
            // Sector size is 1024 bytes, so align to 1024-byte boundary
            return address & ~(uint)(MC9S12XEP100Configuration.SECTOR_SIZE - 1);
        }

        /// <summary>
        /// Loads the flash health status from a file
        /// </summary>
        private void LoadFlashHealthStatus()
        {
            try
            {
                string filePath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                    "VolvoFlashWR",
                    "FlashHealthStatus.json");

                if (File.Exists(filePath))
                {
                    string json = File.ReadAllText(filePath);
                    _flashHealthStatus = System.Text.Json.JsonSerializer.Deserialize<FlashHealthStatus>(json) ?? new FlashHealthStatus();
                    _logger?.LogInformation($"Loaded flash health status from {filePath}", "MC9S12XEP100Helper");
                }
                else
                {
                    _flashHealthStatus = new FlashHealthStatus();
                    _logger?.LogInformation("No flash health status file found, using default values", "MC9S12XEP100Helper");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error loading flash health status: {ex.Message}", "MC9S12XEP100Helper");
                _flashHealthStatus = new FlashHealthStatus();
            }
        }

        /// <summary>
        /// Waits for a flash operation to complete
        /// </summary>
        /// <param name="timeoutMs">The timeout in milliseconds</param>
        /// <returns>True if the operation completed, false if it timed out</returns>
        private async Task<bool> WaitForFlashOperationCompleteAsync(int timeoutMs)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return false;
            }

            int elapsedMs = 0;
            int pollIntervalMs = 10;
            bool operationComplete = false;

            while (elapsedMs < timeoutMs)
            {
                // Read the flash status register
                byte status = await _registerAccess.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_STAT);

                // Check if the Command Complete Interrupt Flag (CCIF) is set
                if ((status & 0x80) != 0)
                {
                    operationComplete = true;
                    break;
                }

                // Wait for a short time before polling again
                await Task.Delay(pollIntervalMs);
                elapsedMs += pollIntervalMs;
            }

            if (!operationComplete)
            {
                _logger?.LogWarning($"Flash operation timed out after {timeoutMs} ms", "MC9S12XEP100Helper");
            }

            return operationComplete;
        }

        /// <summary>
        /// Configures an ECU device with MC9S12XEP100-specific settings
        /// </summary>
        /// <param name="ecu">The ECU device to configure</param>
        public void ConfigureECUDevice(ECUDevice ecu)
        {
            if (ecu == null)
            {
                throw new ArgumentNullException(nameof(ecu));
            }

            // Set MC9S12XEP100-specific properties
            ecu.MicrocontrollerType = MC9S12XEP100Configuration.DeviceInfo.DEVICE_NAME;
            ecu.EEPROMSize = MC9S12XEP100Configuration.EEPROM_SIZE;
            ecu.FlashSize = MC9S12XEP100Configuration.FLASH_SIZE;
            ecu.RAMSize = MC9S12XEP100Configuration.RAM_SIZE;

            // Add MC9S12XEP100-specific properties
            if (!ecu.Properties.ContainsKey("SectorSize"))
            {
                ecu.Properties.Add("SectorSize", MC9S12XEP100Configuration.SECTOR_SIZE);
            }

            if (!ecu.Properties.ContainsKey("PhraseSize"))
            {
                ecu.Properties.Add("PhraseSize", MC9S12XEP100Configuration.PHRASE_SIZE);
            }

            if (!ecu.Properties.ContainsKey("DFlashSize"))
            {
                ecu.Properties.Add("DFlashSize", MC9S12XEP100Configuration.D_FLASH_SIZE);
            }

            if (!ecu.Properties.ContainsKey("BufferRAMSize"))
            {
                ecu.Properties.Add("BufferRAMSize", MC9S12XEP100Configuration.BUFFER_RAM_SIZE);
            }

            if (!ecu.Properties.ContainsKey("MaxCPUFrequency"))
            {
                ecu.Properties.Add("MaxCPUFrequency", MC9S12XEP100Configuration.MAX_CPU_FREQUENCY);
            }

            _logger?.LogInformation($"Configured ECU {ecu.Name} with MC9S12XEP100-specific settings", "MC9S12XEP100Helper");
        }

        /// <summary>
        /// Checks if the ECU is secured
        /// </summary>
        /// <returns>True if the ECU is secured, false otherwise</returns>
        public async Task<bool> IsSecuredAsync()
        {
            return await _security.IsSecuredAsync();
        }

        /// <summary>
        /// Performs security access to the ECU
        /// </summary>
        /// <param name="backdoorKey">Optional backdoor key</param>
        /// <returns>True if security access is successful, false otherwise</returns>
        public async Task<bool> PerformSecurityAccessAsync(byte[]? backdoorKey = null)
        {
            return await _security.PerformSecurityAccessAsync(backdoorKey);
        }

        /// <summary>
        /// Secures the ECU by setting the security bits
        /// </summary>
        /// <returns>True if the ECU is successfully secured, false otherwise</returns>
        public async Task<bool> SecureECUAsync()
        {
            return await _security.SecureECUAsync();
        }

        /// <summary>
        /// Generates a backdoor key for the ECU
        /// </summary>
        /// <param name="ecuId">The ECU ID</param>
        /// <param name="masterKey">The master key (16 bytes)</param>
        /// <returns>The generated backdoor key</returns>
        public byte[] GenerateBackdoorKey(string ecuId, byte[] masterKey)
        {
            return _security.GenerateBackdoorKey(ecuId, masterKey);
        }

        /// <summary>
        /// Enables EEPROM emulation
        /// </summary>
        /// <returns>True if EEPROM emulation is successfully enabled, false otherwise</returns>
        public async Task<bool> EnableEEPROMEmulationAsync()
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return false;
            }

            try
            {
                // Write to the EEPROM control register to enable EEPROM emulation (set EEON bit)
                bool writeResult = await _registerAccess.WriteRegisterByteAsync(
                    MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL, 0x01);

                if (!writeResult)
                {
                    _logger?.LogError("Failed to write to EEPROM control register", "MC9S12XEP100Helper");
                    return false;
                }

                // Verify that EEPROM emulation is enabled
                byte controlReg = await _registerAccess.ReadRegisterByteAsync(
                    MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL);

                bool isEnabled = (controlReg & 0x01) != 0;

                if (isEnabled)
                {
                    _logger?.LogInformation("EEPROM emulation enabled successfully", "MC9S12XEP100Helper");
                }
                else
                {
                    _logger?.LogWarning("EEPROM emulation not enabled after write operation", "MC9S12XEP100Helper");
                }

                return isEnabled;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error enabling EEPROM emulation: {ex.Message}", "MC9S12XEP100Helper");
                return false;
            }
        }

        /// <summary>
        /// Disables EEPROM emulation
        /// </summary>
        /// <returns>True if EEPROM emulation is successfully disabled, false otherwise</returns>
        public async Task<bool> DisableEEPROMEmulationAsync()
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return false;
            }

            try
            {
                // Write to the EEPROM control register to disable EEPROM emulation (clear EEON bit)
                bool writeResult = await _registerAccess.WriteRegisterByteAsync(
                    MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL, 0x00);

                if (!writeResult)
                {
                    _logger?.LogError("Failed to write to EEPROM control register", "MC9S12XEP100Helper");
                    return false;
                }

                // Verify that EEPROM emulation is disabled
                byte controlReg = await _registerAccess.ReadRegisterByteAsync(
                    MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL);

                bool isDisabled = (controlReg & 0x01) == 0;

                if (isDisabled)
                {
                    _logger?.LogInformation("EEPROM emulation disabled successfully", "MC9S12XEP100Helper");
                }
                else
                {
                    _logger?.LogWarning("EEPROM emulation not disabled after write operation", "MC9S12XEP100Helper");
                }

                return isDisabled;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error disabling EEPROM emulation: {ex.Message}", "MC9S12XEP100Helper");
                return false;
            }
        }

        /// <summary>
        /// Reads data from the emulated EEPROM
        /// </summary>
        /// <param name="address">The address to read from</param>
        /// <param name="size">The number of bytes to read</param>
        /// <returns>The read data</returns>
        public async Task<byte[]> ReadEmulatedEEPROMAsync(uint address, int size)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return Array.Empty<byte>();
            }

            if (size <= 0)
            {
                _logger?.LogError("Invalid size for EEPROM read", "MC9S12XEP100Helper");
                return Array.Empty<byte>();
            }

            try
            {
                // Check if EEPROM emulation is enabled
                byte controlReg = await _registerAccess.ReadRegisterByteAsync(
                    MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL);

                bool isEnabled = (controlReg & 0x01) != 0;
                if (!isEnabled)
                {
                    _logger?.LogWarning("EEPROM emulation is not enabled, enabling it now", "MC9S12XEP100Helper");
                    bool enableResult = await EnableEEPROMEmulationAsync();
                    if (!enableResult)
                    {
                        _logger?.LogError("Failed to enable EEPROM emulation", "MC9S12XEP100Helper");
                        return Array.Empty<byte>();
                    }
                }

                // Calculate the D-Flash address for the emulated EEPROM
                uint dFlashAddress = MC9S12XEP100Configuration.MemoryMap.D_FLASH_START + address;

                // Read the data from D-Flash
                byte[] data = new byte[size];
                for (int i = 0; i < size; i++)
                {
                    data[i] = await _registerAccess.ReadRegisterByteAsync(dFlashAddress + (uint)i);
                }

                _logger?.LogInformation($"Read {size} bytes from emulated EEPROM at address 0x{address:X8}", "MC9S12XEP100Helper");
                return data;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading from emulated EEPROM: {ex.Message}", "MC9S12XEP100Helper");
                return Array.Empty<byte>();
            }
        }

        /// <summary>
        /// Writes data to the emulated EEPROM
        /// </summary>
        /// <param name="address">The address to write to</param>
        /// <param name="data">The data to write</param>
        /// <returns>True if the write is successful, false otherwise</returns>
        public async Task<bool> WriteEmulatedEEPROMAsync(uint address, byte[] data)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return false;
            }

            if (data == null || data.Length == 0)
            {
                _logger?.LogError("Invalid data for EEPROM write", "MC9S12XEP100Helper");
                return false;
            }

            try
            {
                // Check if EEPROM emulation is enabled
                byte controlReg = await _registerAccess.ReadRegisterByteAsync(
                    MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL);

                bool isEnabled = (controlReg & 0x01) != 0;
                if (!isEnabled)
                {
                    _logger?.LogWarning("EEPROM emulation is not enabled, enabling it now", "MC9S12XEP100Helper");
                    bool enableResult = await EnableEEPROMEmulationAsync();
                    if (!enableResult)
                    {
                        _logger?.LogError("Failed to enable EEPROM emulation", "MC9S12XEP100Helper");
                        return false;
                    }
                }

                // Calculate the D-Flash address for the emulated EEPROM
                uint dFlashAddress = MC9S12XEP100Configuration.MemoryMap.D_FLASH_START + address;

                // Write the data to D-Flash
                for (int i = 0; i < data.Length; i++)
                {
                    bool writeResult = await _registerAccess.WriteRegisterByteAsync(dFlashAddress + (uint)i, data[i]);
                    if (!writeResult)
                    {
                        _logger?.LogError($"Failed to write byte at address 0x{dFlashAddress + i:X8}", "MC9S12XEP100Helper");
                        return false;
                    }

                    // Verify the write
                    byte readBack = await _registerAccess.ReadRegisterByteAsync(dFlashAddress + (uint)i);
                    if (readBack != data[i])
                    {
                        _logger?.LogError($"Verification failed at address 0x{dFlashAddress + i:X8}: wrote 0x{data[i]:X2}, read 0x{readBack:X2}", "MC9S12XEP100Helper");
                        return false;
                    }
                }

                _logger?.LogInformation($"Wrote {data.Length} bytes to emulated EEPROM at address 0x{address:X8}", "MC9S12XEP100Helper");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing to emulated EEPROM: {ex.Message}", "MC9S12XEP100Helper");
                return false;
            }
        }

        /// <summary>
        /// Erases a sector in the emulated EEPROM
        /// </summary>
        /// <param name="address">The address of the sector to erase</param>
        /// <returns>True if the erase is successful, false otherwise</returns>
        public async Task<bool> EraseEmulatedEEPROMSectorAsync(uint address)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return false;
            }

            try
            {
                // Check if EEPROM emulation is enabled
                byte controlReg = await _registerAccess.ReadRegisterByteAsync(
                    MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL);

                bool isEnabled = (controlReg & 0x01) != 0;
                if (!isEnabled)
                {
                    _logger?.LogWarning("EEPROM emulation is not enabled, enabling it now", "MC9S12XEP100Helper");
                    bool enableResult = await EnableEEPROMEmulationAsync();
                    if (!enableResult)
                    {
                        _logger?.LogError("Failed to enable EEPROM emulation", "MC9S12XEP100Helper");
                        return false;
                    }
                }

                // Calculate the D-Flash address for the emulated EEPROM
                uint dFlashAddress = MC9S12XEP100Configuration.MemoryMap.D_FLASH_START + address;

                // Align to sector boundary
                dFlashAddress = AlignToSectorAddress(dFlashAddress);

                // Set up the flash command registers for sector erase
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_CMD, 0x0A); // Erase sector command

                // Set the address registers
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_HIGH, (byte)(dFlashAddress >> 16));
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_MID, (byte)(dFlashAddress >> 8));
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_LOW, (byte)dFlashAddress);

                // Start the erase operation
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_STAT, 0x80);

                // Wait for the erase operation to complete
                bool eraseComplete = await WaitForFlashOperationCompleteAsync(5000); // 5 second timeout for sector erase
                if (!eraseComplete)
                {
                    _logger?.LogError($"Timeout waiting for EEPROM sector erase to complete at address 0x{dFlashAddress:X8}", "MC9S12XEP100Helper");
                    return false;
                }

                _logger?.LogInformation($"Erased EEPROM sector at address 0x{address:X8}", "MC9S12XEP100Helper");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error erasing EEPROM sector: {ex.Message}", "MC9S12XEP100Helper");
                return false;
            }
        }

        /// <summary>
        /// Verifies the ECC for the specified data
        /// </summary>
        /// <param name="data">The data to verify (must be 8 bytes)</param>
        /// <param name="ecc">The ECC byte to verify against</param>
        /// <returns>True if the ECC is valid, false otherwise</returns>
        public bool VerifyECC(byte[] data, byte ecc)
        {
            if (data == null || data.Length != 8)
            {
                throw new ArgumentException("Data must be exactly 8 bytes for ECC verification", nameof(data));
            }

            // Calculate the ECC for the data
            byte calculatedEcc = CalculateECC(data);

            // Compare the calculated ECC with the provided ECC
            return calculatedEcc == ecc;
        }

        /// <summary>
        /// Checks for ECC errors in the specified data with detailed error information
        /// </summary>
        /// <param name="data">The data to check (must be 8 bytes)</param>
        /// <param name="ecc">The ECC byte to check against</param>
        /// <param name="correctedData">The corrected data (if a single-bit error is detected)</param>
        /// <returns>Detailed ECC error information</returns>
        public ECCErrorInfo CheckECCErrorsDetailed(byte[] data, byte ecc, out byte[] correctedData)
        {
            if (data == null || data.Length != 8)
            {
                throw new ArgumentException("Data must be exactly 8 bytes for ECC error checking", nameof(data));
            }

            // Create a copy of the data for correction
            correctedData = new byte[data.Length];
            Array.Copy(data, correctedData, data.Length);

            // Calculate the ECC for the data
            byte calculatedEcc = CalculateECC(data);

            // Create the error info object
            ECCErrorInfo errorInfo = new ECCErrorInfo
            {
                CalculatedECC = calculatedEcc,
                StoredECC = ecc
            };

            // Calculate the syndrome (XOR of calculated and stored ECC)
            byte syndrome = (byte)(calculatedEcc ^ ecc);
            errorInfo.Syndrome = syndrome;

            // Count the number of bits set in the syndrome
            int syndromeBitCount = CountSetBits(syndrome);
            errorInfo.SyndromeBitCount = syndromeBitCount;

            // Check for errors based on the syndrome
            if (syndrome == 0)
            {
                // No error
                errorInfo.Status = ECCErrorStatus.NoError;
            }
            else if (syndromeBitCount == 1 || syndromeBitCount == 3 || syndromeBitCount == 5 || syndromeBitCount == 7)
            {
                // Single-bit error in the ECC byte itself
                errorInfo.Status = ECCErrorStatus.SingleBitError;
                errorInfo.BytePosition = -1;
                errorInfo.BitPosition = -1;
            }
            else if (syndromeBitCount % 2 == 0)
            {
                // Single-bit error in the data
                errorInfo.Status = ECCErrorStatus.SingleBitError;

                // Determine the byte and bit position of the error
                int bytePos = (syndrome >> 3) & 0x07;
                int bitPos = syndrome & 0x07;
                errorInfo.BytePosition = bytePos;
                errorInfo.BitPosition = bitPos;

                // Correct the error by flipping the bit
                correctedData[bytePos] ^= (byte)(1 << bitPos);
            }
            else
            {
                // Multi-bit error (cannot be corrected)
                errorInfo.Status = ECCErrorStatus.MultiBitError;
            }

            return errorInfo;
        }

        /// <summary>
        /// Checks for ECC errors in the specified data
        /// </summary>
        /// <param name="data">The data to check (must be 8 bytes)</param>
        /// <param name="ecc">The ECC byte to check against</param>
        /// <param name="correctedData">The corrected data (if a single-bit error is detected)</param>
        /// <returns>The ECC error status</returns>
        public ECCErrorStatus CheckECCErrors(byte[] data, byte ecc, out byte[] correctedData)
        {
            ECCErrorInfo errorInfo = CheckECCErrorsDetailed(data, ecc, out correctedData);
            return errorInfo.Status;
        }

        /// <summary>
        /// Pads the specified data to a phrase size (8 bytes)
        /// </summary>
        /// <param name="data">The data to pad</param>
        /// <returns>The padded data</returns>
        public byte[] PadToPhraseSize(byte[] data)
        {
            if (data == null)
            {
                throw new ArgumentNullException(nameof(data));
            }

            // If the data is already a multiple of the phrase size, return it as is
            if (data.Length % MC9S12XEP100Configuration.PHRASE_SIZE == 0)
            {
                return data;
            }

            // Calculate the padded size
            int paddedSize = ((data.Length + MC9S12XEP100Configuration.PHRASE_SIZE - 1) / MC9S12XEP100Configuration.PHRASE_SIZE) * MC9S12XEP100Configuration.PHRASE_SIZE;

            // Create a new array with the padded size
            byte[] paddedData = new byte[paddedSize];

            // Copy the original data
            Array.Copy(data, paddedData, data.Length);

            // Fill the rest with 0xFF (erased state)
            for (int i = data.Length; i < paddedSize; i++)
            {
                paddedData[i] = 0xFF;
            }

            return paddedData;
        }

        /// <summary>
        /// Converts a DTC (Diagnostic Trouble Code) string to bytes
        /// </summary>
        /// <param name="dtc">The DTC string (e.g., "P0123")</param>
        /// <returns>The DTC as bytes</returns>
        public byte[] ConvertDTCToBytes(string dtc)
        {
            if (string.IsNullOrEmpty(dtc) || dtc.Length < 5)
            {
                throw new ArgumentException("Invalid DTC format", nameof(dtc));
            }

            // Extract the DTC components
            char type = dtc[0];
            string code = dtc.Substring(1);

            // Convert the type to a byte
            byte typeByte = 0;
            switch (type)
            {
                case 'P': typeByte = 0x00; break; // Powertrain
                case 'C': typeByte = 0x01; break; // Chassis
                case 'B': typeByte = 0x02; break; // Body
                case 'U': typeByte = 0x03; break; // Network
                default: throw new ArgumentException($"Invalid DTC type: {type}", nameof(dtc));
            }

            // Convert the code to bytes
            if (!int.TryParse(code, System.Globalization.NumberStyles.HexNumber, null, out int codeValue))
            {
                throw new ArgumentException($"Invalid DTC code: {code}", nameof(dtc));
            }

            // Create the DTC bytes
            byte[] dtcBytes = new byte[3];
            dtcBytes[0] = typeByte;
            dtcBytes[1] = (byte)((codeValue >> 8) & 0xFF);
            dtcBytes[2] = (byte)(codeValue & 0xFF);

            return dtcBytes;
        }

        /// <summary>
        /// Converts DTC bytes to a DTC string
        /// </summary>
        /// <param name="dtcBytes">The DTC bytes</param>
        /// <returns>The DTC string</returns>
        public string ConvertBytesToDTC(byte[] dtcBytes)
        {
            if (dtcBytes == null || dtcBytes.Length < 3)
            {
                throw new ArgumentException("Invalid DTC bytes", nameof(dtcBytes));
            }

            // Extract the DTC components
            byte typeByte = dtcBytes[0];
            byte highByte = dtcBytes[1];
            byte lowByte = dtcBytes[2];

            // Convert the type byte to a character
            char type;
            switch (typeByte)
            {
                case 0x00: type = 'P'; break; // Powertrain
                case 0x01: type = 'C'; break; // Chassis
                case 0x02: type = 'B'; break; // Body
                case 0x03: type = 'U'; break; // Network
                default: throw new ArgumentException($"Invalid DTC type byte: {typeByte}", nameof(dtcBytes));
            }

            // Convert the code bytes to a string
            int code = (highByte << 8) | lowByte;

            // Format the DTC string
            return $"{type}{code:X4}";
        }

        /// <summary>
        /// Calculates the optimal burst size for flash programming
        /// </summary>
        /// <param name="dataSize">The size of the data to program</param>
        /// <param name="address">The starting address</param>
        /// <returns>The optimal burst size in phrases</returns>
        public int CalculateOptimalBurstSize(int dataSize, uint address)
        {
            // Default burst size (in phrases)
            int defaultBurstSize = 16;

            // For small data sizes, use smaller bursts
            if (dataSize <= MC9S12XEP100Configuration.PHRASE_SIZE * 4)
            {
                return 1; // Single phrase at a time
            }
            else if (dataSize <= MC9S12XEP100Configuration.PHRASE_SIZE * 16)
            {
                return 4; // 4 phrases at a time
            }
            else if (dataSize <= MC9S12XEP100Configuration.PHRASE_SIZE * 64)
            {
                return 8; // 8 phrases at a time
            }

            // For larger data sizes, consider memory characteristics
            // Check if the address is in a region with slower access
            if (address >= MC9S12XEP100Configuration.MemoryMap.D_FLASH_START &&
                address < MC9S12XEP100Configuration.MemoryMap.D_FLASH_START + MC9S12XEP100Configuration.D_FLASH_SIZE)
            {
                // D-Flash has slower access, use smaller bursts
                return 8;
            }

            // For standard P-Flash, use the default burst size
            return defaultBurstSize;
        }

        /// <summary>
        /// Reads a block of data from flash memory
        /// </summary>
        /// <param name="address">The starting address</param>
        /// <param name="size">The number of bytes to read</param>
        /// <param name="progress">Optional progress reporting</param>
        /// <returns>The read data</returns>
        public async Task<byte[]> ReadFlashBlockAsync(uint address, int size, IProgress<int>? progress = null)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return Array.Empty<byte>();
            }

            _logger?.LogInformation($"Reading flash block at address 0x{address:X8}, size: {size} bytes", "MC9S12XEP100Helper");

            // Create a buffer for the data
            byte[] data = new byte[size];
            int totalRead = 0;
            int lastProgressReported = 0;

            // Read the data in chunks
            for (int i = 0; i < size; i++)
            {
                uint readAddress = address + (uint)i;
                data[i] = await _registerAccess.ReadRegisterByteAsync(readAddress);
                totalRead++;

                // Report progress
                if (progress != null && totalRead % 100 == 0)
                {
                    int progressPercentage = (int)((totalRead / (float)size) * 100);
                    if (progressPercentage > lastProgressReported)
                    {
                        progress.Report(progressPercentage);
                        lastProgressReported = progressPercentage;
                    }
                }
            }

            // Report 100% progress
            progress?.Report(100);

            _logger?.LogInformation($"Successfully read {totalRead} bytes from flash", "MC9S12XEP100Helper");
            return data;
        }

        /// <summary>
        /// Reads a flash phrase (8 bytes) from the specified address
        /// </summary>
        /// <param name="address">The address to read from (must be phrase-aligned)</param>
        /// <returns>The read phrase data (8 bytes) and ECC byte</returns>
        public async Task<(byte[] data, byte ecc)> ReadFlashPhraseAsync(uint address)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return (Array.Empty<byte>(), 0);
            }

            try
            {
                // Align the address to a phrase boundary
                uint alignedAddress = AlignToPhraseAddress(address);

                // Set up the flash command registers for phrase read
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_CMD, 0x04); // Read phrase command

                // Set the address registers
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_HIGH, (byte)(alignedAddress >> 16));
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_MID, (byte)(alignedAddress >> 8));
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_LOW, (byte)alignedAddress);

                // Start the read operation
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_STAT, 0x80);

                // Wait for the read operation to complete
                bool readComplete = await WaitForFlashOperationCompleteAsync(1000); // 1 second timeout for phrase read
                if (!readComplete)
                {
                    _logger?.LogError($"Timeout waiting for flash phrase read to complete at address 0x{alignedAddress:X8}", "MC9S12XEP100Helper");
                    return (Array.Empty<byte>(), 0);
                }

                // Read the data from the flash data buffer
                byte[] data = new byte[MC9S12XEP100Configuration.PHRASE_SIZE];
                for (int i = 0; i < MC9S12XEP100Configuration.PHRASE_SIZE; i++)
                {
                    data[i] = await _registerAccess.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_DATA + (uint)i);
                }

                // Read the ECC byte
                byte ecc = await _registerAccess.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ECC);

                _logger?.LogDebug($"Read flash phrase at address 0x{alignedAddress:X8} with ECC 0x{ecc:X2}", "MC9S12XEP100Helper");
                return (data, ecc);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading flash phrase at address 0x{address:X8}: {ex.Message}", "MC9S12XEP100Helper");
                return (Array.Empty<byte>(), 0);
            }
        }

        /// <summary>
        /// Reads a flash phrase with ECC error reporting via callback
        /// </summary>
        /// <param name="address">The address to read from</param>
        /// <param name="eccErrorCallback">Callback for ECC error reporting</param>
        /// <returns>The read data</returns>
        public async Task<byte[]> ReadFlashPhraseAsync(uint address, Action<uint, ECCErrorInfo> eccErrorCallback)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return Array.Empty<byte>();
            }

            try
            {
                // First read the phrase using the standard method
                var (data, ecc) = await ReadFlashPhraseAsync(address);

                // Calculate our own ECC for the data
                byte calculatedEcc = CalculateECC(data);

                // Check if there's an ECC error
                ECCErrorInfo errorInfo = CheckECCErrorsDetailed(data, ecc, out byte[] correctedData);

                // Set additional information
                errorInfo.CalculatedECC = calculatedEcc;
                errorInfo.StoredECC = ecc;

                // Call the callback with the error info
                eccErrorCallback(address, errorInfo);

                // Return the corrected data if there was a single-bit error,
                // otherwise return the original data
                return errorInfo.Status == ECCErrorStatus.SingleBitError ? correctedData : data;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading flash phrase with ECC callback at address 0x{address:X8}: {ex.Message}", "MC9S12XEP100Helper");
                return Array.Empty<byte>();
            }
        }

        /// <summary>
        /// Programs a flash phrase (8 bytes) at the specified address
        /// </summary>
        /// <param name="address">The address to program (must be phrase-aligned)</param>
        /// <param name="data">The data to program (must be 8 bytes)</param>
        /// <returns>True if programming is successful, false otherwise</returns>
        public async Task<bool> ProgramFlashPhrasePublicAsync(uint address, byte[] data)
        {
            return await ProgramFlashPhraseAsync(address, data);
        }

        /// <summary>
        /// Reads a block of data from flash memory with ECC error reporting
        /// </summary>
        /// <param name="address">The starting address</param>
        /// <param name="size">The number of bytes to read</param>
        /// <param name="eccErrorCallback">Callback for ECC error reporting</param>
        /// <returns>The read data</returns>
        public async Task<byte[]> ReadFlashBlockAsync(uint address, int size, Action<uint, ECCErrorInfo> eccErrorCallback)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return Array.Empty<byte>();
            }

            _logger?.LogInformation($"Reading flash block at address 0x{address:X8}, size: {size} bytes with ECC error reporting", "MC9S12XEP100Helper");

            // Create a buffer for the data
            byte[] data = new byte[size];
            int totalRead = 0;
            int eccErrorCount = 0;

            // Read the data in chunks
            for (int i = 0; i < size; i++)
            {
                uint readAddress = address + (uint)i;
                data[i] = await _registerAccess.ReadRegisterByteAsync(readAddress);
                totalRead++;

                // Check for ECC errors if we're reading a complete phrase
                if ((i + 1) % MC9S12XEP100Configuration.PHRASE_SIZE == 0 || i == size - 1)
                {
                    // Read the ECC status register
                    byte eccStatus = await _registerAccess.ReadRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCSTAT);

                    // Check if there was an ECC error
                    if ((eccStatus & 0x03) != 0)
                    {
                        // Create an ECC error info object
                        ECCErrorInfo errorInfo = new ECCErrorInfo();
                        errorInfo.StoredECC = await _registerAccess.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ECC);

                        // Determine the error type
                        if ((eccStatus & MC9S12XEP100Configuration.ECC.ECCSTAT_SBERR) != 0)
                        {
                            // Single-bit error (correctable)
                            errorInfo.Status = ECCErrorStatus.SingleBitError;

                            // Get the error location from the ECC error register
                            byte errorLoc = await _registerAccess.ReadRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCERR);
                            errorInfo.BytePosition = (errorLoc & MC9S12XEP100Configuration.ECC.ECCERR_WPOS) >> 4; // Word position
                            errorInfo.BitPosition = errorLoc & MC9S12XEP100Configuration.ECC.ECCERR_BPOS; // Bit position
                        }
                        else if ((eccStatus & MC9S12XEP100Configuration.ECC.ECCSTAT_MBERR) != 0)
                        {
                            // Multi-bit error (uncorrectable)
                            errorInfo.Status = ECCErrorStatus.MultiBitError;
                        }

                        // Calculate the syndrome
                        errorInfo.Syndrome = (byte)(eccStatus & 0xFC);
                        errorInfo.SyndromeBitCount = CountSetBits(errorInfo.Syndrome);

                        // Call the callback with the error info
                        eccErrorCallback(readAddress - (uint)(i % MC9S12XEP100Configuration.PHRASE_SIZE), errorInfo);
                        eccErrorCount++;
                    }
                }
            }

            if (eccErrorCount > 0)
            {
                _logger?.LogWarning($"Detected {eccErrorCount} ECC errors during flash read", "MC9S12XEP100Helper");
            }

            _logger?.LogInformation($"Successfully read {totalRead} bytes from flash with ECC error reporting", "MC9S12XEP100Helper");
            return data;
        }

        /// <summary>
        /// Erases a flash sector
        /// </summary>
        /// <param name="address">The sector address</param>
        /// <returns>True if erase is successful, false otherwise</returns>
        public async Task<bool> EraseFlashSectorAsync(uint address)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return false;
            }

            // Align address to sector boundary
            uint sectorAddress = AlignToSectorAddress(address);
            _logger?.LogInformation($"Erasing flash sector at address 0x{sectorAddress:X8}", "MC9S12XEP100Helper");

            try
            {
                // Set up the flash command register for sector erase
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_CMD, 0x0A);

                // Set up the flash address registers
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_HIGH, (byte)((sectorAddress >> 16) & 0xFF));
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_MID, (byte)((sectorAddress >> 8) & 0xFF));
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_LOW, (byte)(sectorAddress & 0xFF));

                // Start the erase operation
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_STAT, 0x80);

                // Wait for the erase operation to complete
                bool eraseComplete = await WaitForFlashOperationCompleteAsync(5000); // 5 second timeout
                if (!eraseComplete)
                {
                    _logger?.LogError($"Timeout waiting for flash sector erase to complete at address 0x{sectorAddress:X8}", "MC9S12XEP100Helper");
                    return false;
                }

                _logger?.LogInformation($"Flash sector at address 0x{sectorAddress:X8} erased successfully", "MC9S12XEP100Helper");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error erasing flash sector at address 0x{address:X8}: {ex.Message}", "MC9S12XEP100Helper");
                return false;
            }
        }

        /// <summary>
        /// Programs a block of data to flash memory
        /// </summary>
        /// <param name="address">The starting address</param>
        /// <param name="data">The data to program</param>
        /// <param name="verifyAfterWrite">Whether to verify the data after writing</param>
        /// <param name="operationName">The name of the operation for logging</param>
        /// <param name="progress">Optional progress reporting</param>
        /// <returns>True if programming is successful, false otherwise</returns>
        public async Task<bool> ProgramFlashBlockAsync(uint address, byte[] data, bool verifyAfterWrite = true, string operationName = "ProgramFlashBlock", IProgress<int>? progress = null)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return false;
            }

            if (data == null || data.Length == 0)
            {
                _logger?.LogError("Data is null or empty", "MC9S12XEP100Helper");
                return false;
            }

            _logger?.LogInformation($"Programming flash block at address 0x{address:X8}, size: {data.Length} bytes", "MC9S12XEP100Helper");

            // Calculate the number of phrases to program
            int phraseCount = (data.Length + MC9S12XEP100Configuration.PHRASE_SIZE - 1) / MC9S12XEP100Configuration.PHRASE_SIZE;
            int totalPhrases = phraseCount;
            int phrasesWritten = 0;
            int lastProgressReported = 0;

            // Program the data phrase by phrase
            for (int i = 0; i < phraseCount; i++)
            {
                uint phraseAddress = address + (uint)(i * MC9S12XEP100Configuration.PHRASE_SIZE);
                int phraseOffset = i * MC9S12XEP100Configuration.PHRASE_SIZE;
                int phraseSize = Math.Min(MC9S12XEP100Configuration.PHRASE_SIZE, data.Length - phraseOffset);

                // Create a phrase buffer
                byte[] phraseData = new byte[MC9S12XEP100Configuration.PHRASE_SIZE];
                Array.Copy(data, phraseOffset, phraseData, 0, phraseSize);

                // If the phrase is not full, fill the rest with 0xFF (erased state)
                for (int j = phraseSize; j < MC9S12XEP100Configuration.PHRASE_SIZE; j++)
                {
                    phraseData[j] = 0xFF;
                }

                // Program the phrase
                bool success = await ProgramFlashPhraseAsync(phraseAddress, phraseData);
                if (!success)
                {
                    _logger?.LogError($"Failed to program phrase at address 0x{phraseAddress:X8}", "MC9S12XEP100Helper");
                    return false;
                }

                phrasesWritten++;

                // Report progress
                if (progress != null && phrasesWritten % 10 == 0)
                {
                    int progressPercentage = (int)((phrasesWritten / (float)totalPhrases) * 100);
                    if (progressPercentage > lastProgressReported)
                    {
                        progress.Report(progressPercentage);
                        lastProgressReported = progressPercentage;
                    }
                }
            }

            // Report 100% progress
            progress?.Report(100);

            // Verify the data if requested
            if (verifyAfterWrite)
            {
                bool verifySuccess = await VerifyFlashIntegrityAsync(address, data.Length);
                if (!verifySuccess)
                {
                    _logger?.LogError("Flash integrity verification failed", "MC9S12XEP100Helper");
                    return false;
                }
            }

            _logger?.LogInformation($"Successfully programmed {phrasesWritten} phrases to flash", "MC9S12XEP100Helper");
            return true;
        }

        /// <summary>
        /// Programs a phrase in flash memory
        /// </summary>
        /// <param name="address">The phrase address</param>
        /// <param name="data">The data to program (must be 8 bytes)</param>
        /// <returns>True if programming is successful, false otherwise</returns>
        private async Task<bool> ProgramFlashPhraseAsync(uint address, byte[] data)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return false;
            }

            if (data == null || data.Length != MC9S12XEP100Configuration.PHRASE_SIZE)
            {
                _logger?.LogError($"Data must be exactly {MC9S12XEP100Configuration.PHRASE_SIZE} bytes for phrase programming", "MC9S12XEP100Helper");
                return false;
            }

            // Align address to phrase boundary
            uint phraseAddress = AlignToPhraseAddress(address);
            _logger?.LogDebug($"Programming flash phrase at address 0x{phraseAddress:X8}", "MC9S12XEP100Helper");

            try
            {
                // Set up the flash command register for phrase program
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_CMD, 0x06);

                // Set up the flash address registers
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_HIGH, (byte)((phraseAddress >> 16) & 0xFF));
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_MID, (byte)((phraseAddress >> 8) & 0xFF));
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_LOW, (byte)(phraseAddress & 0xFF));

                // Write the phrase data to the flash data registers
                for (int i = 0; i < MC9S12XEP100Configuration.PHRASE_SIZE; i++)
                {
                    await _registerAccess.WriteRegisterByteAsync((uint)(MC9S12XEP100Configuration.SPI.Registers.FLASH_DATA + i), data[i]);
                }

                // Calculate ECC for the phrase
                byte ecc = CalculateECC(data);
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ECC, ecc);

                // Start the program operation
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_STAT, 0x80);

                // Wait for the program operation to complete
                bool programComplete = await WaitForFlashOperationCompleteAsync(1000); // 1 second timeout
                if (!programComplete)
                {
                    _logger?.LogError($"Timeout waiting for flash phrase program to complete at address 0x{phraseAddress:X8}", "MC9S12XEP100Helper");
                    return false;
                }

                _logger?.LogDebug($"Flash phrase at address 0x{phraseAddress:X8} programmed successfully", "MC9S12XEP100Helper");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error programming flash phrase at address 0x{address:X8}: {ex.Message}", "MC9S12XEP100Helper");
                return false;
            }
        }

        /// <summary>
        /// Performs secure flash programming with verification
        /// </summary>
        /// <param name="address">The address to program</param>
        /// <param name="data">The data to program</param>
        /// <returns>True if programming is successful, false otherwise</returns>
        public async Task<bool> SecureFlashProgramAsync(uint address, byte[] data)
        {
            return await _security.SecureFlashProgramAsync(address, data);
        }

        /// <summary>
        /// Gets the current flash protection settings
        /// </summary>
        /// <returns>The flash protection settings byte</returns>
        public async Task<byte> GetFlashProtectionAsync()
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return 0;
            }

            try
            {
                // Read the flash protection register
                byte protection = await _registerAccess.ReadRegisterByteAsync(MC9S12XEP100Configuration.FlashProtection.FLASH_PROT);

                _logger?.LogDebug($"Flash protection settings: 0x{protection:X2}", "MC9S12XEP100Helper");
                return protection;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error getting flash protection settings: {ex.Message}", "MC9S12XEP100Helper");
                return 0;
            }
        }

        /// <summary>
        /// Sets the flash protection settings
        /// </summary>
        /// <param name="protection">The flash protection settings byte</param>
        /// <returns>True if the settings are successfully applied, false otherwise</returns>
        public async Task<bool> SetFlashProtectionAsync(byte protection)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return false;
            }

            try
            {
                // Write to the flash protection register
                bool result = await _registerAccess.WriteRegisterByteAsync(
                    MC9S12XEP100Configuration.FlashProtection.FLASH_PROT, protection);

                if (result)
                {
                    _logger?.LogInformation($"Flash protection settings set to 0x{protection:X2}", "MC9S12XEP100Helper");
                }
                else
                {
                    _logger?.LogError("Failed to set flash protection settings", "MC9S12XEP100Helper");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error setting flash protection: {ex.Message}", "MC9S12XEP100Helper");
                return false;
            }
        }

        /// <summary>
        /// Enables flash protection
        /// </summary>
        /// <param name="protectionLevel">The protection level (0-7)</param>
        /// <returns>True if flash protection is successfully enabled, false otherwise</returns>
        public async Task<bool> EnableFlashProtectionAsync(byte protectionLevel)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return false;
            }

            if (protectionLevel > 7)
            {
                _logger?.LogError($"Invalid protection level: {protectionLevel}. Must be 0-7.", "MC9S12XEP100Helper");
                return false;
            }

            try
            {
                // Create protection byte with FPOPEN = 0 (protection enabled)
                // and protection level in FPLS bits
                byte protection = protectionLevel;

                // Write to the flash protection register
                bool result = await _registerAccess.WriteRegisterByteAsync(
                    MC9S12XEP100Configuration.FlashProtection.FLASH_PROT, protection);

                if (result)
                {
                    _logger?.LogInformation($"Flash protection enabled with level {protectionLevel}", "MC9S12XEP100Helper");
                }
                else
                {
                    _logger?.LogError("Failed to enable flash protection", "MC9S12XEP100Helper");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error enabling flash protection: {ex.Message}", "MC9S12XEP100Helper");
                return false;
            }
        }

        /// <summary>
        /// Enables flash protection with detailed configuration
        /// </summary>
        /// <param name="protectLowerRange">Whether to protect the lower range</param>
        /// <param name="protectHigherRange">Whether to protect the higher range</param>
        /// <param name="lowerRangeSize">The size of the lower protected range (0-7)</param>
        /// <param name="higherRangeSize">The size of the higher protected range (0-7)</param>
        /// <returns>True if flash protection is successfully enabled, false otherwise</returns>
        public async Task<bool> EnableFlashProtectionAsync(bool protectLowerRange, bool protectHigherRange, byte lowerRangeSize, byte higherRangeSize)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return false;
            }

            if (lowerRangeSize > 7)
            {
                _logger?.LogError($"Invalid lower range size: {lowerRangeSize}. Must be 0-7.", "MC9S12XEP100Helper");
                return false;
            }

            if (higherRangeSize > 7)
            {
                _logger?.LogError($"Invalid higher range size: {higherRangeSize}. Must be 0-7.", "MC9S12XEP100Helper");
                return false;
            }

            try
            {
                // Create protection byte
                byte protection = 0;

                // Set the FPOPEN bit to 0 to enable protection
                // protection &= 0x7F; // Not needed as we start with 0

                // Set the FPHDIS bit if higher range protection is enabled
                if (protectHigherRange)
                {
                    protection |= 0x40; // FPHDIS = 1
                }

                // Set the FPLDIS bit if lower range protection is enabled
                if (protectLowerRange)
                {
                    protection |= 0x20; // FPLDIS = 1
                }

                // Set the FPHS bits for higher range size
                protection |= (byte)((higherRangeSize & 0x07) << 2);

                // Set the FPLS bits for lower range size
                protection |= (byte)(lowerRangeSize & 0x03);

                // Write to the flash protection register
                bool result = await _registerAccess.WriteRegisterByteAsync(
                    MC9S12XEP100Configuration.FlashProtection.FLASH_PROT, protection);

                if (result)
                {
                    _logger?.LogInformation($"Flash protection enabled with configuration: 0x{protection:X2}", "MC9S12XEP100Helper");
                }
                else
                {
                    _logger?.LogError("Failed to enable flash protection", "MC9S12XEP100Helper");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error enabling flash protection: {ex.Message}", "MC9S12XEP100Helper");
                return false;
            }
        }

        /// <summary>
        /// Gets the current EEPROM protection settings
        /// </summary>
        /// <returns>The EEPROM protection settings byte</returns>
        public async Task<byte> GetEEPROMProtectionAsync()
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return 0;
            }

            try
            {
                // Read the EEPROM protection register
                byte protection = await _registerAccess.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_PROTECT);

                _logger?.LogDebug($"EEPROM protection settings: 0x{protection:X2}", "MC9S12XEP100Helper");
                return protection;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error getting EEPROM protection settings: {ex.Message}", "MC9S12XEP100Helper");
                return 0;
            }
        }

        /// <summary>
        /// Initializes the Memory Protection Unit (MPU)
        /// </summary>
        /// <returns>True if the MPU is successfully initialized, false otherwise</returns>
        public async Task<bool> InitializeMPUAsync()
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return false;
            }

            try
            {
                // Clear any MPU flags
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.MPU.MPUFLG, 0xFF);

                // Enable the MPU for supervisor state
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.MPU.MPUSEL,
                    MC9S12XEP100Configuration.MPU.MPUSEL_SVSEN);

                _logger?.LogInformation("Memory Protection Unit (MPU) initialized", "MC9S12XEP100Helper");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error initializing MPU: {ex.Message}", "MC9S12XEP100Helper");
                return false;
            }
        }

        /// <summary>
        /// Configures an MPU descriptor
        /// </summary>
        /// <param name="descriptorIndex">The descriptor index (0-7)</param>
        /// <param name="startAddress">The start address</param>
        /// <param name="endAddress">The end address</param>
        /// <param name="writeProtect">Whether to enable write protection</param>
        /// <param name="nonExecutable">Whether to make the region non-executable</param>
        /// <returns>True if the descriptor is successfully configured, false otherwise</returns>
        public async Task<bool> ConfigureMPUDescriptorAsync(
            byte descriptorIndex,
            uint startAddress,
            uint endAddress,
            bool writeProtect,
            bool nonExecutable)
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return false;
            }

            if (descriptorIndex > 7)
            {
                _logger?.LogError($"Invalid descriptor index: {descriptorIndex}. Must be 0-7.", "MC9S12XEP100Helper");
                return false;
            }

            try
            {
                // Select the descriptor
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.MPU.MPUSEL,
                    (byte)(MC9S12XEP100Configuration.MPU.MPUSEL_SVSEN | descriptorIndex));

                // Configure descriptor registers
                // MPUDESC0: Enable access for all masters
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.MPU.MPUDESC0, 0xF0);

                // MPUDESC1-4: Start and end addresses
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.MPU.MPUDESC1, (byte)(startAddress >> 16));
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.MPU.MPUDESC2, (byte)(startAddress >> 8));
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.MPU.MPUDESC3, (byte)(endAddress >> 16));
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.MPU.MPUDESC4, (byte)(endAddress >> 8));

                // MPUDESC5: Protection flags and start bit
                byte desc5 = MC9S12XEP100Configuration.MPU.MPUDESC5_START;
                if (writeProtect)
                {
                    desc5 |= MC9S12XEP100Configuration.MPU.MPUDESC5_WP;
                }
                if (nonExecutable)
                {
                    desc5 |= MC9S12XEP100Configuration.MPU.MPUDESC5_NX;
                }
                await _registerAccess.WriteRegisterByteAsync(MC9S12XEP100Configuration.MPU.MPUDESC5, desc5);

                _logger?.LogInformation($"Configured MPU descriptor {descriptorIndex} for address range 0x{startAddress:X8}-0x{endAddress:X8}", "MC9S12XEP100Helper");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error configuring MPU descriptor: {ex.Message}", "MC9S12XEP100Helper");
                return false;
            }
        }

        /// <summary>
        /// Gets the flash health status
        /// </summary>
        /// <returns>The flash health status</returns>
        public FlashHealthStatus GetFlashHealthStatus()
        {
            return _flashHealthStatus;
        }

        /// <summary>
        /// Performs a flash health check
        /// </summary>
        /// <returns>True if the flash is healthy, false otherwise</returns>
        public async Task<bool> PerformFlashHealthCheckAsync()
        {
            if (_registerAccess == null)
            {
                _logger?.LogError("Register access is null", "MC9S12XEP100Helper");
                return false;
            }

            try
            {
                // Read the flash status register
                byte status = await _registerAccess.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_STAT);

                // Check for errors
                bool hasErrors = (status & 0x30) != 0; // Check ACCERR and PVIOL bits

                // Update flash health status
                _flashHealthStatus.LastCheckTime = DateTime.Now;
                _flashHealthStatus.IsHealthy = !hasErrors;

                if (hasErrors)
                {
                    _flashHealthStatus.ErrorCount++;
                    _flashHealthStatus.LastErrorTime = DateTime.Now;
                    _flashHealthStatus.LastErrorCode = status;

                    _logger?.LogWarning($"Flash health check failed. Status: 0x{status:X2}", "MC9S12XEP100Helper");
                }
                else
                {
                    _logger?.LogInformation("Flash health check passed", "MC9S12XEP100Helper");
                }

                // Save the updated status
                SaveFlashHealthStatus();

                return !hasErrors;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error performing flash health check: {ex.Message}", "MC9S12XEP100Helper");
                return false;
            }
        }

        /// <summary>
        /// Saves the flash health status to a file
        /// </summary>
        private void SaveFlashHealthStatus()
        {
            try
            {
                string directory = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                    "VolvoFlashWR");

                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                string filePath = Path.Combine(directory, "FlashHealthStatus.json");
                string json = System.Text.Json.JsonSerializer.Serialize(_flashHealthStatus);
                File.WriteAllText(filePath, json);

                _logger?.LogDebug($"Saved flash health status to {filePath}", "MC9S12XEP100Helper");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error saving flash health status: {ex.Message}", "MC9S12XEP100Helper");
            }
        }

        /// <summary>
        /// Represents a baud rate divisor with high and low bytes
        /// </summary>
        public struct BaudRateDivisor
        {
            /// <summary>
            /// Gets or sets the high byte of the divisor
            /// </summary>
            public byte High { get; set; }

            /// <summary>
            /// Gets or sets the low byte of the divisor
            /// </summary>
            public byte Low { get; set; }

            /// <summary>
            /// Initializes a new instance of the BaudRateDivisor struct
            /// </summary>
            /// <param name="divisor">The 16-bit divisor value</param>
            public BaudRateDivisor(ushort divisor)
            {
                High = (byte)((divisor >> 8) & 0xFF);
                Low = (byte)(divisor & 0xFF);
            }

            /// <summary>
            /// Gets the 16-bit divisor value
            /// </summary>
            public ushort Value => (ushort)((High << 8) | Low);
        }

        /// <summary>
        /// Calculates the SCI baud rate divisor for the specified baud rate
        /// </summary>
        /// <param name="baudRate">The desired baud rate</param>
        /// <returns>The baud rate divisor (16-bit value)</returns>
        public ushort CalculateSCIBaudRateDivisor(int baudRate)
        {
            if (baudRate <= 0)
            {
                _logger?.LogError($"Invalid baud rate: {baudRate}", "MC9S12XEP100Helper");
                return 0;
            }

            // Calculate the divisor based on the CPU bus frequency
            // SCI baud rate = Bus Clock / (16 * SBR)
            // SBR = Bus Clock / (16 * Baud Rate)
            int busFrequency = MC9S12XEP100Configuration.MAX_CPU_FREQUENCY;
            ushort divisor = (ushort)(busFrequency / (16 * baudRate));

            _logger?.LogDebug($"Calculated SCI baud rate divisor {divisor} for {baudRate} bps", "MC9S12XEP100Helper");
            return divisor;
        }

        /// <summary>
        /// Calculates the SCI baud rate divisor for the specified baud rate and bus frequency
        /// </summary>
        /// <param name="baudRate">The desired baud rate</param>
        /// <param name="busFrequency">The bus frequency in Hz</param>
        /// <returns>The baud rate divisor as a BaudRateDivisor struct</returns>
        public BaudRateDivisor CalculateSCIBaudRateDivisor(int baudRate, int busFrequency)
        {
            if (baudRate <= 0)
            {
                _logger?.LogError($"Invalid baud rate: {baudRate}", "MC9S12XEP100Helper");
                return new BaudRateDivisor(0);
            }

            if (busFrequency <= 0)
            {
                _logger?.LogError($"Invalid bus frequency: {busFrequency}", "MC9S12XEP100Helper");
                return new BaudRateDivisor(0);
            }

            // Calculate the divisor based on the specified bus frequency
            // SCI baud rate = Bus Clock / (16 * SBR)
            // SBR = Bus Clock / (16 * Baud Rate)
            ushort divisor = (ushort)(busFrequency / (16 * baudRate));
            BaudRateDivisor result = new BaudRateDivisor(divisor);

            _logger?.LogDebug($"Calculated SCI baud rate divisor 0x{result.High:X2}{result.Low:X2} for {baudRate} bps at {busFrequency} Hz", "MC9S12XEP100Helper");
            return result;
        }

        /// <summary>
        /// Verifies the integrity of flash memory
        /// </summary>
        /// <param name="startAddress">The starting address</param>
        /// <param name="size">The size in bytes</param>
        /// <returns>True if verification is successful, false otherwise</returns>
        public async Task<bool> VerifyFlashIntegrityAsync(uint startAddress, int size)
        {
            return await _security.VerifyFlashIntegrityAsync(startAddress, size);
        }
    }
}
