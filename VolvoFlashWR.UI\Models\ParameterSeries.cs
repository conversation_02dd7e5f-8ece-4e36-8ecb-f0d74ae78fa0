using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;

namespace VolvoFlashWR.UI.Models
{
    /// <summary>
    /// Represents a series of parameter data for charting
    /// </summary>
    public class ParameterSeries
    {
        /// <summary>
        /// The name of the parameter
        /// </summary>
        public string ParameterName { get; set; } = string.Empty;

        /// <summary>
        /// The unit of measurement for the parameter
        /// </summary>
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// The collection of data points for this parameter
        /// </summary>
        public ObservableCollection<ChartDataPoint> DataPoints { get; set; } = new ObservableCollection<ChartDataPoint>();

        /// <summary>
        /// The minimum value in the series
        /// </summary>
        public double MinValue => DataPoints.Count > 0 ? DataPoints.Min(p => p.Value) : 0;

        /// <summary>
        /// The maximum value in the series
        /// </summary>
        public double MaxValue => DataPoints.Count > 0 ? DataPoints.Max(p => p.Value) : 0;

        /// <summary>
        /// The average value in the series
        /// </summary>
        public double AverageValue => DataPoints.Count > 0 ? DataPoints.Average(p => p.Value) : 0;

        /// <summary>
        /// The color of the series in the chart (as a hex string)
        /// </summary>
        public string Color { get; set; } = "#1E88E5"; // Default blue color

        /// <summary>
        /// Default constructor
        /// </summary>
        public ParameterSeries()
        {
        }

        /// <summary>
        /// Constructor with parameter name
        /// </summary>
        /// <param name="parameterName">The name of the parameter</param>
        public ParameterSeries(string parameterName)
        {
            ParameterName = parameterName;
        }

        /// <summary>
        /// Constructor with parameter name and unit
        /// </summary>
        /// <param name="parameterName">The name of the parameter</param>
        /// <param name="unit">The unit of measurement</param>
        public ParameterSeries(string parameterName, string unit)
        {
            ParameterName = parameterName;
            Unit = unit;
        }

        /// <summary>
        /// Adds a new data point to the series
        /// </summary>
        /// <param name="value">The value to add</param>
        public void AddDataPoint(double value)
        {
            DataPoints.Add(new ChartDataPoint(ParameterName, value));
        }

        /// <summary>
        /// Adds a new data point to the series with a specific timestamp
        /// </summary>
        /// <param name="timestamp">The timestamp of the data point</param>
        /// <param name="value">The value to add</param>
        public void AddDataPoint(DateTime timestamp, double value)
        {
            DataPoints.Add(new ChartDataPoint(timestamp, ParameterName, value));
        }

        /// <summary>
        /// Clears all data points from the series
        /// </summary>
        public void ClearDataPoints()
        {
            DataPoints.Clear();
        }

        /// <summary>
        /// Limits the number of data points to the specified maximum
        /// </summary>
        /// <param name="maxPoints">The maximum number of data points to keep</param>
        public void LimitDataPoints(int maxPoints)
        {
            if (DataPoints.Count <= maxPoints)
                return;

            while (DataPoints.Count > maxPoints)
            {
                DataPoints.RemoveAt(0);
            }
        }
    }
}
