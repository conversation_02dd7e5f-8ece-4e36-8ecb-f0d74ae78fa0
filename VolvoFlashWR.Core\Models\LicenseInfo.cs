using System;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents the license status
    /// </summary>
    public enum LicenseStatus
    {
        /// <summary>
        /// The application is not licensed
        /// </summary>
        Unlicensed,

        /// <summary>
        /// The application is in trial period
        /// </summary>
        Trial,

        /// <summary>
        /// The application is licensed
        /// </summary>
        Licensed,

        /// <summary>
        /// The trial period has expired
        /// </summary>
        TrialExpired,

        /// <summary>
        /// The license has expired
        /// </summary>
        LicenseExpired
    }

    /// <summary>
    /// Represents license information
    /// </summary>
    public class LicenseInfo
    {
        /// <summary>
        /// Gets or sets the license status
        /// </summary>
        public LicenseStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the license key
        /// </summary>
        public string? LicenseKey { get; set; }

        /// <summary>
        /// Gets or sets the license activation date
        /// </summary>
        public DateTime? ActivationDate { get; set; }

        /// <summary>
        /// Gets or sets the license expiration date
        /// </summary>
        public DateTime? ExpirationDate { get; set; }

        /// <summary>
        /// Gets or sets the trial start date
        /// </summary>
        public DateTime? TrialStartDate { get; set; }

        /// <summary>
        /// Gets or sets the trial end date
        /// </summary>
        public DateTime? TrialEndDate { get; set; }

        /// <summary>
        /// Gets or sets the number of days remaining in the trial period
        /// </summary>
        public int TrialDaysRemaining { get; set; }

        /// <summary>
        /// Gets or sets the hardware ID
        /// </summary>
        public string? HardwareId { get; set; }

        /// <summary>
        /// Gets or sets the user name
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// Gets or sets the company name
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// Gets or sets the email address
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// Creates a new instance of the LicenseInfo class
        /// </summary>
        public LicenseInfo()
        {
            Status = LicenseStatus.Unlicensed;
            TrialDaysRemaining = 0;
        }
    }
}
