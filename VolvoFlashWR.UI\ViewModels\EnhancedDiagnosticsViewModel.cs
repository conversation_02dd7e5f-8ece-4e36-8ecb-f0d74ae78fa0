using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using Microsoft.Win32;
using System.IO;
using System.Windows.Media;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using LiveChartsCore.Defaults;
using SkiaSharp;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.UI.Commands;
using VolvoFlashWR.UI.Models;

namespace VolvoFlashWR.UI.ViewModels
{
    public class EnhancedDiagnosticsViewModel : INotifyPropertyChanged
    {
        #region Private Fields

        private readonly ILoggingService _loggingService;
        private readonly IECUCommunicationService _ecuCommunicationService;
        private ECUDevice _selectedECUForDiagnostics;
        private ObservableCollection<ECUDevice> _connectedECUs;
        private ObservableCollection<ECUFault> _activeFaults;
        private ObservableCollection<ECUFault> _inactiveFaults;
        private ObservableCollection<ParameterItem> _parameters;
        private DiagnosticData _diagnosticData;
        private string _selectedDiagnosticMode;
        private OperatingMode _selectedOperatingMode;
        private bool _isBusy;
        private string _statusMessage;
        private int _operationProgress;
        private string _operationStatus;
        private ObservableCollection<MonitoringDataPoint> _monitoringData;
        private ObservableCollection<string> _monitorableParameters;
        private string _selectedParameterForChart;
        private List<string> _selectedParametersForMonitoring;
        private string _selectedRefreshInterval;
        private int _selectedMaxDataPoints;
        private bool _isMonitoring;
        private CancellationTokenSource _monitoringCancellationTokenSource;
        private Timer _monitoringTimer;

        // Chart-related fields
        private ObservableCollection<ISeries> _chartSeries;
        private ObservableCollection<ParameterSeries> _parameterSeries;
        private ObservableCollection<Axis> _xAxes;
        private ObservableCollection<Axis> _yAxes;
        private Dictionary<string, ThresholdConfiguration> _thresholdConfigurations;

        #endregion

        #region Properties

        public event PropertyChangedEventHandler? PropertyChanged;

        public ECUDevice SelectedECUForDiagnostics
        {
            get => _selectedECUForDiagnostics;
            set
            {
                _selectedECUForDiagnostics = value;
                OnPropertyChanged(nameof(SelectedECUForDiagnostics));
                UpdateCommandStates();
            }
        }

        public ObservableCollection<ECUDevice> ConnectedECUs
        {
            get => _connectedECUs;
            set
            {
                _connectedECUs = value;
                OnPropertyChanged(nameof(ConnectedECUs));
            }
        }

        public ObservableCollection<ECUFault> ActiveFaults
        {
            get => _activeFaults;
            set
            {
                _activeFaults = value;
                OnPropertyChanged(nameof(ActiveFaults));
            }
        }

        public ObservableCollection<ECUFault> InactiveFaults
        {
            get => _inactiveFaults;
            set
            {
                _inactiveFaults = value;
                OnPropertyChanged(nameof(InactiveFaults));
            }
        }

        public ObservableCollection<ParameterItem> Parameters
        {
            get => _parameters;
            set
            {
                _parameters = value;
                OnPropertyChanged(nameof(Parameters));
            }
        }

        public DiagnosticData DiagnosticData
        {
            get => _diagnosticData;
            set
            {
                _diagnosticData = value;
                OnPropertyChanged(nameof(DiagnosticData));
            }
        }

        public string SelectedDiagnosticMode
        {
            get => _selectedDiagnosticMode;
            set
            {
                _selectedDiagnosticMode = value;
                OnPropertyChanged(nameof(SelectedDiagnosticMode));
            }
        }

        public OperatingMode SelectedOperatingMode
        {
            get => _selectedOperatingMode;
            set
            {
                _selectedOperatingMode = value;
                OnPropertyChanged(nameof(SelectedOperatingMode));
                SetOperatingModeAsync().ConfigureAwait(false);
            }
        }

        public bool IsBusy
        {
            get => _isBusy;
            set
            {
                _isBusy = value;
                OnPropertyChanged(nameof(IsBusy));
                UpdateCommandStates();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged(nameof(StatusMessage));
            }
        }

        public int OperationProgress
        {
            get => _operationProgress;
            set
            {
                _operationProgress = value;
                OnPropertyChanged(nameof(OperationProgress));
            }
        }

        public string OperationStatus
        {
            get => _operationStatus;
            set
            {
                _operationStatus = value;
                OnPropertyChanged(nameof(OperationStatus));
            }
        }

        public List<string> DiagnosticModes => new List<string> { "Standard", "Extended", "Engineering" };

        public Array OperatingModes => Enum.GetValues(typeof(OperatingMode));

        public ObservableCollection<MonitoringDataPoint> MonitoringData
        {
            get => _monitoringData;
            set
            {
                _monitoringData = value;
                OnPropertyChanged(nameof(MonitoringData));
            }
        }

        public ObservableCollection<string> MonitorableParameters
        {
            get => _monitorableParameters;
            set
            {
                _monitorableParameters = value;
                OnPropertyChanged(nameof(MonitorableParameters));
            }
        }

        public string SelectedParameterForChart
        {
            get => _selectedParameterForChart;
            set
            {
                _selectedParameterForChart = value;
                OnPropertyChanged(nameof(SelectedParameterForChart));
                UpdateCommandStates();
            }
        }

        public List<string> SelectedParametersForMonitoring
        {
            get => _selectedParametersForMonitoring;
            set
            {
                _selectedParametersForMonitoring = value;
                OnPropertyChanged(nameof(SelectedParametersForMonitoring));
            }
        }

        public string SelectedRefreshInterval
        {
            get => _selectedRefreshInterval;
            set
            {
                _selectedRefreshInterval = value;
                OnPropertyChanged(nameof(SelectedRefreshInterval));
                UpdateMonitoringInterval();
            }
        }

        public int SelectedMaxDataPoints
        {
            get => _selectedMaxDataPoints;
            set
            {
                _selectedMaxDataPoints = value;
                OnPropertyChanged(nameof(SelectedMaxDataPoints));
            }
        }

        public bool IsMonitoring
        {
            get => _isMonitoring;
            set
            {
                _isMonitoring = value;
                OnPropertyChanged(nameof(IsMonitoring));
                UpdateCommandStates();
            }
        }

        public List<string> RefreshIntervals => new List<string> { "100ms", "250ms", "500ms", "1s", "2s", "5s", "10s" };

        public List<int> MaxDataPoints => new List<int> { 100, 250, 500, 1000, 2000, 5000 };

        // Chart-related properties
        public ObservableCollection<ISeries> ChartSeries
        {
            get => _chartSeries;
            set
            {
                _chartSeries = value;
                OnPropertyChanged(nameof(ChartSeries));
            }
        }

        public ObservableCollection<ParameterSeries> ParameterSeries
        {
            get => _parameterSeries;
            set
            {
                _parameterSeries = value;
                OnPropertyChanged(nameof(ParameterSeries));
            }
        }

        public ObservableCollection<Axis> XAxes
        {
            get => _xAxes;
            set
            {
                _xAxes = value;
                OnPropertyChanged(nameof(XAxes));
            }
        }

        public ObservableCollection<Axis> YAxes
        {
            get => _yAxes;
            set
            {
                _yAxes = value;
                OnPropertyChanged(nameof(YAxes));
            }
        }

        public Dictionary<string, ThresholdConfiguration> ThresholdConfigurations
        {
            get => _thresholdConfigurations;
            set
            {
                _thresholdConfigurations = value;
                OnPropertyChanged(nameof(ThresholdConfigurations));
            }
        }

        #endregion

        #region Commands

        public ICommand RefreshECUCommand { get; private set; }
        public ICommand ConnectToECUCommand { get; private set; }
        public ICommand DisconnectECUCommand { get; private set; }
        public ICommand ReadFaultsCommand { get; private set; }
        public ICommand ClearFaultsCommand { get; private set; }
        public ICommand ReadParametersCommand { get; private set; }
        public ICommand WriteParametersCommand { get; private set; }
        public ICommand RefreshParametersCommand { get; private set; }
        public ICommand ExportParametersCommand { get; private set; }
        public ICommand PerformDiagnosticsCommand { get; private set; }

        // New monitoring commands
        public ICommand StartMonitoringCommand { get; private set; }
        public ICommand StopMonitoringCommand { get; private set; }
        public ICommand ClearMonitoringDataCommand { get; private set; }
        public ICommand AddParameterToChartCommand { get; private set; }
        public ICommand RemoveParameterFromChartCommand { get; private set; }
        public ICommand ExportMonitoringDataCommand { get; private set; }
        public ICommand SaveMonitoringConfigCommand { get; private set; }
        public ICommand LoadMonitoringConfigCommand { get; private set; }

        // New diagnostic results commands
        public ICommand ExportDiagnosticResultsCommand { get; private set; }
        public ICommand PrintDiagnosticReportCommand { get; private set; }
        public ICommand SaveDiagnosticSnapshotCommand { get; private set; }

        #endregion

        #region Constructor

        public EnhancedDiagnosticsViewModel(ILoggingService loggingService, IECUCommunicationService ecuCommunicationService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _ecuCommunicationService = ecuCommunicationService ?? throw new ArgumentNullException(nameof(ecuCommunicationService));

            // Initialize collections
            ConnectedECUs = new ObservableCollection<ECUDevice>();
            ActiveFaults = new ObservableCollection<ECUFault>();
            InactiveFaults = new ObservableCollection<ECUFault>();
            Parameters = new ObservableCollection<ParameterItem>();
            MonitoringData = new ObservableCollection<MonitoringDataPoint>();
            MonitorableParameters = new ObservableCollection<string>();
            SelectedParametersForMonitoring = new List<string>();

            // Initialize chart-related collections
            ChartSeries = new ObservableCollection<ISeries>();
            ParameterSeries = new ObservableCollection<ParameterSeries>();
            XAxes = new ObservableCollection<Axis>
            {
                new Axis
                {
                    Name = "Time",
                    NamePaint = new SolidColorPaint(SKColors.Gray),
                    LabelsPaint = new SolidColorPaint(SKColors.Gray),
                    TextSize = 10,
                    SeparatorsPaint = new SolidColorPaint(SKColors.LightGray) { StrokeThickness = 0.5f }
                }
            };

            YAxes = new ObservableCollection<Axis>
            {
                new Axis
                {
                    Name = "Value",
                    NamePaint = new SolidColorPaint(SKColors.Gray),
                    LabelsPaint = new SolidColorPaint(SKColors.Gray),
                    TextSize = 10,
                    SeparatorsPaint = new SolidColorPaint(SKColors.LightGray) { StrokeThickness = 0.5f }
                }
            };

            ThresholdConfigurations = new Dictionary<string, ThresholdConfiguration>();

            // Initialize default values
            SelectedDiagnosticMode = DiagnosticModes.FirstOrDefault();
            SelectedOperatingMode = OperatingMode.Bench; // Default to Bench mode as per MC9S12XEP100 specifications
            StatusMessage = "Ready";
            OperationStatus = "Ready";
            SelectedRefreshInterval = RefreshIntervals[3]; // Default to 1s
            SelectedMaxDataPoints = MaxDataPoints[2]; // Default to 500 points
            IsMonitoring = false;

            // Initialize commands
            InitializeCommands();

            // Load connected ECUs
            LoadConnectedECUs();
        }

        #endregion

        #region Private Methods

        private void InitializeCommands()
        {
            RefreshECUCommand = new RelayCommand(
                _ => { RefreshECUListAsync().ConfigureAwait(false); },
                _ => !IsBusy);

            ConnectToECUCommand = new RelayCommand(
                _ => { ConnectToECUAsync().ConfigureAwait(false); },
                _ => !IsBusy && SelectedECUForDiagnostics != null &&
                     SelectedECUForDiagnostics.ConnectionStatus != ECUConnectionStatus.Connected);

            DisconnectECUCommand = new RelayCommand(
                _ => { DisconnectECUAsync().ConfigureAwait(false); },
                _ => !IsBusy && SelectedECUForDiagnostics != null &&
                     SelectedECUForDiagnostics.ConnectionStatus == ECUConnectionStatus.Connected);

            ReadFaultsCommand = new RelayCommand(
                _ => { ReadFaultsAsync().ConfigureAwait(false); },
                _ => !IsBusy && SelectedECUForDiagnostics != null &&
                     SelectedECUForDiagnostics.ConnectionStatus == ECUConnectionStatus.Connected);

            ClearFaultsCommand = new RelayCommand(
                _ => { ClearFaultsAsync().ConfigureAwait(false); },
                _ => !IsBusy && SelectedECUForDiagnostics != null &&
                     SelectedECUForDiagnostics.ConnectionStatus == ECUConnectionStatus.Connected);

            ReadParametersCommand = new RelayCommand(
                _ => { ReadParametersAsync().ConfigureAwait(false); },
                _ => !IsBusy && SelectedECUForDiagnostics != null &&
                     SelectedECUForDiagnostics.ConnectionStatus == ECUConnectionStatus.Connected);

            WriteParametersCommand = new RelayCommand(
                _ => { WriteParametersAsync().ConfigureAwait(false); },
                _ => !IsBusy && SelectedECUForDiagnostics != null &&
                     SelectedECUForDiagnostics.ConnectionStatus == ECUConnectionStatus.Connected &&
                     Parameters.Count > 0);

            RefreshParametersCommand = new RelayCommand(
                _ => { ReadParametersAsync().ConfigureAwait(false); },
                _ => !IsBusy && SelectedECUForDiagnostics != null &&
                     SelectedECUForDiagnostics.ConnectionStatus == ECUConnectionStatus.Connected);

            ExportParametersCommand = new RelayCommand(
                _ => { ExportParameters(); },
                _ => !IsBusy && Parameters.Count > 0);

            PerformDiagnosticsCommand = new RelayCommand(
                _ => { PerformDiagnosticsAsync().ConfigureAwait(false); },
                _ => !IsBusy && SelectedECUForDiagnostics != null &&
                     SelectedECUForDiagnostics.ConnectionStatus == ECUConnectionStatus.Connected);

            // Initialize monitoring commands
            StartMonitoringCommand = new RelayCommand(
                _ => { StartMonitoring(); },
                _ => !IsBusy && !IsMonitoring && SelectedECUForDiagnostics != null &&
                     SelectedECUForDiagnostics.ConnectionStatus == ECUConnectionStatus.Connected);

            StopMonitoringCommand = new RelayCommand(
                _ => { StopMonitoring(); },
                _ => IsMonitoring);

            ClearMonitoringDataCommand = new RelayCommand(
                _ => { ClearMonitoringData(); },
                _ => MonitoringData.Count > 0);

            AddParameterToChartCommand = new RelayCommand(
                _ => { AddParameterToChart(); },
                _ => !string.IsNullOrEmpty(SelectedParameterForChart) &&
                     !SelectedParametersForMonitoring.Contains(SelectedParameterForChart));

            RemoveParameterFromChartCommand = new RelayCommand(
                _ => { RemoveParameterFromChart(); },
                _ => !string.IsNullOrEmpty(SelectedParameterForChart) &&
                     SelectedParametersForMonitoring.Contains(SelectedParameterForChart));

            ExportMonitoringDataCommand = new RelayCommand(
                _ => { ExportMonitoringData(); },
                _ => MonitoringData.Count > 0);

            SaveMonitoringConfigCommand = new RelayCommand(
                _ => { SaveMonitoringConfiguration(); },
                _ => SelectedParametersForMonitoring.Count > 0);

            LoadMonitoringConfigCommand = new RelayCommand(
                _ => { LoadMonitoringConfiguration(); },
                _ => !IsBusy);

            // Initialize diagnostic results commands
            ExportDiagnosticResultsCommand = new RelayCommand(
                _ => { ExportDiagnosticResults(); },
                _ => DiagnosticData != null);

            PrintDiagnosticReportCommand = new RelayCommand(
                _ => { PrintDiagnosticReport(); },
                _ => DiagnosticData != null);

            SaveDiagnosticSnapshotCommand = new RelayCommand(
                _ => { SaveDiagnosticSnapshot(); },
                _ => DiagnosticData != null);
        }

        private void UpdateCommandStates()
        {
            (RefreshECUCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (ConnectToECUCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (DisconnectECUCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (ReadFaultsCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (ClearFaultsCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (ReadParametersCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (WriteParametersCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (RefreshParametersCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (ExportParametersCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (PerformDiagnosticsCommand as RelayCommand)?.RaiseCanExecuteChanged();

            // Update monitoring commands
            (StartMonitoringCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (StopMonitoringCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (ClearMonitoringDataCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (AddParameterToChartCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (RemoveParameterFromChartCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (ExportMonitoringDataCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (SaveMonitoringConfigCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (LoadMonitoringConfigCommand as RelayCommand)?.RaiseCanExecuteChanged();

            // Update diagnostic results commands
            (ExportDiagnosticResultsCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (PrintDiagnosticReportCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (SaveDiagnosticSnapshotCommand as RelayCommand)?.RaiseCanExecuteChanged();
        }

        private void LoadConnectedECUs()
        {
            ConnectedECUs.Clear();

            if (_ecuCommunicationService.ConnectedECUs != null)
            {
                foreach (var ecu in _ecuCommunicationService.ConnectedECUs)
                {
                    ConnectedECUs.Add(ecu);
                }
            }

            // Select the first ECU if available
            SelectedECUForDiagnostics = ConnectedECUs.FirstOrDefault();
        }

        private async Task RefreshECUListAsync()
        {
            try
            {
                IsBusy = true;
                StatusMessage = "Refreshing ECU list...";
                _loggingService.LogInformation("Refreshing ECU list", "EnhancedDiagnosticsViewModel");

                // Refresh the ECU list
                await _ecuCommunicationService.ScanForECUsAsync();

                // Update the connected ECUs list
                LoadConnectedECUs();

                StatusMessage = "ECU list refreshed";
                _loggingService.LogInformation("ECU list refreshed", "EnhancedDiagnosticsViewModel");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error refreshing ECU list: {ex.Message}";
                _loggingService.LogError("Error refreshing ECU list", "EnhancedDiagnosticsViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task ConnectToECUAsync()
        {
            if (SelectedECUForDiagnostics == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            try
            {
                IsBusy = true;
                StatusMessage = $"Connecting to ECU {SelectedECUForDiagnostics.Name}...";
                _loggingService.LogInformation($"Connecting to ECU {SelectedECUForDiagnostics.Name}", "EnhancedDiagnosticsViewModel");

                // Connect to the selected ECU
                bool connected = await _ecuCommunicationService.ConnectToECUAsync(SelectedECUForDiagnostics);

                if (connected)
                {
                    StatusMessage = $"Connected to ECU {SelectedECUForDiagnostics.Name}";
                    _loggingService.LogInformation($"Connected to ECU {SelectedECUForDiagnostics.Name}", "EnhancedDiagnosticsViewModel");

                    // Refresh the connected ECUs list
                    LoadConnectedECUs();
                }
                else
                {
                    StatusMessage = $"Failed to connect to ECU {SelectedECUForDiagnostics.Name}";
                    _loggingService.LogError($"Failed to connect to ECU {SelectedECUForDiagnostics.Name}", "EnhancedDiagnosticsViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error connecting to ECU: {ex.Message}";
                _loggingService.LogError("Error connecting to ECU", "EnhancedDiagnosticsViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task DisconnectECUAsync()
        {
            if (SelectedECUForDiagnostics == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            try
            {
                IsBusy = true;
                StatusMessage = $"Disconnecting from ECU {SelectedECUForDiagnostics.Name}...";
                _loggingService.LogInformation($"Disconnecting from ECU {SelectedECUForDiagnostics.Name}", "EnhancedDiagnosticsViewModel");

                // Disconnect from the selected ECU
                bool disconnected = await _ecuCommunicationService.DisconnectFromECUAsync(SelectedECUForDiagnostics);

                if (disconnected)
                {
                    StatusMessage = $"Disconnected from ECU {SelectedECUForDiagnostics.Name}";
                    _loggingService.LogInformation($"Disconnected from ECU {SelectedECUForDiagnostics.Name}", "EnhancedDiagnosticsViewModel");

                    // Refresh the connected ECUs list
                    LoadConnectedECUs();
                }
                else
                {
                    StatusMessage = $"Failed to disconnect from ECU {SelectedECUForDiagnostics.Name}";
                    _loggingService.LogError($"Failed to disconnect from ECU {SelectedECUForDiagnostics.Name}", "EnhancedDiagnosticsViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error disconnecting from ECU: {ex.Message}";
                _loggingService.LogError("Error disconnecting from ECU", "EnhancedDiagnosticsViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task SetOperatingModeAsync()
        {
            if (SelectedECUForDiagnostics == null ||
                SelectedECUForDiagnostics.ConnectionStatus != ECUConnectionStatus.Connected)
            {
                return;
            }

            try
            {
                IsBusy = true;
                StatusMessage = $"Setting operating mode to {SelectedOperatingMode}...";
                _loggingService.LogInformation($"Setting operating mode to {SelectedOperatingMode}", "EnhancedDiagnosticsViewModel");

                // Set the operating mode
                bool success = await _ecuCommunicationService.SetOperatingModeAsync(SelectedOperatingMode);

                if (success)
                {
                    StatusMessage = $"Operating mode set to {SelectedOperatingMode}";
                    _loggingService.LogInformation($"Operating mode set to {SelectedOperatingMode}", "EnhancedDiagnosticsViewModel");
                }
                else
                {
                    StatusMessage = $"Failed to set operating mode to {SelectedOperatingMode}";
                    _loggingService.LogError($"Failed to set operating mode to {SelectedOperatingMode}", "EnhancedDiagnosticsViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error setting operating mode: {ex.Message}";
                _loggingService.LogError("Error setting operating mode", "EnhancedDiagnosticsViewModel", ex);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task ReadFaultsAsync()
        {
            if (SelectedECUForDiagnostics == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            try
            {
                IsBusy = true;
                OperationStatus = "In Progress";
                OperationProgress = 0;
                StatusMessage = $"Reading faults from ECU {SelectedECUForDiagnostics.Name}...";
                _loggingService.LogInformation($"Reading faults from ECU {SelectedECUForDiagnostics.Name}", "EnhancedDiagnosticsViewModel");

                // Clear existing faults
                ActiveFaults.Clear();
                InactiveFaults.Clear();

                // Read faults with progress reporting
                var progress = new Progress<int>(percent =>
                {
                    OperationProgress = percent;
                    StatusMessage = $"Reading faults: {percent}% complete";
                });

                // Read active faults
                var activeFaults = await _ecuCommunicationService.ReadActiveFaultsAsync(SelectedECUForDiagnostics);

                // Read inactive faults
                var inactiveFaults = await _ecuCommunicationService.ReadInactiveFaultsAsync(SelectedECUForDiagnostics);

                // Update collections
                if (activeFaults != null)
                {
                    foreach (var fault in activeFaults)
                    {
                        ActiveFaults.Add(fault);
                    }
                }

                if (inactiveFaults != null)
                {
                    foreach (var fault in inactiveFaults)
                    {
                        InactiveFaults.Add(fault);
                    }
                }

                StatusMessage = $"Faults read successfully: {ActiveFaults.Count} active, {InactiveFaults.Count} inactive";
                _loggingService.LogInformation($"Faults read successfully: {ActiveFaults.Count} active, {InactiveFaults.Count} inactive", "EnhancedDiagnosticsViewModel");
                OperationStatus = "Completed";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error reading faults: {ex.Message}";
                _loggingService.LogError("Error reading faults", "EnhancedDiagnosticsViewModel", ex);
                OperationStatus = "Error";
            }
            finally
            {
                IsBusy = false;
                OperationProgress = 100;
            }
        }

        private async Task ClearFaultsAsync()
        {
            if (SelectedECUForDiagnostics == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            try
            {
                IsBusy = true;
                OperationStatus = "In Progress";
                OperationProgress = 0;
                StatusMessage = $"Clearing faults from ECU {SelectedECUForDiagnostics.Name}...";
                _loggingService.LogInformation($"Clearing faults from ECU {SelectedECUForDiagnostics.Name}", "EnhancedDiagnosticsViewModel");

                // Clear faults with progress reporting
                var progress = new Progress<int>(percent =>
                {
                    OperationProgress = percent;
                    StatusMessage = $"Clearing faults: {percent}% complete";
                });

                // Clear faults
                bool success = await _ecuCommunicationService.ClearFaultsAsync(SelectedECUForDiagnostics);

                if (success)
                {
                    // Clear the fault collections
                    ActiveFaults.Clear();
                    InactiveFaults.Clear();

                    StatusMessage = "Faults cleared successfully";
                    _loggingService.LogInformation("Faults cleared successfully", "EnhancedDiagnosticsViewModel");
                    OperationStatus = "Completed";
                }
                else
                {
                    StatusMessage = "Failed to clear faults";
                    _loggingService.LogError("Failed to clear faults", "EnhancedDiagnosticsViewModel");
                    OperationStatus = "Error";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error clearing faults: {ex.Message}";
                _loggingService.LogError("Error clearing faults", "EnhancedDiagnosticsViewModel", ex);
                OperationStatus = "Error";
            }
            finally
            {
                IsBusy = false;
                OperationProgress = 100;
            }
        }

        private async Task ReadParametersAsync()
        {
            if (SelectedECUForDiagnostics == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            try
            {
                IsBusy = true;
                OperationStatus = "In Progress";
                OperationProgress = 0;
                StatusMessage = $"Reading parameters from ECU {SelectedECUForDiagnostics.Name}...";
                _loggingService.LogInformation($"Reading parameters from ECU {SelectedECUForDiagnostics.Name}", "EnhancedDiagnosticsViewModel");

                // Clear existing parameters
                Parameters.Clear();

                // Read parameters with progress reporting
                var progress = new Progress<int>(percent =>
                {
                    OperationProgress = percent;
                    StatusMessage = $"Reading parameters: {percent}% complete";
                });

                // Read parameters
                var parameters = await _ecuCommunicationService.ReadParametersAsync(SelectedECUForDiagnostics);

                if (parameters != null && parameters.Count > 0)
                {
                    // Add parameters to the collection
                    foreach (var param in parameters)
                    {
                        Parameters.Add(ParameterItem.Create(
                            param.Key,
                            param.Value,
                            GetUnitForParameter(param.Key),
                            GetDescriptionForParameter(param.Key)));
                    }

                    StatusMessage = $"Parameters read successfully: {Parameters.Count} parameters";
                    _loggingService.LogInformation($"Parameters read successfully: {Parameters.Count} parameters", "EnhancedDiagnosticsViewModel");
                    OperationStatus = "Completed";
                }
                else
                {
                    StatusMessage = "No parameters found";
                    _loggingService.LogWarning("No parameters found", "EnhancedDiagnosticsViewModel");
                    OperationStatus = "Completed";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error reading parameters: {ex.Message}";
                _loggingService.LogError("Error reading parameters", "EnhancedDiagnosticsViewModel", ex);
                OperationStatus = "Error";
            }
            finally
            {
                IsBusy = false;
                OperationProgress = 100;
            }
        }

        private async Task WriteParametersAsync()
        {
            if (SelectedECUForDiagnostics == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            try
            {
                IsBusy = true;
                OperationStatus = "In Progress";
                OperationProgress = 0;
                StatusMessage = $"Writing parameters to ECU {SelectedECUForDiagnostics.Name}...";
                _loggingService.LogInformation($"Writing parameters to ECU {SelectedECUForDiagnostics.Name}", "EnhancedDiagnosticsViewModel");

                // Convert parameters to dictionary
                var parameterDict = new Dictionary<string, object>();
                foreach (var param in Parameters)
                {
                    parameterDict[param.Key] = param.Value;
                }

                // Write parameters with progress reporting
                var progress = new Progress<int>(percent =>
                {
                    OperationProgress = percent;
                    StatusMessage = $"Writing parameters: {percent}% complete";
                });

                // Write parameters
                bool success = await _ecuCommunicationService.WriteParametersAsync(SelectedECUForDiagnostics, parameterDict);

                if (success)
                {
                    StatusMessage = "Parameters written successfully";
                    _loggingService.LogInformation("Parameters written successfully", "EnhancedDiagnosticsViewModel");
                    OperationStatus = "Completed";
                }
                else
                {
                    StatusMessage = "Failed to write parameters";
                    _loggingService.LogError("Failed to write parameters", "EnhancedDiagnosticsViewModel");
                    OperationStatus = "Error";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error writing parameters: {ex.Message}";
                _loggingService.LogError("Error writing parameters", "EnhancedDiagnosticsViewModel", ex);
                OperationStatus = "Error";
            }
            finally
            {
                IsBusy = false;
                OperationProgress = 100;
            }
        }

        private void ExportParameters()
        {
            try
            {
                // This would be implemented to export parameters to a file
                // For now, just log the action
                StatusMessage = "Exporting parameters...";
                _loggingService.LogInformation("Exporting parameters", "EnhancedDiagnosticsViewModel");

                // TODO: Implement parameter export functionality

                StatusMessage = "Parameters exported successfully";
                _loggingService.LogInformation("Parameters exported successfully", "EnhancedDiagnosticsViewModel");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error exporting parameters: {ex.Message}";
                _loggingService.LogError("Error exporting parameters", "EnhancedDiagnosticsViewModel", ex);
            }
        }

        private async Task PerformDiagnosticsAsync()
        {
            if (SelectedECUForDiagnostics == null)
            {
                StatusMessage = "No ECU selected";
                return;
            }

            try
            {
                IsBusy = true;
                OperationStatus = "In Progress";
                OperationProgress = 0;
                StatusMessage = $"Performing diagnostics on ECU {SelectedECUForDiagnostics.Name}...";
                _loggingService.LogInformation($"Performing diagnostics on ECU {SelectedECUForDiagnostics.Name}", "EnhancedDiagnosticsViewModel");

                // Perform diagnostics with progress reporting
                var progress = new Progress<int>(percent =>
                {
                    OperationProgress = percent;
                    StatusMessage = $"Performing diagnostics: {percent}% complete";
                });

                // Perform diagnostics
                DiagnosticData = await _ecuCommunicationService.PerformDiagnosticSessionAsync(SelectedECUForDiagnostics);

                if (DiagnosticData != null && DiagnosticData.IsSuccessful)
                {
                    // Update fault collections
                    ActiveFaults.Clear();
                    InactiveFaults.Clear();

                    if (DiagnosticData.ActiveFaults != null)
                    {
                        foreach (var fault in DiagnosticData.ActiveFaults)
                        {
                            ActiveFaults.Add(fault);
                        }
                    }

                    if (DiagnosticData.InactiveFaults != null)
                    {
                        foreach (var fault in DiagnosticData.InactiveFaults)
                        {
                            InactiveFaults.Add(fault);
                        }
                    }

                    // Update parameters
                    Parameters.Clear();
                    if (DiagnosticData.Parameters != null)
                    {
                        foreach (var param in DiagnosticData.Parameters)
                        {
                            Parameters.Add(ParameterItem.Create(
                                param.Key,
                                param.Value,
                                GetUnitForParameter(param.Key),
                                GetDescriptionForParameter(param.Key)));
                        }
                    }

                    StatusMessage = "Diagnostics completed successfully";
                    _loggingService.LogInformation("Diagnostics completed successfully", "EnhancedDiagnosticsViewModel");
                    OperationStatus = "Completed";
                }
                else
                {
                    StatusMessage = "Diagnostics failed";
                    _loggingService.LogError("Diagnostics failed", "EnhancedDiagnosticsViewModel");
                    OperationStatus = "Error";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error performing diagnostics: {ex.Message}";
                _loggingService.LogError("Error performing diagnostics", "EnhancedDiagnosticsViewModel", ex);
                OperationStatus = "Error";
            }
            finally
            {
                IsBusy = false;
                OperationProgress = 100;
            }
        }

        private string GetUnitForParameter(string parameterName)
        {
            // This would be implemented to get the unit for a parameter
            // For now, return some sample units based on parameter name
            if (parameterName.Contains("Temp"))
                return "°C";
            if (parameterName.Contains("Speed"))
                return "km/h";
            if (parameterName.Contains("RPM"))
                return "rpm";
            if (parameterName.Contains("Pressure"))
                return "kPa";
            if (parameterName.Contains("Voltage"))
                return "V";
            if (parameterName.Contains("Current"))
                return "A";
            if (parameterName.Contains("Fuel"))
                return "%";
            if (parameterName.Contains("Position"))
                return "%";

            return "";
        }

        private string GetDescriptionForParameter(string parameterName)
        {
            // This would be implemented to get the description for a parameter
            // For now, return a generic description
            return $"Description for {parameterName}";
        }

        #region Monitoring Methods

        private void UpdateMonitoringInterval()
        {
            if (_monitoringTimer != null && IsMonitoring)
            {
                // Stop the current timer
                _monitoringTimer.Dispose();

                // Create a new timer with the updated interval
                int interval = ParseRefreshInterval(SelectedRefreshInterval);
                _monitoringTimer = new Timer(MonitoringTimerCallback, null, 0, interval);

                _loggingService.LogInformation($"Monitoring interval updated to {SelectedRefreshInterval}", "EnhancedDiagnosticsViewModel");
            }
        }

        private int ParseRefreshInterval(string refreshInterval)
        {
            // Parse the refresh interval string to milliseconds
            if (string.IsNullOrEmpty(refreshInterval))
                return 1000; // Default to 1 second

            if (refreshInterval.EndsWith("ms"))
            {
                if (int.TryParse(refreshInterval.Replace("ms", ""), out int ms))
                    return ms;
            }
            else if (refreshInterval.EndsWith("s"))
            {
                if (int.TryParse(refreshInterval.Replace("s", ""), out int s))
                    return s * 1000;
            }

            return 1000; // Default to 1 second
        }

        private void StartMonitoring()
        {
            if (IsMonitoring || SelectedECUForDiagnostics == null ||
                SelectedECUForDiagnostics.ConnectionStatus != ECUConnectionStatus.Connected)
                return;

            try
            {
                _loggingService.LogInformation("Starting parameter monitoring", "EnhancedDiagnosticsViewModel");

                // Initialize monitoring data if needed
                if (MonitorableParameters.Count == 0)
                {
                    InitializeMonitorableParameters();
                }

                // Create cancellation token source
                _monitoringCancellationTokenSource = new CancellationTokenSource();

                // Create and start the timer
                int interval = ParseRefreshInterval(SelectedRefreshInterval);
                _monitoringTimer = new Timer(MonitoringTimerCallback, null, 0, interval);

                IsMonitoring = true;
                StatusMessage = "Monitoring started";
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error starting monitoring", "EnhancedDiagnosticsViewModel", ex);
                StatusMessage = $"Error starting monitoring: {ex.Message}";
            }
        }

        private void StopMonitoring()
        {
            if (!IsMonitoring)
                return;

            try
            {
                _loggingService.LogInformation("Stopping parameter monitoring", "EnhancedDiagnosticsViewModel");

                // Stop and dispose the timer
                _monitoringTimer?.Dispose();
                _monitoringTimer = null;

                // Cancel any ongoing operations
                _monitoringCancellationTokenSource?.Cancel();
                _monitoringCancellationTokenSource?.Dispose();
                _monitoringCancellationTokenSource = null;

                IsMonitoring = false;
                StatusMessage = "Monitoring stopped";
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error stopping monitoring", "EnhancedDiagnosticsViewModel", ex);
                StatusMessage = $"Error stopping monitoring: {ex.Message}";
            }
        }

        private void ClearMonitoringData()
        {
            // Clear monitoring data
            MonitoringData.Clear();

            // Clear chart data
            foreach (var series in ChartSeries)
            {
                if (series is LineSeries<ObservablePoint> lineSeries)
                {
                    var values = lineSeries.Values as ObservableCollection<ObservablePoint>;
                    values?.Clear();
                }
            }

            // Clear parameter series data
            foreach (var series in ParameterSeries)
            {
                series.ClearDataPoints();
            }

            StatusMessage = "Monitoring data cleared";
            _loggingService.LogInformation("Monitoring data cleared", "EnhancedDiagnosticsViewModel");
        }

        private void AddParameterToChart()
        {
            if (string.IsNullOrEmpty(SelectedParameterForChart) ||
                SelectedParametersForMonitoring.Contains(SelectedParameterForChart))
                return;

            // Add to monitoring list
            SelectedParametersForMonitoring.Add(SelectedParameterForChart);

            // Create a new parameter series
            var paramSeries = new ParameterSeries(SelectedParameterForChart, GetUnitForParameter(SelectedParameterForChart));

            // Assign a color based on the number of series
            paramSeries.Color = GetColorForSeries(ParameterSeries.Count);

            // Add to parameter series collection
            ParameterSeries.Add(paramSeries);

            // Create a LiveCharts series
            var lineSeries = new LineSeries<ObservablePoint>
            {
                Name = SelectedParameterForChart,
                Values = new ObservableCollection<ObservablePoint>(),
                Stroke = new SolidColorPaint(SKColor.Parse(paramSeries.Color)) { StrokeThickness = 2 },
                GeometrySize = 5,
                GeometryStroke = new SolidColorPaint(SKColor.Parse(paramSeries.Color)) { StrokeThickness = 2 },
                Fill = null,
                LineSmoothness = 0.2
            };

            // Add to chart series collection
            ChartSeries.Add(lineSeries);

            // Create default threshold configuration if it doesn't exist
            if (!ThresholdConfigurations.ContainsKey(SelectedParameterForChart))
            {
                var thresholdConfig = new ThresholdConfiguration(SelectedParameterForChart);
                thresholdConfig.SetNormalRange(0, 100);
                thresholdConfig.SetWarningRange(-10, 110);
                thresholdConfig.SetErrorRange(-20, 120);
                ThresholdConfigurations[SelectedParameterForChart] = thresholdConfig;
            }

            _loggingService.LogInformation($"Parameter '{SelectedParameterForChart}' added to monitoring", "EnhancedDiagnosticsViewModel");
            UpdateCommandStates();
        }

        private void RemoveParameterFromChart()
        {
            if (string.IsNullOrEmpty(SelectedParameterForChart) ||
                !SelectedParametersForMonitoring.Contains(SelectedParameterForChart))
                return;

            // Remove from monitoring list
            SelectedParametersForMonitoring.Remove(SelectedParameterForChart);

            // Remove from parameter series collection
            var paramSeries = ParameterSeries.FirstOrDefault(p => p.ParameterName == SelectedParameterForChart);
            if (paramSeries != null)
            {
                ParameterSeries.Remove(paramSeries);
            }

            // Remove from chart series collection
            var chartSeries = ChartSeries.FirstOrDefault(s => s.Name == SelectedParameterForChart);
            if (chartSeries != null)
            {
                ChartSeries.Remove(chartSeries);
            }

            _loggingService.LogInformation($"Parameter '{SelectedParameterForChart}' removed from monitoring", "EnhancedDiagnosticsViewModel");
            UpdateCommandStates();
        }

        private string GetColorForSeries(int index)
        {
            // Define a list of colors for the series
            var colors = new List<string>
            {
                "#1E88E5", // Blue
                "#E53935", // Red
                "#43A047", // Green
                "#FB8C00", // Orange
                "#8E24AA", // Purple
                "#00ACC1", // Cyan
                "#FFB300", // Amber
                "#5E35B1", // Deep Purple
                "#1E88E5", // Blue
                "#E53935"  // Red
            };

            // Return a color based on the index (cycle through the colors if needed)
            return colors[index % colors.Count];
        }

        private void InitializeMonitorableParameters()
        {
            MonitorableParameters.Clear();

            if (SelectedECUForDiagnostics?.Parameters != null)
            {
                foreach (var param in SelectedECUForDiagnostics.Parameters)
                {
                    MonitorableParameters.Add(param.Key);
                }
            }

            _loggingService.LogInformation($"Initialized {MonitorableParameters.Count} monitorable parameters", "EnhancedDiagnosticsViewModel");
        }

        private async void MonitoringTimerCallback(object state)
        {
            if (!IsMonitoring || SelectedECUForDiagnostics == null ||
                SelectedECUForDiagnostics.ConnectionStatus != ECUConnectionStatus.Connected ||
                SelectedParametersForMonitoring.Count == 0)
                return;

            try
            {
                // Read the current parameter values
                var parameters = await _ecuCommunicationService.ReadParametersAsync(SelectedECUForDiagnostics);

                if (parameters != null)
                {
                    // Create data points for the selected parameters
                    var timestamp = DateTime.Now;

                    foreach (var paramName in SelectedParametersForMonitoring)
                    {
                        if (parameters.TryGetValue(paramName, out object value))
                        {
                            // Try to parse the value as double for the chart
                            if (double.TryParse(value?.ToString(), out double doubleValue))
                            {
                                // Get the parameter status based on thresholds
                                string status = "Normal";
                                if (ThresholdConfigurations.TryGetValue(paramName, out ThresholdConfiguration thresholdConfig))
                                {
                                    status = thresholdConfig.GetStatus(doubleValue);
                                }

                                // Create a new data point
                                var dataPoint = new MonitoringDataPoint
                                {
                                    Timestamp = timestamp,
                                    ParameterName = paramName,
                                    Value = doubleValue.ToString("F2"),
                                    Unit = GetUnitForParameter(paramName),
                                    Status = status
                                };

                                // Calculate min, max, average
                                CalculateStatistics(dataPoint);

                                // Add to the collection (on UI thread)
                                App.Current.Dispatcher.Invoke(() =>
                                {
                                    MonitoringData.Add(dataPoint);

                                    // Limit the number of data points
                                    while (MonitoringData.Count > SelectedMaxDataPoints)
                                    {
                                        MonitoringData.RemoveAt(0);
                                    }

                                    // Update the chart
                                    UpdateChart(paramName, timestamp, doubleValue);
                                });
                            }
                            else
                            {
                                // Handle non-numeric values
                                var dataPoint = new MonitoringDataPoint
                                {
                                    Timestamp = timestamp,
                                    ParameterName = paramName,
                                    Value = value?.ToString() ?? "N/A",
                                    Unit = GetUnitForParameter(paramName),
                                    Status = "Unknown"
                                };

                                // Add to the collection (on UI thread)
                                App.Current.Dispatcher.Invoke(() =>
                                {
                                    MonitoringData.Add(dataPoint);

                                    // Limit the number of data points
                                    while (MonitoringData.Count > SelectedMaxDataPoints)
                                    {
                                        MonitoringData.RemoveAt(0);
                                    }
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error during monitoring", "EnhancedDiagnosticsViewModel", ex);

                // Update status on UI thread
                App.Current.Dispatcher.Invoke(() =>
                {
                    StatusMessage = $"Monitoring error: {ex.Message}";
                });
            }
        }

        private void UpdateChart(string parameterName, DateTime timestamp, double value)
        {
            // Find the chart series for this parameter
            var series = ChartSeries.FirstOrDefault(s => s.Name == parameterName) as LineSeries<ObservablePoint>;
            if (series != null)
            {
                // Add the data point to the series
                var values = series.Values as ObservableCollection<ObservablePoint>;
                if (values != null)
                {
                    // Convert timestamp to seconds since start for X-axis
                    double seconds = (timestamp - DateTime.Now.Date).TotalSeconds;
                    values.Add(new ObservablePoint(seconds, value));

                    // Limit the number of points in the chart
                    while (values.Count > SelectedMaxDataPoints)
                    {
                        values.RemoveAt(0);
                    }
                }
            }

            // Find the parameter series for this parameter
            var paramSeries = ParameterSeries.FirstOrDefault(p => p.ParameterName == parameterName);
            if (paramSeries != null)
            {
                // Add the data point to the parameter series
                paramSeries.AddDataPoint(timestamp, value);

                // Limit the number of data points
                paramSeries.LimitDataPoints(SelectedMaxDataPoints);
            }
        }

        private void CalculateStatistics(MonitoringDataPoint dataPoint)
        {
            // Get all data points for this parameter
            var parameterData = MonitoringData.Where(p => p.ParameterName == dataPoint.ParameterName).ToList();

            if (parameterData.Count > 0)
            {
                // Try to parse values as double for calculations
                var values = new List<double>();

                foreach (var point in parameterData)
                {
                    if (double.TryParse(point.Value, out double val))
                    {
                        values.Add(val);
                    }
                }

                if (values.Count > 0)
                {
                    // Calculate min, max, average
                    double min = values.Min();
                    double max = values.Max();
                    double avg = values.Average();

                    dataPoint.MinValue = min.ToString("F2");
                    dataPoint.MaxValue = max.ToString("F2");
                    dataPoint.AverageValue = avg.ToString("F2");
                }
            }
        }

        private void ExportMonitoringData()
        {
            if (MonitoringData.Count == 0)
                return;

            try
            {
                // Create a save file dialog
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "CSV Files (*.csv)|*.csv|All Files (*.*)|*.*",
                    DefaultExt = "csv",
                    Title = "Export Monitoring Data",
                    FileName = $"ECU_Monitoring_{SelectedECUForDiagnostics?.Name}_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // Export the data to CSV
                    using (var writer = new StreamWriter(saveFileDialog.FileName))
                    {
                        // Write header
                        writer.WriteLine("Timestamp,Parameter,Value,Unit,Min,Max,Average,Status");

                        // Write data
                        foreach (var dataPoint in MonitoringData)
                        {
                            writer.WriteLine($"{dataPoint.Timestamp:yyyy-MM-dd HH:mm:ss.fff},{dataPoint.ParameterName},{dataPoint.Value},{dataPoint.Unit},{dataPoint.MinValue},{dataPoint.MaxValue},{dataPoint.AverageValue},{dataPoint.Status}");
                        }
                    }

                    StatusMessage = $"Monitoring data exported to {saveFileDialog.FileName}";
                    _loggingService.LogInformation($"Monitoring data exported to {saveFileDialog.FileName}", "EnhancedDiagnosticsViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error exporting monitoring data: {ex.Message}";
                _loggingService.LogError("Error exporting monitoring data", "EnhancedDiagnosticsViewModel", ex);
            }
        }

        private void SaveMonitoringConfiguration()
        {
            if (SelectedParametersForMonitoring.Count == 0)
                return;

            try
            {
                // Create a save file dialog
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "JSON Files (*.json)|*.json|All Files (*.*)|*.*",
                    DefaultExt = "json",
                    Title = "Save Monitoring Configuration",
                    FileName = $"MonitoringConfig_{SelectedECUForDiagnostics?.Name}_{DateTime.Now:yyyyMMdd}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // Create configuration object
                    var config = new
                    {
                        ECUName = SelectedECUForDiagnostics?.Name,
                        RefreshInterval = SelectedRefreshInterval,
                        MaxDataPoints = SelectedMaxDataPoints,
                        Parameters = SelectedParametersForMonitoring
                    };

                    // Serialize to JSON
                    string json = System.Text.Json.JsonSerializer.Serialize(config, new System.Text.Json.JsonSerializerOptions { WriteIndented = true });

                    // Write to file
                    File.WriteAllText(saveFileDialog.FileName, json);

                    StatusMessage = $"Monitoring configuration saved to {saveFileDialog.FileName}";
                    _loggingService.LogInformation($"Monitoring configuration saved to {saveFileDialog.FileName}", "EnhancedDiagnosticsViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error saving monitoring configuration: {ex.Message}";
                _loggingService.LogError("Error saving monitoring configuration", "EnhancedDiagnosticsViewModel", ex);
            }
        }

        private void LoadMonitoringConfiguration()
        {
            try
            {
                // Create an open file dialog
                var openFileDialog = new OpenFileDialog
                {
                    Filter = "JSON Files (*.json)|*.json|All Files (*.*)|*.*",
                    Title = "Load Monitoring Configuration"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    // Read the JSON file
                    string json = File.ReadAllText(openFileDialog.FileName);

                    // Deserialize the configuration
                    var config = System.Text.Json.JsonSerializer.Deserialize<MonitoringConfig>(json);

                    if (config != null)
                    {
                        // Apply the configuration
                        if (RefreshIntervals.Contains(config.RefreshInterval))
                            SelectedRefreshInterval = config.RefreshInterval;

                        if (MaxDataPoints.Contains(config.MaxDataPoints))
                            SelectedMaxDataPoints = config.MaxDataPoints;

                        // Update selected parameters
                        SelectedParametersForMonitoring.Clear();
                        foreach (var param in config.Parameters)
                        {
                            if (MonitorableParameters.Contains(param))
                                SelectedParametersForMonitoring.Add(param);
                        }

                        StatusMessage = $"Monitoring configuration loaded from {openFileDialog.FileName}";
                        _loggingService.LogInformation($"Monitoring configuration loaded from {openFileDialog.FileName}", "EnhancedDiagnosticsViewModel");
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading monitoring configuration: {ex.Message}";
                _loggingService.LogError("Error loading monitoring configuration", "EnhancedDiagnosticsViewModel", ex);
            }
        }

        #endregion

        #region Diagnostic Results Methods

        private void ExportDiagnosticResults()
        {
            if (DiagnosticData == null)
                return;

            try
            {
                // Create a save file dialog
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "CSV Files (*.csv)|*.csv|All Files (*.*)|*.*",
                    DefaultExt = "csv",
                    Title = "Export Diagnostic Results",
                    FileName = $"ECU_Diagnostics_{SelectedECUForDiagnostics?.Name}_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // Export the data to CSV
                    using (var writer = new StreamWriter(saveFileDialog.FileName))
                    {
                        // Write header and summary
                        writer.WriteLine("ECU Diagnostic Results");
                        writer.WriteLine($"ECU Name: {DiagnosticData.ECUName}");
                        writer.WriteLine($"Timestamp: {DiagnosticData.Timestamp:yyyy-MM-dd HH:mm:ss}");
                        writer.WriteLine($"Session Duration: {DiagnosticData.SessionDurationMs} ms");
                        writer.WriteLine($"Status: {(DiagnosticData.IsSuccessful ? "Success" : "Failed")}");
                        writer.WriteLine($"Operating Mode: {DiagnosticData.OperatingMode}");
                        writer.WriteLine($"Connection Type: {DiagnosticData.ConnectionType}");
                        writer.WriteLine($"Active Faults: {DiagnosticData.ActiveFaults?.Count ?? 0}");
                        writer.WriteLine($"Inactive Faults: {DiagnosticData.InactiveFaults?.Count ?? 0}");
                        writer.WriteLine();

                        // Write active faults
                        if (DiagnosticData.ActiveFaults != null && DiagnosticData.ActiveFaults.Count > 0)
                        {
                            writer.WriteLine("Active Faults");
                            writer.WriteLine("Code,Description,Severity,Timestamp");

                            foreach (var fault in DiagnosticData.ActiveFaults)
                            {
                                writer.WriteLine($"{fault.Code},{fault.Description},{fault.Severity},{fault.Timestamp:yyyy-MM-dd HH:mm:ss}");
                            }

                            writer.WriteLine();
                        }

                        // Write inactive faults
                        if (DiagnosticData.InactiveFaults != null && DiagnosticData.InactiveFaults.Count > 0)
                        {
                            writer.WriteLine("Inactive Faults");
                            writer.WriteLine("Code,Description,Severity,Timestamp");

                            foreach (var fault in DiagnosticData.InactiveFaults)
                            {
                                writer.WriteLine($"{fault.Code},{fault.Description},{fault.Severity},{fault.Timestamp:yyyy-MM-dd HH:mm:ss}");
                            }

                            writer.WriteLine();
                        }

                        // Write parameters
                        if (DiagnosticData.Parameters != null && DiagnosticData.Parameters.Count > 0)
                        {
                            writer.WriteLine("Parameters");
                            writer.WriteLine("Parameter,Value");

                            foreach (var param in DiagnosticData.Parameters)
                            {
                                writer.WriteLine($"{param.Key},{param.Value}");
                            }
                        }
                    }

                    StatusMessage = $"Diagnostic results exported to {saveFileDialog.FileName}";
                    _loggingService.LogInformation($"Diagnostic results exported to {saveFileDialog.FileName}", "EnhancedDiagnosticsViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error exporting diagnostic results: {ex.Message}";
                _loggingService.LogError("Error exporting diagnostic results", "EnhancedDiagnosticsViewModel", ex);
            }
        }

        private void PrintDiagnosticReport()
        {
            if (DiagnosticData == null)
                return;

            try
            {
                // This would be implemented to print the diagnostic report
                // For now, just log the action
                StatusMessage = "Printing diagnostic report...";
                _loggingService.LogInformation("Printing diagnostic report", "EnhancedDiagnosticsViewModel");

                // TODO: Implement printing functionality

                StatusMessage = "Diagnostic report sent to printer";
                _loggingService.LogInformation("Diagnostic report sent to printer", "EnhancedDiagnosticsViewModel");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error printing diagnostic report: {ex.Message}";
                _loggingService.LogError("Error printing diagnostic report", "EnhancedDiagnosticsViewModel", ex);
            }
        }

        private void SaveDiagnosticSnapshot()
        {
            if (DiagnosticData == null)
                return;

            try
            {
                // Create a save file dialog
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "JSON Files (*.json)|*.json|All Files (*.*)|*.*",
                    DefaultExt = "json",
                    Title = "Save Diagnostic Snapshot",
                    FileName = $"ECU_Snapshot_{SelectedECUForDiagnostics?.Name}_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // Create snapshot object
                    var snapshot = new
                    {
                        ECUName = DiagnosticData.ECUName,
                        Timestamp = DiagnosticData.Timestamp,
                        SessionDurationMs = DiagnosticData.SessionDurationMs,
                        IsSuccessful = DiagnosticData.IsSuccessful,
                        OperatingMode = DiagnosticData.OperatingMode.ToString(),
                        ConnectionType = DiagnosticData.ConnectionType,
                        ActiveFaults = DiagnosticData.ActiveFaults?.Select(f => new
                        {
                            Code = f.Code,
                            Description = f.Description,
                            Severity = f.Severity.ToString(),
                            Timestamp = f.Timestamp
                        }).ToList(),
                        InactiveFaults = DiagnosticData.InactiveFaults?.Select(f => new
                        {
                            Code = f.Code,
                            Description = f.Description,
                            Severity = f.Severity.ToString(),
                            Timestamp = f.Timestamp
                        }).ToList(),
                        Parameters = DiagnosticData.Parameters
                    };

                    // Serialize to JSON
                    string json = System.Text.Json.JsonSerializer.Serialize(snapshot, new System.Text.Json.JsonSerializerOptions { WriteIndented = true });

                    // Write to file
                    File.WriteAllText(saveFileDialog.FileName, json);

                    StatusMessage = $"Diagnostic snapshot saved to {saveFileDialog.FileName}";
                    _loggingService.LogInformation($"Diagnostic snapshot saved to {saveFileDialog.FileName}", "EnhancedDiagnosticsViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error saving diagnostic snapshot: {ex.Message}";
                _loggingService.LogError("Error saving diagnostic snapshot", "EnhancedDiagnosticsViewModel", ex);
            }
        }

        #endregion

        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}

