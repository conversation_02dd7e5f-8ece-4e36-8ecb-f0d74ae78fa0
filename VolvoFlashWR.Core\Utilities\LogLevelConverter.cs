using System;
using CoreLogLevel = VolvoFlashWR.Core.Interfaces.LogLevel;
using ModelLogLevel = VolvoFlashWR.Core.Models.LogLevel;

namespace VolvoFlashWR.Core.Utilities
{
    /// <summary>
    /// Utility class for converting between different LogLevel enums
    /// </summary>
    public static class LogLevelConverter
    {
        /// <summary>
        /// Converts from Core.Interfaces.LogLevel to Core.Models.LogLevel
        /// </summary>
        /// <param name="logLevel">The Core.Interfaces.LogLevel to convert</param>
        /// <returns>The equivalent Core.Models.LogLevel</returns>
        public static ModelLogLevel ToModelLogLevel(CoreLogLevel logLevel)
        {
            switch (logLevel)
            {
                case CoreLogLevel.Debug:
                    return ModelLogLevel.Debug;
                case CoreLogLevel.Information:
                    return ModelLogLevel.Information;
                case CoreLogLevel.Warning:
                    return ModelLogLevel.Warning;
                case CoreLogLevel.Error:
                    return ModelLogLevel.Error;
                default:
                    throw new ArgumentOutOfRangeException(nameof(logLevel), logLevel, "Unknown log level");
            }
        }

        /// <summary>
        /// Converts from Core.Models.LogLevel to Core.Interfaces.LogLevel
        /// </summary>
        /// <param name="logLevel">The Core.Models.LogLevel to convert</param>
        /// <returns>The equivalent Core.Interfaces.LogLevel</returns>
        public static CoreLogLevel ToCoreLogLevel(ModelLogLevel logLevel)
        {
            switch (logLevel)
            {
                case ModelLogLevel.Debug:
                    return CoreLogLevel.Debug;
                case ModelLogLevel.Information:
                    return CoreLogLevel.Information;
                case ModelLogLevel.Warning:
                    return CoreLogLevel.Warning;
                case ModelLogLevel.Error:
                    return CoreLogLevel.Error;
                case ModelLogLevel.Critical:
                    return CoreLogLevel.Error; // Map Critical to Error since Core.Interfaces.LogLevel doesn't have Critical
                default:
                    throw new ArgumentOutOfRangeException(nameof(logLevel), logLevel, "Unknown log level");
            }
        }
    }
}
