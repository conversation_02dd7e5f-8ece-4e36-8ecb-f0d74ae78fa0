<UserControl x:Class="VolvoFlashWR.UI.Views.ECUFlashView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:VolvoFlashWR.UI.Views"
             xmlns:viewmodels="clr-namespace:VolvoFlashWR.UI.ViewModels"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <UserControl.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="120"/>
        </Style>

        <Style x:Key="InfoTextStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="0,5"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#F0F0F0" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="ECU Flash Operations" FontSize="20" FontWeight="Bold"/>
                    <TextBlock Text="Read and write EEPROM data and microcontroller code" Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="Selected ECU:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <ComboBox ItemsSource="{Binding ConnectedECUs}"
                              SelectedItem="{Binding SelectedECU}"
                              DisplayMemberPath="Name"
                              Width="150"/>

                    <TextBlock Text="Communication Speed:" VerticalAlignment="Center" Margin="20,0,5,0"/>
                    <ComboBox ItemsSource="{Binding CommunicationSpeedModes}"
                              SelectedItem="{Binding SelectedCommunicationSpeedMode}"
                              Width="100"/>

                    <Button Content="Apply"
                            Command="{Binding SetCommunicationSpeedModeCommand}"
                            Margin="5,0,0,0"
                            ToolTip="Apply the selected communication speed mode to the ECU"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- EEPROM Operations -->
                <Border Grid.Column="0" BorderBrush="#DDDDDD" BorderThickness="1" Margin="5" Padding="10">
                    <StackPanel>
                        <TextBlock Text="EEPROM Operations" Style="{StaticResource SectionHeaderStyle}"/>

                        <TextBlock Style="{StaticResource InfoTextStyle}">
                            EEPROM (Electrically Erasable Programmable Read-Only Memory) stores configuration
                            data that persists when power is removed. Operations on EEPROM are typically faster
                            than flash memory operations.
                        </TextBlock>

                        <Grid Margin="0,10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="EEPROM Size:" Margin="0,5,10,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding EEPROMSizeFormatted}" Margin="0,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Last Read:" Margin="0,5,10,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding LastEEPROMReadTime}" Margin="0,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Last Write:" Margin="0,5,10,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding LastEEPROMWriteTime}" Margin="0,5"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="Checksum:" Margin="0,5,10,5"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding EEPROMChecksum}" Margin="0,5"/>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="Status:" Margin="0,5,10,5"/>
                            <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding EEPROMStatus}" Margin="0,5"/>
                        </Grid>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10">
                            <Button Content="Read EEPROM"
                                    Command="{Binding ReadEEPROMCommand}"
                                    Style="{StaticResource ButtonStyle}"/>

                            <Button Content="Write EEPROM"
                                    Command="{Binding WriteEEPROMCommand}"
                                    Style="{StaticResource ButtonStyle}"/>

                            <Button Content="Save EEPROM"
                                    Command="{Binding SaveEEPROMCommand}"
                                    Style="{StaticResource ButtonStyle}"/>

                            <Button Content="Load EEPROM"
                                    Command="{Binding LoadEEPROMCommand}"
                                    Style="{StaticResource ButtonStyle}"/>
                        </StackPanel>

                        <TextBlock Text="EEPROM Data Preview" Style="{StaticResource SectionHeaderStyle}" Margin="0,20,0,5"/>

                        <Border BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,5" Height="150">
                            <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                                <TextBox Text="{Binding EEPROMDataPreview}"
                                         IsReadOnly="True"
                                         FontFamily="Consolas"
                                         Background="#F8F8F8"/>
                            </ScrollViewer>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Microcontroller Code Operations -->
                <Border Grid.Column="1" BorderBrush="#DDDDDD" BorderThickness="1" Margin="5" Padding="10">
                    <StackPanel>
                        <TextBlock Text="Microcontroller Code Operations" Style="{StaticResource SectionHeaderStyle}"/>

                        <TextBlock Style="{StaticResource InfoTextStyle}">
                            Microcontroller code (Flash memory) contains the program instructions that the ECU executes.
                            Flash operations are typically slower and more complex than EEPROM operations, and require
                            careful handling to avoid bricking the ECU.
                        </TextBlock>

                        <Grid Margin="0,10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Flash Size:" Margin="0,5,10,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding FlashSizeFormatted}" Margin="0,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="MCU Type:" Margin="0,5,10,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding MicrocontrollerType}" Margin="0,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Last Read:" Margin="0,5,10,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding LastMCUCodeReadTime}" Margin="0,5"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="Last Write:" Margin="0,5,10,5"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding LastMCUCodeWriteTime}" Margin="0,5"/>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="Checksum:" Margin="0,5,10,5"/>
                            <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding MCUCodeChecksum}" Margin="0,5"/>

                            <TextBlock Grid.Row="5" Grid.Column="0" Text="Status:" Margin="0,5,10,5"/>
                            <TextBlock Grid.Row="5" Grid.Column="1" Text="{Binding MCUCodeStatus}" Margin="0,5"/>
                        </Grid>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10">
                            <Button Content="Read MCU Code"
                                    Command="{Binding ReadMCUCodeCommand}"
                                    Style="{StaticResource ButtonStyle}"/>

                            <Button Content="Write MCU Code"
                                    Command="{Binding WriteMCUCodeCommand}"
                                    Style="{StaticResource ButtonStyle}"/>

                            <Button Content="Save MCU Code"
                                    Command="{Binding SaveMCUCodeCommand}"
                                    Style="{StaticResource ButtonStyle}"/>

                            <Button Content="Load MCU Code"
                                    Command="{Binding LoadMCUCodeCommand}"
                                    Style="{StaticResource ButtonStyle}"/>
                        </StackPanel>

                        <TextBlock Text="Memory Segments" Style="{StaticResource SectionHeaderStyle}" Margin="0,20,0,5"/>

                        <DataGrid ItemsSource="{Binding MemorySegments}"
                                  AutoGenerateColumns="False"
                                  Height="150"
                                  IsReadOnly="True"
                                  HeadersVisibility="Column"
                                  GridLinesVisibility="All"
                                  BorderBrush="#DDDDDD"
                                  BorderThickness="1">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Type" Binding="{Binding SegmentType}" Width="80"/>
                                <DataGridTextColumn Header="Start Address" Binding="{Binding StartAddressHex}" Width="100"/>
                                <DataGridTextColumn Header="End Address" Binding="{Binding EndAddressHex}" Width="100"/>
                                <DataGridTextColumn Header="Size" Binding="{Binding SizeFormatted}" Width="80"/>
                                <DataGridCheckBoxColumn Header="Read-Only" Binding="{Binding IsReadOnly}" Width="80"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>
            </Grid>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#F0F0F0" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="{Binding StatusMessage}"/>

                <ProgressBar Grid.Column="1" Width="200" Height="15" Value="{Binding ProgressValue}" Visibility="{Binding ProgressVisibility}"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>
