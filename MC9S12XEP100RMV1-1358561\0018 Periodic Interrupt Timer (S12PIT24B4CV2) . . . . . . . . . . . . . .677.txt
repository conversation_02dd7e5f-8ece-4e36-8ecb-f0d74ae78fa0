﻿Chapter 18
Periodic Interrupt Timer (S12PIT24B4CV2)

Table 18-1. Revision History

Revision Revision Sections
Description of Changes

Number Date Affected
V01.00 28 Apr 2005 - Initial Release
V01.01 05 Jul 2005 18.6/18-690 - Added application section.

- Removed table 1-1

18.1 Introduction
The period interrupt timer (PIT) is an array of 24-bit timers that can be used to trigger peripheral modules
or raise periodic interrupts. Refer to Figure 18-1 for a simplified block diagram.

18.1.1 Glossary
Acronyms and Abbreviations

PIT Periodic Interrupt Timer
ISR Interrupt Service Routine
CCR Condition Code Register
SoC System on Chip

micro time bases clock periods of the 16-bit timer modulus down-counters, which are generated by the 8-bit
modulus down-counters.

18.1.2 Features
The PIT includes these features:

• Four timers implemented as modulus down-counters with independent time-out periods.
• Time-out periods selectable between 1 and 224 bus clock cycles. Time-out equals m*n bus clock

cycles with 1 <= m <= 256 and 1 <= n <= 65536.
• Timers that can be enabled individually.
• Four time-out interrupts.
• Four time-out trigger output signals available to trigger peripheral modules.
• Start of timer channels can be aligned to each other.

18.1.3 Modes of Operation
Refer to the device overview for a detailed explanation of the chip modes.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 677



Chapter 18 Periodic Interrupt Timer (S12PIT24B4CV2)

• Run mode
This is the basic mode of operation.

• Wait mode
PIT operation in wait mode is controlled by the PITSWAI bit located in the PITCFLMT register.
In wait mode, if the bus clock is globally enabled and if the PITSWAI bit is clear, the PIT operates
like in run mode. In wait mode, if the PITSWAI bit is set, the PIT module is stalled.

• Stop mode
In full stop mode or pseudo stop mode, the PIT module is stalled.

• Freeze mode
PIT operation in freeze mode is controlled by the PITFRZ bit located in the PITCFLMT register.
In freeze mode, if the PITFRZ bit is clear, the PIT operates like in run mode. In freeze mode, if the
PITFRZ bit is set, the PIT module is stalled.

18.1.4 Block Diagram
Figure 18-1 shows a block diagram of the PIT module.

Micro Time Interrupt 0
Time-Out 0

8-Bit Base 0 16-Bit Timer 0 Interface Trigger 0
Bus Clock Micro Timer 0

Interrupt 1
Time-Out 1

16-Bit Timer 1 Interface Trigger 1

8-Bit Micro
Time Interrupt 2

Micro Timer 1 Time-Out 2
Base 1 16-Bit Timer 2 Interface Trigger 2

Interrupt 3
Time-Out 3

16-Bit Timer 3 Interface Trigger 3

Figure 18-1. PIT24B4C Block Diagram

18.2 External Signal Description
The PIT module has no external pins.

18.3 Register Definition
This section consists of register descriptions in address order of the PIT. Each description includes a
standard register diagram with an associated figure number. Details of register bit and field function follow
the register diagrams, in bit order.

MC9S12XE-Family Reference Manual  Rev. 1.25

678 Freescale Semiconductor



Chapter 18 Periodic Interrupt Timer (S12PIT24B4CV2)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0000 R 0 0 0 0 0
PITCFLMT PITE PITSWAI PITFRZ

W PFLMT1 PFLMT0

0x0001 R 0 0 0 0 0 0 0 0
PITFLT W PFLT3 PFLT2 PFLT1 PFLT0

0x0002 R 0 0 0 0
PITCE PCE3 PCE2 PCE1 PCE0

W

0x0003 R 0 0 0 0
PITMUX PMUX3 PMUX2 PMUX1 PMUX0

W

0x0004 R 0 0 0 0
PITINTE PINTE3 PINTE2 PINTE1 PINTE0

W

0x0005 R 0 0 0 0
PITTF PTF3 PTF2 PTF1 PTF0

W

0x0006 R
PITMTLD0 PMTLD7 PMTLD6 PMTLD5 PMTLD4 PMTLD3 PMTLD2 PMTLD1 PMTLD0

W

0x0007 R
PITMTLD1 PMTLD7 PMTLD6 PMTLD5 PMTLD4 PMTLD3 PMTLD2 PMTLD1 PMTLD0

W

0x0008 R
PITLD0 (High) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8

W

0x0009 R
PITLD0 (Low) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0

W

0x000A R
PITCNT0 (High) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8

W

0x000B R
PITCNT0 (Low) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0

W

0x000C R
PITLD1 (High) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8

W

0x000D R
PITLD1 (Low) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0

W

0x000E R
PITCNT1 (High) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8

W

= Unimplemented or Reserved

Figure 18-2. PIT Register Summary (Sheet 1 of 2)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 679



Chapter 18 Periodic Interrupt Timer (S12PIT24B4CV2)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x000F R
PITCNT1 (Low) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0

W

0x0010 R
PITLD2 (High) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8

W

0x0011 R
PITLD2 (Low) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0

W

0x0012 R
PITCNT2 (High) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8

W

0x0013 R
PITCNT2 (Low) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0

W

0x0014 R
PITLD3 (High) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8

W

0x0015 R
PITLD3 (Low) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0

W

0x0016 R
PITCNT3 (High) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8

W

0x0017 R
PITCNT3 (Low) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0

W

0x0018−0x0027 R 0 0 0 0 0 0 0 0
RESERVED W

= Unimplemented or Reserved

Figure 18-2. PIT Register Summary (Sheet 2 of 2)

18.3.0.1 PIT Control and Force Load Micro Timer Register (PITCFLMT)

Module Base + 0x0000

7 6 5 4 3 2 1 0
R 0 0 0 0 0

PITE PITSWAI PITFRZ
W PFLMT1 PFLMT0

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 18-3. PIT Control and Force Load Micro Timer Register (PITCFLMT)

Read: Anytime

Write: Anytime; writes to the reserved bits have no effect

MC9S12XE-Family Reference Manual  Rev. 1.25

680 Freescale Semiconductor



Chapter 18 Periodic Interrupt Timer (S12PIT24B4CV2)

Table 18-2. PITCFLMT Field Descriptions

Field Description

7 PIT Module Enable Bit — This bit enables the PIT module. If PITE is cleared, the PIT module is disabled and
PITE flag bits in the PITTF register are cleared. When PITE is set, individually enabled timers (PCE set) start down-

counting with the corresponding load register values.
0 PIT disabled (lower power consumption).
1 PIT is enabled.

6 PIT Stop in Wait Mode Bit — This bit is used for power conservation while in wait mode.
PITSWAI 0 PIT operates normally in wait mode

1 PIT clock generation stops and freezes the PIT module when in wait mode

5 PIT Counter Freeze while in Freeze Mode Bit — When during debugging a breakpoint (freeze mode) is
PITFRZ encountered it is useful in many cases to freeze the PIT counters to avoid e.g. interrupt generation. The PITFRZ

bit controls the PIT operation while in freeze mode.
0 PIT operates normally in freeze mode
1 PIT counters are stalled when in freeze mode

1:0 PIT Force Load Bits for Micro Timer 1:0 — These bits have only an effect if the corresponding micro timer is
PFLMT[1:0] active and if the PIT module is enabled (PITE set). Writing a one into a PFLMT bit loads the corresponding 8-bit

micro timer load register into the 8-bit micro timer down-counter. Writing a zero has no effect. Reading these bits
will always return zero.
Note: A micro timer force load affects all timer channels that use the corresponding micro time base.

******** PIT Force Load Timer Register (PITFLT)
Module Base + 0x0001

7 6 5 4 3 2 1 0
R 0 0 0 0 0 0 0 0
W PFLT3 PFLT2 PFLT1 PFLT0

Reset 0 0 0 0 0 0 0 0

Figure 18-4. PIT Force Load Timer Register (PITFLT)

Read: Anytime

Write: Anytime

Table 18-3. PITFLT Field Descriptions

Field Description

3:0 PIT Force Load Bits for Timer 3-0 — These bits have only an effect if the corresponding timer channel (PCE
PFLT[3:0] set) is enabled and if the PIT module is enabled (PITE set). Writing a one into a PFLT bit loads the corresponding

16-bit timer load register into the 16-bit timer down-counter. Writing a zero has no effect. Reading these bits will
always return zero.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 681



Chapter 18 Periodic Interrupt Timer (S12PIT24B4CV2)

******** PIT Channel Enable Register (PITCE)
Module Base + 0x0002

7 6 5 4 3 2 1 0
R 0 0 0 0

PCE3 PCE2 PCE1 PCE0
W

Reset 0 0 0 0 0 0 0 0

Figure 18-5. PIT Channel Enable Register (PITCE)

Read: Anytime

Write: Anytime

Table 18-4. PITCE Field Descriptions

Field Description

3:0 PIT Enable Bits for Timer Channel 3:0 — These bits enable the PIT channels 3-0. If PCE is cleared, the PIT
PCE[3:0] channel is disabled and the corresponding flag bit in the PITTF register is cleared. When PCE is set, and if the

PIT module is enabled (PITE = 1) the 16-bit timer counter is loaded with the start count value and starts down-
counting.
0 The corresponding PIT channel is disabled.
1 The corresponding PIT channel is enabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

682 Freescale Semiconductor



Chapter 18 Periodic Interrupt Timer (S12PIT24B4CV2)

******** PIT Multiplex Register (PITMUX)
Module Base + 0x0003

7 6 5 4 3 2 1 0
R 0 0 0 0

PMUX3 PMUX2 PMUX1 PMUX0
W

Reset 0 0 0 0 0 0 0 0

Figure 18-6. PIT Multiplex Register (PITMUX)

Read: Anytime

Write: Anytime

Table 18-5. PITMUX Field Descriptions

Field Description

3:0 PIT Multiplex Bits for Timer Channel 3:0 — These bits select if the corresponding 16-bit timer is connected to
PMUX[3:0] micro time base 1 or 0. If PMUX is modified, the corresponding 16-bit timer is switched to the other micro time

base immediately.
0 The corresponding 16-bit timer counts with micro time base 0.
1 The corresponding 16-bit timer counts with micro time base 1.

******** PIT Interrupt Enable Register (PITINTE)
Module Base + 0x0004

7 6 5 4 3 2 1 0
R 0 0 0 0

PINTE3 PINTE2 PINTE1 PINTE0
W

Reset 0 0 0 0 0 0 0 0

Figure 18-7. PIT Interrupt Enable Register (PITINTE)

Read: Anytime

Write: Anytime

Table 18-6. PITINTE Field Descriptions

Field Description

3:0 PIT Time-out Interrupt Enable Bits for Timer Channel 3:0 — These bits enable an interrupt service request
PINTE[3:0] whenever the time-out flag PTF of the corresponding PIT channel is set. When an interrupt is pending (PTF set)

enabling the interrupt will immediately cause an interrupt. To avoid this, the corresponding PTF flag has to be
cleared first.
0 Interrupt of the corresponding PIT channel is disabled.
1 Interrupt of the corresponding PIT channel is enabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 683



Chapter 18 Periodic Interrupt Timer (S12PIT24B4CV2)

******** PIT Time-Out Flag Register (PITTF)
Module Base + 0x0005

7 6 5 4 3 2 1 0
R 0 0 0 0

PTF3 PTF2 PTF1 PTF0
W

Reset 0 0 0 0 0 0 0 0

Figure 18-8. PIT Time-Out Flag Register (PITTF)

Read: Anytime

Write: Anytime (write to clear)

Table 18-7. PITTF Field Descriptions

Field Description

3:0 PIT Time-out Flag Bits for Timer Channel 3:0 — PTF is set when the corresponding 16-bit timer modulus
PTF[3:0] down-counter and the selected 8-bit micro timer modulus down-counter have counted to zero. The flag can be

cleared by writing a one to the flag bit. Writing a zero has no effect. If flag clearing by writing a one and flag setting
happen in the same bus clock cycle, the flag remains set. The flag bits are cleared if the PIT module is disabled
or if the corresponding timer channel is disabled.
0 Time-out of the corresponding PIT channel has not yet occurred.
1 Time-out of the corresponding PIT channel has occurred.

******** PIT Micro Timer Load Register 0 to 1 (PITMTLD0–1)
Module Base + 0x0006

7 6 5 4 3 2 1 0
R

PMTLD7 PMTLD6 PMTLD5 PMTLD4 PMTLD3 PMTLD2 PMTLD1 PMTLD0
W

Reset 0 0 0 0 0 0 0 0

Figure 18-9. PIT Micro Timer Load Register 0 (PITMTLD0)

Module Base + 0x0007

7 6 5 4 3 2 1 0
R

PMTLD7 PMTLD6 PMTLD5 PMTLD4 PMTLD3 PMTLD2 PMTLD1 PMTLD0
W

Reset 0 0 0 0 0 0 0 0

Figure 18-10. PIT Micro Timer Load Register 1 (PITMTLD1)

Read: Anytime

Write: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

684 Freescale Semiconductor



Chapter 18 Periodic Interrupt Timer (S12PIT24B4CV2)

Table 18-8. PITMTLD0–1 Field Descriptions

Field Description

7:0 PIT Micro Timer Load Bits 7:0 — These bits set the 8-bit modulus down-counter load value of the micro timers.
PMTLD[7:0] Writing a new value into the PITMTLD register will not restart the timer. When the micro timer has counted down

to zero, the PMTLD register value will be loaded. The PFLMT bits in the PITCFLMT register can be used to
immediately update the count register with the new value if an immediate load is desired.

******** PIT Load Register 0 to 3 (PITLD0–3)

Module Base + 0x0008, 0x0009

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8 PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 18-11. PIT Load Register 0 (PITLD0)

Module Base + 0x000C, 0x000D

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8 PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 18-12. PIT Load Register 1 (PITLD1)

Module Base + 0x0010, 0x0011

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8 PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 18-13. PIT Load Register 2 (PITLD2)

Module Base + 0x0014, 0x0015

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8 PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 18-14. PIT Load Register 3 (PITLD3)

Read: Anytime

Write: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 685



Chapter 18 Periodic Interrupt Timer (S12PIT24B4CV2)

Table 18-9. PITLD0–3 Field Descriptions

Field Description

15:0 PIT Load Bits 15:0 — These bits set the 16-bit modulus down-counter load value. Writing a new value into the
PLD[15:0] PITLD register must be a 16-bit access, to ensure data consistency. It will not restart the timer. When the timer

has counted down to zero the PTF time-out flag will be set and the register value will be loaded. The PFLT bits
in the PITFLT register can be used to immediately update the count register with the new value if an immediate
load is desired.

******** PIT Count Register 0 to 3 (PITCNT0–3)

Module Base + 0x000A, 0x000B

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R PCNT PCNT PCNT PCNT PCNT PCNT PCN PCN PCN PCN PCN PCN PCN PCN PCN PCN
W 15 14 13 12 11 10 T9 T8 T7 T6 T5 T4 T3 T2 T1 T0

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 18-15. PIT Count Register 0 (PITCNT0)

Module Base + 0x000E, 0x000F

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R PCNT PCNT PCNT PCNT PCNT PCNT PCN PCN PCN PCN PCN PCN PCN PCN PCN PCN
W 15 14 13 12 11 10 T9 T8 T7 T6 T5 T4 T3 T2 T1 T0

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 18-16. PIT Count Register 1 (PITCNT1)

Module Base + 0x0012, 0x0013

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R PCNT PCNT PCNT PCNT PCNT PCNT PCN PCN PCN PCN PCN PCN PCN PCN PCN PCN
W 15 14 13 12 11 10 T9 T8 T7 T6 T5 T4 T3 T2 T1 T0

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 18-17. PIT Count Register 2 (PITCNT2)

Module Base + 0x0016, 0x0017

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R PCNT PCNT PCNT PCNT PCNT PCNT PCN PCN PCN PCN PCN PCN PCN PCN PCN PCN
W 15 14 13 12 11 10 T9 T8 T7 T6 T5 T4 T3 T2 T1 T0

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 18-18. PIT Count Register 3 (PITCNT3)

Read: Anytime

Write: Has no meaning or effect

MC9S12XE-Family Reference Manual  Rev. 1.25

686 Freescale Semiconductor



Chapter 18 Periodic Interrupt Timer (S12PIT24B4CV2)

Table 18-10. PITCNT0–3 Field Descriptions

Field Description

15:0 PIT Count Bits 15-0 — These bits represent the current 16-bit modulus down-counter value. The read access
PCNT[15:0] for the count register must take place in one clock cycle as a 16-bit access.

18.4 Functional Description
Figure 18-19 shows a detailed block diagram of the PIT module. The main parts of the PIT are status,
control and data registers, two 8-bit down-counters, four 16-bit down-counters and an interrupt/trigger
interface.

4 PFLT0 PIT24B4C
PITFLT Register Timer 0

PMUX0
4 PITLD0 Register

PITMUX Register PITCNT0 Register time-out 0

PFLT1
PITMLD0 Register [1] Timer 1

Bus PITLD1 Register
8-Bit Micro Timer 0

Clock [0] time-out 1
PITCNT1 Register

PFLT2
[2] Timer 2 Interrupt / 4

PITMLD1 Register PITLD2 Register time- Trigger Interface
out 3

8-Bit Micro Timer 1 PITCNT2 Register Hardware
[1] Trigger

PITCFLMT Register PFLT3 PITTF Register
Timer 3

PFLMT [3]
PITLD3 Register time- 4

out 3
PITCNT3 Register PITINTE Register Interrupt

Request

Figure 18-19. PIT24B4C Detailed Block Diagram

18.4.1 Timer
As shown in Figure 18-1 and Figure 18-19, the 24-bit timers are built in a two-stage architecture with four
16-bit modulus down-counters and two 8-bit modulus down-counters. The 16-bit timers are clocked with
two selectable micro time bases which are generated with 8-bit modulus down-counters. Each 16-bit timer
is connected to micro time base 0 or 1 via the PMUX[3:0] bit setting in the PIT Multiplex (PITMUX)
register.

A timer channel is enabled if the module enable bit PITE in the PIT control and force load micro timer
(PITCFLMT) register is set and if the corresponding PCE bit in the PIT channel enable (PITCE) register
is set. Two 8-bit modulus down-counters are used to generate two micro time bases. As soon as a micro
time base is selected for an enabled timer channel, the corresponding micro timer modulus down-counter
will load its start value as specified in the PITMTLD0 or PITMTLD1 register and will start down-counting.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 687

PMUX



Chapter 18 Periodic Interrupt Timer (S12PIT24B4CV2)

Whenever the micro timer down-counter has counted to zero the PITMTLD register is reloaded and the
connected 16-bit modulus down-counters count one cycle.

Whenever a 16-bit timer counter and the connected 8-bit micro timer counter have counted to zero, the
PITLD register is reloaded and the corresponding time-out flag PTF in the PIT time-out flag (PITTF)
register is set, as shown in Figure 18-20. The time-out period is a function of the timer load (PITLD) and
micro timer load (PITMTLD) registers and the bus clock fBUS:

time-out period = (PITMTLD + 1) * (PITLD + 1) / fBUS.

For example, for a 40 MHz bus clock, the maximum time-out period equals:
256 * 65536 * 25 ns = 419.43 ms.

The current 16-bit modulus down-counter value can be read via the PITCNT register. The micro timer
down-counter values cannot be read.

The 8-bit micro timers can individually be restarted by writing a one to the corresponding force load micro
timer PFLMT bits in the PIT control and force load micro timer (PITCFLMT) register. The 16-bit timers
can individually be restarted by writing a one to the corresponding force load timer PFLT bits in the PIT
forceload timer (PITFLT) register. If desired, any group of timers and micro timers can be restarted at the
same time by using one 16-bit write to the adjacent PITCFLMT and PITFLT registers with the relevant
bits set, as shown in Figure 18-20.

Bus Clock

8-Bit Micro
0 2 1 0 2 1 0 2 1 0 2 1 2 1 0 2 1 0 2 1 0 2

Timer Counter

PITCNT Register 00 0001 0000 0001 0000 0001 0000 0001

8-Bit Force Load

16-Bit Force Load

PTF Flag1

PITTRIG

Time-Out Period Time-Out Period
After Restart

Note 1. The PTF flag clearing depends on the software

Figure 18-20. PIT Trigger and Flag Signal Timing

MC9S12XE-Family Reference Manual  Rev. 1.25

688 Freescale Semiconductor



Chapter 18 Periodic Interrupt Timer (S12PIT24B4CV2)

18.4.2 Interrupt Interface
Each time-out event can be used to trigger an interrupt service request. For each timer channel, an
individual bit PINTE in the PIT interrupt enable (PITINTE) register exists to enable this feature. If PINTE
is set, an interrupt service is requested whenever the corresponding time-out flag PTF in the PIT time-out
flag (PITTF) register is set. The flag can be cleared by writing a one to the flag bit.

NOTE
Be careful when resetting the PITE, PINTE or PITCE bits in case of pending
PIT interrupt requests, to avoid spurious interrupt requests.

18.4.3 Hardware Trigger
The PIT module contains four hardware trigger signal lines PITTRIG[3:0], one for each timer channel.
These signals can be connected on SoC level to peripheral modules enabling e.g. periodic ATD conversion
(please refer to the device overview for the mapping of PITTRIG[3:0] signals to peripheral modules).

Whenever a timer channel time-out is reached, the corresponding PTF flag is set and the corresponding
trigger signal PITTRIG triggers a rising edge. The trigger feature requires a minimum time-out period of
two bus clock cycles because the trigger is asserted high for at least one bus clock cycle. For load register
values PITLD = 0x0001 and PITMTLD = 0x0002 the flag setting, trigger timing and a restart with force
load is shown in Figure 18-20.

18.5 Initialization

18.5.1 Startup
Set the configuration registers before the PITE bit in the PITCFLMT register is set. Before PITE is set, the
configuration registers can be written in arbitrary order.

18.5.2 Shutdown
When the PITCE register bits, the PITINTE register bits or the PITE bit in the PITCFLMT register are
cleared, the corresponding PIT interrupt flags are cleared. In case of a pending PIT interrupt request, a
spurious interrupt can be generated. Two strategies, which avoid spurious interrupts, are recommended:

1. Reset the PIT interrupt flags only in an ISR. When entering the ISR, the I mask bit in the CCR is
set automatically. The I mask bit must not be cleared before the PIT interrupt flags are cleared.

2. After setting the I mask bit with the SEI instruction, the PIT interrupt flags can be cleared. Then
clear the I mask bit with the CLI instruction to re-enable interrupts.

18.5.3 Flag Clearing
A flag is cleared by writing a one to the flag bit. Always use store or move instructions to write a one in
certain bit positions. Do not use the BSET instructions. Do not use any C-constructs that compile to BSET
instructions. “BSET flag_register, #mask” must not be used for flag clearing because BSET is a read-

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 689



Chapter 18 Periodic Interrupt Timer (S12PIT24B4CV2)

modify-write instruction which writes back the “bit-wise or” of the flag_register and the mask into the
flag_register. BSET would clear all flag bits that were set, independent from the mask.

For example, to clear flag bit 0 use: MOVB #$01,PITTF.

18.6 Application Information
To get started quickly with the PIT24B4C module this section provides a small code example how to use
the block. Please note that the example provided is only one specific case out of the possible configurations
and implementations.

Functionality: Generate an PIT interrupt on channel 0 every 500 PIT clock cycles.

ORG CODESTART ; place the program into specific
; range (to be selected)

LDS RAMEND ; load stack pointer to top of RAM
MOVW #CH0_ISR,VEC_PIT_CH0 ; Change value of channel 0 ISR adr

; ******************** Start PIT Initialization *******************************************************

CLR PITCFLMT ; disable PIT
MOVB #$01,PITCE ; enable timer channel 0
CLR PITMUX ; ch0 connected to micro timer 0
MOVB #$63,PITMTLD0 ; micro time base 0 equals 100 clock cycles
MOVW #$0004,PITLD0 ; time base 0 eq. 5 micro time bases 0 =5*100 = 500
MOVB #$01,PITINTE ; enable interupt channel 0
MOVB #$80,PITCFLMT ; enable PIT
CLI ; clear Interupt disable Mask bit

;******************** Main Program *************************************************************

MAIN: BRA * ; loop until interrupt

;******************** Channel 0 Interupt Routine ***************************************************

CH0_ISR: LDAA PITTF ; 8 bit read of PIT time out flags
MOVB #$01,PITTF ; clear PIT channel 0 time out flag
RTI ; return to MAIN

MC9S12XE-Family Reference Manual  Rev. 1.25

690 Freescale Semiconductor