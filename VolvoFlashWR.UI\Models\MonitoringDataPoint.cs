using System;

namespace VolvoFlashWR.UI.Models
{
    /// <summary>
    /// Represents a single data point in the real-time monitoring system
    /// </summary>
    public class MonitoringDataPoint
    {
        /// <summary>
        /// The timestamp when the data point was captured
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// The name of the parameter being monitored
        /// </summary>
        public string ParameterName { get; set; } = string.Empty;

        /// <summary>
        /// The current value of the parameter
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// The unit of measurement for the parameter
        /// </summary>
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// The minimum value observed for this parameter
        /// </summary>
        public string MinValue { get; set; } = string.Empty;

        /// <summary>
        /// The maximum value observed for this parameter
        /// </summary>
        public string MaxValue { get; set; } = string.Empty;

        /// <summary>
        /// The average value observed for this parameter
        /// </summary>
        public string AverageValue { get; set; } = string.Empty;

        /// <summary>
        /// The status of the parameter (Normal, Warning, Error, etc.)
        /// </summary>
        public string Status { get; set; } = "Unknown";

        /// <summary>
        /// Default constructor
        /// </summary>
        public MonitoringDataPoint()
        {
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// Constructor with parameter name
        /// </summary>
        /// <param name="parameterName">The name of the parameter</param>
        public MonitoringDataPoint(string parameterName)
        {
            Timestamp = DateTime.Now;
            ParameterName = parameterName;
        }

        /// <summary>
        /// Constructor with parameter name and value
        /// </summary>
        /// <param name="parameterName">The name of the parameter</param>
        /// <param name="value">The value of the parameter</param>
        /// <param name="unit">The unit of measurement</param>
        public MonitoringDataPoint(string parameterName, string value, string unit = "")
        {
            Timestamp = DateTime.Now;
            ParameterName = parameterName;
            Value = value;
            Unit = unit;
            MinValue = value;
            MaxValue = value;
            AverageValue = value;
            Status = "Normal";
        }
    }
}
