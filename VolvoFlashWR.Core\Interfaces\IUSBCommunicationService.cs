using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for USB communication service
    /// </summary>
    public interface IUSBCommunicationService
    {
        /// <summary>
        /// Event triggered when a USB connection is established
        /// </summary>
        event EventHandler<string> USBConnected;

        /// <summary>
        /// Event triggered when a USB connection is lost
        /// </summary>
        event EventHandler<string> USBDisconnected;

        /// <summary>
        /// Event triggered when an error occurs during USB communication
        /// </summary>
        event EventHandler<string> USBError;

        /// <summary>
        /// Initializes the USB communication service
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        Task<bool> InitializeAsync();

        /// <summary>
        /// Checks if USB is available
        /// </summary>
        /// <returns>True if USB is available, false otherwise</returns>
        Task<bool> IsUSBAvailableAsync();

        /// <summary>
        /// Detects Vocom devices connected via USB
        /// </summary>
        /// <returns>List of port names for connected Vocom devices</returns>
        Task<List<string>> DetectVocomDevicesAsync();

        /// <summary>
        /// Connects to a device via USB
        /// </summary>
        /// <param name="portName">The port name to connect to</param>
        /// <param name="baudRate">The baud rate to use</param>
        /// <param name="timeout">The connection timeout in milliseconds</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        Task<bool> ConnectToDeviceAsync(string portName, int baudRate = 115200, int timeout = 5000);

        /// <summary>
        /// Disconnects from a device
        /// </summary>
        /// <param name="portName">The port name to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        Task<bool> DisconnectFromDeviceAsync(string portName);

        /// <summary>
        /// Sends data to a device
        /// </summary>
        /// <param name="portName">The port name to send data to</param>
        /// <param name="data">The data to send</param>
        /// <returns>True if data is sent successfully, false otherwise</returns>
        Task<bool> SendDataAsync(string portName, byte[] data);

        /// <summary>
        /// Receives data from a device
        /// </summary>
        /// <param name="portName">The port name to receive data from</param>
        /// <param name="timeout">The timeout in milliseconds</param>
        /// <returns>The received data</returns>
        Task<byte[]> ReceiveDataAsync(string portName, int timeout = 5000);
    }
}
