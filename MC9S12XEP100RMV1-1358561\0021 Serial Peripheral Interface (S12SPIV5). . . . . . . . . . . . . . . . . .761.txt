﻿Chapter 21
Serial Peripheral Interface (S12SPIV5)

Table 21-1. Revision History

Revision Sections
Revision Date Description of Changes

Number Affected
V05.00 24 Mar 2005 21.3.2/21-765 - Added 16-bit transfer width feature.

21.1 Introduction
The SPI module allows a duplex, synchronous, serial communication between the MCU and peripheral
devices. Software can poll the SPI status flags or the SPI operation can be interrupt driven.

21.1.1 Glossary of Terms
SPI Serial Peripheral Interface
SS Slave Select

SCK Serial Clock
MOSI Master Output, Slave Input
MISO Master Input, Slave Output
MOMI Master Output, Master Input
SISO Slave Input, Slave Output

21.1.2 Features
The SPI includes these distinctive features:

• Master mode and slave mode
• Selectable 8 or 16-bit transfer width
• Bidirectional mode
• Slave select output
• Mode fault error flag with CPU interrupt capability
• Double-buffered data register
• Serial clock with programmable polarity and phase
• Control of SPI operation during wait mode

21.1.3 Modes of Operation
The SPI functions in three modes: run, wait, and stop.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 761



Chapter 21 Serial Peripheral Interface (S12SPIV5)

• Run mode
This is the basic mode of operation.

• Wait mode
SPI operation in wait mode is a configurable low power mode, controlled by the SPISWAI bit
located in the SPICR2 register. In wait mode, if the SPISWAI bit is clear, the SPI operates like in
run mode. If the SPISWAI bit is set, the SPI goes into a power conservative state, with the SPI clock
generation turned off. If the SPI is configured as a master, any transmission in progress stops, but
is resumed after CPU goes into run mode. If the SPI is configured as a slave, reception and
transmission of data continues, so that the slave stays synchronized to the master.

• Stop mode
The SPI is inactive in stop mode for reduced power consumption. If the SPI is configured as a
master, any transmission in progress stops, but is resumed after CPU goes into run mode. If the SPI
is configured as a slave, reception and transmission of data continues, so that the slave stays
synchronized to the master.

For a detailed description of operating modes, please refer to Section 21.4.7, “Low Power Mode Options”.

21.1.4 Block Diagram
Figure 21-1 gives an overview on the SPI architecture. The main parts of the SPI are status, control and
data registers, shifter logic, baud rate generator, master/slave control logic, and port control logic.

MC9S12XE-Family Reference Manual  Rev. 1.25

762 Freescale Semiconductor



Chapter 21 Serial Peripheral Interface (S12SPIV5)

SPI

2
SPI Control Register 1

BIDIROE

2
SPI Control Register 2

SPC0

SPI Status Register Slave
CPOL

Control CPHA MOSI
SPIF MODF SPTEF

Phase + SCK In
Interrupt Control Slave Baud Rate Polarity

SPI Control
Interrupt Master Baud Rate Phase + SCK Out
Request Polarity Port

Control
Control SCK

Baud Rate Generator Master Logic
Control

Counter SS
Bus Clock

Prescaler Baud Rate
Clock Select Shift Sample

Clock Clock
SPPR 3 SPR 3

Shifter
SPI Baud Rate Register Data In

LSBFE=1 LSBFE=0

LSBFE=1
SPI Data Register MSB

LSBFE=0 LSB

LSBFE=0 LSBFE=1 Data Out

Figure 21-1. SPI Block Diagram

21.2 External Signal Description
This section lists the name and description of all ports including inputs and outputs that do, or may, connect
off chip. The SPI module has a total of four external pins.

21.2.1 MOSI — Master Out/Slave In Pin
This pin is used to transmit data out of the SPI module when it is configured as a master and receive data
when it is configured as slave.

21.2.2 MISO — Master In/Slave Out Pin
This pin is used to transmit data out of the SPI module when it is configured as a slave and receive data
when it is configured as master.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 763



Chapter 21 Serial Peripheral Interface (S12SPIV5)

21.2.3 SS — Slave Select Pin
This pin is used to output the select signal from the SPI module to another peripheral with which a data
transfer is to take place when it is configured as a master and it is used as an input to receive the slave select
signal when the SPI is configured as slave.

21.2.4 SCK — Serial Clock Pin
In master mode, this is the synchronous output clock. In slave mode, this is the synchronous input clock.

21.3 Memory Map and Register Definition
This section provides a detailed description of address space and registers used by the SPI.

21.3.1 Module Memory Map
The memory map for the SPI is given in Figure 21-2. The address listed for each register is the sum of a
base address and an address offset. The base address is defined at the SoC level and the address offset is
defined at the module level. Reads from the reserved bits return zeros and writes to the reserved bits have
no effect.

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name
0x0000 R
SPICR1 SPIE SPE SPTIE MSTR CPOL CPHA SSOE LSBFE

W

0x0001 R 0 0 0
SPICR2 XFRW MODFEN BIDIROE SPISWAI SPC0

W

0x0002 R 0 0
SPIBR SPPR2 SPPR1 SPPR0 SPR2 SPR1 SPR0

W

0x0003 R SPIF 0 SPTEF MODF 0 0 0 0
SPISR W

0x0004 R R15 R14 R13 R12 R11 R10 R9 R8
SPIDRH W T15 T14 T13 T12 T11 T10 T9 T8

0x0005 R R7 R6 R5 R4 R3 R2 R1 R0
SPIDRL W T7 T6 T5 T4 T3 T2 T1 T0

0x0006 R
Reserved W

0x0007 R
Reserved W

= Unimplemented or Reserved

Figure 21-2. SPI Register Summary

MC9S12XE-Family Reference Manual  Rev. 1.25

764 Freescale Semiconductor



Chapter 21 Serial Peripheral Interface (S12SPIV5)

21.3.2 Register Descriptions
This section consists of register descriptions in address order. Each description includes a standard register
diagram with an associated figure number. Details of register bit and field function follow the register
diagrams, in bit order.

******** SPI Control Register 1 (SPICR1)
Module Base +0x0000

7 6 5 4 3 2 1 0
R

SPIE SPE SPTIE MSTR CPOL CPHA SSOE LSBFE
W

Reset 0 0 0 0 0 1 0 0

Figure 21-3. SPI Control Register 1 (SPICR1)

Read: Anytime

Write: Anytime

Table 21-2. SPICR1 Field Descriptions

Field Description

7 SPI Interrupt Enable Bit — This bit enables SPI interrupt requests, if SPIF or MODF status flag is set.
SPIE 0 SPI interrupts disabled.

1 SPI interrupts enabled.

6 SPI System Enable Bit — This bit enables the SPI system and dedicates the SPI port pins to SPI system
SPE functions. If SPE is cleared, SPI is disabled and forced into idle state, status bits in SPISR register are reset.

0 SPI disabled (lower power consumption).
1 SPI enabled, port pins are dedicated to SPI functions.

5 SPI Transmit Interrupt Enable — This bit enables SPI interrupt requests, if SPTEF flag is set.
SPTIE 0 SPTEF interrupt disabled.

1 SPTEF interrupt enabled.

4 SPI Master/Slave Mode Select Bit — This bit selects whether the SPI operates in master or slave mode.
MSTR Switching the SPI from master to slave or vice versa forces the SPI system into idle state.

0 SPI is in slave mode.
1 SPI is in master mode.

3 SPI Clock Polarity Bit — This bit selects an inverted or non-inverted SPI clock. To transmit data between SPI
CPOL modules, the SPI modules must have identical CPOL values. In master mode, a change of this bit will abort a

transmission in progress and force the SPI system into idle state.
0 Active-high clocks selected. In idle state SCK is low.
1 Active-low clocks selected. In idle state SCK is high.

2 SPI Clock Phase Bit — This bit is used to select the SPI clock format. In master mode, a change of this bit will
CPHA abort a transmission in progress and force the SPI system into idle state.

0 Sampling of data occurs at odd edges (1,3,5,...) of the SCK clock.
1 Sampling of data occurs at even edges (2,4,6,...) of the SCK clock.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 765



Chapter 21 Serial Peripheral Interface (S12SPIV5)

Table 21-2. SPICR1 Field Descriptions (continued)

Field Description

1 Slave Select Output Enable — The SS output feature is enabled only in master mode, if MODFEN is set, by
SSOE asserting the SSOE as shown in Table 21-3. In master mode, a change of this bit will abort a transmission in

progress and force the SPI system into idle state.

0 LSB-First Enable — This bit does not affect the position of the MSB and LSB in the data register. Reads and
LSBFE writes of the data register always have the MSB in the highest bit position. In master mode, a change of this bit

will abort a transmission in progress and force the SPI system into idle state.
0 Data is transferred most significant bit first.
1 Data is transferred least significant bit first.

Table 21-3. SS Input / Output Selection

MODFEN SSOE Master Mode Slave Mode
0 0 SS not used by SPI SS input
0 1 SS not used by SPI SS input
1 0 SS input with MODF feature SS input
1 1 SS is slave select output SS input

******** SPI Control Register 2 (SPICR2)
Module Base +0x0001

7 6 5 4 3 2 1 0
R 0 0 0

XFRW MODFEN BIDIROE SPISWAI SPC0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 21-4. SPI Control Register 2 (SPICR2)

Read: Anytime

Write: Anytime; writes to the reserved bits have no effect

MC9S12XE-Family Reference Manual  Rev. 1.25

766 Freescale Semiconductor



Chapter 21 Serial Peripheral Interface (S12SPIV5)

Table 21-4. SPICR2 Field Descriptions

Field Description

6 Transfer Width — This bit is used for selecting the data transfer width. If 8-bit transfer width is selected, SPIDRL
XFRW becomes the dedicated data register and SPIDRH is unused. If 16-bit transfer width is selected, SPIDRH and

SPIDRL form a 16-bit data register. Please refer to Section ********, “SPI Status Register (SPISR) for
information about transmit/receive data handling and the interrupt flag clearing mechanism. In master mode, a
change of this bit will abort a transmission in progress and force the SPI system into idle state.
0 8-bit Transfer Width (n = 8)(1)

1 16-bit Transfer Width (n = 16)1

4 Mode Fault Enable Bit — This bit allows the MODF failure to be detected. If the SPI is in master mode and
MODFEN MODFEN is cleared, then the SS port pin is not used by the SPI. In slave mode, the SS is available only as an

input regardless of the value of MODFEN. For an overview on the impact of the MODFEN bit on the SS port pin
configuration, refer to Table 21-3. In master mode, a change of this bit will abort a transmission in progress and
force the SPI system into idle state.
0 SS port pin is not used by the SPI.
1 SS port pin with MODF feature.

3 Output Enable in the Bidirectional Mode of Operation — This bit controls the MOSI and MISO output buffer
BIDIROE of the SPI, when in bidirectional mode of operation (SPC0 is set). In master mode, this bit controls the output

buffer of the MOSI port, in slave mode it controls the output buffer of the MISO port. In master mode, with SPC0
set, a change of this bit will abort a transmission in progress and force the SPI into idle state.
0 Output buffer disabled.
1 Output buffer enabled.

1 SPI Stop in Wait Mode Bit — This bit is used for power conservation while in wait mode.
SPISWAI 0 SPI clock operates normally in wait mode.

1 Stop SPI clock generation when in wait mode.

0 Serial Pin Control Bit 0 — This bit enables bidirectional pin configurations as shown in Table 21-5. In master
SPC0 mode, a change of this bit will abort a transmission in progress and force the SPI system into idle state.

1. n is used later in this document as a placeholder for the selected transfer width.

Table 21-5. Bidirectional Pin Configurations

Pin Mode SPC0 BIDIROE MISO MOSI

Master Mode of Operation
Normal 0 X Master In Master Out

Bidirectional 1 0 MISO not used by SPI Master In
1 Master I/O

Slave Mode of Operation
Normal 0 X Slave Out Slave In

Bidirectional 1 0 Slave In MOSI not used by SPI
1 Slave I/O

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 767



Chapter 21 Serial Peripheral Interface (S12SPIV5)

******** SPI Baud Rate Register (SPIBR)
Module Base +0x0002

7 6 5 4 3 2 1 0
R 0 0

SPPR2 SPPR1 SPPR0 SPR2 SPR1 SPR0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 21-5. SPI Baud Rate Register (SPIBR)

Read: Anytime

Write: Anytime; writes to the reserved bits have no effect

Table 21-6. SPIBR Field Descriptions

Field Description

6–4 SPI Baud Rate Preselection Bits — These bits specify the SPI baud rates as shown in Table 21-7. In master
SPPR[2:0] mode, a change of these bits will abort a transmission in progress and force the SPI system into idle state.

2–0 SPI Baud Rate Selection Bits — These bits specify the SPI baud rates as shown in Table 21-7. In master mode,
SPR[2:0] a change of these bits will abort a transmission in progress and force the SPI system into idle state.

The baud rate divisor equation is as follows:
BaudRateDivisor = (SPPR + 1) • 2(SPR + 1) Eqn. 21-1

The baud rate can be calculated with the following equation:
Baud Rate = BusClock / BaudRateDivisor Eqn. 21-2

NOTE
For maximum allowed baud rates, please refer to the SPI Electrical
Specification in the Electricals chapter of this data sheet.

Table 21-7. Example SPI Baud Rate Selection (25 MHz Bus Clock)  (Sheet 1 of 3)

Baud Rate
SPPR2 SPPR1 SPPR0 SPR2 SPR1 SPR0 Baud Rate

Divisor
0 0 0 0 0 0 2 12.5 Mbit/s
0 0 0 0 0 1 4 6.25 Mbit/s
0 0 0 0 1 0 8 3.125 Mbit/s
0 0 0 0 1 1 16 1.5625 Mbit/s
0 0 0 1 0 0 32 781.25 kbit/s
0 0 0 1 0 1 64 390.63 kbit/s
0 0 0 1 1 0 128 195.31 kbit/s
0 0 0 1 1 1 256 97.66 kbit/s
0 0 1 0 0 0 4 6.25 Mbit/s
0 0 1 0 0 1 8 3.125 Mbit/s
0 0 1 0 1 0 16 1.5625 Mbit/s
0 0 1 0 1 1 32 781.25 kbit/s

MC9S12XE-Family Reference Manual  Rev. 1.25

768 Freescale Semiconductor



Chapter 21 Serial Peripheral Interface (S12SPIV5)

Table 21-7. Example SPI Baud Rate Selection (25 MHz Bus Clock)  (Sheet 2 of 3)

Baud Rate
SPPR2 SPPR1 SPPR0 SPR2 SPR1 SPR0 Baud Rate

Divisor
0 0 1 1 0 0 64 390.63 kbit/s
0 0 1 1 0 1 128 195.31 kbit/s
0 0 1 1 1 0 256 97.66 kbit/s
0 0 1 1 1 1 512 48.83 kbit/s
0 1 0 0 0 0 6 4.16667 Mbit/s
0 1 0 0 0 1 12 2.08333 Mbit/s
0 1 0 0 1 0 24 1.04167 Mbit/s
0 1 0 0 1 1 48 520.83 kbit/s
0 1 0 1 0 0 96 260.42 kbit/s
0 1 0 1 0 1 192 130.21 kbit/s
0 1 0 1 1 0 384 65.10 kbit/s
0 1 0 1 1 1 768 32.55 kbit/s
0 1 1 0 0 0 8 3.125 Mbit/s
0 1 1 0 0 1 16 1.5625 Mbit/s
0 1 1 0 1 0 32 781.25 kbit/s
0 1 1 0 1 1 64 390.63 kbit/s
0 1 1 1 0 0 128 195.31 kbit/s
0 1 1 1 0 1 256 97.66 kbit/s
0 1 1 1 1 0 512 48.83 kbit/s
0 1 1 1 1 1 1024 24.41 kbit/s
1 0 0 0 0 0 10 2.5 Mbit/s
1 0 0 0 0 1 20 1.25 Mbit/s
1 0 0 0 1 0 40 625 kbit/s
1 0 0 0 1 1 80 312.5 kbit/s
1 0 0 1 0 0 160 156.25 kbit/s
1 0 0 1 0 1 320 78.13 kbit/s
1 0 0 1 1 0 640 39.06 kbit/s
1 0 0 1 1 1 1280 19.53 kbit/s
1 0 1 0 0 0 12 2.08333 Mbit/s
1 0 1 0 0 1 24 1.04167 Mbit/s
1 0 1 0 1 0 48 520.83 kbit/s
1 0 1 0 1 1 96 260.42 kbit/s
1 0 1 1 0 0 192 130.21 kbit/s
1 0 1 1 0 1 384 65.10 kbit/s
1 0 1 1 1 0 768 32.55 kbit/s
1 0 1 1 1 1 1536 16.28 kbit/s
1 1 0 0 0 0 14 1.78571 Mbit/s
1 1 0 0 0 1 28 892.86 kbit/s
1 1 0 0 1 0 56 446.43 kbit/s
1 1 0 0 1 1 112 223.21 kbit/s
1 1 0 1 0 0 224 111.61 kbit/s
1 1 0 1 0 1 448 55.80 kbit/s

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 769



Chapter 21 Serial Peripheral Interface (S12SPIV5)

Table 21-7. Example SPI Baud Rate Selection (25 MHz Bus Clock)  (Sheet 3 of 3)

Baud Rate
SPPR2 SPPR1 SPPR0 SPR2 SPR1 SPR0 Baud Rate

Divisor
1 1 0 1 1 0 896 27.90 kbit/s
1 1 0 1 1 1 1792 13.95 kbit/s
1 1 1 0 0 0 16 1.5625 Mbit/s
1 1 1 0 0 1 32 781.25 kbit/s
1 1 1 0 1 0 64 390.63 kbit/s
1 1 1 0 1 1 128 195.31 kbit/s
1 1 1 1 0 0 256 97.66 kbit/s
1 1 1 1 0 1 512 48.83 kbit/s
1 1 1 1 1 0 1024 24.41 kbit/s
1 1 1 1 1 1 2048 12.21 kbit/s

******** SPI Status Register (SPISR)
Module Base +0x0003

7 6 5 4 3 2 1 0
R SPIF 0 SPTEF MODF 0 0 0 0
W

Reset 0 0 1 0 0 0 0 0
= Unimplemented or Reserved

Figure 21-6. SPI Status Register (SPISR)

Read: Anytime

Write: Has no effect

Table 21-8. SPISR Field Descriptions

Field Description

7 SPIF Interrupt Flag — This bit is set after received data has been transferred into the SPI data register. For
SPIF information about clearing SPIF Flag, please refer to Table 21-9.

0 Transfer not yet complete.
1 New data copied to SPIDR.

5 SPI Transmit Empty Interrupt Flag — If set, this bit indicates that the transmit data register is empty. For
SPTEF information about clearing this bit and placing data into the transmit data register, please refer to Table 21-10.

0 SPI data register not empty.
1 SPI data register empty.

4 Mode Fault Flag — This bit is set if the SS input becomes low while the SPI is configured as a master and mode
MODF fault detection is enabled, MODFEN bit of SPICR2 register is set. Refer to MODFEN bit description in

Section ********, “SPI Control Register 2 (SPICR2)”. The flag is cleared automatically by a read of the SPI status
register (with MODF set) followed by a write to the SPI control register 1.
0 Mode fault has not occurred.
1 Mode fault has occurred.

MC9S12XE-Family Reference Manual  Rev. 1.25

770 Freescale Semiconductor



Chapter 21 Serial Peripheral Interface (S12SPIV5)

Table 21-9. SPIF Interrupt Flag Clearing Sequence

XFRW Bit SPIF Interrupt Flag Clearing Sequence

0 Read SPISR with SPIF == 1 then Read SPIDRL

1 Read SPISR with SPIF == 1 Byte Read SPIDRL (1)

or

then Byte Read SPIDRH (2) Byte Read SPIDRL

or

Word Read (SPIDRH:SPIDRL)
1. Data in SPIDRH is lost in this case.
2. SPIDRH can be read repeatedly without any effect on SPIF. SPIF Flag is cleared only by the read

of SPIDRL after reading SPISR with SPIF == 1.

Table 21-10. SPTEF Interrupt Flag Clearing Sequence

XFRW Bit SPTEF Interrupt Flag Clearing Sequence

0 Read SPISR with SPTEF == 1 then Write to SPIDRL (1)

1 Read SPISR with SPTEF == 1 Byte Write to SPIDRL 1(2)

or

thenByte Write to SPIDRH 1(3) Byte Write to SPIDRL 1

or

Word Write to (SPIDRH:SPIDRL) 1

1. Any write to SPIDRH or SPIDRL with SPTEF == 0 is effectively ignored.
2. Data in SPIDRH is undefined in this case.
3. SPIDRH can be written repeatedly without any effect on SPTEF. SPTEF Flag is cleared only by

writing to SPIDRL after reading SPISR with SPTEF == 1.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 771



Chapter 21 Serial Peripheral Interface (S12SPIV5)

******** SPI Data Register (SPIDR = SPIDRH:SPIDRL)
Module Base +0x0004

7 6 5 4 3 2 1 0
R R15 R14 R13 R12 R11 R10 R9 R8
W T15 T14 T13 T12 T11 T10 T9 T8

Reset 0 0 0 0 0 0 0 0

Figure 21-7. SPI Data Register High (SPIDRH)

Module Base +0x0005

7 6 5 4 3 2 1 0
R R7 R6 R5 R4 R3 R2 R1 R0
W T7 T6 T5 T4 T3 T2 T1 T0

Reset 0 0 0 0 0 0 0 0

Figure 21-8. SPI Data Register Low (SPIDRL)

Read: Anytime; read data only valid when SPIF is set

Write: Anytime
The SPI data register is both the input and output register for SPI data. A write to this register
allows data to be queued and transmitted. For an SPI configured as a master, queued data is
transmitted immediately after the previous transmission has completed. The SPI transmitter empty
flag SPTEF in the SPISR register indicates when the SPI data register is ready to accept new data.
Received data in the SPIDR is valid when SPIF is set.
If SPIF is cleared and data has been received, the received data is transferred from the receive shift
register to the SPIDR and SPIF is set.
If SPIF is set and not serviced, and a second data value has been received, the second received data
is kept as valid data in the receive shift register until the start of another transmission. The data in
the SPIDR does not change.
If SPIF is set and valid data is in the receive shift register, and SPIF is serviced before the start of
a third transmission, the data in the receive shift register is transferred into the SPIDR and SPIF
remains set (see Figure 21-9).
If SPIF is set and valid data is in the receive shift register, and SPIF is serviced after the start of a
third transmission, the data in the receive shift register has become invalid and is not transferred
into the SPIDR (see Figure 21-10).

MC9S12XE-Family Reference Manual  Rev. 1.25

772 Freescale Semiconductor



Chapter 21 Serial Peripheral Interface (S12SPIV5)

Data A Received Data B Received Data C Received

SPIF Serviced

Receive Shift Register Data A Data B Data C

SPIF

SPI Data Register Data A Data B Data C

= Unspecified = Reception in progress

Figure 21-9. Reception with SPIF serviced in Time

Data A Received Data B Received Data C Received
Data B Lost

SPIF Serviced

Receive Shift Register Data A Data B Data C

SPIF

SPI Data Register Data A Data C

= Unspecified = Reception in progress

Figure 21-10. Reception with SPIF serviced too late

21.4 Functional Description
The SPI module allows a duplex, synchronous, serial communication between the MCU and peripheral
devices. Software can poll the SPI status flags or SPI operation can be interrupt driven.

The SPI system is enabled by setting the SPI enable (SPE) bit in SPI control register 1. While SPE is set,
the four associated SPI port pins are dedicated to the SPI function as:

• Slave select (SS)
• Serial clock (SCK)
• Master out/slave in (MOSI)
• Master in/slave out (MISO)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 773



Chapter 21 Serial Peripheral Interface (S12SPIV5)

The main element of the SPI system is the SPI data register. The n-bit1 data register in the master and the
n-bit1 data register in the slave are linked by the MOSI and MISO pins to form a distributed 2n-bit1
register. When a data transfer operation is performed, this 2n-bit1 register is serially shifted n1 bit positions
by the S-clock from the master, so data is exchanged between the master and the slave. Data written to the
master SPI data register becomes the output data for the slave, and data read from the master SPI data
register after a transfer operation is the input data from the slave.

A read of SPISR with SPTEF = 1 followed by a write to SPIDR puts data into the transmit data register.
When a transfer is complete and SPIF is cleared, received data is moved into the receive data register. This
data register acts as the SPI receive data register for reads and as the SPI transmit data register for writes.
A common SPI data register address is shared for reading data from the read data buffer and for writing
data to the transmit data register.

The clock phase control bit (CPHA) and a clock polarity control bit (CPOL) in the SPI control register 1
(SPICR1) select one of four possible clock formats to be used by the SPI system. The CPOL bit simply
selects a non-inverted or inverted clock. The CPHA bit is used to accommodate two fundamentally
different protocols by sampling data on odd numbered SCK edges or on even numbered SCK edges (see
Section 21.4.3, “Transmission Formats”).

The SPI can be configured to operate as a master or as a slave. When the MSTR bit in SPI control register1
is set, master mode is selected, when the MSTR bit is clear, slave mode is selected.

NOTE
A change of CPOL or MSTR bit while there is a received byte pending in
the receive shift register will destroy the received byte and must be avoided.

21.4.1 Master Mode
The SPI operates in master mode when the MSTR bit is set. Only a master SPI module can initiate
transmissions. A transmission begins by writing to the master SPI data register. If the shift register is
empty, data immediately transfers to the shift register. Data begins shifting out on the MOSI pin under the
control of the serial clock.

• Serial clock
The SPR2, SPR1, and SPR0 baud rate selection bits, in conjunction with the SPPR2, SPPR1, and
SPPR0 baud rate preselection bits in the SPI baud rate register, control the baud rate generator and
determine the speed of the transmission. The SCK pin is the SPI clock output. Through the SCK
pin, the baud rate generator of the master controls the shift register of the slave peripheral.

• MOSI, MISO pin
In master mode, the function of the serial data output pin (MOSI) and the serial data input pin
(MISO) is determined by the SPC0 and BIDIROE control bits.

• SS pin
If MODFEN and SSOE are set, the SS pin is configured as slave select output. The SS output
becomes low during each transmission and is high when the SPI is in idle state.
If MODFEN is set and SSOE is cleared, the SS pin is configured as input for detecting mode fault
error. If the SS input becomes low this indicates a mode fault error where another master tries to

1. n depends on the selected transfer width, please refer to Section ********, “SPI Control Register 2 (SPICR2)

MC9S12XE-Family Reference Manual  Rev. 1.25

774 Freescale Semiconductor



Chapter 21 Serial Peripheral Interface (S12SPIV5)

drive the MOSI and SCK lines. In this case, the SPI immediately switches to slave mode, by
clearing the MSTR bit and also disables the slave output buffer MISO (or SISO in bidirectional
mode). So the result is that all outputs are disabled and SCK, MOSI, and MISO are inputs. If a
transmission is in progress when the mode fault occurs, the transmission is aborted and the SPI is
forced into idle state.
This mode fault error also sets the mode fault (MODF) flag in the SPI status register (SPISR). If
the SPI interrupt enable bit (SPIE) is set when the MODF flag becomes set, then an SPI interrupt
sequence is also requested.
When a write to the SPI data register in the master occurs, there is a half SCK-cycle delay. After
the delay, SCK is started within the master. The rest of the transfer operation differs slightly,
depending on the clock format specified by the SPI clock phase bit, CPHA, in SPI control register 1
(see Section 21.4.3, “Transmission Formats”).

NOTE
A change of the bits CPOL, CPHA, SSOE, LSBFE, XFRW, MODFEN,
SPC0, or BIDIROE with SPC0 set, SPPR2-SPPR0 and SPR2-SPR0 in
master mode will abort a transmission in progress and force the SPI into idle
state. The remote slave cannot detect this, therefore the master must ensure
that the remote slave is returned to idle state.

21.4.2 Slave Mode
The SPI operates in slave mode when the MSTR bit in SPI control register 1 is clear.

• Serial clock
In slave mode, SCK is the SPI clock input from the master.

• MISO, MOSI pin
In slave mode, the function of the serial data output pin (MISO) and serial data input pin (MOSI)
is determined by the SPC0 bit and BIDIROE bit in SPI control register 2.

• SS pin
The SS pin is the slave select input. Before a data transmission occurs, the SS pin of the slave SPI
must be low. SS must remain low until the transmission is complete. If SS goes high, the SPI is
forced into idle state.
The SS input also controls the serial data output pin, if SS is high (not selected), the serial data
output pin is high impedance, and, if SS is low, the first bit in the SPI data register is driven out of
the serial data output pin. Also, if the slave is not selected (SS is high), then the SCK input is
ignored and no internal shifting of the SPI shift register occurs.
Although the SPI is capable of duplex operation, some SPI peripherals are capable of only
receiving SPI data in a slave mode. For these simpler devices, there is no serial data out pin.

NOTE
When peripherals with duplex capability are used, take care not to
simultaneously enable two receivers whose serial outputs drive the same
system slave’s serial data output line.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 775



Chapter 21 Serial Peripheral Interface (S12SPIV5)

As long as no more than one slave device drives the system slave’s serial data output line, it is possible for
several slaves to receive the same transmission from a master, although the master would not receive return
information from all of the receiving slaves.

If the CPHA bit in SPI control register 1 is clear, odd numbered edges on the SCK input cause the data at
the serial data input pin to be latched. Even numbered edges cause the value previously latched from the
serial data input pin to shift into the LSB or MSB of the SPI shift register, depending on the LSBFE bit.

If the CPHA bit is set, even numbered edges on the SCK input cause the data at the serial data input pin to
be latched. Odd numbered edges cause the value previously latched from the serial data input pin to shift
into the LSB or MSB of the SPI shift register, depending on the LSBFE bit.

When CPHA is set, the first edge is used to get the first data bit onto the serial data output pin. When CPHA
is clear and the SS input is low (slave selected), the first bit of the SPI data is driven out of the serial data
output pin. After the nth1 shift, the transfer is considered complete and the received data is transferred into
the SPI data register. To indicate transfer is complete, the SPIF flag in the SPI status register is set.

NOTE
A change of the bits CPOL, CPHA, SSOE, LSBFE, MODFEN, SPC0, or
BIDIROE with SPC0 set in slave mode will corrupt a transmission in
progress and must be avoided.

21.4.3 Transmission Formats
During an SPI transmission, data is transmitted (shifted out serially) and received (shifted in serially)
simultaneously. The serial clock (SCK) synchronizes shifting and sampling of the information on the two
serial data lines. A slave select line allows selection of an individual slave SPI device; slave devices that
are not selected do not interfere with SPI bus activities. Optionally, on a master SPI device, the slave select
line can be used to indicate multiple-master bus contention.

MASTER SPI SLAVE SPI

MISO MISO
 SHIFT REGISTER

MOSI MOSI
 SHIFT REGISTER

SCK SCK

BAUD RATE
GENERATOR SS SS

VDD

Figure 21-11. Master/Slave Transfer Block Diagram

******** Clock Phase and Polarity Controls
Using two bits in the SPI control register 1, software selects one of four combinations of serial clock phase
and polarity.

1. n depends on the selected transfer width, please refer to Section ********, “SPI Control Register 2 (SPICR2)

MC9S12XE-Family Reference Manual  Rev. 1.25

776 Freescale Semiconductor



Chapter 21 Serial Peripheral Interface (S12SPIV5)

The CPOL clock polarity control bit specifies an active high or low clock and has no significant effect on
the transmission format.

The CPHA clock phase control bit selects one of two fundamentally different transmission formats.

Clock phase and polarity should be identical for the master SPI device and the communicating slave
device. In some cases, the phase and polarity are changed between transmissions to allow a master device
to communicate with peripheral slaves having different requirements.

******** CPHA = 0 Transfer Format
The first edge on the SCK line is used to clock the first data bit of the slave into the master and the first
data bit of the master into the slave. In some peripherals, the first bit of the slave’s data is available at the
slave’s data out pin as soon as the slave is selected. In this format, the first SCK edge is issued a half cycle
after SS has become low.

A half SCK cycle later, the second edge appears on the SCK line. When this second edge occurs, the value
previously latched from the serial data input pin is shifted into the LSB or MSB of the shift register,
depending on LSBFE bit.

After this second edge, the next bit of the SPI master data is transmitted out of the serial data output pin of
the master to the serial input pin on the slave. This process continues for a total of 16 edges on the SCK
line, with data being latched on odd numbered edges and shifted on even numbered edges.

Data reception is double buffered. Data is shifted serially into the SPI shift register during the transfer and
is transferred to the parallel SPI data register after the last bit is shifted in.

After 2n1 (last) SCK edges:
• Data that was previously in the master SPI data register should now be in the slave data register and

the data that was in the slave data register should be in the master.
• The SPIF flag in the SPI status register is set, indicating that the transfer is complete.

Figure 21-12 is a timing diagram of an SPI transfer where CPHA = 0. SCK waveforms are shown for
CPOL = 0 and CPOL = 1. The diagram may be interpreted as a master or slave timing diagram because
the SCK, MISO, and MOSI pins are connected directly between the master and the slave. The MISO signal
is the output from the slave and the MOSI signal is the output from the master. The SS pin of the master
must be either high or reconfigured as a general-purpose output not affecting the SPI.

1. n depends on the selected transfer width, please refer to Section ********, “SPI Control Register 2 (SPICR2)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 777



Chapter 21 Serial Peripheral Interface (S12SPIV5)

End of Idle State Begin Transfer End Begin of Idle State

SCK Edge Number 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16

SCK (CPOL = 0)

SCK (CPOL = 1)

SAMPLE I
MOSI/MISO

CHANGE O
MOSI pin

CHANGE O
MISO pin

SEL SS (O)
Master only

SEL SS (I)

tL tT tI tL
MSB first (LSBFE = 0): MSB Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 LSB Minimum 1/2 SCK
 LSB first (LSBFE = 1): LSB Bit 1 Bit 2 Bit 3 Bit 4 Bit 5 Bit 6 MSB for tT, tl, tL
tL = Minimum leading time before the first SCK edge
tT = Minimum trailing time after the last SCK edge
tI = Minimum idling time between transfers (minimum SS high time)
tL, tT, and tI are guaranteed for the master mode and required for the slave mode.

Figure 21-12. SPI Clock Format 0 (CPHA = 0), with 8-bit Transfer Width selected (XFRW = 0)

MC9S12XE-Family Reference Manual  Rev. 1.25

778 Freescale Semiconductor

If next transfer begins here



Chapter 21 Serial Peripheral Interface (S12SPIV5)

End of Idle State Begin Transfer End Begin of Idle State

SCK Edge Number 1 3 5 7 9 11 13 15 17 19 21 23 25 27 29 31
2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32

SCK (CPOL = 0)

SCK (CPOL = 1)

SAMPLE I
MOSI/MISO

CHANGE O
MOSI pin

CHANGE O
MISO pin

SEL SS (O)
Master only

SEL SS (I)

tL tT tI tL
MSB first (LSBFE = 0) MSB Bit 14Bit 13Bit 12Bit 11Bit 10 Bit 9 Bit 8 Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 LSB Minimum 1/2 SCK
LSB first (LSBFE = 1) LSB Bit 1 Bit 2 Bit 3 Bit 4 Bit 5 Bit 6 Bit 7 Bit 8 Bit 9 Bit 10Bit 11Bit 12Bit 13Bit 14 MSB for tT, tl, tL

tL = Minimum leading time before the first SCK edge
tT = Minimum trailing time after the last SCK edge
tI = Minimum idling time between transfers (minimum SS high time)
tL, tT, and tI are guaranteed for the master mode and required for the slave mode.

Figure 21-13. SPI Clock Format 0 (CPHA = 0), with 16-Bit Transfer Width selected (XFRW = 1)

In slave mode, if the SS line is not deasserted between the successive transmissions then the content of the
SPI data register is not transmitted; instead the last received data is transmitted. If the SS line is deasserted
for at least minimum idle time (half SCK cycle) between successive transmissions, then the content of the
SPI data register is transmitted.

In master mode, with slave select output enabled the SS line is always deasserted and reasserted between
successive transfers for at least minimum idle time.

******** CPHA = 1 Transfer Format
Some peripherals require the first SCK edge before the first data bit becomes available at the data out pin,
the second edge clocks data into the system. In this format, the first SCK edge is issued by setting the
CPHA bit at the beginning of the n1-cycle transfer operation.

The first edge of SCK occurs immediately after the half SCK clock cycle synchronization delay. This first
edge commands the slave to transfer its first data bit to the serial data input pin of the master.

A half SCK cycle later, the second edge appears on the SCK pin. This is the latching edge for both the
master and slave.

1. n depends on the selected transfer width, please refer to Section ********, “SPI Control Register 2 (SPICR2)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 779

If next transfer begins here



Chapter 21 Serial Peripheral Interface (S12SPIV5)

When the third edge occurs, the value previously latched from the serial data input pin is shifted into the
LSB or MSB of the SPI shift register, depending on LSBFE bit. After this edge, the next bit of the master
data is coupled out of the serial data output pin of the master to the serial input pin on the slave.

This process continues for a total of n1 edges on the SCK line with data being latched on even numbered
edges and shifting taking place on odd numbered edges.

Data reception is double buffered, data is serially shifted into the SPI shift register during the transfer and
is transferred to the parallel SPI data register after the last bit is shifted in.

After 2n1 SCK edges:
• Data that was previously in the SPI data register of the master is now in the data register of the

slave, and data that was in the data register of the slave is in the master.
• The SPIF flag bit in SPISR is set indicating that the transfer is complete.

Figure 21-14 shows two clocking variations for CPHA = 1. The diagram may be interpreted as a master or
slave timing diagram because the SCK, MISO, and MOSI pins are connected directly between the master
and the slave. The MISO signal is the output from the slave, and the MOSI signal is the output from the
master. The SS line is the slave select input to the slave. The SS pin of the master must be either high or
reconfigured as a general-purpose output not affecting the SPI.

End of Idle State Begin Transfer End Begin of Idle State

SCK Edge Number 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16

SCK (CPOL = 0)

SCK (CPOL = 1)

SAMPLE I
MOSI/MISO

CHANGE O
MOSI pin

CHANGE O
MISO pin

SEL SS (O)
Master only

SEL SS (I)

tL tT tI tL
MSB first (LSBFE = 0): MSB Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 LSB Minimum 1/2 SCK
 LSB first (LSBFE = 1): LSB Bit 1 Bit 2 Bit 3 Bit 4 Bit 5 Bit 6 MSB for tT, tl, tL
tL = Minimum leading time before the first SCK edge, not required for back-to-back transfers
tT = Minimum trailing time after the last SCK edge
tI = Minimum idling time between transfers (minimum SS high time), not required for back-to-back transfers

Figure 21-14. SPI Clock Format 1 (CPHA = 1), with 8-Bit Transfer Width selected (XFRW = 0)

MC9S12XE-Family Reference Manual  Rev. 1.25

780 Freescale Semiconductor

If next transfer begins here



Chapter 21 Serial Peripheral Interface (S12SPIV5)

End of Idle State Begin Transfer End Begin of Idle State

SCK Edge Number 1 3 5 7 9 11 13 15 17 19 21 23 25 27 29 31
2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32

SCK (CPOL = 0)

SCK (CPOL = 1)

SAMPLE I
MOSI/MISO

CHANGE O
MOSI pin

CHANGE O
MISO pin

SEL SS (O)
Master only

SEL SS (I)

tL tT tI tL
MSB first (LSBFE = 0) MSB Bit 14Bit 13Bit 12Bit 11Bit 10 Bit 9 Bit 8 Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 LSB Minimum 1/2 SCK
LSB first (LSBFE = 1) LSB Bit 1 Bit 2 Bit 3 Bit 4 Bit 5 Bit 6 Bit 7 Bit 8 Bit 9 Bit 10Bit 11Bit 12Bit 13Bit 14 MSB for tT, tl, tL

tL = Minimum leading time before the first SCK edge, not required for back-to-back transfers
tT = Minimum trailing time after the last SCK edge
tI = Minimum idling time between transfers (minimum SS high time), not required for back-to-back transfers

Figure 21-15. SPI Clock Format 1 (CPHA = 1), with 16-Bit Transfer Width selected (XFRW = 1)

The SS line can remain active low between successive transfers (can be tied low at all times). This format
is sometimes preferred in systems having a single fixed master and a single slave that drive the MISO data
line.

• Back-to-back transfers in master mode
In master mode, if a transmission has completed and new data is available in the SPI data register,
this data is sent out immediately without a trailing and minimum idle time.

The SPI interrupt request flag (SPIF) is common to both the master and slave modes. SPIF gets set one
half SCK cycle after the last SCK edge.

21.4.4 SPI Baud Rate Generation
Baud rate generation consists of a series of divider stages. Six bits in the SPI baud rate register (SPPR2,
SPPR1, SPPR0, SPR2, SPR1, and SPR0) determine the divisor to the SPI module clock which results in
the SPI baud rate.

The SPI clock rate is determined by the product of the value in the baud rate preselection bits
(SPPR2–SPPR0) and the value in the baud rate selection bits (SPR2–SPR0). The module clock divisor
equation is shown in Equation 21-3.

BaudRateDivisor = (SPPR + 1) • 2(SPR + 1) Eqn. 21-3

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 781

If next transfer begins here



Chapter 21 Serial Peripheral Interface (S12SPIV5)

When all bits are clear (the default condition), the SPI module clock is divided by 2. When the selection
bits (SPR2–SPR0) are 001 and the preselection bits (SPPR2–SPPR0) are 000, the module clock divisor
becomes 4. When the selection bits are 010, the module clock divisor becomes 8, etc.

When the preselection bits are 001, the divisor determined by the selection bits is multiplied by 2. When
the preselection bits are 010, the divisor is multiplied by 3, etc. See Table 21-7 for baud rate calculations
for all bit conditions, based on a 25 MHz bus clock. The two sets of selects allows the clock to be divided
by a non-power of two to achieve other baud rates such as divide by 6, divide by 10, etc.

The baud rate generator is activated only when the SPI is in master mode and a serial transfer is taking
place. In the other cases, the divider is disabled to decrease IDD current.

NOTE
For maximum allowed baud rates, please refer to the SPI Electrical
Specification in the Electricals chapter of this data sheet.

21.4.5 Special Features

******** SS Output
The SS output feature automatically drives the SS pin low during transmission to select external devices
and drives it high during idle to deselect external devices. When SS output is selected, the SS output pin
is connected to the SS input pin of the external device.

The SS output is available only in master mode during normal SPI operation by asserting SSOE and
MODFEN bit as shown in Table 21-3.

The mode fault feature is disabled while SS output is enabled.

NOTE
Care must be taken when using the SS output feature in a multimaster
system because the mode fault feature is not available for detecting system
errors between masters.

21.4.5.2 Bidirectional Mode (MOMI or SISO)
The bidirectional mode is selected when the SPC0 bit is set in SPI control register 2 (see Table 21-11). In
this mode, the SPI uses only one serial data pin for the interface with external device(s). The MSTR bit
decides which pin to use. The MOSI pin becomes the serial data I/O (MOMI) pin for the master mode, and
the MISO pin becomes serial data I/O (SISO) pin for the slave mode. The MISO pin in master mode and
MOSI pin in slave mode are not used by the SPI.

MC9S12XE-Family Reference Manual  Rev. 1.25

782 Freescale Semiconductor



Chapter 21 Serial Peripheral Interface (S12SPIV5)

Table 21-11. Normal Mode and Bidirectional Mode

When SPE = 1 Master Mode MSTR = 1 Slave Mode MSTR = 0

Serial Out MOSI Serial In MOSI
Normal Mode

SPC0 = 0 SPI SPI

Serial In MISO Serial Out MISO

Serial Out MOMI Serial In
Bidirectional Mode BIDIROE

SPC0 = 1 SPI SPI
BIDIROE

Serial In Serial Out SISO

The direction of each serial I/O pin depends on the BIDIROE bit. If the pin is configured as an output,
serial data from the shift register is driven out on the pin. The same pin is also the serial input to the shift
register.

• The SCK is output for the master mode and input for the slave mode.
• The SS is the input or output for the master mode, and it is always the input for the slave mode.
• The bidirectional mode does not affect SCK and SS functions.

NOTE
In bidirectional master mode, with mode fault enabled, both data pins MISO
and MOSI can be occupied by the SPI, though MOSI is normally used for
transmissions in bidirectional mode and MISO is not used by the SPI. If a
mode fault occurs, the SPI is automatically switched to slave mode. In this
case MISO becomes occupied by the SPI and MOSI is not used. This must
be considered, if the MISO pin is used for another purpose.

21.4.6 Error Conditions
The SPI has one error condition:

• Mode fault error

21.4.6.1 Mode Fault Error
If the SS input becomes low while the SPI is configured as a master, it indicates a system error where more
than one master may be trying to drive the MOSI and SCK lines simultaneously. This condition is not
permitted in normal operation, the MODF bit in the SPI status register is set automatically, provided the
MODFEN bit is set.

In the special case where the SPI is in master mode and MODFEN bit is cleared, the SS pin is not used by
the SPI. In this special case, the mode fault error function is inhibited and MODF remains cleared. In case

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 783



Chapter 21 Serial Peripheral Interface (S12SPIV5)

the SPI system is configured as a slave, the SS pin is a dedicated input pin. Mode fault error doesn’t occur
in slave mode.

If a mode fault error occurs, the SPI is switched to slave mode, with the exception that the slave output
buffer is disabled. So SCK, MISO, and MOSI pins are forced to be high impedance inputs to avoid any
possibility of conflict with another output driver. A transmission in progress is aborted and the SPI is
forced into idle state.

If the mode fault error occurs in the bidirectional mode for a SPI system configured in master mode, output
enable of the MOMI (MOSI in bidirectional mode) is cleared if it was set. No mode fault error occurs in
the bidirectional mode for SPI system configured in slave mode.

The mode fault flag is cleared automatically by a read of the SPI status register (with MODF set) followed
by a write to SPI control register 1. If the mode fault flag is cleared, the SPI becomes a normal master or
slave again.

NOTE
If a mode fault error occurs and a received data byte is pending in the receive
shift register, this data byte will be lost.

21.4.7 Low Power Mode Options

21.4.7.1 SPI in Run Mode
In run mode with the SPI system enable (SPE) bit in the SPI control register clear, the SPI system is in a
low-power, disabled state. SPI registers remain accessible, but clocks to the core of this module are
disabled.

21.4.7.2 SPI in Wait Mode
SPI operation in wait mode depends upon the state of the SPISWAI bit in SPI control register 2.

• If SPISWAI is clear, the SPI operates normally when the CPU is in wait mode
• If SPISWAI is set, SPI clock generation ceases and the SPI module enters a power conservation

state when the CPU is in wait mode.
– If SPISWAI is set and the SPI is configured for master, any transmission and reception in

progress stops at wait mode entry. The transmission and reception resumes when the SPI exits
wait mode.

– If SPISWAI is set and the SPI is configured as a slave, any transmission and reception in
progress continues if the SCK continues to be driven from the master. This keeps the slave
synchronized to the master and the SCK.
If the master transmits several bytes while the slave is in wait mode, the slave will continue to
send out bytes consistent with the operation mode at the start of wait mode (i.e., if the slave is
currently sending its SPIDR to the master, it will continue to send the same byte. Else if the
slave is currently sending the last received byte from the master, it will continue to send each
previous master byte).

MC9S12XE-Family Reference Manual  Rev. 1.25

784 Freescale Semiconductor



Chapter 21 Serial Peripheral Interface (S12SPIV5)

NOTE
Care must be taken when expecting data from a master while the slave is in
wait or stop mode. Even though the shift register will continue to operate,
the rest of the SPI is shut down (i.e., a SPIF interrupt will not be generated
until exiting stop or wait mode). Also, the byte from the shift register will
not be copied into the SPIDR register until after the slave SPI has exited wait
or stop mode. In slave mode, a received byte pending in the receive shift
register will be lost when entering wait or stop mode. An SPIF flag and
SPIDR copy is generated only if wait mode is entered or exited during a
tranmission. If the slave enters wait mode in idle mode and exits wait mode
in idle mode, neither a SPIF nor a SPIDR copy will occur.

******** SPI in Stop Mode
Stop mode is dependent on the system. The SPI enters stop mode when the module clock is disabled (held
high or low). If the SPI is in master mode and exchanging data when the CPU enters stop mode, the
transmission is frozen until the CPU exits stop mode. After stop, data to and from the external SPI is
exchanged correctly. In slave mode, the SPI will stay synchronized with the master.

The stop mode is not dependent on the SPISWAI bit.

21.4.7.4 Reset
The reset values of registers and signals are described in Section 21.3, “Memory Map and Register
Definition”, which details the registers and their bit fields.

• If a data transmission occurs in slave mode after reset without a write to SPIDR, it will transmit
garbage, or the data last received from the master before the reset.

• Reading from the SPIDR after reset will always read zeros.

******** Interrupts
The SPI only originates interrupt requests when SPI is enabled (SPE bit in SPICR1 set). The following is
a description of how the SPI makes a request and how the MCU should acknowledge that request. The
interrupt vector offset and interrupt priority are chip dependent.

The interrupt flags MODF, SPIF, and SPTEF are logically ORed to generate an interrupt request.

********.1 MODF
MODF occurs when the master detects an error on the SS pin. The master SPI must be configured for the
MODF feature (see Table 21-3). After MODF is set, the current transfer is aborted and the following bit is
changed:

• MSTR = 0, The master bit in SPICR1 resets.

The MODF interrupt is reflected in the status register MODF flag. Clearing the flag will also clear the
interrupt. This interrupt will stay active while the MODF flag is set. MODF has an automatic clearing
process which is described in Section ********, “SPI Status Register (SPISR)”.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 785



Chapter 21 Serial Peripheral Interface (S12SPIV5)

********.2 SPIF
SPIF occurs when new data has been received and copied to the SPI data register. After SPIF is set, it does
not clear until it is serviced. SPIF has an automatic clearing process, which is described in
Section ********, “SPI Status Register (SPISR)”.

********.3 SPTEF
SPTEF occurs when the SPI data register is ready to accept new data. After SPTEF is set, it does not clear
until it is serviced. SPTEF has an automatic clearing process, which is described in Section ********, “SPI
Status Register (SPISR)”.

MC9S12XE-Family Reference Manual  Rev. 1.25

786 Freescale Semiconductor