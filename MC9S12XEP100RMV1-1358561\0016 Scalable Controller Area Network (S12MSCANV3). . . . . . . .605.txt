﻿Chapter 16
Freescale’s Scalable Controller Area Network
(S12MSCANV3)

Table 16-1. Revision History

Revision Sections
Revision Date Description of Changes

Number Affected

V03.13 03 Mar 2011 Figure 16-4  • Corrected CANE write restrictions
Table 16-3  • Removed footnote from RXFRM bit

V03.14 12 Nov 2012 Table 16-11  • Corrected RxWRN and TxWRN threshold values

V03.15 12 Jan 2013 Table 16-3  • Updated TIME bit description
Table 16-26  • Added register names to buffer map
Figure 16-37  • Updated TSRH and TSRL read conditions
16.1/16-605  • Updated introduction
********5/16-  • Updated CANTXERR and CANRXERR register notes

626

16.1 Introduction
Freescale’s scalable controller area network (S12MSCANV3) definition is based on the MSCAN12
definition, which is the specific implementation of the MSCAN concept targeted for the S12, S12X and
S12Z microcontroller families.

The module is a communication controller implementing the CAN 2.0A/B protocol as defined in the
Bosch specification dated September 1991. For users to fully understand the MSCAN specification, it is
recommended that the Bosch specification be read first to familiarize the reader with the terms and
concepts contained within this document.

Though not exclusively intended for automotive applications, CAN protocol is designed to meet the
specific requirements of a vehicle serial data bus: real-time processing, reliable operation in the EMI
environment of a vehicle, cost-effectiveness, and required bandwidth.

MSCAN uses an advanced buffer arrangement resulting in predictable real-time behavior and simplified
application software.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 605



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

16.1.1 Glossary
Table 16-2. Terminology

ACK Acknowledge of CAN message

CAN Controller Area Network

CRC Cyclic Redundancy Code

EOF End of Frame

FIFO First-In-First-Out Memory

IFS Inter-Frame Sequence

SOF Start of Frame

CPU bus CPU related read/write data bus

CAN bus CAN protocol related serial bus

oscillator clock Direct clock from external oscillator

bus clock CPU bus related clock

CAN clock CAN protocol related clock

16.1.2 Block Diagram

MSCAN
Oscillator Clock CANCLK Tq Clk

MUX Presc.
Bus Clock RXCAN

Receive/
Transmit
Engine

TXCAN

Transmit Interrupt Req.
Message

Receive Interrupt Req. Control Filtering
and

Errors Interrupt Req. and
Status Buffering

Wake-Up Interrupt Req.

Configuration
Registers Wake-Up

Low Pass Filter

Figure 16-1. MSCAN Block Diagram

MC9S12XE-Family Reference Manual  Rev. 1.25

606 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

16.1.3 Features
The basic features of the MSCAN are as follows:

• Implementation of the CAN protocol — Version 2.0A/B
— Standard and extended data frames
— Zero to eight bytes data length
— Programmable bit rate up to 1 Mbps1

— Support for remote frames
• Five receive buffers with FIFO storage scheme
• Three transmit buffers with internal prioritization using a “local priority” concept
• Flexible maskable identifier filter supports two full-size (32-bit) extended identifier filters, or four

16-bit filters, or eight 8-bit filters
• Programmable wake-up functionality with integrated low-pass filter
• Programmable loopback mode supports self-test operation
• Programmable listen-only mode for monitoring of CAN bus
• Programmable bus-off recovery functionality
• Separate signalling and interrupt capabilities for all CAN receiver and transmitter error states

(warning, error passive, bus-off)
• Programmable MSCAN clock source either bus clock or oscillator clock
• Internal timer for time-stamping of received and transmitted messages
• Three low-power modes: sleep, power down, and MSCAN enable
• Global initialization of configuration registers

16.1.4 Modes of Operation
For a description of the specific MSCAN modes and the module operation related to the system operating
modes refer to Section 16.4.4, “Modes of Operation”.

1. Depending on the actual bit timing and the clock jitter of the PLL.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 607



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

16.2 External Signal Description
The MSCAN uses two external pins.

NOTE
On MCUs with an integrated CAN physical interface (transceiver) the
MSCAN interface is connected internally to the transceiver interface. In
these cases the external availability of signals TXCAN and RXCAN is
optional.

16.2.1 RXCAN — CAN Receiver Input Pin
RXCAN is the MSCAN receiver input pin.

16.2.2 TXCAN — CAN Transmitter Output Pin
TXCAN is the MSCAN transmitter output pin. The TXCAN output pin represents the logic level on the
CAN bus:

0 = Dominant state
1 = Recessive state

16.2.3 CAN System
A typical CAN system with MSCAN is shown in Figure 16-2. Each CAN station is connected physically
to the CAN bus lines through a transceiver device. The transceiver is capable of driving the large current
needed for the CAN bus and has current protection against defective CAN or defective stations.

CAN node 1 CAN node 2 CAN node n

MCU

CAN Controller
(MSCAN)

TXCAN RXCAN

Transceiver

CANH CANL
CAN Bus

Figure 16-2. CAN System

MC9S12XE-Family Reference Manual  Rev. 1.25

608 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

16.3 Memory Map and Register Definition
This section provides a detailed description of all registers accessible in the MSCAN.

16.3.1 Module Memory Map
Figure 16-3 gives an overview on all registers and their individual bits in the MSCAN memory map. The
register address results from the addition of base address and address offset. The base address is
determined at the MCU level and can be found in the MCU memory map description. The address offset
is defined at the module level.

The MSCAN occupies 64 bytes in the memory space. The base address of the MSCAN module is
determined at the MCU level when the MCU is defined. The register decode map is fixed and begins at the
first address of the module address offset.

The detailed register descriptions follow in the order they appear in the register map.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 609



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0000 R RXACT SYNCH
CANCTL0 RXFRM CSWAI TIME WUPE SLPRQ INITRQ

W

0x0001 R SLPAK INITAK
CANCTL1 CANE CLKSRC LOOPB LISTEN BORM WUPM

W

0x0002 R
CANBTR0 SJW1 SJW0 BRP5 BRP4 BRP3 BRP2 BRP1 BRP0

W

0x0003 R
CANBTR1 SAMP TSEG22 TSEG21 TSEG20 TSEG13 TSEG12 TSEG11 TSEG10

W

0x0004 R RSTAT1 RSTAT0 TSTAT1 TSTAT0
CANRFLG WUPIF CSCIF OVRIF RXF

W

0x0005 R
CANRIER WUPIE CSCIE RSTATE1 RSTATE0 TSTATE1 TSTATE0 OVRIE RXFIE

W

0x0006 R 0 0 0 0 0
CANTFLG TXE2 TXE1 TXE0

W

0x0007 R 0 0 0 0 0
CANTIER TXEIE2 TXEIE1 TXEIE0

W

0x0008 R 0 0 0 0 0
CANTARQ ABTRQ2 ABTRQ1 ABTRQ0

W

0x0009 R 0 0 0 0 0 ABTAK2 ABTAK1 ABTAK0
CANTAAK W

0x000A R 0 0 0 0 0
CANTBSEL TX2 TX1 TX0

W

0x000B R 0 0 0 IDHIT2 IDHIT1 IDHIT0
CANIDAC IDAM1 IDAM0

W

0x000C R 0 0 0 0 0 0 0 0
Reserved W

0x000D R 0 0 0 0 0 0 0
CANMISC BOHOLD

W

0x000E R RXERR7 RXERR6 RXERR5 RXERR4 RXERR3 RXERR2 RXERR1 RXERR0
CANRXERR W

= Unimplemented or Reserved

Figure 16-3. MSCAN Register Summary
MC9S12XE-Family Reference Manual  Rev. 1.25

610 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x000F R TXERR7 TXERR6 TXERR5 TXERR4 TXERR3 TXERR2 TXERR1 TXERR0
CANTXERR W

0x0010–0x0013 R
CANIDAR0–3 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0

W

0x0014–0x0017 R
CANIDMRx AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0

W

0x0018–0x001B R
CANIDAR4–7 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0

W

0x001C–0x001F R
CANIDMR4–7 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0

W

0x0020–0x002F R
CANRXFG See Section 16.3.3, “Programmer’s Model of Message Storage”

W

0x0030–0x003F R
CANTXFG See Section 16.3.3, “Programmer’s Model of Message Storage”

W

= Unimplemented or Reserved

Figure 16-3. MSCAN Register Summary (continued)

16.3.2 Register Descriptions
This section describes in detail all the registers and register bits in the MSCAN module. Each description
includes a standard register diagram with an associated figure number. Details of register bit and field
function follow the register diagrams, in bit order. All bits of all registers in this module are completely
synchronous to internal clocks during a register read.

******** MSCAN Control Register 0 (CANCTL0)
The CANCTL0 register provides various control bits of the MSCAN module as described below.

Module Base + 0x0000 Access: User read/write(1)

7 6 5 4 3 2 1 0

R RXACT SYNCH
RXFRM CSWAI TIME WUPE SLPRQ INITRQ

W

Reset: 0 0 0 0 0 0 0 1

= Unimplemented

Figure 16-4. MSCAN Control Register 0 (CANCTL0)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 611



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

1. Read: Anytime
Write: Anytime when out of initialization mode; exceptions are read-only RXACT and SYNCH, RXFRM (which is set by the
module only), and INITRQ (which is also writable in initialization mode)

NOTE
The CANCTL0 register, except WUPE, INITRQ, and SLPRQ, is held in the
reset state when the initialization mode is active (INITRQ = 1 and
INITAK = 1). This register is writable again as soon as the initialization
mode is exited (INITRQ = 0 and INITAK = 0).

Table 16-3. CANCTL0 Register Field Descriptions

Field Description

7 Received Frame Flag — This bit is read and clear only. It is set when a receiver has received a valid message
RXFRM correctly, independently of the filter configuration. After it is set, it remains set until cleared by software or reset.

Clearing is done by writing a 1. Writing a 0 is ignored. This bit is not valid in loopback mode.
0 No valid message was received since last clearing this flag
1 A valid message was received since last clearing of this flag

6 Receiver Active Status — This read-only flag indicates the MSCAN is receiving a message(1). The flag is
RXACT controlled by the receiver front end. This bit is not valid in loopback mode.

0 MSCAN is transmitting or idle
1 MSCAN is receiving a message (including when arbitration is lost)

5 CAN Stops in Wait Mode — Enabling this bit allows for lower power consumption in wait mode by disabling all
CSWAI(2) the clocks at the CPU bus interface to the MSCAN module.

0 The module is not affected during wait mode
1 The module ceases to be clocked during wait mode

4 Synchronized Status — This read-only flag indicates whether the MSCAN is synchronized to the CAN bus and
SYNCH able to participate in the communication process. It is set and cleared by the MSCAN.

0 MSCAN is not synchronized to the CAN bus
1 MSCAN is synchronized to the CAN bus

3 Timer Enable — This bit activates an internal 16-bit wide free running timer which is clocked by the bit clock rate.
TIME If the timer is enabled, a 16-bit time stamp will be assigned to each transmitted/received message within the

active TX/RX buffer. Right after the EOF of a valid message on the CAN bus, the time stamp is written to the
highest bytes (0x000E, 0x000F) in the appropriate buffer (see Section 16.3.3, “Programmer’s Model of Message
Storage”). In loopback mode no receive timestamp is generated. The internal timer is reset (all bits set to 0) when
disabled. This bit is held low in initialization mode.
0 Disable internal MSCAN timer
1 Enable internal MSCAN timer

2 Wake-Up Enable — This configuration bit allows the MSCAN to restart from sleep mode or from power down
WUPE(3) mode (entered from sleep) when traffic on CAN is detected (see Section ********, “MSCAN Sleep Mode”). This

bit must be configured before sleep mode entry for the selected function to take effect.
0 Wake-up disabled — The MSCAN ignores traffic on CAN
1 Wake-up enabled — The MSCAN is able to restart

MC9S12XE-Family Reference Manual  Rev. 1.25

612 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Table 16-3. CANCTL0 Register Field Descriptions (continued)

Field Description

1 Sleep Mode Request — This bit requests the MSCAN to enter sleep mode, which is an internal power saving
SLPRQ(4) mode (see Section ********, “MSCAN Sleep Mode”). The sleep mode request is serviced when the CAN bus is

idle, i.e., the module is not receiving a message and all transmit buffers are empty. The module indicates entry
to sleep mode by setting SLPAK = 1 (see Section ********, “MSCAN Control Register 1 (CANCTL1)”). SLPRQ
cannot be set while the WUPIF flag is set (see Section ********, “MSCAN Receiver Flag Register (CANRFLG)”).
Sleep mode will be active until SLPRQ is cleared by the CPU or, depending on the setting of WUPE, the MSCAN
detects activity on the CAN bus and clears SLPRQ itself.
0 Running — The MSCAN functions normally
1 Sleep mode request — The MSCAN enters sleep mode when CAN bus idle

0 Initialization Mode Request — When this bit is set by the CPU, the MSCAN skips to initialization mode (see
INITRQ(5),(6) Section ********, “MSCAN Initialization Mode”). Any ongoing transmission or reception is aborted and

synchronization to the CAN bus is lost. The module indicates entry to initialization mode by setting INITAK = 1
(Section ********, “MSCAN Control Register 1 (CANCTL1)”).
The following registers enter their hard reset state and restore their default values: CANCTL0(7), CANRFLG(8),
CANRIER(9), CANTFLG, CANTIER, CANTARQ, CANTAAK, and CANTBSEL.
The registers CANCTL1, CANBTR0, CANBTR1, CANIDAC, CANIDAR0-7, and CANIDMR0-7 can only be
written by the CPU when the MSCAN is in initialization mode (INITRQ = 1 and INITAK = 1). The values of the
error counters are not affected by initialization mode.
When this bit is cleared by the CPU, the MSCAN restarts and then tries to synchronize to the CAN bus. If the
MSCAN is not in bus-off state, it synchronizes after 11 consecutive recessive bits on the CAN bus; if the MSCAN
is in bus-off state, it continues to wait for 128 occurrences of 11 consecutive recessive bits.
Writing to other bits in CANCTL0, CANRFLG, CANRIER, CANTFLG, or CANTIER must be done only after
initialization mode is exited, which is INITRQ = 0 and INITAK = 0.
0 Normal operation
1 MSCAN in initialization mode

1. See the Bosch CAN 2.0A/B specification for a detailed definition of transmitter and receiver states.
2. In order to protect from accidentally violating the CAN protocol, TXCAN is immediately forced to a recessive state when the

CPU enters wait (CSWAI = 1) or stop mode (see Section 16.4.5.2, “Operation in Wait Mode” and Section 16.4.5.3, “Operation
in Stop Mode”).

3. The CPU has to make sure that the WUPE register and the WUPIE wake-up interrupt enable register (see Section ********,
“MSCAN Receiver Interrupt Enable Register (CANRIER)) is enabled, if the recovery mechanism from stop or wait is required.

4. The CPU cannot clear SLPRQ before the MSCAN has entered sleep mode (SLPRQ = 1 and SLPAK = 1).
5. The CPU cannot clear INITRQ before the MSCAN has entered initialization mode (INITRQ = 1 and INITAK = 1).
6. In order to protect from accidentally violating the CAN protocol, TXCAN is immediately forced to a recessive state when the

initialization mode is requested by the CPU. Thus, the recommended procedure is to bring the MSCAN into sleep mode
(SLPRQ = 1 and SLPAK = 1) before requesting initialization mode.

7. Not including WUPE, INITRQ, and SLPRQ.
8. TSTAT1 and TSTAT0 are not affected by initialization mode.
9. RSTAT1 and RSTAT0 are not affected by initialization mode.

******** MSCAN Control Register 1 (CANCTL1)
The CANCTL1 register provides various control bits and handshake status information of the MSCAN
module as described below.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 613



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Module Base + 0x0001 Access: User read/write(1)

7 6 5 4 3 2 1 0

R SLPAK INITAK
CANE CLKSRC LOOPB LISTEN BORM WUPM

W

Reset: 0 0 0 1 0 0 0 1

= Unimplemented

Figure 16-5. MSCAN Control Register 1 (CANCTL1)
1. Read: Anytime

Write: Anytime in initialization mode (INITRQ = 1 and INITAK = 1), except CANE which is write once in normal and anytime in
special system operation modes when the MSCAN is in initialization mode (INITRQ = 1 and INITAK = 1)

Table 16-4. CANCTL1 Register Field Descriptions

Field Description

7 MSCAN Enable
CANE 0 MSCAN module is disabled

1 MSCAN module is enabled

6 MSCAN Clock Source — This bit defines the clock source for the MSCAN module (only for systems with a clock
CLKSRC generation module; Section ********, “Clock System,” and Section Figure 16-43., “MSCAN Clocking Scheme,”).

0 MSCAN clock source is the oscillator clock
1 MSCAN clock source is the bus clock

5 Loopback Self Test Mode — When this bit is set, the MSCAN performs an internal loopback which can be used
LOOPB for self test operation. The bit stream output of the transmitter is fed back to the receiver internally. The RXCAN

input is ignored and the TXCAN output goes to the recessive state (logic 1). The MSCAN behaves as it does
normally when transmitting and treats its own transmitted message as a message received from a remote node.
In this state, the MSCAN ignores the bit sent during the ACK slot in the CAN frame acknowledge field to ensure
proper reception of its own message. Both transmit and receive interrupts are generated.
0 Loopback self test disabled
1 Loopback self test enabled

4 Listen Only Mode — This bit configures the MSCAN as a CAN bus monitor. When LISTEN is set, all valid CAN
LISTEN messages with matching ID are received, but no acknowledgement or error frames are sent out (see

Section 16.4.4.4, “Listen-Only Mode”). In addition, the error counters are frozen. Listen only mode supports
applications which require “hot plugging” or throughput analysis. The MSCAN is unable to transmit any
messages when listen only mode is active.
0 Normal operation
1 Listen only mode activated

3 Bus-Off Recovery Mode — This bit configures the bus-off state recovery mode of the MSCAN. Refer to
BORM Section 16.5.2, “Bus-Off Recovery,” for details.

0 Automatic bus-off recovery (see Bosch CAN 2.0A/B protocol specification)
1 Bus-off recovery upon user request

2 Wake-Up Mode — If WUPE in CANCTL0 is enabled, this bit defines whether the integrated low-pass filter is
WUPM applied to protect the MSCAN from spurious wake-up (see Section ********, “MSCAN Sleep Mode”).

0 MSCAN wakes up on any dominant level on the CAN bus
1 MSCAN wakes up only in case of a dominant pulse on the CAN bus that has a length of Twup

MC9S12XE-Family Reference Manual  Rev. 1.25

614 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Table 16-4. CANCTL1 Register Field Descriptions (continued)

Field Description

1 Sleep Mode Acknowledge — This flag indicates whether the MSCAN module has entered sleep mode (see
SLPAK Section ********, “MSCAN Sleep Mode”). It is used as a handshake flag for the SLPRQ sleep mode request.

Sleep mode is active when SLPRQ = 1 and SLPAK = 1. Depending on the setting of WUPE, the MSCAN will
clear the flag if it detects activity on the CAN bus while in sleep mode.
0 Running — The MSCAN operates normally
1 Sleep mode active — The MSCAN has entered sleep mode

0 Initialization Mode Acknowledge — This flag indicates whether the MSCAN module is in initialization mode
INITAK (see Section ********, “MSCAN Initialization Mode”). It is used as a handshake flag for the INITRQ initialization

mode request. Initialization mode is active when INITRQ = 1 and INITAK = 1. The registers CANCTL1,
CANBTR0, CANBTR1, CANIDAC, CANIDAR0–CANIDAR7, and CANIDMR0–CANIDMR7 can be written only by
the CPU when the MSCAN is in initialization mode.
0 Running — The MSCAN operates normally
1 Initialization mode active — The MSCAN has entered initialization mode

******** MSCAN Bus Timing Register 0 (CANBTR0)
The CANBTR0 register configures various CAN bus timing parameters of the MSCAN module.

Module Base + 0x0002 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
SJW1 SJW0 BRP5 BRP4 BRP3 BRP2 BRP1 BRP0

W

Reset: 0 0 0 0 0 0 0 0

Figure 16-6. MSCAN Bus Timing Register 0 (CANBTR0)
1. Read: Anytime

Write: Anytime in initialization mode (INITRQ = 1 and INITAK = 1)

Table 16-5. CANBTR0 Register Field Descriptions

Field Description

7-6 Synchronization Jump Width — The synchronization jump width defines the maximum number of time quanta
SJW[1:0] (Tq) clock cycles a bit can be shortened or lengthened to achieve resynchronization to data transitions on the

CAN bus (see Table 16-6).

5-0 Baud Rate Prescaler — These bits determine the time quanta (Tq) clock which is used to build up the bit timing
BRP[5:0] (see Table 16-7).

Table 16-6. Synchronization Jump Width

SJW1 SJW0 Synchronization Jump Width

0 0 1 Tq clock cycle

0 1 2 Tq clock cycles

1 0 3 Tq clock cycles

1 1 4 Tq clock cycles

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 615



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Table 16-7. Baud Rate Prescaler

BRP5 BRP4 BRP3 BRP2 BRP1 BRP0 Prescaler value (P)

0 0 0 0 0 0 1

0 0 0 0 0 1 2

0 0 0 0 1 0 3

0 0 0 0 1 1 4

: : : : : : :

1 1 1 1 1 1 64

******** MSCAN Bus Timing Register 1 (CANBTR1)
The CANBTR1 register configures various CAN bus timing parameters of the MSCAN module.

Module Base + 0x0003 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
SAMP TSEG22 TSEG21 TSEG20 TSEG13 TSEG12 TSEG11 TSEG10

W

Reset: 0 0 0 0 0 0 0 0

Figure 16-7. MSCAN Bus Timing Register 1 (CANBTR1)
1. Read: Anytime

Write: Anytime in initialization mode (INITRQ = 1 and INITAK = 1)

Table 16-8. CANBTR1 Register Field Descriptions

Field Description

7 Sampling — This bit determines the number of CAN bus samples taken per bit time.
SAMP 0 One sample per bit.

1 Three samples per bit(1).
If SAMP = 0, the resulting bit value is equal to the value of the single bit positioned at the sample point. If
SAMP = 1, the resulting bit value is determined by using majority rule on the three total samples. For higher bit
rates, it is recommended that only one sample is taken per bit time (SAMP = 0).

6-4 Time Segment 2 — Time segments within the bit time fix the number of clock cycles per bit time and the location
TSEG2[2:0] of the sample point (see Figure 16-44). Time segment 2 (TSEG2) values are programmable as shown in

Table 16-9.

3-0 Time Segment 1 — Time segments within the bit time fix the number of clock cycles per bit time and the location
TSEG1[3:0] of the sample point (see Figure 16-44). Time segment 1 (TSEG1) values are programmable as shown in

Table 16-10.
1. In this case, PHASE_SEG1 must be at least 2 time quanta (Tq).

MC9S12XE-Family Reference Manual  Rev. 1.25

616 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Table 16-9. Time Segment 2 Values

TSEG22 TSEG21 TSEG20 Time Segment 2

0 0 0 1 Tq clock cycle(1)

0 0 1 2 Tq clock cycles

: : : :

1 1 0 7 Tq clock cycles

1 1 1 8 Tq clock cycles
1. This setting is not valid. Please refer to Table 16-37 for valid settings.

Table 16-10. Time Segment 1 Values

TSEG13 TSEG12 TSEG11 TSEG10 Time segment 1

0 0 0 0 1 Tq clock cycle(1)

0 0 0 1 2 Tq clock cycles1

0 0 1 0 3 Tq clock cycles1

0 0 1 1 4 Tq clock cycles

: : : : :

1 1 1 0 15 Tq clock cycles

1 1 1 1 16 Tq clock cycles
1. This setting is not valid. Please refer to Table 16-37 for valid settings.

The bit time is determined by the oscillator frequency, the baud rate prescaler, and the number of time
quanta (Tq) clock cycles per bit (as shown in Table 16-9 and Table 16-10).

Eqn. 16-1

Bit Time= (Prescaler value)
----------------------------------------------------- • (1 + TimeSegment1 + TimeSegment2)

fCANCLK

******** MSCAN Receiver Flag Register (CANRFLG)
A flag can be cleared only by software (writing a 1 to the corresponding bit position) when the condition
which caused the setting is no longer valid. Every flag has an associated interrupt enable bit in the
CANRIER register.

Module Base + 0x0004 Access: User read/write(1)

7 6 5 4 3 2 1 0

R RSTAT1 RSTAT0 TSTAT1 TSTAT0
WUPIF CSCIF OVRIF RXF

W

Reset: 0 0 0 0 0 0 0 0

= Unimplemented

Figure 16-8. MSCAN Receiver Flag Register (CANRFLG)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 617



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

1. Read: Anytime
Write: Anytime when not in initialization mode, except RSTAT[1:0] and TSTAT[1:0] flags which are read-only; write of 1 clears
flag; write of 0 is ignored

NOTE
The CANRFLG register is held in the reset state1 when the initialization
mode is active (INITRQ = 1 and INITAK = 1). This register is writable again
as soon as the initialization mode is exited (INITRQ = 0 and INITAK = 0).

Table 16-11. CANRFLG Register Field Descriptions

Field Description

7 Wake-Up Interrupt Flag — If the MSCAN detects CAN bus activity while in sleep mode (see Section ********,
WUPIF “MSCAN Sleep Mode,”) and WUPE = 1 in CANTCTL0 (see Section ********, “MSCAN Control Register 0

(CANCTL0)”), the module will set WUPIF. If not masked, a wake-up interrupt is pending while this flag is set.
0 No wake-up activity observed while in sleep mode
1 MSCAN detected activity on the CAN bus and requested wake-up

6 CAN Status Change Interrupt Flag — This flag is set when the MSCAN changes its current CAN bus status
CSCIF due to the actual value of the transmit error counter (TEC) and the receive error counter (REC). An additional 4-

bit (RSTAT[1:0], TSTAT[1:0]) status register, which is split into separate sections for TEC/REC, informs the
system on the actual CAN bus status (see Section ********, “MSCAN Receiver Interrupt Enable Register
(CANRIER)”). If not masked, an error interrupt is pending while this flag is set. CSCIF provides a blocking
interrupt. That guarantees that the receiver/transmitter status bits (RSTAT/TSTAT) are only updated when no
CAN status change interrupt is pending. If the TECs/RECs change their current value after the CSCIF is
asserted, which would cause an additional state change in the RSTAT/TSTAT bits, these bits keep their status
until the current CSCIF interrupt is cleared again.
0 No change in CAN bus status occurred since last interrupt
1 MSCAN changed current CAN bus status

5-4 Receiver Status Bits — The values of the error counters control the actual CAN bus status of the MSCAN. As
RSTAT[1:0] soon as the status change interrupt flag (CSCIF) is set, these bits indicate the appropriate receiver related CAN

bus status of the MSCAN. The coding for the bits RSTAT1, RSTAT0 is:
00 RxOK: 0 ≤ receive error counter < 96
01 RxWRN: 96 ≤ receive error counter < 128
10 RxERR: 128 ≤ receive error counter
11 Bus-off(1): 256 ≤ transmit error counter

3-2 Transmitter Status Bits — The values of the error counters control the actual CAN bus status of the MSCAN.
TSTAT[1:0] As soon as the status change interrupt flag (CSCIF) is set, these bits indicate the appropriate transmitter related

CAN bus status of the MSCAN. The coding for the bits TSTAT1, TSTAT0 is:
00 TxOK: 0 ≤ transmit error counter < 96
01 TxWRN: 96 ≤ transmit error counter < 128
10 TxERR: 128 ≤ transmit error counter < 256
11 Bus-Off: 256 ≤ transmit error counter

1. The RSTAT[1:0], TSTAT[1:0] bits are not affected by initialization mode.

MC9S12XE-Family Reference Manual  Rev. 1.25

618 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Table 16-11. CANRFLG Register Field Descriptions (continued)

Field Description

1 Overrun Interrupt Flag — This flag is set when a data overrun condition occurs. If not masked, an error interrupt
OVRIF is pending while this flag is set.

0 No data overrun condition
1 A data overrun detected

0 Receive Buffer Full Flag — RXF is set by the MSCAN when a new message is shifted in the receiver FIFO.
RXF(2) This flag indicates whether the shifted buffer is loaded with a correctly received message (matching identifier,

matching cyclic redundancy code (CRC) and no other errors detected). After the CPU has read that message
from the RxFG buffer in the receiver FIFO, the RXF flag must be cleared to release the buffer. A set RXF flag
prohibits the shifting of the next FIFO entry into the foreground buffer (RxFG). If not masked, a receive interrupt
is pending while this flag is set.
0 No new message available within the RxFG
1 The receiver FIFO is not empty. A new message is available in the RxFG

1. Redundant Information for the most critical CAN bus status which is “bus-off”. This only occurs if the Tx error counter exceeds
a number of 255 errors. Bus-off affects the receiver state. As soon as the transmitter leaves its bus-off state the receiver state
skips to RxOK too. Refer also to TSTAT[1:0] coding in this register.

2. To ensure data integrity, do not read the receive buffer registers while the RXF flag is cleared. For MCUs with dual CPUs,
reading the receive buffer registers while the RXF flag is cleared may result in a CPU fault condition.

******** MSCAN Receiver Interrupt Enable Register (CANRIER)
This register contains the interrupt enable bits for the interrupt flags described in the CANRFLG register.

Module Base + 0x0005 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
WUPIE CSCIE RSTATE1 RSTATE0 TSTATE1 TSTATE0 OVRIE RXFIE

W

Reset: 0 0 0 0 0 0 0 0

Figure 16-9. MSCAN Receiver Interrupt Enable Register (CANRIER)
1. Read: Anytime

Write: Anytime when not in initialization mode

NOTE
The CANRIER register is held in the reset state when the initialization mode
is active (INITRQ=1 and INITAK=1). This register is writable when not in
initialization mode (INITRQ=0 and INITAK=0).

The RSTATE[1:0], TSTATE[1:0] bits are not affected by initialization
mode.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 619



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Table 16-12. CANRIER Register Field Descriptions

Field Description

7 Wake-Up Interrupt Enable
WUPIE(1) 0 No interrupt request is generated from this event.

1 A wake-up event causes a Wake-Up interrupt request.

6 CAN Status Change Interrupt Enable
CSCIE 0 No interrupt request is generated from this event.

1 A CAN Status Change event causes an error interrupt request.

5-4 Receiver Status Change Enable — These RSTAT enable bits control the sensitivity level in which receiver state
RSTATE[1:0] changes are causing CSCIF interrupts. Independent of the chosen sensitivity level the RSTAT flags continue to

indicate the actual receiver state and are only updated if no CSCIF interrupt is pending.
00 Do not generate any CSCIF interrupt caused by receiver state changes.
01 Generate CSCIF interrupt only if the receiver enters or leaves “bus-off” state. Discard other receiver state

changes for generating CSCIF interrupt.
10 Generate CSCIF interrupt only if the receiver enters or leaves “RxErr” or “bus-off”(2) state. Discard other

receiver state changes for generating CSCIF interrupt.
11 Generate CSCIF interrupt on all state changes.

3-2 Transmitter Status Change Enable — These TSTAT enable bits control the sensitivity level in which transmitter
TSTATE[1:0] state changes are causing CSCIF interrupts. Independent of the chosen sensitivity level, the TSTAT flags

continue to indicate the actual transmitter state and are only updated if no CSCIF interrupt is pending.
00 Do not generate any CSCIF interrupt caused by transmitter state changes.
01 Generate CSCIF interrupt only if the transmitter enters or leaves “bus-off” state. Discard other transmitter

state changes for generating CSCIF interrupt.
10 Generate CSCIF interrupt only if the transmitter enters or leaves “TxErr” or “bus-off” state. Discard other

transmitter state changes for generating CSCIF interrupt.
11 Generate CSCIF interrupt on all state changes.

1 Overrun Interrupt Enable
OVRIE 0 No interrupt request is generated from this event.

1 An overrun event causes an error interrupt request.

0 Receiver Full Interrupt Enable
RXFIE 0 No interrupt request is generated from this event.

1 A receive buffer full (successful message reception) event causes a receiver interrupt request.
1. WUPIE and WUPE (see Section ********, “MSCAN Control Register 0 (CANCTL0)”) must both be enabled if the recovery

mechanism from stop or wait is required.
2. Bus-off state is only defined for transmitters by the CAN standard (see Bosch CAN 2.0A/B protocol specification). Because

the only possible state change for the transmitter from bus-off to TxOK also forces the receiver to skip its current state to RxOK,
the coding of the RXSTAT[1:0] flags define an additional bus-off state for the receiver (see Section ********, “MSCAN Receiver
Flag Register (CANRFLG)”).

******** MSCAN Transmitter Flag Register (CANTFLG)
The transmit buffer empty flags each have an associated interrupt enable bit in the CANTIER register.

MC9S12XE-Family Reference Manual  Rev. 1.25

620 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Module Base + 0x0006 Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0
TXE2 TXE1 TXE0

W

Reset: 0 0 0 0 0 1 1 1

= Unimplemented

Figure 16-10. MSCAN Transmitter Flag Register (CANTFLG)
1. Read: Anytime

Write: Anytime when not in initialization mode; write of 1 clears flag, write of 0 is ignored

NOTE
The CANTFLG register is held in the reset state when the initialization
mode is active (INITRQ = 1 and INITAK = 1). This register is writable when
not in initialization mode (INITRQ = 0 and INITAK = 0).

Table 16-13. CANTFLG Register Field Descriptions

Field Description

2-0 Transmitter Buffer Empty — This flag indicates that the associated transmit message buffer is empty, and thus
TXE[2:0] not scheduled for transmission. The CPU must clear the flag after a message is set up in the transmit buffer and

is due for transmission. The MSCAN sets the flag after the message is sent successfully. The flag is also set by
the MSCAN when the transmission request is successfully aborted due to a pending abort request (see
Section ********, “MSCAN Transmitter Message Abort Request Register (CANTARQ)”). If not masked, a
transmit interrupt is pending while this flag is set.
Clearing a TXEx flag also clears the corresponding ABTAKx (see Section ********0, “MSCAN Transmitter
Message Abort Acknowledge Register (CANTAAK)”). When a TXEx flag is set, the corresponding ABTRQx bit
is cleared (see Section ********, “MSCAN Transmitter Message Abort Request Register (CANTARQ)”).
When listen-mode is active (see Section ********, “MSCAN Control Register 1 (CANCTL1)”) the TXEx flags
cannot be cleared and no transmission is started.
Read and write accesses to the transmit buffer will be blocked, if the corresponding TXEx bit is cleared
(TXEx = 0) and the buffer is scheduled for transmission.
0 The associated message buffer is full (loaded with a message due for transmission)
1 The associated message buffer is empty (not scheduled)

******** MSCAN Transmitter Interrupt Enable Register (CANTIER)
This register contains the interrupt enable bits for the transmit buffer empty interrupt flags.

Module Base + 0x0007 Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0
TXEIE2 TXEIE1 TXEIE0

W

Reset: 0 0 0 0 0 0 0 0

= Unimplemented

Figure 16-11. MSCAN Transmitter Interrupt Enable Register (CANTIER)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 621



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

1. Read: Anytime
Write: Anytime when not in initialization mode

NOTE
The CANTIER register is held in the reset state when the initialization mode
is active (INITRQ = 1 and INITAK = 1). This register is writable when not
in initialization mode (INITRQ = 0 and INITAK = 0).

Table 16-14. CANTIER Register Field Descriptions

Field Description

2-0 Transmitter Empty Interrupt Enable
TXEIE[2:0] 0 No interrupt request is generated from this event.

1 A transmitter empty (transmit buffer available for transmission) event causes a transmitter empty interrupt
request.

******** MSCAN Transmitter Message Abort Request Register (CANTARQ)
The CANTARQ register allows abort request of queued messages as described below.

Module Base + 0x0008 Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0
ABTRQ2 ABTRQ1 ABTRQ0

W

Reset: 0 0 0 0 0 0 0 0

= Unimplemented

Figure 16-12. MSCAN Transmitter Message Abort Request Register (CANTARQ)
1. Read: Anytime

Write: Anytime when not in initialization mode

NOTE
The CANTARQ register is held in the reset state when the initialization
mode is active (INITRQ = 1 and INITAK = 1). This register is writable when
not in initialization mode (INITRQ = 0 and INITAK = 0).

Table 16-15. CANTARQ Register Field Descriptions

Field Description

2-0 Abort Request — The CPU sets the ABTRQx bit to request that a scheduled message buffer (TXEx = 0) be
ABTRQ[2:0] aborted. The MSCAN grants the request if the message has not already started transmission, or if the

transmission is not successful (lost arbitration or error). When a message is aborted, the associated TXE (see
Section ********, “MSCAN Transmitter Flag Register (CANTFLG)”) and abort acknowledge flags (ABTAK, see
Section ********0, “MSCAN Transmitter Message Abort Acknowledge Register (CANTAAK)”) are set and a
transmit interrupt occurs if enabled. The CPU cannot reset ABTRQx. ABTRQx is reset whenever the associated
TXE flag is set.
0 No abort request
1 Abort request pending

MC9S12XE-Family Reference Manual  Rev. 1.25

622 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

********0 MSCAN Transmitter Message Abort Acknowledge Register (CANTAAK)
The CANTAAK register indicates the successful abort of a queued message, if requested by the
appropriate bits in the CANTARQ register.

Module Base + 0x0009 Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0 ABTAK2 ABTAK1 ABTAK0

W

Reset: 0 0 0 0 0 0 0 0

= Unimplemented

Figure 16-13. MSCAN Transmitter Message Abort Acknowledge Register (CANTAAK)
1. Read: Anytime

Write: Unimplemented

NOTE
The CANTAAK register is held in the reset state when the initialization
mode is active (INITRQ = 1 and INITAK = 1).

Table 16-16. CANTAAK Register Field Descriptions

Field Description

2-0 Abort Acknowledge — This flag acknowledges that a message was aborted due to a pending abort request
ABTAK[2:0] from the CPU. After a particular message buffer is flagged empty, this flag can be used by the application

software to identify whether the message was aborted successfully or was sent anyway. The ABTAKx flag is
cleared whenever the corresponding TXE flag is cleared.
0 The message was not aborted.
1 The message was aborted.

********1 MSCAN Transmit Buffer Selection Register (CANTBSEL)
The CANTBSEL register allows the selection of the actual transmit message buffer, which then will be
accessible in the CANTXFG register space.

Module Base + 0x000A Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0
TX2 TX1 TX0

W

Reset: 0 0 0 0 0 0 0 0

= Unimplemented

Figure 16-14. MSCAN Transmit Buffer Selection Register (CANTBSEL)
1. Read: Find the lowest ordered bit set to 1, all other bits will be read as 0

Write: Anytime when not in initialization mode

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 623



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

NOTE
The CANTBSEL register is held in the reset state when the initialization
mode is active (INITRQ = 1 and INITAK=1). This register is writable when
not in initialization mode (INITRQ = 0 and INITAK = 0).

Table 16-17. CANTBSEL Register Field Descriptions

Field Description

2-0 Transmit Buffer Select — The lowest numbered bit places the respective transmit buffer in the CANTXFG
TX[2:0] register space (e.g., TX1 = 1 and TX0 = 1 selects transmit buffer TX0; TX1 = 1 and TX0 = 0 selects transmit

buffer TX1). Read and write accesses to the selected transmit buffer will be blocked, if the corresponding TXEx
bit is cleared and the buffer is scheduled for transmission (see Section ********, “MSCAN Transmitter Flag
Register (CANTFLG)”).
0 The associated message buffer is deselected
1 The associated message buffer is selected, if lowest numbered bit

The following gives a short programming example of the usage of the CANTBSEL register:

To get the next available transmit buffer, application software must read the CANTFLG register and write
this value back into the CANTBSEL register. In this example Tx buffers TX1 and TX2 are available. The
value read from CANTFLG is therefore 0b0000_0110. When writing this value back to CANTBSEL, the
Tx buffer TX1 is selected in the CANTXFG because the lowest numbered bit set to 1 is at bit position 1.
Reading back this value out of CANTBSEL results in 0b0000_0010, because only the lowest numbered
bit position set to 1 is presented. This mechanism eases the application software’s selection of the next
available Tx buffer.

• LDAA CANTFLG; value read is 0b0000_0110
• STAA CANTBSEL; value written is 0b0000_0110
• LDAA CANTBSEL; value read is 0b0000_0010

If all transmit message buffers are deselected, no accesses are allowed to the CANTXFG registers.

********2 MSCAN Identifier Acceptance Control Register (CANIDAC)
The CANIDAC register is used for identifier acceptance control as described below.

Module Base + 0x000B Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0 0 0 IDHIT2 IDHIT1 IDHIT0
IDAM1 IDAM0

W

Reset: 0 0 0 0 0 0 0 0

= Unimplemented

Figure 16-15. MSCAN Identifier Acceptance Control Register (CANIDAC)
1. Read: Anytime

Write: Anytime in initialization mode (INITRQ = 1 and INITAK = 1), except bits IDHITx, which are read-only

MC9S12XE-Family Reference Manual  Rev. 1.25

624 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Table 16-18. CANIDAC Register Field Descriptions

Field Description

5-4 Identifier Acceptance Mode — The CPU sets these flags to define the identifier acceptance filter organization
IDAM[1:0] (see Section 16.4.3, “Identifier Acceptance Filter”). Table 16-19 summarizes the different settings. In filter closed

mode, no message is accepted such that the foreground buffer is never reloaded.

2-0 Identifier Acceptance Hit Indicator — The MSCAN sets these flags to indicate an identifier acceptance hit (see
IDHIT[2:0] Section 16.4.3, “Identifier Acceptance Filter”). Table 16-20 summarizes the different settings.

Table 16-19. Identifier Acceptance Mode Settings

IDAM1 IDAM0 Identifier Acceptance Mode

0 0 Two 32-bit acceptance filters

0 1 Four 16-bit acceptance filters

1 0 Eight 8-bit acceptance filters

1 1 Filter closed

Table 16-20. Identifier Acceptance Hit Indication

IDHIT2 IDHIT1 IDHIT0 Identifier Acceptance Hit

0 0 0 Filter 0 hit
0 0 1 Filter 1 hit
0 1 0 Filter 2 hit
0 1 1 Filter 3 hit
1 0 0 Filter 4 hit
1 0 1 Filter 5 hit
1 1 0 Filter 6 hit
1 1 1 Filter 7 hit

The IDHITx indicators are always related to the message in the foreground buffer (RxFG). When a
message gets shifted into the foreground buffer of the receiver FIFO the indicators are updated as well.

********3 MSCAN Reserved Register
This register is reserved for factory testing of the MSCAN module and is not available in normal system
operating modes.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 625



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Module Base + 0x000C Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W

Reset: 0 0 0 0 0 0 0 0

= Unimplemented

Figure 16-16. MSCAN Reserved Register
1. Read: Always reads zero in normal system operation modes

Write: Unimplemented in normal system operation modes

NOTE
Writing to this register when in special system operating modes can alter the
MSCAN functionality.

********4 MSCAN Miscellaneous Register (CANMISC)
This register provides additional features.

Module Base + 0x000D Access: User read/write(1)

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0
BOHOLD

W

Reset: 0 0 0 0 0 0 0 0

= Unimplemented

Figure 16-17. MSCAN Miscellaneous Register (CANMISC)
1. Read: Anytime

Write: Anytime; write of ‘1’ clears flag; write of ‘0’ ignored

Table 16-21. CANMISC Register Field Descriptions

Field Description

0 Bus-off State Hold Until User Request — If BORM is set in MSCAN Control Register 1 (CANCTL1), this bit
BOHOLD indicates whether the module has entered the bus-off state. Clearing this bit requests the recovery from bus-off.

Refer to Section 16.5.2, “Bus-Off Recovery,” for details.
0 Module is not bus-off or recovery has been requested by user in bus-off state
1 Module is bus-off and holds this state until user request

********5 MSCAN Receive Error Counter (CANRXERR)
This register reflects the status of the MSCAN receive error counter.

MC9S12XE-Family Reference Manual  Rev. 1.25

626 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Module Base + 0x000E Access: User read/write(1)

7 6 5 4 3 2 1 0

R RXERR7 RXERR6 RXERR5 RXERR4 RXERR3 RXERR2 RXERR1 RXERR0

W

Reset: 0 0 0 0 0 0 0 0

= Unimplemented

Figure 16-18. MSCAN Receive Error Counter (CANRXERR)
1. Read: Only when in sleep mode (SLPRQ = 1 and SLPAK = 1) or initialization mode (INITRQ = 1 and INITAK = 1)

Write: Unimplemented

NOTE
Reading this register when in any other mode other than sleep or
initialization mode may return an incorrect value. For MCUs with dual
CPUs, this may result in a CPU fault condition.

********6 MSCAN Transmit Error Counter (CANTXERR)
This register reflects the status of the MSCAN transmit error counter.

Module Base + 0x000F Access: User read/write(1)

7 6 5 4 3 2 1 0

R TXERR7 TXERR6 TXERR5 TXERR4 TXERR3 TXERR2 TXERR1 TXERR0

W

Reset: 0 0 0 0 0 0 0 0

= Unimplemented

Figure 16-19. MSCAN Transmit Error Counter (CANTXERR)
1. Read: Only when in sleep mode (SLPRQ = 1 and SLPAK = 1) or initialization mode (INITRQ = 1 and INITAK = 1)

Write: Unimplemented

NOTE
Reading this register when in any other mode other than sleep or
initialization mode, may return an incorrect value. For MCUs with dual
CPUs, this may result in a CPU fault condition.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 627



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

********7 MSCAN Identifier Acceptance Registers (CANIDAR0-7)
On reception, each message is written into the background receive buffer. The CPU is only signalled to
read the message if it passes the criteria in the identifier acceptance and identifier mask registers
(accepted); otherwise, the message is overwritten by the next message (dropped).

The acceptance registers of the MSCAN are applied on the IDR0–IDR3 registers (see Section ********,
“Identifier Registers (IDR0–IDR3)”) of incoming messages in a bit by bit manner (see Section 16.4.3,
“Identifier Acceptance Filter”).

For extended identifiers, all four acceptance and mask registers are applied. For standard identifiers, only
the first two (CANIDAR0/1, CANIDMR0/1) are applied.

Module Base + 0x0010 to Module Base + 0x0013 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0

W

Reset 0 0 0 0 0 0 0 0

Figure 16-20. MSCAN Identifier Acceptance Registers (First Bank) — CANIDAR0–CANIDAR3
1. Read: Anytime

Write: Anytime in initialization mode (INITRQ = 1 and INITAK = 1)

Table 16-22. CANIDAR0–CANIDAR3 Register Field Descriptions

Field Description

7-0 Acceptance Code Bits — AC[7:0] comprise a user-defined sequence of bits with which the corresponding bits
AC[7:0] of the related identifier register (IDRn) of the receive message buffer are compared. The result of this comparison

is then masked with the corresponding identifier mask register.

Module Base + 0x0018 to Module Base + 0x001B Access: User read/write(1)

7 6 5 4 3 2 1 0

R
AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0

W

Reset 0 0 0 0 0 0 0 0

Figure 16-21. MSCAN Identifier Acceptance Registers (Second Bank) — CANIDAR4–CANIDAR7
1. Read: Anytime

Write: Anytime in initialization mode (INITRQ = 1 and INITAK = 1)

MC9S12XE-Family Reference Manual  Rev. 1.25

628 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Table 16-23. CANIDAR4–CANIDAR7 Register Field Descriptions

Field Description

7-0 Acceptance Code Bits — AC[7:0] comprise a user-defined sequence of bits with which the corresponding bits
AC[7:0] of the related identifier register (IDRn) of the receive message buffer are compared. The result of this comparison

is then masked with the corresponding identifier mask register.

********8 MSCAN Identifier Mask Registers (CANIDMR0–CANIDMR7)
The identifier mask register specifies which of the corresponding bits in the identifier acceptance register
are relevant for acceptance filtering. To receive standard identifiers in 32 bit filter mode, it is required to
program the last three bits (AM[2:0]) in the mask registers CANIDMR1 and CANIDMR5 to “don’t care.”
To receive standard identifiers in 16 bit filter mode, it is required to program the last three bits (AM[2:0])
in the mask registers CANIDMR1, CANIDMR3, CANIDMR5, and CANIDMR7 to “don’t care.”

Module Base + 0x0014 to Module Base + 0x0017 Access: User read/write(1)

7 6 5 4 3 2 1 0

R
AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0

W

Reset 0 0 0 0 0 0 0 0

Figure 16-22. MSCAN Identifier Mask Registers (First Bank) — CANIDMR0–CANIDMR3
1. Read: Anytime

Write: Anytime in initialization mode (INITRQ = 1 and INITAK = 1)

Table 16-24. CANIDMR0–CANIDMR3 Register Field Descriptions

Field Description

7-0 Acceptance Mask Bits — If a particular bit in this register is cleared, this indicates that the corresponding bit in
AM[7:0] the identifier acceptance register must be the same as its identifier bit before a match is detected. The message

is accepted if all such bits match. If a bit is set, it indicates that the state of the corresponding bit in the identifier
acceptance register does not affect whether or not the message is accepted.
0 Match corresponding acceptance code register and identifier bits
1 Ignore corresponding acceptance code register bit

Module Base + 0x001C to Module Base + 0x001F Access: User read/write(1)

7 6 5 4 3 2 1 0

R
AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0

W

Reset 0 0 0 0 0 0 0 0

Figure 16-23. MSCAN Identifier Mask Registers (Second Bank) — CANIDMR4–CANIDMR7

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 629



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

1. Read: Anytime
Write: Anytime in initialization mode (INITRQ = 1 and INITAK = 1)

Table 16-25. CANIDMR4–CANIDMR7 Register Field Descriptions

Field Description

7-0 Acceptance Mask Bits — If a particular bit in this register is cleared, this indicates that the corresponding bit in
AM[7:0] the identifier acceptance register must be the same as its identifier bit before a match is detected. The message

is accepted if all such bits match. If a bit is set, it indicates that the state of the corresponding bit in the identifier
acceptance register does not affect whether or not the message is accepted.
0 Match corresponding acceptance code register and identifier bits
1 Ignore corresponding acceptance code register bit

16.3.3 Programmer’s Model of Message Storage
The following section details the organization of the receive and transmit message buffers and the
associated control registers.

To simplify the programmer interface, the receive and transmit message buffers have the same outline.
Each message buffer allocates 16 bytes in the memory map containing a 13 byte data structure.

An additional transmit buffer priority register (TBPR) is defined for the transmit buffers. Within the last
two bytes of this memory map, the MSCAN stores a special 16-bit time stamp, which is sampled from an
internal timer after successful transmission or reception of a message. This feature is only available for
transmit and receiver buffers, if the TIME bit is set (see Section ********, “MSCAN Control Register 0
(CANCTL0)”).

The time stamp register is written by the MSCAN. The CPU can only read these registers.

MC9S12XE-Family Reference Manual  Rev. 1.25

630 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Table 16-26. Message Buffer Organization

Offset
Register Access

Address

0x00X0 IDR0 — Identifier Register 0 R/W

0x00X1 IDR1 — Identifier Register 1 R/W

0x00X2 IDR2 — Identifier Register 2 R/W

0x00X3 IDR3 — Identifier Register 3 R/W

0x00X4 DSR0 — Data Segment Register 0 R/W

0x00X5 DSR1 — Data Segment Register 1 R/W

0x00X6 DSR2 — Data Segment Register 2 R/W

0x00X7 DSR3 — Data Segment Register 3 R/W

0x00X8 DSR4 — Data Segment Register 4 R/W

0x00X9 DSR5 — Data Segment Register 5 R/W

0x00XA DSR6 — Data Segment Register 6 R/W

0x00XB DSR7 — Data Segment Register 7 R/W

0x00XC DLR — Data Length Register R/W

0x00XD TBPR — Transmit Buffer Priority Register(1) R/W

0x00XE TSRH — Time Stamp Register (High Byte) R

0x00XF TSRL — Time Stamp Register (Low Byte) R
1. Not applicable for receive buffers

Figure 16-24 shows the common 13-byte data structure of receive and transmit buffers for extended
identifiers. The mapping of standard identifiers into the IDR registers is shown in Figure 16-25.

All bits of the receive and transmit buffers are ‘x’ out of reset because of RAM-based implementation1.
All reserved or unused bits of the receive and transmit buffers always read ‘x’.

1. Exception: The transmit buffer priority registers are 0 out of reset.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 631



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Figure 16-24. Receive/Transmit Message Buffer — Extended Identifier Mapping

Register
Bit 7 6 5 4 3 2 1 Bit0

Name

0x00X0 R
ID28 ID27 ID26 ID25 ID24 ID23 ID22 ID21

IDR0 W

0x00X1 R
ID20 ID19 ID18 SRR (=1) IDE (=1) ID17 ID16 ID15

IDR1 W

0x00X2 R
ID14 ID13 ID12 ID11 ID10 ID9 ID8 ID7

IDR2 W

0x00X3 R
ID6 ID5 ID4 ID3 ID2 ID1 ID0 RTR

IDR3 W

0x00X4 R
DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0

DSR0 W

0x00X5 R
DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0

DSR1 W

0x00X6 R
DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0

DSR2 W

0x00X7 R
DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0

DSR3 W

0x00X8 R
DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0

DSR4 W

0x00X9 R
DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0

DSR5 W

0x00XA R
DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0

DSR6 W

0x00XB R
DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0

DSR7 W

0x00XC R
DLC3 DLC2 DLC1 DLC0

DLR W

MC9S12XE-Family Reference Manual  Rev. 1.25

632 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Figure 16-24. Receive/Transmit Message Buffer — Extended Identifier Mapping (continued)

Register
Bit 7 6 5 4 3 2 1 Bit0

Name

= Unused, always read ‘x’

Read:
• For transmit buffers, anytime when TXEx flag is set (see Section ********, “MSCAN Transmitter

Flag Register (CANTFLG)”) and the corresponding transmit buffer is selected in CANTBSEL (see
Section ********1, “MSCAN Transmit Buffer Selection Register (CANTBSEL)”).

• For receive buffers, only when RXF flag is set (see Section ********, “MSCAN Receiver Flag
Register (CANRFLG)”).

Write:
• For transmit buffers, anytime when TXEx flag is set (see Section ********, “MSCAN Transmitter

Flag Register (CANTFLG)”) and the corresponding transmit buffer is selected in CANTBSEL (see
Section ********1, “MSCAN Transmit Buffer Selection Register (CANTBSEL)”).

• Unimplemented for receive buffers.

Reset: Undefined because of RAM-based implementation

Figure 16-25. Receive/Transmit Message Buffer — Standard Identifier Mapping

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

IDR0 R
ID10 ID9 ID8 ID7 ID6 ID5 ID4 ID3

0x00X0 W

IDR1 R
ID2 ID1 ID0 RTR IDE (=0)

0x00X1 W

IDR2 R
0x00X2 W

IDR3 R
0x00X3 W

= Unused, always read ‘x’

******** Identifier Registers (IDR0–IDR3)
The identifier registers for an extended format identifier consist of a total of 32 bits: ID[28:0], SRR, IDE,
and RTR. The identifier registers for a standard format identifier consist of a total of 13 bits: ID[10:0],
RTR, and IDE.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 633



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

********.1 IDR0–IDR3 for Extended Identifier Mapping

Module Base + 0x00X0

7 6 5 4 3 2 1 0

R
ID28 ID27 ID26 ID25 ID24 ID23 ID22 ID21

W

Reset: x x x x x x x x

Figure 16-26. Identifier Register 0 (IDR0) — Extended Identifier Mapping

Table 16-27. IDR0 Register Field Descriptions — Extended

Field Description

7-0 Extended Format Identifier — The identifiers consist of 29 bits (ID[28:0]) for the extended format. ID28 is the
ID[28:21] most significant bit and is transmitted first on the CAN bus during the arbitration procedure. The priority of an

identifier is defined to be highest for the smallest binary number.

Module Base + 0x00X1

7 6 5 4 3 2 1 0

R
ID20 ID19 ID18 SRR (=1) IDE (=1) ID17 ID16 ID15

W

Reset: x x x x x x x x

Figure 16-27. Identifier Register 1 (IDR1) — Extended Identifier Mapping

Table 16-28. IDR1 Register Field Descriptions — Extended

Field Description

7-5 Extended Format Identifier — The identifiers consist of 29 bits (ID[28:0]) for the extended format. ID28 is the
ID[20:18] most significant bit and is transmitted first on the CAN bus during the arbitration procedure. The priority of an

identifier is defined to be highest for the smallest binary number.

4 Substitute Remote Request — This fixed recessive bit is used only in extended format. It must be set to 1 by
SRR the user for transmission buffers and is stored as received on the CAN bus for receive buffers.

3 ID Extended — This flag indicates whether the extended or standard identifier format is applied in this buffer. In
IDE the case of a receive buffer, the flag is set as received and indicates to the CPU how to process the buffer

identifier registers. In the case of a transmit buffer, the flag indicates to the MSCAN what type of identifier to send.
0 Standard format (11 bit)
1 Extended format (29 bit)

2-0 Extended Format Identifier — The identifiers consist of 29 bits (ID[28:0]) for the extended format. ID28 is the
ID[17:15] most significant bit and is transmitted first on the CAN bus during the arbitration procedure. The priority of an

identifier is defined to be highest for the smallest binary number.

MC9S12XE-Family Reference Manual  Rev. 1.25

634 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Module Base + 0x00X2

7 6 5 4 3 2 1 0

R
ID14 ID13 ID12 ID11 ID10 ID9 ID8 ID7

W

Reset: x x x x x x x x

Figure 16-28. Identifier Register 2 (IDR2) — Extended Identifier Mapping

Table 16-29. IDR2 Register Field Descriptions — Extended

Field Description

7-0 Extended Format Identifier — The identifiers consist of 29 bits (ID[28:0]) for the extended format. ID28 is the
ID[14:7] most significant bit and is transmitted first on the CAN bus during the arbitration procedure. The priority of an

identifier is defined to be highest for the smallest binary number.

Module Base + 0x00X3

7 6 5 4 3 2 1 0

R
ID6 ID5 ID4 ID3 ID2 ID1 ID0 RTR

W

Reset: x x x x x x x x

Figure 16-29. Identifier Register 3 (IDR3) — Extended Identifier Mapping

Table 16-30. IDR3 Register Field Descriptions — Extended

Field Description

7-1 Extended Format Identifier — The identifiers consist of 29 bits (ID[28:0]) for the extended format. ID28 is the
ID[6:0] most significant bit and is transmitted first on the CAN bus during the arbitration procedure. The priority of an

identifier is defined to be highest for the smallest binary number.

0 Remote Transmission Request — This flag reflects the status of the remote transmission request bit in the
RTR CAN frame. In the case of a receive buffer, it indicates the status of the received frame and supports the

transmission of an answering frame in software. In the case of a transmit buffer, this flag defines the setting of
the RTR bit to be sent.
0 Data frame
1 Remote frame

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 635



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

********.2 IDR0–IDR3 for Standard Identifier Mapping

Module Base + 0x00X0

7 6 5 4 3 2 1 0

R
ID10 ID9 ID8 ID7 ID6 ID5 ID4 ID3

W

Reset: x x x x x x x x

Figure 16-30. Identifier Register 0 — Standard Mapping

Table 16-31. IDR0 Register Field Descriptions — Standard

Field Description

7-0 Standard Format Identifier — The identifiers consist of 11 bits (ID[10:0]) for the standard format. ID10 is the
ID[10:3] most significant bit and is transmitted first on the CAN bus during the arbitration procedure. The priority of an

identifier is defined to be highest for the smallest binary number. See also ID bits in Table 16-32.

Module Base + 0x00X1

7 6 5 4 3 2 1 0

R
ID2 ID1 ID0 RTR IDE (=0)

W

Reset: x x x x x x x x

= Unused; always read ‘x’

Figure 16-31. Identifier Register 1 — Standard Mapping

Table 16-32. IDR1 Register Field Descriptions

Field Description

7-5 Standard Format Identifier — The identifiers consist of 11 bits (ID[10:0]) for the standard format. ID10 is the
ID[2:0] most significant bit and is transmitted first on the CAN bus during the arbitration procedure. The priority of an

identifier is defined to be highest for the smallest binary number. See also ID bits in Table 16-31.

4 Remote Transmission Request — This flag reflects the status of the Remote Transmission Request bit in the
RTR CAN frame. In the case of a receive buffer, it indicates the status of the received frame and supports the

transmission of an answering frame in software. In the case of a transmit buffer, this flag defines the setting of
the RTR bit to be sent.
0 Data frame
1 Remote frame

3 ID Extended — This flag indicates whether the extended or standard identifier format is applied in this buffer. In
IDE the case of a receive buffer, the flag is set as received and indicates to the CPU how to process the buffer

identifier registers. In the case of a transmit buffer, the flag indicates to the MSCAN what type of identifier to send.
0 Standard format (11 bit)
1 Extended format (29 bit)

MC9S12XE-Family Reference Manual  Rev. 1.25

636 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Module Base + 0x00X2

7 6 5 4 3 2 1 0

R

W

Reset: x x x x x x x x

= Unused; always read ‘x’

Figure 16-32. Identifier Register 2 — Standard Mapping

Module Base + 0x00X3

7 6 5 4 3 2 1 0

R

W

Reset: x x x x x x x x

= Unused; always read ‘x’

Figure 16-33. Identifier Register 3 — Standard Mapping

16.3.3.2 Data Segment Registers (DSR0-7)
The eight data segment registers, each with bits DB[7:0], contain the data to be transmitted or received.
The number of bytes to be transmitted or received is determined by the data length code in the
corresponding DLR register.

Module Base + 0x00X4 to Module Base + 0x00XB

7 6 5 4 3 2 1 0

R
DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0

W

Reset: x x x x x x x x

Figure 16-34. Data Segment Registers (DSR0–DSR7) — Extended Identifier Mapping

Table 16-33.  DSR0–DSR7 Register Field Descriptions

Field Description

7-0 Data bits 7-0
DB[7:0]

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 637



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

******** Data Length Register (DLR)
This register keeps the data length field of the CAN frame.

Module Base + 0x00XC

7 6 5 4 3 2 1 0

R
DLC3 DLC2 DLC1 DLC0

W

Reset: x x x x x x x x

= Unused; always read “x”

Figure 16-35. Data Length Register (DLR) — Extended Identifier Mapping

Table 16-34.  DLR Register Field Descriptions

Field Description

3-0 Data Length Code Bits — The data length code contains the number of bytes (data byte count) of the respective
DLC[3:0] message. During the transmission of a remote frame, the data length code is transmitted as programmed while

the number of transmitted data bytes is always 0. The data byte count ranges from 0 to 8 for a data frame.
Table 16-35 shows the effect of setting the DLC bits.

Table 16-35. Data Length Codes

Data Length Code Data Byte
DLC3 DLC2 DLC1 DLC0 Count

0 0 0 0 0

0 0 0 1 1

0 0 1 0 2

0 0 1 1 3

0 1 0 0 4

0 1 0 1 5

0 1 1 0 6

0 1 1 1 7

1 0 0 0 8

******** Transmit Buffer Priority Register (TBPR)
This register defines the local priority of the associated message buffer. The local priority is used for the
internal prioritization process of the MSCAN and is defined to be highest for the smallest binary number.
The MSCAN implements the following internal prioritization mechanisms:

• All transmission buffers with a cleared TXEx flag participate in the prioritization immediately
before the SOF (start of frame) is sent.

MC9S12XE-Family Reference Manual  Rev. 1.25

638 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

• The transmission buffer with the lowest local priority field wins the prioritization.

In cases of more than one buffer having the same lowest priority, the message buffer with the lower index
number wins.

Module Base + 0x00XD Access: User read/write(1)

7 6 5 4 3 2 1 0

R
PRIO7 PRIO6 PRIO5 PRIO4 PRIO3 PRIO2 PRIO1 PRIO0

W

Reset: 0 0 0 0 0 0 0 0

Figure 16-36. Transmit Buffer Priority Register (TBPR)
1. Read: Anytime when TXEx flag is set (see Section ********, “MSCAN Transmitter Flag Register (CANTFLG)”) and the

corresponding transmit buffer is selected in CANTBSEL (see Section ********1, “MSCAN Transmit Buffer Selection Register
(CANTBSEL)”)
Write: Anytime when TXEx flag is set (see Section ********, “MSCAN Transmitter Flag Register (CANTFLG)”) and the
corresponding transmit buffer is selected in CANTBSEL (see Section ********1, “MSCAN Transmit Buffer Selection Register
(CANTBSEL)”)

******** Time Stamp Register (TSRH–TSRL)
If the TIME bit is enabled, the MSCAN will write a time stamp to the respective registers in the active
transmit or receive buffer right after the EOF of a valid message on the CAN bus (see Section ********,
“MSCAN Control Register 0 (CANCTL0)”). In case of a transmission, the CPU can only read the time
stamp after the respective transmit buffer has been flagged empty.

The timer value, which is used for stamping, is taken from a free running internal CAN bit clock. A timer
overrun is not indicated by the MSCAN. The timer is reset (all bits set to 0) during initialization mode. The
CPU can only read the time stamp registers.

Module Base + 0x00XE Access: User read/write(1)

7 6 5 4 3 2 1 0

R TSR15 TSR14 TSR13 TSR12 TSR11 TSR10 TSR9 TSR8

W

Reset: x x x x x x x x

Figure 16-37. Time Stamp Register — High Byte (TSRH)
1. Read: For transmit buffers: Anytime when TXEx flag is set (see Section ********, “MSCAN Transmitter Flag Register

(CANTFLG)”) and the corresponding transmit buffer is selected in CANTBSEL (see Section ********1, “MSCAN Transmit
Buffer Selection Register (CANTBSEL)”). For receive buffers: Anytime when RXF is set.
Write: Unimplemented

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 639



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Module Base + 0x00XF Access: User read/write(1)

7 6 5 4 3 2 1 0

R TSR7 TSR6 TSR5 TSR4 TSR3 TSR2 TSR1 TSR0

W

Reset: x x x x x x x x

Figure 16-38. Time Stamp Register — Low Byte (TSRL)
1. Read: or transmit buffers: Anytime when TXEx flag is set (see Section ********, “MSCAN Transmitter Flag Register

(CANTFLG)”) and the corresponding transmit buffer is selected in CANTBSEL (see Section ********1, “MSCAN Transmit
Buffer Selection Register (CANTBSEL)”). For receive buffers: Anytime when RXF is set.
Write: Unimplemented

MC9S12XE-Family Reference Manual  Rev. 1.25

640 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

16.4 Functional Description

16.4.1 General
This section provides a complete functional description of the MSCAN.

16.4.2 Message Storage
CAN Receive / Transmit Engine Memory Mapped I/O

Rx0
Rx1

Rx2
MSCAN Rx3

Rx4
RXF

CPU bus
Receiver

Tx0 TXE0

PRIO

Tx1 TXE1

MSCAN CPU bus

PRIO

Tx2 TXE2

Transmitter PRIO

Figure 16-39. User Model for Message Buffer Organization

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 641

RxBG

TxBG TxFG TxBG
RxFG



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

The MSCAN facilitates a sophisticated message storage system which addresses the requirements of a
broad range of network applications.

******** Message Transmit Background
Modern application layer software is built upon two fundamental assumptions:

• Any CAN node is able to send out a stream of scheduled messages without releasing the CAN bus
between the two messages. Such nodes arbitrate for the CAN bus immediately after sending the
previous message and only release the CAN bus in case of lost arbitration.

• The internal message queue within any CAN node is organized such that the highest priority
message is sent out first, if more than one message is ready to be sent.

The behavior described in the bullets above cannot be achieved with a single transmit buffer. That buffer
must be reloaded immediately after the previous message is sent. This loading process lasts a finite amount
of time and must be completed within the inter-frame sequence (IFS) to be able to send an uninterrupted
stream of messages. Even if this is feasible for limited CAN bus speeds, it requires that the CPU reacts
with short latencies to the transmit interrupt.

A double buffer scheme de-couples the reloading of the transmit buffer from the actual message sending
and, therefore, reduces the reactiveness requirements of the CPU. Problems can arise if the sending of a
message is finished while the CPU re-loads the second buffer. No buffer would then be ready for
transmission, and the CAN bus would be released.

At least three transmit buffers are required to meet the first of the above requirements under all
circumstances. The MSCAN has three transmit buffers.

The second requirement calls for some sort of internal prioritization which the MSCAN implements with
the “local priority” concept described in Section ********, “Transmit Structures.”

******** Transmit Structures
The MSCAN triple transmit buffer scheme optimizes real-time performance by allowing multiple
messages to be set up in advance. The three buffers are arranged as shown in Figure 16-39.

All three buffers have a 13-byte data structure similar to the outline of the receive buffers (see
Section 16.3.3, “Programmer’s Model of Message Storage”). An additional Transmit Buffer Priority
Register (TBPR) contains an 8-bit local priority field (PRIO) (see Section ********, “Transmit Buffer
Priority Register (TBPR)”). The remaining two bytes are used for time stamping of a message, if required
(see Section ********, “Time Stamp Register (TSRH–TSRL)”).

To transmit a message, the CPU must identify an available transmit buffer, which is indicated by a set
transmitter buffer empty (TXEx) flag (see Section ********, “MSCAN Transmitter Flag Register
(CANTFLG)”). If a transmit buffer is available, the CPU must set a pointer to this buffer by writing to the
CANTBSEL register (see Section ********1, “MSCAN Transmit Buffer Selection Register
(CANTBSEL)”). This makes the respective buffer accessible within the CANTXFG address space (see
Section 16.3.3, “Programmer’s Model of Message Storage”). The algorithmic feature associated with the
CANTBSEL register simplifies the transmit buffer selection. In addition, this scheme makes the handler

MC9S12XE-Family Reference Manual  Rev. 1.25

642 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

software simpler because only one address area is applicable for the transmit process, and the required
address space is minimized.

The CPU then stores the identifier, the control bits, and the data content into one of the transmit buffers.
Finally, the buffer is flagged as ready for transmission by clearing the associated TXE flag.

The MSCAN then schedules the message for transmission and signals the successful transmission of the
buffer by setting the associated TXE flag. A transmit interrupt (see Section ********, “Transmit Interrupt”)
is generated1 when TXEx is set and can be used to drive the application software to re-load the buffer.

If more than one buffer is scheduled for transmission when the CAN bus becomes available for arbitration,
the MSCAN uses the local priority setting of the three buffers to determine the prioritization. For this
purpose, every transmit buffer has an 8-bit local priority field (PRIO). The application software programs
this field when the message is set up. The local priority reflects the priority of this particular message
relative to the set of messages being transmitted from this node. The lowest binary value of the PRIO field
is defined to be the highest priority. The internal scheduling process takes place whenever the MSCAN
arbitrates for the CAN bus. This is also the case after the occurrence of a transmission error.

When a high priority message is scheduled by the application software, it may become necessary to abort
a lower priority message in one of the three transmit buffers. Because messages that are already in
transmission cannot be aborted, the user must request the abort by setting the corresponding abort request
bit (ABTRQ) (see Section ********, “MSCAN Transmitter Message Abort Request Register
(CANTARQ)”.) The MSCAN then grants the request, if possible, by:

1. Setting the corresponding abort acknowledge flag (ABTAK) in the CANTAAK register.
2. Setting the associated TXE flag to release the buffer.
3. Generating a transmit interrupt. The transmit interrupt handler software can determine from the

setting of the ABTAK flag whether the message was aborted (ABTAK = 1) or sent (ABTAK = 0).

******** Receive Structures
The received messages are stored in a five stage input FIFO. The five message buffers are alternately
mapped into a single memory area (see Figure 16-39). The background receive buffer (RxBG) is
exclusively associated with the MSCAN, but the foreground receive buffer (RxFG) is addressable by the
CPU (see Figure 16-39). This scheme simplifies the handler software because only one address area is
applicable for the receive process.

All receive buffers have a size of 15 bytes to store the CAN control bits, the identifier (standard or
extended), the data contents, and a time stamp, if enabled (see Section 16.3.3, “Programmer’s Model of
Message Storage”).

The receiver full flag (RXF) (see Section ********, “MSCAN Receiver Flag Register (CANRFLG)”)
signals the status of the foreground receive buffer. When the buffer contains a correctly received message
with a matching identifier, this flag is set.

On reception, each message is checked to see whether it passes the filter (see Section 16.4.3, “Identifier
Acceptance Filter”) and simultaneously is written into the active RxBG. After successful reception of a
valid message, the MSCAN shifts the content of RxBG into the receiver FIFO, sets the RXF flag, and

1. The transmit interrupt occurs only if not masked. A polling scheme can be applied on TXEx also.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 643



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

generates a receive interrupt1 (see Section ********, “Receive Interrupt”) to the CPU. The user’s receive
handler must read the received message from the RxFG and then reset the RXF flag to acknowledge the
interrupt and to release the foreground buffer. A new message, which can follow immediately after the IFS
field of the CAN frame, is received into the next available RxBG. If the MSCAN receives an invalid
message in its RxBG (wrong identifier, transmission errors, etc.) the actual contents of the buffer will be
over-written by the next message. The buffer will then not be shifted into the FIFO.

When the MSCAN module is transmitting, the MSCAN receives its own transmitted messages into the
background receive buffer, RxBG, but does not shift it into the receiver FIFO, generate a receive interrupt,
or acknowledge its own messages on the CAN bus. The exception to this rule is in loopback mode (see
Section ********, “MSCAN Control Register 1 (CANCTL1)”) where the MSCAN treats its own messages
exactly like all other incoming messages. The MSCAN receives its own transmitted messages in the event
that it loses arbitration. If arbitration is lost, the MSCAN must be prepared to become a receiver.

An overrun condition occurs when all receive message buffers in the FIFO are filled with correctly
received messages with accepted identifiers and another message is correctly received from the CAN bus
with an accepted identifier. The latter message is discarded and an error interrupt with overrun indication
is generated if enabled (see Section ********, “Error Interrupt”). The MSCAN remains able to transmit
messages while the receiver FIFO is being filled, but all incoming messages are discarded. As soon as a
receive buffer in the FIFO is available again, new valid messages will be accepted.

16.4.3 Identifier Acceptance Filter
The MSCAN identifier acceptance registers (see Section ********2, “MSCAN Identifier Acceptance
Control Register (CANIDAC)”) define the acceptable patterns of the standard or extended identifier
(ID[10:0] or ID[28:0]). Any of these bits can be marked ‘don’t care’ in the MSCAN identifier mask
registers (see Section ********8, “MSCAN Identifier Mask Registers (CANIDMR0–CANIDMR7)”).

A filter hit is indicated to the application software by a set receive buffer full flag (RXF = 1) and three bits
in the CANIDAC register (see Section ********2, “MSCAN Identifier Acceptance Control Register
(CANIDAC)”). These identifier hit flags (IDHIT[2:0]) clearly identify the filter section that caused the
acceptance. They simplify the application software’s task to identify the cause of the receiver interrupt. If
more than one hit occurs (two or more filters match), the lower hit has priority.

A very flexible programmable generic identifier acceptance filter has been introduced to reduce the CPU
interrupt loading. The filter is programmable to operate in four different modes:

• Two identifier acceptance filters, each to be applied to:
— The full 29 bits of the extended identifier and to the following bits of the CAN 2.0B frame:

– Remote transmission request (RTR)
– Identifier extension (IDE)
– Substitute remote request (SRR)

— The 11 bits of the standard identifier plus the RTR and IDE bits of the CAN 2.0A/B messages.
This mode implements two filters for a full length CAN 2.0B compliant extended identifier.
Although this mode can be used for standard identifiers, it is recommended to use the four or
eight identifier acceptance filters.

1. The receive interrupt occurs only if not masked. A polling scheme can be applied on RXF also.

MC9S12XE-Family Reference Manual  Rev. 1.25

644 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Figure 16-40 shows how the first 32-bit filter bank (CANIDAR0–CANIDAR3,
CANIDMR0–CANIDMR3) produces a filter 0 hit. Similarly, the second filter bank
(CANIDAR4–CANIDAR7, CANIDMR4–CANIDMR7) produces a filter 1 hit.

• Four identifier acceptance filters, each to be applied to:
— The 14 most significant bits of the extended identifier plus the SRR and IDE bits of CAN 2.0B

messages.
— The 11 bits of the standard identifier, the RTR and IDE bits of CAN 2.0A/B messages.

Figure 16-41 shows how the first 32-bit filter bank (CANIDAR0–CANIDAR3,
CANIDMR0–CANIDMR3) produces filter 0 and 1 hits. Similarly, the second filter bank
(CANIDAR4–CANIDAR7, CANIDMR4–CANIDMR7) produces filter 2 and 3 hits.

• Eight identifier acceptance filters, each to be applied to the first 8 bits of the identifier. This mode
implements eight independent filters for the first 8 bits of a CAN 2.0A/B compliant standard
identifier or a CAN 2.0B compliant extended identifier.
Figure 16-42 shows how the first 32-bit filter bank (CANIDAR0–CANIDAR3,
CANIDMR0–CANIDMR3) produces filter 0 to 3 hits. Similarly, the second filter bank
(CANIDAR4–CANIDAR7, CANIDMR4–CANIDMR7) produces filter 4 to 7 hits.

• Closed filter. No CAN message is copied into the foreground buffer RxFG, and the RXF flag is
never set.

CAN 2.0B
Extended Identifier ID28 IDR0 ID21 ID20 IDR1 ID15 ID14 IDR2 ID7 ID6 IDR3 RTR

CAN 2.0A/B
Standard Identifier ID10 IDR0 ID3 ID2 IDR1 IDE ID10 IDR2 ID3 ID10 IDR3 ID3

AM7 CANIDMR0 AM0 AM7 CANIDMR1 AM0 AM7 CANIDMR2 AM0 AM7 CANIDMR3 AM0

AC7 CANIDAR0 AC0 AC7 CANIDAR1 AC0 AC7 CANIDAR2 AC0 AC7 CANIDAR3 AC0

ID Accepted (Filter 0 Hit)

Figure 16-40. 32-bit Maskable Identifier Acceptance Filter

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 645



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

CAN 2.0B
Extended Identifier ID28 IDR0 ID21 ID20 IDR1 ID15 ID14 IDR2 ID7 ID6 IDR3 RTR

CAN 2.0A/B
Standard Identifier ID10 IDR0 ID3 ID2 IDR1 IDE ID10 IDR2 ID3 ID10 IDR3 ID3

AM7 CANIDMR0 AM0 AM7 CANIDMR1 AM0

AC7 CANIDAR0 AC0 AC7 CANIDAR1 AC0

ID Accepted (Filter 0 Hit)

AM7 CANIDMR2 AM0 AM7 CANIDMR3 AM0

AC7 CANIDAR2 AC0 AC7 CANIDAR3 AC0

ID Accepted (Filter 1 Hit)

Figure 16-41. 16-bit Maskable Identifier Acceptance Filters

MC9S12XE-Family Reference Manual  Rev. 1.25

646 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

CAN 2.0B
Extended Identifier ID28 IDR0 ID21 ID20 IDR1 ID15 ID14 IDR2 ID7 ID6 IDR3 RTR

CAN 2.0A/B
Standard Identifier ID10 IDR0 ID3 ID2 IDR1 IDE ID10 IDR2 ID3 ID10 IDR3 ID3

AM7 CIDMR0 AM0

AC7 CIDAR0 AC0

ID Accepted (Filter 0 Hit)

AM7 CIDMR1 AM0

AC7 CIDAR1 AC0

ID Accepted (Filter 1 Hit)

AM7 CIDMR2 AM0

AC7 CIDAR2 AC0

ID Accepted (Filter 2 Hit)

AM7 CIDMR3 AM0

AC7 CIDAR3 AC0

ID Accepted (Filter 3 Hit)

Figure 16-42. 8-bit Maskable Identifier Acceptance Filters

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 647



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

******** Protocol Violation Protection
The MSCAN protects the user from accidentally violating the CAN protocol through programming errors.
The protection logic implements the following features:

• The receive and transmit error counters cannot be written or otherwise manipulated.
• All registers which control the configuration of the MSCAN cannot be modified while the MSCAN

is on-line. The MSCAN has to be in Initialization Mode. The corresponding INITRQ/INITAK
handshake bits in the CANCTL0/CANCTL1 registers (see Section ********, “MSCAN Control
Register 0 (CANCTL0)”) serve as a lock to protect the following registers:
— MSCAN control 1 register (CANCTL1)
— MSCAN bus timing registers 0 and 1 (CANBTR0, CANBTR1)
— MSCAN identifier acceptance control register (CANIDAC)
— MSCAN identifier acceptance registers (CANIDAR0–CANIDAR7)
— MSCAN identifier mask registers (CANIDMR0–CANIDMR7)

• The TXCAN is immediately forced to a recessive state when the MSCAN goes into the power
down mode or initialization mode (see Section ********, “MSCAN Power Down Mode,” and
Section ********, “MSCAN Initialization Mode”).

• The MSCAN enable bit (CANE) is writable only once in normal system operation modes, which
provides further protection against inadvertently disabling the MSCAN.

******** Clock System
Figure 16-43 shows the structure of the MSCAN clock generation circuitry.

MSCAN
Bus Clock

CANCLK Time quanta clock (Tq)
Prescaler
(1 .. 64)

CLKSRC

CLKSRC
Oscillator Clock

Figure 16-43. MSCAN Clocking Scheme

The clock source bit (CLKSRC) in the CANCTL1 register (********/16-613) defines whether the internal
CANCLK is connected to the output of a crystal oscillator (oscillator clock) or to the bus clock.

The clock source has to be chosen such that the tight oscillator tolerance requirements (up to 0.4%) of the
CAN protocol are met. Additionally, for high CAN bus rates (1 Mbps), a 45% to 55% duty cycle of the
clock is required.

If the bus clock is generated from a PLL, it is recommended to select the oscillator clock rather than the
bus clock due to jitter considerations, especially at the faster CAN bus rates.

MC9S12XE-Family Reference Manual  Rev. 1.25

648 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

For microcontrollers without a clock and reset generator (CRG), CANCLK is driven from the crystal
oscillator (oscillator clock).

A programmable prescaler generates the time quanta (Tq) clock from CANCLK. A time quantum is the
atomic unit of time handled by the MSCAN.

Eqn. 16-2
f

= CANCLK
Tq (-----------------------------------------------------Prescaler value)

A bit time is subdivided into three segments as described in the Bosch CAN 2.0A/B specification. (see
Figure 16-44):

• SYNC_SEG: This segment has a fixed length of one time quantum. Signal edges are expected to
happen within this section.

• Time Segment 1: This segment includes the PROP_SEG and the PHASE_SEG1 of the CAN
standard. It can be programmed by setting the parameter TSEG1 to consist of 4 to 16 time quanta.

• Time Segment 2: This segment represents the PHASE_SEG2 of the CAN standard. It can be
programmed by setting the TSEG2 parameter to be 2 to 8 time quanta long.

Eqn. 16-3

f
Bit Rate= Tq

(--------------------------------------------------------------------------------number of Time Quanta)

NRZ Signal

SYNC_SEG Time Segment 1 Time Segment 2
 (PROP_SEG + PHASE_SEG1)  (PHASE_SEG2)

1 4 ... 16 2 ... 8

8 ... 25 Time Quanta
= 1 Bit Time

Transmit Point Sample Point
(single or triple sampling)

Figure 16-44. Segments within the Bit Time

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 649



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Table 16-36. Time Segment Syntax

Syntax Description

System expects transitions to occur on the CAN bus during this
SYNC_SEG

period.

A node in transmit mode transfers a new value to the CAN bus at
Transmit Point

this point.

A node in receive mode samples the CAN bus at this point. If the
Sample Point three samples per bit option is selected, then this point marks the

position of the third sample.

The synchronization jump width (see the Bosch CAN 2.0A/B specification for details) can be programmed
in a range of 1 to 4 time quanta by setting the SJW parameter.

The SYNC_SEG, TSEG1, TSEG2, and SJW parameters are set by programming the MSCAN bus timing
registers (CANBTR0, CANBTR1) (see Section ********, “MSCAN Bus Timing Register 0 (CANBTR0)”
and Section ********, “MSCAN Bus Timing Register 1 (CANBTR1)”).

Table 16-37 gives an overview of the Bosch CAN 2.0A/B specification compliant segment settings and the
related parameter values.

NOTE
It is the user’s responsibility to ensure the bit time settings are in compliance
with the CAN standard.

Table 16-37. Bosch CAN 2.0A/B Compliant Bit Time Segment Settings

     Synchronization
Time Segment 1 TSEG1 Time Segment 2 TSEG2 SJW

Jump Width

5 .. 10 4 .. 9 2 1 1 .. 2 0 .. 1

4 .. 11 3 .. 10 3 2 1 .. 3 0 .. 2

5 .. 12 4 .. 11 4 3 1 .. 4 0 .. 3

6 .. 13 5 .. 12 5 4 1 .. 4 0 .. 3

7 .. 14 6 .. 13 6 5 1 .. 4 0 .. 3

8 .. 15 7 .. 14 7 6 1 .. 4 0 .. 3

9 .. 16 8 .. 15 8 7 1 .. 4 0 .. 3

16.4.4 Modes of Operation

16.4.4.1 Normal System Operating Modes
The MSCAN module behaves as described within this specification in all normal system operating modes.
Write restrictions exist for some registers.

MC9S12XE-Family Reference Manual  Rev. 1.25

650 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

16.4.4.2 Special System Operating Modes
The MSCAN module behaves as described within this specification in all special system operating modes.
Write restrictions which exist on specific registers in normal modes are lifted for test purposes in special
modes.

16.4.4.3 Emulation Modes
In all emulation modes, the MSCAN module behaves just like in normal system operating modes as
described within this specification.

16.4.4.4 Listen-Only Mode
In an optional CAN bus monitoring mode (listen-only), the CAN node is able to receive valid data frames
and valid remote frames, but it sends only “recessive” bits on the CAN bus. In addition, it cannot start a
transmission.

If the MAC sub-layer is required to send a “dominant” bit (ACK bit, overload flag, or active error flag), the
bit is rerouted internally so that the MAC sub-layer monitors this “dominant” bit, although the CAN bus
may remain in recessive state externally.

******** MSCAN Initialization Mode
The MSCAN enters initialization mode when it is enabled (CANE=1).

When entering initialization mode during operation, any on-going transmission or reception is
immediately aborted and synchronization to the CAN bus is lost, potentially causing CAN protocol
violations. To protect the CAN bus system from fatal consequences of violations, the MSCAN
immediately drives TXCAN into a recessive state.

NOTE
The user is responsible for ensuring that the MSCAN is not active when
initialization mode is entered. The recommended procedure is to bring the
MSCAN into sleep mode (SLPRQ = 1 and SLPAK = 1) before setting the
INITRQ bit in the CANCTL0 register. Otherwise, the abort of an on-going
message can cause an error condition and can impact other CAN bus
devices.

In initialization mode, the MSCAN is stopped. However, interface registers remain accessible. This mode
is used to reset the CANCTL0, CANRFLG, CANRIER, CANTFLG, CANTIER, CANTARQ,
CANTAAK, and CANTBSEL registers to their default values. In addition, the MSCAN enables the
configuration of the CANBTR0, CANBTR1 bit timing registers; CANIDAC; and the CANIDAR,
CANIDMR message filters. See Section ********, “MSCAN Control Register 0 (CANCTL0),” for a
detailed description of the initialization mode.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 651



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Bus Clock Domain CAN Clock Domain

INIT
INITRQ SYNC sync. Flag

CPU INITRQ
Init Request

INITAK sync. SYNC
Flag INITAK INITAK

Figure 16-45. Initialization Request/Acknowledge Cycle

Due to independent clock domains within the MSCAN, INITRQ must be synchronized to all domains by
using a special handshake mechanism. This handshake causes additional synchronization delay (see
Figure 16-45).

If there is no message transfer ongoing on the CAN bus, the minimum delay will be two additional bus
clocks and three additional CAN clocks. When all parts of the MSCAN are in initialization mode, the
INITAK flag is set. The application software must use INITAK as a handshake indication for the request
(INITRQ) to go into initialization mode.

NOTE
The CPU cannot clear INITRQ before initialization mode (INITRQ = 1 and
INITAK = 1) is active.

16.4.5 Low-Power Options
If the MSCAN is disabled (CANE = 0), the MSCAN clocks are stopped for power saving.

If the MSCAN is enabled (CANE = 1), the MSCAN has two additional modes with reduced power
consumption, compared to normal mode: sleep and power down mode. In sleep mode, power consumption
is reduced by stopping all clocks except those to access the registers from the CPU side. In power down
mode, all clocks are stopped and no power is consumed.

Table 16-38 summarizes the combinations of MSCAN and CPU modes. A particular combination of
modes is entered by the given settings on the CSWAI and SLPRQ/SLPAK bits.

MC9S12XE-Family Reference Manual  Rev. 1.25

652 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

Table 16-38. CPU vs. MSCAN Operating Modes

MSCAN Mode

Reduced Power Consumption
CPU Mode

Normal Disabled
Sleep Power Down

(CANE=0)

CSWAI = X(1) CSWAI = X CSWAI = X
RUN SLPRQ = 0 SLPRQ = 1 SLPRQ = X

SLPAK = 0 SLPAK = 1 SLPAK = X

CSWAI = 0 CSWAI = 0 CSWAI = 1 CSWAI = X
WAIT SLPRQ = 0 SLPRQ = 1 SLPRQ = X SLPRQ = X

SLPAK = 0 SLPAK = 1 SLPAK = X SLPAK = X

CSWAI = X CSWAI = X
STOP SLPRQ = X SLPRQ = X

SLPAK = X SLPAK = X
1. ‘X’ means don’t care.

16.4.5.1 Operation in Run Mode
As shown in Table 16-38, only MSCAN sleep mode is available as low power option when the CPU is in
run mode.

16.4.5.2 Operation in Wait Mode
The WAI instruction puts the MCU in a low power consumption stand-by mode. If the CSWAI bit is set,
additional power can be saved in power down mode because the CPU clocks are stopped. After leaving
this power down mode, the MSCAN restarts and enters normal mode again.

While the CPU is in wait mode, the MSCAN can be operated in normal mode and generate interrupts
(registers can be accessed via background debug mode).

16.4.5.3 Operation in Stop Mode
The STOP instruction puts the MCU in a low power consumption stand-by mode. In stop mode, the
MSCAN is set in power down mode regardless of the value of the SLPRQ/SLPAK and CSWAI bits
(Table 16-38).

16.4.5.4 MSCAN Normal Mode
This is a non-power-saving mode. Enabling the MSCAN puts the module from disabled mode into normal
mode. In this mode the module can either be in initialization mode or out of initialization mode. See
Section ********, “MSCAN Initialization Mode”.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 653



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

******** MSCAN Sleep Mode
The CPU can request the MSCAN to enter this low power mode by asserting the SLPRQ bit in the
CANCTL0 register. The time when the MSCAN enters sleep mode depends on a fixed synchronization
delay and its current activity:

• If there are one or more message buffers scheduled for transmission (TXEx = 0), the MSCAN will
continue to transmit until all transmit message buffers are empty (TXEx = 1, transmitted
successfully or aborted) and then goes into sleep mode.

• If the MSCAN is receiving, it continues to receive and goes into sleep mode as soon as the CAN
bus next becomes idle.

• If the MSCAN is neither transmitting nor receiving, it immediately goes into sleep mode.

Bus Clock Domain CAN Clock Domain

SLPRQ
SLPRQ SYNC sync. Flag

CPU SLPRQ
Sleep Request

SLPAK sync. SYNC
Flag SLPAK SLPAK

MSCAN
in Sleep Mode

Figure 16-46. Sleep Request / Acknowledge Cycle

NOTE
The application software must avoid setting up a transmission (by clearing
one or more TXEx flag(s)) and immediately request sleep mode (by setting
SLPRQ). Whether the MSCAN starts transmitting or goes into sleep mode
directly depends on the exact sequence of operations.

If sleep mode is active, the SLPRQ and SLPAK bits are set (Figure 16-46). The application software must
use SLPAK as a handshake indication for the request (SLPRQ) to go into sleep mode.

When in sleep mode (SLPRQ = 1 and SLPAK = 1), the MSCAN stops its internal clocks. However, clocks
that allow register accesses from the CPU side continue to run.

If the MSCAN is in bus-off state, it stops counting the 128 occurrences of 11 consecutive recessive bits
due to the stopped clocks. TXCAN remains in a recessive state. If RXF = 1, the message can be read and
RXF can be cleared. Shifting a new message into the foreground buffer of the receiver FIFO (RxFG) does
not take place while in sleep mode.

It is possible to access the transmit buffers and to clear the associated TXE flags. No message abort takes
place while in sleep mode.

MC9S12XE-Family Reference Manual  Rev. 1.25

654 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

If the WUPE bit in CANCTL0 is not asserted, the MSCAN will mask any activity it detects on CAN.
RXCAN is therefore held internally in a recessive state. This locks the MSCAN in sleep mode. WUPE
must be set before entering sleep mode to take effect.

The MSCAN is able to leave sleep mode (wake up) only when:
• CAN bus activity occurs and WUPE = 1

or
• the CPU clears the SLPRQ bit

NOTE
The CPU cannot clear the SLPRQ bit before sleep mode (SLPRQ = 1 and
SLPAK = 1) is active.

After wake-up, the MSCAN waits for 11 consecutive recessive bits to synchronize to the CAN bus. As a
consequence, if the MSCAN is woken-up by a CAN frame, this frame is not received.

The receive message buffers (RxFG and RxBG) contain messages if they were received before sleep mode
was entered. All pending actions will be executed upon wake-up; copying of RxBG into RxFG, message
aborts and message transmissions. If the MSCAN remains in bus-off state after sleep mode was exited, it
continues counting the 128 occurrences of 11 consecutive recessive bits.

******** MSCAN Power Down Mode
The MSCAN is in power down mode (Table 16-38) when

• CPU is in stop mode
or

• CPU is in wait mode and the CSWAI bit is set

When entering the power down mode, the MSCAN immediately stops all ongoing transmissions and
receptions, potentially causing CAN protocol violations. To protect the CAN bus system from fatal
consequences of violations to the above rule, the MSCAN immediately drives TXCAN into a recessive
state.

NOTE
The user is responsible for ensuring that the MSCAN is not active when
power down mode is entered. The recommended procedure is to bring the
MSCAN into Sleep mode before the STOP or WAI instruction (if CSWAI
is set) is executed. Otherwise, the abort of an ongoing message can cause an
error condition and impact other CAN bus devices.

In power down mode, all clocks are stopped and no registers can be accessed. If the MSCAN was not in
sleep mode before power down mode became active, the module performs an internal recovery cycle after
powering up. This causes some fixed delay before the module enters normal mode again.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 655



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

16.4.5.7 Disabled Mode
The MSCAN is in disabled mode out of reset (CANE=0). All module clocks are stopped for power saving,
however the register map can still be accessed as specified.

16.4.5.8 Programmable Wake-Up Function
The MSCAN can be programmed to wake up from sleep or power down mode as soon as CAN bus activity
is detected (see control bit WUPE in MSCAN Control Register 0 (CANCTL0). The sensitivity to existing
CAN bus action can be modified by applying a low-pass filter function to the RXCAN input line (see
control bit WUPM in Section ********, “MSCAN Control Register 1 (CANCTL1)”).

This feature can be used to protect the MSCAN from wake-up due to short glitches on the CAN bus lines.
Such glitches can result from—for example—electromagnetic interference within noisy environments.

16.4.6 Reset Initialization
The reset state of each individual bit is listed in Section 16.3.2, “Register Descriptions,” which details all
the registers and their bit-fields.

16.4.7 Interrupts
This section describes all interrupts originated by the MSCAN. It documents the enable bits and generated
flags. Each interrupt is listed and described separately.

******** Description of Interrupt Operation
The MSCAN supports four interrupt vectors (see Table 16-39), any of which can be individually masked
(for details see Section ********, “MSCAN Receiver Interrupt Enable Register (CANRIER)” to
Section ********, “MSCAN Transmitter Interrupt Enable Register (CANTIER)”).

Refer to the device overview section to determine the dedicated interrupt vector addresses.

Table 16-39. Interrupt Vectors

Interrupt Source CCR Mask Local Enable
Wake-Up Interrupt (WUPIF) I bit CANRIER (WUPIE)
Error Interrupts Interrupt (CSCIF, OVRIF) I bit CANRIER (CSCIE, OVRIE)
Receive Interrupt (RXF) I bit CANRIER (RXFIE)
Transmit Interrupts (TXE[2:0]) I bit CANTIER (TXEIE[2:0])

******** Transmit Interrupt
At least one of the three transmit buffers is empty (not scheduled) and can be loaded to schedule a message
for transmission. The TXEx flag of the empty message buffer is set.

MC9S12XE-Family Reference Manual  Rev. 1.25

656 Freescale Semiconductor



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

******** Receive Interrupt
A message is successfully received and shifted into the foreground buffer (RxFG) of the receiver FIFO.
This interrupt is generated immediately after receiving the EOF symbol. The RXF flag is set. If there are
multiple messages in the receiver FIFO, the RXF flag is set as soon as the next message is shifted to the
foreground buffer.

******** Wake-Up Interrupt
A wake-up interrupt is generated if activity on the CAN bus occurs during MSCAN sleep or power-down
mode.

NOTE
This interrupt can only occur if the MSCAN was in sleep mode (SLPRQ = 1
and SLPAK = 1) before entering power down mode, the wake-up option is
enabled (WUPE = 1), and the wake-up interrupt is enabled (WUPIE = 1).

******** Error Interrupt
An error interrupt is generated if an overrun of the receiver FIFO, error, warning, or bus-off condition
occurrs. MSCAN Receiver Flag Register (CANRFLG) indicates one of the following conditions:

• Overrun — An overrun condition of the receiver FIFO as described in Section ********, “Receive
Structures,” occurred.

• CAN Status Change — The actual value of the transmit and receive error counters control the
CAN bus state of the MSCAN. As soon as the error counters skip into a critical range (Tx/Rx-
warning, Tx/Rx-error, bus-off) the MSCAN flags an error condition. The status change, which
caused the error condition, is indicated by the TSTAT and RSTAT flags (see Section ********,
“MSCAN Receiver Flag Register (CANRFLG)” and Section ********, “MSCAN Receiver
Interrupt Enable Register (CANRIER)”).

******** Interrupt Acknowledge
Interrupts are directly associated with one or more status flags in either the MSCAN Receiver Flag Register
(CANRFLG) or the MSCAN Transmitter Flag Register (CANTFLG). Interrupts are pending as long as
one of the corresponding flags is set. The flags in CANRFLG and CANTFLG must be reset within the
interrupt handler to handshake the interrupt. The flags are reset by writing a 1 to the corresponding bit
position. A flag cannot be cleared if the respective condition prevails.

NOTE
It must be guaranteed that the CPU clears only the bit causing the current
interrupt. For this reason, bit manipulation instructions (BSET) must not be
used to clear interrupt flags. These instructions may cause accidental
clearing of interrupt flags which are set after entering the current interrupt
service routine.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 657



Chapter 16 Freescale’s Scalable Controller Area Network (S12MSCANV3)

16.5 Initialization/Application Information

16.5.1 MSCAN initialization
The procedure to initially start up the MSCAN module out of reset is as follows:

1. Assert CANE
2. Write to the configuration registers in initialization mode
3. Clear INITRQ to leave initialization mode

If the configuration of registers which are only writable in initialization mode shall be changed:
1. Bring the module into sleep mode by setting SLPRQ and awaiting SLPAK to assert after the CAN

bus becomes idle.
2. Enter initialization mode: assert INITRQ and await INITAK
3. Write to the configuration registers in initialization mode
4. Clear INITRQ to leave initialization mode and continue

16.5.2 Bus-Off Recovery
The bus-off recovery is user configurable. The bus-off state can either be left automatically or on user
request.

For reasons of backwards compatibility, the MSCAN defaults to automatic recovery after reset. In this
case, the MSCAN will become error active again after counting 128 occurrences of 11 consecutive
recessive bits on the CAN bus (see the Bosch CAN 2.0 A/B specification for details).

If the MSCAN is configured for user request (BORM set in MSCAN Control Register 1 (CANCTL1)), the
recovery from bus-off starts after both independent events have become true:

• 128 occurrences of 11 consecutive recessive bits on the CAN bus have been monitored
• BOHOLD in MSCAN Miscellaneous Register (CANMISC) has been cleared by the user

These two events may occur in any order.

MC9S12XE-Family Reference Manual  Rev. 1.25

658 Freescale Semiconductor