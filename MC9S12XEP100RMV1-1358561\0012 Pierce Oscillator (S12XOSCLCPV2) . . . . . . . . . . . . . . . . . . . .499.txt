﻿Chapter 12
Pierce Oscillator (S12XOSCLCPV2)

Table 12-1. Revision History

Revision Revision Sections
Description of Changes

Number Date Affected
V01.05 19 Jul 2006 - All xclks info was removed
V02.00 04 Aug 2006 - Incremented revision to match the design system spec revision

12.1 Introduction
The Pierce oscillator (XOSC) module provides a robust, low-noise and low-power clock source. The
module will be operated from the VDDPLL supply rail (1.8 V nominal) and require the minimum number
of external components. It is designed for optimal start-up margin with typical crystal oscillators.

12.1.1 Features
The XOSC will contain circuitry to dynamically control current gain in the output amplitude. This ensures
a signal with low harmonic distortion, low power and good noise immunity.

• High noise immunity due to input hysteresis
• Low RF emissions with peak-to-peak swing limited dynamically
• Transconductance (gm) sized for optimum start-up margin for typical oscillators
• Dynamic gain control eliminates the need for external current limiting resistor
• Integrated resistor eliminates the need for external bias resistor in loop controlled Pierce mode.
• Low power consumption:

— Operates from 1.8 V (nominal) supply
— Amplitude control limits power

• Clock monitor

12.1.2 Modes of Operation
Two modes of operation exist:

1. Loop controlled Pierce (LCP) oscillator
2. External square wave mode featuring also full swing Pierce (FSP) without internal bias resistor

The oscillator mode selection is described in the Device Overview section, subsection Oscillator
Configuration.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 499



Chapter 12 Pierce Oscillator (S12XOSCLCPV2)

12.1.3 Block Diagram
Figure 12-1 shows a block diagram of the XOSC.

Monitor_Failure
Clock

Monitor

OSCCLK

Peak
Detector Gain Control

VDDPLL = 1.8 V

Rf

EXTAL XTAL

Figure 12-1. XOSC Block Diagram

12.2 External Signal Description
This section lists and describes the signals that connect off chip

12.2.1 VDDPLL and VSSPLL — Operating and Ground Voltage Pins
Theses pins provides operating voltage (VDDPLL) and ground (VSSPLL) for the XOSC circuitry. This
allows the supply voltage to the XOSC to use an independent bypass capacitor.

12.2.2 EXTAL and XTAL — Input and Output Pins
These pins provide the interface for either a crystal or a 1.8V CMOS compatible clock to control the
internal clock generator circuitry. EXTAL is the external clock input or the input to the crystal oscillator
amplifier. XTAL is the output of the crystal oscillator amplifier. The MCU internal system clock is derived

MC9S12XE-Family Reference Manual  Rev. 1.25

500 Freescale Semiconductor



Chapter 12 Pierce Oscillator (S12XOSCLCPV2)

from the EXTAL input frequency. In full stop mode (PSTP = 0), the EXTAL pin is pulled down by an
internal resistor of typical 200 kΩ.

NOTE
Freescale recommends an evaluation of the application board and chosen
resonator or crystal by the resonator or crystal supplier.

Loop controlled circuit is not suited for overtone resonators and crystals.

EXTAL
C1

MCU Crystal or
Ceramic Resonator

XTAL
C2

VSSPLL

Figure 12-2. Loop Controlled Pierce Oscillator Connections (LCP mode selected)

NOTE
Full swing Pierce circuit is not suited for overtone resonators and crystals
without a careful component selection.

EXTAL
C1

MCU RB Crystal or
Ceramic Resonator

RS*
XTAL

C2
VSSPLL

* Rs can be zero (shorted) when use with higher frequency crystals.
Refer to manufacturer’s data.

Figure 12-3. Full Swing Pierce Oscillator Connections (FSP mode selected)

CMOS Compatible
EXTAL External Oscillator

(VDDPLL Level)

MCU

XTAL Not Connected

Figure 12-4. External Clock Connections (FSP mode selected)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 501



Chapter 12 Pierce Oscillator (S12XOSCLCPV2)

12.3 Memory Map and Register Definition
The CRG contains the registers and associated bits for controlling and monitoring the oscillator module.

12.4 Functional Description
The XOSC module has control circuitry to maintain the crystal oscillator circuit voltage level to an optimal
level which is determined by the amount of hysteresis being used and the maximum oscillation range.

The oscillator block has two external pins, EXTAL and XTAL. The oscillator input pin, EXTAL, is
intended to be connected to either a crystal or an external clock source. The XTAL pin is an output signal
that provides crystal circuit feedback.

A buffered EXTAL signal becomes the internal clock. To improve noise immunity, the oscillator is
powered by the VDDPLL and VSSPLL power supply pins.

12.4.1 Gain Control
In LCP mode a closed loop control system will be utilized whereby the amplifier is modulated to keep the
output waveform sinusoidal and to limit the oscillation amplitude. The output peak to peak voltage will be
kept above twice the maximum hysteresis level of the input buffer. Electrical specification details are
provided in the Electrical Characteristics appendix.

12.4.2 Clock Monitor
The clock monitor circuit is based on an internal RC time delay so that it can operate without any MCU
clocks. If no OSCCLK edges are detected within this RC time delay, the clock monitor indicates failure
which asserts self-clock mode or generates a system reset depending on the state of SCME bit. If the clock
monitor is disabled or the presence of clocks is detected no failure is indicated.The clock monitor function
is enabled/disabled by the CME control bit, described in the CRG block description chapter.

12.4.3 Wait Mode Operation
During wait mode, XOSC is not impacted.

12.4.4 Stop Mode Operation
XOSC is placed in a static state when the part is in stop mode except when pseudo-stop mode is enabled.
During pseudo-stop mode, XOSC is not impacted.

MC9S12XE-Family Reference Manual  Rev. 1.25

502 Freescale Semiconductor