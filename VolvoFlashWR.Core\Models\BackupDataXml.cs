using System;
using System.Collections.Generic;
using System.Xml.Serialization;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// XML-serializable version of backup data
    /// </summary>
    [XmlRoot("Backup")]
    public class BackupDataXml
    {
        /// <summary>
        /// Gets or sets the unique identifier for the backup
        /// </summary>
        [XmlElement("Id")]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the ECU ID
        /// </summary>
        [XmlElement("ECUId")]
        public string ECUId { get; set; }

        /// <summary>
        /// Gets or sets the ECU name
        /// </summary>
        [XmlElement("ECUName")]
        public string ECUName { get; set; }

        /// <summary>
        /// Gets or sets the ECU serial number
        /// </summary>
        [XmlElement("ECUSerialNumber")]
        public string ECUSerialNumber { get; set; }

        /// <summary>
        /// Gets or sets the ECU hardware version
        /// </summary>
        [XmlElement("ECUHardwareVersion")]
        public string ECUHardwareVersion { get; set; }

        /// <summary>
        /// Gets or sets the ECU software version
        /// </summary>
        [XmlElement("ECUSoftwareVersion")]
        public string ECUSoftwareVersion { get; set; }

        /// <summary>
        /// Gets or sets the backup description
        /// </summary>
        [XmlElement("Description")]
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the backup creation time
        /// </summary>
        [XmlElement("CreationTime")]
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// Gets or sets the backup last modified time
        /// </summary>
        [XmlElement("LastModifiedTime")]
        public DateTime LastModifiedTime { get; set; }

        /// <summary>
        /// Gets or sets the user who created the backup
        /// </summary>
        [XmlElement("CreatedBy")]
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the user who last modified the backup
        /// </summary>
        [XmlElement("LastModifiedBy")]
        public string LastModifiedBy { get; set; }

        /// <summary>
        /// Gets or sets the backup category
        /// </summary>
        [XmlElement("Category")]
        public string Category { get; set; }

        /// <summary>
        /// Gets or sets the backup tags
        /// </summary>
        [XmlArray("Tags")]
        [XmlArrayItem("Tag")]
        public string[] Tags { get; set; }

        /// <summary>
        /// Gets or sets the backup version
        /// </summary>
        [XmlElement("Version")]
        public int Version { get; set; }

        /// <summary>
        /// Gets or sets the parent backup ID
        /// </summary>
        [XmlElement("ParentBackupId")]
        public string ParentBackupId { get; set; }

        /// <summary>
        /// Gets or sets the root backup ID
        /// </summary>
        [XmlElement("RootBackupId")]
        public string RootBackupId { get; set; }

        /// <summary>
        /// Gets or sets the version notes
        /// </summary>
        [XmlElement("VersionNotes")]
        public string VersionNotes { get; set; }

        /// <summary>
        /// Gets or sets the version creation time
        /// </summary>
        [XmlElement("VersionCreationTime")]
        public DateTime? VersionCreationTime { get; set; }

        /// <summary>
        /// Gets or sets whether this is the latest version
        /// </summary>
        [XmlElement("IsLatestVersion")]
        public bool IsLatestVersion { get; set; }

        /// <summary>
        /// Gets or sets the backup checksum
        /// </summary>
        [XmlElement("Checksum")]
        public string Checksum { get; set; }

        /// <summary>
        /// Gets or sets the EEPROM data as a Base64 string
        /// </summary>
        [XmlElement("EEPROMData")]
        public string EEPROMData { get; set; }

        /// <summary>
        /// Gets or sets the microcontroller code as a Base64 string
        /// </summary>
        [XmlElement("MicrocontrollerCode")]
        public string MicrocontrollerCode { get; set; }

        /// <summary>
        /// Gets or sets the parameters
        /// </summary>
        [XmlArray("Parameters")]
        [XmlArrayItem("Parameter")]
        public ParameterEntry[] Parameters { get; set; }
    }

    /// <summary>
    /// XML-serializable parameter entry
    /// </summary>
    public class ParameterEntry
    {
        /// <summary>
        /// Gets or sets the parameter name
        /// </summary>
        [XmlAttribute("Name")]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the parameter value as a string
        /// </summary>
        [XmlAttribute("Value")]
        public string Value { get; set; }

        /// <summary>
        /// Gets or sets the parameter type
        /// </summary>
        [XmlAttribute("Type")]
        public string Type { get; set; }
    }
}
