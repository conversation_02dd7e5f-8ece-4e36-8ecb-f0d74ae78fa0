using System;
using System.Threading.Tasks;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for WiFi communication service
    /// </summary>
    public interface IWiFiCommunicationService
    {
        /// <summary>
        /// Event triggered when a WiFi connection is established
        /// </summary>
        event EventHandler<string> WiFiConnected;

        /// <summary>
        /// Event triggered when a WiFi connection is lost
        /// </summary>
        event EventHandler<string> WiFiDisconnected;

        /// <summary>
        /// Event triggered when an error occurs during WiFi communication
        /// </summary>
        event EventHandler<string> WiFiError;

        /// <summary>
        /// Initializes the WiFi communication service
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        Task<bool> InitializeAsync();

        /// <summary>
        /// Checks if WiFi is available
        /// </summary>
        /// <returns>True if WiFi is available, false otherwise</returns>
        Task<bool> IsWiFiAvailableAsync();

        /// <summary>
        /// Enables WiFi if it is disabled
        /// </summary>
        /// <returns>True if WiFi is successfully enabled, false otherwise</returns>
        Task<bool> EnableWiFiAsync();

        /// <summary>
        /// Connects to a device via WiFi
        /// </summary>
        /// <param name="ipAddress">The IP address of the device</param>
        /// <param name="port">The port to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        Task<bool> ConnectToDeviceAsync(string ipAddress, int port);

        /// <summary>
        /// Disconnects from a device
        /// </summary>
        /// <param name="ipAddress">The IP address of the device</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        Task<bool> DisconnectFromDeviceAsync(string ipAddress);

        /// <summary>
        /// Sends data to a device
        /// </summary>
        /// <param name="ipAddress">The IP address of the device</param>
        /// <param name="data">The data to send</param>
        /// <returns>True if data is sent successfully, false otherwise</returns>
        Task<bool> SendDataAsync(string ipAddress, byte[] data);

        /// <summary>
        /// Receives data from a device
        /// </summary>
        /// <param name="ipAddress">The IP address of the device</param>
        /// <param name="timeout">The timeout in milliseconds</param>
        /// <returns>The received data</returns>
        Task<byte[]> ReceiveDataAsync(string ipAddress, int timeout);

        /// <summary>
        /// Scans for available WiFi networks
        /// </summary>
        /// <returns>List of available WiFi networks</returns>
        Task<string[]> ScanNetworksAsync();

        /// <summary>
        /// Connects to a WiFi network
        /// </summary>
        /// <param name="ssid">The SSID of the network</param>
        /// <param name="password">The password for the network</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        Task<bool> ConnectToNetworkAsync(string ssid, string password);
    }
}
