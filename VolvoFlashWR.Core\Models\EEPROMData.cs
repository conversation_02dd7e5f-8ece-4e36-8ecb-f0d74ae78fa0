using System;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents EEPROM data from an ECU
    /// </summary>
    public class EEPROMData
    {
        /// <summary>
        /// The unique identifier for this EEPROM data
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// The ECU ID this EEPROM data belongs to
        /// </summary>
        public string ECUId { get; set; } = string.Empty;

        /// <summary>
        /// The ECU name this EEPROM data belongs to
        /// </summary>
        public string ECUName { get; set; } = string.Empty;

        /// <summary>
        /// The timestamp when this EEPROM data was read
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// The raw EEPROM data bytes
        /// </summary>
        public byte[] Data { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// The size of the EEPROM data in bytes
        /// </summary>
        public int Size => Data?.Length ?? 0;

        /// <summary>
        /// The checksum of the EEPROM data
        /// </summary>
        public string Checksum { get; set; } = string.Empty;

        /// <summary>
        /// The version of the EEPROM data
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// Additional metadata about the EEPROM data
        /// </summary>
        public string Metadata { get; set; } = string.Empty;

        /// <summary>
        /// Calculates the checksum of the EEPROM data
        /// </summary>
        /// <returns>The calculated checksum</returns>
        public string CalculateChecksum()
        {
            if (Data == null || Data.Length == 0)
                return string.Empty;

            // Simple checksum calculation (CRC32 would be better in a real implementation)
            uint checksum = 0;
            foreach (byte b in Data)
            {
                checksum += b;
            }

            return checksum.ToString("X8");
        }

        /// <summary>
        /// Validates the EEPROM data against the stored checksum
        /// </summary>
        /// <returns>True if the data is valid, false otherwise</returns>
        public bool Validate()
        {
            if (string.IsNullOrEmpty(Checksum) || Data == null || Data.Length == 0)
                return false;

            string calculatedChecksum = CalculateChecksum();
            return string.Equals(calculatedChecksum, Checksum, StringComparison.OrdinalIgnoreCase);
        }
    }
}
