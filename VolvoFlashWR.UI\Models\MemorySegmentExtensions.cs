using System;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.UI.Models
{
    /// <summary>
    /// Extension methods for the MemorySegment class
    /// </summary>
    public static class MemorySegmentExtensions
    {
        /// <summary>
        /// Gets the start address as a hexadecimal string
        /// </summary>
        /// <param name="segment">The memory segment</param>
        /// <returns>The start address as a hexadecimal string</returns>
        public static string GetStartAddressHex(this MemorySegment segment)
        {
            return $"0x{segment.StartAddress:X8}";
        }
        
        /// <summary>
        /// Gets the end address as a hexadecimal string
        /// </summary>
        /// <param name="segment">The memory segment</param>
        /// <returns>The end address as a hexadecimal string</returns>
        public static string GetEndAddressHex(this MemorySegment segment)
        {
            return $"0x{segment.EndAddress:X8}";
        }
        
        /// <summary>
        /// Gets the size as a formatted string
        /// </summary>
        /// <param name="segment">The memory segment</param>
        /// <returns>The size as a formatted string</returns>
        public static string GetSizeFormatted(this MemorySegment segment)
        {
            return FormatByteSize(segment.Size);
        }
        
        /// <summary>
        /// Formats a byte size to a human-readable string
        /// </summary>
        /// <param name="bytes">The size in bytes</param>
        /// <returns>A formatted string</returns>
        private static string FormatByteSize(int bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
