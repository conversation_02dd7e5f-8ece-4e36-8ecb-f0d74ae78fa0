using System;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Diagnostics;
using VolvoFlashWR.Core.ErrorHandling;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for the flash operation monitor service
    /// </summary>
    public interface IFlashOperationMonitorService : IDisposable
    {
        /// <summary>
        /// Gets the flash operation monitor
        /// </summary>
        FlashOperationMonitor Monitor { get; }

        /// <summary>
        /// Gets the flash operation error handler
        /// </summary>
        FlashOperationErrorHandler ErrorHandler { get; }

        /// <summary>
        /// Initializes the service
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        Task<bool> InitializeAsync();

        /// <summary>
        /// Handles an error in a flash operation
        /// </summary>
        /// <param name="operationId">The operation ID</param>
        /// <param name="errorType">The type of error</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="errorData">Additional error data</param>
        /// <returns>A recovery result indicating the outcome of the recovery attempt</returns>
        Task<FlashErrorRecoveryResult> HandleErrorAsync(Guid operationId, FlashErrorType errorType, string errorMessage, object? errorData = null);
    }
}
