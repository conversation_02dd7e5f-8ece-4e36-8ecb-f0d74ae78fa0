﻿Chapter 17
Periodic Interrupt Timer (S12PIT24B8CV2)

Table 17-1. Revision History

Revision Sections
RevisionDate Description of Changes

Number Affected
V01.00 28 Apr 2005 - Initial Release.
V01.01 05 Jul 2005 17.6/17-674 - Added application section.

- Removed table 1-1.

17.1 Introduction
The period interrupt timer (PIT) is an array of 24-bit timers that can be used to trigger peripheral modules
or raise periodic interrupts. Refer to Figure 17-1 for a simplified block diagram.

17.1.1 Glossary
Acronyms and Abbreviations

PIT Periodic Interrupt Timer
ISR Interrupt Service Routine
CCR Condition Code Register
SoC System on Chip

micro time bases clock periods of the 16-bit timer modulus down-counters, which are generated by the 8-bit
modulus down-counters.

17.1.2 Features
The PIT includes these features:

• Eight timers implemented as modulus down-counters with independent time-out periods.
• Time-out periods selectable between 1 and 224 bus clock cycles. Time-out equals m*n bus clock

cycles with 1 <= m <= 256 and 1 <= n <= 65536.
• Timers that can be enabled individually.
• Eight time-out interrupts.
• Eight time-out trigger output signals available to trigger peripheral modules.
• Start of timer channels can be aligned to each other.

17.1.3 Modes of Operation
Refer to the device overview for a detailed explanation of the chip modes.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 659



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

• Run mode
This is the basic mode of operation.

• Wait mode
PIT operation in wait mode is controlled by the PITSWAI bit located in the PITCFLMT register.
In wait mode, if the bus clock is globally enabled and if the PITSWAI bit is clear, the PIT operates
like in run mode. In wait mode, if the PITSWAI bit is set, the PIT module is stalled.

• Stop mode
In full stop mode or pseudo stop mode, the PIT module is stalled.

• Freeze mode
PIT operation in freeze mode is controlled by the PITFRZ bit located in the PITCFLMT register.
In freeze mode, if the PITFRZ bit is clear, the PIT operates like in run mode. In freeze mode, if the
PITFRZ bit is set, the PIT module is stalled.

17.1.4 Block Diagram
Figure 17-1 shows a block diagram of the PIT module.

Micro Time Interrupt 0
Time-Out 0

8-Bit Base 0 16-Bit Timer 0 Interface Trigger 0
Bus Clock Micro Timer 0

Interrupt 1
Time-Out 1

16-Bit Timer 1 Interface Trigger 1

8-Bit Micro
Time Interrupt 2

Micro Timer 1 Time-Out 2
Base 1 16-Bit Timer 2 Interface Trigger 2

Interrupt 3
Time-Out 3

16-Bit Timer 3 Interface Trigger 3

Interrupt 4
Time-Out 4

16-Bit Timer 4 Interface Trigger 4

Interrupt 5
Time-Out 5

16-Bit Timer 5 Interface Trigger 5

Interrupt 6
Time-Out 6

16-Bit Timer 6 Interface Trigger 6

Interrupt 7
Time-Out 7

16-Bit Timer 7 Interface Trigger 7

Figure 17-1. PIT24B8C Block Diagram

17.2 External Signal Description
The PIT module has no external pins.

MC9S12XE-Family Reference Manual  Rev. 1.25

660 Freescale Semiconductor



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

17.3 Register Definition
This section consists of register descriptions in address order of the PIT. Each description includes a
standard register diagram with an associated figure number. Details of register bit and field function follow
the register diagrams, in bit order.

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0000 R 0 0 0 0 0
PITCFLMT PITE PITSWAI PITFRZ

W PFLMT1 PFLMT0

0x0001 R 0 0 0 0 0 0 0 0
PITFLT W PFLT7 PFLT6 PFLT5 PFLT4 PFLT3 PFLT2 PFLT1 PFLT0

0x0002 R
PITCE PCE7 PCE6 PCE5 PCE4 PCE3 PCE2 PCE1 PCE0

W

0x0003 R
PITMUX PMUX7 PMUX6 PMUX5 PMUX4 PMUX3 PMUX2 PMUX1 PMUX0

W

0x0004 R
PITINTE PINTE7 PINTE6 PINTE5 PINTE4 PINTE3 PINTE2 PINTE1 PINTE0

W

0x0005 R
PITTF PTF7 PTF6 PTF5 PTF4 PTF3 PTF2 PTF1 PTF0

W

0x0006 R
PITMTLD0 PMTLD7 PMTLD6 PMTLD5 PMTLD4 PMTLD3 PMTLD2 PMTLD1 PMTLD0

W

0x0007 R
PITMTLD1 PMTLD7 PMTLD6 PMTLD5 PMTLD4 PMTLD3 PMTLD2 PMTLD1 PMTLD0

W

0x0008 R
PITLD0 (High) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8

W

0x0009 R
PITLD0 (Low) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0

W

0x000A R
PITCNT0 (High) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8

W

0x000B R
PITCNT0 (Low) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0

W

0x000C R
PITLD1 (High) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8

W

= Unimplemented or Reserved

Figure 17-2. PIT Register Summary (Sheet 1 of 3)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 661



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x000D R
PITLD1 (Low) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0

W

0x000E R
PITCNT1 (High) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8

W

0x000F R
PITCNT1 (Low) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0

W

0x0010 R
PITLD2 (High) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8

W

0x0011 R
PITLD2 (Low) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0

W

0x0012 R
PITCNT2 (High) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8

W

0x0013 R
PITCNT2 (Low) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0

W

0x0014 R
PITLD3 (High) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8

W

0x0015 R
PITLD3 (Low) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0

W

0x0016 R
PITCNT3 (High) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8

W

0x0017 R
PITCNT3 (Low) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0

W

0x0018 R
PITLD4 (High) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8

W

0x0019 R
PITLD4 (Low) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0

W

0x001A R
PITCNT4 (High) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8

W

0x001B R
PITCNT4 (Low) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0

W

= Unimplemented or Reserved

Figure 17-2. PIT Register Summary (Sheet 2 of 3)

MC9S12XE-Family Reference Manual  Rev. 1.25

662 Freescale Semiconductor



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x001C R
PITLD5 (High) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8

W

0x001D R
PITLD5 (Low) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0

W

0x001E R
PITCNT5 (High) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8

W

0x001F R
PITCNT5 (Low) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0

W

0x0020 R
PITLD6 (High) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8

W

0x0021 R
PITLD6 (Low) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0

W

0x0022 R
PITCNT6 (High) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8

W

0x0023 R
PITCNT6 (Low) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0

W

0x0024 R
PITLD7 (High) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8

W

0x0025 R
PITLD7 (Low) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0

W

0x0026 R
PITCNT7 (High) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8

W

0x0027 R
PITCNT7 (Low) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0

W

= Unimplemented or Reserved

Figure 17-2. PIT Register Summary (Sheet 3 of 3)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 663



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

******** PIT Control and Force Load Micro Timer Register (PITCFLMT)

Module Base + 0x0000

7 6 5 4 3 2 1 0
R 0 0 0 0 0

PITE PITSWAI PITFRZ
W PFLMT1 PFLMT0

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 17-3. PIT Control and Force Load Micro Timer Register (PITCFLMT)

Read: Anytime

Write: Anytime; writes to the reserved bits have no effect

Table 17-2. PITCFLMT Field Descriptions

Field Description

7 PIT Module Enable Bit — This bit enables the PIT module. If PITE is cleared, the PIT module is disabled and
PITE flag bits in the PITTF register are cleared. When PITE is set, individually enabled timers (PCE set) start down-

counting with the corresponding load register values.
0 PIT disabled (lower power consumption).
1 PIT is enabled.

6 PIT Stop in Wait Mode Bit — This bit is used for power conservation while in wait mode.
PITSWAI 0 PIT operates normally in wait mode

1 PIT clock generation stops and freezes the PIT module when in wait mode

5 PIT Counter Freeze while in Freeze Mode Bit — When during debugging a breakpoint (freeze mode) is
PITFRZ encountered it is useful in many cases to freeze the PIT counters to avoid e.g. interrupt generation. The PITFRZ

bit controls the PIT operation while in freeze mode.
0 PIT operates normally in freeze mode
1 PIT counters are stalled when in freeze mode

1:0 PIT Force Load Bits for Micro Timer 1:0 — These bits have only an effect if the corresponding micro timer is
PFLMT[1:0] active and if the PIT module is enabled (PITE set). Writing a one into a PFLMT bit loads the corresponding 8-bit

micro timer load register into the 8-bit micro timer down-counter. Writing a zero has no effect. Reading these bits
will always return zero.
Note: A micro timer force load affects all timer channels that use the corresponding micro time base.

******** PIT Force Load Timer Register (PITFLT)

Module Base + 0x0001

7 6 5 4 3 2 1 0
R 0 0 0 0 0 0 0 0
W PFLT7 PFLT6 PFLT5 PFLT4 PFLT3 PFLT2 PFLT1 PFLT0

Reset 0 0 0 0 0 0 0 0

Figure 17-4. PIT Force Load Timer Register (PITFLT)

Read: Anytime

Write: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

664 Freescale Semiconductor



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

Table 17-3. PITFLT Field Descriptions

Field Description

7:0 PIT Force Load Bits for Timer 7-0 — These bits have only an effect if the corresponding timer channel (PCE
PFLT[7:0] set) is enabled and if the PIT module is enabled (PITE set). Writing a one into a PFLT bit loads the corresponding

16-bit timer load register into the 16-bit timer down-counter. Writing a zero has no effect. Reading these bits will
always return zero.

******** PIT Channel Enable Register (PITCE)
Module Base + 0x0002

7 6 5 4 3 2 1 0
R

PCE7 PCE6 PCE5 PCE4 PCE3 PCE2 PCE1 PCE0
W

Reset 0 0 0 0 0 0 0 0

Figure 17-5. PIT Channel Enable Register (PITCE)

Read: Anytime

Write: Anytime

Table 17-4. PITCE Field Descriptions

Field Description

7:0 PIT Enable Bits for Timer Channel 7:0 — These bits enable the PIT channels 7-0. If PCE is cleared, the PIT
PCE[7:0] channel is disabled and the corresponding flag bit in the PITTF register is cleared. When PCE is set, and if the

PIT module is enabled (PITE = 1) the 16-bit timer counter is loaded with the start count value and starts down-
counting.
0 The corresponding PIT channel is disabled.
1 The corresponding PIT channel is enabled.

******** PIT Multiplex Register (PITMUX)
Module Base + 0x0003

7 6 5 4 3 2 1 0
R

PMUX7 PMUX6 PMUX5 PMUX4 PMUX3 PMUX2 PMUX1 PMUX0
W

Reset 0 0 0 0 0 0 0 0

Figure 17-6. PIT Multiplex Register (PITMUX)

Read: Anytime

Write: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 665



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

Table 17-5. PITMUX Field Descriptions

Field Description

7:0 PIT Multiplex Bits for Timer Channel 7:0 — These bits select if the corresponding 16-bit timer is connected to
PMUX[7:0] micro time base 1 or 0. If PMUX is modified, the corresponding 16-bit timer is immediately switched to the other

micro time base.
0 The corresponding 16-bit timer counts with micro time base 0.
1 The corresponding 16-bit timer counts with micro time base 1.

******** PIT Interrupt Enable Register (PITINTE)
Module Base + 0x0004

7 6 5 4 3 2 1 0
R

PINTE7 PINTE6 PINTE5 PINTE4 PINTE3 PINTE2 PINTE1 PINTE0
W

Reset 0 0 0 0 0 0 0 0

Figure 17-7. PIT Interrupt Enable Register (PITINTE)

Read: Anytime

Write: Anytime

Table 17-6. PITINTE Field Descriptions

Field Description

7:0 PIT Time-out Interrupt Enable Bits for Timer Channel 7:0 — These bits enable an interrupt service request
PINTE[7:0] whenever the time-out flag PTF of the corresponding PIT channel is set. When an interrupt is pending (PTF set)

enabling the interrupt will immediately cause an interrupt. To avoid this, the corresponding PTF flag has to be
cleared first.
0 Interrupt of the corresponding PIT channel is disabled.
1 Interrupt of the corresponding PIT channel is enabled.

******** PIT Time-Out Flag Register (PITTF)
Module Base + 0x0005

7 6 5 4 3 2 1 0
R

PTF7 PTF6 PTF5 PTF4 PTF3 PTF2 PTF1 PTF0
W

Reset 0 0 0 0 0 0 0 0

Figure 17-8. PIT Time-Out Flag Register (PITTF)

Read: Anytime

Write: Anytime (write to clear)

MC9S12XE-Family Reference Manual  Rev. 1.25

666 Freescale Semiconductor



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

Table 17-7. PITTF Field Descriptions

Field Description

7:0 PIT Time-out Flag Bits for Timer Channel 7:0 — PTF is set when the corresponding 16-bit timer modulus
PTF[7:0] down-counter and the selected 8-bit micro timer modulus down-counter have counted to zero. The flag can be

cleared by writing a one to the flag bit. Writing a zero has no effect. If flag clearing by writing a one and flag setting
happen in the same bus clock cycle, the flag remains set. The flag bits are cleared if the PIT module is disabled
or if the corresponding timer channel is disabled.
0 Time-out of the corresponding PIT channel has not yet occurred.
1 Time-out of the corresponding PIT channel has occurred.

******** PIT Micro Timer Load Register 0 to 1 (PITMTLD0–1)
Module Base + 0x0006

7 6 5 4 3 2 1 0
R

PMTLD7 PMTLD6 PMTLD5 PMTLD4 PMTLD3 PMTLD2 PMTLD1 PMTLD0
W

Reset 0 0 0 0 0 0 0 0

Figure 17-9. PIT Micro Timer Load Register 0 (PITMTLD0)

Module Base + 0x0007

7 6 5 4 3 2 1 0
R

PMTLD7 PMTLD6 PMTLD5 PMTLD4 PMTLD3 PMTLD2 PMTLD1 PMTLD0
W

Reset 0 0 0 0 0 0 0 0

Figure 17-10. PIT Micro Timer Load Register 1 (PITMTLD1)

Read: Anytime

Write: Anytime

Table 17-8. PITMTLD0–1 Field Descriptions

Field Description

7:0 PIT Micro Timer Load Bits 7:0 — These bits set the 8-bit modulus down-counter load value of the micro timers.
PMTLD[7:0] Writing a new value into the PITMTLD register will not restart the timer. When the micro timer has counted down

to zero, the PMTLD register value will be loaded. The PFLMT bits in the PITCFLMT register can be used to
immediately update the count register with the new value if an immediate load is desired.

******** PIT Load Register 0 to 7 (PITLD0–7)

Module Base + 0x0008, 0x0009

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8 PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-11. PIT Load Register 0 (PITLD0)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 667



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

Module Base + 0x000C, 0x000D

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8 PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-12. PIT Load Register 1 (PITLD1)

Module Base + 0x0010, 0x0011

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8 PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-13. PIT Load Register 2 (PITLD2)

Module Base + 0x0014, 0x0015

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8 PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-14. PIT Load Register 3 (PITLD3)

Module Base + 0x0018, 0x0019

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8 PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-15. PIT Load Register 4 (PITLD4)

Module Base + 0x001C, 0x001D

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8 PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-16. PIT Load Register 5 (PITLD5)

Module Base + 0x0020, 0x0021

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8 PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-17. PIT Load Register 6 (PITLD6)

MC9S12XE-Family Reference Manual  Rev. 1.25

668 Freescale Semiconductor



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

Module Base + 0x0024, 0x0025

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8 PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-18. PIT Load Register 7 (PITLD7)

Read: Anytime

Write: Anytime

Table 17-9. PITLD0–7 Field Descriptions

Field Description

15:0 PIT Load Bits 15:0 — These bits set the 16-bit modulus down-counter load value. Writing a new value into the
PLD[15:0] PITLD register must be a 16-bit access, to ensure data consistency. It will not restart the timer. When the timer

has counted down to zero the PTF time-out flag will be set and the register value will be loaded. The PFLT bits
in the PITFLT register can be used to immediately update the count register with the new value if an immediate
load is desired.

******** PIT Count Register 0 to 7 (PITCNT0–7)

Module Base + 0x000A, 0x000B

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R PCNT PCNT PCNT PCNT PCNT PCNT PCN PCN PCN PCN PCN PCN PCN PCN PCN PCN
W 15 14 13 12 11 10 T9 T8 T7 T6 T5 T4 T3 T2 T1 T0

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-19. PIT Count Register 0 (PITCNT0)

Module Base + 0x000E, 0x000F

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R PCNT PCNT PCNT PCNT PCNT PCNT PCN PCN PCN PCN PCN PCN PCN PCN PCN PCN
W 15 14 13 12 11 10 T9 T8 T7 T6 T5 T4 T3 T2 T1 T0

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-20. PIT Count Register 1 (PITCNT1)

Module Base + 0x0012, 0x0013

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R PCNT PCNT PCNT PCNT PCNT PCNT PCN PCN PCN PCN PCN PCN PCN PCN PCN PCN
W 15 14 13 12 11 10 T9 T8 T7 T6 T5 T4 T3 T2 T1 T0

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-21. PIT Count Register 2 (PITCNT2)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 669



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

Module Base + 0x0016, 0x0017

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R PCNT PCNT PCNT PCNT PCNT PCNT PCN PCN PCN PCN PCN PCN PCN PCN PCN PCN
W 15 14 13 12 11 10 T9 T8 T7 T6 T5 T4 T3 T2 T1 T0

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-22. PIT Count Register 3 (PITCNT3)

Module Base + 0x001A, 0x001B

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R PCNT PCNT PCNT PCNT PCNT PCNT PCN PCN PCN PCN PCN PCN PCN PCN PCN PCN
W 15 14 13 12 11 10 T9 T8 T7 T6 T5 T4 T3 T2 T1 T0

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-23. PIT Count Register 4 (PITCNT4)

Module Base + 0x001E, 0x001F

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R PCNT PCNT PCNT PCNT PCNT PCNT PCN PCN PCN PCN PCN PCN PCN PCN PCN PCN
W 15 14 13 12 11 10 T9 T8 T7 T6 T5 T4 T3 T2 T1 T0

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-24. PIT Count Register 5 (PITCNT5)

Module Base + 0x0022, 0x0023

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R PCNT PCNT PCNT PCNT PCNT PCNT PCN PCN PCN PCN PCN PCN PCN PCN PCN PCN
W 15 14 13 12 11 10 T9 T8 T7 T6 T5 T4 T3 T2 T1 T0

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-25. PIT Count Register 6 (PITCNT6)

Module Base + 0x0026, 0x0027

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R PCNT PCNT PCNT PCNT PCNT PCNT PCN PCN PCN PCN PCN PCN PCN PCN PCN PCN
W 15 14 13 12 11 10 T9 T8 T7 T6 T5 T4 T3 T2 T1 T0

Reset 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0

Figure 17-26. PIT Count Register 7 (PITCNT7)

Read: Anytime

Write: Has no meaning or effect

Table 17-10. PITCNT0–7 Field Descriptions

Field Description

15:0 PIT Count Bits 15-0 — These bits represent the current 16-bit modulus down-counter value. The read access
PCNT[15:0] for the count register must take place in one clock cycle as a 16-bit access.

MC9S12XE-Family Reference Manual  Rev. 1.25

670 Freescale Semiconductor



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

17.4 Functional Description
Figure 17-27 shows a detailed block diagram of the PIT module. The main parts of the PIT are status,
control and data registers, two 8-bit down-counters, eight 16-bit down-counters and an interrupt/trigger
interface.

8 PFLT0 PIT24B8C
PITFLT Register Timer 0

PMUX0
8 PITLD0 Register

PITMUX Register PITCNT0 Register time-out 0

PFLT1

Bus PITMLD0 Register [1] Timer 1
PITLD1 Register

8-Bit Micro Timer 0
Clock [0] time-out 1

PITCNT1 Register

PFLT2
[2] Timer 2

PITMLD1 Register PITLD2 Register
time-out 2

8-Bit Micro Timer 1 PITCNT2 Register
[1]

PITCFLMT Register PFLT3
Timer 3

PFLMT [3] Interrupt /
PITLD3 Register time- 8

out 3 Trigger Interface
PITCNT3 Register Hardware

Trigger
PFLT4

Timer 4 PITTF Register
[4]

PITLD4 Register time- 8
out 4

PITCNT4 Register PITINTE Register Interrupt
Request

PFLT5
Timer 5

[5]
PITLD5 Register
PITCNT5 Register time-out 5

PFLT6
Timer 6

[6]
PITLD6 Register

time-out 6
PITCNT6 Register

PFLT7
Timer 7

[7]
PITLD7 Register

PMUX7 time-out 7
PITCNT7 Register

Figure 17-27. PIT24B8C Detailed Block Diagram

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 671

PMUX



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

17.4.1 Timer
As shown in Figure 17-1 and Figure 17-27, the 24-bit timers are built in a two-stage architecture with eight
16-bit modulus down-counters and two 8-bit modulus down-counters. The 16-bit timers are clocked with
two selectable micro time bases which are generated with 8-bit modulus down-counters. Each 16-bit timer
is connected to micro time base 0 or 1 via the PMUX[7:0] bit setting in the PIT Multiplex (PITMUX)
register.

A timer channel is enabled if the module enable bit PITE in the PIT control and force load micro timer
(PITCFLMT) register is set and if the corresponding PCE bit in the PIT channel enable (PITCE) register
is set. Two 8-bit modulus down-counters are used to generate two micro time bases. As soon as a micro
time base is selected for an enabled timer channel, the corresponding micro timer modulus down-counter
will load its start value as specified in the PITMTLD0 or PITMTLD1 register and will start down-counting.
Whenever the micro timer down-counter has counted to zero the PITMTLD register is reloaded and the
connected 16-bit modulus down-counters count one cycle.

Whenever a 16-bit timer counter and the connected 8-bit micro timer counter have counted to zero, the
PITLD register is reloaded and the corresponding time-out flag PTF in the PIT time-out flag (PITTF)
register is set, as shown in Figure 17-28. The time-out period is a function of the timer load (PITLD) and
micro timer load (PITMTLD) registers and the bus clock fBUS:

time-out period = (PITMTLD + 1) * (PITLD + 1) / fBUS.

For example, for a 40 MHz bus clock, the maximum time-out period equals:
256 * 65536 * 25 ns = 419.43 ms.

The current 16-bit modulus down-counter value can be read via the PITCNT register. The micro timer
down-counter values cannot be read.

The 8-bit micro timers can individually be restarted by writing a one to the corresponding force load micro
timer PFLMT bits in the PIT control and force load micro timer (PITCFLMT) register. The 16-bit timers
can individually be restarted by writing a one to the corresponding force load timer PFLT bits in the PIT
forceload timer (PITFLT) register. If desired, any group of timers and micro timers can be restarted at the
same time by using one 16-bit write to the adjacent PITCFLMT and PITFLT registers with the relevant
bits set, as shown in Figure 17-28.

MC9S12XE-Family Reference Manual  Rev. 1.25

672 Freescale Semiconductor



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

Bus Clock

8-Bit Micro
0 2 1 0 2 1 0 2 1 0 2 1 2 1 0 2 1 0 2 1 0 2

Timer Counter

PITCNT Register 00 0001 0000 0001 0000 0001 0000 0001

8-Bit Force Load

16-Bit Force Load

PTF Flag1

PITTRIG

Time-Out Period Time-Out Period
After Restart

Note 1. The PTF flag clearing depends on the software

Figure 17-28. PIT Trigger and Flag Signal Timing

17.4.2 Interrupt Interface
Each time-out event can be used to trigger an interrupt service request. For each timer channel, an
individual bit PINTE in the PIT interrupt enable (PITINTE) register exists to enable this feature. If PINTE
is set, an interrupt service is requested whenever the corresponding time-out flag PTF in the PIT time-out
flag (PITTF) register is set. The flag can be cleared by writing a one to the flag bit.

NOTE
Be careful when resetting the PITE, PINTE or PITCE bits in case of pending
PIT interrupt requests, to avoid spurious interrupt requests.

17.4.3 Hardware Trigger
The PIT module contains eight hardware trigger signal lines PITTRIG[7:0], one for each timer channel.
These signals can be connected on SoC level to peripheral modules enabling e.g. periodic ATD conversion
(please refer to the device overview for the mapping of PITTRIG[7:0] signals to peripheral modules).

Whenever a timer channel time-out is reached, the corresponding PTF flag is set and the corresponding
trigger signal PITTRIG triggers a rising edge. The trigger feature requires a minimum time-out period of
two bus clock cycles because the trigger is asserted high for at least one bus clock cycle. For load register
values PITLD = 0x0001 and PITMTLD = 0x0002 the flag setting, trigger timing and a restart with force
load is shown in Figure 17-28.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 673



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

17.5 Initialization

17.5.1 Startup
Set the configuration registers before the PITE bit in the PITCFLMT register is set. Before PITE is set, the
configuration registers can be written in arbitrary order.

17.5.2 Shutdown
When the PITCE register bits, the PITINTE register bits or the PITE bit in the PITCFLMT register are
cleared, the corresponding PIT interrupt flags are cleared. In case of a pending PIT interrupt request, a
spurious interrupt can be generated. Two strategies, which avoid spurious interrupts, are recommended:

1. Reset the PIT interrupt flags only in an ISR. When entering the ISR, the I mask bit in the CCR is
set automatically. The I mask bit must not be cleared before the PIT interrupt flags are cleared.

2. After setting the I mask bit with the SEI instruction, the PIT interrupt flags can be cleared. Then
clear the I mask bit with the CLI instruction to re-enable interrupts.

17.5.3 Flag Clearing
A flag is cleared by writing a one to the flag bit. Always use store or move instructions to write a one in
certain bit positions. Do not use the BSET instructions. Do not use any C-constructs that compile to BSET
instructions. “BSET flag_register, #mask” must not be used for flag clearing because BSET is a read-
modify-write instruction which writes back the “bit-wise or” of the flag_register and the mask into the
flag_register. BSET would clear all flag bits that were set, independent from the mask.

For example, to clear flag bit 0 use: MOVB #$01,PITTF.

17.6 Application Information
To get started quickly with the PIT24B8C module this section provides a small code example how to use
the block. Please note that the example provided is only one specific case out of the possible configurations
and implementations.

Functionality: Generate an PIT interrupt on channel 0 every 500 PIT clock cycles.

ORG CODESTART ; place the program into specific
; range (to be selected)

LDS RAMEND ; load stack pointer to top of RAM
MOVW #CH0_ISR,VEC_PIT_CH0 ; Change value of channel 0 ISR adr

; ******************** Start PIT Initialization *******************************************************

CLR PITCFLMT ; disable PIT
MOVB #$01,PITCE ; enable timer channel 0
CLR PITMUX ; ch0 connected to micro timer 0
MOVB #$63,PITMTLD0 ; micro time base 0 equals 100 clock cycles
MOVW #$0004,PITLD0 ; time base 0 eq. 5 micro time bases 0 =5*100 = 500

MC9S12XE-Family Reference Manual  Rev. 1.25

674 Freescale Semiconductor



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

MOVB #$01,PITINTE ; enable interupt channel 0
MOVB #$80,PITCFLMT ; enable PIT
CLI ; clear Interupt disable Mask bit

;******************** Main Program *************************************************************

MAIN: BRA * ; loop until interrupt

;******************** Channel 0 Interupt Routine ***************************************************

CH0_ISR: LDAA PITTF ; 8 bit read of PIT time out flags
MOVB #$01,PITTF ; clear PIT channel 0 time out flag
RTI ; return to MAIN

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 675



Chapter 17 Periodic Interrupt Timer (S12PIT24B8CV2)

MC9S12XE-Family Reference Manual  Rev. 1.25

676 Freescale Semiconductor