using System.Collections.Generic;
using System.Threading.Tasks;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for dialog service
    /// </summary>
    public interface IDialogService
    {
        /// <summary>
        /// Shows a message dialog
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the dialog</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task ShowMessageAsync(string message, string title);

        /// <summary>
        /// Shows a confirmation dialog
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the dialog</param>
        /// <returns>True if the user confirmed, false otherwise</returns>
        Task<bool> ShowConfirmationAsync(string message, string title);

        /// <summary>
        /// Shows a confirmation dialog (synchronous version)
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the dialog</param>
        /// <returns>True if the user confirmed, false otherwise</returns>
        bool ShowConfirmation(string message, string title);

        /// <summary>
        /// Shows an error dialog
        /// </summary>
        /// <param name="message">The error message to display</param>
        /// <param name="title">The title of the dialog</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task ShowErrorAsync(string message, string title);

        /// <summary>
        /// Shows an error dialog (synchronous version)
        /// </summary>
        /// <param name="message">The error message to display</param>
        /// <param name="title">The title of the dialog</param>
        void ShowError(string message, string title);

        /// <summary>
        /// Shows an information dialog
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the dialog</param>
        void ShowInformation(string message, string title);

        /// <summary>
        /// Shows a dialog to select a file to open
        /// </summary>
        /// <param name="filter">The file filter</param>
        /// <param name="title">The title of the dialog</param>
        /// <returns>The selected file path, or null if the user cancelled</returns>
        Task<string?> ShowOpenFileDialogAsync(string filter, string title);

        /// <summary>
        /// Shows a dialog to select multiple files to open
        /// </summary>
        /// <param name="filter">The file filter</param>
        /// <param name="title">The title of the dialog</param>
        /// <returns>The selected file paths, or empty list if the user cancelled</returns>
        Task<List<string>> ShowOpenFilesDialogAsync(string filter, string title);

        /// <summary>
        /// Shows a dialog to select a file to save
        /// </summary>
        /// <param name="filter">The file filter</param>
        /// <param name="title">The title of the dialog</param>
        /// <param name="defaultFileName">The default file name</param>
        /// <returns>The selected file path, or null if the user cancelled</returns>
        Task<string?> ShowSaveFileDialogAsync(string filter, string title, string defaultFileName = "");

        /// <summary>
        /// Shows a dialog to select a folder
        /// </summary>
        /// <param name="title">The title of the dialog</param>
        /// <returns>The selected folder path, or null if the user cancelled</returns>
        Task<string?> ShowFolderBrowserDialogAsync(string title);

        /// <summary>
        /// Shows an input dialog
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the dialog</param>
        /// <param name="defaultValue">The default value</param>
        /// <returns>The input value, or null if the user cancelled</returns>
        Task<string?> ShowInputDialogAsync(string message, string title, string defaultValue = "");

        /// <summary>
        /// Shows an input dialog (synchronous version)
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the dialog</param>
        /// <param name="defaultValue">The default value</param>
        /// <returns>The input value, or null if the user cancelled</returns>
        string? ShowInputDialog(string message, string title, string defaultValue = "");
    }
}
