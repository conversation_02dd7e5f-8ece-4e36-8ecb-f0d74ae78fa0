using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// A null implementation of ILoggingService that does nothing
    /// </summary>
    public class NullLoggingService : ILoggingService
    {
        /// <summary>
        /// Event that is never triggered
        /// </summary>
        public event EventHandler<LogEntry> LogEntryAdded;

        /// <summary>
        /// Gets an empty log file path
        /// </summary>
        public string LogFilePath => string.Empty;

        /// <summary>
        /// Gets whether detailed logging is enabled (always false)
        /// </summary>
        public bool DetailedLoggingEnabled => false;

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="logFilePath">Path to the log file</param>
        /// <param name="enableDetailedLogging">Whether to enable detailed logging</param>
        /// <returns>Always returns true</returns>
        public Task<bool> InitializeAsync(string logFilePath, bool enableDetailedLogging)
        {
            return Task.FromResult(true);
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        public void LogInformation(string message, string source = "")
        {
            // Do nothing
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        public void LogWarning(string message, string source = "")
        {
            // Do nothing
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        /// <param name="exception">The exception associated with the error</param>
        public void LogError(string message, string source = "", Exception? exception = null)
        {
            // Do nothing
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        public void LogDebug(string message, string source = "")
        {
            // Do nothing
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <returns>Always returns an empty list</returns>
        public Task<List<LogEntry>> GetAllLogEntriesAsync()
        {
            return Task.FromResult(new List<LogEntry>());
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="level">The log level to filter by</param>
        /// <returns>Always returns an empty list</returns>
        public Task<List<LogEntry>> GetLogEntriesByLevelAsync(LogLevel level)
        {
            return Task.FromResult(new List<LogEntry>());
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="source">The source to filter by</param>
        /// <returns>Always returns an empty list</returns>
        public Task<List<LogEntry>> GetLogEntriesBySourceAsync(string source)
        {
            return Task.FromResult(new List<LogEntry>());
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="startDate">The start date</param>
        /// <param name="endDate">The end date</param>
        /// <returns>Always returns an empty list</returns>
        public Task<List<LogEntry>> GetLogEntriesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return Task.FromResult(new List<LogEntry>());
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <returns>Always returns true</returns>
        public Task<bool> ClearLogEntriesAsync()
        {
            return Task.FromResult(true);
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="filePath">The path to export to</param>
        /// <returns>Always returns true</returns>
        public Task<bool> ExportLogEntriesAsync(string filePath)
        {
            return Task.FromResult(true);
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="message">The message to log</param>
        public void LogDebug(string message)
        {
            // Do nothing
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="message">The message to log</param>
        public void LogError(string message)
        {
            // Do nothing
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="message">The message to log</param>
        public void LogInformation(string message)
        {
            // Do nothing
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="message">The message to log</param>
        public void LogWarning(string message)
        {
            // Do nothing
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="args">The format arguments</param>
        public void LogDebug(string message, params object[] args)
        {
            // Do nothing
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="args">The format arguments</param>
        public void LogError(string message, params object[] args)
        {
            // Do nothing
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="args">The format arguments</param>
        public void LogInformation(string message, params object[] args)
        {
            // Do nothing
        }

        /// <summary>
        /// Does nothing
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="args">The format arguments</param>
        public void LogWarning(string message, params object[] args)
        {
            // Do nothing
        }
    }
}
