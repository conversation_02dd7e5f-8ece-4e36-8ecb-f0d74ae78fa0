using System.Windows;
using System.Windows.Controls;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.UI.ViewModels;

namespace VolvoFlashWR.UI.Views
{
    /// <summary>
    /// Interaction logic for BackupVersionsView.xaml
    /// </summary>
    public partial class BackupVersionsView : UserControl
    {
        public BackupVersionsView()
        {
            InitializeComponent();
        }

        private void VersionTree_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (DataContext is BackupVersionsViewModel viewModel && e.NewValue is BackupVersionNode node)
            {
                viewModel.SelectedVersion = node.Backup;
            }
        }
    }
}
