using System;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Tests.Models
{
    [TestFixture]
    public class FlashHealthStatusTests
    {
        private FlashHealthStatus _status;

        [SetUp]
        public void Setup()
        {
            _status = new FlashHealthStatus();
        }

        [Test]
        public void Constructor_InitializesDefaultValues()
        {
            // Assert
            Assert.That(_status.TotalProgramEraseCycles, Is.EqualTo(0));
            Assert.That(_status.MaxProgramEraseCycles, Is.EqualTo(100000));
            Assert.That(_status.SingleBitErrorCount, Is.EqualTo(0));
            Assert.That(_status.MultiBitErrorCount, Is.EqualTo(0));
            Assert.That(_status.BadBlocks, Is.Empty);
            Assert.That(_status.SectorProgramEraseCycles, Is.Empty);
            Assert.That(_status.SectorErrorCounts, Is.Empty);
            Assert.That(_status.LastHealthCheckTimestamp, Is.LessThanOrEqualTo(DateTime.Now));
        }

        [Test]
        public void WearLevelPercentage_CalculatesCorrectly()
        {
            // Arrange
            _status.TotalProgramEraseCycles = 50000;
            _status.MaxProgramEraseCycles = 100000;

            // Act
            double wearLevel = _status.WearLevelPercentage;

            // Assert
            Assert.That(wearLevel, Is.EqualTo(50.0));
        }

        [Test]
        public void WearLevelPercentage_MaximumIs100Percent()
        {
            // Arrange
            _status.TotalProgramEraseCycles = 200000;
            _status.MaxProgramEraseCycles = 100000;

            // Act
            double wearLevel = _status.WearLevelPercentage;

            // Assert
            Assert.That(wearLevel, Is.EqualTo(100.0));
        }

        [Test]
        public void HealthStatus_Good_WhenNoIssues()
        {
            // Arrange
            _status.TotalProgramEraseCycles = 10000;
            _status.SingleBitErrorCount = 10;

            // Act
            FlashHealthStatusLevel level = _status.HealthStatusLevel;

            // Assert
            Assert.That(level, Is.EqualTo(FlashHealthStatusLevel.Good));
        }

        [Test]
        public void HealthStatus_Fair_WhenMinorIssues()
        {
            // Arrange
            _status.TotalProgramEraseCycles = 75000;
            _status.SingleBitErrorCount = 60;
            _status.BadBlocks.Add(0x1000);

            // Act
            FlashHealthStatusLevel level = _status.HealthStatusLevel;

            // Assert
            Assert.That(level, Is.EqualTo(FlashHealthStatusLevel.Fair));
        }

        [Test]
        public void HealthStatus_Warning_WhenSignificantIssues()
        {
            // Arrange
            _status.TotalProgramEraseCycles = 95000;
            _status.SingleBitErrorCount = 150;
            _status.BadBlocks.Add(0x1000);
            _status.BadBlocks.Add(0x2000);
            _status.BadBlocks.Add(0x3000);
            _status.BadBlocks.Add(0x4000);
            _status.BadBlocks.Add(0x5000);
            _status.BadBlocks.Add(0x6000);

            // Act
            FlashHealthStatusLevel level = _status.HealthStatusLevel;

            // Assert
            Assert.That(level, Is.EqualTo(FlashHealthStatusLevel.Warning));
        }

        [Test]
        public void HealthStatus_Critical_WhenSeriousIssues()
        {
            // Arrange
            _status.MultiBitErrorCount = 1;

            // Act
            FlashHealthStatusLevel level = _status.HealthStatusLevel;

            // Assert
            Assert.That(level, Is.EqualTo(FlashHealthStatusLevel.Critical));
        }

        [Test]
        public void AddProgramEraseCycle_IncrementsTotalAndSectorCount()
        {
            // Arrange
            uint sectorAddress = 0x1000;

            // Act
            _status.AddProgramEraseCycle(sectorAddress);
            _status.AddProgramEraseCycle(sectorAddress);

            // Assert
            Assert.That(_status.TotalProgramEraseCycles, Is.EqualTo(2));
            Assert.That(_status.SectorProgramEraseCycles[sectorAddress], Is.EqualTo(2));
        }

        [Test]
        public void AddError_IncrementsErrorCountsCorrectly()
        {
            // Arrange
            uint sectorAddress = 0x1000;

            // Act
            _status.AddError(sectorAddress, true); // Single-bit error
            _status.AddError(sectorAddress, false); // Multi-bit error

            // Assert
            Assert.That(_status.SingleBitErrorCount, Is.EqualTo(1));
            Assert.That(_status.MultiBitErrorCount, Is.EqualTo(1));
            Assert.That(_status.SectorErrorCounts[sectorAddress], Is.EqualTo(2));
        }

        [Test]
        public void AddBadBlock_AddsUniqueBlocksOnly()
        {
            // Arrange
            uint blockAddress = 0x1000;

            // Act
            _status.AddBadBlock(blockAddress);
            _status.AddBadBlock(blockAddress); // Add the same block again

            // Assert
            Assert.That(_status.BadBlocks.Count, Is.EqualTo(1));
            Assert.That(_status.BadBlocks[0], Is.EqualTo(blockAddress));
        }

        [Test]
        public void UpdateHealthCheckTimestamp_UpdatesTimestamp()
        {
            // Arrange
            DateTime originalTimestamp = _status.LastHealthCheckTimestamp;
            System.Threading.Thread.Sleep(10); // Ensure time difference

            // Act
            _status.UpdateHealthCheckTimestamp();

            // Assert
            Assert.That(_status.LastHealthCheckTimestamp, Is.GreaterThan(originalTimestamp));
        }

        [Test]
        public void HealthStatusDescription_ReturnsAppropriateMessage()
        {
            // Test for each health status level

            // Good
            _status.TotalProgramEraseCycles = 10000;
            Assert.That(_status.HealthStatusDescription, Does.Contain("good health"));

            // Fair
            _status.TotalProgramEraseCycles = 75000;
            _status.BadBlocks.Add(0x1000);
            Assert.That(_status.HealthStatusDescription, Does.Contain("fair health"));

            // Warning
            _status.TotalProgramEraseCycles = 95000;
            _status.SingleBitErrorCount = 150;
            _status.BadBlocks.Add(0x2000);
            _status.BadBlocks.Add(0x3000);
            _status.BadBlocks.Add(0x4000);
            _status.BadBlocks.Add(0x5000);
            _status.BadBlocks.Add(0x6000);
            Assert.That(_status.HealthStatusDescription, Does.Contain("showing signs of wear"));

            // Critical
            _status.MultiBitErrorCount = 1;
            Assert.That(_status.HealthStatusDescription, Does.Contain("critical condition"));
        }
    }
}

