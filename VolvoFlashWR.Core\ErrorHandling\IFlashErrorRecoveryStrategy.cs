using System.Threading.Tasks;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.ErrorHandling
{
    /// <summary>
    /// Interface for flash error recovery strategies
    /// </summary>
    public interface IFlashErrorRecoveryStrategy
    {
        /// <summary>
        /// Attempts to recover from an error in a flash operation
        /// </summary>
        /// <param name="operation">The flash operation</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="errorData">Additional error data</param>
        /// <returns>A recovery result indicating the outcome of the recovery attempt</returns>
        Task<FlashErrorRecoveryResult> RecoverAsync(FlashOperation operation, string errorMessage, object? errorData = null);
    }
}
