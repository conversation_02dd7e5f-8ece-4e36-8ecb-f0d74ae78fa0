# VolvoFlashWR Application

## 🚀 Real Hardware Support Now Available!

The VolvoFlashWR application now includes **complete real hardware support** with 136 libraries for Vocom 1 adapter communication!

## Quick Start for Real Hardware

### ✅ Prerequisites Complete
- **136 DLL libraries** downloaded and configured
- **Vocom driver integration** ready
- **All communication protocols** implemented

### 🔧 Running with Real Hardware
1. **Verify Setup**: Run `Verify_Libraries.bat`
2. **Connect Vocom**: Connect your Vocom 1 adapter via USB
3. **Start Application**: Run `Run_Real_Hardware_Mode.bat`

### 📋 Alternative Run Methods

#### Option 1: Real Hardware Mode (Recommended)
```batch
Run_Real_Hardware_Mode.bat
```

#### Option 2: Normal Mode (Auto-detection)
```batch
run_normal_mode.bat
```

#### Option 3: Using Launcher
```bash
VolvoFlashWR.Launcher.exe --mode=normal --hardware=real
```

#### Option 4: Using dotnet CLI
```bash
cd VolvoFlashWR.Launcher
dotnet run --framework net8.0-windows
```

> **Important Note**: This application requires the Single-Threaded Apartment (STA) model for the UI thread because it uses WPF components. The `--framework net8.0-windows` parameter is required to ensure the application runs correctly.

## 📚 Complete Library Integration

The application now includes **136 libraries** for comprehensive Vocom hardware support:

### 🔧 Core Vocom Libraries
- **WUDFPuma.dll** - Main Vocom driver
- **apci.dll** - APCI communication core
- **Volvo.ApciPlus.dll** - Enhanced APCI communication

### 🏭 Phoenix Diag Integration
- **PhoenixESW.dll** - Phoenix ESW library
- **PhoenixGeneral.dll** - Phoenix utilities
- Complete Phoenix Diag Flash Editor Plus 2021 integration

### 🚗 Volvo-Specific Libraries
- **Volvo.NAMS.AC.Services** - NAMS AC services
- **Volvo.NVS.Core** - NVS core functionality
- **VolvoIt.Waf.ServiceContract** - WAF service contracts
- **Vodia.CommonDomain.Model** - Common domain models

### 📁 Library Organization
```
Libraries/          (136 DLL files)
├── Core Vocom drivers
├── APCI communication
├── Phoenix Diag libraries
├── Volvo-specific libraries
├── Vodia components
└── Supporting libraries
```

## Vocom Driver Integration

The application integrates with the Vocom hardware adapter using the official Vocom driver. To use the application with real hardware:

1. Install the Vocom1 driver (CommunicationUnitInstaller-*******.msi)
2. Connect your Vocom adapter via USB, Bluetooth, or WiFi
3. Launch the application using `Run_Real_Hardware_Mode.bat`

The application automatically locates Vocom driver files from multiple sources:
- `C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll`
- `C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\`
- Local `Libraries\` and `Drivers\Vocom\` folders

### Custom Driver Path Configuration

If the application cannot find the Vocom driver automatically, you can specify a custom path in the configuration file:

1. Open `VolvoFlashWR.UI\Config\vocom_config.json`
2. Set the `Vocom.DriverDllPath` value to the full path of your WUDFPuma.dll file
3. Save the file and restart the application

Example configuration:
```json
{
  "Vocom": {
    "DriverDllPath": "C:\\Path\\To\\Your\\WUDFPuma.dll",
    "AutoConnect": true
  }
}
```

## Troubleshooting

### STA Thread Error

If you encounter the following error:

```
Error initializing application: The calling must be STA, because many UI componenets require this.
```

This is because WPF applications require the Single-Threaded Apartment (STA) threading model. The application has been configured to use STA threading by:

1. Setting the `[STAThread]` attribute on the `Main` method in `Program.cs`
2. Changing the output type to `WinExe` in the project file
3. Adding a `launchSettings.json` file with `"useSTAThread": true`

If you still encounter this error, try running the application using the provided batch file `run_volvo_flash_wr.bat` or by building and running the executable directly:

```bash
dotnet build VolvoFlashWR.Launcher
cd VolvoFlashWR.Launcher\bin\Debug\net8.0-windows
VolvoFlashWR.Launcher.exe
```

### Vocom Driver Not Found

If you encounter an error about the Vocom driver not being found:

1. Make sure you have installed the Vocom1 driver (CommunicationUnitInstaller-*******.msi)
2. Check if the WUDFPuma.dll file exists in the expected locations
3. Configure a custom path as described in the "Custom Driver Path Configuration" section
4. Try running the application in "Dummy Mode" for testing without hardware

## Project Structure

- **VolvoFlashWR.Launcher**: Entry point for the application
- **VolvoFlashWR.UI**: WPF user interface components
- **VolvoFlashWR.Core**: Core business logic and models
- **VolvoFlashWR.Communication**: Communication services for ECU interaction

## Development

To build the application:

```bash
dotnet build
```

To run tests:

```bash
dotnet test
```
