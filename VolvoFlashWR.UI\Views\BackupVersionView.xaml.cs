using System.Windows;
using VolvoFlashWR.UI.ViewModels;

namespace VolvoFlashWR.UI.Views
{
    /// <summary>
    /// Interaction logic for BackupVersionView.xaml
    /// </summary>
    public partial class BackupVersionView : Window
    {
        public BackupVersionView(BackupVersionsViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            
            // Subscribe to close request from view model
            if (viewModel != null)
            {
                viewModel.CloseRequested += (s, e) => Close();
            }
        }
    }
}
