using System;
using System.Collections.Generic;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Tests.Models
{
    [TestFixture]
    public class BackupDataTests
    {
        [Test]
        public void BackupData_DefaultValues_AreCorrect()
        {
            // Arrange & Act
            var backupData = new BackupData();
            var now = DateTime.Now;

            // Assert
            Assert.That(backupData.Id, Is.Not.Null.Or.Empty);
            Assert.That(backupData.CreationTime.Date, Is.EqualTo(now.Date));
            Assert.That(backupData.Parameters, Is.Not.Null);
            Assert.That(backupData.Tags, Is.Not.Null);
            Assert.That(backupData.Version, Is.EqualTo(1));
            Assert.That(backupData.LastModifiedTime.Date, Is.EqualTo(now.Date));
            Assert.That(backupData.ChildBackupIds, Is.Not.Null);
            Assert.That(backupData.IsLatestVersion, Is.True);
            Assert.That(backupData.VersionCreationTime.Date, Is.EqualTo(now.Date));
        }

        [Test]
        public void BackupData_CustomValues_AreCorrect()
        {
            // Arrange
            string id = "Backup123";
            DateTime creationTime = new DateTime(2023, 1, 1);
            string ecuId = "ECU123";
            string ecuName = "TestECU";
            string ecuSerialNumber = "SN12345";
            string ecuHardwareVersion = "HW1.0";
            string ecuSoftwareVersion = "SW2.0";
            byte[] eepromData = new byte[] { 1, 2, 3 };
            byte[] microcontrollerCode = new byte[] { 4, 5, 6 };
            var parameters = new Dictionary<string, object> { { "TestParam", "Value" } };
            string description = "Test Backup";
            string createdBy = "TestUser";
            string checksum = "ABC123";
            bool isComplete = true;
            string filePath = "C:\\Backups\\Backup123.bak";
            string category = "Production";
            var tags = new List<string> { "Tag1", "Tag2" };
            int version = 2;
            bool isCompressed = true;
            bool isEncrypted = false;
            DateTime lastModifiedTime = new DateTime(2023, 1, 2);
            string lastModifiedBy = "AnotherUser";
            long sizeInBytes = 1024;
            string parentBackupId = "Backup122";
            string rootBackupId = "Backup100";
            var childBackupIds = new List<string> { "Backup124", "Backup125" };
            string versionNotes = "Updated parameters";
            DateTime versionCreationTime = new DateTime(2023, 1, 1, 12, 0, 0);
            bool isLatestVersion = false;

            // Act
            var backupData = new BackupData
            {
                Id = id,
                CreationTime = creationTime,
                ECUId = ecuId,
                ECUName = ecuName,
                ECUSerialNumber = ecuSerialNumber,
                ECUHardwareVersion = ecuHardwareVersion,
                ECUSoftwareVersion = ecuSoftwareVersion,
                EEPROMData = eepromData,
                MicrocontrollerCode = microcontrollerCode,
                Parameters = parameters,
                Description = description,
                CreatedBy = createdBy,
                Checksum = checksum,
                IsComplete = isComplete,
                FilePath = filePath,
                Category = category,
                Tags = tags,
                Version = version,
                IsCompressed = isCompressed,
                IsEncrypted = isEncrypted,
                LastModifiedTime = lastModifiedTime,
                LastModifiedBy = lastModifiedBy,
                SizeInBytes = sizeInBytes,
                ParentBackupId = parentBackupId,
                RootBackupId = rootBackupId,
                ChildBackupIds = childBackupIds,
                VersionNotes = versionNotes,
                VersionCreationTime = versionCreationTime,
                IsLatestVersion = isLatestVersion
            };

            // Assert
            Assert.That(backupData.Id, Is.EqualTo(id));
            Assert.That(backupData.CreationTime, Is.EqualTo(creationTime));
            Assert.That(backupData.ECUId, Is.EqualTo(ecuId));
            Assert.That(backupData.ECUName, Is.EqualTo(ecuName));
            Assert.That(backupData.ECUSerialNumber, Is.EqualTo(ecuSerialNumber));
            Assert.That(backupData.ECUHardwareVersion, Is.EqualTo(ecuHardwareVersion));
            Assert.That(backupData.ECUSoftwareVersion, Is.EqualTo(ecuSoftwareVersion));
            Assert.That(backupData.EEPROMData, Is.EqualTo(eepromData));
            Assert.That(backupData.MicrocontrollerCode, Is.EqualTo(microcontrollerCode));
            Assert.That(backupData.Parameters, Is.EqualTo(parameters));
            Assert.That(backupData.Description, Is.EqualTo(description));
            Assert.That(backupData.CreatedBy, Is.EqualTo(createdBy));
            Assert.That(backupData.Checksum, Is.EqualTo(checksum));
            Assert.That(backupData.IsComplete, Is.EqualTo(isComplete));
            Assert.That(backupData.FilePath, Is.EqualTo(filePath));
            Assert.That(backupData.Category, Is.EqualTo(category));
            Assert.That(backupData.Tags, Is.EqualTo(tags));
            Assert.That(backupData.Version, Is.EqualTo(version));
            Assert.That(backupData.IsCompressed, Is.EqualTo(isCompressed));
            Assert.That(backupData.IsEncrypted, Is.EqualTo(isEncrypted));
            Assert.That(backupData.LastModifiedTime, Is.EqualTo(lastModifiedTime));
            Assert.That(backupData.LastModifiedBy, Is.EqualTo(lastModifiedBy));
            Assert.That(backupData.SizeInBytes, Is.EqualTo(sizeInBytes));
            Assert.That(backupData.ParentBackupId, Is.EqualTo(parentBackupId));
            Assert.That(backupData.RootBackupId, Is.EqualTo(rootBackupId));
            Assert.That(backupData.ChildBackupIds, Is.EqualTo(childBackupIds));
            Assert.That(backupData.VersionNotes, Is.EqualTo(versionNotes));
            Assert.That(backupData.VersionCreationTime, Is.EqualTo(versionCreationTime));
            Assert.That(backupData.IsLatestVersion, Is.EqualTo(isLatestVersion));
        }
    }
}

