using System;
using System.Collections.Generic;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents a backup schedule configuration
    /// </summary>
    public class BackupSchedule
    {
        /// <summary>
        /// Unique identifier for the schedule
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Name of the schedule
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Description of the schedule
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// ID of the ECU to backup
        /// </summary>
        public string ECUId { get; set; }

        /// <summary>
        /// Name of the ECU to backup (for display purposes)
        /// </summary>
        public string ECUName { get; set; }

        /// <summary>
        /// The selected ECU device (transient property, not serialized)
        /// </summary>
        [System.Text.Json.Serialization.JsonIgnore]
        public ECUDevice SelectedECU { get; set; }

        /// <summary>
        /// Whether the schedule is enabled
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// The frequency type of the schedule
        /// </summary>
        public BackupFrequencyType FrequencyType { get; set; } = BackupFrequencyType.Daily;

        /// <summary>
        /// The user-friendly frequency of the schedule
        /// </summary>
        public BackupFrequency Frequency
        {
            get
            {
                switch (FrequencyType)
                {
                    case BackupFrequencyType.Hourly:
                        return BackupFrequency.Hourly;
                    case BackupFrequencyType.Daily:
                        return BackupFrequency.Daily;
                    case BackupFrequencyType.Weekly:
                        return BackupFrequency.Weekly;
                    case BackupFrequencyType.Monthly:
                        return BackupFrequency.Monthly;
                    default:
                        return BackupFrequency.Custom;
                }
            }
            set
            {
                switch (value)
                {
                    case BackupFrequency.Hourly:
                        FrequencyType = BackupFrequencyType.Hourly;
                        break;
                    case BackupFrequency.Daily:
                        FrequencyType = BackupFrequencyType.Daily;
                        break;
                    case BackupFrequency.Weekly:
                        FrequencyType = BackupFrequencyType.Weekly;
                        break;
                    case BackupFrequency.Monthly:
                        FrequencyType = BackupFrequencyType.Monthly;
                        break;
                    case BackupFrequency.Custom:
                        // Custom frequency type handling
                        break;
                }
            }
        }

        /// <summary>
        /// The interval value for the frequency (e.g., every 2 hours, every 3 days)
        /// </summary>
        public int Interval { get; set; } = 1;

        /// <summary>
        /// The time of day to run the backup (for daily, weekly, monthly schedules)
        /// </summary>
        public TimeSpan TimeOfDay { get; set; } = new TimeSpan(3, 0, 0); // Default to 3:00 AM

        /// <summary>
        /// The hour of the day to start the backup (0-23)
        /// </summary>
        public int StartHour
        {
            get => TimeOfDay.Hours;
            set => TimeOfDay = new TimeSpan(value, TimeOfDay.Minutes, 0);
        }

        /// <summary>
        /// The minute of the hour to start the backup (0-59)
        /// </summary>
        public int StartMinute
        {
            get => TimeOfDay.Minutes;
            set => TimeOfDay = new TimeSpan(TimeOfDay.Hours, value, 0);
        }

        /// <summary>
        /// Days of the week to run the backup (for weekly schedules)
        /// </summary>
        public List<DayOfWeek> DaysOfWeek { get; set; } = new List<DayOfWeek> { DayOfWeek.Monday };

        /// <summary>
        /// The day of the week to start the backup (for weekly schedules)
        /// </summary>
        public DayOfWeek StartDayOfWeek { get; set; } = DayOfWeek.Monday;

        /// <summary>
        /// Day of the month to run the backup (for monthly schedules)
        /// </summary>
        public int DayOfMonth { get; set; } = 1;

        /// <summary>
        /// The start date of the schedule
        /// </summary>
        public DateTime StartDate { get; set; } = DateTime.Today;

        /// <summary>
        /// The end date of the schedule (null for no end date)
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// The last time the backup was executed
        /// </summary>
        public DateTime? LastExecutionTime { get; set; }

        /// <summary>
        /// The next scheduled execution time
        /// </summary>
        public DateTime NextExecutionTime { get; set; }

        /// <summary>
        /// Category to assign to created backups
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// Tags to assign to created backups
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// Whether to include EEPROM data in the backup
        /// </summary>
        public bool IncludeEEPROM { get; set; } = true;

        /// <summary>
        /// Whether to include microcontroller code in the backup
        /// </summary>
        public bool IncludeMicrocontrollerCode { get; set; } = true;

        /// <summary>
        /// Whether to include parameters in the backup
        /// </summary>
        public bool IncludeParameters { get; set; } = true;

        /// <summary>
        /// Maximum number of backups to keep for this schedule (0 for unlimited)
        /// </summary>
        public int MaxBackupsToKeep { get; set; } = 0;

        /// <summary>
        /// Maximum age in days for backups before creating a new full backup (0 for no limit)
        /// </summary>
        public int MaxBackupAge { get; set; } = 30;

        /// <summary>
        /// Number of retry attempts if backup fails
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// Custom interval in days for custom frequency type
        /// </summary>
        public int CustomIntervalDays { get; set; } = 1;

        /// <summary>
        /// Type of schedule (e.g., "Standard", "Incremental", "Full")
        /// </summary>
        public string ScheduleType { get; set; } = "Standard";

        /// <summary>
        /// IDs of backups created by this schedule
        /// </summary>
        public List<string> CreatedBackupIds { get; set; } = new List<string>();

        /// <summary>
        /// Calculates the next execution time based on the schedule settings and the current or last execution time
        /// </summary>
        /// <param name="fromTime">The time to calculate from (usually current time or last execution time)</param>
        /// <returns>The next execution time</returns>
        public DateTime CalculateNextExecutionTime(DateTime fromTime)
        {
            DateTime baseTime = fromTime;

            // If we're calculating from a past execution, start from the next interval
            if (LastExecutionTime.HasValue && fromTime <= LastExecutionTime.Value)
            {
                baseTime = LastExecutionTime.Value;
            }

            DateTime nextTime;

            switch (FrequencyType)
            {
                case BackupFrequencyType.Hourly:
                    // For hourly, add the specified number of hours
                    nextTime = baseTime.AddHours(Interval);
                    break;

                case BackupFrequencyType.Daily:
                    // For daily, set the time of day and add the specified number of days
                    nextTime = baseTime.Date.AddDays(Interval).Add(TimeOfDay);

                    // If the calculated time is in the past, add another interval
                    if (nextTime <= baseTime)
                    {
                        nextTime = nextTime.AddDays(Interval);
                    }
                    break;

                case BackupFrequencyType.Weekly:
                    // For weekly, find the next occurrence of one of the specified days of the week
                    nextTime = baseTime.Date.Add(TimeOfDay);

                    // If the time today is already past, start from tomorrow
                    if (nextTime <= baseTime)
                    {
                        nextTime = nextTime.AddDays(1);
                    }

                    // Find the next day that matches one of the specified days of the week
                    while (!DaysOfWeek.Contains(nextTime.DayOfWeek))
                    {
                        nextTime = nextTime.AddDays(1);
                    }
                    break;

                case BackupFrequencyType.Monthly:
                    // For monthly, set to the specified day of the month
                    int year = baseTime.Year;
                    int month = baseTime.Month;

                    // Move to the next month if we're past the day or time for this month
                    if (baseTime.Day > DayOfMonth || (baseTime.Day == DayOfMonth && baseTime.TimeOfDay >= TimeOfDay))
                    {
                        month++;
                        if (month > 12)
                        {
                            month = 1;
                            year++;
                        }
                    }

                    // Ensure the day is valid for the month
                    int daysInMonth = DateTime.DaysInMonth(year, month);
                    int day = Math.Min(DayOfMonth, daysInMonth);

                    nextTime = new DateTime(year, month, day).Add(TimeOfDay);
                    break;

                default:
                    // Default to daily if unknown frequency type
                    nextTime = baseTime.Date.AddDays(1).Add(TimeOfDay);
                    break;
            }

            // Check if the next time is after the end date
            if (EndDate.HasValue && nextTime > EndDate.Value)
            {
                // If so, disable the schedule
                IsEnabled = false;
                return DateTime.MaxValue;
            }

            return nextTime;
        }

        /// <summary>
        /// Creates a deep copy of this backup schedule
        /// </summary>
        /// <returns>A new backup schedule with the same properties</returns>
        public BackupSchedule Clone()
        {
            var clone = new BackupSchedule
            {
                Id = this.Id,
                Name = this.Name,
                Description = this.Description,
                ECUId = this.ECUId,
                ECUName = this.ECUName,
                SelectedECU = this.SelectedECU,
                IsEnabled = this.IsEnabled,
                FrequencyType = this.FrequencyType,
                Interval = this.Interval,
                TimeOfDay = this.TimeOfDay,
                StartDayOfWeek = this.StartDayOfWeek,
                DayOfMonth = this.DayOfMonth,
                StartDate = this.StartDate,
                EndDate = this.EndDate,
                LastExecutionTime = this.LastExecutionTime,
                NextExecutionTime = this.NextExecutionTime,
                Category = this.Category,
                IncludeEEPROM = this.IncludeEEPROM,
                IncludeMicrocontrollerCode = this.IncludeMicrocontrollerCode,
                IncludeParameters = this.IncludeParameters,
                MaxBackupsToKeep = this.MaxBackupsToKeep,
                MaxBackupAge = this.MaxBackupAge
            };

            // Clone the days of week list
            if (this.DaysOfWeek != null)
            {
                clone.DaysOfWeek = new List<DayOfWeek>(this.DaysOfWeek);
            }

            // Clone the tags list
            if (this.Tags != null)
            {
                clone.Tags = new List<string>(this.Tags);
            }

            // Clone the created backup IDs list
            if (this.CreatedBackupIds != null)
            {
                clone.CreatedBackupIds = new List<string>(this.CreatedBackupIds);
            }

            return clone;
        }
    }

    /// <summary>
    /// Defines the frequency types for backup schedules
    /// </summary>
    public enum BackupFrequencyType
    {
        /// <summary>
        /// Hourly backup schedule
        /// </summary>
        Hourly,

        /// <summary>
        /// Daily backup schedule
        /// </summary>
        Daily,

        /// <summary>
        /// Weekly backup schedule
        /// </summary>
        Weekly,

        /// <summary>
        /// Monthly backup schedule
        /// </summary>
        Monthly,

        /// <summary>
        /// Custom backup schedule
        /// </summary>
        Custom
    }
}
