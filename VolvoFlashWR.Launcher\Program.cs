using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace VolvoFlashWR.Launcher
{
    class Program
    {
        private static readonly string ConfigFilePath = Path.Combine(
            AppDomain.CurrentDomain.BaseDirectory, "Config", "launcher_config.json");

        [STAThread]
        static void Main(string[] args)
        {
            try
            {
                // Run the async method in a synchronous context to maintain STA thread
                MainAsync(args).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error starting application: {ex.Message}");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
        }

        private static async Task MainAsync(string[] args)
        {
            try
            {
                // Load launcher configuration
                var config = await LauncherConfig.LoadAsync(ConfigFilePath);

                // Process command line arguments
                ProcessCommandLineArguments(args, config);

                // Show UI or use default/last mode
                LaunchMode selectedMode;

                if (config.ShowLauncherUI && !args.Contains("--silent"))
                {
                    selectedMode = await ShowLauncherUI(config);
                }
                else
                {
                    // Use last selected mode if remember is enabled, otherwise use default
                    selectedMode = config.RememberLastMode ? config.LastSelectedMode : config.DefaultLaunchMode;
                    Console.WriteLine($"Starting in {selectedMode} Mode (from configuration)...");
                }

                // Apply the selected mode
                ApplyLaunchMode(selectedMode, config);

                // Update last selected mode if remember is enabled
                if (config.RememberLastMode && selectedMode != config.LastSelectedMode)
                {
                    config.LastSelectedMode = selectedMode;
                    await LauncherConfig.SaveAsync(config, ConfigFilePath);
                }

                Console.WriteLine("Starting VolvoFlashWR application...");

                // Create an instance of the UI application and run it
                var app = new VolvoFlashWR.UI.App();
                app.Run();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error starting application: {ex.Message}");
                throw; // Rethrow to be caught by the outer try-catch
            }
        }

        /// <summary>
        /// Shows the launcher UI and gets the selected mode
        /// </summary>
        /// <param name="config">The launcher configuration</param>
        /// <returns>The selected launch mode</returns>
        private static Task<LaunchMode> ShowLauncherUI(LauncherConfig config)
        {
            // Check if we should use a graphical UI
            if (config.UseGraphicalUI)
            {
                // For now, we'll just use the console UI
                // In a future implementation, we could create a WPF-based launcher UI
                Console.WriteLine("Graphical UI not implemented yet. Using console UI.");
            }

            // Show header
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("VolvoFlashWR Launcher");
            Console.WriteLine("====================");
            Console.ResetColor();
            Console.WriteLine();

            // Show version information
            var assembly = Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version;
            Console.WriteLine($"Version: {version}");
            Console.WriteLine();

            // Show launch modes
            Console.WriteLine("Select launch mode:");
            Console.ForegroundColor = ConsoleColor.White;
            Console.WriteLine("1. Normal Mode (Requires hardware)");
            Console.WriteLine("2. Dummy Mode (No hardware required)");
            Console.WriteLine("3. Debug Mode (Verbose logging)");
            Console.WriteLine("4. Advanced Mode (Custom configuration)");
            Console.WriteLine("5. Safe Mode (Minimal configuration with error recovery)");
            Console.WriteLine("6. Demo Mode (Pre-configured for demonstrations)");
            Console.ResetColor();
            Console.WriteLine();

            // Show default or last selected mode
            string defaultChoice = config.RememberLastMode
                ? ((int)config.LastSelectedMode).ToString()
                : ((int)config.DefaultLaunchMode).ToString();

            Console.Write($"Enter your choice (1-6) [{defaultChoice}]: ");

            string? choice = Console.ReadLine();

            // Use default if no choice is made
            if (string.IsNullOrWhiteSpace(choice))
            {
                choice = defaultChoice;
            }

            LaunchMode selectedMode;

            switch (choice)
            {
                case "1":
                    Console.ForegroundColor = ConsoleColor.Green;
                    Console.WriteLine("Starting in Normal Mode...");
                    Console.ResetColor();
                    selectedMode = LaunchMode.Normal;
                    break;

                case "2":
                    Console.ForegroundColor = ConsoleColor.Yellow;
                    Console.WriteLine("Starting in Dummy Mode...");
                    Console.ResetColor();
                    selectedMode = LaunchMode.Dummy;
                    break;

                case "3":
                    Console.ForegroundColor = ConsoleColor.Magenta;
                    Console.WriteLine("Starting in Debug Mode...");
                    Console.ResetColor();
                    selectedMode = LaunchMode.Debug;
                    break;

                case "4":
                    Console.ForegroundColor = ConsoleColor.Blue;
                    Console.WriteLine("Starting in Advanced Mode...");
                    Console.ResetColor();
                    selectedMode = LaunchMode.Advanced;

                    // Show advanced options if enabled
                    if (config.ShowAdvancedOptions)
                    {
                        ShowAdvancedOptions(config);
                    }
                    else
                    {
                        // Prompt for custom configuration file
                        Console.Write("Enter path to custom configuration file (leave empty for default): ");
                        string? customConfigPath = Console.ReadLine();

                        if (!string.IsNullOrWhiteSpace(customConfigPath))
                        {
                            config.CustomConfigPath = customConfigPath;
                        }
                    }
                    break;

                case "5":
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine("Starting in Safe Mode...");
                    Console.ResetColor();
                    selectedMode = LaunchMode.Safe;
                    break;

                case "6":
                    Console.ForegroundColor = ConsoleColor.Cyan;
                    Console.WriteLine("Starting in Demo Mode...");
                    Console.ResetColor();
                    selectedMode = LaunchMode.Demo;
                    break;

                default:
                    Console.ForegroundColor = ConsoleColor.Yellow;
                    Console.WriteLine("Invalid choice. Starting in Dummy Mode...");
                    Console.ResetColor();
                    selectedMode = LaunchMode.Dummy;
                    break;
            }

            return Task.FromResult(selectedMode);
        }

        /// <summary>
        /// Shows advanced options for configuration
        /// </summary>
        /// <param name="config">The launcher configuration</param>
        private static void ShowAdvancedOptions(LauncherConfig config)
        {
            Console.WriteLine();
            Console.WriteLine("Advanced Options:");
            Console.WriteLine("=================");

            // Custom configuration file
            Console.Write("Enter path to custom configuration file (leave empty for default): ");
            string? customConfigPath = Console.ReadLine();
            if (!string.IsNullOrWhiteSpace(customConfigPath))
            {
                config.CustomConfigPath = customConfigPath;
            }

            // Logging options
            Console.WriteLine();
            Console.WriteLine("Logging Options:");
            Console.WriteLine("---------------");

            Console.Write($"Enable verbose logging (y/n) [{(config.Logging.VerboseLogging ? "y" : "n")}]: ");
            string? verboseLogging = Console.ReadLine();
            if (!string.IsNullOrWhiteSpace(verboseLogging))
            {
                config.Logging.VerboseLogging = verboseLogging.ToLower() == "y";
            }

            Console.Write($"Log file path [{config.Logging.LogFilePath}]: ");
            string? logFilePath = Console.ReadLine();
            if (!string.IsNullOrWhiteSpace(logFilePath))
            {
                config.Logging.LogFilePath = logFilePath;
            }

            Console.Write($"Minimum log level (Trace, Debug, Information, Warning, Error, Critical) [{config.Logging.MinimumLogLevel}]: ");
            string? logLevel = Console.ReadLine();
            if (!string.IsNullOrWhiteSpace(logLevel) && Enum.TryParse<LogLevel>(logLevel, true, out LogLevel level))
            {
                config.Logging.MinimumLogLevel = level;
            }

            // UI options
            Console.WriteLine();
            Console.WriteLine("UI Options:");
            Console.WriteLine("-----------");

            Console.Write($"UI theme (Light, Dark) [{config.UI.Theme}]: ");
            string? theme = Console.ReadLine();
            if (!string.IsNullOrWhiteSpace(theme))
            {
                config.UI.Theme = theme;
            }

            Console.Write($"UI language (e.g., en-US, fr-FR) [{config.UI.Language}]: ");
            string? language = Console.ReadLine();
            if (!string.IsNullOrWhiteSpace(language))
            {
                config.UI.Language = language;
            }

            Console.Write($"Show splash screen (y/n) [{(config.UI.ShowSplashScreen ? "y" : "n")}]: ");
            string? showSplashScreen = Console.ReadLine();
            if (!string.IsNullOrWhiteSpace(showSplashScreen))
            {
                config.UI.ShowSplashScreen = showSplashScreen.ToLower() == "y";
            }

            // Communication options
            Console.WriteLine();
            Console.WriteLine("Communication Options:");
            Console.WriteLine("---------------------");

            Console.Write($"Auto-connect to Vocom devices (y/n) [{(config.Communication.AutoConnectVocom ? "y" : "n")}]: ");
            string? autoConnectVocom = Console.ReadLine();
            if (!string.IsNullOrWhiteSpace(autoConnectVocom))
            {
                config.Communication.AutoConnectVocom = autoConnectVocom.ToLower() == "y";
            }

            Console.Write($"Use WiFi fallback (y/n) [{(config.Communication.UseWiFiFallback ? "y" : "n")}]: ");
            string? useWiFiFallback = Console.ReadLine();
            if (!string.IsNullOrWhiteSpace(useWiFiFallback))
            {
                config.Communication.UseWiFiFallback = useWiFiFallback.ToLower() == "y";
            }

            Console.Write($"Default protocol type (CAN, J1939, KWP2000, ISO9141) [{config.Communication.DefaultProtocolType}]: ");
            string? protocolType = Console.ReadLine();
            if (!string.IsNullOrWhiteSpace(protocolType) && Enum.TryParse<ProtocolType>(protocolType, true, out ProtocolType protocol))
            {
                config.Communication.DefaultProtocolType = protocol;
            }

            // Backup options
            Console.WriteLine();
            Console.WriteLine("Backup Options:");
            Console.WriteLine("---------------");

            Console.Write($"Use compression (y/n) [{(config.Backup.UseCompression ? "y" : "n")}]: ");
            string? useCompression = Console.ReadLine();
            if (!string.IsNullOrWhiteSpace(useCompression))
            {
                config.Backup.UseCompression = useCompression.ToLower() == "y";
            }

            Console.Write($"Use encryption (y/n) [{(config.Backup.UseEncryption ? "y" : "n")}]: ");
            string? useEncryption = Console.ReadLine();
            if (!string.IsNullOrWhiteSpace(useEncryption))
            {
                config.Backup.UseEncryption = useEncryption.ToLower() == "y";
            }

            Console.Write($"Maximum backups to keep [{config.Backup.MaxBackupsToKeep}]: ");
            string? maxBackupsToKeep = Console.ReadLine();
            if (!string.IsNullOrWhiteSpace(maxBackupsToKeep) && int.TryParse(maxBackupsToKeep, out int maxBackups))
            {
                config.Backup.MaxBackupsToKeep = maxBackups;
            }

            // Environment variables
            Console.WriteLine();
            Console.WriteLine("Environment Variables:");
            Console.WriteLine("---------------------");
            Console.WriteLine("Enter environment variables (key=value), one per line. Leave empty to finish.");

            while (true)
            {
                Console.Write("Environment variable (key=value): ");
                string? envVar = Console.ReadLine();
                if (string.IsNullOrWhiteSpace(envVar))
                {
                    break;
                }

                string[] parts = envVar.Split('=', 2);
                if (parts.Length == 2)
                {
                    string key = parts[0].Trim();
                    string value = parts[1].Trim();
                    config.AdditionalEnvironmentVariables[key] = value;
                    Console.WriteLine($"Added environment variable: {key}={value}");
                }
                else
                {
                    Console.WriteLine("Invalid format. Use key=value format.");
                }
            }

            // Save configuration
            Console.WriteLine();
            Console.Write("Save these settings as default (y/n) [n]: ");
            string? saveSettings = Console.ReadLine();
            if (!string.IsNullOrWhiteSpace(saveSettings) && saveSettings.ToLower() == "y")
            {
                LauncherConfig.SaveAsync(config, ConfigFilePath).ConfigureAwait(false);
                Console.WriteLine("Settings saved as default.");
            }
        }

        /// <summary>
        /// Applies the selected launch mode
        /// </summary>
        /// <param name="mode">The launch mode to apply</param>
        /// <param name="config">The launcher configuration</param>
        private static void ApplyLaunchMode(LaunchMode mode, LauncherConfig config)
        {
            // Apply common settings
            if (!string.IsNullOrEmpty(config.AppConfigPath))
            {
                Environment.SetEnvironmentVariable("APP_CONFIG_PATH", config.AppConfigPath);
            }

            // Apply logging settings
            if (config.Logging.VerboseLogging)
            {
                Environment.SetEnvironmentVariable("VERBOSE_LOGGING", "true");
            }

            if (!string.IsNullOrEmpty(config.Logging.LogFilePath))
            {
                Environment.SetEnvironmentVariable("LOG_FILE_PATH", config.Logging.LogFilePath);
            }

            Environment.SetEnvironmentVariable("LOG_LEVEL", config.Logging.MinimumLogLevel.ToString());
            Environment.SetEnvironmentVariable("LOG_TO_CONSOLE", config.Logging.LogToConsole.ToString());
            Environment.SetEnvironmentVariable("LOG_TO_FILE", config.Logging.LogToFile.ToString());
            Environment.SetEnvironmentVariable("MAX_LOG_FILE_SIZE_MB", config.Logging.MaxLogFileSizeMB.ToString());
            Environment.SetEnvironmentVariable("MAX_LOG_FILE_COUNT", config.Logging.MaxLogFileCount.ToString());

            // Apply UI settings
            Environment.SetEnvironmentVariable("UI_THEME", config.UI.Theme);
            Environment.SetEnvironmentVariable("UI_LANGUAGE", config.UI.Language);
            Environment.SetEnvironmentVariable("SHOW_SPLASH_SCREEN", config.UI.ShowSplashScreen.ToString());
            Environment.SetEnvironmentVariable("SHOW_TOOLTIPS", config.UI.ShowTooltips.ToString());
            Environment.SetEnvironmentVariable("CONFIRM_EXIT", config.UI.ConfirmExit.ToString());
            Environment.SetEnvironmentVariable("SHOW_ADVANCED_OPTIONS", config.UI.ShowAdvancedOptions.ToString());
            Environment.SetEnvironmentVariable("FONT_SIZE", config.UI.FontSize.ToString());

            // Apply communication settings
            Environment.SetEnvironmentVariable("AUTO_CONNECT_VOCOM", config.Communication.AutoConnectVocom.ToString());
            Environment.SetEnvironmentVariable("USE_WIFI_FALLBACK", config.Communication.UseWiFiFallback.ToString());
            Environment.SetEnvironmentVariable("CONNECTION_TIMEOUT_MS", config.Communication.ConnectionTimeoutMs.ToString());
            Environment.SetEnvironmentVariable("RETRY_ATTEMPTS", config.Communication.RetryAttempts.ToString());
            Environment.SetEnvironmentVariable("RETRY_DELAY_MS", config.Communication.RetryDelayMs.ToString());
            Environment.SetEnvironmentVariable("AUTO_SCAN_ECUS", config.Communication.AutoScanECUs.ToString());
            Environment.SetEnvironmentVariable("DEFAULT_PROTOCOL_TYPE", config.Communication.DefaultProtocolType.ToString());

            // Apply backup settings
            if (!string.IsNullOrEmpty(config.Backup.BackupDirectoryPath))
            {
                Environment.SetEnvironmentVariable("BACKUP_DIRECTORY_PATH", config.Backup.BackupDirectoryPath);
            }

            Environment.SetEnvironmentVariable("USE_COMPRESSION", config.Backup.UseCompression.ToString());
            Environment.SetEnvironmentVariable("USE_ENCRYPTION", config.Backup.UseEncryption.ToString());
            Environment.SetEnvironmentVariable("MAX_BACKUPS_TO_KEEP", config.Backup.MaxBackupsToKeep.ToString());
            Environment.SetEnvironmentVariable("AUTOMATIC_BACKUPS", config.Backup.AutomaticBackups.ToString());
            Environment.SetEnvironmentVariable("AUTOMATIC_BACKUP_INTERVAL_HOURS", config.Backup.AutomaticBackupIntervalHours.ToString());

            // Apply mode-specific settings
            switch (mode)
            {
                case LaunchMode.Normal:
                    // No additional environment variables needed for normal mode
                    break;

                case LaunchMode.Dummy:
                    Environment.SetEnvironmentVariable("USE_DUMMY_IMPLEMENTATIONS", "true");
                    break;

                case LaunchMode.Debug:
                    Environment.SetEnvironmentVariable("USE_DUMMY_IMPLEMENTATIONS", "true");
                    Environment.SetEnvironmentVariable("VERBOSE_LOGGING", "true");
                    Environment.SetEnvironmentVariable("LOG_LEVEL", "Debug");
                    break;

                case LaunchMode.Advanced:
                    // Set custom configuration path if specified
                    if (!string.IsNullOrEmpty(config.CustomConfigPath))
                    {
                        Environment.SetEnvironmentVariable("CUSTOM_CONFIG_PATH", config.CustomConfigPath);
                    }
                    break;

                case LaunchMode.Safe:
                    // Safe mode uses minimal configuration with error recovery
                    Environment.SetEnvironmentVariable("SAFE_MODE", "true");
                    Environment.SetEnvironmentVariable("USE_DUMMY_IMPLEMENTATIONS", "true");
                    Environment.SetEnvironmentVariable("ERROR_RECOVERY_ENABLED", "true");
                    Environment.SetEnvironmentVariable("VERBOSE_LOGGING", "true");
                    Environment.SetEnvironmentVariable("LOG_LEVEL", "Debug");
                    break;

                case LaunchMode.Demo:
                    // Demo mode is pre-configured for demonstrations
                    Environment.SetEnvironmentVariable("DEMO_MODE", "true");
                    Environment.SetEnvironmentVariable("USE_DUMMY_IMPLEMENTATIONS", "true");
                    Environment.SetEnvironmentVariable("SHOW_SPLASH_SCREEN", "true");
                    Environment.SetEnvironmentVariable("AUTO_CONNECT_VOCOM", "false");
                    break;
            }

            // Apply additional environment variables from config
            foreach (var kvp in config.AdditionalEnvironmentVariables)
            {
                Environment.SetEnvironmentVariable(kvp.Key, kvp.Value);
            }

            // Log the applied configuration
            Console.WriteLine($"Applied launch mode: {mode}");
            if (config.Logging.VerboseLogging)
            {
                Console.WriteLine("Environment variables set:");
                foreach (DictionaryEntry entry in Environment.GetEnvironmentVariables())
                {
                    if (entry.Key.ToString().StartsWith("UI_") ||
                        entry.Key.ToString().StartsWith("LOG_") ||
                        entry.Key.ToString().StartsWith("BACKUP_") ||
                        entry.Key.ToString().StartsWith("USE_") ||
                        entry.Key.ToString().StartsWith("AUTO_") ||
                        entry.Key.ToString().StartsWith("SHOW_") ||
                        entry.Key.ToString().StartsWith("DEFAULT_") ||
                        entry.Key.ToString().StartsWith("CUSTOM_") ||
                        entry.Key.ToString().StartsWith("VERBOSE_") ||
                        entry.Key.ToString().StartsWith("SAFE_") ||
                        entry.Key.ToString().StartsWith("DEMO_") ||
                        entry.Key.ToString().StartsWith("ERROR_") ||
                        entry.Key.ToString().StartsWith("CONNECTION_") ||
                        entry.Key.ToString().StartsWith("RETRY_") ||
                        entry.Key.ToString().StartsWith("MAX_") ||
                        entry.Key.ToString().StartsWith("FONT_") ||
                        entry.Key.ToString().StartsWith("CONFIRM_") ||
                        entry.Key.ToString().StartsWith("AUTOMATIC_"))
                    {
                        Console.WriteLine($"  {entry.Key} = {entry.Value}");
                    }
                }
            }
        }

        /// <summary>
        /// Processes command line arguments
        /// </summary>
        /// <param name="args">The command line arguments</param>
        /// <param name="config">The launcher configuration</param>
        private static void ProcessCommandLineArguments(string[] args, LauncherConfig config)
        {
            // Check for help argument
            if (args.Contains("--help") || args.Contains("-h") || args.Contains("/?"))
            {
                ShowHelp();
                Environment.Exit(0);
            }

            // Check for version argument
            if (args.Contains("--version") || args.Contains("-v"))
            {
                ShowVersion();
                Environment.Exit(0);
            }

            for (int i = 0; i < args.Length; i++)
            {
                string arg = args[i].ToLower();

                switch (arg)
                {
                    case "--mode":
                    case "-m":
                        if (i + 1 < args.Length && Enum.TryParse<LaunchMode>(args[i + 1], true, out LaunchMode mode))
                        {
                            config.DefaultLaunchMode = mode;
                            i++; // Skip the next argument
                        }
                        break;

                    case "--silent":
                    case "-s":
                        config.ShowLauncherUI = false;
                        break;

                    case "--gui":
                    case "-g":
                        config.UseGraphicalUI = true;
                        break;

                    case "--config":
                    case "-c":
                        if (i + 1 < args.Length)
                        {
                            config.CustomConfigPath = args[i + 1];
                            i++; // Skip the next argument
                        }
                        break;

                    case "--app-config":
                    case "-a":
                        if (i + 1 < args.Length)
                        {
                            config.AppConfigPath = args[i + 1];
                            i++; // Skip the next argument
                        }
                        break;

                    case "--env":
                    case "-e":
                        if (i + 2 < args.Length)
                        {
                            string key = args[i + 1];
                            string value = args[i + 2];
                            config.AdditionalEnvironmentVariables[key] = value;
                            i += 2; // Skip the next two arguments
                        }
                        break;

                    case "--log-level":
                    case "-l":
                        if (i + 1 < args.Length && Enum.TryParse<LogLevel>(args[i + 1], true, out LogLevel logLevel))
                        {
                            config.Logging.MinimumLogLevel = logLevel;
                            i++; // Skip the next argument
                        }
                        break;

                    case "--verbose":
                        config.Logging.VerboseLogging = true;
                        break;

                    case "--log-path":
                        if (i + 1 < args.Length)
                        {
                            config.Logging.LogFilePath = args[i + 1];
                            i++; // Skip the next argument
                        }
                        break;

                    case "--no-check-updates":
                        config.CheckForUpdates = false;
                        break;

                    case "--advanced":
                        config.ShowAdvancedOptions = true;
                        break;

                    case "--theme":
                        if (i + 1 < args.Length)
                        {
                            config.UI.Theme = args[i + 1];
                            i++; // Skip the next argument
                        }
                        break;

                    case "--language":
                        if (i + 1 < args.Length)
                        {
                            config.UI.Language = args[i + 1];
                            i++; // Skip the next argument
                        }
                        break;

                    case "--no-splash":
                        config.UI.ShowSplashScreen = false;
                        break;

                    case "--no-auto-connect":
                        config.Communication.AutoConnectVocom = false;
                        break;

                    case "--use-wifi":
                        config.Communication.UseWiFiFallback = true;
                        break;

                    case "--protocol":
                        if (i + 1 < args.Length && Enum.TryParse<ProtocolType>(args[i + 1], true, out ProtocolType protocol))
                        {
                            config.Communication.DefaultProtocolType = protocol;
                            i++; // Skip the next argument
                        }
                        break;

                    case "--safe-mode":
                        config.DefaultLaunchMode = LaunchMode.Safe;
                        break;

                    case "--demo-mode":
                        config.DefaultLaunchMode = LaunchMode.Demo;
                        break;
                }
            }
        }

        /// <summary>
        /// Shows the help text
        /// </summary>
        private static void ShowHelp()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version;

            Console.WriteLine($"VolvoFlashWR Launcher v{version}");
            Console.WriteLine("Usage: VolvoFlashWR.Launcher.exe [options]");
            Console.WriteLine();
            Console.WriteLine("Options:");
            Console.WriteLine("  --help, -h, /?             Show this help text");
            Console.WriteLine("  --version, -v              Show version information");
            Console.WriteLine("  --mode, -m <mode>          Set the launch mode (Normal, Dummy, Debug, Advanced, Safe, Demo)");
            Console.WriteLine("  --silent, -s               Don't show the launcher UI");
            Console.WriteLine("  --gui, -g                  Use graphical launcher UI instead of console");
            Console.WriteLine("  --config, -c <path>        Specify a custom launcher configuration file");
            Console.WriteLine("  --app-config, -a <path>    Specify a custom application configuration file");
            Console.WriteLine("  --env, -e <key> <value>    Set an environment variable");
            Console.WriteLine("  --log-level, -l <level>    Set the minimum log level (Trace, Debug, Information, Warning, Error, Critical)");
            Console.WriteLine("  --verbose                  Enable verbose logging");
            Console.WriteLine("  --log-path <path>          Specify a custom log file path");
            Console.WriteLine("  --no-check-updates         Disable automatic update checking");
            Console.WriteLine("  --advanced                 Show advanced options in the launcher UI");
            Console.WriteLine("  --theme <theme>            Set the UI theme (Light, Dark)");
            Console.WriteLine("  --language <language>      Set the UI language (e.g., en-US, fr-FR)");
            Console.WriteLine("  --no-splash                Don't show the splash screen");
            Console.WriteLine("  --no-auto-connect          Don't automatically connect to Vocom devices");
            Console.WriteLine("  --use-wifi                 Use WiFi fallback for Vocom devices");
            Console.WriteLine("  --protocol <protocol>      Set the default protocol type (CAN, J1939, KWP2000, ISO9141)");
            Console.WriteLine("  --safe-mode                Start in safe mode (minimal configuration with error recovery)");
            Console.WriteLine("  --demo-mode                Start in demo mode (pre-configured for demonstrations)");
            Console.WriteLine();
            Console.WriteLine("Examples:");
            Console.WriteLine("  VolvoFlashWR.Launcher.exe --mode Dummy --silent");
            Console.WriteLine("  VolvoFlashWR.Launcher.exe --config custom_config.json --verbose");
            Console.WriteLine("  VolvoFlashWR.Launcher.exe --env USE_DUMMY_IMPLEMENTATIONS true --env VERBOSE_LOGGING true");
        }

        /// <summary>
        /// Shows the version information
        /// </summary>
        private static void ShowVersion()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version;
            var fileVersionInfo = System.Diagnostics.FileVersionInfo.GetVersionInfo(assembly.Location);

            Console.WriteLine($"VolvoFlashWR Launcher v{version}");
            Console.WriteLine($"File Version: {fileVersionInfo.FileVersion}");
            Console.WriteLine($"Product Version: {fileVersionInfo.ProductVersion}");
            Console.WriteLine($"Copyright: {fileVersionInfo.LegalCopyright}");
            Console.WriteLine($"Build Date: {GetBuildDate(assembly)}");
        }

        /// <summary>
        /// Gets the build date of the assembly
        /// </summary>
        /// <param name="assembly">The assembly</param>
        /// <returns>The build date</returns>
        private static DateTime GetBuildDate(Assembly assembly)
        {
            try
            {
                const int peHeaderOffset = 60;
                const int linkerTimestampOffset = 8;

                var bytes = new byte[2048];
                using (var stream = new FileStream(assembly.Location, FileMode.Open, FileAccess.Read))
                {
                    stream.Read(bytes, 0, bytes.Length);
                }

                var headerPos = BitConverter.ToInt32(bytes, peHeaderOffset);
                var secondsSince1970 = BitConverter.ToInt32(bytes, headerPos + linkerTimestampOffset);
                var dt = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

                return dt.AddSeconds(secondsSince1970).ToLocalTime();
            }
            catch
            {
                return DateTime.Now;
            }
        }
    }
}
