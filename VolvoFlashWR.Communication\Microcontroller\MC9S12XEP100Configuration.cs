using System;
using System.Collections.Generic;

namespace VolvoFlashWR.Communication.Microcontroller
{
    /// <summary>
    /// Configuration class for the MC9S12XEP100 microcontroller
    /// Based on the MC9S12XEP100RMV1-1358561 datasheet
    /// </summary>
    public static class MC9S12XEP100Configuration
    {
        #region Memory Specifications

        /// <summary>
        /// Flash memory size in bytes (768KB)
        /// </summary>
        public const int FLASH_SIZE = 768 * 1024;

        /// <summary>
        /// EEPROM size in bytes (4KB)
        /// </summary>
        public const int EEPROM_SIZE = 4 * 1024;

        /// <summary>
        /// RAM size in bytes (48KB)
        /// </summary>
        public const int RAM_SIZE = 48 * 1024;

        /// <summary>
        /// Flash sector size in bytes (1KB)
        /// </summary>
        public const int SECTOR_SIZE = 1024;

        /// <summary>
        /// Flash phrase size in bytes (8 bytes = 64 bits + 8 ECC bits)
        /// </summary>
        public const int PHRASE_SIZE = 8;

        /// <summary>
        /// D-Flash size in bytes (32KB)
        /// </summary>
        public const int D_FLASH_SIZE = 32 * 1024;

        /// <summary>
        /// Buffer RAM size in bytes (2KB)
        /// </summary>
        public const int BUFFER_RAM_SIZE = 2 * 1024;

        #endregion

        #region Clock Specifications

        /// <summary>
        /// Maximum CPU bus frequency in Hz (50MHz)
        /// </summary>
        public const int MAX_CPU_FREQUENCY = 50_000_000;

        /// <summary>
        /// Maximum XGATE bus frequency in Hz (100MHz)
        /// </summary>
        public const int MAX_XGATE_FREQUENCY = 100_000_000;

        /// <summary>
        /// Minimum crystal frequency in Hz (4MHz)
        /// </summary>
        public const int MIN_CRYSTAL_FREQUENCY = 4_000_000;

        /// <summary>
        /// Maximum crystal frequency in Hz (16MHz)
        /// </summary>
        public const int MAX_CRYSTAL_FREQUENCY = 16_000_000;

        #endregion

        #region Communication Protocols

        /// <summary>
        /// CAN protocol configuration
        /// </summary>
        public static class CAN
        {
            /// <summary>
            /// CAN register addresses
            /// </summary>
            public static class Registers
            {
                public const uint CAN0_CTL0 = 0x0140; // CAN0 Control Register 0
                public const uint CAN0_CTL1 = 0x0141; // CAN0 Control Register 1
                public const uint CAN0_BTR0 = 0x0142; // CAN0 Bus Timing Register 0
                public const uint CAN0_BTR1 = 0x0143; // CAN0 Bus Timing Register 1
                public const uint CAN0_RFLG = 0x0144; // CAN0 Receiver Flag Register
                public const uint CAN0_RIER = 0x0145; // CAN0 Receiver Interrupt Enable Register
                public const uint CAN0_TFLG = 0x0146; // CAN0 Transmitter Flag Register
                public const uint CAN0_TIER = 0x0147; // CAN0 Transmitter Interrupt Enable Register
                public const uint CAN0_TARQ = 0x0148; // CAN0 Transmitter Message Abort Request
                public const uint CAN0_TAAK = 0x0149; // CAN0 Transmitter Message Abort Acknowledge
                public const uint CAN0_TBSEL = 0x014A; // CAN0 Transmit Buffer Selection
                public const uint CAN0_IDAC = 0x014B; // CAN0 Identifier Acceptance Control Register
            }

            /// <summary>
            /// CAN baud rates
            /// </summary>
            public static class BaudRates
            {
                public const int HIGH_SPEED = 500_000; // 500 kbps
                public const int LOW_SPEED = 125_000;  // 125 kbps
            }

            /// <summary>
            /// CAN command codes
            /// </summary>
            public static class Commands
            {
                public const byte READ_EEPROM = 0x23;
                public const byte WRITE_EEPROM = 0x3D;
                public const byte READ_FLASH = 0x22;
                public const byte WRITE_FLASH = 0x3C;
                public const byte READ_FAULTS = 0x19;
                public const byte CLEAR_FAULTS = 0x14;
                public const byte READ_PARAMS = 0x22;
                public const byte WRITE_PARAMS = 0x2E;
                public const byte DIAGNOSTIC_SESSION = 0x10;
                public const byte TESTER_PRESENT = 0x3E;
                public const byte ECU_RESET = 0x11;
                public const byte SECURITY_ACCESS = 0x27;
            }
        }

        /// <summary>
        /// SPI protocol configuration
        /// </summary>
        public static class SPI
        {
            /// <summary>
            /// SPI register addresses
            /// </summary>
            public static class Registers
            {
                public const uint SPI0_CR1 = 0x00D0; // SPI0 Control Register 1
                public const uint SPI0_CR2 = 0x00D1; // SPI0 Control Register 2
                public const uint SPI0_BR = 0x00D2;  // SPI0 Baud Rate Register
                public const uint SPI0_SR = 0x00D3;  // SPI0 Status Register
                public const uint SPI0_DR = 0x00D5;  // SPI0 Data Register

                // Flash memory registers
                public const uint FLASH_CMD = 0x0100;       // Flash Command Register
                public const uint FLASH_ADDR_HIGH = 0x0101; // Flash Address Register High
                public const uint FLASH_ADDR_MID = 0x0102;  // Flash Address Register Middle
                public const uint FLASH_ADDR_LOW = 0x0103;  // Flash Address Register Low
                public const uint FLASH_DATA = 0x0104;      // Flash Data Register (start of 8-byte buffer)
                public const uint FLASH_ECC = 0x010C;       // Flash ECC Register
                public const uint FLASH_STAT = 0x010D;      // Flash Status Register
                public const uint FLASH_CNFG = 0x010E;      // Flash Configuration Register
                public const uint FLASH_SECURITY = 0x010F;  // Flash Security Register
                public const uint FLASH_PROT = 0x0110;      // Flash Protection Register
                public const uint FLASH_BURST_DATA = 0x0120; // Flash Burst Data Buffer (start of 256-byte buffer)

                // EEPROM emulation registers
                public const uint EEPROM_CONTROL = 0x0110;  // EEPROM Emulation Control Register
                public const uint EEPROM_STATUS = 0x0111;   // EEPROM Emulation Status Register
                public const uint EEPROM_PROTECT = 0x0112;  // EEPROM Emulation Protection Register
                public const uint EEPROM_CONFIG = 0x0113;   // EEPROM Emulation Configuration Register
            }

            /// <summary>
            /// SPI clock rates
            /// </summary>
            public static class ClockRates
            {
                public const int HIGH_SPEED = 12_500_000; // 12.5 MHz
                public const int LOW_SPEED = 1_000_000;   // 1 MHz
            }

            /// <summary>
            /// SPI command codes
            /// </summary>
            public static class Commands
            {
                public const byte READ_EEPROM = 0x03;
                public const byte WRITE_EEPROM = 0x02;
                public const byte READ_FLASH = 0x0B;
                public const byte WRITE_FLASH = 0x02;
                public const byte ERASE_SECTOR = 0xD8;
                public const byte WRITE_ENABLE = 0x06;
                public const byte READ_STATUS = 0x05;
                public const byte READ_ECC_STATUS = 0x07;
                public const byte WRITE_PHRASE = 0x0A;
            }
        }

        /// <summary>
        /// SCI protocol configuration
        /// </summary>
        public static class SCI
        {
            /// <summary>
            /// SCI register addresses
            /// </summary>
            public static class Registers
            {
                public const uint SCI0_BDH = 0x00C8; // SCI0 Baud Rate Register High
                public const uint SCI0_BDL = 0x00C9; // SCI0 Baud Rate Register Low
                public const uint SCI0_CR1 = 0x00CA; // SCI0 Control Register 1
                public const uint SCI0_CR2 = 0x00CB; // SCI0 Control Register 2
                public const uint SCI0_SR1 = 0x00CC; // SCI0 Status Register 1
                public const uint SCI0_SR2 = 0x00CD; // SCI0 Status Register 2
                public const uint SCI0_DRH = 0x00CE; // SCI0 Data Register High
                public const uint SCI0_DRL = 0x00CF; // SCI0 Data Register Low
            }

            /// <summary>
            /// SCI baud rates
            /// </summary>
            public static class BaudRates
            {
                public const int HIGH_SPEED = 115_200; // 115.2 kbps
                public const int LOW_SPEED = 9_600;    // 9.6 kbps
            }

            /// <summary>
            /// SCI command codes
            /// </summary>
            public static class Commands
            {
                public const byte READ_EEPROM = 0x23;
                public const byte WRITE_EEPROM = 0x3D;
                public const byte READ_FLASH = 0x22;
                public const byte WRITE_FLASH = 0x3C;
                public const byte READ_FAULTS = 0x19;
                public const byte CLEAR_FAULTS = 0x14;
                public const byte READ_PARAMS = 0x22;
                public const byte WRITE_PARAMS = 0x2E;
                public const byte DIAGNOSTIC_SESSION = 0x10;
                public const byte TESTER_PRESENT = 0x3E;
                public const byte ECU_RESET = 0x11;
                public const byte SECURITY_ACCESS = 0x27;
            }
        }

        /// <summary>
        /// IIC protocol configuration
        /// </summary>
        public static class IIC
        {
            /// <summary>
            /// IIC register addresses
            /// </summary>
            public static class Registers
            {
                public const uint IBAD = 0x0036; // IIC Bus Address Register
                public const uint IBFD = 0x0037; // IIC Bus Frequency Divider Register
                public const uint IBCR = 0x0038; // IIC Bus Control Register
                public const uint IBSR = 0x0039; // IIC Bus Status Register
                public const uint IBDR = 0x003A; // IIC Bus Data I/O Register
            }

            /// <summary>
            /// IIC bus speeds
            /// </summary>
            public static class BusSpeeds
            {
                public const int STANDARD_MODE = 100_000; // 100 kHz
                public const int FAST_MODE = 400_000;     // 400 kHz
            }

            /// <summary>
            /// IIC command codes
            /// </summary>
            public static class Commands
            {
                public const byte READ_MEMORY = 0x22;
                public const byte WRITE_MEMORY = 0x2E;
                public const byte READ_EEPROM = 0x23;
                public const byte WRITE_EEPROM = 0x3D;
                public const byte READ_FAULTS = 0x19;
                public const byte CLEAR_FAULTS = 0x14;
                public const byte READ_PARAMS = 0x22;
                public const byte WRITE_PARAMS = 0x2E;
                public const byte DIAGNOSTIC = 0x10;
                public const byte TESTER_PRESENT = 0x3E;
                public const byte ECU_RESET = 0x11;
                public const byte SECURITY_ACCESS = 0x27;
            }
        }

        #endregion

        #region Memory Map

        /// <summary>
        /// Memory map addresses
        /// </summary>
        public static class MemoryMap
        {
            public const uint REGISTERS_START = 0x0000;
            public const uint REGISTERS_END = 0x07FF;
            public const uint RAM_START = 0x0F0000;
            public const uint EEPROM_START = 0x13F000;
            public const uint FLASH_START = 0x700000;
            public const uint D_FLASH_START = 0x100000; // Start of D-Flash memory (used for EEPROM emulation)
            public const uint BUFFER_RAM_START = 0x1000; // Start of Buffer RAM
            public const uint XGATE_RAM_START = 0x0F8000;
            public const uint XGATE_FLASH_START = 0x788000;

            // Flash configuration field addresses
            public const uint BACKDOOR_KEY_START = 0x7FFF00; // Backdoor comparison key (8 bytes)
            public const uint FPROT_ADDRESS = 0x7FFF0C;      // Flash protection byte
            public const uint EPROT_ADDRESS = 0x7FFF0D;      // EEPROM protection byte
            public const uint FOPT_ADDRESS = 0x7FFF0E;       // Flash option byte
        }

        #endregion

        #region Memory Protection Unit (MPU)

        /// <summary>
        /// Memory Protection Unit (MPU) registers and configuration
        /// </summary>
        public static class MPU
        {
            // MPU Register Addresses
            public const uint MPUFLG = 0x0000;      // MPU Flag Register
            public const uint MPUASTAT0 = 0x0001;   // MPU Address Status Register 0
            public const uint MPUASTAT1 = 0x0002;   // MPU Address Status Register 1
            public const uint MPUASTAT2 = 0x0003;   // MPU Address Status Register 2
            public const uint MPUSEL = 0x0005;      // MPU Descriptor Select Register
            public const uint MPUDESC0 = 0x0006;    // MPU Descriptor Register 0
            public const uint MPUDESC1 = 0x0007;    // MPU Descriptor Register 1
            public const uint MPUDESC2 = 0x0008;    // MPU Descriptor Register 2
            public const uint MPUDESC3 = 0x0009;    // MPU Descriptor Register 3
            public const uint MPUDESC4 = 0x000A;    // MPU Descriptor Register 4
            public const uint MPUDESC5 = 0x000B;    // MPU Descriptor Register 5

            // MPU Flag Register (MPUFLG) bit masks
            public const byte MPUFLG_WPF = 0x80;    // Write Protection Fault Flag
            public const byte MPUFLG_NEXF = 0x40;   // Non-Executable Protection Fault Flag
            public const byte MPUFLG_AEF = 0x01;    // Access Error Flag

            // MPU Descriptor Select Register (MPUSEL) bit masks
            public const byte MPUSEL_SVSEN = 0x10;  // Supervisor State Enable
            public const byte MPUSEL_SEL = 0x07;    // Descriptor Select (3 bits)

            // MPU Descriptor Register 0 (MPUDESC0) bit masks
            public const byte MPUDESC0_MSTR0 = 0x80; // Master 0 (S12X CPU) Access Enable
            public const byte MPUDESC0_MSTR1 = 0x40; // Master 1 (XGATE) Access Enable
            public const byte MPUDESC0_MSTR2 = 0x20; // Master 2 Access Enable
            public const byte MPUDESC0_MSTR3 = 0x10; // Master 3 Access Enable

            // MPU Descriptor Register 5 (MPUDESC5) bit masks
            public const byte MPUDESC5_WP = 0x80;    // Write Protect
            public const byte MPUDESC5_NX = 0x40;    // Non-Executable
            public const byte MPUDESC5_START = 0x01; // Start Descriptor
        }

        #endregion

        #region Flash Protection and ECC

        /// <summary>
        /// ECC (Error Correction Code) registers and configuration
        /// </summary>
        public static class ECC
        {
            // ECC register addresses
            public const uint ECCSTAT = 0x0110;    // ECC Status Register
            public const uint ECCCTL = 0x0111;     // ECC Control Register
            public const uint ECCDR = 0x0112;      // ECC Data Register
            public const uint ECCERR = 0x0113;     // ECC Error Register
            public const uint ECCTEST = 0x0114;    // ECC Test Register

            // ECC Status Register (ECCSTAT) bit masks
            public const byte ECCSTAT_SBERR = 0x01; // Single-Bit Error Flag
            public const byte ECCSTAT_MBERR = 0x02; // Multi-Bit Error Flag
            public const byte ECCSTAT_ACCERR = 0x04; // Access Error Flag

            // ECC Control Register (ECCCTL) bit masks
            public const byte ECCCTL_ECCE = 0x01;   // ECC Enable
            public const byte ECCCTL_ECCIE = 0x02;  // ECC Interrupt Enable
            public const byte ECCCTL_SBSTP = 0x04;  // Single-Bit Error Stop
            public const byte ECCCTL_MBSTP = 0x08;  // Multi-Bit Error Stop

            // ECC Error Register (ECCERR) bit masks
            public const byte ECCERR_BPOS = 0x07;   // Bit Position (3 bits)
            public const byte ECCERR_WPOS = 0x70;   // Word Position (3 bits)
            public const byte ECCERR_PHAS = 0x80;   // Phase Indicator
        }

        /// <summary>
        /// Flash protection and ECC registers
        /// </summary>
        public static class FlashProtection
        {
            public const uint FLASH_PROT = 0x0100;      // Flash protection register
            public const uint FLASH_ECC_CTRL = 0x0104;  // Flash ECC control register
            public const uint FLASH_ECC_ERR = 0x0108;   // Flash ECC error register

            // Flash Protection Register (FPROT) bit masks
            public const byte FPOPEN = 0x80;            // Flash Protection Open: 0=Protection Enabled, 1=Protection Disabled
            public const byte FPHDIS = 0x40;            // Flash Protection Higher Address Range Disable: 0=Enabled, 1=Disabled
            public const byte FPHS = 0x30;              // Flash Protection Higher Address Size (2 bits)
            public const byte FPLDIS = 0x08;            // Flash Protection Lower Address Range Disable: 0=Enabled, 1=Disabled
            public const byte FPLS = 0x07;              // Flash Protection Lower Address Size (3 bits)

            // EEPROM Protection Register (EPROT) bit masks
            public const byte EPOPEN = 0x80;            // EEPROM Protection Open: 0=Protection Enabled, 1=Protection Disabled
            public const byte EPDIS = 0x40;             // EEPROM Protection Disable: 0=Enabled, 1=Disabled
            public const byte EPS = 0x07;               // EEPROM Protection Size (3 bits)

            // Flash Security Register bit masks
            public const byte SEC = 0x03;               // Flash Security Bits: 00,01,11=Secured, 10=Unsecured

            // Flash Option Register (FOPT) bit masks
            public const byte KEYEN = 0x80;             // Backdoor Key Enable: 0=Disabled, 1=Enabled
            public const byte FNORED = 0x40;            // Vector Redirection Disable: 0=Enabled, 1=Disabled
            public const byte SEC01 = 0x20;             // Security State Code Bit 0,1: 00=Secured, 01=Secured, 10=Unsecured, 11=Secured
            public const byte SEC23 = 0x10;             // Security State Code Bit 2,3: 00=Secured, 01=Secured, 10=Unsecured, 11=Secured
        }

        #endregion

        #region Flash Performance Configuration

        /// <summary>
        /// Flash performance configuration
        /// </summary>
        public static class FlashPerformance
        {
            // Flash Command Register (FCMD) command codes
            public const byte CMD_ERASE_SECTOR = 0x0A;         // Erase P-Flash Sector
            public const byte CMD_ERASE_D_FLASH_SECTOR = 0x0B; // Erase D-Flash Sector
            public const byte CMD_PROGRAM_PHRASE = 0x06;       // Program P-Flash Phrase
            public const byte CMD_PROGRAM_D_FLASH_PHRASE = 0x07; // Program D-Flash Phrase
            public const byte CMD_PROGRAM_BURST = 0x08;        // Program P-Flash Burst
            public const byte CMD_PROGRAM_D_FLASH_BURST = 0x09; // Program D-Flash Burst
            public const byte CMD_ERASE_VERIFY = 0x05;         // Erase Verify
            public const byte CMD_PROGRAM_VERIFY = 0x04;       // Program Verify
            public const byte CMD_READ_ONCE = 0x0C;            // Read Once
            public const byte CMD_PROGRAM_ONCE = 0x0D;         // Program Once
            public const byte CMD_BACKDOOR_ACCESS = 0x0E;      // Backdoor Access
            public const byte CMD_SET_USER_MARGIN = 0x0F;      // Set User Margin

            // Flash Status Register (FSTAT) bit masks
            public const byte FSTAT_CCIF = 0x40;       // Command Complete Interrupt Flag
            public const byte FSTAT_ACCERR = 0x20;     // Access Error Flag
            public const byte FSTAT_PVIOL = 0x10;      // Protection Violation Flag
            public const byte FSTAT_MGSTAT1 = 0x02;    // Memory Controller Command Completion Status Flag 1
            public const byte FSTAT_MGSTAT0 = 0x01;    // Memory Controller Command Completion Status Flag 0
            public const byte FSTAT_CBEIF = 0x80;      // Command Buffer Empty Interrupt Flag

            // Flash Configuration Register (FCNFG) bit masks
            public const byte FCNFG_CCIE = 0x80;       // Command Complete Interrupt Enable
            public const byte FCNFG_CBEIE = 0x40;      // Command Buffer Empty Interrupt Enable
            public const byte FCNFG_KEYACC = 0x20;     // Enable Security Key Writing

            // Flash burst mode configuration
            public const int MAX_BURST_SIZE = 32;      // Maximum number of phrases in a burst (32 phrases = 256 bytes)
            public const int BURST_BUFFER_SIZE = 256;  // Size of the burst buffer in bytes

            // Flash operation timing (in milliseconds)
            public const int SECTOR_ERASE_TIME = 10;   // Typical time to erase a sector (10ms)
            public const double PHRASE_PROGRAM_TIME = 0.5; // Typical time to program a phrase (0.5ms)
            public const double BURST_PROGRAM_TIME = 0.2; // Typical time per phrase in burst mode (0.2ms)
            public const double PHRASE_READ_TIME = 0.1; // Typical time to read a phrase (0.1ms)

            // Flash endurance specifications
            public const int PROGRAM_ERASE_CYCLES = 100000; // Minimum P/E cycles
            public const int DATA_RETENTION_YEARS = 20;     // Data retention in years

            // ECC error handling configuration
            public const bool AUTO_CORRECT_SINGLE_BIT_ERRORS = true; // Automatically correct single-bit errors by rewriting the data
            public const int MAX_ACCEPTABLE_SINGLE_BIT_ERRORS = 10;  // Maximum number of single-bit errors before taking action
            public const int MAX_ACCEPTABLE_MULTI_BIT_ERRORS = 1;    // Maximum number of multi-bit errors before aborting operation

            // Adaptive performance configuration
            public const bool USE_ADAPTIVE_BURST_SIZE = true;        // Use adaptive burst size based on memory characteristics
            public const int MIN_BURST_SIZE = 4;                     // Minimum burst size for adaptive mode
            public const int OPTIMAL_BURST_SIZE_EEPROM = 8;          // Optimal burst size for EEPROM operations
            public const int OPTIMAL_BURST_SIZE_DFLASH = 16;         // Optimal burst size for D-Flash operations
            public const int OPTIMAL_BURST_SIZE_PFLASH = 32;         // Optimal burst size for P-Flash operations
        }

        #endregion

        #region Device Information

        /// <summary>
        /// Device information
        /// </summary>
        public static class DeviceInfo
        {
            public const string DEVICE_NAME = "MC9S12XEP100";
            public const string MANUFACTURER = "Freescale Semiconductor";
            public const string ARCHITECTURE = "HCS12X";
            public const string CORE = "S12X";
            public const string FAMILY = "S12XE";
        }

        #endregion

        #region Protocol Configuration

        /// <summary>
        /// Gets the default protocol configuration for the specified protocol type
        /// </summary>
        /// <param name="protocolType">The protocol type</param>
        /// <returns>Dictionary containing protocol configuration parameters</returns>
        public static Dictionary<string, object> GetDefaultProtocolConfiguration(Core.Enums.ECUProtocolType protocolType)
        {
            switch (protocolType)
            {
                case Core.Enums.ECUProtocolType.CAN:
                    return new Dictionary<string, object>
                    {
                        { "HighSpeedBaudRate", CAN.BaudRates.HIGH_SPEED },
                        { "LowSpeedBaudRate", CAN.BaudRates.LOW_SPEED },
                        { "DefaultRequestId", 0x7E0 },
                        { "DefaultResponseId", 0x7E8 },
                        { "MaxBlockSize", 64 }
                    };

                case Core.Enums.ECUProtocolType.SPI:
                    return new Dictionary<string, object>
                    {
                        { "HighSpeedClockRate", SPI.ClockRates.HIGH_SPEED },
                        { "LowSpeedClockRate", SPI.ClockRates.LOW_SPEED },
                        { "DataWidth", 16 },
                        { "ClockPolarity", 0 },
                        { "ClockPhase", 0 },
                        { "MaxBlockSize", 32 }
                    };

                case Core.Enums.ECUProtocolType.SCI:
                    return new Dictionary<string, object>
                    {
                        { "HighSpeedBaudRate", SCI.BaudRates.HIGH_SPEED },
                        { "LowSpeedBaudRate", SCI.BaudRates.LOW_SPEED },
                        { "DataBits", 8 },
                        { "Parity", "None" },
                        { "StopBits", 1 },
                        { "MaxBlockSize", 64 }
                    };

                case Core.Enums.ECUProtocolType.IIC:
                    return new Dictionary<string, object>
                    {
                        { "StandardModeBusSpeed", IIC.BusSpeeds.STANDARD_MODE },
                        { "FastModeBusSpeed", IIC.BusSpeeds.FAST_MODE },
                        { "DefaultAddress", 0x42 },
                        { "MaxBlockSize", 32 }
                    };

                default:
                    return new Dictionary<string, object>();
            }
        }

        #endregion
    }
}
