using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.UI.Commands;
using VolvoFlashWR.UI.Services;
using IDialogService = VolvoFlashWR.Core.Interfaces.IDialogService;

namespace VolvoFlashWR.UI.ViewModels
{
    /// <summary>
    /// ViewModel for the backup versions view
    /// </summary>
    public class BackupVersionsViewModel : INotifyPropertyChanged
    {
        private readonly IBackupService _backupService;
        private readonly ILoggingService _logger;
        private readonly IDialogService _dialogService;
        private readonly IECUCommunicationService _ecuService;

        private BackupData _selectedBackup;
        private BackupData _selectedVersion;
        private BackupVersionTree _versionTree;
        private bool _isLoading;

        /// <summary>
        /// Event that is fired when a property changes
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// The selected backup
        /// </summary>
        public BackupData SelectedBackup
        {
            get => _selectedBackup;
            set
            {
                if (_selectedBackup != value)
                {
                    _selectedBackup = value;
                    OnPropertyChanged();
                    LoadVersionTree();
                }
            }
        }

        /// <summary>
        /// The currently selected version
        /// </summary>
        public BackupData SelectedVersion
        {
            get => _selectedVersion;
            set
            {
                if (_selectedVersion != value)
                {
                    _selectedVersion = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ParameterCount));
                }
            }
        }

        /// <summary>
        /// The version tree for the selected backup
        /// </summary>
        public BackupVersionTree VersionTree
        {
            get => _versionTree;
            set
            {
                if (_versionTree != value)
                {
                    _versionTree = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Whether the view is currently loading data
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (_isLoading != value)
                {
                    _isLoading = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// The number of parameters in the selected version
        /// </summary>
        public int ParameterCount => SelectedVersion?.Parameters?.Count ?? 0;

        /// <summary>
        /// Command to create a new version of the selected backup
        /// </summary>
        public ICommand CreateNewVersionCommand { get; }

        /// <summary>
        /// Command to compare two versions
        /// </summary>
        public ICommand CompareVersionsCommand { get; }

        /// <summary>
        /// Command to merge two versions
        /// </summary>
        public ICommand MergeVersionsCommand { get; }

        /// <summary>
        /// Command to restore the selected version
        /// </summary>
        public ICommand RestoreVersionCommand { get; }

        /// <summary>
        /// Command to close the view
        /// </summary>
        public ICommand CloseCommand { get; }

        /// <summary>
        /// Event that is fired when the view should be closed
        /// </summary>
        public event EventHandler CloseRequested;

        /// <summary>
        /// Initializes a new instance of the BackupVersionsViewModel class
        /// </summary>
        /// <param name="backupService">The backup service</param>
        /// <param name="logger">The logging service</param>
        /// <param name="dialogService">The dialog service</param>
        /// <param name="ecuService">The ECU communication service</param>
        public BackupVersionsViewModel(IBackupService backupService, ILoggingService logger, IDialogService dialogService, IECUCommunicationService ecuService)
        {
            _backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            _ecuService = ecuService ?? throw new ArgumentNullException(nameof(ecuService));

            // Initialize commands
            CreateNewVersionCommand = new RelayCommand(
                _ => { CreateNewVersionAsync().ConfigureAwait(false); },
                _ => CanCreateNewVersion());
            CompareVersionsCommand = new RelayCommand(
                _ => { CompareVersionsAsync().ConfigureAwait(false); },
                _ => CanCompareVersions());
            MergeVersionsCommand = new RelayCommand(
                _ => { MergeVersionsAsync().ConfigureAwait(false); },
                _ => CanMergeVersions());
            RestoreVersionCommand = new RelayCommand(
                _ => { RestoreVersionAsync().ConfigureAwait(false); },
                _ => CanRestoreVersion());
            CloseCommand = new RelayCommand(
                _ => Close(),
                _ => true);
        }

        /// <summary>
        /// Initializes a new instance of the BackupVersionsViewModel class with a selected backup
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="backupService">The backup service</param>
        /// <param name="dialogService">The dialog service</param>
        /// <param name="ecuService">The ECU communication service</param>
        /// <param name="selectedBackup">The selected backup</param>
        public BackupVersionsViewModel(ILoggingService logger, IBackupService backupService, IDialogService dialogService, IECUCommunicationService ecuService, BackupData selectedBackup)
        {
            _backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            _ecuService = ecuService ?? throw new ArgumentNullException(nameof(ecuService));

            // Initialize commands
            CreateNewVersionCommand = new RelayCommand(
                _ => { CreateNewVersionAsync().ConfigureAwait(false); },
                _ => CanCreateNewVersion());
            CompareVersionsCommand = new RelayCommand(
                _ => { CompareVersionsAsync().ConfigureAwait(false); },
                _ => CanCompareVersions());
            MergeVersionsCommand = new RelayCommand(
                _ => { MergeVersionsAsync().ConfigureAwait(false); },
                _ => CanMergeVersions());
            RestoreVersionCommand = new RelayCommand(
                _ => { RestoreVersionAsync().ConfigureAwait(false); },
                _ => CanRestoreVersion());
            CloseCommand = new RelayCommand(
                _ => Close(),
                _ => true);

            // Set the selected backup
            SelectedBackup = selectedBackup;
        }

        /// <summary>
        /// Loads the version tree for the selected backup
        /// </summary>
        private async void LoadVersionTree()
        {
            if (SelectedBackup == null)
            {
                VersionTree = null;
                SelectedVersion = null;
                return;
            }

            try
            {
                IsLoading = true;
                VersionTree = await _backupService.GetBackupVersionTreeAsync(SelectedBackup.Id);
                SelectedVersion = VersionTree?.LatestVersion;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error loading version tree", "BackupVersionsViewModel", ex);
                _dialogService.ShowError("Error loading version tree", ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Creates a new version of the selected backup
        /// </summary>
        private async Task CreateNewVersionAsync()
        {
            try
            {
                IsLoading = true;

                // Find the ECU
                var ecuDevices = await _ecuService.ScanForECUsAsync();
                var ecu = ecuDevices.Find(e => e.Id == SelectedBackup.ECUId);

                if (ecu == null)
                {
                    _dialogService.ShowError("ECU Not Found", $"Could not find ECU with ID {SelectedBackup.ECUId}");
                    return;
                }

                // Get version notes from user
                string versionNotes = _dialogService.ShowInputDialog("Version Notes", "Enter notes for the new version:", "");

                // Create the new version
                var newVersion = await _backupService.CreateBackupVersionAsync(
                    SelectedVersion,
                    ecu,
                    versionNotes
                );

                if (newVersion != null)
                {
                    _dialogService.ShowInformation("Success", "New version created successfully.");
                    LoadVersionTree();
                }
                else
                {
                    _dialogService.ShowError("Error", "Failed to create new version.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error creating new version", "BackupVersionsViewModel", ex);
                _dialogService.ShowError("Error creating new version", ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Compares two versions of the backup
        /// </summary>
        private async Task CompareVersionsAsync()
        {
            try
            {
                // Add a delay to make this method truly async
                await Task.Delay(1);

                // TODO: Implement version comparison UI
                _dialogService.ShowInformation("Not Implemented", "Version comparison is not yet implemented.");
            }
            catch (Exception ex)
            {
                _logger.LogError("Error comparing versions", "BackupVersionsViewModel", ex);
                _dialogService.ShowError("Error comparing versions", ex.Message);
            }
        }

        /// <summary>
        /// Merges two versions of the backup
        /// </summary>
        private async Task MergeVersionsAsync()
        {
            try
            {
                // Add a delay to make this method truly async
                await Task.Delay(1);

                // TODO: Implement version merging UI
                _dialogService.ShowInformation("Not Implemented", "Version merging is not yet implemented.");
            }
            catch (Exception ex)
            {
                _logger.LogError("Error merging versions", "BackupVersionsViewModel", ex);
                _dialogService.ShowError("Error merging versions", ex.Message);
            }
        }

        /// <summary>
        /// Restores the selected version to the ECU
        /// </summary>
        private async Task RestoreVersionAsync()
        {
            try
            {
                IsLoading = true;

                // Find the ECU
                var ecuDevices = await _ecuService.ScanForECUsAsync();
                var ecu = ecuDevices.Find(e => e.Id == SelectedVersion.ECUId);

                if (ecu == null)
                {
                    _dialogService.ShowError("ECU Not Found", $"Could not find ECU with ID {SelectedVersion.ECUId}");
                    return;
                }

                // Confirm with user
                bool confirmed = _dialogService.ShowConfirmation(
                    "Confirm Restore",
                    $"Are you sure you want to restore version {SelectedVersion.Version} to ECU {ecu.Name}?\n\n" +
                    "This will overwrite the current data on the ECU."
                );

                if (!confirmed)
                {
                    return;
                }

                // Restore the backup
                bool success = await _backupService.RestoreBackupAsync(SelectedVersion, ecu);

                if (success)
                {
                    _dialogService.ShowInformation("Success", "Backup restored successfully.");
                }
                else
                {
                    _dialogService.ShowError("Error", "Failed to restore backup.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error restoring version", "BackupVersionsViewModel", ex);
                _dialogService.ShowError("Error restoring version", ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Closes the view
        /// </summary>
        private void Close()
        {
            CloseRequested?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Determines whether a new version can be created
        /// </summary>
        private bool CanCreateNewVersion()
        {
            return SelectedVersion != null && !IsLoading;
        }

        /// <summary>
        /// Determines whether versions can be compared
        /// </summary>
        private bool CanCompareVersions()
        {
            return VersionTree != null && VersionTree.VersionCount > 1 && !IsLoading;
        }

        /// <summary>
        /// Determines whether versions can be merged
        /// </summary>
        private bool CanMergeVersions()
        {
            return VersionTree != null && VersionTree.VersionCount > 1 && !IsLoading;
        }

        /// <summary>
        /// Determines whether the selected version can be restored
        /// </summary>
        private bool CanRestoreVersion()
        {
            return SelectedVersion != null && !IsLoading;
        }

        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">The name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}

