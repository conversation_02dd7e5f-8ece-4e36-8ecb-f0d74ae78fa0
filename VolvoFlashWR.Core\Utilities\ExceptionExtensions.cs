using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace VolvoFlashWR.Core.Utilities
{
    /// <summary>
    /// Extension methods for Exception class
    /// </summary>
    public static class ExceptionExtensions
    {
        /// <summary>
        /// Gets a detailed message from an exception, including inner exceptions
        /// </summary>
        /// <param name="ex">The exception</param>
        /// <param name="includeStackTrace">Whether to include stack traces</param>
        /// <returns>A detailed message</returns>
        public static string GetDetailedMessage(this Exception ex, bool includeStackTrace = false)
        {
            if (ex == null)
            {
                return string.Empty;
            }

            var sb = new StringBuilder();
            AppendExceptionDetails(sb, ex, includeStackTrace, 0);
            return sb.ToString();
        }

        /// <summary>
        /// Gets the root cause of an exception by traversing inner exceptions
        /// </summary>
        /// <param name="ex">The exception</param>
        /// <returns>The root cause exception</returns>
        public static Exception GetRootCause(this Exception ex)
        {
            if (ex == null)
            {
                return null;
            }

            var innerException = ex.InnerException;
            while (innerException != null)
            {
                ex = innerException;
                innerException = ex.InnerException;
            }

            return ex;
        }

        /// <summary>
        /// Determines if an exception is of a specific type or has an inner exception of that type
        /// </summary>
        /// <typeparam name="T">The exception type to check for</typeparam>
        /// <param name="ex">The exception</param>
        /// <returns>True if the exception or any inner exception is of type T</returns>
        public static bool ContainsExceptionOfType<T>(this Exception ex) where T : Exception
        {
            if (ex == null)
            {
                return false;
            }

            if (ex is T)
            {
                return true;
            }

            return ex.InnerException != null && ContainsExceptionOfType<T>(ex.InnerException);
        }

        /// <summary>
        /// Finds an exception of a specific type in the exception chain
        /// </summary>
        /// <typeparam name="T">The exception type to find</typeparam>
        /// <param name="ex">The exception</param>
        /// <returns>The exception of type T, or null if not found</returns>
        public static T FindExceptionOfType<T>(this Exception ex) where T : Exception
        {
            if (ex == null)
            {
                return null;
            }

            if (ex is T typedException)
            {
                return typedException;
            }

            return ex.InnerException != null ? FindExceptionOfType<T>(ex.InnerException) : null;
        }

        /// <summary>
        /// Determines if an exception is transient (temporary) and can be retried
        /// </summary>
        /// <param name="ex">The exception</param>
        /// <returns>True if the exception is transient</returns>
        public static bool IsTransient(this Exception ex)
        {
            if (ex == null)
            {
                return false;
            }

            // Check for common transient exceptions
            if (ex is TimeoutException ||
                ex is System.Net.Sockets.SocketException ||
                ex is System.IO.IOException ||
                ex is System.Net.WebException)
            {
                return true;
            }

            // Check inner exception
            return ex.InnerException != null && IsTransient(ex.InnerException);
        }

        /// <summary>
        /// Gets a dictionary of exception properties and their values
        /// </summary>
        /// <param name="ex">The exception to get properties from</param>
        /// <returns>A dictionary of property names and values</returns>
        public static Dictionary<string, string> GetExceptionProperties(this Exception ex)
        {
            if (ex == null)
            {
                return new Dictionary<string, string>();
            }

            var properties = new Dictionary<string, string>
            {
                { "ExceptionType", ex.GetType().FullName },
                { "Message", ex.Message },
                { "Source", ex.Source },
                { "TargetSite", ex.TargetSite?.ToString() }
            };

            // Add data dictionary items
            if (ex.Data != null && ex.Data.Count > 0)
            {
                foreach (var key in ex.Data.Keys)
                {
                    if (key != null)
                    {
                        properties.Add($"Data[{key}]", ex.Data[key]?.ToString() ?? "null");
                    }
                }
            }

            // Add specific exception type properties using reflection
            var type = ex.GetType();
            var propertyInfos = type.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.Name != "Message" && p.Name != "Source" && p.Name != "StackTrace" &&
                            p.Name != "TargetSite" && p.Name != "Data" && p.Name != "InnerException" &&
                            p.Name != "HelpLink" && p.Name != "HResult");

            foreach (var propertyInfo in propertyInfos)
            {
                try
                {
                    var value = propertyInfo.GetValue(ex);
                    properties.Add(propertyInfo.Name, value?.ToString() ?? "null");
                }
                catch
                {
                    // Ignore any errors in property access
                    properties.Add(propertyInfo.Name, "Error accessing property");
                }
            }

            return properties;
        }

        /// <summary>
        /// Gets a flat list of all inner exceptions, including those in AggregateException
        /// </summary>
        /// <param name="ex">The exception to get inner exceptions from</param>
        /// <returns>A list of all inner exceptions</returns>
        public static List<Exception> GetAllInnerExceptions(this Exception ex)
        {
            var exceptions = new List<Exception>();
            if (ex == null)
            {
                return exceptions;
            }

            exceptions.Add(ex);

            // Handle AggregateException specially
            if (ex is AggregateException aggregateEx)
            {
                foreach (var innerEx in aggregateEx.InnerExceptions)
                {
                    exceptions.AddRange(GetAllInnerExceptions(innerEx));
                }
            }
            else if (ex.InnerException != null)
            {
                exceptions.AddRange(GetAllInnerExceptions(ex.InnerException));
            }

            return exceptions;
        }

        /// <summary>
        /// Determines if an exception is critical (i.e., should terminate the application)
        /// </summary>
        /// <param name="ex">The exception to check</param>
        /// <returns>True if the exception is critical, false otherwise</returns>
        public static bool IsCriticalException(this Exception ex)
        {
            if (ex == null)
            {
                return false;
            }

            // Check for critical exception types
            return ex is OutOfMemoryException ||
                   ex is StackOverflowException ||
                   ex is System.Threading.ThreadAbortException ||
                   ex is AccessViolationException ||
                   ex is AppDomainUnloadedException ||
                   ex is BadImageFormatException ||
                   ex is InvalidProgramException;
        }

        /// <summary>
        /// Attempts to get a user-friendly error message from an exception
        /// </summary>
        /// <param name="ex">The exception to get a message from</param>
        /// <returns>A user-friendly error message</returns>
        public static string GetUserFriendlyMessage(this Exception ex)
        {
            if (ex == null)
            {
                return "An unknown error occurred.";
            }

            // Handle specific exception types with user-friendly messages
            if (ex is UnauthorizedAccessException)
            {
                return "You don't have permission to access the requested resource. Please check your credentials or contact your administrator.";
            }
            else if (ex is System.IO.IOException)
            {
                return "An error occurred while reading or writing to a file or device. Please check if the file is accessible and not in use by another program.";
            }
            else if (ex is System.Net.WebException)
            {
                return "A network error occurred. Please check your internet connection and try again.";
            }
            else if (ex is TimeoutException)
            {
                return "The operation timed out. Please try again later.";
            }
            else if (ex is System.Data.DataException)
            {
                return "An error occurred while processing data. Please check your input and try again.";
            }
            else if (ex is InvalidOperationException)
            {
                return "The requested operation is invalid in the current state. Please try a different action.";
            }
            else if (ex is ArgumentException)
            {
                return "Invalid input provided. Please check your input and try again.";
            }
            else if (ex is NullReferenceException)
            {
                return "The application encountered a null reference. This is likely a bug in the application.";
            }
            else if (ex is FormatException)
            {
                return "The input format is incorrect. Please check your input and try again.";
            }
            else if (ex is OverflowException)
            {
                return "A numeric overflow occurred. Please use smaller values and try again.";
            }
            else if (ex is OutOfMemoryException)
            {
                return "The application has run out of memory. Please close other applications and try again.";
            }
            else if (ex is System.IO.FileNotFoundException)
            {
                return "The requested file was not found. Please check the file path and try again.";
            }
            else if (ex is System.IO.DirectoryNotFoundException)
            {
                return "The requested directory was not found. Please check the directory path and try again.";
            }
            else if (ex is System.IO.PathTooLongException)
            {
                return "The specified path is too long. Please use a shorter path and try again.";
            }
            else if (ex is System.IO.DriveNotFoundException)
            {
                return "The specified drive was not found. Please check the drive and try again.";
            }
            else if (ex is System.Security.SecurityException)
            {
                return "A security error occurred. You may not have permission to perform this action.";
            }
            else if (ex is TaskCanceledException)
            {
                return "The operation was canceled. Please try again.";
            }
            else if (ex is AggregateException aggregateEx)
            {
                // For AggregateException, get the first inner exception's message
                if (aggregateEx.InnerExceptions.Count > 0)
                {
                    return GetUserFriendlyMessage(aggregateEx.InnerExceptions[0]);
                }
            }

            // For other exceptions, use the exception message if it's not too technical
            if (!string.IsNullOrEmpty(ex.Message) && ex.Message.Length < 150 && !ex.Message.Contains("Exception"))
            {
                return ex.Message;
            }

            // Default generic message
            return "An unexpected error occurred. Please try again or contact support if the problem persists.";
        }

        /// <summary>
        /// Appends exception details to a StringBuilder
        /// </summary>
        /// <param name="sb">The StringBuilder</param>
        /// <param name="ex">The exception</param>
        /// <param name="includeStackTrace">Whether to include stack traces</param>
        /// <param name="level">The nesting level</param>
        private static void AppendExceptionDetails(StringBuilder sb, Exception ex, bool includeStackTrace, int level)
        {
            if (ex == null)
            {
                return;
            }

            // Add indentation based on level
            string indent = new string(' ', level * 4);

            // Add exception type and message
            sb.AppendLine($"{indent}Exception: {ex.GetType().Name}");
            sb.AppendLine($"{indent}Message: {ex.Message}");

            // Add stack trace if requested
            if (includeStackTrace && !string.IsNullOrEmpty(ex.StackTrace))
            {
                sb.AppendLine($"{indent}StackTrace:");
                sb.AppendLine($"{indent}{ex.StackTrace.Replace("\n", $"\n{indent}")}");
            }

            // Add data if available
            if (ex.Data.Count > 0)
            {
                sb.AppendLine($"{indent}Additional Data:");
                foreach (var key in ex.Data.Keys)
                {
                    sb.AppendLine($"{indent}    {key}: {ex.Data[key]}");
                }
            }

            // Add inner exception details
            if (ex.InnerException != null)
            {
                sb.AppendLine($"{indent}Inner Exception:");
                AppendExceptionDetails(sb, ex.InnerException, includeStackTrace, level + 1);
            }
        }
    }
}
