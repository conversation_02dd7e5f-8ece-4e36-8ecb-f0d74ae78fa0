using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Diagnostics;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Tests.Diagnostics
{
    [TestFixture]
    public class FlashOperationMonitorTests
    {
        private Mock<ILoggingService> _mockLogger;
        private FlashOperationMonitor _monitor;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _monitor = new FlashOperationMonitor(_mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _monitor.Dispose();
        }

        [Test]
        public void RegisterOperation_ShouldAddOperationToList()
        {
            // Arrange
            var operation = new FlashOperation(FlashOperationType.Write, 0x1000, 1024);

            // Act
            var operationId = _monitor.RegisterOperation(operation);

            // Assert
            Assert.That(_monitor.Operations.Count, Is.EqualTo(1));
            Assert.That(_monitor.Operations[0].Id, Is.EqualTo(operationId));
            Assert.That(_monitor.Operations[0].OperationType, Is.EqualTo(FlashOperationType.Write));
            Assert.That(_monitor.Operations[0].Address, Is.EqualTo(0x1000));
            Assert.That(_monitor.Operations[0].Size, Is.EqualTo(1024));
        }

        [Test]
        public void UpdateOperationProgress_ShouldUpdateOperation()
        {
            // Arrange
            var operation = new FlashOperation(FlashOperationType.Write, 0x1000, 1024);
            var operationId = _monitor.RegisterOperation(operation);

            // Act
            _monitor.UpdateOperationProgress(operationId, 512, FlashOperationStatus.InProgress);

            // Assert
            var updatedOperation = _monitor.GetOperation(operationId);
            Assert.That(updatedOperation, Is.Not.Null);
            Assert.That(updatedOperation.BytesProcessed, Is.EqualTo(512));
            Assert.That(updatedOperation.Status, Is.EqualTo(FlashOperationStatus.InProgress));
            Assert.That(updatedOperation.ProgressPercentage, Is.EqualTo(50).Within(0.1));
        }

        [Test]
        public void CompleteOperation_ShouldMarkOperationAsCompleted()
        {
            // Arrange
            var operation = new FlashOperation(FlashOperationType.Write, 0x1000, 1024);
            var operationId = _monitor.RegisterOperation(operation);

            // Act
            _monitor.CompleteOperation(operationId, true);

            // Assert
            var completedOperation = _monitor.GetOperation(operationId);
            Assert.That(completedOperation, Is.Not.Null);
            Assert.That(completedOperation.Status, Is.EqualTo(FlashOperationStatus.Completed));
            Assert.That(completedOperation.BytesProcessed, Is.EqualTo(1024));
            Assert.That(completedOperation.EndTime, Is.Not.Null);
        }

        [Test]
        public void CompleteOperation_WithFailure_ShouldMarkOperationAsFailed()
        {
            // Arrange
            var operation = new FlashOperation(FlashOperationType.Write, 0x1000, 1024);
            var operationId = _monitor.RegisterOperation(operation);

            // Act
            _monitor.CompleteOperation(operationId, false, "Test error message");

            // Assert
            var failedOperation = _monitor.GetOperation(operationId);
            Assert.That(failedOperation, Is.Not.Null);
            Assert.That(failedOperation.Status, Is.EqualTo(FlashOperationStatus.Failed));
            Assert.That(failedOperation.ErrorMessage, Is.EqualTo("Test error message"));
            Assert.That(failedOperation.EndTime, Is.Not.Null);
        }

        [Test]
        public void ClearCompletedOperations_ShouldRemoveCompletedAndFailedOperations()
        {
            // Arrange
            var operation1 = new FlashOperation(FlashOperationType.Write, 0x1000, 1024);
            var operation2 = new FlashOperation(FlashOperationType.Read, 0x2000, 512);
            var operation3 = new FlashOperation(FlashOperationType.Erase, 0x3000, 2048);

            var operationId1 = _monitor.RegisterOperation(operation1);
            var operationId2 = _monitor.RegisterOperation(operation2);
            var operationId3 = _monitor.RegisterOperation(operation3);

            _monitor.CompleteOperation(operationId1, true);
            _monitor.CompleteOperation(operationId2, false, "Test error");

            // Act
            _monitor.ClearCompletedOperations();

            // Assert
            Assert.That(_monitor.Operations.Count, Is.EqualTo(1));
            Assert.That(_monitor.Operations[0].Id, Is.EqualTo(operationId3));
        }

        [Test]
        public void StartMonitoring_ShouldSetIsMonitoringToTrue()
        {
            // Act
            _monitor.StartMonitoring();

            // Assert
            Assert.That(_monitor.IsMonitoring, Is.True);
        }

        [Test]
        public void StopMonitoring_ShouldSetIsMonitoringToFalse()
        {
            // Arrange
            _monitor.StartMonitoring();

            // Act
            _monitor.StopMonitoring();

            // Assert
            Assert.That(_monitor.IsMonitoring, Is.False);
        }

        [Test]
        public void MonitorCallback_ShouldCheckForStalledOperations()
        {
            // Arrange
            var operation = new FlashOperation(FlashOperationType.Write, 0x1000, 1024);
            var operationId = _monitor.RegisterOperation(operation);

            // Set the last updated time to more than 10 seconds ago
            var stalledOperation = _monitor.GetOperation(operationId);
            stalledOperation.LastUpdated = DateTime.Now.AddSeconds(-15);
            stalledOperation.Status = FlashOperationStatus.InProgress;

            // Start monitoring
            _monitor.StartMonitoring();

            // Act - wait for the monitor to check for stalled operations
            Thread.Sleep(2000);

            // Assert
            var checkedOperation = _monitor.GetOperation(operationId);
            Assert.That(checkedOperation.Status, Is.EqualTo(FlashOperationStatus.Stalled));
        }

        [Test]
        public void OperationEvents_ShouldBeRaised()
        {
            // Arrange
            var startedRaised = false;
            var updatedRaised = false;
            var completedRaised = false;
            var failedRaised = false;

            _monitor.OperationStarted += (sender, e) => startedRaised = true;
            _monitor.OperationUpdated += (sender, e) => updatedRaised = true;
            _monitor.OperationCompleted += (sender, e) => completedRaised = true;
            _monitor.OperationFailed += (sender, e) => failedRaised = true;

            var operation = new FlashOperation(FlashOperationType.Write, 0x1000, 1024);

            // Act
            var operationId = _monitor.RegisterOperation(operation);
            _monitor.UpdateOperationProgress(operationId, 512, FlashOperationStatus.InProgress);
            _monitor.CompleteOperation(operationId, true);

            var operation2 = new FlashOperation(FlashOperationType.Read, 0x2000, 512);
            var operationId2 = _monitor.RegisterOperation(operation2);
            _monitor.CompleteOperation(operationId2, false, "Test error");

            // Assert
            Assert.That(startedRaised, Is.True);
            Assert.That(updatedRaised, Is.True);
            Assert.That(completedRaised, Is.True);
            Assert.That(failedRaised, Is.True);
        }
    }
}

