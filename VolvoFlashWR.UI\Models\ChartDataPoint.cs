using System;

namespace VolvoFlashWR.UI.Models
{
    /// <summary>
    /// Represents a data point for charting
    /// </summary>
    public class ChartDataPoint
    {
        /// <summary>
        /// The timestamp of the data point
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// The value of the data point
        /// </summary>
        public double Value { get; set; }

        /// <summary>
        /// The parameter name associated with this data point
        /// </summary>
        public string ParameterName { get; set; } = string.Empty;

        /// <summary>
        /// Default constructor
        /// </summary>
        public ChartDataPoint()
        {
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// Constructor with parameter name and value
        /// </summary>
        /// <param name="parameterName">The name of the parameter</param>
        /// <param name="value">The value of the parameter</param>
        public ChartDataPoint(string parameterName, double value)
        {
            Timestamp = DateTime.Now;
            ParameterName = parameterName;
            Value = value;
        }

        /// <summary>
        /// Constructor with timestamp, parameter name and value
        /// </summary>
        /// <param name="timestamp">The timestamp of the data point</param>
        /// <param name="parameterName">The name of the parameter</param>
        /// <param name="value">The value of the parameter</param>
        public ChartDataPoint(DateTime timestamp, string parameterName, double value)
        {
            Timestamp = timestamp;
            ParameterName = parameterName;
            Value = value;
        }
    }
}
