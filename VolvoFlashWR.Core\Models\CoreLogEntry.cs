using System;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents a log entry in the core system
    /// </summary>
    public class CoreLogEntry
    {
        /// <summary>
        /// Gets or sets the timestamp of the log entry
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the log level
        /// </summary>
        public LogLevel Level { get; set; }

        /// <summary>
        /// Gets or sets the message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Gets or sets the source of the log entry
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// Gets or sets the exception details (if any)
        /// </summary>
        public string ExceptionDetails { get; set; }

        /// <summary>
        /// Creates a new instance of the CoreLogEntry class
        /// </summary>
        public CoreLogEntry()
        {
            Timestamp = DateTime.Now;
            Level = LogLevel.Information;
            Message = string.Empty;
            Source = string.Empty;
            ExceptionDetails = string.Empty;
        }

        /// <summary>
        /// Creates a new instance of the CoreLogEntry class with the specified parameters
        /// </summary>
        /// <param name="level">The log level</param>
        /// <param name="message">The message</param>
        /// <param name="source">The source</param>
        /// <param name="exceptionDetails">The exception details</param>
        public CoreLogEntry(LogLevel level, string message, string source = "", string exceptionDetails = "")
        {
            Timestamp = DateTime.Now;
            Level = level;
            Message = message ?? string.Empty;
            Source = source ?? string.Empty;
            ExceptionDetails = exceptionDetails ?? string.Empty;
        }
    }

    /// <summary>
    /// Represents the log level
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// Debug level
        /// </summary>
        Debug,

        /// <summary>
        /// Information level
        /// </summary>
        Information,

        /// <summary>
        /// Warning level
        /// </summary>
        Warning,

        /// <summary>
        /// Error level
        /// </summary>
        Error,

        /// <summary>
        /// Critical level
        /// </summary>
        Critical
    }
}
