Chapter 1
Device Overview MC9S12XE-Family
1.1
Introduction
The MC9S12XE-Family of micro controllers is a further development of the S12XD-Family including
new features for enhanced system integrity and greater functionality. These new features include a
Memory Protection Unit (MPU) and Error Correction Code (ECC) on the Flash memory together with
enhanced EEPROM functionality (EEE), an enhanced XGATE, an Internally filtered, frequency
modulated Phase Locked Loop (IPLL) and an enhanced ATD. The E-Family extends the S12X product
range up to 1MB of Flash memory with increased I/O capability in the 208-pin version of the flagship
MC9S12XE100.
The MC9S12XE-Family delivers 32-bit performance with all the advantages and efficiencies of a 16 bit
MCU. It retains the low cost, power consumption, EMC and code-size efficiency advantages currently
enjoyed by users of Freescale’s existing 16-Bit MC9S12 and S12X MCU families. There is a high level of
compatibility between the S12XE and S12XD families.
The MC9S12XE-Family features an enhanced version of the performance-boosting XGATE co-processor
which is programmable in “C” language and runs at twice the bus frequency of the S12X with an
instruction set optimized for data movement, logic and bit manipulation instructions and which can service
any peripheral module on the device. The new enhanced version has improved interrupt handling
capability and is fully compatible with the existing XGATE module.
The MC9S12XE-Family is composed of standard on-chip peripherals including up to 64Kbytes of RAM,
eight asynchronous serial communications interfaces (SCI), three serial peripheral interfaces (SPI), an 8channel IC/OC enhanced capture timer (ECT), two 16-channel, 12-bit analog-to-digital converters, an 8channel pulse-width modulator (PWM), five CAN 2.0 A, B software compatible modules (MSCAN12),
two inter-IC bus blocks (IIC), an 8-channel 24-bit periodic interrupt timer (PIT) and an 8-channel 16-bit
standard timer module (TIM).
The MC9S12XE-Family uses 16-bit wide accesses without wait states for all peripherals and memories.
The non-multiplexed expanded bus interface available on the 144/208-Pin versions allows an easy
interface to external memories.
In addition to the I/O ports available in each module, up to 26 further I/O ports are available with interrupt
capability allowing Wake-Up from STOP or WAIT modes. The MC9S12XE-Family is available in 208Pin MAPBGA, 144-Pin LQFP, 112-Pin LQFP or 80-Pin QFP options.

1.1.1
Features
Features of the MC9S12XE-Family are listed here. Please see Table D-2.for memory options and Table D2. for the peripheral features that are available on the different family members.
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
27
Chapter 1 Device Overview MC9S12XE-Family
•
•
•
•
•
•
•
•
•
•
16-Bit CPU12X
— Upward compatible with MC9S12 instruction set with the exception of five Fuzzy instructions
(MEM, WAV, WAVR, REV, REVW) which have been removed
— Enhanced indexed addressing
— Access to large data segments independent of PPAGE
INT (interrupt module)
— Eight levels of nested interrupts
— Flexible assignment of interrupt sources to each interrupt level.
— External non-maskable high priority interrupt (XIRQ)
— Internal non-maskable high priority Memory Protection Unit interrupt
— Up to 24 pins on ports J, H and P configurable as rising or falling edge sensitive interrupts
EBI (external bus interface)(available in 208-Pin and 144-Pin packages only)
— Up to four chip select outputs to select 16K, 1M, 2M and up to 4MByte address spaces
— Each chip select output can be configured to complete transaction on either the time-out of one
of the two wait state generators or the deassertion of EWAIT signal
MMC (module mapping control)
DBG (debug module)
— Monitoring of CPU and/or XGATE busses with tag-type or force-type breakpoint requests
— 64 x 64-bit circular trace buffer captures change-of-flow or memory access information
BDM (background debug mode)
MPU (memory protection unit)
— 8 address regions definable per active program task
— Address range granularity as low as 8-bytes
— No write / No execute Protection Attributes
— Non-maskable interrupt on access violation
XGATE
— Programmable, high performance I/O coprocessor module
— Transfers data to or from all peripherals and RAM without CPU intervention or CPU wait states
— Performs logical, shifts, arithmetic, and bit operations on data
— Can interrupt the HCS12X CPU signalling transfer completion
— Triggers from any hardware module as well as from the CPU possible
— Two interrupt levels to service high priority tasks
— Hardware support for stack pointer initialisation
OSC_LCP (oscillator)
— Low power loop control Pierce oscillator utilizing a 4MHz to 16MHz crystal
— Good noise immunity
— Full-swing Pierce option utilizing a 2MHz to 40MHz crystal
— Transconductance sized for optimum start-up margin for typical crystals
IPLL (Internally filtered, frequency modulated phase-locked-loop clock generation)
MC9S12XE-Family Reference Manual Rev. 1.25
28
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
•
•
•
•
•
•
•
— No external components required
— Configurable option to spread spectrum for reduced EMC radiation (frequency modulation)
CRG (clock and reset generation)
— COP watchdog
— Real time interrupt
— Clock monitor
— Fast wake up from STOP in self clock mode
Memory Options
— 128K, 256k, 384K, 512K, 768K and 1M byte Flash
— 2K, 4K byte emulated EEPROM
— 12K, 16K, 24K, 32K, 48K and 64K Byte RAM
Flash General Features
— 64 data bits plus 8 syndrome ECC (Error Correction Code) bits allow single bit failure
correction and double fault detection
— Erase sector size 1024 bytes
— Automated program and erase algorithm
D-Flash Features
— Up to 32 Kbytes of D-Flash memory with 256 byte sectors for user access.
— Dedicated commands to control access to the D-Flash memory over EEE operation.
— Single bit fault correction and double bit fault detection within a word during read operations.
— Automated program and erase algorithm with verify and generation of ECC parity bits.
— Fast sector erase and word program operation.
— Ability to program up to four words in a burst sequence
Emulated EEPROM Features
— Automatic EEE file handling using an internal Memory Controller.
— Automatic transfer of valid EEE data from D-Flash memory to buffer RAM on reset.
— Ability to monitor the number of outstanding EEE related buffer RAM words left to be
programmed into D-Flash memory.
— Ability to disable EEE operation and allow priority access to the D-Flash memory.
— Ability to cancel all pending EEE operations and allow priority access to the D-Flash memory.
Two 16-channel, 12-bit Analog-to-Digital Converters
— 8/10/12 Bit resolution
— 3µs, 10-bit single conversion time
— Left/right, signed/unsigned result data
— External and internal conversion trigger capability
— Internal oscillator for conversion in Stop modes
— Wake from low power modes on analog comparison > or <= match
Five MSCAN (1 M bit per second, CAN 2.0 A, B software compatible modules)
— Five receive and three transmit buffers
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
29
Chapter 1 Device Overview MC9S12XE-Family
•
•
•
•
•
•
•
•
— Flexible identifier filter programmable as 2 x 32 bit, 4 x 16 bit, or 8 x 8 bit
— Four separate interrupt channels for Rx, Tx, error, and wake-up
— Low-pass filter wake-up function
— Loop-back for self-test operation
ECT (enhanced capture timer)
— 8 x 16-bit channels for input capture or output compare
— 16-bit free-running counter with 8-bit precision prescaler
— 16-bit modulus down counter with 8-bit precision prescaler
— Four 8-bit or two 16-bit pulse accumulators
TIM (standard timer module)
— 8 x 16-bit channels for input capture or output compare
— 16-bit free-running counter with 8-bit precision prescaler
— 1 x 16-bit pulse accumulator
PIT (periodic interrupt timer)
— Up to eight timers with independent time-out periods
— Time-out periods selectable between 1 and 224 bus clock cycles
— Time-out interrupt and peripheral triggers
8 PWM (pulse-width modulator) channels
— 8 channel x 8-bit or 4 channel x 16-bit Pulse Width Modulator
— programmable period and duty cycle per channel
— Center- or left-aligned outputs
— Programmable clock select logic with a wide range of frequencies
— Fast emergency shutdown input
Three Serial Peripheral Interface Modules (SPI)
— Configurable for 8 or 16-bit data size
Eight Serial Communication Interfaces (SCI)
— Standard mark/space non-return-to-zero (NRZ) format
— Selectable IrDA 1.4 return-to-zero-inverted (RZI) format with programmable pulse widths
Two Inter-IC bus (IIC) Modules
— Multi-master operation
— Software programmable for one of 256 different serial clock frequencies
— Broadcast mode support
— 10-bit address support
On-Chip Voltage Regulator
— Two parallel, linear voltage regulators with bandgap reference
— Low-voltage detect (LVD) with low-voltage interrupt (LVI)
— Power-on reset (POR) circuit
— 3.3V and 5V range operation
— Low-voltage reset (LVR)
MC9S12XE-Family Reference Manual Rev. 1.25
30
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
•
•
•
•
1.1.2
Low-power wake-up timer (API)
— Available in all modes including Full Stop Mode
— Trimmable to +-5% accuracy
— Time-out periods range from 0.2ms to ~13s with a 0.2ms resolution
Input/Output
— Up to 152 general-purpose input/output (I/O) pins plus 2 input-only pins
— Hysteresis and configurable pull up/pull down device on all input pins
— Configurable drive strength on all output pins
Package Options
— 208-pin MAPBGA
— 144-pin low-profile quad flat-pack (LQFP)
— 112-pin low-profile quad flat-pack (LQFP)
— 80-pin quad flat-pack (QFP)
50MHz maximum CPU bus frequency, 100MHz maximum XGATE bus frequency
Modes of Operation
Memory map and bus interface modes:
• Normal and emulation operating modes
— Normal single-chip mode
— Normal expanded mode
— Emulation of single-chip mode
— Emulation of expanded mode
• Special Operating Modes
— Special single-chip mode with active background debug mode
— Special test mode (Freescale use only)
Low-power modes:
• System stop modes
— Pseudo stop mode
— Full stop mode with fast wake-up option
• System wait mode
Operating system states
• Supervisor state
• User state
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
31
Chapter 1 Device Overview MC9S12XE-Family
1.1.3
Block Diagram
Reset Generation
and Test Entry
EWAIT
PA[7:0]
PTA
ADDR[15:8]
PB[7:0]
PTB
ADDR[7:0]
PC[7:0]
PTC
ADDR[22:16]
DATA[15:8]
PD[7:0]
DATA[7:0]
PTF
CS0
CS1
CS2
CS3
SDA
SCL
RXD
TXD
PF0
PF1
PF2
PF3
PF4
PF5
PF6
PF7
INT
Enhanced Multilevel
Interrupt Module
MPU
XIRQ
IRQ
RW/WE
LSTRB/LDS
ECLK
MODA/TAGLO/RE
MODB/TAGHI
XCLKS/ECLKX2
PTD
PK[7:0]
PTK
PE0
PE1
PE2
PE3
PE4
PE5
PE6
PE7
PTE
TEST
Memory Protection
8 regions
Non-Multiplexed External Bus Interface
RESET
PWM
PIT
PWM[7:0]
8-bit 8 channel
Pulse Width Modulator
RXD
SCI0
TXD
Asynchronous Serial IF
RXD
SCI1
TXD
Asynchronous Serial IF
SPI0
MISO
Synchronous Serial IF
CAN0
msCAN 2.0B
CAN1
msCAN 2.0B
CAN2
msCAN 2.0B
CAN3
msCAN 2.0B
SCI4
Asynchronous Serial IF
SCI5
Asynchronous Serial IF
SCI6
Asynchronous Serial IF
SCI7
Asynchronous Serial IF
SCI2
Asynchronous Serial IF
MOSI
SCK
SS
MISO
MOSI
SCK
SS
MISO
MOSI
SCK
SS
RXCAN
TXCAN
RXCAN
TXCAN
RXCAN
TXCAN
RXCAN
TXCAN
RXD
TXD
RXD
TXD
RXD
TXD
RXD
TXD
RXD
TXD
IIC1
Inter IC Module
CAN4
msCAN 2.0B
SDA
SCL
RXCAN
TXCAN
Synchronous Serial IF
SPI1
Synchronous Serial IF
SPI2
8ch 16-bit Timer
IIC0
Inter IC Module
SCI3
Asynchronous Serial IF
Figure 1-1. MC9S12XE-Family
PTAD0
IPLL with Frequency
Modulation option
Clock Monitor
COP Watchdog
Periodic Interrupt
Async. Periodic Int.

IOC[7:0]
PTAD1
XTAL
Amplitude Controlled
Low Power Pierce or
Full drive Pierce
Oscillator
X
EXTAL
16-bit 8 channel
Timer
XGATE
BKGD
Debug Module
Single-wire Background 4 address breakpoints
Debug Module
2 data breakpoints
512 Byte Trace Buffer
PTT
CPU12X
PT[7:0]
PTR
IOC[7:0]
16-bit 8 channel
Enhanced Capture Timer
TIM
PR[7:0]
PP[7:0]
PTS
Voltage Regulator
PAD[31:16]
PTH (Wake-up Int)
8/10/12-bit 16-channel AN[15:0]
Analog-Digital Converter
ECT
PTM
2K … 4K bytes EEPROM
VDDR
VDD
VDDF
VDDPLL
PAD[15:0]
PTL
8/10/12-bit 16-channel AN[15:0]
Analog-Digital Converter
ATD1
12K … 64K bytes RAM
PTJ (Wake-up Int.)

ATD0
128K … 1M bytes Flash
PTP (Int)
Figure 1-1 shows a block diagram of the MC9S12XE-Family devices
PS0
PS1
PS2
PS3
PS4
PS5
PS6
PS7
PH0
PH1
PH2
PH3
PH4
PH5
PH6
PH7
PM0
PM1
PM2
PM3
PM4
PM5
PM6
PM7
PL0
PL1
PL2
PL3
PL4
PL5
PL6
PL7
PJ0
PJ1
PJ2
PJ3
PJ4
PJ5
PJ6
PJ7
Block Diagram
MC9S12XE-Family Reference Manual Rev. 1.25
32
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
1.1.4
Device Memory Map
Table 1-1 shows the device register memory map.
Table 1-1. Device Register Memory Map
Address
Module
Size
(Bytes)
0x0000–0x0009
PIM (port integration module)
10
0x000A–0x000B
MMC (memory map control)
2
0x000C–0x000D
PIM (port integration module)
2
0x000E–0x000F
EBI (external bus interface)
2
0x0010–0x0017
MMC (memory map control)
8
0x0018–0x0019
Reserved
2
0x001A–0x001B
Device ID register
2
0x001C–0x001F
PIM (port integration module)
4
0x0020–0x002F
DBG (debug module)
16
0x0030–0x0031
Reserved
2
0x0032–0x0033
PIM (port integration module)
2
0x0034–0x003F
ECRG (clock and reset generator)
12
0x0040–0x007F
ECT (enhanced capture timer 16-bit 8-channel)s
64
0x0080–0x00AF
ATD1 (analog-to-digital converter 12-bit 16-channel)
48
0x00B0–0x00B7
IIC1 (inter IC bus)
8
0x00B8–0x00BF
SCI2 (serial communications interface)
8
0x00C0–0x00C7
SCI3 (serial communications interface)
8
0x00C8–0x00CF
SCI0 (serial communications interface)
8
0x00D0–0x00D7
SCI1 (serial communications interface)
8
0x00D8–0x00DF
SPI0 (serial peripheral interface)
8
0x00E0–0x00E7
IIC0 (inter IC bus)
8
0x00E8–0x00EF
Reserved
8
0x00F0–0x00F7
SPI1 (serial peripheral interface)
8
0x00F8–0x00FF
SPI2 (serial peripheral interface)
8
0x0100–0x0113
FTM control registers
20
0x0114–0x011F
MPU (memory protection unit)
12
0x0120–0x012F
INT (interrupt module)
16
0x0130–0x0137
SCI4 (serial communications interface)
8
0x0138–0x013F
SCI5 (serial communications interface)
8
0x0140–0x017F
CAN0
64
0x0180–0x01BF
CAN1
64
0x01C0–0x01FF
CAN2
64
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
33
Chapter 1 Device Overview MC9S12XE-Family
Table 1-1. Device Register Memory Map (continued)
Address
Module
Size
(Bytes)
0x0200–0x023F
CAN3
64
0x0240–0x027F
PIM (port integration module)
64
0x0280–0x02BF
CAN4
64
0x02C0–0x02EF
ATD0 (analog-to-digital converter 12 bit 16-channel)
48
0x02F0–0x02F7
Voltage regulator
8
0x02F8–0x02FF
Reserved
8
0x0300–0x0327
PWM (pulse-width modulator 8 channels)
40
0x0328–0x032F
Reserved
8
0x0330–0x0337
SCI6 (serial communications interface)
8
0x0338–0x033F
SCI7 (serial communications interface)
8
0x0340–0x0367
PIT (periodic interrupt timer)
40
0x0368–0x037F
PIM (port integration module)
24
0x0380–0x03BF
XGATE
64
0x03C0–0x03CF
Reserved
16
0x03D0–0x03FF
TIM (timer module)
48
0x0400–0x07FF
Reserved
1024
NOTE
Reserved register space shown in Table 1-1 is not allocated to any module.
This register space is reserved for future use. Writing to these locations have
no effect. Read access to these locations returns zero.

1.1.5
Address Mapping
Figure 1-2 shows S12XE CPU & BDM local address translation to the global memory map. It indicates
also the location of the internal resources in the memory map.
EEEPROM size is presented like a fixed 256 KByte in the memory map.

MC9S12XE-Family Reference Manual Rev. 1.25
34
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
CPU and BDM
Local Memory Map
Global Memory Map
0x00_0000
0x00_07FF
2K REGISTERS
CS3
Unimplemented
RAM
0x0000
0x0800
0x0C00
0x1000
RAM
2K REGISTERS
1K EEPROM window
EPAGE
RAMSIZE
RAM_LOW
0x0F_FFFF
1K EEPROM
4K RAM window
RPAGE
0x2000
256 K EEEPROM
RESOURCES
8K RAM
0x4000
0x13_FFFF
CS2
Unpaged
16K FLASH
0x1F_FFFF
External
Space
CS1
0x8000
PPAGE
0x3F_FFFF
0xC000
CS0
16K FLASH window
Unimplemented
FLASH
Unpaged
16K FLASH
FLASH_LOW
Reset Vectors
FLASH
NOTE: On smaller derivatives the flash
memory map is split into 2 ranges separated
by an unimplemeted range, as depicted by
the dashed lines. For more information
refer to tables below and MMC section.

FLASHSIZE
0xFFFF
0x7F_FFFF
Figure 1-2. MC9S12XE100 Global Memory Map
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
35
Chapter 1 Device Overview MC9S12XE-Family
Unimplemented RAM pages are mapped externally in expanded modes. Accessing unimplemented RAM
pages in single chip modes causes an illegal address reset if the MPU is not configured to flag an MPU
protection error in that range.
Accessing unimplemented FLASH pages in single chip modes causes an illegal address reset if the MPU
is not configured to flag an MPU protection error in that range.
The PARTID value should be referenced regarding the specific memory map for any given device. For
devices sharing the same part ID, the memory regions which are implemented on the larger device but not
supported on the smaller device are implemented but untested on that smaller device. These regions do not
appear as unimplemented in the memory map and do not result in an illegal address reset if erroneously
accessed.
Table 1-2. Unimplemented Range Mapping to Part ID
Part ID
RAM_LOW
EE_LOW
Flash Blocks
Registers
0xCC8x
0x0F_0000
0x13_F000
B3, B2, B1S, B1N, B0
2K
0xCC9x
0x0F_0000
0x13_F000
B3, B2, B1S, B1N, B0
2K
0xC48x
0x0F_8000
0x13_F000
B1N, B1S, B0
2K
0xC08x
0x0F_C000
0x13_F000
B1S, B0(128K)
2K
From the above the following examples can be derived.
The 9S12XEP768 is currently only available as a 9S12XEP100 die, thus the unimplemented FLASH pages
are those of the 9S12XEP100 device map.
The 9S12XEQ384, 9S12XEG384, 9S12XES384 are currently only available as a 9S12XEQ512 die, thus
the unimplemented FLASH pages are those of the 9S12XEQ512 device map.
The 9S12XEG128 is currently only available as a 9S12XET256 die, thus the unimplemented FLASH
pages are those of the 9S12XET256 device map.
The range between 0x10_0000 and 0x13_FFFF is mapped to EEPROM resources. The actual EEPROM
and dataflash block sizes are listed in Table 1-4. Within EEPROM resource range an address range exists
which is neither used by EEPROM resources nor remapped to external resources via chip selects (see the
FTM/MMC descriptions for details). These ranges do not constitute unimplemented areas.
Accessing reserved registers within the 2K register space does not generate an illegal address reset.
The fixed 8K RAM default location in the global map is 0x0F_E000- 0x0F_FFFF. This is subject to
remapping when configuring the local address map for a larger RAM access range.

MC9S12XE-Family Reference Manual Rev. 1.25
36
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
Figure 1-3 shows XGATE local address translation to the global memory map. It indicates also the location
of used internal resources in the memory map.
Table 1-3. XGATE Resources
Internal Resource
Size /KByte
$Address
XGATE RAM
32K
XGRAM_LOW = 0x0F_8000
(1)
FLASH
30K
XGFLASH_HIGH = 0x78_8000
1. This value is calculated by the following formula: (64K -2K- XGRAMSIZE)
Table 1-4. Derivative Dependent Memory Parameters
Device
FLASH_LOW
PPAGE
(1)
RAM_LOW
RPAGE
(2)
EE_LOW
EPAGE
9S12XEP100
0x70_0000
64
0x0F_0000
16
0x13_F000
4(3) + 32(4)
9S12XEP768
0x74_0000
48
0x0F_4000
12
0x13_F000
4 + 32
9S12XEQ512
0x78_0000
32
0x0F_8000
8
0x13_F000
4 + 32
9S12XEx384
0x78_0000(5)
24
0x0F_A000
6
0x13_F000
4 + 32
9S12XET256
9S12XEA256
0x78_0000(7)
16
0x0F_C000
4
0x13_F000
4 + 32
0x78_0000(8)
8
0x0F_D000
3
0x13_F800
2 + 32
(6)
9S12XEG128
9S12XEA1286
1. Number of 16K pages addressable via PPAGE register
2. Number of 4K pages addressing the RAM. RAM can also be mapped to 0x4000 - 0x7FFF
3. Number of 1K pages addressing the Cache RAM via the EPAGE register counting downwards from 0xFF
4. Number of 1K pages addressing the Data flash via the EPAGE register starting upwards from 0x00
5. The 384K memory map is split into a 128K block from 0x78_0000 to 0x79_FFFF and a 256K block from
0x7C_0000 to 0x7F_FFFF
6. The 9S12XEA devices are a special bondout for access to extra ADC channels in 80QFP.
Available in 80QFP only. WARNING: NOT PIN-COMPATIBLE WITH REST OF FAMILY.
7. The 256K memory map is split into a 128K block from 0x78_0000 to 0x79_FFFF and a 128K block from
0x7E_0000 to 0x7F_FFFF
8. The 128K memory map is split into a 64K block from 0x78_0000 to 0x78_FFFF and a 64K block from
0x7F_0000 to 0x7F_FFFF
Table 1-5. Derivative Dependent Flash Block Mapping
Device
0x70_0000
0x74_0000
0x78_0000
0x7A_0000
0x7C_0000
0x7E_0000
9S12XEP100
B3
B2
B1S
B1N
B0
9S12XEP768
—
B2
B1S
B1N
B0
9S12XEQ512
—
—
B1S
B1N
B0
9S12XEx384
—
—
B1S
—
B0
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
37
Chapter 1 Device Overview MC9S12XE-Family
Table 1-5. Derivative Dependent Flash Block Mapping (continued)
Device
0x70_0000
0x74_0000
9S12XET256
9S12XEA256
—
—
0x78_0000
0x7A_0000
0x7C_0000
—
—
B1S
0x7E_0000
B0(128K)
(1)
9S12XEG128
9S12XEA1281
—
—
B1S (64K)
—
—
B0 (64K)
1. The 9S12XEA devices are special bondouts for access to extra ADC channels in 80QFP.
Available in 80QFP only. WARNING: NOT PIN-COMPATIBLE WITH REST OF FAMILY.

Block B1 is divided into two 128K blocks. The XGATE is always mapped to block B1S.
On the 9S12XEG128 the flash is divided into two 64K blocks B0 and B1S, the B1S range extending from
0x78_0000 to 0x78_FFFF, the B0 range extending from 0x7F_0000 to 0x7F_FFFF.
The block B0 is a reduced size 128K block on the 256K derivative. On the larger derivatives B0 is a 256K
block. The block B0 is a reduced size 64K block on the 128K derivative.

MC9S12XE-Family Reference Manual Rev. 1.25
38
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
XGATE
Local Memory Map
Global Memory Map
0x00_0000
Registers
0x00_07FF
XGRAM_LOW
0x0800
RAM
0x0F_FFFF
RAMSIZE
Registers
XGRAMSIZE
0x0000
FLASH
RAM
0x78_0800
0xFFFF
FLASHSIZE
FLASH
XGFLASH_HIGH
0x7F_FFFF
Figure 1-3. XGATE Global Address Mapping
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
39
Chapter 1 Device Overview MC9S12XE-Family
1.1.6
Detailed Register Map
The detailed register map is listed in Appendix A.

1.1.7
Part ID Assignments
The part ID is located in two 8-bit registers PARTIDH and PARTIDL (addresses 0x001A and 0x001B).
The read-only value is a unique part ID for each revision of the chip. Table 1-6 shows the assigned part ID
number and Mask Set number.

MC9S12XE-Family Reference Manual Rev. 1.25
40
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
The Version ID is a word located in a flash information row at 0x40_00E8. The version ID number
indicates a specific version of internal NVM variables used to patch NVM errata.
The default is no patch (0xFFFF).
Table 1-6. Assigned Part ID Numbers
Device
Mask Set Number
Part ID(1)
Version ID
MC9S12XEP100
0M22E
0xCC80
0xFFFF
MC9S12XEP100
1M22E
0xCC80
0xFFFF
MC9S12XEP100
2M22E
0xCC82
0xFFFF
MC9S12XEP100
0M48H
0xCC90
0xFFFF
MC9S12XEP100
1M48H
0xCC91
0xFFFF
MC9S12XEP100
2M48H
0xCC92
0xFFFF
MC9S12XEP100
3M48H
0xCC93
0xFFFF
(2)
4M48H
0xCC94
0xFFFF
2
MC9S12XEP100, MC9S12XEP768
5M48H
0xCC94
0x0004
MC9S12XEP100, MC9S12XEP7682
0N35H
0xCC95
0xFFFF
MC9S12XEP100, MC9S12XEP7682
1N35H
0xCC95
0x0004
MC9S12XEQ512
0M25J
0xC480
0xFFFF
MC9S12XEP100, MC9S12XEP768
MC9S12XEQ512
1M25J
0xC481
0xFFFF
MC9S12XEQ512, MC9S12XET512
2M25J
0xC482
0xFFFF
MC9S12XEQ512, MC9S12XET512
3M25J
0xC482
0x0004
2M25J
0xC482
0xFFFF
MC9S12XEQ384
(3),
MC9S12XEQ3843,
MC9S12XEG3843,
MC9S12XEG3843,
MC9S12XES3843
MC9S12XES3843
3M25J
0xC482
0x0004
MC9S12XEQ512, MC9S12XET512
0M12S
0xC483
0xFFFF
MC9S12XEQ512, MC9S12XET512
1M12S
0xC483
0x0004
3,
MC9S12XEG3843,
MC9S12XES3843
0M12S
0xC483
0xFFFF
3,
MC9S12XEG3843,
MC9S12XES3843
MC9S12XEQ384
1M12S
0xC483
0x0004
MC9S12XET256, MC9S12XEG256
0M53J
0xC080
0xFFFF
MC9S12XET256, MC9S12XEG256, MC9S12XEA256
1M53J
0xC081
0xFFFF
MC9S12XET256, MC9S12XEG256, MC9S12XEA256
2M53J
0xC081
0x0004
MC9S12XEQ384
(4),
MC9S12XEA1284
1M53J
0xC081
0xFFFF
MC9S12XEG1284, MC9S12XEA1284
2M53J
0xC081
0x0004
MC9S12XET256, MC9S12XEG256, MC9S12XEA256
0N36H
0xC082
0xFFFF
MC9S12XET256, MC9S12XEG256, MC9S12XEA256
1N36H
0xC082
0x0004
0N36H
0xC082
0xFFFF
1N36H
0xC082
0x0004
MC9S12XEG128
MC9S12XEG128
4,
MC9S12XEA1284
MC9S12XEG1284, MC9S12XEA1284
1. The coding is as follows:
Bit 15-12: Major family identifier
Bit 11-6: Minor family identifier
Bit 5-4: Major mask set revision number including FAB transfers
Bit 3-0: Minor — non full — mask set revision
2. Currently available as MC9S12XEP100 die only
3. Currently available as MC9S12XEQ512 die only
4. Currently available as MC9S12XET256 die only
1.2
Signal Description
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
41
Chapter 1 Device Overview MC9S12XE-Family
This section describes signals that connect off-chip. It includes a pinout diagram, a table of signal
properties, and detailed discussion of signals. It is built from the signal description sections of the Block
User Guides of the individual IP blocks on the device.

1.2.1
Device Pinout
The MC9S12XE-Family offers pin-compatible packaged devices to assist with system development and
accommodate expansion of the application.
NOTE
Smaller derivatives within the MC9S12XE-Family feature a subset of the
listed modules. Refer to Appendix D Derivative Differences for more
information about derivative device module subset and to Table 1-7. Port
Availability by Package Option and Table 1-9. Pin-Out Summary for
details of pins available in different package options.
The MC9S12XE-Family devices are offered in the following package options:
• 208-pin MAPBGA package with an external bus interface (address/data bus)
• 144-pin LQFP package with an external bus interface (address/data bus)
• 112-pin LQFP without external bus interface
• 80-pin QFP without external bus interface
MC9S12XE-Family Reference Manual Rev. 1.25
42
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
1
2
3
4
5
6
7
8
9
10
11
12
A
N.C.

N.C.

PP7
PM0
PM1
PF5
PF3
PF1
PJ6
PS6
PS5
PS3
B
N.C.

PP2
PP6
PF7
PF6
PF4
PF2
PF0
C
PJ2
PP1
PP4
PP5
PK7
PM2
PM4
PJ5
PS7
D
PK1
PJ3
PP0
PP3
VDDX
PM3
PM5
PJ4
PJ7 VDDX PS0 PAD22 VRH PAD17 PAD30 PAD29
E
PK0
PK3
PK2
PK6
VSSA PAD15 PAD06 PAD28
F
PR1
PR0
PT0
VDDX
VDDA PAD05 PAD13 PAD27
G
PT2
PT3
PR2
PT1
VSSX VSSX VSSX VSSX
VDDA PAD12 PAD04 PAD11
H
PR3
PR4
PT4
VDDF
VSSX VSSX VSSX VSSX
VSSA PAD26 PAD03 PAD10
J
PT5
PR5
PT6
VSS1
VSSX VSSX VSSX VSSX
VSS2 PAD09 PAD25 PAD02
K
PR6
PT7
PK4
PR7
VSSX VSSX VSSX VSSX
L
PK5
PJ1
M
PJ0
PC0
PB1
PC1
N
PC2
PC3
PB2
PC7
PL1
PE6
P
PB0
PB3
PB4
PC4
PL2
R
N.C.

PB5
PB6
PB7
T
N.C.

N.C.

PC5
PL3
TEST PS4
PS2
14
15
PM6 PAD19 N.C.

16
N.C.

PS1 PAD23 PAD21 PAD18 PAD31 N.C.
PM7 PAD20 VRL PAD16 PAD07 PAD14
BKGD VDDX
VDDX VDDR
13
VDD
PD7 PAD24 PAD01
VDDX
PD4 PAD00 PAD08
PA6
PA2
PD5
PD6
VSS3
PH3
PH1
VDDX
PE1
PA1
PA5
PA7
PL0
PE4 RESET PL7
PL6
PH0
PE2
PE0
PA0
PA3
PA4
PC6
PH6
PH4
PE5
VSS
PLL
VDD
PLL
PH2
PL4
PD1
PD3
PE3
N.C.

PH7
PH5
PE7
VSS
EXTAL XTAL
PLL
VDD
PLL
PL5
PD0
PD2
N.C.

N.C.

Figure 1-4. - Pin Assignments, 208 MAPBGA Package
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
43
144
143
142
141
140
139
138
137
136
135
134
133
132
131
130
129
128
127
126
125
124
123
122
121
120
119
118
117
116
115
114
113
112
111
110
109
PP4/KWP4/PWM4/MISO2/TIMIOC4
PP5/KWP5/PWM5/MOSI2/TIMIOC5
PP6/KWP6/PWM6/SS2/TIMIOC6
PP7/KWP7/PWM7/SCK2/TIMIOC7
PK7/ROMCTL/EWAIT
VDDX1
VSSX1
PM0/RXCAN0
PM1/TXCAN0
PM2/RXCAN1/RXCAN0/MISO0
PM3/TXCAN1/TXCAN0/SS0
PM4/RXCAN2/RXCAN0/RXCAN4/MOSI0
PM5/TXCAN2/TXCAN0/TXCAN4/SCK0
PJ4/KWJ4/SDA1/CS0
PJ5/KWJ5/SCL1/CS2
PJ6/KWJ6/RXCAN4/SDA0/RXCAN0
PJ7/KWJ7/TXCAN4/SCL0/TXACAN0
TEST
PS7/SS0
PS6/SCK0
PS5/MOSI0
PS4/MISO0
PS3/TXD1
PS2/RXD1
PS1/TXD0
PS0/RXD0
PM6/RXCAN3/RXCAN4/RXD3
PM7/TXCAN3/TXCAN4/TXD3
PAD23/AN23
PAD22/AN22
PAD21/AN21
PAD20/AN20
PAD19/AN19
PAD18/AN18
VSSA1
VRL
Chapter 1 Device Overview MC9S12XE-Family
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
MC9S12XE-Family
144 LQFP
Pins shown in BOLD-ITALICS neither available on the 112 LQFP
nor on the 80 QFP Package Option
Pins shown in BOLD are not available on the 80 QFP package
108
107
106
105
104
103
102
101
100
99
98
97
96
95
94
93
92
91
90
89
88
87
86
85
84
83
82
81
80
79
78
77
76
75
74
73
VRH
VDDA1
PAD17/AN17
PAD16/AN16
PAD15/AN15
PAD07/AN07
PAD14/AN14
PAD06/AN06
PAD13/AN13
PAD05/AN05
PAD12/AN12
PAD04/AN04
PAD11/AN11
PAD03/AN03
PAD10/AN10
PAD02/AN02
PAD09/AN09
PAD01/AN01
PAD08/AN08
PAD00/AN00
VSS2
VDD
PD7/DATA7
PD6/DATA6
PD5/DATA5
PD4/DATA4
VDDX3
VSSX3
PA7/ADDR15
PA6/ADDR14
PA5/ADDR13
PA4/ADDR12
PA3/ADDR11
PA2/ADDR10
PA1/ADDR9
PA0/ADDR8
ADDR5/PB5
ADDR6/PB6
ADDR7/PB7
DATA12/PC4
DATA13/PC5
DATA14/PC6
DATA15/PC7
TXD5/SS2/KWH7/PH7
RXD5/SCK2/KWH6/PH6
TXD4/MOSI2/KWH5/PH5
RXD4/MISO2/KWH4/PH4
XCLKS/ECLK2X/PE7
TAGHI/MODB/PE6
RE/TAGLO/MODA/PE5
ECLK/PE4
VSSX2
VDDX2
RESET
VDDR
VSS3
VSSPLL
EXTAL
XTAL
VDDPLL
TXD7/SS1/KWH3/PH3
RXD7/SCK1/KWH2/PH2
TXD6/MOSI1/KWH1/PH1
RXD6/MISO1/KWH0/PH0
DATA0/PD0
DATA1/PD1
DATA2/PD2
DATA3/PD3
EROMCTL/LDS/LSTRB/PE3
WE/RW/PE2
IRQ/PE1
XIRQ/PE0
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
TIMIOC3/SS1/PWM3/KWP3/PP3
TIMIOC2/SCK1/PWM2/KWP2/PP2
TIMIOC1/MOSI1/PWM1/KWP1/PP1
TIMIOC0/MISO1/PWM0/KWP0/PP0
CS1/KWJ2/PJ2
ACC/ADDR22/PK6
ADDR19/PK3
IQSTAT2/ADDR18/PK2
IQSTAT1/ADDR17/PK1
IQSTAT0/ADDR16/PK0
IOC0/PT0
IOC1/PT1
IOC2/PT2
IOC3/PT3
VDDF
VSS1
IOC4/PT4
VREGAPI/IOC5/PT5
IOC6/PT6
IOC7/PT7
ACC/ADDR21/PK5
ACC/ADDR20/PK4
TXD2/KWJ1/PJ1
RXD2/KWJ0/PJ0
MODC/BKGD
VDDX4
VSSX4
DATA8/PC0
DATA9/PC1
DATA10/PC2
DATA11/PC3
UDS/ADDR0/PB0
ADDR1/PB1
ADDR2/PB2
ADDR3/PB3
ADDR4/PB4
Figure 1-5. MC9S12XE-Family Pin Assignments 144-pin LQFP Package
MC9S12XE-Family Reference Manual Rev. 1.25
44
Freescale Semiconductor
84
83
82
81
80
79
78
77
76
75
74
73
72
71
70
69
68
Pins shown in BOLD are not available on the 80 QFP package 67
66
65
64
63
62
61
60
59
58
57
MC9S12XE-Family
112LQFP
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
VRH
VDDA1
PAD15/AN15
PAD07/AN07
PAD14/AN14
PAD06/AN06
PAD13/AN13
PAD05/AN05
PAD12/AN12
PAD04/AN04
PAD11/AN11
PAD03/AN03
PAD10/AN10
PAD02/AN02
PAD09/AN09
PAD01/AN01
PAD08/AN08
PAD00/AN00
VSS2
VDD
PA7
PA6
PA5
PA4
PA3
PA2
PA1
PA0
PB5
PB6
PB7
TXD5/SS2/KWH7/PH7
RXD5/SCK2/KWH6/PH6
TXD4/MOSI2/KWH5/PH5
RXD4/MISO2/KWH4/PH4
ECLK2X/XCLKS/PE7
MODB/PE6
MODA/PE5
ECLK/PE4
VSSX2
VDDX2
RESET
VDDR
VSS3
VSSPLL
EXTAL
XTAL
VDDPLL
TXD7/SS1/KWH3/PH3
RXD7/SCK1/KWH2/PH2
TXD6/MOSI1/KWH1/PH1
RXD6/MISO1/KWH0/PH0
PE3
PE2
IRQ/PE1
XIRQ/PE0
TIMIOC3/SS1/PWM3/KWP3/PP3
TIMIOC2/SCK1/PWM2/KWP2/PP2
TIMIOC1/MOSI1/PWM1/KWP1/PP1
TIMIOC0/MISO1/PWM0/KWP0/PP0
PK3
PK2
PK1
PK0
IOC0/PT0
IOC1/PT1
IOC2/PT2
IOC3/PT3
VDDF
VSS1
IOC4/PT4
VREGAPI/IOC5/PT5
IOC6/PT6
IOC7/PT7
PK5
PK4
TXD2/KWJ1/PJ1
RXD2/KWJ0/PJ0
MODC/BKGD
PB0
PB1
PB2
PB3
PB4
112
111
110
109
108
107
106
105
104
103
102
101
100
99
98
97
96
95
94
93
92
91
90
89
88
87
86
85
PP4/KWP4/PWM4/MISO2/TIMIOC4
PP5/KWP5/PWM5/MOSI2/TIMIOC5
PP6/KWP6/PWM6/SS2/TIMIOC6
PP7/KWP7/PWM7/SCK2/TIMIOC7
PK7
VDDX1
VSSX1
PM0/RXCAN0
PM1/TXCAN0
PM2/RXCAN1/RXCAN0/MISO0
PM3/TXCAN1/TXCAN0/SS0
PM4/RXCAN2/RXCAN0/RXCAN4/MOSI0
PM5/TXCAN2/TXCAN0/TXCAN4/SCK0
PJ6/KWJ6/RXCAN4/SDA0/RXCAN0
PJ7/KWJ7/TXCAN4/SCL0/TXCAN0
TEST
PS7/SS0
PS6/SCK0
PS5/MOSI0
PS4/MISO0
PS3/TXD1
PS2/RXD1
PS1/TXD0
PS0/RXD0
PM6/RXCAN3/RXCAN4/RXD3
PM7/TXCAN3/TXCAN4/TXD3
VSSA1
VRL
Chapter 1 Device Overview MC9S12XE-Family
Figure 1-6. MC9S12XE-Family Pin Assignments 112-pin LQFP Package
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
45
80
79
78
77
76
75
74
73
72
71
70
69
68
67
66
65
64
63
62
61
PP4/KWP4/PWM4/MISO2/TIMIOC4
PP5/KWP5/PWM5/MOSI2/TIMIOC5
PP7/KWP7/PWM7/SCK2/TIMIOC7
VDDX1
VSSX1
PM0/RXCAN0
PM1/TXCAN0
PM2/RXCAN1/RXCAN0/MISO0
PM3/TXCAN1/TXCAN0/SS0
PM4/RXCAN2/RXCAN0/RXCAN4/MOSI0
PM5/TXCAN2/TXCAN0/TXCAN4/SCK0
PJ6/KWJ6/RXCAN4/SDA0/RXCAN0
PJ7/KWJ7/TXCAN4/SCL0/TXCAN0
TEST
PS3/TXD1
PS2/RXD1
PS1/TXD0
PS0/RXD0
VSSA1
VRL
Chapter 1 Device Overview MC9S12XE-Family
60
59
58
57
56
55
54
53
52
51
50
49
48
47
46
45
44
43
42
41
MC9S12XE-Family
80QFP
VRH
VDDA1
PAD07/AN07
PAD06/AN06
PAD05/AN05
PAD04/AN04
PAD03/AN03
PAD02/AN02
PAD01/AN01
PAD00/AN00
VSS2
VDD
PA7
PA6
PA5
PA4
PA3
PA2
PA1
PA0
XIRQ/PE0
IRQ/PE1
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
PB5
PB6
PB7
ECLK2X/XCLKS/PE7
MODB/PE6
MODA/PE5
ECLK/PE4
VSSX2
VDDX2
RESET
VDDR
VSS3
VSSPLL
EXTAL
XTAL
VDDPLL
PE3
PE2
TIMIOC3/SS1/PWM3/KWP3/PP3
TIMIOC2/SCK1/PWM2/KWP2/PP2
TIMIOC1/MOSI1/PWM1/KWP1/PP1
TIMIOC0/MISO1/PWM0/KWP0/PP0
IOC0/PT0
IOC1/PT1
IOC2/PT2
IOC3/PT3
VDDF
VSS1
IOC4/PT4
VREGAPI/IOC5/PT5
IOC6/PT6
IOC7/PT7
MODC/BKGD
PB0
PB1
PB2
PB3
PB4
Figure 1-7. MC9S12XE-Family Pin Assignments 80-pin QFP Package
MC9S12XE-Family Reference Manual Rev. 1.25
46
Freescale Semiconductor
80
79
78
77
76
75
74
73
72
71
70
69
68
67
66
65
64
63
62
61
PP4/KWP4/PWM4/MISO2/TIMIOC4
PP5/KWP5/PWM5/MOSI2/TIMIOC5
PP7/KWP7/PWM7/SCK2/TIMIOC7
VDDX1
VSSX1
PM0/RXCAN0
PM1/TXCAN0
PM2/RXCAN1/RXCAN0/MISO0
PM3/TXCAN1/TXCAN0/SS0
PM4/RXCAN2/RXCAN0/RXCAN4/MOSI0
PM5/TXCAN2/TXCAN0/TXCAN4/SCK0
PJ6/KWJ6/RXCAN4/SDA0/RXCAN0
PJ7/KWJ7/TXCAN4/SCL0/TXCAN0
TEST
PS3/TXD1
PS2/RXD1
PS1/TXD0
PS0/RXD0
VSSA1
VRL
Chapter 1 Device Overview MC9S12XE-Family
60
59
58
57
56
55
54
53
52
51
50
49
48
47
46
45
44
43
42
41
MC9S12XEA256
MC9S12XEA128
80QFP
VRH
VDDA1
PAD07/AN07
PAD06/AN06
PAD05/AN05
PAD04/AN04
PAD11/AN11
PAD03/AN03
PAD10/AN10
PAD02/AN02
PAD09/AN09
PAD01/AN01
PAD08/AN08
PAD00/AN00
VSS2
VDD
PA3
PA2
PA1
PA0
XIRQ/PE0
IRQ/PE1
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
PB5
PB6
PB7
ECLK2X/XCLKS/PE7
MODB/PE6
MODA/PE5
ECLK/PE4
VSSX2
VDDX2
RESET
VDDR
VSS3
VSSPLL
EXTAL
XTAL
VDDPLL
PE3
PE2
TIMIOC3/SS1/PWM3/KWP3/PP3
TIMIOC2/SCK1/PWM2/KWP2/PP2
TIMIOC1/MOSI1/PWM1/KWP1/PP1
TIMIOC0/MISO1/PWM0/KWP0/PP0
IOC0/PT0
IOC1/PT1
IOC2/PT2
IOC3/PT3
VDDF
VSS1
IOC4/PT4
VREGAPI/IOC5/PT5
IOC6/PT6
IOC7/PT7
MODC/BKGD
PB0
PB1
PB2
PB3
PB4
Figure 1-8. MC9S12XEA256/MC9S12XEA128 80-pin QFP Package Pin Assignment
NOTE
SPECIAL BOND-OUT TO PROVIDE ACCESS TO EXTRA ADC
CHANNELS IN 80QFP. WARNING: NOT PIN-COMPATIBLE WITH
REST OF FAMILY. THE MC9S12XET256 AND MC9S12XEG128 USE
THE STANDARD 80QFP BOND-OUT, COMPATIBLE WITH OTHER
FAMILY MEMBERS.

MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
47
Chapter 1 Device Overview MC9S12XE-Family
1.2.2
Pin Assignment Overview
Table 1-7 provides a summary of which Ports are available for each package option.
Routing of pin functions is summarized in Table 1-8.
Table 1-9 provides a pin out summary listing the availability of individual pins for each package option.

MC9S12XE-Family Reference Manual Rev. 1.25
48
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
Table 1-10 provides a list of individual pin functionality
Table 1-7. Port Availability by Package Option
Port
208
MAPBGA
144 LQFP
112 LQFP
Standard
80 QFP
XEA256(1)
80 QFP
Port AD/ADC Channels
32/32
24/24
16/16
8/8
12/12
Port A pins
8
8
8
8
4
Port B pins
8
8
8
8
8
Port C pins
8
8
0
0
0
Port D pins
8
8
0
0
0
Port E pins inc. IRQ/XIRQ input only
8
8
8
8
8
Port F
8
0
0
0
0
Port H
8
8
8
0
0
Port J
8
7
4
2
2
Port K
8
8
7
0
0
Port L
8
0
0
0
0
Port M
8
8
8
6
6
Port P
8
8
8
7
7
Port R
8
0
0
0
0
Port S
8
8
8
4
4
Port T
8
8
8
8
8
Sum of Ports
152
119
91
59
59
I/O Power Pairs VDDX/VSSX
7/7
4/4
2/2
2/2
2/2
1. The 9S12XEA256 is a special bondout for access to extra ADC channels in 80QFP.
Available in 80QFP / 256K memory size only. WARNING: NOT PIN-COMPATIBLE WITH REST OF FAMILY.
The 9S12XET256 is the standard 256K/80QFP bondout, compatible with other family members.

MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
49
Chapter 1 Device Overview MC9S12XE-Family
PF[0]
TIM
CS3
CS2
CS1
CS0
IIC1
IIC0
SPI2
SPI1
SPI0
SCI7
SCI6
SCI5
SCI4
SCI3
SCI2
SCI1
SCI0
CAN4
CAN3
CAN2
CAN1
CAN0
Table 1-8. Peripheral - Port Routing Options(1)
X
PF[1]
X
PF[2]
X
PF[3]
X
PF[5:4]
X
PF[7:6]
X
PH[1:0]
O
PH[3:2]
X
O
PH[5:4]
X
O
PH[7:6]
X
O
PJ[0]
O
PJ[1]
O
X
O
PJ[2]
O
PJ[3]
PJ[4]
O
PJ[5]
O
PJ[7:6]
X
O
X
PL[3:2]
X
PL[5:4]
X
PL[7:6]
X
PM[1:0]
O
PM[3:2]
X
PM[5:4]
X
PP[3:0]
O
O
PL[1:0]
PM[7:6]
O
O
X
O
X
O
X
X
O
O
PP[7:4]
X
O
PR[7:0]
X
O
MC9S12XE-Family Reference Manual Rev. 1.25
50
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
PS[1:0]
TIM
CS3
CS2
CS1
CS0
IIC1
IIC0
SPI2
SPI1
SPI0
SCI7
SCI6
SCI5
SCI4
SCI3
SCI2
SCI1
SCI0
CAN4
CAN3
CAN2
CAN1
CAN0
Table 1-8. Peripheral - Port Routing Options(1) (continued)
O
PS[3:2]
O
PS[7:4]
O
1. “O” denotes reset condition, “X” denotes a possible rerouting under software control
Table 1-9. Pin-Out Summary (Sheet 1 of 7)
LQFP
144
LQFP
112
QFP(1)
80
D4
1
1
1
PP3
KWP3
PWM3
SS1
TIMIOC3
B2
2
2
2
PP2
KWP2
PWM2
SCK1
TIMIOC2
C2
3
3
3
PP1
KWP1
PWM1
MOSI1
TIMIOC1
D3
4
4
4
PP0
KWP0
PWM0
MISO1
TIMIOC0
PJ3
KWJ3
208
MAPBGA
D2
Pin
2nd
Func.

3rd
Func.

C1
5
PJ2
KWJ2
CS1
E4
6
PK6
ADDR22
ACC2
E2
7
5
PK3
ADDR19
IQSTAT3
E3
8
6
PK2
ADDR18
IQSTAT2
D1
9
7
PK1
ADDR17
IQSTAT1
E1
10
8
PK0
ADDR16
IQSTAT0
VDDX
VDDX7
VSSX
VSSX7
F3
11
9
5
F2
G4
12
10
6
F1
G1
13
11
7
G3
G2
14
12
8
H1
H4
15
13
9
PT0
IOC0
PR0
TIMIOC0
PT1
IOC1
PR1
TIMIOC1
PT2
IOC2
PR2
TIMIOC2
PT3
IOC3
PR3
TIMIOC3
4th
Func.

5th
Func.

VDDF
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
51
Chapter 1 Device Overview MC9S12XE-Family
Table 1-9. Pin-Out Summary (Sheet 2 of 7)
LQFP
144
LQFP
112
QFP(1)
80
J4
16
14
10
VSS1
H3
17
15
11
PT4
IOC4
PR4
TIMIOC4
PT5
IOC5
PR5
TIMIOC5
PT6
IOC6
PR6
TIMIOC6
PT7
IOC7
PR7
TIMIOC7
208
MAPBGA
H2
J1
18
16
12
J2
J3
19
17
13
K1
K2
20
18
14
K4
Pin
2nd
Func.

3rd
Func.

4th
Func.

VREGAPI
L1
21
19
PK5
ADDR21
ACC1
K3
22
20
PK4
ADDR20
ACC0
L2
23
21
PJ1
KWJ1
TXD2
M1
24
22
PJ0
KWJ0
RXD2
CS3
L3
25
23
BKGD
MODC
VDDX
26
VDDX4
VSSX
27
VSSX4
M2
28
PC0
DATA8
M4
29
PC1
DATA9
N1
30
PC2
DATA10
N2
31
PC3
DATA11
P1
32
24
16
PB0
ADDR0
IVD0
UDS
M3
33
25
17
PB1
ADDR1
IVD1
N3
34
26
18
PB2
ADDR2
IVD2
P2
35
27
19
PB3
ADDR3
IVD3
P3
36
28
20
PB4
ADDR4
IVD4
R2
37
29
21
PB5
ADDR5
IVD5
R3
38
30
22
PB6
ADDR6
IVD6
R4
39
31
23
PB7
ADDR7
IVD7
P4
40
PC4
DATA12
15
5th
Func.

MC9S12XE-Family Reference Manual Rev. 1.25
52
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
Table 1-9. Pin-Out Summary (Sheet 3 of 7)
208
MAPBGA
LQFP
144
LQFP
112
QFP(1)
80
Pin
2nd
Func.

T3
41
PC5
DATA13
R5
42
PC6
DATA14
N4
43
PC7
DATA15
PL3
TXD5
PH7
KWH7
PL2
RXD5
PH6
KWH6
PL1
TXD4
PH5
KWH5
PL0
RXD4
PH4
T4
T5
44
32
P5
R6
45
33
N5
T6
46
34
P6
3rd
Func.

4th
Func.

SS2
TXD5
SCK2
RXD5
MOSI2
TXD4
KWH4
MISO2
RXD4
R7
47
35
T7
48
36
24
PE7
XCLKS
ECLKX2
N6
49
37
25
PE6
MODB
TAGHI
R8
50
38
26
PE5
MODA
TAGLO
RE
P7
51
39
27
PE4
ECLK
VSSX
52
40
28
VSSX2
VDDX
53
41
29
VDDX2
P8
54
42
30
RESET
N8
55
43
31
VDDR
N9
56
44
32
VSS3
R9/T8
57
45
33
VSSPLL
T9
58
46
34
EXTAL
T10
59
47
35
XTAL
R10/T11
60
48
36
VDDPLL
SS1
TXD7
SCK1
RXD7
P9
N10
61
49
P10
R11
62
T12
50
PL7
TXD7
PH3
KWH3
PL6
RXD7
PH2
KWH2
PL5
TXD6
5th
Func.

MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
53
Chapter 1 Device Overview MC9S12XE-Family
Table 1-9. Pin-Out Summary (Sheet 4 of 7)
208
MAPBGA
N11
LQFP
144
LQFP
112
63
51
QFP(1)
80
R12
52
Pin
2nd
Func.

PH1
KWH1
PL4
RXD6
PH0
KWH0
P11
64
T13
65
PD0
DATA0
R13
66
PD1
DATA1
T14
67
PD2
DATA2
R14
68
PD3
DATA3
VDDX
VDDX5
VSSX
VSSX5
3rd
Func.

4th
Func.

MOSI1
TXD6
MISO1
RXD6
EROMCTL
R15
69
53
37
PE3
LSTRB
LDS
P12
70
54
38
PE2
RW
WE
N13
71
55
39
PE1
IRQ
P13
72
56
40
PE0
XIRQ
P14
73
57
41
PA0
ADDR8
IVD8
N14
74
58
42
PA1
ADDR9
IVD9
M14
75
59
43
PA2
ADDR10
IVD10
P15
76
60
44
PA3
ADDR11
IVD11
P16
77
61
45
PA4
ADDR12
IVD12
N15
78
62
46
PA5
ADDR13
IVD13
M13
79
63
47
PA6
ADDR14
IVD14
N16
80
64
48
PA7
ADDR15
IVD15
VSSX
81
VSSX3
VDDX
82
VDDX3
L14
83
PD4
DATA4
M15
84
PD5
DATA5
M16
85
PD6
DATA6
K14
86
PD7
DATA7
K13
87
65
49
VDD
J13
88
66
50
VSS2
5th
Func.

MC9S12XE-Family Reference Manual Rev. 1.25
54
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
Table 1-9. Pin-Out Summary (Sheet 5 of 7)
LQFP
144
LQFP
112
QFP(1)
80
L15
89
67
51
L16
90
68
208
MAPBGA
Pin
2nd
Func.

PAD00
AN00
PAD08
AN08
PAD24
AN24
PAD01
AN01
PAD09
AN09
PAD25
AN25
PAD02
AN02
PAD10
AN10
H14
PAD26
AN26
H13
VSSA2
G13
VDDA2
K15
K16
91
69
J14
92
70
52
J15
J16
93
71
H16
94
72
H15
95
73
G16
96
74
53
54
F16
G15
97
75
G14
98
76
55
E16
F14
99
77
F15
100
78
56
D16
E15
101
79
C16
102
80
57
D15
C15
103
81
E14
104
82
58
B15
PAD03
AN03
PAD11
AN11
PAD27
AN27
PAD04
AN04
PAD12
AN12
PAD28
AN28
PAD05
AN05
PAD13
AN13
PAD29
AN29
PAD06
AN06
PAD14
AN14
PAD30
AN30
PAD07
AN07
PAD15
AN15
PAD31
AN31
C14
105
PAD16
AN16
D14
106
PAD17
AN17
F13
107
83
59
3rd
Func.

4th
Func.

5th
Func.

VDDA1
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
55
Chapter 1 Device Overview MC9S12XE-Family
Table 1-9. Pin-Out Summary (Sheet 6 of 7)
LQFP
144
LQFP
112
QFP(1)
80
D13
108
84
60
VRH
C13
109
85
61
VRL
E13
110
86
62
VSSA1
B14
111
PAD18
AN18
A14
112
PAD19
AN19
C12
113
PAD20
AN20
B13
114
PAD21
AN21
D12
115
PAD22
AN22
B12
116
PAD23
AN23
C11
117
87
PM7
TXCAN3
TXCAN4
TXD3
A13
118
88
PM6
RXCAN3
RXCAN4
RXD3
D11
119
89
63
PS0
RXD0
B11
120
90
64
PS1
TXD0
C10
121
91
65
PS2
RXD1
A12
122
92
66
PS3
TXD1
208
MAPBGA
Pin
VSSX
VSSX6
VDDX
VDDX6
2nd
Func.

3rd
Func.

4th
Func.

5th
Func.

B10
123
93
PS4
MISO0
A11
124
94
PS5
MOSI0
A10
125
95
PS6
SCK0
C9
126
96
PS7
SS0
B9
127
97
67
TEST
D9
128
98
68
PJ7
KWJ7
TXCAN4
SCL0
TXCAN0
A9
129
99
69
PJ6
KWJ6
RXCAN4
SDA0
RXCAN0
C8
130
PJ5
KWJ5
SCL1
CS2
PF0
CS0
PJ4
KWJ4
SDA1
CS0
PF1
CS1
PM5
TXCAN2
TXCAN0
TXCAN4
B8
D8
131
A8
D7
132
100
70
SCK0
MC9S12XE-Family Reference Manual Rev. 1.25
56
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
Table 1-9. Pin-Out Summary (Sheet 7 of 7)
208
MAPBGA
LQFP
144
LQFP
112
QFP(1)
80
B7
C7
133
101
71
A7
D6
134
102
72
B6
C6
135
103
73
A6
A5
136
104
74
B5
A4
137
105
75
B4
Pin
2nd
Func.

PF2
CS2
PM4
RXCAN2
PF3
CS3
PM3
TXCAN1
PF4
SDA0
PM2
RXCAN1
PF5
SCL0
PM1
TXCAN0
PF6
RXD3
PM0
RXCAN0
PF7
TXD3
VSSX
138
106
76
VSSX1
VDDX
139
107
77
VDDX1
C5
140
108
A3
141
109
B3
142
110
C4
143
111
C3
144
112
3rd
Func.

4th
Func.

5th
Func.

RXCAN0
RXCAN4
MOSI0
TXCAN0
SS0
RXCAN0
MISO0
PK7
ROMCTL
EWAIT
PP7
KWP7
PWM7
SCK2
TIMIOC7
PP6
KWP6
PWM6
SS2
TIMIOC6
79
PP5
KWP5
PWM5
MOSI2
TIMIOC5
80
PP4
KWP4
PWM4
MISO2
TIMIOC4
78
1. Standard 80QFP only. NOTE that XEA256 80QFP is not compatible
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
57
Chapter 1 Device Overview MC9S12XE-Family
Table 1-10. Signal Properties Summary (Sheet 1 of 4)
Pin
Pin
Pin
Pin
Pin
Power
Name
Name
Name
Name
Name
Supply
Function 1 Function 2 Function 3 Function 4 Function 5
Internal Pull
Resistor
Description
CTRL
Reset
State
EXTAL
—
—
—
—
VDDPLL
NA
NA
NA
NA
Oscillator pins
XTAL
—
—
—
—
VDDPLL
RESET
—
—
—
—
VDDX
TEST
—
—
—
—
N.A.

RESET pin
BKGD
MODC
—
—
—
VDDX
Always on
PAD[31:16]
AN[31:16]
—
—
—
VDDA
PER0AD1
PER1AD1
Disabled Port AD inputs of ATD1,
analog inputs of ATD1
PAD[15:0]
AN[15:0]
—
—
—
VDDA
PER0AD0
PER1AD0
Disabled Port AD inputs of ATD0,
analog inputs of ATD0
PULLUP
External reset
DOWN Test input
Up
Background debug
PA[7:0]
ADDR[15:8] IVD[15:8]
—
—
VDDX
PUCR
Disabled Port A I/O, address bus,
internal visibility data
PB[7:1]
ADDR[7:1]
IVD[7:0]
—
—
VDDX
PUCR
Disabled Port B I/O, address bus,
internal visibility data
PB0
ADDR0
UDS
VDDX
PUCR
Disabled Port B I/O, address bus,
upper data strobe
PC[7:0]
DATA[15:8]
—
—
—
VDDX
PUCR
Disabled Port C I/O, data bus
PD[7:0]
DATA[7:0]
—
—
—
VDDX
PUCR
Disabled Port D I/O, data bus
PE7
ECLKX2
XCLKS
—
—
VDDX
PUCR
PE6
TAGHI
MODB
—
—
VDDX
While RESET
pin is low: down
Port E I/O, tag high, mode
input
PE5
RE
MODA
TAGLO
—
VDDX
While RESET
pin is low: down
Port E I/O, read enable,
mode input, tag low input
PE4
ECLK
—
—
—
VDDX
PUCR
Up
Port E I/O, bus clock output
PE3
LSTRB
LDS
EROMCTL
—
VDDX
PUCR
Up
Port E I/O, low byte data
strobe, EROMON control
PE2
R/W
WE
—
—
VDDX
PUCR
Up
Port E I/O, read/write
PE1
IRQ
—
—
—
VDDX
PUCR
Up
Port E Input, maskable
interrupt
PE0
XIRQ
—
—
—
VDDX
PUCR
Up
Port E input, non-maskable
interrupt
PF7
TXD3
—
—
—
VDDX
PERF/
PPSF
Up
Port F I/O, interrupt, TXD of
SCI3
PF6
RXD3
—
—
—
VDDX
PERF/
PPSF
Up
Port F I/O, interrupt, RXD of
SCI3
PF5
SCL0
—
—
—
VDDX
PERF/
PPSF
Up
Port F I/O, interrupt, SCL of
IIC0
PF4
SDA0
—
—
—
VDDX
PERF/
PPSF
Up
Port F I/O, interrupt, SDA of
IIC0
PF3
CS3
—
—
—
VDDX
PERF/
PPSF
Up
Port F I/O, interrupt, chip
select 3
Up
Port E I/O, system clock
output, clock select
MC9S12XE-Family Reference Manual Rev. 1.25
58
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
Table 1-10. Signal Properties Summary (Sheet 2 of 4)
Pin
Pin
Pin
Pin
Pin
Power
Name
Name
Name
Name
Name
Supply
Function 1 Function 2 Function 3 Function 4 Function 5
Internal Pull
Resistor
Description
Reset
State
CTRL
PF2
CS2
—
—
—
VDDX
PERF/
PPSF
Up
Port F I/O, interrupt, chip
select 2
PF1
CS1
—
—
—
VDDX
PERF/
PPSF
Up
Port F I/O, interrupt, chip
select 1
PF0
CS0
—
—
—
VDDX
PERF/
PPSF
Up
Port F I/O, interrupt, chip
select 0
PH7
KWH7
SS2
TXD5
—
VDDX
PERH/
PPSH
Disabled Port H I/O, interrupt, SS of
SPI2, TXD of SCI5
PH6
KWH6
SCK2
RXD5
—
VDDX
PERH/
PPSH
Disabled Port H I/O, interrupt, SCK of
SPI2, RXD of SCI5
PH5
KWH5
MOSI2
TXD4
—
VDDX
PERH/
PPSH
Disabled Port H I/O, interrupt, MOSI
of SPI2, TXD of SCI4
PH4
KWH4
MISO2
RXD4
—
VDDX
PERH/PPSH Disabled Port H I/O, interrupt, MISO
of SPI2, RXD of SCI4
PH3
KWH3
SS1
TXD7
—
VDDX
PERH/PPSH Disabled Port H I/O, interrupt, SS of
SPI1
PH2
KWH2
SCK1
RXD7
—
VDDX
PERH/PPSH Disabled Port H I/O, interrupt, SCK of
SPI1
PH1
KWH1
MOSI1
TXD6
—
VDDX
PERH/PPSH Disabled Port H I/O, interrupt, MOSI
of SPI1
PH0
KWH0
MISO1
RXD6
—
VDDX
PERH/PPSH Disabled Port H I/O, interrupt, MISO
of SPI1
PJ7
KWJ7
TXCAN4
SCL0
TXCAN0
VDDX
PERJ/
PPSJ
Up
Port J I/O, interrupt, TX of
CAN4, SCL of IIC0, TX of
CAN0
PJ6
KWJ6
RXCAN4
SDA0
RXCAN0
VDDX
PERJ/
PPSJ
Up
Port J I/O, interrupt, RX of
CAN4, SDA of IIC0, RX of
CAN0
PJ5
KWJ5
SCL1
CS2
—
VDDX
PERJ/
PPSJ
Up
Port J I/O, interrupt, SCL of
IIC1, chip select 2
PJ4
KWJ4
SDA1
CS0
—
VDDX
PERJ/
PPSJ
Up
Port J I/O, interrupt, SDA of
IIC1, chip select 0
PJ3
KWJ3
—
—
—
VDDX
PERJ/
PPSJ
Up
Port J I/O, interrupt,
PJ2
KWJ2
CS1
—
—
VDDX
PERJ/
PPSJ
Up
Port J I/O, interrupt, chip
select 1
PJ1
KWJ1
TXD2
—
—
VDDX
PERJ/
PPSJ
Up
Port J I/O, interrupt, TXD of
SCI2
PJ0
KWJ0
RXD2
CS3
—
VDDX
PERJ/
PPSJ
Up
Port J I/O, interrupt, RXD of
SCI2
PK7
EWAIT
ROMCTL
—
—
VDDX
PUCR
Up
Port K I/O, EWAIT input,
ROM on control
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
59
Chapter 1 Device Overview MC9S12XE-Family
Table 1-10. Signal Properties Summary (Sheet 3 of 4)
Pin
Pin
Pin
Pin
Pin
Power
Name
Name
Name
Name
Name
Supply
Function 1 Function 2 Function 3 Function 4 Function 5
Internal Pull
Resistor
Description
CTRL
Reset
State
PK[6:4]
ADDR
[22:20]
ACC[2:0]
—
—
VDDX
PUCR
Up
Port K I/O, extended
addresses, access source
for external access
PK[3:0]
ADDR
[19:16]
IQSTAT
[3:0]
—
—
VDDX
PUCR
Up
Extended address, PIPE
status
PL7
TXD7
—
—
—
VDDX
PERL/
PPSL
Up
Port L I/O, TXD of SCI7
PL6
RXD7
—
—
—
VDDX
PERL/
PPSL
Up
Port LI/O, RXD of SCI7
PL5
TXD6
—
—
—
VDDX
PERL/
PPSL
Up
Port L I/O, TXD of SCI6
PL4
RXD6
—
—
—
VDDX
PERL/
PPSL
Up
Port LI/O, RXD of SCI6
PL3
TXD5
—
—
—
VDDX
PERL/
PPSL
Up
Port L I/O, TXD of SCI5
PL2
RXD5
—
—
—
VDDX
PERL/
PPSL
Up
Port LI/O, RXD of SCI5
PL1
TXD4
—
—
—
VDDX
PERL/
PPSL
Up
Port L I/O, TXD of SCI4
PL0
RXD4
—
—
—
VDDX
PERL/
PPSL
Up
Port LI/O, RXD of SCI4
PM7
TXCAN3
TXD3
TXCAN4
—
VDDX
PERM/
PPSM
PM6
RXCAN3
RXD3
RXCAN4
—
VDDX PERM/PPSM Disabled Port M I/O RX of CAN3 and
CAN4, RXD of SCI3
PM5
TXCAN2
TXCAN0
TXCAN4
SCK0
VDDX PERM/PPSM Disabled Port M I/OCAN0, CAN2,
CAN4, SCK of SPI0
PM4
RXCAN2
RXCAN0
RXCAN4
MOSI0
VDDX PERM/PPSM Disabled Port M I/O, CAN0, CAN2,
CAN4, MOSI of SPI0
PM3
TXCAN1
TXCAN0
SS0
—
VDDX PERM/PPSM Disabled Port M I/O TX of CAN1,
CAN0, SS of SPI0
PM2
RXCAN1
RXCAN0
MISO0
—
VDDX PERM/PPSM Disabled Port M I/O, RX of CAN1,
CAN0, MISO of SPI0
Disabled Port M I/O, TX of CAN3 and
CAN4, TXD of SCI3
PM1
TXCAN0
—
—
VDDX PERM/PPSM Disabled Port M I/O, TX of CAN0
PM0
RXCAN0
—
—
VDDX PERM/PPSM Disabled Port M I/O, RX of CAN0
PP7
KWP7
PWM7
SCK2
TIMIOC7
VDDX
PERP/
PPSP
Disabled Port P I/O, interrupt, channel
7 of PWM/TIM , SCK of SPI2
PP6
KWP6
PWM6
SS2
TIMIOC6
VDDX
PERP/
PPSP
Disabled Port P I/O, interrupt, channel
6 of PWM/TIM, SS of SPI2
PP5
KWP5
PWM5
MOSI2
TIMIOC5
VDDX
PERP/
PPSP
Disabled Port P I/O, interrupt, channel
5 of PWM/TIM, MOSI of
SPI2
MC9S12XE-Family Reference Manual Rev. 1.25
60
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
Table 1-10. Signal Properties Summary (Sheet 4 of 4)
Pin
Pin
Pin
Pin
Pin
Power
Name
Name
Name
Name
Name
Supply
Function 1 Function 2 Function 3 Function 4 Function 5
Internal Pull
Resistor
Description
Reset
State
CTRL
PP4
KWP4
PWM4
MISO2
TIMIOC4
VDDX
PERP/
PPSP
Disabled Port P I/O, interrupt, channel
4 of PWM/TIM, MISO2 of
SPI2
PP3
KWP3
PWM3
SS1
TIMIOC3
VDDX
PERP/
PPSP
Disabled Port P I/O, interrupt, channel
3 of PWM/TIM, SS of SPI1
PP2
KWP2
PWM2
SCK1
TIMIOC2
VDDX
PERP/
PPSP
Disabled Port P I/O, interrupt, channel
2 of PWM/TIM, SCK of SPI1
PP1
KWP1
PWM1
MOSI1
TIMIOC1
VDDX
PERP/
PPSP
Disabled Port P I/O, interrupt, channel
1 of PWM/TIM, MOSI of
SPI1
PP0
KWP0
PWM0
MISO1
TIMIOC0
VDDX
PERP/
PPSP
Disabled Port P I/O, interrupt, channel
0 of PWM/TIM, MISO2 of
SPI1
PR[7:0]
TIMIOC
[7:0]
—
—
—
VDDX
PERR/
PPSR
Disabled Port RI/O, TIM channels
PS7
SS0
—
—
—
VDDX
PERS/
PPSS
Up
Port S I/O, SS of SPI0
PS6
SCK0
—
—
—
VDDX
PERS/
PPSS
Up
Port S I/O, SCK of SPI0
PS5
MOSI0
—
—
—
VDDX
PERS/
PPSS
Up
Port S I/O, MOSI of SPI0
PS4
MISO0
—
—
—
VDDX
PERS/
PPSS
Up
Port S I/O, MISO of SPI0
PS3
TXD1
—
—
—
VDDX
PERS/
PPSS
Up
Port S I/O, TXD of SCI1
PS2
RXD1
—
—
—
VDDX
PERS/
PPSS
Up
Port S I/O, RXD of SCI1
PS1
TXD0
—
—
—
VDDX
PERS/
PPSS
Up
Port S I/O, TXD of SCI0
PS0
RXD0
—
—
—
VDDX
PERS/
PPSS
Up
Port S I/O, RXD of SCI0
PT[7:6]
IOC[7:6]
—
—
—
VDDX
PERT/
PPST
Disabled Port T I/O, ECT channels
PT[5]
IOC[5]
VREGAPI
—
—
VDDX
PERT/
PPST
Disabled Port T I/O, ECT channels
PT[4:0]
IOC[4:0]
—
—
—
VDDX
PERT/
PPST
Disabled Port T I/O, ECT channels
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
61
Chapter 1 Device Overview MC9S12XE-Family
1.2.3
Detailed Signal Descriptions
NOTE
The pin list of the largest package version of each MC9S12XE-Family
derivative gives the complete of interface signals that also exist on smaller
package options, although some of them are not bonded out. For devices
assembled in smaller packages all non-bonded out pins should be
configured as outputs after reset in order to avoid current drawn from
floating inputs. Refer to Table 1-10 for affected pins. Particular attention is
drawn to Port R, which does not have enabled pull-up/pull-down devices
coming out of reset.

*******
EXTAL, XTAL — Oscillator Pins
EXTAL and XTAL are the crystal driver and external clock pins. On reset all the device clocks are derived
from the EXTAL input frequency. XTAL is the oscillator output.

*******
RESET — External Reset Pin
The RESET pin is an active low bidirectional control signal. It acts as an input to initialize the MCU to a
known start-up state. As an output it is driven low to indicate when any internal MCU reset source triggers.
The RESET pin has an internal pull-up device.

1.2.3.3
TEST — Test Pin
This input only pin is reserved for test. This pin has a pull-down device.
NOTE
The TEST pin must be tied to VSS in all applications.

1.2.3.4
BKGD / MODC — Background Debug and Mode Pin
The BKGD/MODC pin is used as a pseudo-open-drain pin for the background debug communication. It
is used as a MCU operating mode select pin during reset. The state of this pin is latched to the MODC bit
at the rising edge of RESET. The BKGD pin has a pull-up device.

1.2.3.5
PAD[15:0] / AN[15:0] — Port AD Input Pins of ATD0
PAD[15:0] are general-purpose input or output pins and analog inputs AN[15:0] of the analog-to-digital
converter ATD0.

*******
PAD[31:16] / AN[31:16] — Port AD Input Pins of ATD1
PAD[31:16] are general-purpose input or output pins and analog inputs AN[31:16] of the analog-to-digital
converter ATD1.

MC9S12XE-Family Reference Manual Rev. 1.25
62
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
*******
PA[7:0] / ADDR[15:8] / IVD[15:8] — Port A I/O Pins
PA[7:0] are general-purpose input or output pins. In MCU expanded modes of operation, these pins are
used for the external address bus. In MCU emulation modes of operation, these pins are used for external
address bus and internal visibility read data.

*******
PB[7:1] / ADDR[7:1] / IVD[7:1] — Port B I/O Pins
PB[7:1] are general-purpose input or output pins. In MCU expanded modes of operation, these pins are
used for the external address bus. In MCU emulation modes of operation, these pins are used for external
address bus and internal visibility read data.

*******
PB0 / ADDR0 / UDS / IVD[0] — Port B I/O Pin 0
PB0 is a general-purpose input or output pin. In MCU expanded modes of operation, this pin is used for
the external address bus ADDR0 or as upper data strobe signal. In MCU emulation modes of operation,
this pin is used for external address bus ADDR0 and internal visibility read data IVD0.

*******0
PC[7:0] / DATA [15:8] — Port C I/O Pins
PC[7:0] are general-purpose input or output pins. In MCU expanded modes of operation, these pins are
used for the external data bus.
The input voltage thresholds for PC[7:0] can be configured to reduced levels, to allow data from an external
3.3-V peripheral to be read by the MCU operating at 5.0 V. The input voltage thresholds for PC[7:0] are
configured to reduced levels out of reset in expanded and emulation modes. The input voltage thresholds
for PC[7:0] are configured to 5-V levels out of reset in normal modes.

*******1
PD[7:0] / DATA [7:0] — Port D I/O Pins
PD[7:0] are general-purpose input or output pins. In MCU expanded modes of operation, these pins are
used for the external data bus.
The input voltage thresholds for PD[7:0] can be configured to reduced levels, to allow data from an
external 3.3-V peripheral to be read by the MCU operating at 5.0 V. The input voltage thresholds for
PD[7:0] are configured to reduced levels out of reset in expanded and emulation modes. The input voltage
thresholds for PC[7:0] are configured to 5-V levels out of reset in normal modes.

*******2
PE7 / ECLKX2 / XCLKS — Port E I/O Pin 7
PE7 is a general-purpose input or output pin. ECLKX2 is a free running clock of twice the internal bus
frequency, available by default in emulation modes and when enabled in other modes. The XCLKS is an
input signal which controls whether a crystal in combination with the internal loop controlled Pierce
oscillator is used or whether full swing Pierce oscillator/external clock circuitry is used (refer to Oscillator
Configuration). An internal pullup is enabled during reset.

MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
63
Chapter 1 Device Overview MC9S12XE-Family
*******3
PE6 / MODB / TAGHI — Port E I/O Pin 6
PE6 is a general-purpose input or output pin. It is used as a MCU operating mode select pin during reset.
The state of this pin is latched to the MODB bit at the rising edge of RESET. This pin is an input with a
pull-down device which is only active when RESET is low. TAGHI is used to tag the high half of the
instruction word being read into the instruction queue.
The input voltage threshold for PE6 can be configured to reduced levels, to allow data from an external
3.3-V peripheral to be read by the MCU operating at 5.0 V. The input voltage threshold for PE6 is
configured to reduced levels out of reset in expanded and emulation modes.

*******4
PE5 / MODA / TAGLO / RE — Port E I/O Pin 5
PE5 is a general-purpose input or output pin. It is used as an MCU operating mode select pin during reset.
The state of this pin is latched to the MODA bit at the rising edge of RESET. This pin is shared with the
read enable RE output. This pin is an input with a pull-down device which is only active when RESET is
low. TAGLO is used to tag the low half of the instruction word being read into the instruction queue.
The input voltage threshold for PE5 can be configured to reduced levels, to allow data from an external
3.3-V peripheral to be read by the MCU operating at 5.0 V. The input voltage threshold for PE5 is
configured to reduced levels out of reset in expanded and emulation modes.

*******5
PE4 / ECLK — Port E I/O Pin 4
PE4 is a general-purpose input or output pin. It can be configured to drive the internal bus clock ECLK.
ECLK can be used as a timing reference. The ECLK output has a programmable prescaler.

*******6
PE3 / LSTRB / LDS / EROMCTL— Port E I/O Pin 3
PE3 is a general-purpose input or output pin. In MCU expanded modes of operation, LSTRB or LDS can
be used for the low byte strobe function to indicate the type of bus access. At the rising edge of RESET
the state of this pin is latched to the EROMON bit.

*******7
PE2 / R/W / WE— Port E I/O Pin 2
PE2 is a general-purpose input or output pin. In MCU expanded modes of operations, this pin drives the
read/write output signal or write enable output signal for the external bus. It indicates the direction of data
on the external bus.

*******8
PE1 / IRQ — Port E Input Pin 1
PE1 is a general-purpose input pin and the maskable interrupt request input that provides a means of
applying asynchronous interrupt requests. This will wake up the MCU from stop or wait mode.

*******9
PE0 / XIRQ — Port E Input Pin 0
PE0 is a general-purpose input pin and the non-maskable interrupt request input that provides a means of
applying asynchronous interrupt requests. This will wake up the MCU from stop or wait mode. The XIRQ
MC9S12XE-Family Reference Manual Rev. 1.25
64
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
interrupt is level sensitive and active low. As XIRQ is level sensitive, while this pin is low the MCU will
not enter STOP mode.

*******0
PF7 / TXD3 — Port F I/O Pin 7
PF7 is a general-purpose input or output pin. It can be configured as the transmit pin TXD of serial
communication interface 3 (SCI3).

*******1
PF6 / RXD3 — Port F I/O Pin 6
PF6 is a general-purpose input or output pin. It can be configured as the transmit pin RXD of serial
communication interface 3 (SCI3).

*******2
PF5 / SCL0 — Port F I/O Pin 5
PF5 is a general-purpose input or output pin. It can be configured as the serial clock pin SCL of the IIC0
module.

*******3
PF4 / SDA0 — Port F I/O Pin 4
PF4 is a general-purpose input or output pin. It can be configured as the serial data pin SDA of the IIC0
module.

*******4
PF[3:0] / CS[3:0] — Port F I/O Pins 3 to 0
PF[3:0] are a general-purpose input or output pins. They can be configured as chip select outputs [3:0].

*******5
PH7 / KWH7 / SS2 / TXD5 — Port H I/O Pin 7
PH7 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as slave select pin SS of the serial peripheral interface 2 (SPI2). It can be configured as the
transmit pin TXD of serial communication interface 5 (SCI5).

*******6
PH6 / KWH6 / SCK2 / RXD5 — Port H I/O Pin 6
PH6 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as serial clock pin SCK of the serial peripheral interface 2 (SPI2). It can be configured as the
receive pin (RXD) of serial communication interface 5 (SCI5).

*******7
PH5 / KWH5 / MOSI2 / TXD4 — Port H I/O Pin 5
PH5 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as master output (during master mode) or slave input pin (during slave mode) MOSI of the
serial peripheral interface 2 (SPI2). It can be configured as the transmit pin TXD of serial communication
interface 4 (SCI4).

MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
65
Chapter 1 Device Overview MC9S12XE-Family
*******8
PH4 / KWH4 / MISO2 / RXD4 — Port H I/O Pin 4
PH4 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as master input (during master mode) or slave output (during slave mode) pin MISO of the
serial peripheral interface 2 (SPI2). It can be configured as the receive pin RXD of serial communication
interface 4 (SCI4).

*******9
PH3 / KWH3 / SS1 — Port H I/O Pin 3
PH3 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as slave select pin SS of the serial peripheral interface 1 (SPI1). It can also be configured as the
transmit pin TXD of serial communication interface 7 (SCI7).

********
PH2 / KWH2 / SCK1 — Port H I/O Pin 2
PH2 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as serial clock pin SCK of the serial peripheral interface 1 (SPI1). It can be configured as the
receive pin RXD of serial communication interface 7 (SCI7).

********
PH1 / KWH1 / MOSI1 — Port H I/O Pin 1
PH1 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as master output (during master mode) or slave input pin (during slave mode) MOSI of the
serial peripheral interface 1 (SPI1). It can also be configured as the transmit pin TXD of serial
communication interface 6 (SCI6).

********
PH0 / KWH0 / MISO1 — Port H I/O Pin 0
PH0 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as master input (during master mode) or slave output (during slave mode) pin MISO of the
serial peripheral interface 1 (SPI1). It can be configured as the receive pin RXD of serial communication
interface 6 (SCI6).

********
PJ7 / KWJ7 / TXCAN4 / SCL0 / TXCAN0— PORT J I/O Pin 7
PJ7 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as the transmit pin TXCAN for the scalable controller area network controller 0 or 4 (CAN0 or
CAN4) or as the serial clock pin SCL of the IIC0 module.

********
PJ6 / KWJ6 / RXCAN4 / SDA0 / RXCAN0 — PORT J I/O Pin 6
PJ6 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as the receive pin RXCAN for the scalable controller area network controller 0 or 4 (CAN0 or
CAN4) or as the serial data pin SDA of the IIC0 module.

MC9S12XE-Family Reference Manual Rev. 1.25
66
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
********
PJ5 / KWJ5 / SCL1 / CS2 — PORT J I/O Pin 5
PJ5 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as the serial clock pin SCL of the IIC1 module. It can be also configured as chip-select output 2.

********
PJ4 / KWJ4 / SDA1 / CS0 — PORT J I/O Pin 4
PJ4 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as the serial data pin SDA of the IIC1 module. It can also be configured as chip-select output.

********
PJ3 / KWJ3 — PORT J I/O Pin 3
PJ3 is a general-purpose input or output pins. It can be configured as a keypad wakeup input.

********
PJ2 / KWJ2 / CS1 — PORT J I/O Pin 2
PJ2 is a general-purpose input or output pins. It can be configured as a keypad wakeup input. It can also
be configured as chip-select output.

********
PJ1 / KWJ1 / TXD2 — PORT J I/O Pin 1
PJ1 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as the transmit pin TXD of the serial communication interface 2 (SCI2).

********
PJ0 / KWJ0 / RXD2 / CS3 — PORT J I/O Pin 0
PJ0 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as the receive pin RXD of the serial communication interface 2 (SCI2).It can also be configured
as chip-select output 3.

********
PK7 / EWAIT / ROMCTL — Port K I/O Pin 7
PK7 is a general-purpose input or output pin. During MCU emulation modes and normal expanded modes
of operation, this pin is used to enable the Flash EEPROM memory in the memory map (ROMCTL). At
the rising edge of RESET, the state of this pin is latched to the ROMON bit. The EWAIT input signal
maintains the external bus access until the external device is ready to capture data (write) or provide data
(read).
The input voltage threshold for PK7 can be configured to reduced levels, to allow data from an external
3.3-V peripheral to be read by the MCU operating at 5.0 V.

********
PK[6:4] / ADDR[22:20] / ACC[2:0] — Port K I/O Pin [6:4]
PK[6:4] are general-purpose input or output pins. During MCU expanded modes of operation, the
ACC[2:0] signals are used to indicate the access source of the bus cycle. These pins also provide the
expanded addresses ADDR[22:20] for the external bus. In Emulation modes ACC[2:0] is available and is
time multiplexed with the high addresses
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
67
Chapter 1 Device Overview MC9S12XE-Family
********
PK[3:0] / ADDR[19:16] / IQSTAT[3:0] — Port K I/O Pins [3:0]
PK3-PK0 are general-purpose input or output pins. In MCU expanded modes of operation, these pins
provide the expanded address ADDR[19:16] for the external bus and carry instruction pipe information.

********
PL7 / TXD7 — Port L I/O Pin 7
PL7 is a general-purpose input or output pin. It can be configured as the transmit pin TXD of serial
communication interface 7 (SCI7).

********
PL6 / RXD7 — Port L I/O Pin 6
PL6 is a general-purpose input or output pin. It can be configured as the receive pin RXD of serial
communication interface 7 (SCI7).

********
PL5 / TXD6 — Port L I/O Pin 5
PL5 is a general-purpose input or output pin. It can be configured as the transmit pin TXD of serial
communication interface 6 (SCI6).

********
PL4 / RXD6 — Port L I/O Pin 4
PL4 is a general-purpose input or output pin. It can be configured as the receive pin RXD of serial
communication interface 6 (SCI6).

********
PL3 / TXD5 — Port L I/O Pin 3
PL3 is a general-purpose input or output pin. It can be configured as the transmit pin TXD of serial
communication interface 5 (SCI5).

********
PL2 / RXD5 — Port L I/O Pin 2
PL2 is a general-purpose input or output pin. It can be configured as the receive pin RXD of serial
communication interface 5 (SCI5).

1.2.3.50
PL1 / TXD4 — Port L I/O Pin 1
PL1 is a general-purpose input or output pin. It can be configured as the transmit pin TXD of serial
communication interface 4 (SCI4).

1.2.3.51
PL0 / RXD4 — Port L I/O Pin 0
PL0 is a general-purpose input or output pin. It can be configured as the receive pin RXD of serial
communication interface 4 (SCI4).

MC9S12XE-Family Reference Manual Rev. 1.25
68
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
1.2.3.52
PM7 / TXCAN3 / TXCAN4 / TXD3 — Port M I/O Pin 7
PM7 is a general-purpose input or output pin. It can be configured as the transmit pin TXCAN of the
scalable controller area network controller 3 or 4 (CAN3 or CAN4). PM7 can be configured as the transmit
pin TXD3 of the serial communication interface 3 (SCI3).

1.2.3.53
PM6 / RXCAN3 / RXCAN4 / RXD3 — Port M I/O Pin 6
PM6 is a general-purpose input or output pin. It can be configured as the receive pin RXCAN of the
scalable controller area network controller 3 or 4 (CAN3 or CAN4). PM6 can be configured as the receive
pin RXD3 of the serial communication interface 3 (SCI3).

********
PM5 / TXCAN0 / TXCAN2 / TXCAN4 / SCK0 — Port M I/O Pin 5
PM5 is a general-purpose input or output pin. It can be configured as the transmit pin TXCAN of the
scalable controller area network controllers 0, 2 or 4 (CAN0, CAN2, or CAN4). It can be configured as
the serial clock pin SCK of the serial peripheral interface 0 (SPI0).

********
PM4 / RXCAN0 / RXCAN2 / RXCAN4 / MOSI0 — Port M I/O Pin 4
PM4 is a general-purpose input or output pin. It can be configured as the receive pin RXCAN of the
scalable controller area network controllers 0, 2, or 4 (CAN0, CAN2, or CAN4). It can be configured as
the master output (during master mode) or slave input pin (during slave mode) MOSI for the serial
peripheral interface 0 (SPI0).

********
PM3 / TXCAN1 / TXCAN0 / SS0 — Port M I/O Pin 3
PM3 is a general-purpose input or output pin. It can be configured as the transmit pin TXCAN of the
scalable controller area network controllers 1 or 0 (CAN1 or CAN0). It can be configured as the slave
select pin SS of the serial peripheral interface 0 (SPI0).

********
PM2 / RXCAN1 / RXCAN0 / MISO0 — Port M I/O Pin 2
PM2 is a general-purpose input or output pin. It can be configured as the receive pin RXCAN of the
scalable controller area network controllers 1 or 0 (CAN1 or CAN0). It can be configured as the master
input (during master mode) or slave output pin (during slave mode) MISO for the serial peripheral
interface 0 (SPI0).

********
PM1 / TXCAN0 — Port M I/O Pin 1
PM1 is a general-purpose input or output pin. It can be configured as the transmit pin TXCAN of the
scalable controller area network controller 0 (CAN0).

********
PM0 / RXCAN0 — Port M I/O Pin 0
PM0 is a general-purpose input or output pin. It can be configured as the receive pin RXCAN of the
scalable controller area network controller 0 (CAN0).

MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
69
Chapter 1 Device Overview MC9S12XE-Family
*******0
PP7 / KWP7 / PWM7 / SCK2 / TIMIOC7— Port P I/O Pin 7
PP7 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as pulse width modulator (PWM) channel 7 output, TIM channel 7, or as serial clock pin SCK
of the serial peripheral interface 2 (SPI2).

*******1
PP6 / KWP6 / PWM6 / SS2 / TIMIOC6— Port P I/O Pin 6
PP6 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as pulse width modulator (PWM) channel 6 output, TIM channel 6 or as the slave select pin SS
of the serial peripheral interface 2 (SPI2).

*******2
PP5 / KWP5 / PWM5 / MOSI2 / TIMIOC5— Port P I/O Pin 5
PP5 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as pulse width modulator (PWM) channel 5 output, TIM channel 5 or as the master output
(during master mode) or slave input pin (during slave mode) MOSI of the serial peripheral interface 2
(SPI2).

*******3
PP4 / KWP4 / PWM4 / MISO2 / TIMIOC4— Port P I/O Pin 4
PP4 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as pulse width modulator (PWM) channel 4 output, TIM channel 4 or as the master input
(during master mode) or slave output (during slave mode) pin MISO of the serial peripheral interface 2
(SPI2).

*******4
PP3 / KWP3 / PWM3 / SS1 / TIMIOC3— Port P I/O Pin 3
PP3 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as pulse width modulator (PWM) channel 3 output, TIM channel 3, or as the slave select pin
SS of the serial peripheral interface 1 (SPI1).

*******5
PP2 / KWP2 / PWM2 / SCK1 / TIMIOC2— Port P I/O Pin 2
PP2 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as pulse width modulator (PWM) channel 2 output, TIM channel 2, or as the serial clock pin
SCK of the serial peripheral interface 1 (SPI1).

*******6
PP1 / KWP1 / PWM1 / MOSI1 / TIMIOC1— Port P I/O Pin 1
PP1 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as pulse width modulator (PWM) channel 1 output, TIM channel 1, or master output (during
master mode) or slave input pin (during slave mode) MOSI of the serial peripheral interface 1 (SPI1).

MC9S12XE-Family Reference Manual Rev. 1.25
70
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
*******7
PP0 / KWP0 / PWM0 / MISO1 / TIMIOC0— Port P I/O Pin 0
PP0 is a general-purpose input or output pin. It can be configured as a keypad wakeup input. It can be
configured as pulse width modulator (PWM) channel 0 output, TIM channel 0 or as the master input
(during master mode) or slave output (during slave mode) pin MISO of the serial peripheral interface 1
(SPI1).

*******8
PR[7:0] / TIMIOC[7:0] — Port R I/O Pins [7:0]
PR[7:0] are general-purpose input or output pins. They can be configured as input capture or output
compare pins IOC[7:0] of the standard timer (TIM).

*******9
PS7 / SS0 — Port S I/O Pin 7
PS7 is a general-purpose input or output pin. It can be configured as the slave select pin SS of the serial
peripheral interface 0 (SPI0).

*******0
PS6 / SCK0 — Port S I/O Pin 6
PS6 is a general-purpose input or output pin. It can be configured as the serial clock pin SCK of the serial
peripheral interface 0 (SPI0).

*******1
PS5 / MOSI0 — Port S I/O Pin 5
PS5 is a general-purpose input or output pin. It can be configured as master output (during master mode)
or slave input pin (during slave mode) MOSI of the serial peripheral interface 0 (SPI0).

*******2
PS4 / MISO0 — Port S I/O Pin 4
PS4 is a general-purpose input or output pin. It can be configured as master input (during master mode) or
slave output pin (during slave mode) MOSI of the serial peripheral interface 0 (SPI0).

*******3
PS3 / TXD1 — Port S I/O Pin 3
PS3 is a general-purpose input or output pin. It can be configured as the transmit pin TXD of serial
communication interface 1 (SCI1).

*******4
PS2 / RXD1 — Port S I/O Pin 2
PS2 is a general-purpose input or output pin. It can be configured as the receive pin RXD of serial
communication interface 1 (SCI1).

*******5
PS1 / TXD0 — Port S I/O Pin 1
PS1 is a general-purpose input or output pin. It can be configured as the transmit pin TXD of serial
communication interface 0 (SCI0).

MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
71
Chapter 1 Device Overview MC9S12XE-Family
*******6
PS0 / RXD0 — Port S I/O Pin 0
PS0 is a general-purpose input or output pin. It can be configured as the receive pin RXD of serial
communication interface 0 (SCI0).

*******7
PT[7:6] / IOC[7:6] — Port T I/O Pins [7:6]
PT[7:6] are general-purpose input or output pins. They can be configured as input capture or output
compare pins IOC[7:6] of the enhanced capture timer (ECT).

*******8
PT[5] / IOC[5] / VREG_API— Port T I/O Pins [5]
PT[5] is a general-purpose input or output pin. It can be configured as input capture or output compare pin
IOC[5] of the enhanced capture timer (ECT) or can be configured to output the VREG_API signal.

*******9
PT[4:0] / IOC[4:0] — Port T I/O Pins [4:0]
PT[4:0] are general-purpose input or output pins. They can be configured as input capture or output
compare pins IOC[4:0] of the enhanced capture timer (ECT).

1.2.4
Power Supply Pins
MC9S12XE-Family power and ground pins are described below.
Because fast signal transitions place high, short-duration current demands on the power supply, use bypass
capacitors with high-frequency characteristics and place them as close to the MCU as possible.
NOTE
All VSS pins must be connected together in the application.

1.2.4.1
VDDX[7:1], VSSX[7:1] — Power and Ground Pins for I/O Drivers
External power and ground for I/O drivers. Bypass requirements depend on how heavily the MCU pins are
loaded. All VDDX pins are connected together internally. All VSSX pins are connected together internally.

1.2.4.2
VDDR — Power Pin for Internal Voltage Regulator
Input to the internal voltage regulator. The internal voltage regulator is turned off, if VDDR is tied to ground
1.2.4.3
VDD, VSS1,VSS2,VSS3 — Core Power Pins
Power is supplied to the MCU core from the internal voltage regulator, whose load capacitor must be
connected to VDD. The voltage supply of nominally 1.8V is derived from the internal voltage regulator.
The return current path is through the VSS1,VSS2 and VSS3 pins. No static external loading of these pins
is permitted.

MC9S12XE-Family Reference Manual Rev. 1.25
72
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
1.2.4.4
VDDF — NVM Power Pin
Power is supplied to the MCU NVM through VDDF . The voltage supply of nominally 2.8V is derived
from the internal voltage regulator. No static external loading of these pins is permitted.

1.2.4.5
VDDA2, VDDA1, VSSA2, VSSA1 — Power Supply Pins for ATD and
Voltage Regulator
These are the power supply and ground input pins for the analog-to-digital converters and the voltage
regulator. Internally the VDDA pins are connected together. Internally the VSSA pins are connected
together.

1.2.4.6
VRH, VRL — ATD Reference Voltage Input Pins
VRH and VRL are the reference voltage input pins for the analog-to-digital converter.

1.2.4.7
VDDPLL, VSSPL — Power Supply Pins for PLL
These pins provide operating voltage and ground for the oscillator and the phased-locked loop. The voltage
supply of nominally 1.8V is derived from the internal voltage regulator. This allows the supply voltage to
the oscillator and PLL to be bypassed independently. This voltage is generated by the internal voltage
regulator. No static external loading of these pins is permitted.
Table 1-11. Power and Ground Connection Summary
Mnemonic
Nominal
Voltage
VDDR
5.0 V
External power supply to internal voltage
regulator
VDDX[7:1]
5.0 V
VSSX[7:1]
0V
External power and ground, supply to pin
drivers
VDDA2,
VDDA1
5.0 V
VSSA2,
VSSA1
0V
VRL
0V
VRH
5.0 V
VDD
1.8 V
VSS1, VSS2,
VSS3
0V
VDDF
2.8 V
Description
Operating voltage and ground for the
analog-to-digital converters and the
reference for the internal voltage regulator,
allows the supply voltage to the A/D to be
bypassed independently.
Reference voltages for the analog-to-digital
converter.
Internal power and ground generated by
internal regulator for the internal core.

Internal power and ground generated by
internal regulator for the internal NVM.

MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
73
Chapter 1 Device Overview MC9S12XE-Family
Table 1-11. Power and Ground Connection Summary (continued)
Mnemonic
Nominal
Voltage
VDDPLL
1.8 V
VSSPLL
0V
Description
Provides operating voltage and ground for
the phased-locked loop. This allows the
supply voltage to the PLL to be bypassed
independently. Internal power and ground
generated by internal regulator.

MC9S12XE-Family Reference Manual Rev. 1.25
74
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
1.3
System Clock Description
The clock and reset generator module (CRG) provides the internal clock signals for the core and all
peripheral modules. Figure 1-9 shows the clock connections from the CRG to all modules.
Consult the CRG specification for details on clock generation.
SCI0 . . SCI 7
CAN0 . . CAN4
SPI0 . . SPI2
IIC0 & IIC1
ATD0 & ATD1
Bus Clock
PIT
EXTAL
Oscillator Clock
ECT
CRG
PIM
XTAL
Core Clock
PWM
RAM
S12X
XGATE
FLASH &
EEE
TIM
Figure 1-9. Clock Connections
The system clock can be supplied in several ways enabling a range of system operating frequencies to be
supported:
• The on-chip phase locked loop (PLL)
• the PLL self clocking
• the oscillator
The clock generated by the PLL or oscillator provides the main system clock frequencies core clock and
bus clock. As shown in Figure 1-9, these system clocks are used throughout the MCU to drive the core,
the memories, and the peripherals.

MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
75
Chapter 1 Device Overview MC9S12XE-Family
The program Flash memory and the EEPROM are supplied by the bus clock and the oscillator clock. The
oscillator clock is used as a time base to derive the program and erase times for the NVM’s.
The CAN modules may be configured to have their clock sources derived either from the bus clock or
directly from the oscillator clock. This allows the user to select its clock based on the required jitter
performance.
In order to ensure the presence of the clock the MCU includes an on-chip clock monitor connected to the
output of the oscillator. The clock monitor can be configured to invoke the PLL self-clocking mode or to
generate a system reset if it is allowed to time out as a result of no oscillator clock being present.
In addition to the clock monitor, the MCU also provides a clock quality checker which performs a more
accurate check of the clock. The clock quality checker counts a predetermined number of clock edges
within a defined time window to insure that the clock is running. The checker can be invoked following
specific events such as on wake-up or clock monitor failure.

1.4
Modes of Operation
The MCU can operate in different modes associated with MCU resource mapping and bus interface
configuration. These are described in 1.4.1 Chip Configuration Summary.
The MCU can operate in different power modes to facilitate power saving when full system performance
is not required. These are described in 1.4.2 Power Modes.
Some modules feature a software programmable option to freeze the module status whilst the background
debug module is active to facilitate debugging. This is described in 1.4.3 Freeze Mode.
For system integrity support separate system states are featured as explained in 1.4.4 System States.

1.4.1
Chip Configuration Summary
The MCU can operate in six different modes associated with resource configuration. The different modes,
the state of ROMCTL and EROMCTL signal on rising edge of RESET and the security state of the MCU
affect the following device characteristics:
• External bus interface configuration
• Flash in memory map, or not
• Debug features enabled or disabled
The operating mode out of reset is determined by the states of the MODC, MODB, and MODA signals
during reset (see Table 1-12). The MODC, MODB, and MODA bits in the MODE register show the current
operating mode and provide limited mode switching during operation. The states of the MODC, MODB,
and MODA signals are latched into these bits on the rising edge of RESET.
In normal expanded mode and in emulation modes the ROMON bit and the EROMON bit in the
MMCCTL1 register defines if the on chip flash memory is the memory map, or not. (See Table 1-12.) For
a detailed explanation of the ROMON and EROMON bits refer to the MMC description.

MC9S12XE-Family Reference Manual Rev. 1.25
76
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
The state of the ROMCTL signal is latched into the ROMON bit in the MMCCTL1 register on the rising
edge of RESET. The state of the EROMCTL signal is latched into the EROMON bit in the MMCCTL1
register on the rising edge of RESET.
Table 1-12. Chip Modes and Data Sources
Chip Modes
Data Source(1)
MODC
MODB
MODA
ROMCTL
EROMCTL
Normal single chip
1
0
0
X
X
Internal
Special single chip
0
0
0
Emulation single chip
0
0
1
X
0
Emulation memory
X
1
Internal Flash
Normal expanded
Emulation expanded
Special test
1
0
0
0
1
1
1
1
0
0
X
External application
1
X
Internal Flash
0
X
External application
1
0
Emulation memory
1
1
Internal Flash
0
X
External application
1
X
Internal Flash
1. Internal means resources inside the MCU are read/written.
Internal Flash means Flash resources inside the MCU are read/written.
Emulation memory means resources inside the emulator are read/written (PRU registers, Flash replacement, RAM, EEPROM,
and register space are always considered internal).
External application means resources residing outside the MCU are read/written.

*******
Normal Expanded Mode
Ports K, A, and B are configured as a 23-bit address bus, ports C and D are configured as a 16-bit data bus,
and port E provides bus control and status signals. This mode allows 16-bit external memory and
peripheral devices to be interfaced to the system. The fastest external bus rate is divide by 2 from the
internal bus rate.

*******
Normal Single-Chip Mode
There is no external bus in this mode. The processor program is executed from internal memory. Ports A,
B,C,D, K, and most pins of port E are available as general-purpose I/O.

*******
Special Single-Chip Mode
This mode is used for debugging single-chip operation, boot-strapping, or security related operations. The
background debug module BDM is active in this mode. The CPU executes a monitor program located in
an on-chip ROM. BDM firmware waits for additional serial commands through the BKGD pin. There is
no external bus after reset in this mode.

*******
Emulation of Expanded Mode
Developers use this mode for emulation systems in which the users target application is normal expanded
mode. Code is executed from external memory or from internal memory depending on the state of
ROMON and EROMON bit. In this mode the internal operation is visible on external bus interface.
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
77
Chapter 1 Device Overview MC9S12XE-Family
*******
Emulation of Single-Chip Mode
Developers use this mode for emulation systems in which the user’s target application is normal singlechip mode. Code is executed from external memory or from internal memory depending on the state of
ROMON and EROMON bit. In this mode the internal operation is visible on external bus interface.

*******
Special Test Mode
This is for Freescale internal use only.

1.4.2
Power Modes
The MCU features two main low-power modes. Consult the respective module description for module
specific behavior in system stop, system pseudo stop, and system wait mode. An important source of
information about the clock system is the Clock and Reset Generator description (CRG).

*******
System Stop Modes
The system stop modes are entered if the CPU executes the STOP instruction unless either the XGATE is
active or an NVM command is active. The XGATE is active if it executes a thread or the XGFACT bit in
the XGMCTL register is set. Depending on the state of the PSTP bit in the CLKSEL register the MCU
goes into pseudo stop mode or full stop mode. Please refer to CRG description. Asserting RESET, XIRQ,
IRQ or any other interrupt that is not masked exits system stop modes. System stop modes can be exited
by XGATE or CPU activity independently, depending on the configuration of the interrupt request. If
System-Stop is exited on an XGATE request then, as long as the XGATE does not set an interrupt flag on
the CPU and the XGATE fake activity bit (FACT) remains cleared, once XGATE activity is completed
System Stop mode will automatically be re-entered.
If the CPU executes the STOP instruction whilst XGATE is active or an NVM command is being
processed, then the system clocks continue running until XGATE/NVM activity is completed. If a nonmasked interrupt occurs within this time then the system does not effectively enter stop mode although the
STOP instruction has been executed.

*******
Full Stop Mode
The oscillator is stopped in this mode. By default all clocks are switched off and all counters and dividers
remain frozen. The Autonomous Periodic Interrupt (API) and ATD modules may be enabled to self wake
the device. A Fast wake up mode is available to allow the device to wake from Full Stop mode immediately
on the PLL internal clock without starting the oscillator clock.

*******
Pseudo Stop Mode
In this mode the system clocks are stopped but the oscillator is still running and the real time interrupt
(RTI) and watchdog (COP), API and ATD modules may be enabled. Other peripherals are turned off. This
mode consumes more current than system stop mode but, as the oscillator continues to run, the full speed
wake up time from this mode is significantly shorter.

MC9S12XE-Family Reference Manual Rev. 1.25
78
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
*******
XGATE Fake Activity Mode
This mode is entered if the CPU executes the STOP instruction when the XGATE is not executing a thread
and the XGFACT bit in the XGMCTL register is set. The oscillator remains active and any enabled
peripherals continue to function.

*******
Wait Mode
This mode is entered when the CPU executes the WAI instruction. In this mode the CPU will not execute
instructions. The internal CPU clock is switched off. All peripherals and the XGATE can be active in
system wait mode. For further power consumption the peripherals can individually turn off their local
clocks. Asserting RESET, XIRQ, IRQ or any other interrupt that is not masked and is not routed to XGATE
ends system wait mode.

*******
Run Mode
Although this is not a low-power mode, unused peripheral modules should not be enabled in order to save
power.

1.4.3
Freeze Mode
The enhanced capture timer, pulse width modulator, analog-to-digital converters, and the periodic interrupt
timer provide a software programmable option to freeze the module status when the background debug
module is active. This is useful when debugging application software. For detailed description of the
behavior of the ATD0, ATD1, ECT, PWM, and PIT when the background debug module is active consult
the corresponding Block Guides.

1.4.4
System States
To facilitate system integrity the MCU can run in Supervisor state or User state. The System States strategy
is implemented by additional features on the S12X CPU and a Memory Protection Unit. This is designed
to support restricted access for code modules executed by kernels or operating systems supporting access
control to system resources.
The current system state is indicated by the U bit in the CPU condition code register. In User state certain
CPU instructions are restricted. See the CPU reference guide for details of the U bit and of those
instructions affected by User state.
In the case that software task accesses resources outside those defined for it in the MPU a non-maskable
interrupt is generated.

*******
Supervisor State
This state is intended for configuring the MPU for different tasks that are then executed in User state,
returning to Supervisor state on completion of each task. This is the default ’state’ following reset and can
be re-entered from User state by an exception (interrupt). If the SVSEN bit in the MPUSEL register of the
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
79
Chapter 1 Device Overview MC9S12XE-Family
MPU is set, access to system resources is only allowed if enabled by a memory range descriptor as defined
in the Memory Protection Unit (MPU) description.

*******
User State
This state is intended for carrying out system tasks and is entered by setting the U bit of the condition codes
register while in Supervisor state. Restrictions apply for the execution of several CPU instructions in User
state and access to system resources is only allowed in if enabled by a memory range descriptor as defined
in the Memory Protection Unit (MPU) description.

1.5
Security
The MCU security feature allows the protection of the on chip Flash and emulated EEPROM memory. For
a detailed description of the security features refer to the S12X9SEC description.

1.6
Resets and Interrupts
Consult the S12XCPU manual and the S12XINT description for information on exception processing.

1.6.1
Resets
Resets are explained in detail in the Clock Reset Generator (CRG) description.
Table 1-13. Reset Sources and Vector Locations
1.6.2
Vector Address
Reset Source
CCR
Mask
Local Enable
$FFFE
Power-On Reset (POR)
None
None
$FFFE
Low Voltage Reset (LVR)
None
None
$FFFE
External pin RESET
None
None
$FFFE
Illegal Address Reset
None
None
$FFFC
Clock monitor reset
None
PLLCTL (CME, SCME)
$FFFA
COP watchdog reset
None
COP rate select
Vectors
Table 1-14 lists all interrupt sources and vectors in the default order of priority. The interrupt module
(S12XINT) provides an interrupt vector base register (IVBR) to relocate the vectors. Associated with each
I-bit maskable service request is a configuration register. It selects if the service request is enabled, the
service request priority level and whether the service request is handled either by the S12X CPU or by the
XGATE module.

MC9S12XE-Family Reference Manual Rev. 1.25
80
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
Table 1-14. Interrupt Vector Locations (Sheet 1 of 4)
Vector Address(1)
XGATE
Channel
ID(2)
Interrupt Source
CCR
Mask
Local Enable
Vector base + $F8
—
Unimplemented instruction trap
None
None
—
—
Vector base+ $F6
—
SWI
None
None
—
—
Vector base+ $F4
—
XIRQ
X Bit
None
Yes
Yes
Vector base+ $F2
—
IRQ
I bit
IRQCR (IRQEN)
Yes
Yes
Vector base+ $F0
$78
Real time interrupt
I bit
CRGINT (RTIE)
Vector base+ $EE
$77
Enhanced capture timer channel 0
I bit
TIE (C0I)
No
Yes
Vector base + $EC
$76
Enhanced capture timer channel 1
I bit
TIE (C1I)
No
Yes
Vector base+ $EA
$75
Enhanced capture timer channel 2
I bit
TIE (C2I)
No
Yes
Vector base+ $E8
$74
Enhanced capture timer channel 3
I bit
TIE (C3I)
No
Yes
Vector base+ $E6
$73
Enhanced capture timer channel 4
I bit
TIE (C4I)
No
Yes
Vector base+ $E4
$72
Enhanced capture timer channel 5
I bit
TIE (C5I)
No
Yes
Vector base + $E2
$71
Enhanced capture timer channel 6
I bit
TIE (C6I)
No
Yes
Vector base+ $E0
$70
Enhanced capture timer channel 7
I bit
TIE (C7I)
No
Yes
Vector base+ $DE
$6F
Enhanced capture timer overflow
I bit
TSRC2 (TOF)
No
Yes
Vector base+ $DC
$6E
Pulse accumulator A overflow
I bit
PACTL (PAOVI)
No
Yes
Vector base + $DA
$6D
Pulse accumulator input edge
I bit
PACTL (PAI)
No
Yes
Vector base + $D8
$6C
SPI0
I bit
SPI0CR1
(SPIE, SPTIE)
No
Yes
Vector base+ $D6
$6B
SCI0
I bit
SCI0CR2
(TIE, TCIE, RIE, ILIE)
Yes
Yes
Vector base + $D4
$6A
SCI1
I bit
SCI1CR2
(TIE, TCIE, RIE, ILIE)
Yes
Yes
Vector base + $D2
$69
ATD0
I bit
ATD0CTL2 (ASCIE)
Yes
Yes
Vector base + $D0
$68
ATD1
I bit
ATD1CTL2 (ASCIE)
Yes
Yes
Vector base + $CE
$67
Port J
I bit
PIEJ (PIEJ7-PIEJ0)
Yes
Yes
Vector base + $CC
$66
Port H
I bit
PIEH (PIEH7-PIEH0)
Yes
Yes
Vector base + $CA
$65
Modulus down counter underflow
I bit
MCCTL(MCZI)
No
Yes
Vector base + $C8
$64
Pulse accumulator B overflow
I bit
PBCTL(PBOVI)
No
Yes
Vector base + $C6
$63
CRG PLL lock
I bit
CRGINT(LOCKIE)
Refer to CRG
interrupt section
Vector base + $C4
$62
CRG self-clock mode
I bit
CRGINT (SCMIE)
Refer to CRG
interrupt section
Vector base + $C2
$61
SCI6
I bit
SCI6CR2
(TIE, TCIE, RIE, ILIE)
Yes
Yes
Vector base + $C0
$60
IIC0 bus
I bit
IBCR0 (IBIE)
No
Yes
STOP
WAIT
Wake up Wake up
Refer to CRG
interrupt section
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
81
Chapter 1 Device Overview MC9S12XE-Family
Table 1-14. Interrupt Vector Locations (Sheet 2 of 4)
Vector Address(1)
XGATE
Channel
ID(2)
Interrupt Source
CCR
Mask
Vector base + $BE
$5F
SPI1
I bit
SPI1CR1 (SPIE,
SPTIE)
No
Yes
Vector base + $BC
$5E
SPI2
I bit
SPI2CR1 (SPIE,
SPTIE)
No
Yes
Vector base + $BA
$5D
FLASH Fault Detect
I bit
FCNFG2 (FDIE)
No
No
Vector base + $B8
$5C
FLASH
I bit
FCNFG (CCIE, CBEIE)
No
Yes
Vector base + $B6
$5B
CAN0 wake-up
I bit
CAN0RIER (WUPIE)
Yes
Yes
Vector base + $B4
$5A
CAN0 errors
I bit
CAN0RIER (CSCIE,
OVRIE)
No
Yes
Vector base + $B2
$59
CAN0 receive
I bit
CAN0RIER (RXFIE)
No
Yes
Vector base + $B0
$58
CAN0 transmit
I bit
CAN0TIER
(TXEIE[2:0])
No
Yes
Vector base + $AE
$57
CAN1 wake-up
I bit
CAN1RIER (WUPIE)
Yes
Yes
Vector base + $AC
$56
CAN1 errors
I bit
CAN1RIER (CSCIE,
OVRIE)
No
Yes
Vector base + $AA
$55
CAN1 receive
I bit
CAN1RIER (RXFIE)
No
Yes
Vector base + $A8
$54
CAN1 transmit
I bit
CAN1TIER
(TXEIE[2:0])
No
Yes
Vector base + $A6
$53
CAN2 wake-up
I bit
CAN2RIER (WUPIE)
Yes
Yes
Vector base + $A4
$52
CAN2 errors
I bit
CAN2RIER
(CSCIE, OVRIE)
No
Yes
Vector base + $A2
$51
CAN2 receive
I bit
CAN2RIER (RXFIE)
No
Yes
Vector base + $A0
$50
CAN2 transmit
I bit
CAN2TIER
(TXEIE[2:0])
No
Yes
Vector base + $9E
$4F
CAN3 wake-up
I bit
CAN3RIER (WUPIE)
Yes
Yes
Vector base+ $9C
$4E
CAN3 errors
I bit
CAN3RIER (CSCIE,
OVRIE)
No
Yes
Vector base+ $9A
$4D
CAN3 receive
I bit
CAN3RIER (RXFIE)
No
Yes
Vector base + $98
$4C
CAN3 transmit
I bit
CAN3TIER
(TXEIE[2:0])
No
Yes
Vector base + $96
$4B
CAN4 wake-up
I bit
CAN4RIER (WUPIE)
Yes
Yes
Vector base + $94
$4A
CAN4 errors
I bit
CAN4RIER (CSCIE,
OVRIE)
No
Yes
Vector base + $92
$49
CAN4 receive
I bit
CAN4RIER (RXFIE)
No
Yes
Vector base + $90
$48
CAN4 transmit
I bit
CAN4TIER
(TXEIE[2:0])
No
Yes
Vector base + $8E
$47
Port P Interrupt
I bit
PIEP (PIEP7-PIEP0)
Yes
Yes
Vector base+ $8C
$46
PWM emergency shutdown
I bit
PWMSDN (PWMIE)
No
Yes
STOP
WAIT
Wake up Wake up
Local Enable
MC9S12XE-Family Reference Manual Rev. 1.25
82
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
Table 1-14. Interrupt Vector Locations (Sheet 3 of 4)
Vector Address(1)
XGATE
Channel
ID(2)
Interrupt Source
CCR
Mask
Vector base + $8A
$45
SCI2
I bit
SCI2CR2
(TIE, TCIE, RIE, ILIE)
Yes
Yes
Vector base + $88
$44
SCI3
I bit
SCI3CR2
(TIE, TCIE, RIE, ILIE)
Yes
Yes
Vector base + $86
$43
SCI4
I bit
SCI4CR2
(TIE, TCIE, RIE, ILIE)
Yes
Yes
Vector base + $84
$42
SCI5
I bit
SCI5CR2
(TIE, TCIE, RIE, ILIE)
Yes
Yes
Vector base + $82
$41
IIC1 Bus
I bit
IBCR (IBIE)
No
Yes
Vector base + $80
$40
Low-voltage interrupt (LVI)
I bit
VREGCTRL (LVIE)
No
Yes
Vector base + $7E
$3F
Autonomous periodical interrupt (API)
I bit
VREGAPICTRL (APIE)
Yes
Yes
Vector base + $7C
—
High Temperature Interrupt
I bit
VREGHTCL (HTIE)
No
Yes
Vector base + $7A
$3D
Periodic interrupt timer channel 0
I bit
PITINTE (PINTE0)
No
Yes
Vector base + $78
$3C
Periodic interrupt timer channel 1
I bit
PITINTE (PINTE1)
No
Yes
Vector base + $76
$3B
Periodic interrupt timer channel 2
I bit
PITINTE (PINTE2)
No
Yes
Vector base + $74
$3A
Periodic interrupt timer channel 3
I bit
PITINTE (PINTE3)
No
Yes
Vector base + $72
$39
XGATE software trigger 0
I bit
XGMCTL (XGIE)
No
Yes
Vector base + $70
$38
XGATE software trigger 1
I bit
XGMCTL (XGIE)
No
Yes
Vector base + $6E
$37
XGATE software trigger 2
I bit
XGMCTL (XGIE)
No
Yes
Vector base + $6C
$36
XGATE software trigger 3
I bit
XGMCTL (XGIE)
No
Yes
Vector base + $6A
$35
XGATE software trigger 4
I bit
XGMCTL (XGIE)
No
Yes
Vector base + $68
$34
XGATE software trigger 5
I bit
XGMCTL (XGIE)
No
Yes
Vector base + $66
$33
XGATE software trigger 6
I bit
XGMCTL (XGIE)
No
Yes
Vector base + $64
$32
XGATE software trigger 7
I bit
XGMCTL (XGIE)
No
Yes
Vector base + $62
Reserved
Vector base + $60
Reserved
Local Enable
STOP
WAIT
Wake up Wake up
Vector base + $5E
$2F
Periodic interrupt timer channel 4
I bit
PITINTE (PINTE4)
No
Yes
Vector base + $5C
$2E
Periodic interrupt timer channel 5
I bit
PITINTE (PINTE5)
No
Yes
Vector base + $5A
$2D
Periodic interrupt timer channel 6
I bit
PITINTE (PINTE6)
No
Yes
Vector base + $58
$2C
Periodic interrupt timer channel 7
I bit
PITINTE (PINTE7)
No
Yes
Vector base + $56
$2B
SCI7
I bit
SCI7CR2
(TIE, TCIE, RIE, ILIE)
Yes
Yes
Vector base + $54
$2A
TIM timer channel 0
I bit
TIE (C0I)
No
Yes
Vector base + $52
$29
TIM timer channel 1
I bit
TIE (C1I)
No
Yes
Vector base + $50
$28
TIM timer channel 2
I bit
TIE (C2I)
No
Yes
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
83
Chapter 1 Device Overview MC9S12XE-Family
Table 1-14. Interrupt Vector Locations (Sheet 4 of 4)
Vector Address(1)
XGATE
Channel
ID(2)
Interrupt Source
CCR
Mask
Local Enable
Vector base+ $4E
$27
TIM timer channel 3
I bit
TIE (C3I)
No
Yes
Vector base + $4C
$26
TIM timer channel 4
I bit
TIE (C4I)
No
Yes
Vector base+ $4A
$25
TIM timer channel 5
I bit
TIE (C5I)
No
Yes
Vector base+ $48
$24
TIM timer channel 6
I bit
TIE (C6I)
No
Yes
Vector base+ $46
$23
TIM timer channel 7
I bit
TIE (C7I)
No
Yes
Vector base+ $44
$22
TIM timer overflow
I bit
TSRC2 (TOF)
No
Yes
Vector base + $42
$21
TIM Pulse accumulator A overflow
I bit
PACTL (PAOVI)
No
Yes
Vector base+ $40
$20
TIM Pulse accumulator input edge
I bit
PACTL (PAI)
No
Yes
Vector base + $3E
$1F
ATD0 Compare Interrupt
I bit
ATD0CTL2 (ACMPIE)
Yes
Yes
Vector base + $3C
$1E
ATD1 Compare Interrupt
I bit
ATD1CTL2 (ACMPIE)
Yes
Yes
Vector base+ $18
to
Vector base + $3A
STOP
WAIT
Wake up Wake up
Reserved
Vector base + $16
—
XGATE software error interrupt
None
None
No
Yes
Vector base + $14
—
MPU Access Error
None
None
No
No
Vector base + $12
—
System Call Interrupt (SYS)
—
None
—
—
Vector base + $10
—
Spurious interrupt
—
1. 16 bits vector address based
2. For detailed description of XGATE channel ID refer to XGATE Block Guide
None
—
—
1.6.3
Effects of Reset
When a reset occurs, MCU registers and control bits are initialized. Refer to the respective block
descriptions for register reset states.
On each reset, the Flash module executes a reset sequence to load Flash configuration registers and
initialize the buffer RAM EEE partition, if required.

*******
Flash Configuration Reset Sequence (Core Hold Phase)
On each reset, the Flash module will hold CPU activity while loading Flash module registers and
configuration from the Flash memory. The duration of this phase is given as tRST in the device electrical
parameter specification. If double faults are detected in the reset phase, Flash module protection and
security may be active on leaving reset. This is explained in more detail in the Flash module section.

*******
EEE Reset Sequence Phase (Core Active Phase)
During this phase of the reset sequence (following on from the core hold phase) the CPU can execute
instructions while the FTM initialization completes and, if configured for EEE operation, the EEE RAM
MC9S12XE-Family Reference Manual Rev. 1.25
84
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
is loaded with valid data from the D-Flash EEE partition. Completion of this phase is indicated by the
CCIF flag in the FTM FSTAT register becoming set. If the CPU accesses any EEE RAM location before
the CCIF flag is set, the CPU is stalled until the FTM reset sequence is complete and the EEE RAM data
is valid. Once the CCIF flag is set, indicating the end of this phase, the EEE RAM can be accessed without
impacting the CPU and FTM commands can be executed.

*******
Reset While Flash Command Active
If a reset occurs while any Flash command is in progress, that command will be immediately aborted. The
state of the word being programmed or the sector/block being erased is not guaranteed.

*******
I/O Pins
Refer to the PIM block description for reset configurations of all peripheral module ports.

*******
Memory
The RAM arrays are not initialized out of reset.

*******
COP Configuration
The COP timeout rate bits CR[2:0] and the WCOP bit in the COPCTL register are loaded on rising edge
of RESET from the Flash register FOPT. See Table 1-15 and Table 1-16 for coding. The FOPT register is
loaded from the Flash configuration field byte at global address $7FFF0E during the reset sequence.
If the MCU is secured the COP timeout rate is always set to the longest period (CR[2:0] = 111) after COP
reset.
Table 1-15. Initial COP Rate Configuration
NV[2:0] in
FOPT Register
CR[2:0] in
COPCTL Register
000
111
001
110
010
101
011
100
100
011
101
010
110
001
111
000
Table 1-16. Initial WCOP Configuration
NV[3] in
FOPT Register
WCOP in
COPCTL Register
1
0
0
1
MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
85
Chapter 1 Device Overview MC9S12XE-Family
1.7
1.7.1
ADC0 Configuration
External Trigger Input Connection
The ADC module includes four external trigger inputs ETRIG0, ETRIG1, ETRIG2, and ETRIG3. The
external trigger allows the user to synchronize ADC conversion to external trigger events. Table 1-17
shows the connection of the external trigger inputs.
Table 1-17. ATD0 External Trigger Sources
External Trigger
Input
Connectivity
ETRIG0
Pulse width modulator channel 1
ETRIG1
Pulse width modulator channel 3
ETRIG2
Periodic interrupt timer hardware trigger 0
ETRIG3
Periodic interrupt timer hardware trigger 1
Consult the ATD block description for information about the analog-to-digital converter module. ATD
block description refererences to freeze mode are equivalent to active BDM mode.

1.7.2
ADC0 Channel[17] Connection
Further to the 16 externally available channels, ADC0 features an extra channel[17] that is connected to
the internal temperature sensor at device level. To access this channel ADC0 must use the channel
encoding SC:CD:CC:CB:CA = 1:0:0:0:1 in ATDCTL5. For more temperature sensor information, please
refer to 1.10.1 Temperature Sensor Configuration
1.8
ADC1 External Trigger Input Connection
The ADC module includes four external trigger inputs ETRIG0, ETRIG1, ETRIG2, and ETRIG3. The
external trigger feature allows the user to synchronize ADC conversion to external trigger events. Table 118 shows the connection of the external trigger inputs.
Table 1-18. ATD1 External Trigger Sources
External Trigger
Input
Connectivity
ETRIG0
Pulse width modulator channel 1
ETRIG1
Pulse width modulator channel 3
ETRIG2
Periodic interrupt timer hardware trigger 0
ETRIG3
Periodic interrupt timer hardware trigger 1
Consult the ADC block description for information about the analog-to-digital converter module. ADC
block description refererences to freeze mode are equivalent to active BDM mode.
MC9S12XE-Family Reference Manual Rev. 1.25
86
Freescale Semiconductor
Chapter 1 Device Overview MC9S12XE-Family
1.9
MPU Configuration
The MPU has the option of a third bus master (CPU + XGATE + other) which is not present on this device
family but may be on other parts.

1.10
VREG Configuration
The VREGEN connection of the voltage regulator is tied internally to VDDR such that the voltage
regulator is always enabled with VDDR connected to a positive supply voltage. The device must be
configured with the internal voltage regulator enabled. Operation in conjunction with an external voltage
regulator is not supported.
The autonomous periodic interrupt clock output is mapped to PortT[5].
The API trimming register APITR is loaded on rising edge of RESET from the Flash IFR option field at
global address 0x40_00F0 bits[5:0] during the reset sequence. Currently factory programming of this IFR
range is not supported.

1.10.1
Temperature Sensor Configuration
The VREG high temperature trimming register bits VREGHTTR[3:0] are loaded from the internal Flash
during the reset sequence. To use the high temperature interrupt within the specified limits (THTIA and
THTID) these bits must be loaded with 0x8. Currently factory programming is not supported.
The device temperature can be monitored on ADC0 channel[17].
The internal bandgap reference voltage can also be mapped to ADC0 analog input channel[17]. The
voltage regulator VSEL bit when set, maps the bandgap and, when clear, maps the temperature sensor to
ADC0 channel[17].
Read access to reserved VREG register space returns “0”. Write accesses have no effect. This device does
not support access abort of reserved VREG register space.

1.11
BDM Clock Configuration
The BDM alternate clock source is the oscillator clock.

1.12
S12XEPIM Configuration
On smaller derivatives the S12XEPIM module is a subset of the S12XEP100. The registers of the
unavailable ports are unimplemented.

MC9S12XE-Family Reference Manual Rev. 1.25
Freescale Semiconductor
87
Chapter 1 Device Overview MC9S12XE-Family
1.13
Oscillator Configuration
The XCLKS is an input signal which controls whether a crystal in combination with the internal loop
controlled (low power) Pierce oscillator is used or whether full swing Pierce oscillator/external clock
circuitry is used. For this device XCLKS is mapped to PE7.
The XCLKS signal selects the oscillator configuration during reset low phase while a clock quality check
is ongoing. This is the case for:
• Power on reset or low-voltage reset
• Clock monitor reset
• Any reset while in self-clock mode or full stop mode
The selected oscillator configuration is frozen with the rising edge of the RESET pin in any of these above
described reset cases.

EXTAL
C1
MCU
Crystal or
Ceramic Resonator
XTAL
C2
VSSPLL
Figure 1-10. Loop Controlled Pierce Oscillator Connections (XCLKS = 1)
EXTAL
C1
MCU
RB
RS
Crystal or
Ceramic Resonator
XTAL
C2
RB=1MΩ ; RS specified by crystal vendor
VSSPLL
Figure 1-11. Full Swing Pierce Oscillator Connections (XCLKS = 0)
EXTAL
CMOS-Compatible
External Oscillator
MCU
XTAL
Not Connected
Figure 1-12. External Clock Connections (XCLKS = 0)
MC9S12XE-Family Reference Manual Rev. 1.25
88
Freescale Semiconductor
