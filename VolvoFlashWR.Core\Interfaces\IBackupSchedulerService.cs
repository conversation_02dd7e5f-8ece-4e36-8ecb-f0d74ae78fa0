using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for backup scheduler service
    /// </summary>
    public interface IBackupSchedulerService
    {
        /// <summary>
        /// Event triggered when a backup schedule is created
        /// </summary>
        event EventHandler<BackupSchedule> ScheduleCreated;

        /// <summary>
        /// Event triggered when a backup schedule is updated
        /// </summary>
        event EventHandler<BackupSchedule> ScheduleUpdated;

        /// <summary>
        /// Event triggered when a backup schedule is deleted
        /// </summary>
        event EventHandler<BackupSchedule> ScheduleDeleted;

        /// <summary>
        /// Event triggered when a scheduled backup starts
        /// </summary>
        event EventHandler<BackupSchedule> ScheduledBackupStarted;

        /// <summary>
        /// Event triggered when a scheduled backup completes
        /// </summary>
        event EventHandler<(BackupSchedule Schedule, BackupData Backup)> ScheduledBackupCompleted;

        /// <summary>
        /// Event triggered when an error occurs during scheduled backup
        /// </summary>
        event EventHandler<(BackupSchedule Schedule, string ErrorMessage)> ScheduledBackupError;

        /// <summary>
        /// Gets whether the scheduler is running
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// Gets the schedules directory path
        /// </summary>
        string SchedulesDirectoryPath { get; }

        /// <summary>
        /// Gets the list of backup schedules
        /// </summary>
        List<BackupSchedule> Schedules { get; }

        /// <summary>
        /// Initializes the backup scheduler service
        /// </summary>
        /// <param name="backupService">The backup service to use</param>
        /// <param name="ecuService">The ECU communication service to use</param>
        /// <returns>True if initialization is successful, false otherwise</returns>
        Task<bool> InitializeAsync(IBackupService backupService, IECUCommunicationService ecuService);

        /// <summary>
        /// Starts the scheduler
        /// </summary>
        /// <returns>True if the scheduler was started successfully, false otherwise</returns>
        Task<bool> StartAsync();

        /// <summary>
        /// Stops the scheduler
        /// </summary>
        /// <returns>True if the scheduler was stopped successfully, false otherwise</returns>
        Task<bool> StopAsync();

        /// <summary>
        /// Creates a new backup schedule
        /// </summary>
        /// <param name="schedule">The schedule to create</param>
        /// <returns>The created schedule, or null if creation fails</returns>
        Task<BackupSchedule?> CreateScheduleAsync(BackupSchedule schedule);

        /// <summary>
        /// Updates an existing backup schedule
        /// </summary>
        /// <param name="schedule">The schedule to update</param>
        /// <returns>The updated schedule, or null if update fails</returns>
        Task<BackupSchedule?> UpdateScheduleAsync(BackupSchedule schedule);

        /// <summary>
        /// Deletes a backup schedule
        /// </summary>
        /// <param name="scheduleId">The ID of the schedule to delete</param>
        /// <returns>True if deletion is successful, false otherwise</returns>
        Task<bool> DeleteScheduleAsync(string scheduleId);

        /// <summary>
        /// Gets a backup schedule by ID
        /// </summary>
        /// <param name="scheduleId">The ID of the schedule to get</param>
        /// <returns>The backup schedule, or null if not found</returns>
        Task<BackupSchedule?> GetScheduleAsync(string scheduleId);

        /// <summary>
        /// Gets all backup schedules
        /// </summary>
        /// <returns>List of all backup schedules</returns>
        Task<List<BackupSchedule>> GetAllSchedulesAsync();

        /// <summary>
        /// Gets backup schedules for a specific ECU
        /// </summary>
        /// <param name="ecuId">The ID of the ECU</param>
        /// <returns>List of backup schedules for the specified ECU</returns>
        Task<List<BackupSchedule>> GetSchedulesForECUAsync(string ecuId);

        /// <summary>
        /// Enables a backup schedule
        /// </summary>
        /// <param name="scheduleId">The ID of the schedule to enable</param>
        /// <returns>True if enabling is successful, false otherwise</returns>
        Task<bool> EnableScheduleAsync(string scheduleId);

        /// <summary>
        /// Disables a backup schedule
        /// </summary>
        /// <param name="scheduleId">The ID of the schedule to disable</param>
        /// <returns>True if disabling is successful, false otherwise</returns>
        Task<bool> DisableScheduleAsync(string scheduleId);

        /// <summary>
        /// Executes a backup schedule immediately
        /// </summary>
        /// <param name="scheduleId">The ID of the schedule to execute</param>
        /// <returns>The created backup data, or null if execution fails</returns>
        Task<BackupData?> ExecuteScheduleNowAsync(string scheduleId);

        /// <summary>
        /// Saves all schedules to disk
        /// </summary>
        /// <returns>True if saving is successful, false otherwise</returns>
        Task<bool> SaveSchedulesAsync();

        /// <summary>
        /// Loads all schedules from disk
        /// </summary>
        /// <returns>True if loading is successful, false otherwise</returns>
        Task<bool> LoadSchedulesAsync();

        /// <summary>
        /// Adds a new backup schedule
        /// </summary>
        /// <param name="schedule">The schedule to add</param>
        /// <returns>True if the schedule was added successfully, false otherwise</returns>
        Task<bool> AddScheduleAsync(BackupSchedule schedule);

        /// <summary>
        /// Executes a backup schedule immediately
        /// </summary>
        /// <param name="schedule">The schedule to execute</param>
        /// <returns>The created backup data, or null if execution fails</returns>
        Task<BackupData?> ExecuteScheduleAsync(BackupSchedule schedule);
    }
}
