@echo off
echo === Library Verification ===
echo Checking for critical Vocom libraries...

set MISSING=0

if not exist "Libraries\WUDFPuma.dll" (
    echo X WUDFPuma.dll missing
    set /a MISSING+=1
) else (
    echo + WUDFPuma.dll found
)

if not exist "Libraries\apci.dll" (
    echo X apci.dll missing
    set /a MISSING+=1
) else (
    echo + apci.dll found
)

if not exist "Libraries\Volvo.ApciPlus.dll" (
    echo X Volvo.ApciPlus.dll missing
    set /a MISSING+=1
) else (
    echo + Volvo.ApciPlus.dll found
)

if %MISSING% EQU 0 (
    echo.
    echo + All critical libraries are present!
    echo The application is ready for real hardware communication.
) else (
    echo.
    echo ! %MISSING% critical libraries are missing!
    echo The application may fall back to dummy mode.
)

echo.
echo Total DLL files in Libraries folder:
dir /b Libraries\*.dll | find /c ".dll"

pause
