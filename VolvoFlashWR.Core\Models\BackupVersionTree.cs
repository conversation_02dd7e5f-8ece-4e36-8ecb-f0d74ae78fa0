using System;
using System.Collections.Generic;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents a tree structure of backup versions
    /// </summary>
    public class BackupVersionTree
    {
        /// <summary>
        /// The root backup (first version)
        /// </summary>
        public BackupData RootBackup { get; set; }

        /// <summary>
        /// The latest version of the backup
        /// </summary>
        public BackupData LatestVersion { get; set; }

        /// <summary>
        /// All versions of the backup in a flat list
        /// </summary>
        public List<BackupData> AllVersions { get; set; } = new List<BackupData>();

        /// <summary>
        /// Tree structure of versions (parent-child relationships)
        /// </summary>
        public List<BackupVersionNode> VersionNodes { get; set; } = new List<BackupVersionNode>();

        /// <summary>
        /// Total number of versions
        /// </summary>
        public int VersionCount => AllVersions.Count;

        /// <summary>
        /// Timestamp of the first version
        /// </summary>
        public DateTime FirstVersionTime => RootBackup?.CreationTime ?? DateTime.MinValue;

        /// <summary>
        /// Timestamp of the latest version
        /// </summary>
        public DateTime LatestVersionTime => LatestVersion?.CreationTime ?? DateTime.MinValue;
    }

    /// <summary>
    /// Represents a node in the backup version tree
    /// </summary>
    public class BackupVersionNode
    {
        /// <summary>
        /// The backup data for this node
        /// </summary>
        public BackupData Backup { get; set; }

        /// <summary>
        /// Parent node (null for root)
        /// </summary>
        public BackupVersionNode Parent { get; set; }

        /// <summary>
        /// Child nodes (newer versions)
        /// </summary>
        public List<BackupVersionNode> Children { get; set; } = new List<BackupVersionNode>();

        /// <summary>
        /// Depth in the version tree (0 for root)
        /// </summary>
        public int Depth { get; set; }

        /// <summary>
        /// Whether this is a branch point (has multiple children)
        /// </summary>
        public bool IsBranchPoint => Children.Count > 1;

        /// <summary>
        /// Whether this is a leaf node (no children)
        /// </summary>
        public bool IsLeaf => Children.Count == 0;

        /// <summary>
        /// Whether this is the root node (no parent)
        /// </summary>
        public bool IsRoot => Parent == null;
    }
}
