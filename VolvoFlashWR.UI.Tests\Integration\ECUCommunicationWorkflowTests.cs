using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.ECU;
using VolvoFlashWR.Communication.Vocom;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Services;
using VolvoFlashWR.UI.Tests.Helpers;

namespace VolvoFlashWR.UI.Tests.Integration
{
    [TestFixture]
    public class ECUCommunicationWorkflowTests
    {
        private ILoggingService _loggingService;
        private IVocomService _vocomService;
        private IECUCommunicationService _ecuCommunicationService;

        [SetUp]
        public async Task Setup()
        {
            // Set up the test environment
            TestHelper.SetupTestEnvironment();

            // Create logging service
            _loggingService = new LoggingService();
            await _loggingService.InitializeAsync(Path.Combine(Path.GetTempPath(), "VolvoFlashWR_Tests", "Logs"), true);

            // Create Vocom service
            var vocomFactory = new VocomServiceFactory(_loggingService);
            _vocomService = await vocomFactory.CreateServiceAsync();

            // Create ECU communication service
            var ecuFactory = new ECUCommunicationServiceFactory(_loggingService, _vocomService);
            _ecuCommunicationService = await ecuFactory.CreateServiceAsync();
        }

        [TearDown]
        public void TearDown()
        {
            // Clean up the test environment
            TestHelper.CleanupTestEnvironment();
        }

        [Test]
        [Ignore("Integration test requires actual hardware")]
        public async Task ScanAndConnectToECU_ValidECU_ConnectsSuccessfully()
        {
            // Arrange - Scan for Vocom devices
            var vocomDevices = await _vocomService.ScanForDevicesAsync();
            Assert.That(vocomDevices, Has.Count.GreaterThan(0), "No Vocom devices found");

            // Connect to the first Vocom device
            bool vocomConnected = await _vocomService.ConnectAsync(vocomDevices[0]);
            Assert.That(vocomConnected, Is.True, "Failed to connect to Vocom device");

            // Act - Scan for ECUs
            var ecus = await _ecuCommunicationService.ScanForECUsAsync();
            Assert.That(ecus, Has.Count.GreaterThan(0), "No ECUs found");

            // Connect to the first ECU
            bool ecuConnected = await _ecuCommunicationService.ConnectToECUAsync(ecus[0]);

            // Assert
            Assert.That(ecuConnected, Is.True, "Failed to connect to ECU");
            Assert.That(ecus[0].ConnectionStatus, Is.EqualTo(ECUConnectionStatus.Connected), "ECU connection status is not Connected");
            Assert.That(_ecuCommunicationService.ConnectedECUs, Has.Count.EqualTo(1), "Connected ECUs count is incorrect");

            // Cleanup
            await _ecuCommunicationService.DisconnectFromECUAsync(ecus[0]);
            await _vocomService.DisconnectAsync();
        }

        [Test]
        [Ignore("Integration test requires actual hardware")]
        public async Task ReadEEPROMAndFlash_ValidECU_ReadsDataSuccessfully()
        {
            // Arrange - Scan for ECUs
            var ecus = await _ecuCommunicationService.ScanForECUsAsync();
            Assert.That(ecus, Has.Count.GreaterThan(0), "No ECUs found");

            // Connect to the first ECU
            var ecu = ecus[0];
            bool connected = await _ecuCommunicationService.ConnectToECUAsync(ecu);
            Assert.That(connected, Is.True, "Failed to connect to ECU");

            // Act - Read EEPROM
            var eepromData = await _ecuCommunicationService.ReadEEPROMAsync(ecu);

            // Assert
            Assert.That(eepromData, Is.Not.Null, "EEPROM data is null");
            Assert.That(eepromData.Length, Is.GreaterThan(0), "EEPROM data is empty");
            Assert.That(eepromData.Length, Is.EqualTo(ecu.EEPROMSize), "EEPROM data size does not match ECU EEPROM size");

            // Act - Read microcontroller code
            var mcuCode = await _ecuCommunicationService.ReadMicrocontrollerCodeAsync(ecu);

            // Assert
            Assert.That(mcuCode, Is.Not.Null, "Microcontroller code is null");
            Assert.That(mcuCode.Length, Is.GreaterThan(0), "Microcontroller code is empty");
            Assert.That(mcuCode.Length, Is.EqualTo(ecu.FlashSize), "Microcontroller code size does not match ECU Flash size");

            // Cleanup
            await _ecuCommunicationService.DisconnectFromECUAsync(ecu);
        }

        [Test]
        [Ignore("Integration test requires actual hardware")]
        public async Task ReadFaultsAndParameters_ValidECU_ReadsDataSuccessfully()
        {
            // Arrange - Scan for ECUs
            var ecus = await _ecuCommunicationService.ScanForECUsAsync();
            Assert.That(ecus, Has.Count.GreaterThan(0), "No ECUs found");

            // Connect to the first ECU
            var ecu = ecus[0];
            bool connected = await _ecuCommunicationService.ConnectToECUAsync(ecu);
            Assert.That(connected, Is.True, "Failed to connect to ECU");

            // Act - Read active faults
            var activeFaults = await _ecuCommunicationService.ReadActiveFaultsAsync(ecu);

            // Assert
            Assert.That(activeFaults, Is.Not.Null, "Active faults list is null");

            // Act - Read inactive faults
            var inactiveFaults = await _ecuCommunicationService.ReadInactiveFaultsAsync(ecu);

            // Assert
            Assert.That(inactiveFaults, Is.Not.Null, "Inactive faults list is null");

            // Act - Read parameters
            var parameters = await _ecuCommunicationService.ReadParametersAsync(ecu);

            // Assert
            Assert.That(parameters, Is.Not.Null, "Parameters dictionary is null");
            Assert.That(parameters.Count, Is.GreaterThan(0), "Parameters dictionary is empty");

            // Act - Perform diagnostics
            var diagnosticData = await _ecuCommunicationService.PerformDiagnosticSessionAsync(ecu);

            // Assert
            Assert.That(diagnosticData, Is.Not.Null, "Diagnostic data is null");
            Assert.That(diagnosticData.ECUId, Is.EqualTo(ecu.Id), "Diagnostic data ECU ID does not match");
            Assert.That(diagnosticData.Parameters, Is.Not.Null, "Diagnostic data parameters is null");
            Assert.That(diagnosticData.ActiveFaults, Is.Not.Null, "Diagnostic data active faults is null");
            Assert.That(diagnosticData.InactiveFaults, Is.Not.Null, "Diagnostic data inactive faults is null");

            // Cleanup
            await _ecuCommunicationService.DisconnectFromECUAsync(ecu);
        }

        [Test]
        [Ignore("Integration test requires actual hardware")]
        public async Task RefreshECU_ValidECU_RefreshesSuccessfully()
        {
            // Arrange - Scan for ECUs
            var ecus = await _ecuCommunicationService.ScanForECUsAsync();
            Assert.That(ecus, Has.Count.GreaterThan(0), "No ECUs found");

            // Connect to the first ECU
            var ecu = ecus[0];
            bool connected = await _ecuCommunicationService.ConnectToECUAsync(ecu);
            Assert.That(connected, Is.True, "Failed to connect to ECU");

            // Act - Refresh the ECU
            bool refreshed = await _ecuCommunicationService.RefreshECUAsync(ecu);

            // Assert
            Assert.That(refreshed, Is.True, "Failed to refresh ECU");
            Assert.That(ecu.ConnectionStatus, Is.EqualTo(ECUConnectionStatus.Connected), "ECU connection status is not Connected");

            // Cleanup
            await _ecuCommunicationService.DisconnectFromECUAsync(ecu);
        }
    }
}

