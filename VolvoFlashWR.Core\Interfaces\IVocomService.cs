using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for Vocom communication service
    /// </summary>
    public interface IVocomService
    {
        /// <summary>
        /// Event triggered when a Vocom device is connected
        /// </summary>
        event EventHandler<VocomDevice>? VocomConnected;

        /// <summary>
        /// Event triggered when a Vocom device is disconnected
        /// </summary>
        event EventHandler<VocomDevice>? VocomDisconnected;

        /// <summary>
        /// Event triggered when an error occurs during Vocom communication
        /// </summary>
        event EventHandler<string>? VocomError;

        /// <summary>
        /// Gets the currently connected Vocom device
        /// </summary>
        VocomDevice? CurrentDevice { get; }

        /// <summary>
        /// Gets the current connection settings
        /// </summary>
        ConnectionSettings ConnectionSettings { get; }

        /// <summary>
        /// Initializes the Vocom service
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        Task<bool> InitializeAsync();

        /// <summary>
        /// Scans for available Vocom devices
        /// </summary>
        /// <returns>List of available Vocom devices</returns>
        Task<List<VocomDevice>> ScanForDevicesAsync();

        /// <summary>
        /// Connects to a Vocom device
        /// </summary>
        /// <param name="device">The device to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        Task<bool> ConnectAsync(VocomDevice device);

        /// <summary>
        /// Disconnects from the currently connected Vocom device
        /// </summary>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        Task<bool> DisconnectAsync();

        /// <summary>
        /// Checks if PTT application is running and disconnects it if necessary
        /// </summary>
        /// <returns>True if PTT is successfully disconnected or not running, false otherwise</returns>
        Task<bool> DisconnectPTTAsync();

        /// <summary>
        /// Checks if Bluetooth is enabled
        /// </summary>
        /// <returns>True if Bluetooth is enabled, false otherwise</returns>
        Task<bool> IsBluetoothEnabledAsync();

        /// <summary>
        /// Enables Bluetooth if it is disabled
        /// </summary>
        /// <returns>True if Bluetooth is successfully enabled, false otherwise</returns>
        Task<bool> EnableBluetoothAsync();

        /// <summary>
        /// Checks if the connected ECU is a MC9S12XEP100 microcontroller
        /// </summary>
        /// <param name="ecuId">The ID of the ECU to check</param>
        /// <returns>True if the ECU is a MC9S12XEP100, false otherwise</returns>
        Task<bool> IsMC9S12XEP100ECUAsync(string ecuId);

        /// <summary>
        /// Checks if WiFi is available for connection
        /// </summary>
        /// <returns>True if WiFi is available, false otherwise</returns>
        Task<bool> IsWiFiAvailableAsync();

        /// <summary>
        /// Updates the connection settings
        /// </summary>
        /// <param name="settings">The new connection settings</param>
        void UpdateConnectionSettings(ConnectionSettings settings);

        /// <summary>
        /// Connects to the first available Vocom device
        /// </summary>
        /// <returns>True if connection is successful, false otherwise</returns>
        Task<bool> ConnectToFirstAvailableDeviceAsync();

        /// <summary>
        /// Reconnects to the last connected Vocom device
        /// </summary>
        /// <returns>True if reconnection is successful, false otherwise</returns>
        Task<bool> ReconnectAsync();

        /// <summary>
        /// Checks if PTT application is running
        /// </summary>
        /// <returns>True if PTT is running, false otherwise</returns>
        Task<bool> IsPTTRunningAsync();

        /// <summary>
        /// Sets the connection settings for the Vocom device
        /// </summary>
        /// <param name="settings">The connection settings to apply</param>
        /// <returns>True if settings were applied successfully, false otherwise</returns>
        Task<bool> SetConnectionSettings(VocomConnectionSettings settings);

        /// <summary>
        /// Reads the MC9S12XEP100 specific registers from an ECU
        /// </summary>
        /// <param name="ecuId">The ID of the ECU to read from</param>
        /// <returns>Dictionary of register names and values</returns>
        Task<Dictionary<string, object>> ReadMC9S12XEP100RegistersAsync(string ecuId);

        /// <summary>
        /// Sends a CAN frame to an ECU
        /// </summary>
        /// <param name="device">The Vocom device to use</param>
        /// <param name="canId">The CAN ID to use</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseTimeout">The timeout for waiting for a response in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendCANFrameAsync(VocomDevice device, uint canId, byte[] data, int responseTimeout);

        /// <summary>
        /// Sends data to the device and waits for a response
        /// </summary>
        /// <param name="data">The data to send</param>
        /// <param name="responseTimeout">The timeout for waiting for a response in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendAndReceiveDataAsync(byte[] data, int responseTimeout = 1000);

        /// <summary>
        /// Gets the current device asynchronously
        /// </summary>
        /// <returns>The current Vocom device</returns>
        Task<VocomDevice?> GetCurrentDeviceAsync();

        /// <summary>
        /// Sends SPI data to the device
        /// </summary>
        /// <param name="device">The Vocom device to use</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseTimeout">The timeout for waiting for a response in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendSPIDataAsync(VocomDevice device, byte[] data, int responseTimeout = 1000);

        /// <summary>
        /// Sends SCI data to the device
        /// </summary>
        /// <param name="device">The Vocom device to use</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseTimeout">The timeout for waiting for a response in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendSCIDataAsync(VocomDevice device, byte[] data, int responseTimeout = 1000);

        /// <summary>
        /// Sends IIC data to the device
        /// </summary>
        /// <param name="device">The Vocom device to use</param>
        /// <param name="address">The IIC device address</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseTimeout">The timeout for waiting for a response in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendIICDataAsync(VocomDevice device, byte address, byte[] data, int responseTimeout = 1000);
    }
}
