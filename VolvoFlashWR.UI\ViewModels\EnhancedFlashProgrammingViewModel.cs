using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Microsoft.Win32;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.UI.Commands;
using VolvoFlashWR.UI.Models;
using LogLevel = VolvoFlashWR.Core.Models.LogLevel;

namespace VolvoFlashWR.UI.ViewModels
{
    public class EnhancedFlashProgrammingViewModel : INotifyPropertyChanged
    {
        #region Private Fields

        private readonly ILoggingService _loggingService;
        private readonly IECUCommunicationService _ecuCommunicationService;
        private readonly IFlashOperationMonitorService _flashOperationMonitorService;
        private ECUDevice _selectedECUForFlash;
        private ObservableCollection<ECUDevice> _connectedECUs;
        private ObservableCollection<HexLine> _hexLines;
        private ObservableCollection<CoreLogEntry> _operationLogs;
        private string _filePath;
        private string _currentAddress;
        private string _selectedOperationType;
        private int _dataSize;
        private bool _isBusy;
        private string _currentOperation;
        private string _operationStatus;
        private int _operationProgress;
        private int _eepromSize;
        private int _mcuCodeSize;
        private int _flashMemorySize;
        private int _ramSize;
        private string _bootloaderVersion;
        private string _securityLevel;

        #endregion

        #region Properties

        public event PropertyChangedEventHandler? PropertyChanged;

        public ECUDevice SelectedECUForFlash
        {
            get => _selectedECUForFlash;
            set
            {
                _selectedECUForFlash = value;
                OnPropertyChanged(nameof(SelectedECUForFlash));
                UpdateECUDetails();
                UpdateCommandStates();
            }
        }

        public ObservableCollection<ECUDevice> ConnectedECUs
        {
            get => _connectedECUs;
            set
            {
                _connectedECUs = value;
                OnPropertyChanged(nameof(ConnectedECUs));
            }
        }

        public ObservableCollection<HexLine> HexLines
        {
            get => _hexLines;
            set
            {
                _hexLines = value;
                OnPropertyChanged(nameof(HexLines));
            }
        }

        public ObservableCollection<CoreLogEntry> OperationLogs
        {
            get => _operationLogs;
            set
            {
                _operationLogs = value;
                OnPropertyChanged(nameof(OperationLogs));
            }
        }

        public string FilePath
        {
            get => _filePath;
            set
            {
                _filePath = value;
                OnPropertyChanged(nameof(FilePath));
                UpdateCommandStates();
            }
        }

        public string CurrentAddress
        {
            get => _currentAddress;
            set
            {
                _currentAddress = value;
                OnPropertyChanged(nameof(CurrentAddress));
                UpdateCommandStates();
            }
        }

        public string SelectedOperationType
        {
            get => _selectedOperationType;
            set
            {
                _selectedOperationType = value;
                OnPropertyChanged(nameof(SelectedOperationType));
            }
        }

        public int DataSize
        {
            get => _dataSize;
            set
            {
                _dataSize = value;
                OnPropertyChanged(nameof(DataSize));
            }
        }

        public bool IsBusy
        {
            get => _isBusy;
            set
            {
                _isBusy = value;
                OnPropertyChanged(nameof(IsBusy));
                UpdateCommandStates();
            }
        }

        public string CurrentOperation
        {
            get => _currentOperation;
            set
            {
                _currentOperation = value;
                OnPropertyChanged(nameof(CurrentOperation));
            }
        }

        public string OperationStatus
        {
            get => _operationStatus;
            set
            {
                _operationStatus = value;
                OnPropertyChanged(nameof(OperationStatus));
            }
        }

        public int OperationProgress
        {
            get => _operationProgress;
            set
            {
                _operationProgress = value;
                OnPropertyChanged(nameof(OperationProgress));
            }
        }

        public int EEPROMSize
        {
            get => _eepromSize;
            set
            {
                _eepromSize = value;
                OnPropertyChanged(nameof(EEPROMSize));
            }
        }

        public int MCUCodeSize
        {
            get => _mcuCodeSize;
            set
            {
                _mcuCodeSize = value;
                OnPropertyChanged(nameof(MCUCodeSize));
            }
        }

        public int FlashMemorySize
        {
            get => _flashMemorySize;
            set
            {
                _flashMemorySize = value;
                OnPropertyChanged(nameof(FlashMemorySize));
            }
        }

        public int RAMSize
        {
            get => _ramSize;
            set
            {
                _ramSize = value;
                OnPropertyChanged(nameof(RAMSize));
            }
        }

        public string BootloaderVersion
        {
            get => _bootloaderVersion;
            set
            {
                _bootloaderVersion = value;
                OnPropertyChanged(nameof(BootloaderVersion));
            }
        }

        public string SecurityLevel
        {
            get => _securityLevel;
            set
            {
                _securityLevel = value;
                OnPropertyChanged(nameof(SecurityLevel));
            }
        }

        public List<string> OperationTypes => new List<string> { "EEPROM", "Microcontroller Code", "Flash Memory", "Parameters" };

        #endregion

        #region Commands

        public ICommand RefreshECUCommand { get; private set; }
        public ICommand ConnectToECUCommand { get; private set; }
        public ICommand DisconnectECUCommand { get; private set; }
        public ICommand ReadEEPROMCommand { get; private set; }
        public ICommand WriteEEPROMCommand { get; private set; }
        public ICommand ReadMicrocontrollerCodeCommand { get; private set; }
        public ICommand WriteMicrocontrollerCodeCommand { get; private set; }
        public ICommand BrowseFileCommand { get; private set; }
        public ICommand GoToAddressCommand { get; private set; }
        public ICommand SaveDataCommand { get; private set; }
        public ICommand CompareDataCommand { get; private set; }
        public ICommand CancelOperationCommand { get; private set; }

        #endregion

        #region Constructor

        public EnhancedFlashProgrammingViewModel(
            ILoggingService loggingService,
            IECUCommunicationService ecuCommunicationService,
            IFlashOperationMonitorService flashOperationMonitorService = null)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _ecuCommunicationService = ecuCommunicationService ?? throw new ArgumentNullException(nameof(ecuCommunicationService));
            _flashOperationMonitorService = flashOperationMonitorService;

            // Initialize collections
            ConnectedECUs = new ObservableCollection<ECUDevice>();
            HexLines = new ObservableCollection<HexLine>();
            OperationLogs = new ObservableCollection<CoreLogEntry>();

            // Initialize default values
            SelectedOperationType = OperationTypes.FirstOrDefault();
            CurrentAddress = "00000000";
            OperationStatus = "Ready";
            CurrentOperation = "None";

            // Initialize commands
            InitializeCommands();

            // Load connected ECUs
            LoadConnectedECUs();
        }

        #endregion

        #region Private Methods

        private void InitializeCommands()
        {
            RefreshECUCommand = new RelayCommand(
                _ => { RefreshECUListAsync().ConfigureAwait(false); },
                _ => !IsBusy);

            ConnectToECUCommand = new RelayCommand(
                _ => { ConnectToECUAsync().ConfigureAwait(false); },
                _ => !IsBusy && SelectedECUForFlash != null &&
                     SelectedECUForFlash.ConnectionStatus != ECUConnectionStatus.Connected);

            DisconnectECUCommand = new RelayCommand(
                _ => { DisconnectECUAsync().ConfigureAwait(false); },
                _ => !IsBusy && SelectedECUForFlash != null &&
                     SelectedECUForFlash.ConnectionStatus == ECUConnectionStatus.Connected);

            ReadEEPROMCommand = new RelayCommand(
                _ => { ReadEEPROMAsync().ConfigureAwait(false); },
                _ => !IsBusy && SelectedECUForFlash != null &&
                     SelectedECUForFlash.ConnectionStatus == ECUConnectionStatus.Connected);

            WriteEEPROMCommand = new RelayCommand(
                _ => { WriteEEPROMAsync().ConfigureAwait(false); },
                _ => !IsBusy && SelectedECUForFlash != null &&
                     SelectedECUForFlash.ConnectionStatus == ECUConnectionStatus.Connected &&
                     !string.IsNullOrEmpty(FilePath));

            ReadMicrocontrollerCodeCommand = new RelayCommand(
                _ => { ReadMicrocontrollerCodeAsync().ConfigureAwait(false); },
                _ => !IsBusy && SelectedECUForFlash != null &&
                     SelectedECUForFlash.ConnectionStatus == ECUConnectionStatus.Connected);

            WriteMicrocontrollerCodeCommand = new RelayCommand(
                _ => { WriteMicrocontrollerCodeAsync().ConfigureAwait(false); },
                _ => !IsBusy && SelectedECUForFlash != null &&
                     SelectedECUForFlash.ConnectionStatus == ECUConnectionStatus.Connected &&
                     !string.IsNullOrEmpty(FilePath));

            BrowseFileCommand = new RelayCommand(
                _ => { BrowseFile(); },
                _ => !IsBusy);

            GoToAddressCommand = new RelayCommand(
                _ => { GoToAddress(); },
                _ => !IsBusy && !string.IsNullOrEmpty(CurrentAddress) && HexLines.Count > 0);

            SaveDataCommand = new RelayCommand(
                _ => { SaveData(); },
                _ => !IsBusy && HexLines.Count > 0);

            CompareDataCommand = new RelayCommand(
                _ => { CompareData(); },
                _ => !IsBusy && HexLines.Count > 0);

            CancelOperationCommand = new RelayCommand(
                _ => { CancelOperation(); },
                _ => IsBusy);
        }

        private void UpdateCommandStates()
        {
            (RefreshECUCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (ConnectToECUCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (DisconnectECUCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (ReadEEPROMCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (WriteEEPROMCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (ReadMicrocontrollerCodeCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (WriteMicrocontrollerCodeCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (BrowseFileCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (GoToAddressCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (SaveDataCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (CompareDataCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (CancelOperationCommand as RelayCommand)?.RaiseCanExecuteChanged();
        }

        private void LoadConnectedECUs()
        {
            ConnectedECUs.Clear();

            if (_ecuCommunicationService.ConnectedECUs != null)
            {
                foreach (var ecu in _ecuCommunicationService.ConnectedECUs)
                {
                    ConnectedECUs.Add(ecu);
                }
            }

            // Select the first ECU if available
            SelectedECUForFlash = ConnectedECUs.FirstOrDefault();
        }

        private void UpdateECUDetails()
        {
            if (SelectedECUForFlash != null)
            {
                // Set default values for memory information
                EEPROMSize = 65536; // 64KB default
                MCUCodeSize = 1048576; // 1MB default
                FlashMemorySize = 2097152; // 2MB default
                RAMSize = 131072; // 128KB default
                BootloaderVersion = "Unknown";
                SecurityLevel = "Unknown";

                // If the ECU has properties, update the memory information
                if (SelectedECUForFlash.Properties != null)
                {
                    if (SelectedECUForFlash.Properties.TryGetValue("EEPROMSize", out object eepromSizeObj))
                    {
                        if (eepromSizeObj is int eepromSizeInt)
                        {
                            EEPROMSize = eepromSizeInt;
                        }
                        else if (eepromSizeObj is string eepromSizeStr && int.TryParse(eepromSizeStr, out int eepromSize))
                        {
                            EEPROMSize = eepromSize;
                        }
                    }

                    if (SelectedECUForFlash.Properties.TryGetValue("MCUCodeSize", out object mcuCodeSizeObj))
                    {
                        if (mcuCodeSizeObj is int mcuCodeSizeInt)
                        {
                            MCUCodeSize = mcuCodeSizeInt;
                        }
                        else if (mcuCodeSizeObj is string mcuCodeSizeStr && int.TryParse(mcuCodeSizeStr, out int mcuCodeSize))
                        {
                            MCUCodeSize = mcuCodeSize;
                        }
                    }

                    if (SelectedECUForFlash.Properties.TryGetValue("FlashMemorySize", out object flashMemorySizeObj))
                    {
                        if (flashMemorySizeObj is int flashMemorySizeInt)
                        {
                            FlashMemorySize = flashMemorySizeInt;
                        }
                        else if (flashMemorySizeObj is string flashMemorySizeStr && int.TryParse(flashMemorySizeStr, out int flashMemorySize))
                        {
                            FlashMemorySize = flashMemorySize;
                        }
                    }

                    if (SelectedECUForFlash.Properties.TryGetValue("RAMSize", out object ramSizeObj))
                    {
                        if (ramSizeObj is int ramSizeInt)
                        {
                            RAMSize = ramSizeInt;
                        }
                        else if (ramSizeObj is string ramSizeStr && int.TryParse(ramSizeStr, out int ramSize))
                        {
                            RAMSize = ramSize;
                        }
                    }

                    if (SelectedECUForFlash.Properties.TryGetValue("BootloaderVersion", out object bootloaderVersionObj))
                    {
                        BootloaderVersion = bootloaderVersionObj?.ToString() ?? string.Empty;
                    }

                    if (SelectedECUForFlash.Properties.TryGetValue("SecurityLevel", out object securityLevelObj))
                    {
                        SecurityLevel = securityLevelObj?.ToString() ?? string.Empty;
                    }
                }
            }
            else
            {
                // Reset memory information if no ECU is selected
                EEPROMSize = 0;
                MCUCodeSize = 0;
                FlashMemorySize = 0;
                RAMSize = 0;
                BootloaderVersion = string.Empty;
                SecurityLevel = string.Empty;
            }
        }

        private async Task RefreshECUListAsync()
        {
            try
            {
                IsBusy = true;
                OperationStatus = "In Progress";
                CurrentOperation = "Refresh ECU List";
                AddOperationLog("Refreshing ECU list...");
                _loggingService.LogInformation("Refreshing ECU list", "EnhancedFlashProgrammingViewModel");

                // Refresh the ECU list
                await _ecuCommunicationService.ScanForECUsAsync();

                // Update the connected ECUs list
                LoadConnectedECUs();

                AddOperationLog("ECU list refreshed");
                _loggingService.LogInformation("ECU list refreshed", "EnhancedFlashProgrammingViewModel");
                OperationStatus = "Ready";
            }
            catch (Exception ex)
            {
                AddOperationLog($"Error refreshing ECU list: {ex.Message}");
                _loggingService.LogError("Error refreshing ECU list", "EnhancedFlashProgrammingViewModel", ex);
                OperationStatus = "Error";
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task ConnectToECUAsync()
        {
            if (SelectedECUForFlash == null)
            {
                AddOperationLog("No ECU selected");
                return;
            }

            try
            {
                IsBusy = true;
                OperationStatus = "In Progress";
                CurrentOperation = "Connect to ECU";
                AddOperationLog($"Connecting to ECU {SelectedECUForFlash.Name}...");
                _loggingService.LogInformation($"Connecting to ECU {SelectedECUForFlash.Name}", "EnhancedFlashProgrammingViewModel");

                // Connect to the selected ECU
                bool connected = await _ecuCommunicationService.ConnectToECUAsync(SelectedECUForFlash);

                if (connected)
                {
                    AddOperationLog($"Connected to ECU {SelectedECUForFlash.Name}");
                    _loggingService.LogInformation($"Connected to ECU {SelectedECUForFlash.Name}", "EnhancedFlashProgrammingViewModel");
                    OperationStatus = "Ready";

                    // Refresh the connected ECUs list
                    LoadConnectedECUs();
                }
                else
                {
                    AddOperationLog($"Failed to connect to ECU {SelectedECUForFlash.Name}");
                    _loggingService.LogError($"Failed to connect to ECU {SelectedECUForFlash.Name}", "EnhancedFlashProgrammingViewModel");
                    OperationStatus = "Error";
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"Error connecting to ECU: {ex.Message}");
                _loggingService.LogError("Error connecting to ECU", "EnhancedFlashProgrammingViewModel", ex);
                OperationStatus = "Error";
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task DisconnectECUAsync()
        {
            if (SelectedECUForFlash == null)
            {
                AddOperationLog("No ECU selected");
                return;
            }

            try
            {
                IsBusy = true;
                OperationStatus = "In Progress";
                CurrentOperation = "Disconnect from ECU";
                AddOperationLog($"Disconnecting from ECU {SelectedECUForFlash.Name}...");
                _loggingService.LogInformation($"Disconnecting from ECU {SelectedECUForFlash.Name}", "EnhancedFlashProgrammingViewModel");

                // Disconnect from the selected ECU
                bool disconnected = await _ecuCommunicationService.DisconnectFromECUAsync(SelectedECUForFlash);

                if (disconnected)
                {
                    AddOperationLog($"Disconnected from ECU {SelectedECUForFlash.Name}");
                    _loggingService.LogInformation($"Disconnected from ECU {SelectedECUForFlash.Name}", "EnhancedFlashProgrammingViewModel");
                    OperationStatus = "Ready";

                    // Refresh the connected ECUs list
                    LoadConnectedECUs();
                }
                else
                {
                    AddOperationLog($"Failed to disconnect from ECU {SelectedECUForFlash.Name}");
                    _loggingService.LogError($"Failed to disconnect from ECU {SelectedECUForFlash.Name}", "EnhancedFlashProgrammingViewModel");
                    OperationStatus = "Error";
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"Error disconnecting from ECU: {ex.Message}");
                _loggingService.LogError("Error disconnecting from ECU", "EnhancedFlashProgrammingViewModel", ex);
                OperationStatus = "Error";
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task ReadEEPROMAsync()
        {
            if (SelectedECUForFlash == null)
            {
                AddOperationLog("No ECU selected");
                return;
            }

            try
            {
                IsBusy = true;
                OperationStatus = "In Progress";
                CurrentOperation = "Read EEPROM";
                OperationProgress = 0;
                HexLines.Clear();
                AddOperationLog($"Reading EEPROM from ECU {SelectedECUForFlash.Name}...");
                _loggingService.LogInformation($"Reading EEPROM from ECU {SelectedECUForFlash.Name}", "EnhancedFlashProgrammingViewModel");

                // Read EEPROM data with progress reporting
                var progress = new Progress<int>(percent =>
                {
                    OperationProgress = percent;
                    AddOperationLog($"Reading EEPROM: {percent}% complete");
                });

                byte[] eepromData = await _ecuCommunicationService.ReadEEPROMAsync(SelectedECUForFlash, progress);

                if (eepromData != null && eepromData.Length > 0)
                {
                    DataSize = eepromData.Length;
                    AddOperationLog($"EEPROM read successfully ({eepromData.Length} bytes)");
                    _loggingService.LogInformation($"EEPROM read successfully ({eepromData.Length} bytes)", "EnhancedFlashProgrammingViewModel");
                    OperationStatus = "Completed";

                    // Display the data in the hex viewer
                    var hexLines = HexLine.FromByteArray(eepromData);
                    foreach (var line in hexLines)
                    {
                        HexLines.Add(line);
                    }
                }
                else
                {
                    AddOperationLog("Failed to read EEPROM data");
                    _loggingService.LogError("Failed to read EEPROM data", "EnhancedFlashProgrammingViewModel");
                    OperationStatus = "Error";
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"Error reading EEPROM: {ex.Message}");
                _loggingService.LogError("Error reading EEPROM", "EnhancedFlashProgrammingViewModel", ex);
                OperationStatus = "Error";
            }
            finally
            {
                IsBusy = false;
                OperationProgress = 100;
            }
        }

        private async Task WriteEEPROMAsync()
        {
            if (SelectedECUForFlash == null)
            {
                AddOperationLog("No ECU selected");
                return;
            }

            if (string.IsNullOrEmpty(FilePath) || !File.Exists(FilePath))
            {
                AddOperationLog("Invalid file path");
                return;
            }

            try
            {
                // Ask for confirmation
                var result = MessageBox.Show(
                    $"Are you sure you want to write EEPROM data to ECU {SelectedECUForFlash.Name}?\n\n" +
                    "This operation may cause the ECU to malfunction if the data is incorrect.",
                    "Write EEPROM",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                {
                    AddOperationLog("Write EEPROM operation cancelled");
                    return;
                }

                IsBusy = true;
                OperationStatus = "In Progress";
                CurrentOperation = "Write EEPROM";
                OperationProgress = 0;
                AddOperationLog($"Writing EEPROM to ECU {SelectedECUForFlash.Name}...");
                _loggingService.LogInformation($"Writing EEPROM to ECU {SelectedECUForFlash.Name}", "EnhancedFlashProgrammingViewModel");

                // Read the file
                byte[] eepromData = File.ReadAllBytes(FilePath);

                // Check if the data size is valid
                if (eepromData.Length > EEPROMSize)
                {
                    AddOperationLog($"EEPROM data size ({eepromData.Length} bytes) exceeds maximum size ({EEPROMSize} bytes)");
                    _loggingService.LogError($"EEPROM data size ({eepromData.Length} bytes) exceeds maximum size ({EEPROMSize} bytes)", "EnhancedFlashProgrammingViewModel");
                    OperationStatus = "Error";
                    IsBusy = false;
                    return;
                }

                // Write EEPROM data with progress reporting
                var progress = new Progress<int>(percent =>
                {
                    OperationProgress = percent;
                    AddOperationLog($"Writing EEPROM: {percent}% complete");
                });

                bool success = await _ecuCommunicationService.WriteEEPROMAsync(SelectedECUForFlash, eepromData, progress);

                if (success)
                {
                    AddOperationLog($"EEPROM written successfully ({eepromData.Length} bytes)");
                    _loggingService.LogInformation($"EEPROM written successfully ({eepromData.Length} bytes)", "EnhancedFlashProgrammingViewModel");
                    OperationStatus = "Completed";
                }
                else
                {
                    AddOperationLog("Failed to write EEPROM data");
                    _loggingService.LogError("Failed to write EEPROM data", "EnhancedFlashProgrammingViewModel");
                    OperationStatus = "Error";
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"Error writing EEPROM: {ex.Message}");
                _loggingService.LogError("Error writing EEPROM", "EnhancedFlashProgrammingViewModel", ex);
                OperationStatus = "Error";
            }
            finally
            {
                IsBusy = false;
                OperationProgress = 100;
            }
        }

        private async Task ReadMicrocontrollerCodeAsync()
        {
            if (SelectedECUForFlash == null)
            {
                AddOperationLog("No ECU selected");
                return;
            }

            try
            {
                IsBusy = true;
                OperationStatus = "In Progress";
                CurrentOperation = "Read MCU Code";
                OperationProgress = 0;
                HexLines.Clear();
                AddOperationLog($"Reading microcontroller code from ECU {SelectedECUForFlash.Name}...");
                _loggingService.LogInformation($"Reading microcontroller code from ECU {SelectedECUForFlash.Name}", "EnhancedFlashProgrammingViewModel");

                // Read microcontroller code with progress reporting
                var progress = new Progress<int>(percent =>
                {
                    OperationProgress = percent;
                    AddOperationLog($"Reading microcontroller code: {percent}% complete");
                });

                byte[] mcuCode = await _ecuCommunicationService.ReadMicrocontrollerCodeAsync(SelectedECUForFlash, progress);

                if (mcuCode != null && mcuCode.Length > 0)
                {
                    DataSize = mcuCode.Length;
                    AddOperationLog($"Microcontroller code read successfully ({mcuCode.Length} bytes)");
                    _loggingService.LogInformation($"Microcontroller code read successfully ({mcuCode.Length} bytes)", "EnhancedFlashProgrammingViewModel");
                    OperationStatus = "Completed";

                    // Display the data in the hex viewer
                    var hexLines = HexLine.FromByteArray(mcuCode);
                    foreach (var line in hexLines)
                    {
                        HexLines.Add(line);
                    }
                }
                else
                {
                    AddOperationLog("Failed to read microcontroller code");
                    _loggingService.LogError("Failed to read microcontroller code", "EnhancedFlashProgrammingViewModel");
                    OperationStatus = "Error";
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"Error reading microcontroller code: {ex.Message}");
                _loggingService.LogError("Error reading microcontroller code", "EnhancedFlashProgrammingViewModel", ex);
                OperationStatus = "Error";
            }
            finally
            {
                IsBusy = false;
                OperationProgress = 100;
            }
        }

        private async Task WriteMicrocontrollerCodeAsync()
        {
            if (SelectedECUForFlash == null)
            {
                AddOperationLog("No ECU selected");
                return;
            }

            if (string.IsNullOrEmpty(FilePath) || !File.Exists(FilePath))
            {
                AddOperationLog("Invalid file path");
                return;
            }

            try
            {
                // Ask for confirmation
                var result = MessageBox.Show(
                    $"Are you sure you want to write microcontroller code to ECU {SelectedECUForFlash.Name}?\n\n" +
                    "This operation may cause the ECU to malfunction if the code is incorrect.",
                    "Write Microcontroller Code",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                {
                    AddOperationLog("Write microcontroller code operation cancelled");
                    return;
                }

                IsBusy = true;
                OperationStatus = "In Progress";
                CurrentOperation = "Write MCU Code";
                OperationProgress = 0;
                AddOperationLog($"Writing microcontroller code to ECU {SelectedECUForFlash.Name}...");
                _loggingService.LogInformation($"Writing microcontroller code to ECU {SelectedECUForFlash.Name}", "EnhancedFlashProgrammingViewModel");

                // Read the file
                byte[] mcuCode = File.ReadAllBytes(FilePath);

                // Check if the data size is valid
                if (mcuCode.Length > MCUCodeSize)
                {
                    AddOperationLog($"Microcontroller code size ({mcuCode.Length} bytes) exceeds maximum size ({MCUCodeSize} bytes)");
                    _loggingService.LogError($"Microcontroller code size ({mcuCode.Length} bytes) exceeds maximum size ({MCUCodeSize} bytes)", "EnhancedFlashProgrammingViewModel");
                    OperationStatus = "Error";
                    IsBusy = false;
                    return;
                }

                // Write microcontroller code with progress reporting
                var progress = new Progress<int>(percent =>
                {
                    OperationProgress = percent;
                    AddOperationLog($"Writing microcontroller code: {percent}% complete");
                });

                bool success = await _ecuCommunicationService.WriteMicrocontrollerCodeAsync(SelectedECUForFlash, mcuCode, progress);

                if (success)
                {
                    AddOperationLog($"Microcontroller code written successfully ({mcuCode.Length} bytes)");
                    _loggingService.LogInformation($"Microcontroller code written successfully ({mcuCode.Length} bytes)", "EnhancedFlashProgrammingViewModel");
                    OperationStatus = "Completed";
                }
                else
                {
                    AddOperationLog("Failed to write microcontroller code");
                    _loggingService.LogError("Failed to write microcontroller code", "EnhancedFlashProgrammingViewModel");
                    OperationStatus = "Error";
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"Error writing microcontroller code: {ex.Message}");
                _loggingService.LogError("Error writing microcontroller code", "EnhancedFlashProgrammingViewModel", ex);
                OperationStatus = "Error";
            }
            finally
            {
                IsBusy = false;
                OperationProgress = 100;
            }
        }

        private void BrowseFile()
        {
            try
            {
                var dialog = new OpenFileDialog
                {
                    Title = "Select File",
                    Filter = "Binary Files (*.bin)|*.bin|Hex Files (*.hex)|*.hex|All Files (*.*)|*.*",
                    CheckFileExists = true
                };

                if (dialog.ShowDialog() == true)
                {
                    FilePath = dialog.FileName;
                    AddOperationLog($"Selected file: {FilePath}");
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"Error browsing for file: {ex.Message}");
                _loggingService.LogError("Error browsing for file", "EnhancedFlashProgrammingViewModel", ex);
            }
        }

        private void GoToAddress()
        {
            try
            {
                if (string.IsNullOrEmpty(CurrentAddress) || HexLines.Count == 0)
                {
                    return;
                }

                // Parse the address
                if (int.TryParse(CurrentAddress, System.Globalization.NumberStyles.HexNumber, null, out int address))
                {
                    // Find the closest line
                    int lineIndex = address / 16; // Assuming 16 bytes per line
                    if (lineIndex < HexLines.Count)
                    {
                        // TODO: Implement scrolling to the specified line in the UI
                        AddOperationLog($"Navigated to address: 0x{address:X8}");
                    }
                    else
                    {
                        AddOperationLog($"Address 0x{address:X8} is out of range");
                    }
                }
                else
                {
                    AddOperationLog($"Invalid address format: {CurrentAddress}");
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"Error navigating to address: {ex.Message}");
                _loggingService.LogError("Error navigating to address", "EnhancedFlashProgrammingViewModel", ex);
            }
        }

        private void SaveData()
        {
            try
            {
                if (HexLines.Count == 0)
                {
                    AddOperationLog("No data to save");
                    return;
                }

                var dialog = new SaveFileDialog
                {
                    Title = "Save Data",
                    Filter = "Binary Files (*.bin)|*.bin|Hex Files (*.hex)|*.hex|All Files (*.*)|*.*",
                    DefaultExt = ".bin"
                };

                if (dialog.ShowDialog() == true)
                {
                    // Convert hex lines back to bytes
                    byte[] data = new byte[DataSize];

                    foreach (var line in HexLines)
                    {
                        // Parse the address
                        int lineAddress = int.Parse(line.Address, System.Globalization.NumberStyles.HexNumber);

                        // Parse the hex data
                        string[] hexBytes = line.HexData.Split(' ');
                        for (int i = 0; i < hexBytes.Length; i++)
                        {
                            if (!string.IsNullOrWhiteSpace(hexBytes[i]))
                            {
                                int byteOffset = lineAddress + i;
                                if (byteOffset < data.Length)
                                {
                                    data[byteOffset] = byte.Parse(hexBytes[i], System.Globalization.NumberStyles.HexNumber);
                                }
                            }
                        }
                    }

                    // Save the data to file
                    File.WriteAllBytes(dialog.FileName, data);
                    AddOperationLog($"Data saved to {dialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"Error saving data: {ex.Message}");
                _loggingService.LogError("Error saving data", "EnhancedFlashProgrammingViewModel", ex);
            }
        }

        private void CompareData()
        {
            try
            {
                if (HexLines.Count == 0)
                {
                    AddOperationLog("No data to compare");
                    return;
                }

                var dialog = new OpenFileDialog
                {
                    Title = "Select File to Compare",
                    Filter = "Binary Files (*.bin)|*.bin|Hex Files (*.hex)|*.hex|All Files (*.*)|*.*",
                    CheckFileExists = true
                };

                if (dialog.ShowDialog() == true)
                {
                    // Read the file
                    byte[] fileData = File.ReadAllBytes(dialog.FileName);

                    // Convert hex lines to bytes
                    byte[] currentData = new byte[DataSize];

                    foreach (var line in HexLines)
                    {
                        // Parse the address
                        int lineAddress = int.Parse(line.Address, System.Globalization.NumberStyles.HexNumber);

                        // Parse the hex data
                        string[] hexBytes = line.HexData.Split(' ');
                        for (int i = 0; i < hexBytes.Length; i++)
                        {
                            if (!string.IsNullOrWhiteSpace(hexBytes[i]))
                            {
                                int byteOffset = lineAddress + i;
                                if (byteOffset < currentData.Length)
                                {
                                    currentData[byteOffset] = byte.Parse(hexBytes[i], System.Globalization.NumberStyles.HexNumber);
                                }
                            }
                        }
                    }

                    // Compare the data
                    int differences = 0;
                    int minLength = Math.Min(currentData.Length, fileData.Length);

                    for (int i = 0; i < minLength; i++)
                    {
                        if (currentData[i] != fileData[i])
                        {
                            differences++;
                        }
                    }

                    // Report the results
                    if (differences == 0 && currentData.Length == fileData.Length)
                    {
                        AddOperationLog($"Files are identical ({fileData.Length} bytes)");
                    }
                    else
                    {
                        AddOperationLog($"Files differ: {differences} different bytes");
                        if (currentData.Length != fileData.Length)
                        {
                            AddOperationLog($"File sizes differ: Current={currentData.Length} bytes, File={fileData.Length} bytes");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"Error comparing data: {ex.Message}");
                _loggingService.LogError("Error comparing data", "EnhancedFlashProgrammingViewModel", ex);
            }
        }

        private void CancelOperation()
        {
            try
            {
                // TODO: Implement cancellation of the current operation
                AddOperationLog("Operation cancelled");
                OperationStatus = "Cancelled";
                IsBusy = false;
            }
            catch (Exception ex)
            {
                AddOperationLog($"Error cancelling operation: {ex.Message}");
                _loggingService.LogError("Error cancelling operation", "EnhancedFlashProgrammingViewModel", ex);
            }
        }

        private void AddOperationLog(string message)
        {
            OperationLogs.Add(new CoreLogEntry { Timestamp = DateTime.Now, Message = message, Level = LogLevel.Information });

            // Limit the number of log entries
            while (OperationLogs.Count > 100)
            {
                OperationLogs.RemoveAt(0);
            }
        }

        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}

