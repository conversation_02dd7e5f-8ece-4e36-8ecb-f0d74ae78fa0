using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Utilities
{
    /// <summary>
    /// Helper class for connection-related operations
    /// </summary>
    public static class ConnectionHelper
    {
        /// <summary>
        /// Checks if a process is running by name
        /// </summary>
        /// <param name="processName">The name of the process to check</param>
        /// <returns>True if the process is running, false otherwise</returns>
        public static bool IsProcessRunning(string processName)
        {
            return Process.GetProcessesByName(processName).Length > 0;
        }

        /// <summary>
        /// Attempts to kill a process by name
        /// </summary>
        /// <param name="processName">The name of the process to kill</param>
        /// <param name="waitTimeoutMs">Timeout in milliseconds to wait for the process to exit</param>
        /// <returns>True if the process was killed, false otherwise</returns>
        public static bool KillProcess(string processName, int waitTimeoutMs = 5000)
        {
            try
            {
                Process[] processes = Process.GetProcessesByName(processName);
                if (processes.Length == 0)
                {
                    return true; // No processes to kill
                }

                foreach (Process process in processes)
                {
                    try
                    {
                        // Try to close gracefully first
                        if (!process.HasExited && process.CloseMainWindow())
                        {
                            if (process.WaitForExit(waitTimeoutMs))
                            {
                                continue; // Process exited gracefully
                            }
                        }

                        // If graceful close failed or timed out, force kill
                        if (!process.HasExited)
                        {
                            process.Kill();
                            process.WaitForExit(waitTimeoutMs);
                        }
                    }
                    catch (Exception)
                    {
                        // If we can't close it gracefully, try to force kill
                        try
                        {
                            if (!process.HasExited)
                            {
                                process.Kill();
                                process.WaitForExit(waitTimeoutMs);
                            }
                        }
                        catch
                        {
                            // Ignore any exceptions during force kill
                        }
                    }
                }

                // Verify all processes are gone
                processes = Process.GetProcessesByName(processName);
                return processes.Length == 0;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Attempts to gracefully close a process by sending a close message
        /// </summary>
        /// <param name="processName">The name of the process to close</param>
        /// <param name="waitTimeoutMs">Timeout in milliseconds to wait for the process to exit</param>
        /// <returns>True if the process was closed, false otherwise</returns>
        public static bool GracefullyCloseProcess(string processName, int waitTimeoutMs = 5000)
        {
            try
            {
                Process[] processes = Process.GetProcessesByName(processName);
                if (processes.Length == 0)
                {
                    return true; // No processes to close
                }

                bool allClosed = true;
                foreach (Process process in processes)
                {
                    try
                    {
                        if (!process.HasExited)
                        {
                            // Try to close the main window
                            if (process.CloseMainWindow())
                            {
                                // Wait for the process to exit
                                if (!process.WaitForExit(waitTimeoutMs))
                                {
                                    allClosed = false;
                                }
                            }
                            else
                            {
                                allClosed = false;
                            }
                        }
                    }
                    catch
                    {
                        allClosed = false;
                    }
                }

                return allClosed;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Checks if a USB device is connected by device ID or serial number
        /// </summary>
        /// <param name="deviceIdOrSerial">The device ID or serial number to check</param>
        /// <returns>True if the device is connected, false otherwise</returns>
        public static bool IsUSBDeviceConnected(string deviceIdOrSerial)
        {
            if (string.IsNullOrEmpty(deviceIdOrSerial))
            {
                return false;
            }

            try
            {
                // Use WMI to check for USB devices
                using (var searcher = new System.Management.ManagementObjectSearcher(
                    "SELECT * FROM Win32_PnPEntity WHERE (PNPDeviceID LIKE '%VID_%&PID_%' OR Caption LIKE '%USB%')"))
                {
                    foreach (var device in searcher.Get())
                    {
                        string deviceId = device["DeviceID"]?.ToString() ?? string.Empty;
                        string caption = device["Caption"]?.ToString() ?? string.Empty;
                        string description = device["Description"]?.ToString() ?? string.Empty;

                        // Check if this is a Vocom device
                        if ((deviceId.Contains("VID_1A12") && deviceId.Contains("PID_0001")) || // Vocom 1 VID/PID
                            caption.Contains("Vocom") ||
                            description.Contains("Vocom") ||
                            deviceId.Contains("88890300") ||
                            caption.Contains("88890300") ||
                            description.Contains("88890300"))
                        {
                            // If we're looking for a specific device, check if it matches
                            if (string.IsNullOrEmpty(deviceIdOrSerial) ||
                                deviceId.Contains(deviceIdOrSerial) ||
                                caption.Contains(deviceIdOrSerial) ||
                                description.Contains(deviceIdOrSerial))
                            {
                                return true;
                            }
                        }
                    }
                }

                return false;
            }
            catch (Exception)
            {
                // Fallback to a simpler check if WMI fails
                try {
                    // Check if the Vocom driver is installed
                    string vocomDriverPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86),
                        "88890020 Adapter", "UMDF", "WUDFPuma.dll");

                    if (File.Exists(vocomDriverPath))
                    {
                        // If the driver is installed, assume the device might be connected
                        return true;
                    }
                }
                catch
                {
                    // Ignore any exceptions in the fallback
                }

                return false;
            }
        }

        /// <summary>
        /// Gets information about a USB device
        /// </summary>
        /// <param name="deviceIdOrSerial">The device ID or serial number</param>
        /// <returns>A dictionary containing device information, or null if the device is not found</returns>
        public static Dictionary<string, string> GetUSBDeviceInfo(string deviceIdOrSerial)
        {
            if (string.IsNullOrEmpty(deviceIdOrSerial))
            {
                return null;
            }

            try
            {
                // Use WMI to get USB device information
                using (var searcher = new System.Management.ManagementObjectSearcher(
                    "SELECT * FROM Win32_PnPEntity WHERE (PNPDeviceID LIKE '%VID_%&PID_%' OR Caption LIKE '%USB%')"))
                {
                    foreach (var device in searcher.Get())
                    {
                        string deviceId = device["DeviceID"]?.ToString() ?? string.Empty;
                        string caption = device["Caption"]?.ToString() ?? string.Empty;
                        string description = device["Description"]?.ToString() ?? string.Empty;

                        // Check if this is a Vocom device
                        if ((deviceId.Contains("VID_1A12") && deviceId.Contains("PID_0001")) || // Vocom 1 VID/PID
                            caption.Contains("Vocom") ||
                            description.Contains("Vocom") ||
                            deviceId.Contains("88890300") ||
                            caption.Contains("88890300") ||
                            description.Contains("88890300"))
                        {
                            // If we're looking for a specific device, check if it matches
                            if (string.IsNullOrEmpty(deviceIdOrSerial) ||
                                deviceId.Contains(deviceIdOrSerial) ||
                                caption.Contains(deviceIdOrSerial) ||
                                description.Contains(deviceIdOrSerial))
                            {
                                // Extract the COM port from the caption if available
                                string comPort = string.Empty;
                                if (caption.Contains("(COM"))
                                {
                                    int startIndex = caption.IndexOf("(COM") + 1;
                                    int endIndex = caption.IndexOf(")", startIndex);
                                    if (endIndex > startIndex)
                                    {
                                        comPort = caption.Substring(startIndex, endIndex - startIndex);
                                    }
                                }

                                // Extract VID and PID from device ID
                                string vid = string.Empty;
                                string pid = string.Empty;
                                if (deviceId.Contains("VID_") && deviceId.Contains("PID_"))
                                {
                                    int vidIndex = deviceId.IndexOf("VID_") + 4;
                                    int pidIndex = deviceId.IndexOf("PID_") + 4;

                                    if (vidIndex > 4 && pidIndex > 4)
                                    {
                                        vid = deviceId.Substring(vidIndex, 4);
                                        pid = deviceId.Substring(pidIndex, 4);
                                    }
                                }

                                // Create the device info dictionary
                                var deviceInfo = new Dictionary<string, string>
                                {
                                    { "DeviceID", deviceId },
                                    { "SerialNumber", "88890300" }, // Default serial number for Vocom 1
                                    { "Manufacturer", "Volvo" },
                                    { "Product", caption },
                                    { "Description", description },
                                    { "Status", device["Status"]?.ToString() ?? "Unknown" }
                                };

                                // Add COM port if available
                                if (!string.IsNullOrEmpty(comPort))
                                {
                                    deviceInfo.Add("COMPort", comPort);
                                }

                                // Add VID and PID if available
                                if (!string.IsNullOrEmpty(vid) && !string.IsNullOrEmpty(pid))
                                {
                                    deviceInfo.Add("VID", vid);
                                    deviceInfo.Add("PID", pid);
                                }

                                return deviceInfo;
                            }
                        }
                    }
                }

                // If we couldn't find the device using WMI, fall back to a simulated device
                if (deviceIdOrSerial.Contains("88890300") || deviceIdOrSerial.Contains("Vocom"))
                {
                    return new Dictionary<string, string>
                    {
                        { "DeviceID", "USB\\VID_1A12&PID_0001\\88890300" },
                        { "SerialNumber", "88890300" },
                        { "Manufacturer", "Volvo" },
                        { "Product", "Vocom Interface" },
                        { "Description", "Volvo Communication Unit" },
                        { "Status", "OK" }
                    };
                }

                return null;
            }
            catch (Exception)
            {
                // Fallback to a simpler approach if WMI fails
                if (deviceIdOrSerial.Contains("88890300") || deviceIdOrSerial.Contains("Vocom"))
                {
                    return new Dictionary<string, string>
                    {
                        { "DeviceID", "USB\\VID_1A12&PID_0001\\88890300" },
                        { "SerialNumber", "88890300" },
                        { "Manufacturer", "Volvo" },
                        { "Product", "Vocom Interface" },
                        { "Description", "Volvo Communication Unit" },
                        { "Status", "OK" }
                    };
                }

                return null;
            }
        }

        /// <summary>
        /// Checks if Bluetooth is enabled
        /// </summary>
        /// <returns>True if Bluetooth is enabled, false otherwise</returns>
        public static bool IsBluetoothEnabled()
        {
            // This is a placeholder implementation
            // In a real implementation, this would use Windows APIs to check Bluetooth status
            return true;
        }

        /// <summary>
        /// Attempts to enable Bluetooth
        /// </summary>
        /// <returns>True if Bluetooth was enabled, false otherwise</returns>
        public static bool EnableBluetooth()
        {
            // This is a placeholder implementation
            // In a real implementation, this would use Windows APIs to enable Bluetooth
            return true;
        }

        /// <summary>
        /// Checks if WiFi is available
        /// </summary>
        /// <returns>True if WiFi is available, false otherwise</returns>
        public static bool IsWiFiAvailable()
        {
            try
            {
                // In a real implementation, this would use Windows APIs to check WiFi status
                // For example, using the Native WiFi API (wlanapi.dll) or NetworkInterface class

                // For now, we'll simulate WiFi availability
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Attempts to enable WiFi
        /// </summary>
        /// <returns>True if WiFi was enabled, false otherwise</returns>
        public static bool EnableWiFi()
        {
            try
            {
                // In a real implementation, this would use Windows APIs to enable WiFi
                // For example, using the Native WiFi API (wlanapi.dll) to enable the WiFi adapter

                // For now, we'll simulate enabling WiFi
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Executes an operation with retry logic
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <param name="retryCount">The number of retry attempts</param>
        /// <param name="retryDelayMs">The delay between retry attempts in milliseconds</param>
        /// <param name="logger">Optional logger for logging errors</param>
        /// <returns>The result of the operation</returns>
        public static async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, int retryCount, int retryDelayMs, ILoggingService logger = null)
        {
            int attempts = 0;
            while (true)
            {
                try
                {
                    attempts++;
                    return await operation();
                }
                catch (Exception ex)
                {
                    if (attempts > retryCount)
                    {
                        logger?.LogError($"Operation failed after {attempts} attempts", "ConnectionHelper", ex);
                        throw;
                    }

                    logger?.LogWarning($"Operation failed, retrying ({attempts}/{retryCount}): {ex.Message}", "ConnectionHelper");
                    await Task.Delay(retryDelayMs);
                }
            }
        }

        /// <summary>
        /// Checks if a specific PTT application is running
        /// </summary>
        /// <param name="pttProcessName">The process name of the PTT application (default is "ptt")</param>
        /// <returns>True if the PTT application is running, false otherwise</returns>
        public static bool IsPTTApplicationRunning(string pttProcessName = "ptt")
        {
            // Check for multiple possible PTT process names
            string[] possibleProcessNames = new[] { pttProcessName, "ptt", "PTT", "VolvoPTT", "VolvoTechTool", "TechTool" };

            foreach (string processName in possibleProcessNames)
            {
                if (IsProcessRunning(processName))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Forcefully terminates the PTT process using more aggressive methods
        /// </summary>
        /// <param name="pttProcessName">The process name of the PTT application (default is "ptt")</param>
        /// <returns>True if the PTT process was terminated, false otherwise</returns>
        public static bool ForceTerminatePTTProcess(string pttProcessName = "ptt")
        {
            try
            {
                // Check for multiple possible PTT process names
                string[] possibleProcessNames = new[] { pttProcessName, "ptt", "PTT", "VolvoPTT", "VolvoTechTool", "TechTool" };
                bool anyProcessTerminated = false;

                foreach (string processName in possibleProcessNames)
                {
                    // Check if this specific process is running
                    Process[] processes = Process.GetProcessesByName(processName);
                    if (processes.Length == 0)
                    {
                        continue; // Skip to next process name
                    }

                    foreach (Process process in processes)
                    {
                        try
                        {
                            if (!process.HasExited)
                            {
                                // Force kill with no waiting
                                process.Kill();
                                anyProcessTerminated = true;
                            }
                        }
                        catch
                        {
                            // Try alternative methods if standard kill fails
                            try
                            {
                                // Use taskkill command with /F (force) flag
                                using (Process taskkill = new Process())
                                {
                                    taskkill.StartInfo.FileName = "taskkill";
                                    taskkill.StartInfo.Arguments = $"/F /IM {processName}.exe";
                                    taskkill.StartInfo.UseShellExecute = false;
                                    taskkill.StartInfo.CreateNoWindow = true;
                                    taskkill.Start();
                                    taskkill.WaitForExit(1000);
                                    anyProcessTerminated = true;
                                }
                            }
                            catch
                            {
                                // Ignore any exceptions during force kill
                            }
                        }
                    }
                }

                // Verify all processes are gone
                foreach (string processName in possibleProcessNames)
                {
                    if (IsProcessRunning(processName))
                    {
                        return false; // At least one process is still running
                    }
                }

                return anyProcessTerminated;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Attempts to disconnect the PTT application
        /// </summary>
        /// <param name="pttProcessName">The process name of the PTT application (default is "ptt")</param>
        /// <param name="waitTimeoutMs">Timeout in milliseconds to wait for the process to exit</param>
        /// <returns>True if the PTT application was disconnected, false otherwise</returns>
        public static async Task<bool> DisconnectPTTApplicationAsync(string pttProcessName = "ptt", int waitTimeoutMs = 5000)
        {
            try
            {
                // Check for multiple possible PTT process names
                string[] possibleProcessNames = new[] { pttProcessName, "ptt", "PTT", "VolvoPTT", "VolvoTechTool", "TechTool" };
                bool anyProcessKilled = false;

                foreach (string processName in possibleProcessNames)
                {
                    // Check if this specific process is running
                    if (!IsProcessRunning(processName))
                    {
                        continue; // Skip to next process name
                    }

                    // Try to gracefully close first
                    bool gracefullyClosed = GracefullyCloseProcess(processName, waitTimeoutMs);
                    if (gracefullyClosed)
                    {
                        // Wait a moment to ensure resources are released
                        await Task.Delay(500);
                        anyProcessKilled = true;
                        continue; // Try next process name
                    }

                    // If graceful close failed, try to kill the process
                    bool killed = KillProcess(processName, waitTimeoutMs);
                    if (killed)
                    {
                        // Wait a moment to ensure resources are released
                        await Task.Delay(500);
                        anyProcessKilled = true;
                    }
                }

                // Try to find and close any PTT-related windows by title
                string[] pttWindowTitles = new[] { "PTT", "Tech Tool", "Volvo PTT", "Volvo Tech Tool" };
                foreach (string windowTitle in pttWindowTitles)
                {
                    bool closed = await CloseWindowByTitleAsync(windowTitle, waitTimeoutMs);
                    if (closed)
                    {
                        anyProcessKilled = true;
                    }
                }

                // Final check to see if any PTT process is still running
                bool anyStillRunning = false;
                foreach (string processName in possibleProcessNames)
                {
                    if (IsProcessRunning(processName))
                    {
                        anyStillRunning = true;
                        break;
                    }
                }

                // If we killed something and nothing is still running, consider it a success
                if (anyProcessKilled && !anyStillRunning)
                {
                    return true;
                }

                // If nothing was running to begin with, that's also a success
                if (!anyStillRunning)
                {
                    return true;
                }

                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Attempts to close a window by its title
        /// </summary>
        /// <param name="windowTitle">The title of the window to close</param>
        /// <param name="waitTimeoutMs">Timeout in milliseconds to wait for the window to close</param>
        /// <returns>True if the window was closed, false otherwise</returns>
        private static async Task<bool> CloseWindowByTitleAsync(string windowTitle, int waitTimeoutMs = 5000)
        {
            try
            {
                // Find windows by title
                var windows = FindWindowsByTitle(windowTitle);
                if (windows.Count == 0)
                {
                    return true; // No windows to close
                }

                bool allClosed = true;
                foreach (IntPtr hWnd in windows)
                {
                    // Try to send a close message to the window
                    if (!SendCloseMessage(hWnd, waitTimeoutMs))
                    {
                        allClosed = false;
                    }
                }

                // Wait a moment to ensure resources are released
                await Task.Delay(500);

                return allClosed;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Finds windows by title
        /// </summary>
        /// <param name="windowTitle">The title of the windows to find</param>
        /// <returns>A list of window handles</returns>
        private static List<IntPtr> FindWindowsByTitle(string windowTitle)
        {
            var windows = new List<IntPtr>();

            try
            {
                // Use EnumWindows to find all top-level windows
                EnumWindows((hWnd, lParam) =>
                {
                    // Get the window title
                    int length = GetWindowTextLength(hWnd);
                    if (length > 0)
                    {
                        StringBuilder sb = new StringBuilder(length + 1);
                        GetWindowText(hWnd, sb, sb.Capacity);
                        string title = sb.ToString();

                        // Check if the title contains the search string
                        if (title.Contains(windowTitle, StringComparison.OrdinalIgnoreCase))
                        {
                            windows.Add(hWnd);
                        }
                    }

                    return true; // Continue enumeration
                }, IntPtr.Zero);
            }
            catch
            {
                // Ignore any exceptions
            }

            return windows;
        }

        /// <summary>
        /// Sends a close message to a window
        /// </summary>
        /// <param name="hWnd">The handle of the window to close</param>
        /// <param name="waitTimeoutMs">Timeout in milliseconds to wait for the window to close</param>
        /// <returns>True if the window was closed, false otherwise</returns>
        private static bool SendCloseMessage(IntPtr hWnd, int waitTimeoutMs)
        {
            try
            {
                // Check if the window is valid
                if (!IsWindow(hWnd))
                {
                    return true; // Window already closed
                }

                // Send a close message to the window
                SendMessage(hWnd, WM_CLOSE, IntPtr.Zero, IntPtr.Zero);

                // Wait for the window to close
                DateTime startTime = DateTime.Now;
                while (IsWindow(hWnd) && (DateTime.Now - startTime).TotalMilliseconds < waitTimeoutMs)
                {
                    Thread.Sleep(100);
                }

                // Check if the window is still open
                if (IsWindow(hWnd))
                {
                    // Try to force close the window
                    SendMessage(hWnd, WM_DESTROY, IntPtr.Zero, IntPtr.Zero);
                    Thread.Sleep(100);
                }

                return !IsWindow(hWnd);
            }
            catch
            {
                return false;
            }
        }

        // Windows API constants and methods for window management
        private const int WM_CLOSE = 0x0010;
        private const int WM_DESTROY = 0x0002;

        [DllImport("user32.dll")]
        private static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);

        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        private static extern int GetWindowTextLength(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern IntPtr SendMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool IsWindow(IntPtr hWnd);

        private delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        /// <summary>
        /// Attempts to find the installation path of the PTT application
        /// </summary>
        /// <param name="pttProcessName">The process name of the PTT application (default is "ptt")</param>
        /// <returns>The installation path if found, null otherwise</returns>
        public static string FindPTTInstallationPath(string pttProcessName = "ptt")
        {
            try
            {
                Process[] processes = Process.GetProcessesByName(pttProcessName);
                if (processes.Length > 0)
                {
                    try
                    {
                        // Try to get the main module path
                        string path = processes[0].MainModule?.FileName;
                        if (!string.IsNullOrEmpty(path))
                        {
                            return Path.GetDirectoryName(path);
                        }
                    }
                    catch
                    {
                        // Ignore errors when trying to access the main module
                    }
                }

                // If we can't find it from running processes, try common installation paths
                string[] commonPaths = new[]
                {
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "Volvo", "PTT"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "Volvo", "PTT")
                };

                foreach (string path in commonPaths)
                {
                    if (Directory.Exists(path) && File.Exists(Path.Combine(path, $"{pttProcessName}.exe")))
                    {
                        return path;
                    }
                }

                return null;
            }
            catch
            {
                return null;
            }
        }
    }
}
