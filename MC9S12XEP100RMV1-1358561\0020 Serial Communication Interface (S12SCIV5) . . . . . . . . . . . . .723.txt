﻿Chapter 20
Serial Communication Interface (S12SCIV5)

Table 20-1. Revision History

Version Revision Effective
Number Date Date Author Description of Changes

05.03 12/25/2008 remove redundancy comments in Figure1-2

05.04 08/05/2009 fix typo, SCIBDL reset value be 0x04, not 0x00

05.05 06/03/2010 fix typo, Table 20-4,SCICR1 Even parity should be PT=0
fix typo, on page 20-745,should be BKDIF,not BLDIF

20.1 Introduction
This block guide provides an overview of the serial communication interface (SCI) module.

The SCI allows asynchronous serial communications with peripheral devices and other CPUs.

20.1.1 Glossary
IR: InfraRed

IrDA: Infrared Design Associate

IRQ: Interrupt Request

LIN: Local Interconnect Network

LSB: Least Significant Bit

MSB: Most Significant Bit

NRZ: Non-Return-to-Zero

RZI: Return-to-Zero-Inverted

RXD: Receive Pin

SCI : Serial Communication Interface

TXD: Transmit Pin

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 723



Chapter 20 Serial Communication Interface (S12SCIV5)

20.1.2 Features
The SCI includes these distinctive features:

• Full-duplex or single-wire operation
• Standard mark/space non-return-to-zero (NRZ) format
• Selectable IrDA 1.4 return-to-zero-inverted (RZI) format with programmable pulse widths
• 13-bit baud rate selection
• Programmable 8-bit or 9-bit data format
• Separately enabled transmitter and receiver
• Programmable polarity for transmitter and receiver
• Programmable transmitter output parity
• Two receiver wakeup methods:

— Idle line wakeup
— Address mark wakeup

• Interrupt-driven operation with eight flags:
— Transmitter empty
— Transmission complete
— Receiver full
— Idle receiver input
— Receiver overrun
— Noise error
— Framing error
— Parity error
— Receive wakeup on active edge
— Transmit collision detect supporting LIN
— Break Detect supporting LIN

• Receiver framing error detection
• Hardware parity checking
• 1/16 bit-time noise detection

20.1.3 Modes of Operation
The SCI functions the same in normal, special, and emulation modes. It has two low power modes, wait
and stop modes.

• Run mode
• Wait mode
• Stop mode

MC9S12XE-Family Reference Manual  Rev. 1.25

724 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

20.1.4 Block Diagram
Figure 20-1 is a high level block diagram of the SCI module, showing the interaction of various function
blocks.

SCI Data Register

RXD Data In Infrared Receive Shift Register
Decoder IDLE

Receive
RDRF/OR

Receive & Wakeup Interrupt

Control Generation BRKD SCI
Interrupt

RXEDG Request
Bus Clock Baud Rate Data Format Control BERR

Generator
Transmit

TDRE
Interrupt

1/16 Transmit Control Generation TC

Transmit Shift Register Infrared Data Out TXD
Encoder

SCI Data Register

Figure 20-1. SCI Block Diagram

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 725



Chapter 20 Serial Communication Interface (S12SCIV5)

20.2 External Signal Description
The SCI module has a total of two external pins.

20.2.1 TXD — Transmit Pin
The TXD pin transmits SCI (standard or infrared) data. It will idle high in either mode and is high
impedance anytime the transmitter is disabled.

20.2.2 RXD — Receive Pin
The RXD pin receives SCI (standard or infrared) data. An idle line is detected as a line high. This input is
ignored when the receiver is disabled and should be terminated to a known voltage.

20.3 Memory Map and Register Definition
This section provides a detailed description of all the SCI registers.

20.3.1 Module Memory Map and Register Definition
The memory map for the SCI module is given below in Figure 20-2. The address listed for each register is
the address offset. The total address for each register is the sum of the base address for the SCI module and
the address offset for each register.

MC9S12XE-Family Reference Manual  Rev. 1.25

726 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

20.3.2 Register Descriptions
This section consists of register descriptions in address order. Each description includes a standard register
diagram with an associated figure number. Writes to a reserved register locations do not have any effect
and reads of these locations return a zero. Details of register bit and field function follow the register
diagrams, in bit order.

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0000 R
SCIBDH1 IREN TNP1 TNP0 SBR12 SBR11 SBR10 SBR9 SBR8

W

0x0001 R
SCIBDL1 SBR7 SBR6 SBR5 SBR4 SBR3 SBR2 SBR1 SBR0

W

0x0002 R
SCICR11 LOOPS SCISWAI RSRC M WAKE ILT PE PT

W

0x0000 R 0 0 0 0
SCIASR12 RXEDGIF BERRV BERRIF BKDIF

W

0x0001 R 0 0 0 0 0
SCIACR12 RXEDGIE BERRIE BKDIE

W

0x0002 R 0 0 0 0 0
SCIACR22 BERRM1 BERRM0 BKDFE

W

0x0003 R
SCICR2 TIE TCIE RIE ILIE TE RE RWU SBK

W

0x0004 R TDRE TC RDRF IDLE OR NF FE PF
SCISR1 W

0x0005 R 0 0 RAF
SCISR2 AMAP TXPOL RXPOL BRK13 TXDIR

W

0x0006 R R8 0 0 0 0 0 0
SCIDRH T8

W

0x0007 R R7 R6 R5 R4 R3 R2 R1 R0
SCIDRL W T7 T6 T5 T4 T3 T2 T1 T0

1.These registers are accessible if the AMAP bit in the SCISR2 register is set to zero.
2,These registers are accessible if the AMAP bit in the SCISR2 register is set to one.

= Unimplemented or Reserved

Figure 20-2. SCI Register Summary

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 727



Chapter 20 Serial Communication Interface (S12SCIV5)

20.3.2.1 SCI Baud Rate Registers (SCIBDH, SCIBDL)

Module Base + 0x0000

7 6 5 4 3 2 1 0
R

IREN TNP1 TNP0 SBR12 SBR11 SBR10 SBR9 SBR8
W

Reset 0 0 0 0 0 0 0 0

Figure 20-3. SCI Baud Rate Register (SCIBDH)

Module Base + 0x0001

7 6 5 4 3 2 1 0
R

SBR7 SBR6 SBR5 SBR4 SBR3 SBR2 SBR1 SBR0
W

Reset 0 0 0 0 0 1 0 0

Figure 20-4. SCI Baud Rate Register (SCIBDL)

Read: Anytime, if AMAP = 0. If only SCIBDH is written to, a read will not return the correct data until
SCIBDL is written to as well, following a write to SCIBDH.

Write: Anytime, if AMAP = 0.

NOTE
Those two registers are only visible in the memory map if AMAP = 0 (reset
condition).

The SCI baud rate register is used by to determine the baud rate of the SCI, and to control the infrared
modulation/demodulation submodule.

Table 20-2. SCIBDH and SCIBDL Field Descriptions

Field Description

7 Infrared Enable Bit — This bit enables/disables the infrared modulation/demodulation submodule.
IREN 0 IR disabled

1 IR enabled

6:5 Transmitter Narrow Pulse Bits — These bits enable whether the SCI transmits a 1/16, 3/16, 1/32 or 1/4 narrow
TNP[1:0] pulse. See Table 20-3.

4:0 SCI Baud Rate Bits — The baud rate for the SCI is determined by the bits in this register. The baud rate is
7:0 calculated two different ways depending on the state of the IREN bit.

SBR[12:0] The formulas for calculating the baud rate are:
When IREN = 0 then,

SCI baud rate = SCI bus clock / (16 x SBR[12:0])
When IREN = 1 then,

SCI baud rate = SCI bus clock / (32 x SBR[12:1])
Note: The baud rate generator is disabled after reset and not started until the TE bit or the RE bit is set for the

first time. The baud rate generator is disabled when (SBR[12:0] = 0 and IREN = 0) or (SBR[12:1] = 0 and
IREN = 1).

Note: Writing to SCIBDH has no effect without writing to SCIBDL, because writing to SCIBDH puts the data in
a temporary location until SCIBDL is written to.

MC9S12XE-Family Reference Manual  Rev. 1.25

728 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

Table 20-3. IRSCI Transmit Pulse Width

TNP[1:0] Narrow Pulse Width

11 1/4
10 1/32
01 1/16
00 3/16

******** SCI Control Register 1 (SCICR1)

Module Base + 0x0002

7 6 5 4 3 2 1 0
R

LOOPS SCISWAI RSRC M WAKE ILT PE PT
W

Reset 0 0 0 0 0 0 0 0

Figure 20-5. SCI Control Register 1 (SCICR1)

Read: Anytime, if AMAP = 0.

Write: Anytime, if AMAP = 0.

NOTE
This register is only visible in the memory map if AMAP = 0 (reset
condition).

Table 20-4. SCICR1 Field Descriptions

Field Description

7 Loop Select Bit — LOOPS enables loop operation. In loop operation, the RXD pin is disconnected from the SCI
LOOPS and the transmitter output is internally connected to the receiver input. Both the transmitter and the receiver must

be enabled to use the loop function.
0 Normal operation enabled
1 Loop operation enabled
The receiver input is determined by the RSRC bit.

6 SCI Stop in Wait Mode Bit — SCISWAI disables the SCI in wait mode.
SCISWAI 0 SCI enabled in wait mode

1 SCI disabled in wait mode

5 Receiver Source Bit — When LOOPS = 1, the RSRC bit determines the source for the receiver shift register
RSRC input. See Table 20-5.

0 Receiver input internally connected to transmitter output
1 Receiver input connected externally to transmitter

4 Data Format Mode Bit — MODE determines whether data characters are eight or nine bits long.
M 0 One start bit, eight data bits, one stop bit

1 One start bit, nine data bits, one stop bit

3 Wakeup Condition Bit — WAKE determines which condition wakes up the SCI: a logic 1 (address mark) in the
WAKE most significant bit position of a received data character or an idle condition on the RXD pin.

0 Idle line wakeup
1 Address mark wakeup

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 729



Chapter 20 Serial Communication Interface (S12SCIV5)

Table 20-4. SCICR1 Field Descriptions (continued)

Field Description

2 Idle Line Type Bit — ILT determines when the receiver starts counting logic 1s as idle character bits. The
ILT counting begins either after the start bit or after the stop bit. If the count begins after the start bit, then a string of

logic 1s preceding the stop bit may cause false recognition of an idle character. Beginning the count after the
stop bit avoids false idle character recognition, but requires properly synchronized transmissions.
0 Idle character bit count begins after start bit
1 Idle character bit count begins after stop bit

1 Parity Enable Bit — PE enables the parity function. When enabled, the parity function inserts a parity bit in the
PE most significant bit position.

0 Parity function disabled
1 Parity function enabled

0 Parity Type Bit — PT determines whether the SCI generates and checks for even parity or odd parity. With even
PT parity, an even number of 1s clears the parity bit and an odd number of 1s sets the parity bit. With odd parity, an

odd number of 1s clears the parity bit and an even number of 1s sets the parity bit.
0 Even parity
1 Odd parity

Table 20-5. Loop Functions

LOOPS RSRC Function

0 x Normal operation
1 0 Loop mode with transmitter output internally connected to receiver input
1 1 Single-wire mode with TXD pin connected to receiver input

MC9S12XE-Family Reference Manual  Rev. 1.25

730 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

******** SCI Alternative Status Register 1 (SCIASR1)

Module Base + 0x0000

7 6 5 4 3 2 1 0
R 0 0 0 0 BERRV

RXEDGIF BERRIF BKDIF
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 20-6. SCI Alternative Status Register 1 (SCIASR1)

Read: Anytime, if AMAP = 1

Write: Anytime, if AMAP = 1
Table 20-6. SCIASR1 Field Descriptions

Field Description

7 Receive Input Active Edge Interrupt Flag — RXEDGIF is asserted, if an active edge (falling if RXPOL = 0,
RXEDGIF rising if RXPOL = 1) on the RXD input occurs. RXEDGIF bit is cleared by writing a “1” to it.

0 No active receive on the receive input has occurred
1 An active edge on the receive input has occurred

2 Bit Error Value — BERRV reflects the state of the RXD input when the bit error detect circuitry is enabled and
BERRV a mismatch to the expected value happened. The value is only meaningful, if BERRIF = 1.

0 A low input was sampled, when a high was expected
1 A high input reassembled, when a low was expected

1 Bit Error Interrupt Flag — BERRIF is asserted, when the bit error detect circuitry is enabled and if the value
BERRIF sampled at the RXD input does not match the transmitted value. If the BERRIE interrupt enable bit is set an

interrupt will be generated. The BERRIF bit is cleared by writing a “1” to it.
0 No mismatch detected
1 A mismatch has occurred

0 Break Detect Interrupt Flag — BKDIF is asserted, if the break detect circuitry is enabled and a break signal is
BKDIF received. If the BKDIE interrupt enable bit is set an interrupt will be generated. The BKDIF bit is cleared by writing

a “1” to it.
0 No break signal was received
1 A break signal was received

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 731



Chapter 20 Serial Communication Interface (S12SCIV5)

******** SCI Alternative Control Register 1 (SCIACR1)

Module Base + 0x0001

7 6 5 4 3 2 1 0
R 0 0 0 0 0

RXEDGIE BERRIE BKDIE
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 20-7. SCI Alternative Control Register 1 (SCIACR1)

Read: Anytime, if AMAP = 1

Write: Anytime, if AMAP = 1
Table 20-7. SCIACR1 Field Descriptions

Field Description

7 Receive Input Active Edge Interrupt Enable — RXEDGIE enables the receive input active edge interrupt flag,
RSEDGIE RXEDGIF, to generate interrupt requests.

0 RXEDGIF interrupt requests disabled
1 RXEDGIF interrupt requests enabled

1 Bit Error Interrupt Enable — BERRIE enables the bit error interrupt flag, BERRIF, to generate interrupt
BERRIE requests.

0 BERRIF interrupt requests disabled
1 BERRIF interrupt requests enabled

0 Break Detect Interrupt Enable — BKDIE enables the break detect interrupt flag, BKDIF, to generate interrupt
BKDIE requests.

0 BKDIF interrupt requests disabled
1 BKDIF interrupt requests enabled

MC9S12XE-Family Reference Manual  Rev. 1.25

732 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

******** SCI Alternative Control Register 2 (SCIACR2)

Module Base + 0x0002

7 6 5 4 3 2 1 0
R 0 0 0 0 0

BERRM1 BERRM0 BKDFE
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 20-8. SCI Alternative Control Register 2 (SCIACR2)

Read: Anytime, if AMAP = 1

Write: Anytime, if AMAP = 1

Table 20-8. SCIACR2 Field Descriptions

Field Description

2:1 Bit Error Mode — Those two bits determines the functionality of the bit error detect feature. See Table 20-9.
BERRM[1:0]

0 Break Detect Feature Enable — BKDFE enables the break detect circuitry.
BKDFE 0 Break detect circuit disabled

1 Break detect circuit enabled

Table 20-9. Bit Error Mode Coding

BERRM1 BERRM0 Function

0 0 Bit error detect circuit is disabled
0 1 Receive input sampling occurs during the 9th time tick of a transmitted bit

(refer to Figure 20-19)
1 0 Receive input sampling occurs during the 13th time tick of a transmitted bit

(refer to Figure 20-19)
1 1 Reserved

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 733



Chapter 20 Serial Communication Interface (S12SCIV5)

******** SCI Control Register 2 (SCICR2)

Module Base + 0x0003

7 6 5 4 3 2 1 0
R

TIE TCIE RIE ILIE TE RE RWU SBK
W

Reset 0 0 0 0 0 0 0 0

Figure 20-9. SCI Control Register 2 (SCICR2)

Read: Anytime

Write: Anytime
Table 20-10. SCICR2 Field Descriptions

Field Description

7 Transmitter Interrupt Enable Bit — TIE enables the transmit data register empty flag, TDRE, to generate
TIE interrupt requests.

0 TDRE interrupt requests disabled
1 TDRE interrupt requests enabled

6 Transmission Complete Interrupt Enable Bit — TCIE enables the transmission complete flag, TC, to generate
TCIE interrupt requests.

0 TC interrupt requests disabled
1 TC interrupt requests enabled

5 Receiver Full Interrupt Enable Bit — RIE enables the receive data register full flag, RDRF, or the overrun flag,
RIE OR, to generate interrupt requests.

0 RDRF and OR interrupt requests disabled
1 RDRF and OR interrupt requests enabled

4 Idle Line Interrupt Enable Bit — ILIE enables the idle line flag, IDLE, to generate interrupt requests.
ILIE 0 IDLE interrupt requests disabled

1 IDLE interrupt requests enabled

3 Transmitter Enable Bit — TE enables the SCI transmitter and configures the TXD pin as being controlled by
TE the SCI. The TE bit can be used to queue an idle preamble.

0 Transmitter disabled
1 Transmitter enabled

2 Receiver Enable Bit — RE enables the SCI receiver.
RE 0 Receiver disabled

1 Receiver enabled

1 Receiver Wakeup Bit — Standby state
RWU 0 Normal operation.

1 RWU enables the wakeup function and inhibits further receiver interrupt requests. Normally, hardware wakes
the receiver by automatically clearing RWU.

0 Send Break Bit — Toggling SBK sends one break character (10 or 11 logic 0s, respectively 13 or 14 logics 0s
SBK if BRK13 is set). Toggling implies clearing the SBK bit before the break character has finished transmitting. As

long as SBK is set, the transmitter continues to send complete break characters (10 or 11 bits, respectively 13
or 14 bits).
0 No break characters
1 Transmit break characters

MC9S12XE-Family Reference Manual  Rev. 1.25

734 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

20.3.2.7 SCI Status Register 1 (SCISR1)
The SCISR1 and SCISR2 registers provides inputs to the MCU for generation of SCI interrupts. Also,
these registers can be polled by the MCU to check the status of these bits. The flag-clearing procedures
require that the status register be read followed by a read or write to the SCI data register.It is permissible
to execute other instructions between the two steps as long as it does not compromise the handling of I/O,
but the order of operations is important for flag clearing.

Module Base + 0x0004

7 6 5 4 3 2 1 0
R TDRE TC RDRF IDLE OR NF FE PF
W

Reset 1 1 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 20-10. SCI Status Register 1 (SCISR1)

Read: Anytime

Write: Has no meaning or effect
Table 20-11. SCISR1 Field Descriptions

Field Description

7 Transmit Data Register Empty Flag — TDRE is set when the transmit shift register receives a byte from the
TDRE SCI data register. When TDRE is 1, the transmit data register (SCIDRH/L) is empty and can receive a new value

to transmit.Clear TDRE by reading SCI status register 1 (SCISR1), with TDRE set and then writing to SCI data
register low (SCIDRL).
0 No byte transferred to transmit shift register
1 Byte transferred to transmit shift register; transmit data register empty

6 Transmit Complete Flag — TC is set low when there is a transmission in progress or when a preamble or break
TC character is loaded. TC is set high when the TDRE flag is set and no data, preamble, or break character is being

transmitted.When TC is set, the TXD pin becomes idle (logic 1). Clear TC by reading SCI status register 1
(SCISR1) with TC set and then writing to SCI data register low (SCIDRL). TC is cleared automatically when data,
preamble, or break is queued and ready to be sent. TC is cleared in the event of a simultaneous set and clear of
the TC flag (transmission not complete).
0 Transmission in progress
1 No transmission in progress

5 Receive Data Register Full Flag — RDRF is set when the data in the receive shift register transfers to the SCI
RDRF data register. Clear RDRF by reading SCI status register 1 (SCISR1) with RDRF set and then reading SCI data

register low (SCIDRL).
0 Data not available in SCI data register
1 Received data available in SCI data register

4 Idle Line Flag — IDLE is set when 10 consecutive logic 1s (if M = 0) or 11 consecutive logic 1s (if M =1) appear
IDLE on the receiver input. Once the IDLE flag is cleared, a valid frame must again set the RDRF flag before an idle

condition can set the IDLE flag.Clear IDLE by reading SCI status register 1 (SCISR1) with IDLE set and then
reading SCI data register low (SCIDRL).
0 Receiver input is either active now or has never become active since the IDLE flag was last cleared
1 Receiver input has become idle
Note: When the receiver wakeup bit (RWU) is set, an idle line condition does not set the IDLE flag.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 735



Chapter 20 Serial Communication Interface (S12SCIV5)

Table 20-11. SCISR1 Field Descriptions (continued)

Field Description

3 Overrun Flag — OR is set when software fails to read the SCI data register before the receive shift register
OR receives the next frame. The OR bit is set immediately after the stop bit has been completely received for the

second frame. The data in the shift register is lost, but the data already in the SCI data registers is not affected.
Clear OR by reading SCI status register 1 (SCISR1) with OR set and then reading SCI data register low
(SCIDRL).
0 No overrun
1 Overrun
Note: OR flag may read back as set when RDRF flag is clear. This may happen if the following sequence of

events occurs:
1. After the first frame is received, read status register SCISR1 (returns RDRF set and OR flag clear);
2. Receive second frame without reading the first frame in the data register (the second frame is not

received and OR flag is set);
3. Read data register SCIDRL (returns first frame and clears RDRF flag in the status register);
4. Read status register SCISR1 (returns RDRF clear and OR set).

Event 3 may be at exactly the same time as event 2 or any time after. When this happens, a dummy
SCIDRL read following event 4 will be required to clear the OR flag if further frames are to be received.

2 Noise Flag — NF is set when the SCI detects noise on the receiver input. NF bit is set during the same cycle as
NF the RDRF flag but does not get set in the case of an overrun. Clear NF by reading SCI status register 1(SCISR1),

and then reading SCI data register low (SCIDRL).
0 No noise
1 Noise

1 Framing Error Flag — FE is set when a logic 0 is accepted as the stop bit. FE bit is set during the same cycle
FE as the RDRF flag but does not get set in the case of an overrun. FE inhibits further data reception until it is

cleared. Clear FE by reading SCI status register 1 (SCISR1) with FE set and then reading the SCI data register
low (SCIDRL).
0 No framing error
1 Framing error

0 Parity Error Flag — PF is set when the parity enable bit (PE) is set and the parity of the received data does not
PF match the parity type bit (PT). PF bit is set during the same cycle as the RDRF flag but does not get set in the

case of an overrun. Clear PF by reading SCI status register 1 (SCISR1), and then reading SCI data register low
(SCIDRL).
0 No parity error
1 Parity error

MC9S12XE-Family Reference Manual  Rev. 1.25

736 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

******** SCI Status Register 2 (SCISR2)

Module Base + 0x0005

7 6 5 4 3 2 1 0
R 0 0 RAF

AMAP TXPOL RXPOL BRK13 TXDIR
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 20-11. SCI Status Register 2 (SCISR2)

Read: Anytime

Write: Anytime
Table 20-12. SCISR2 Field Descriptions

Field Description

7 Alternative Map — This bit controls which registers sharing the same address space are accessible. In the reset
AMAP condition the SCI behaves as previous versions. Setting AMAP=1 allows the access to another set of control and

status registers and hides the baud rate and SCI control Register 1.
0 The registers labelled SCIBDH (0x0000),SCIBDL (0x0001), SCICR1 (0x0002) are accessible
1 The registers labelled SCIASR1 (0x0000),SCIACR1 (0x0001), SCIACR2 (0x00002) are accessible

4 Transmit Polarity — This bit control the polarity of the transmitted data. In NRZ format, a one is represented by
TXPOL a mark and a zero is represented by a space for normal polarity, and the opposite for inverted polarity. In IrDA

format, a zero is represented by short high pulse in the middle of a bit time remaining idle low for a one for normal
polarity, and a zero is represented by short low pulse in the middle of a bit time remaining idle high for a one for
inverted polarity.
0 Normal polarity
1 Inverted polarity

3 Receive Polarity — This bit control the polarity of the received data. In NRZ format, a one is represented by a
RXPOL mark and a zero is represented by a space for normal polarity, and the opposite for inverted polarity. In IrDA

format, a zero is represented by short high pulse in the middle of a bit time remaining idle low for a one for normal
polarity, and a zero is represented by short low pulse in the middle of a bit time remaining idle high for a one for
inverted polarity.
0 Normal polarity
1 Inverted polarity

2 Break Transmit Character Length — This bit determines whether the transmit break character is 10 or 11 bit
BRK13 respectively 13 or 14 bits long. The detection of a framing error is not affected by this bit.

0 Break character is 10 or 11 bit long
1 Break character is 13 or 14 bit long

1 Transmitter Pin Data Direction in Single-Wire Mode — This bit determines whether the TXD pin is going to
TXDIR be used as an input or output, in the single-wire mode of operation. This bit is only relevant in the single-wire

mode of operation.
0 TXD pin to be used as an input in single-wire mode
1 TXD pin to be used as an output in single-wire mode

0 Receiver Active Flag — RAF is set when the receiver detects a logic 0 during the RT1 time period of the start
RAF bit search. RAF is cleared when the receiver detects an idle character.

0 No reception in progress
1 Reception in progress

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 737



Chapter 20 Serial Communication Interface (S12SCIV5)

20.3.2.9 SCI Data Registers (SCIDRH, SCIDRL)

Module Base + 0x0006

7 6 5 4 3 2 1 0
R R8 0 0 0 0 0 0

T8
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 20-12. SCI Data Registers (SCIDRH)

Module Base + 0x0007

7 6 5 4 3 2 1 0
R R7 R6 R5 R4 R3 R2 R1 R0
W T7 T6 T5 T4 T3 T2 T1 T0

Reset 0 0 0 0 0 0 0 0

Figure 20-13. SCI Data Registers (SCIDRL)

Read: Anytime; reading accesses SCI receive data register

Write: Anytime; writing accesses SCI transmit data register; writing to R8 has no effect
Table 20-13. SCIDRH and SCIDRL Field Descriptions

Field Description

SCIDRH Received Bit 8 — R8 is the ninth data bit received when the SCI is configured for 9-bit data format (M = 1).
7

R8

SCIDRH Transmit Bit 8 — T8 is the ninth data bit transmitted when the SCI is configured for 9-bit data format (M = 1).
6

T8

SCIDRL R7:R0 — Received bits seven through zero for 9-bit or 8-bit data formats
7:0 T7:T0 — Transmit bits seven through zero for 9-bit or 8-bit formats

R[7:0]
T[7:0]

NOTE
If the value of T8 is the same as in the previous transmission, T8 does not
have to be rewritten.The same value is transmitted until T8 is rewritten

In 8-bit data format, only SCI data register low (SCIDRL) needs to be
accessed.

When transmitting in 9-bit data format and using 8-bit write instructions,
write first to SCI data register high (SCIDRH), then SCIDRL.

MC9S12XE-Family Reference Manual  Rev. 1.25

738 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

20.4 Functional Description
This section provides a complete functional description of the SCI block, detailing the operation of the
design from the end user perspective in a number of subsections.

Figure 20-14   shows the structure of the SCI module. The SCI allows full duplex, asynchronous, serial
communication between the CPU and remote devices, including other CPUs. The SCI transmitter and
receiver operate independently, although they use the same baud rate generator. The CPU monitors the
status of the SCI, writes the data to be transmitted, and processes received data.

IREN R8

SCI Data
Register NF

FE
RXD Infrared Ir_RXD

Receive SCRXD Receive PF
Shift Register

Decoder RAF ILIE IDLE
RE IDLE

RWU
Receive RDRF

and Wakeup LOOPS
Control OR

RSRC
RIE

Bus M TIE
Clock Baud Rate

Generator WAKE
Data Format

Control ILT TDRE TDRE
PE TC SCI

SBR12:SBR0 PT Interrupt
TCIE TC Request

TE

÷ Transmit RXEDGIE
16 Control LOOPS

SBK
Active Edge RXEDGIF

RSRC Detect
Transmit

T8 BKDIF
Shift Register Break Detect

RXD

SCI Data BKDIE
Register BKDFE

LIN Transmit BERRIF
Collision

SCTXD Detect

R16XCLK BERRIE
Infrared BERRM[1:0]
Transmit Ir_TXD TXD
Encoder

R32XCLK

TNP[1:0] IREN

Figure 20-14. Detailed SCI Block Diagram

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 739

R16XCLK

RDRF/OR



Chapter 20 Serial Communication Interface (S12SCIV5)

20.4.1 Infrared Interface Submodule
This module provides the capability of transmitting narrow pulses to an IR LED and receiving narrow
pulses and transforming them to serial bits, which are sent to the SCI. The IrDA physical layer
specification defines a half-duplex infrared communication link for exchange data. The full standard
includes data rates up to 16 Mbits/s. This design covers only data rates between 2.4 Kbits/s and 115.2
Kbits/s.

The infrared submodule consists of two major blocks: the transmit encoder and the receive decoder. The
SCI transmits serial bits of data which are encoded by the infrared submodule to transmit a narrow pulse
for every zero bit. No pulse is transmitted for every one bit. When receiving data, the IR pulses should be
detected using an IR photo diode and transformed to CMOS levels by the IR receive decoder (external
from the MCU). The narrow pulses are then stretched by the infrared submodule to get back to a serial bit
stream to be received by the SCI.The polarity of transmitted pulses and expected receive pulses can be
inverted so that a direct connection can be made to external IrDA transceiver modules that use active low
pulses.

The infrared submodule receives its clock sources from the SCI. One of these two clocks are selected in
the infrared submodule in order to generate either 3/16, 1/16, 1/32 or 1/4 narrow pulses during
transmission. The infrared block receives two clock sources from the SCI, R16XCLK and R32XCLK,
which are configured to generate the narrow pulse width during transmission. The R16XCLK and
R32XCLK are internal clocks with frequencies 16 and 32 times the baud rate respectively. Both
R16XCLK and R32XCLK clocks are used for transmitting data. The receive decoder uses only the
R16XCLK clock.

20.4.1.1 Infrared Transmit Encoder
The infrared transmit encoder converts serial bits of data from transmit shift register to the TXD pin. A
narrow pulse is transmitted for a zero bit and no pulse for a one bit. The narrow pulse is sent in the middle
of the bit with a duration of 1/32, 1/16, 3/16 or 1/4 of a bit time. A narrow high pulse is transmitted for a
zero bit when TXPOL is cleared, while a narrow low pulse is transmitted for a zero bit when TXPOL is set.

20.4.1.2 Infrared Receive Decoder
The infrared receive block converts data from the RXD pin to the receive shift register. A narrow pulse is
expected for each zero received and no pulse is expected for each one received. A narrow high pulse is
expected for a zero bit when RXPOL is cleared, while a narrow low pulse is expected for a zero bit when
RXPOL is set. This receive decoder meets the edge jitter requirement as defined by the IrDA serial infrared
physical layer specification.

20.4.2 LIN Support
This module provides some basic support for the LIN protocol. At first this is a break detect circuitry
making it easier for the LIN software to distinguish a break character from an incoming data stream. As a
further addition is supports a collision detection at the bit level as well as cancelling pending transmissions.

MC9S12XE-Family Reference Manual  Rev. 1.25

740 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

20.4.3 Data Format
The SCI uses the standard NRZ mark/space data format. When Infrared is enabled, the SCI uses RZI data
format where zeroes are represented by light pulses and ones remain low. See Figure 20-15 below.

8-Bit Data Format
(Bit M in SCICR1 Clear) Possible

Parity
Bit Next

Start Start Standard
Bit Bit 0 Bit 1 Bit 2 Bit 3 Bit 4 Bit 5 Bit 6 Bit 7 STOP Bit SCI Data

Bit

Infrared
SCI Data

9-Bit Data Format
(Bit M in SCICR1 Set) POSSIBLE

PARITY
Bit NEXT

Start START
Bit Standard

Bit 0 Bit 1 Bit 2 Bit 3 Bit 4 Bit 5 Bit 6 Bit 7 Bit 8 STOP Bit SCI Data
Bit

Infrared
SCI Data

Figure 20-15. SCI Data Formats

Each data character is contained in a frame that includes a start bit, eight or nine data bits, and a stop bit.
Clearing the M bit in SCI control register 1 configures the SCI for 8-bit data characters. A frame with eight
data bits has a total of 10 bits. Setting the M bit configures the SCI for nine-bit data characters. A frame
with nine data bits has a total of 11 bits.

Table 20-14. Example of 8-Bit Data Formats

Start Data Address Parity Stop
Bit Bits Bits Bits Bit

1 8 0 0 1
1 7 0 1 1
1 7 1(1) 0 1

1. The address bit identifies the frame as an address
character. See Section ********, “Receiver Wakeup”.

When the SCI is configured for 9-bit data characters, the ninth data bit is the T8 bit in SCI data register
high (SCIDRH). It remains unchanged after transmission and can be used repeatedly without rewriting it.
A frame with nine data bits has a total of 11 bits.

Table 20-15. Example of 9-Bit Data Formats

Start Data Address Parity Stop
Bit Bits Bits Bits Bit

1 9 0 0 1
1 8 0 1 1
1 8 1(1) 0 1

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 741



Chapter 20 Serial Communication Interface (S12SCIV5)

1. The address bit identifies the frame as an address
character. See Section ********, “Receiver Wakeup”.

20.4.4 Baud Rate Generation
A 13-bit modulus counter in the baud rate generator derives the baud rate for both the receiver and the
transmitter. The value from 0 to 8191 written to the SBR12:SBR0 bits determines the bus clock divisor.
The SBR bits are in the SCI baud rate registers (SCIBDH and SCIBDL). The baud rate clock is
synchronized with the bus clock and drives the receiver. The baud rate clock divided by 16 drives the
transmitter. The receiver has an acquisition rate of 16 samples per bit time.

Baud rate generation is subject to one source of error:
• Integer division of the bus clock may not give the exact target frequency.

Table 20-16 lists some examples of achieving target baud rates with a bus clock frequency of 25 MHz.

When IREN = 0 then,
SCI baud rate = SCI bus clock / (16 * SCIBR[12:0])

Table 20-16. Baud Rates (Example: Bus Clock = 25 MHz)

Bits Receiver Transmitter Target Error
SBR[12:0] Clock (Hz) Clock (Hz) Baud Rate (%)

41 609,756.1 38,109.8 38,400 .76
81 308,642.0 19,290.1 19,200 .47
163 153,374.2 9585.9 9,600 .16
326 76,687.1 4792.9 4,800 .15
651 38,402.5 2400.2 2,400 .01
1302 19,201.2 1200.1 1,200 .01
2604 9600.6 600.0 600 .00
5208 4800.0 300.0 300 .00

MC9S12XE-Family Reference Manual  Rev. 1.25

742 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

20.4.5 Transmitter
Internal Bus

Bus
Clock Baud Divider ÷ 16 SCI Data Registers

SBR12:SBR0

11-Bit Transmit Register TXPOL
SCTXD

M H 8 7 6 5 4 3 2 1 0 L

LOOP
T8 To Receiver

CONTROL

PE Parity LOOPS
PT Generation

RSRC

TDRE IRQ TIE

TDRE
Transmitter Control

TC
 TC IRQ

TCIE
TE SBK BERRM[1:0]

SCTXD
BERRIF Transmit

BER IRQ  Collision Detect SCRXD
TCIE (From Receiver)

Figure 20-16. Transmitter Block Diagram

20.4.5.1 Transmitter Character Length
The SCI transmitter can accommodate either 8-bit or 9-bit data characters. The state of the M bit in SCI
control register 1 (SCICR1) determines the length of data characters. When transmitting 9-bit data, bit T8
in SCI data register high (SCIDRH) is the ninth bit (bit 8).

******** Character Transmission
To transmit data, the MCU writes the data bits to the SCI data registers (SCIDRH/SCIDRL), which in turn
are transferred to the transmitter shift register. The transmit shift register then shifts a frame out through
the TXD pin, after it has prefaced them with a start bit and appended them with a stop bit. The SCI data
registers (SCIDRH and SCIDRL) are the write-only buffers between the internal data bus and the transmit
shift register.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 743

Stop
MSB

Load from SCIDR

Shift Enable

Preamble (All 1s)

Break (All 0s) Start



Chapter 20 Serial Communication Interface (S12SCIV5)

The SCI also sets a flag, the transmit data register empty flag (TDRE), every time it transfers data from the
buffer (SCIDRH/L) to the transmitter shift register.The transmit driver routine may respond to this flag by
writing another byte to the Transmitter buffer (SCIDRH/SCIDRL), while the shift register is still shifting
out the first byte.

To initiate an SCI transmission:
1. Configure the SCI:

a) Select a baud rate. Write this value to the SCI baud registers (SCIBDH/L) to begin the baud
rate generator. Remember that the baud rate generator is disabled when the baud rate is zero.
Writing to the SCIBDH has no effect without also writing to SCIBDL.

b) Write to SCICR1 to configure word length, parity, and other configuration bits
(LOOPS,RSRC,M,WAKE,ILT,PE,PT).

c) Enable the transmitter, interrupts, receive, and wake up as required, by writing to the SCICR2
register bits (TIE,TCIE,RIE,ILIE,TE,RE,RWU,SBK). A preamble or idle character will now
be shifted out of the transmitter shift register.

2. Transmit Procedure for each byte:
a) Poll the TDRE flag by reading the SCISR1 or responding to the TDRE interrupt. Keep in mind

that the TDRE bit resets to one.
b) If the TDRE flag is set, write the data to be transmitted to SCIDRH/L, where the ninth bit is

written to the T8 bit in SCIDRH if the SCI is in 9-bit data format. A new transmission will not
result until the TDRE flag has been cleared.

3. Repeat step 2 for each subsequent transmission.

NOTE
The TDRE flag is set when the shift register is loaded with the next data to
be transmitted from SCIDRH/L, which happens, generally speaking, a little
over half-way through the stop bit of the previous frame. Specifically, this
transfer occurs 9/16ths of a bit time AFTER the start of the stop bit of the
previous frame.

Writing the TE bit from 0 to a 1 automatically loads the transmit shift register with a preamble of 10 logic
1s (if M = 0) or 11 logic 1s (if M = 1). After the preamble shifts out, control logic transfers the data from
the SCI data register into the transmit shift register. A logic 0 start bit automatically goes into the least
significant bit position of the transmit shift register. A logic 1 stop bit goes into the most significant bit
position.

Hardware supports odd or even parity. When parity is enabled, the most significant bit (MSB) of the data
character is the parity bit.

The transmit data register empty flag, TDRE, in SCI status register 1 (SCISR1) becomes set when the SCI
data register transfers a byte to the transmit shift register. The TDRE flag indicates that the SCI data
register can accept new data from the internal data bus. If the transmit interrupt enable bit, TIE, in SCI
control register 2 (SCICR2) is also set, the TDRE flag generates a transmitter interrupt request.

MC9S12XE-Family Reference Manual  Rev. 1.25

744 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

When the transmit shift register is not transmitting a frame, the TXD pin goes to the idle condition, logic
1. If at any time software clears the TE bit in SCI control register 2 (SCICR2), the transmitter enable signal
goes low and the transmit signal goes idle.

If software clears TE while a transmission is in progress (TC = 0), the frame in the transmit shift register
continues to shift out. To avoid accidentally cutting off the last frame in a message, always wait for TDRE
to go high after the last frame before clearing TE.

To separate messages with preambles with minimum idle line time, use this sequence between messages:
1. Write the last byte of the first message to SCIDRH/L.
2. Wait for the TDRE flag to go high, indicating the transfer of the last frame to the transmit shift

register.
3. Queue a preamble by clearing and then setting the TE bit.
4. Write the first byte of the second message to SCIDRH/L.

20.4.5.3 Break Characters
Writing a logic 1 to the send break bit, SBK, in SCI control register 2 (SCICR2) loads the transmit shift
register with a break character. A break character contains all logic 0s and has no start, stop, or parity bit.
Break character length depends on the M bit in SCI control register 1 (SCICR1). As long as SBK is at logic
1, transmitter logic continuously loads break characters into the transmit shift register. After software
clears the SBK bit, the shift register finishes transmitting the last break character and then transmits at least
one logic 1. The automatic logic 1 at the end of a break character guarantees the recognition of the start bit
of the next frame.

The SCI recognizes a break character when there are 10 or 11(M = 0 or M = 1) consecutive zero received.
Depending if the break detect feature is enabled or not receiving a break character has these effects on SCI
registers.

If the break detect feature is disabled (BKDFE = 0):
• Sets the framing error flag, FE
• Sets the receive data register full flag, RDRF
• Clears the SCI data registers (SCIDRH/L)
• May set the overrun flag, OR, noise flag, NF, parity error flag, PE, or the receiver active flag, RAF

(see 3.4.4 and 3.4.5 SCI Status Register 1 and 2)

If the break detect feature is enabled (BKDFE = 1) there are two scenarios1

The break is detected right from a start bit or is detected during a byte reception.
• Sets the break detect interrupt flag, BKDIF
• Does not change the data register full flag, RDRF or overrun flag OR
• Does not change the framing error flag FE, parity error flag PE.
• Does not clear the SCI data registers (SCIDRH/L)
• May set noise flag NF, or receiver active flag RAF.

1. A Break character in this context are either 10 or 11 consecutive zero received bits

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 745



Chapter 20 Serial Communication Interface (S12SCIV5)

Figure 20-17 shows two cases of break detect. In trace RXD_1 the break symbol starts with the start bit,
while in RXD_2 the break starts in the middle of a transmission. If BRKDFE = 1, in RXD_1 case there
will be no byte transferred to the receive buffer and the RDRF flag will not be modified. Also no framing
error or parity error will be flagged from this transfer. In RXD_2 case, however the break signal starts later
during the transmission. At the expected stop bit position the byte received so far will be transferred to the
receive buffer, the receive data register full flag will be set, a framing error and if enabled and appropriate
a parity error will be set. Once the break is detected the BRKDIF flag will be set.

Start Bit Position Stop Bit Position

BRKDIF = 1

RXD_1

Zero Bit Counter 1 2 3 4 5 6 7 8 9 10 . . .

FE = 1 BRKDIF = 1

RXD_2

Zero Bit Counter 1 2 3 4 5 6 7 8 9 10 . . .

Figure 20-17. Break Detection if BRKDFE = 1 (M = 0)

20.4.5.4 Idle Characters
An idle character (or preamble) contains all logic 1s and has no start, stop, or parity bit. Idle character
length depends on the M bit in SCI control register 1 (SCICR1). The preamble is a synchronizing idle
character that begins the first transmission initiated after writing the TE bit from 0 to 1.

If the TE bit is cleared during a transmission, the TXD pin becomes idle after completion of the
transmission in progress. Clearing and then setting the TE bit during a transmission queues an idle
character to be sent after the frame currently being transmitted.

NOTE
When queueing an idle character, return the TE bit to logic 1 before the stop
bit of the current frame shifts out through the TXD pin. Setting TE after the
stop bit appears on TXD causes data previously written to the SCI data
register to be lost. Toggle the TE bit for a queued idle character while the
TDRE flag is set and immediately before writing the next byte to the SCI
data register.

If the TE bit is clear and the transmission is complete, the SCI is not the
master of the TXD pin

MC9S12XE-Family Reference Manual  Rev. 1.25

746 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

******** LIN Transmit Collision Detection
This module allows to check for collisions on the LIN bus.

Synchronizer Stage LIN Physical Interface
Receive Shift
Register

Compare
Bit Error RXD Pin

LIN Bus
Bus Clock

Sample
Point

Transmit Shift
Register TXD Pin

Figure 20-18. Collision Detect Principle

If the bit error circuit is enabled (BERRM[1:0] = 0:1 or = 1:0]), the error detect circuit will compare the
transmitted and the received data stream at a point in time and flag any mismatch. The timing checks run
when transmitter is active (not idle). As soon as a mismatch between the transmitted data and the received
data is detected the following happens:

• The next bit transmitted will have a high level (TXPOL = 0) or low level (TXPOL = 1)
• The transmission is aborted and the byte in transmit buffer is discarded.
• the transmit data register empty and the transmission complete flag will be set
• The bit error interrupt flag, BERRIF, will be set.
• No further transmissions will take place until the BERRIF is cleared.

0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 0

Output Transmit
Shift Register

Input Receive
Shift Register

BERRM[1:0] = 0:1 BERRM[1:0] = 1:1

Compare Sample Points

Figure 20-19. Timing Diagram Bit Error Detection

If the bit error detect feature is disabled, the bit error interrupt flag is cleared.

NOTE
The RXPOL and TXPOL bit should be set the same when transmission
collision detect feature is enabled, otherwise the bit error interrupt flag may
be set incorrectly.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 747

Sampling Begin

Sampling End

Sampling Begin

Sampling End



Chapter 20 Serial Communication Interface (S12SCIV5)

20.4.6 Receiver
Internal Bus

SBR12:SBR0 SCI Data Register

Bus
Clock Baud Divider

11-Bit Receive Shift Register
RXPOL Data

SCRXD Recovery H 8 7 6 5 4 3 2 1 0 L

From TXD Pin Loop
or Transmitter Control RE

RAF

LOOPS FE
M

RSRC NF RWU
WAKE Wakeup

ILT Logic PE

PE Parity R8
PT Checking

Idle IRQ
IDLE

ILIE

BRKDFE RDRF RDRF/OR
    IRQ

OR
RIE

Break BRKDIF
Detect Logic Break IRQ

BRKDIE

Active Edge RXEDGIF
Detect Logic RX Active Edge IRQ

RXEDGIE

Figure 20-20. SCI Receiver Block Diagram

20.4.6.1 Receiver Character Length
The SCI receiver can accommodate either 8-bit or 9-bit data characters. The state of the M bit in SCI
control register 1 (SCICR1) determines the length of data characters. When receiving 9-bit data, bit R8 in
SCI data register high (SCIDRH) is the ninth bit (bit 8).

20.4.6.2 Character Reception
During an SCI reception, the receive shift register shifts a frame in from the RXD pin. The SCI data register
is the read-only buffer between the internal data bus and the receive shift register.

After a complete frame shifts into the receive shift register, the data portion of the frame transfers to the
SCI data register. The receive data register full flag, RDRF, in SCI status register 1 (SCISR1) becomes set,

MC9S12XE-Family Reference Manual  Rev. 1.25

748 Freescale Semiconductor

MSB Stop

All 1s

Start



Chapter 20 Serial Communication Interface (S12SCIV5)

indicating that the received byte can be read. If the receive interrupt enable bit, RIE, in SCI control
register 2 (SCICR2) is also set, the RDRF flag generates an RDRF interrupt request.

20.4.6.3 Data Sampling
The RT clock rate. The RT clock is an internal signal with a frequency 16 times the baud rate. To adjust
for baud rate mismatch, the RT clock (see Figure 20-21) is re-synchronized:

• After every start bit
• After the receiver detects a data bit change from logic 1 to logic 0 (after the majority of data bit

samples at RT8, RT9, and RT10 returns a valid logic 1 and the majority of the next RT8, RT9, and
RT10 samples returns a valid logic 0)

To locate the start bit, data recovery logic does an asynchronous search for a logic 0 preceded by three logic
1s.When the falling edge of a possible start bit occurs, the RT clock begins to count to 16.

Start Bit LSB
RXD

Samples 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0

Start Bit Start Bit Data
Qualification Verification Sampling

RT Clock

RT CLock Count

Reset RT Clock

Figure 20-21. Receiver Data Sampling

To verify the start bit and to detect noise, data recovery logic takes samples at RT3, RT5, and RT7.
Figure 20-17 summarizes the results of the start bit verification samples.

Table 20-17. Start Bit Verification

RT3, RT5, and RT7 Samples Start Bit Verification Noise Flag

000 Yes 0
001 Yes 1
010 Yes 1
011 No 0
100 Yes 1
101 No 0
110 No 0
111 No 0

If start bit verification is not successful, the RT clock is reset and a new search for a start bit begins.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 749

RT1

RT1

RT1

RT1

RT1

RT1

RT1

RT1

RT1

RT2

RT3

RT4

RT5

RT6

RT7

RT8

RT9

RT10

RT11

RT12

RT13

RT14

RT15

RT16

RT1

RT2

RT3

RT4



Chapter 20 Serial Communication Interface (S12SCIV5)

To determine the value of a data bit and to detect noise, recovery logic takes samples at RT8, RT9, and
RT10. Table 20-18 summarizes the results of the data bit samples.

Table 20-18. Data Bit Recovery

RT8, RT9, and RT10 Samples Data Bit Determination Noise Flag

000 0 0
001 0 1
010 0 1
011 1 1
100 0 1
101 1 1
110 1 1
111 1 0

NOTE
The RT8, RT9, and RT10 samples do not affect start bit verification. If any
or all of the RT8, RT9, and RT10 start bit samples are logic 1s following a
successful start bit verification, the noise flag (NF) is set and the receiver
assumes that the bit is a start bit (logic 0).

To verify a stop bit and to detect noise, recovery logic takes samples at RT8, RT9, and RT10. Table 20-19
summarizes the results of the stop bit samples.

Table 20-19. Stop Bit Recovery

RT8, RT9, and RT10 Samples Framing Error Flag Noise Flag

000 1 0
001 1 1
010 1 1
011 0 1
100 1 1
101 0 1
110 0 1
111 0 0

MC9S12XE-Family Reference Manual  Rev. 1.25

750 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

In Figure 20-22 the verification samples RT3 and RT5 determine that the first low detected was noise and
not the beginning of a start bit. The RT clock is reset and the start bit search begins again. The noise flag
is not set because the noise occurred before the start bit was found.

Start Bit LSB
RXD

Samples 1 1 1 0 1 1 1 0 0 0 0 0 0 0

RT Clock

RT Clock Count

Reset RT Clock

Figure 20-22. Start Bit Search Example 1

In Figure 20-23, verification sample at RT3 is high. The RT3 sample sets the noise flag. Although the
perceived bit time is misaligned, the data samples RT8, RT9, and RT10 are within the bit time and data
recovery is successful.

Perceived Start Bit

Actual Start Bit LSB
RXD

Samples 1 1 1 1 1 0 1 0 0 0 0 0

RT Clock

RT Clock Count

Reset RT Clock

Figure 20-23. Start Bit Search Example 2

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 751

RT1 RT1
RT1 RT1
RT1 RT1
RT1 RT1
RT1 RT2
RT1 RT3
RT2 RT4
RT3 RT5
RT4 RT1
RT5 RT1
RT6 RT2
RT7 RT3
RT8 RT4
RT9 RT5

RT10 RT6
RT11 RT7
RT12 RT8
RT13 RT9
RT14 RT10
RT15 RT11
RT16 RT12

RT1 RT13
RT2 RT14
RT3 RT15
RT4 RT16
RT5 RT1
RT6 RT2
RT7 RT3



Chapter 20 Serial Communication Interface (S12SCIV5)

In Figure 20-24, a large burst of noise is perceived as the beginning of a start bit, although the test sample
at RT5 is high. The RT5 sample sets the noise flag. Although this is a worst-case misalignment of perceived
bit time, the data samples RT8, RT9, and RT10 are within the bit time and data recovery is successful.

Perceived Start Bit

Actual Start Bit LSB
RXD

Samples 1 1 1 0 0 1 0 0 0 0

RT Clock

RT Clock Count

Reset RT Clock

Figure 20-24. Start Bit Search Example 3

Figure 20-25 shows the effect of noise early in the start bit time. Although this noise does not affect proper
synchronization with the start bit time, it does set the noise flag.

Perceived and Actual Start Bit LSB
RXD

Samples 1 1 1 1 1 1 1 1 1 0 1 0

RT Clock

RT Clock Count

Reset RT Clock

Figure 20-25. Start Bit Search Example 4

MC9S12XE-Family Reference Manual  Rev. 1.25

752 Freescale Semiconductor

RT1 RT1
RT1 RT1
RT1 RT1
RT1 RT1
RT1 RT2
RT1 RT3
RT1 RT4
RT1 RT5
RT1 RT6
RT1 RT7
RT2 RT8
RT3 RT9
RT4 RT10
RT5 RT11
RT6 RT12
RT7 RT13
RT8 RT14
RT9 RT15

RT10 RT16
RT11 RT1
RT12 RT2
RT13 RT3
RT14 RT4
RT15 RT5
RT16 RT6

RT1 RT7
RT2 RT8
RT3 RT9



Chapter 20 Serial Communication Interface (S12SCIV5)

Figure 20-26 shows a burst of noise near the beginning of the start bit that resets the RT clock. The sample
after the reset is low but is not preceded by three high samples that would qualify as a falling edge.
Depending on the timing of the start bit search and on the data, the frame may be missed entirely or it may
set the framing error flag.

Start Bit LSB
RXD No Start Bit Found

Samples 1 1 1 1 1 1 1 1 1 0 0 1 1 0 0 0 0 0 0 0 0

RT Clock

RT Clock Count

Reset RT Clock

Figure 20-26. Start Bit Search Example 5

In Figure 20-27, a noise burst makes the majority of data samples RT8, RT9, and RT10 high. This sets the
noise flag but does not reset the RT clock. In start bits only, the RT8, RT9, and RT10 data samples are
ignored.

Start Bit LSB
RXD

Samples 1 1 1 1 1 1 1 1 1 0 0 0 0 1 0 1

RT Clock

RT Clock Count

Reset RT Clock

Figure 20-27. Start Bit Search Example 6

20.4.6.4 Framing Errors
If the data recovery logic does not detect a logic 1 where the stop bit should be in an incoming frame, it
sets the framing error flag, FE, in SCI status register 1 (SCISR1). A break character also sets the FE flag
because a break character has no stop bit. The FE flag is set at the same time that the RDRF flag is set.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 753

RT1 RT1
RT1 RT1
RT1 RT1
RT1 RT1
RT1 RT1
RT1 RT1
RT1 RT1
RT1 RT1
RT1 RT1
RT1 RT1
RT2 RT2
RT3 RT3
RT4 RT4
RT5 RT5
RT6 RT6
RT7 RT7
RT8 RT1
RT9 RT1

RT10 RT1
RT11 RT1
RT12 RT1
RT13 RT1
RT14 RT1
RT15 RT1
RT16 RT1
RT1 RT1
RT2 RT1
RT3 RT1



Chapter 20 Serial Communication Interface (S12SCIV5)

20.4.6.5 Baud Rate Tolerance
A transmitting device may be operating at a baud rate below or above the receiver baud rate. Accumulated
bit time misalignment can cause one of the three stop bit data samples (RT8, RT9, and RT10) to fall outside
the actual stop bit. A noise error will occur if the RT8, RT9, and RT10 samples are not all the same logical
values. A framing error will occur if the receiver clock is misaligned in such a way that the majority of the
RT8, RT9, and RT10 stop bit samples are a logic zero.

As the receiver samples an incoming frame, it re-synchronizes the RT clock on any valid falling edge
within the frame. Re synchronization within frames will correct a misalignment between transmitter bit
times and receiver bit times.

20.4.6.5.1 Slow Data Tolerance
Figure 20-28 shows how much a slow received frame can be misaligned without causing a noise error or
a framing error. The slow stop bit begins at RT8 instead of RT1 but arrives in time for the stop bit data
samples at RT8, RT9, and RT10.

MSB Stop

Receiver
RT Clock

Data
Samples

Figure 20-28. Slow Data

Let’s take RTr as receiver RT clock and RTt as transmitter RT clock.

For an 8-bit data character, it takes the receiver 9 bit times x 16 RTr cycles +7 RTr cycles = 151 RTr cycles
to start data sampling of the stop bit.

With the misaligned character shown in Figure 20-28, the receiver counts 151 RTr cycles at the point when
the count of the transmitting device is 9 bit times x 16 RTt cycles = 144 RTt cycles.

The maximum percent difference between the receiver count and the transmitter count of a slow 8-bit data
character with no errors is:

((151 – 144) / 151) x 100 = 4.63%

For a 9-bit data character, it takes the receiver 10 bit times x 16 RTr cycles + 7 RTr cycles = 167 RTr cycles
to start data sampling of the stop bit.

With the misaligned character shown in Figure 20-28, the receiver counts 167 RTr cycles at the point when
the count of the transmitting device is 10 bit times x 16 RTt cycles = 160 RTt cycles.

The maximum percent difference between the receiver count and the transmitter count of a slow 9-bit
character with no errors is:

((167 – 160) / 167) X 100 = 4.19%

MC9S12XE-Family Reference Manual  Rev. 1.25

754 Freescale Semiconductor

RT1

RT2

RT3

RT4

RT5

RT6

RT7

RT8

RT9

RT10

RT11

RT12

RT13

RT14

RT15

RT16



Chapter 20 Serial Communication Interface (S12SCIV5)

20.4.6.5.2  Fast Data Tolerance
Figure 20-29 shows how much a fast received frame can be misaligned. The fast stop bit ends at RT10
instead of RT16 but is still sampled at RT8, RT9, and RT10.

Stop Idle or Next Frame

Receiver
RT Clock

Data
Samples

Figure 20-29. Fast Data

For an 8-bit data character, it takes the receiver 9 bit times x 16 RTr cycles + 10 RTr cycles = 154 RTr cycles
to finish data sampling of the stop bit.

With the misaligned character shown in Figure 20-29, the receiver counts 154 RTr cycles at the point when
the count of the transmitting device is 10 bit times x 16 RTt cycles = 160 RTt cycles.

The maximum percent difference between the receiver count and the transmitter count of a fast 8-bit
character with no errors is:

((160 – 154) / 160) x 100 = 3.75%

For a 9-bit data character, it takes the receiver 10 bit times x 16 RTr cycles + 10 RTr cycles = 170 RTr cycles
to finish data sampling of the stop bit.

With the misaligned character shown in Figure 20-29, the receiver counts 170 RTr cycles at the point when
the count of the transmitting device is 11 bit times x 16 RTt cycles = 176 RTt cycles.

The maximum percent difference between the receiver count and the transmitter count of a fast 9-bit
character with no errors is:

((176 – 170) /176) x 100 = 3.40%

******** Receiver Wakeup
To enable the SCI to ignore transmissions intended only for other receivers in multiple-receiver systems,
the receiver can be put into a standby state. Setting the receiver wakeup bit, RWU, in SCI control register 2
(SCICR2) puts the receiver into standby state during which receiver interrupts are disabled.The SCI will
still load the receive data into the SCIDRH/L registers, but it will not set the RDRF flag.

The transmitting device can address messages to selected receivers by including addressing information in
the initial frame or frames of each message.

The WAKE bit in SCI control register 1 (SCICR1) determines how the SCI is brought out of the standby
state to process an incoming message. The WAKE bit enables either idle line wakeup or address mark
wakeup.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 755

RT1

RT2

RT3

RT4

RT5

RT6

RT7

RT8

RT9

RT10

RT11

RT12

RT13

RT14

RT15

RT16



Chapter 20 Serial Communication Interface (S12SCIV5)

********.1 Idle Input line Wakeup (WAKE = 0)
In this wakeup method, an idle condition on the RXD pin clears the RWU bit and wakes up the SCI. The
initial frame or frames of every message contain addressing information. All receivers evaluate the
addressing information, and receivers for which the message is addressed process the frames that follow.
Any receiver for which a message is not addressed can set its RWU bit and return to the standby state. The
RWU bit remains set and the receiver remains on standby until another idle character appears on the RXD
pin.

Idle line wakeup requires that messages be separated by at least one idle character and that no message
contains idle characters.

The idle character that wakes a receiver does not set the receiver idle bit, IDLE, or the receive data register
full flag, RDRF.

The idle line type bit, ILT, determines whether the receiver begins counting logic 1s as idle character bits
after the start bit or after the stop bit. ILT is in SCI control register 1 (SCICR1).

********.2 Address Mark Wakeup (WAKE = 1)
In this wakeup method, a logic 1 in the most significant bit (MSB) position of a frame clears the RWU bit
and wakes up the SCI. The logic 1 in the MSB position marks a frame as an address frame that contains
addressing information. All receivers evaluate the addressing information, and the receivers for which the
message is addressed process the frames that follow.Any receiver for which a message is not addressed can
set its RWU bit and return to the standby state. The RWU bit remains set and the receiver remains on
standby until another address frame appears on the RXD pin.

The logic 1 MSB of an address frame clears the receiver’s RWU bit before the stop bit is received and sets
the RDRF flag.

Address mark wakeup allows messages to contain idle characters but requires that the MSB be reserved
for use in address frames.

NOTE
With the WAKE bit clear, setting the RWU bit after the RXD pin has been
idle can cause the receiver to wake up immediately.

20.4.7 Single-Wire Operation
Normally, the SCI uses two pins for transmitting and receiving. In single-wire operation, the RXD pin is
disconnected from the SCI. The SCI uses the TXD pin for both receiving and transmitting.

Transmitter TXD

Receiver RXD

Figure 20-30. Single-Wire Operation (LOOPS = 1, RSRC = 1)

MC9S12XE-Family Reference Manual  Rev. 1.25

756 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

Enable single-wire operation by setting the LOOPS bit and the receiver source bit, RSRC, in SCI control
register 1 (SCICR1). Setting the LOOPS bit disables the path from the RXD pin to the receiver. Setting
the RSRC bit connects the TXD pin to the receiver. Both the transmitter and receiver must be enabled
(TE = 1 and RE = 1).The TXDIR bit (SCISR2[1]) determines whether the TXD pin is going to be used as
an input (TXDIR = 0) or an output (TXDIR = 1) in this mode of operation.

NOTE
In single-wire operation data from the TXD pin is inverted if RXPOL is set.

20.4.8 Loop Operation
In loop operation the transmitter output goes to the receiver input. The RXD pin is disconnected from the
SCI.

Transmitter TXD

Receiver RXD

Figure 20-31. Loop Operation (LOOPS = 1, RSRC = 0)

Enable loop operation by setting the LOOPS bit and clearing the RSRC bit in SCI control register 1
(SCICR1). Setting the LOOPS bit disables the path from the RXD pin to the receiver. Clearing the RSRC
bit connects the transmitter output to the receiver input. Both the transmitter and receiver must be enabled
(TE = 1 and RE = 1).

NOTE
In loop operation data from the transmitter is not recognized by the receiver
if RXPOL and TXPOL are not the same.

20.5 Initialization/Application Information

20.5.1 Reset Initialization
See Section 20.3.2, “Register Descriptions”.

20.5.2 Modes of Operation

******** Run Mode
Normal mode of operation.

To initialize a SCI transmission, see Section ********, “Character Transmission”.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 757



Chapter 20 Serial Communication Interface (S12SCIV5)

******** Wait Mode
SCI operation in wait mode depends on the state of the SCISWAI bit in the SCI control register 1
(SCICR1).

• If SCISWAI is clear, the SCI operates normally when the CPU is in wait mode.
• If SCISWAI is set, SCI clock generation ceases and the SCI module enters a power-conservation

state when the CPU is in wait mode. Setting SCISWAI does not affect the state of the receiver
enable bit, RE, or the transmitter enable bit, TE.
If SCISWAI is set, any transmission or reception in progress stops at wait mode entry. The
transmission or reception resumes when either an internal or external interrupt brings the CPU out
of wait mode. Exiting wait mode by reset aborts any transmission or reception in progress and
resets the SCI.

20.5.2.3 Stop Mode
The SCI is inactive during stop mode for reduced power consumption. The STOP instruction does not
affect the SCI register states, but the SCI bus clock will be disabled. The SCI operation resumes from
where it left off after an external interrupt brings the CPU out of stop mode. Exiting stop mode by reset
aborts any transmission or reception in progress and resets the SCI.

The receive input active edge detect circuit is still active in stop mode. An active edge on the receive input
can be used to bring the CPU out of stop mode.

20.5.3 Interrupt Operation
This section describes the interrupt originated by the SCI block.The MCU must service the interrupt
requests. Table 20-20 lists the eight interrupt sources of the SCI.

Table 20-20. SCI Interrupt Sources

Interrupt Source Local Enable Description

TDRE SCISR1[7] TIE Active high level. Indicates that a byte was transferred from SCIDRH/L to the
transmit shift register.

TC SCISR1[6] TCIE Active high level. Indicates that a transmit is complete.
RDRF SCISR1[5] RIE Active high level. The RDRF interrupt indicates that received data is available

in the SCI data register.
OR SCISR1[3] Active high level. This interrupt indicates that an overrun condition has occurred.

IDLE SCISR1[4] ILIE Active high level. Indicates that receiver input has become idle.
RXEDGIF SCIASR1[7] RXEDGIE Active high level. Indicates that an active edge (falling for RXPOL = 0, rising for

RXPOL = 1) was detected.
BERRIF SCIASR1[1] BERRIE Active high level. Indicates that a mismatch between transmitted and received data

in a single wire application has happened.
BKDIF SCIASR1[0] BRKDIE Active high level. Indicates that a break character has been received.

MC9S12XE-Family Reference Manual  Rev. 1.25

758 Freescale Semiconductor



Chapter 20 Serial Communication Interface (S12SCIV5)

******** Description of Interrupt Operation
The SCI only originates interrupt requests. The following is a description of how the SCI makes a request
and how the MCU should acknowledge that request. The interrupt vector offset and interrupt number are
chip dependent. The SCI only has a single interrupt line (SCI Interrupt Signal, active high operation) and
all the following interrupts, when generated, are ORed together and issued through that port.

********.1 TDRE Description
The TDRE interrupt is set high by the SCI when the transmit shift register receives a byte from the SCI
data register. A TDRE interrupt indicates that the transmit data register (SCIDRH/L) is empty and that a
new byte can be written to the SCIDRH/L for transmission.Clear TDRE by reading SCI status register 1
with TDRE set and then writing to SCI data register low (SCIDRL).

********.2 TC Description
The TC interrupt is set by the SCI when a transmission has been completed. Transmission is completed
when all bits including the stop bit (if transmitted) have been shifted out and no data is queued to be
transmitted. No stop bit is transmitted when sending a break character and the TC flag is set (providing
there is no more data queued for transmission) when the break character has been shifted out. A TC
interrupt indicates that there is no transmission in progress. TC is set high when the TDRE flag is set and
no data, preamble, or break character is being transmitted. When TC is set, the TXD pin becomes idle
(logic 1). Clear TC by reading SCI status register 1 (SCISR1) with TC set and then writing to SCI data
register low (SCIDRL).TC is cleared automatically when data, preamble, or break is queued and ready to
be sent.

********.3 RDRF Description
The RDRF interrupt is set when the data in the receive shift register transfers to the SCI data register. A
RDRF interrupt indicates that the received data has been transferred to the SCI data register and that the
byte can now be read by the MCU. The RDRF interrupt is cleared by reading the SCI status register one
(SCISR1) and then reading SCI data register low (SCIDRL).

********.4 OR Description
The OR interrupt is set when software fails to read the SCI data register before the receive shift register
receives the next frame. The newly acquired data in the shift register will be lost in this case, but the data
already in the SCI data registers is not affected. The OR interrupt is cleared by reading the SCI status
register one (SCISR1) and then reading SCI data register low (SCIDRL).

********.5 IDLE Description
The IDLE interrupt is set when 10 consecutive logic 1s (if M = 0) or 11 consecutive logic 1s (if M = 1)
appear on the receiver input. Once the IDLE is cleared, a valid frame must again set the RDRF flag before
an idle condition can set the IDLE flag. Clear IDLE by reading SCI status register 1 (SCISR1) with IDLE
set and then reading SCI data register low (SCIDRL).

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 759



Chapter 20 Serial Communication Interface (S12SCIV5)

********.6 RXEDGIF Description
The RXEDGIF interrupt is set when an active edge (falling if RXPOL = 0, rising if RXPOL = 1) on the
RXD pin is detected. Clear RXEDGIF by writing a “1” to the SCIASR1 SCI alternative status register 1.

********.7 BERRIF Description
The BERRIF interrupt is set when a mismatch between the transmitted and the received data in a single
wire application like LIN was detected. Clear BERRIF by writing a “1” to the SCIASR1 SCI alternative
status register 1. This flag is also cleared if the bit error detect feature is disabled.

********.8 BKDIF Description
The BKDIF interrupt is set when a break signal was received. Clear BKDIF by writing a “1” to the
SCIASR1 SCI alternative status register 1. This flag is also cleared if break detect feature is disabled.

20.5.4 Recovery from Wait Mode
The SCI interrupt request can be used to bring the CPU out of wait mode.

20.5.5 Recovery from Stop Mode
An active edge on the receive input can be used to bring the CPU out of stop mode.

MC9S12XE-Family Reference Manual  Rev. 1.25

760 Freescale Semiconductor