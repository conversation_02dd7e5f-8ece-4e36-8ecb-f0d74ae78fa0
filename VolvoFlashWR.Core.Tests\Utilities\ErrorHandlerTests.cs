using System;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Utilities;

namespace VolvoFlashWR.Core.Tests.Utilities
{
    [TestFixture]
    public class ErrorHandlerTests
    {
        private Mock<ILoggingService> _mockLoggingService;

        [SetUp]
        public void Setup()
        {
            _mockLoggingService = new Mock<ILoggingService>();
        }

        [Test]
        public void HandleException_LogsException()
        {
            // Arrange
            var exception = new InvalidOperationException("Test exception");
            string source = "TestSource";
            string message = "Error occurred";

            // Act
            ErrorHandler.HandleException<object>(exception, source, message, _mockLoggingService.Object);

            // Assert
            _mockLoggingService.Verify(m => m.LogError(It.IsAny<string>(), source, exception), Times.Once);
        }

        [Test]
        public async Task HandleExceptionAsync_WithCriticalException_LogsError()
        {
            // Arrange
            var exception = new OutOfMemoryException("Out of memory");
            string source = "TestSource";
            string message = "Critical error";

            // Act
            await ErrorHandler.HandleExceptionAsync<object>(exception, source, message, _mockLoggingService.Object);

            // Assert
            _mockLoggingService.Verify(m => m.LogError(It.IsAny<string>(), source, exception), Times.Once);
        }

        [Test]
        public async Task ExecuteWithErrorHandlingAsync_WithSuccessfulAction_ReturnsTrue()
        {
            // Arrange
            Func<Task> action = () => Task.CompletedTask;
            string source = "TestSource";

            // Act
            bool result = await ErrorHandler.ExecuteWithErrorHandlingAsync(action, source, _mockLoggingService.Object);

            // Assert
            Assert.That(result, Is.True);
            _mockLoggingService.Verify(m => m.LogError(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Exception>()), Times.Never);
        }

        [Test]
        public async Task ExecuteWithErrorHandlingAsync_WithExceptionThrowingAction_ReturnsFalse()
        {
            // Arrange
            Func<Task> action = () => Task.FromException(new InvalidOperationException("Test exception"));
            string source = "TestSource";

            // Act
            bool result = await ErrorHandler.ExecuteWithErrorHandlingAsync(action, source, _mockLoggingService.Object);

            // Assert
            Assert.That(result, Is.False);
            _mockLoggingService.Verify(m => m.LogError(It.IsAny<string>(), source, It.IsAny<Exception>()), Times.Once);
        }

        [Test]
        public async Task ExecuteWithErrorHandlingAsync_WithGenericReturnType_ReturnsDefaultValueOnException()
        {
            // Arrange
            Func<Task<int>> func = () => Task.FromException<int>(new InvalidOperationException("Test exception"));
            int defaultValue = 42;
            string source = "TestSource";

            // Act
            int result = await ErrorHandler.ExecuteWithErrorHandlingAsync(func, defaultValue, source, _mockLoggingService.Object);

            // Assert
            Assert.That(result, Is.EqualTo(defaultValue));
            _mockLoggingService.Verify(m => m.LogError(It.IsAny<string>(), source, It.IsAny<Exception>()), Times.Once);
        }

        [Test]
        public async Task ExecuteWithErrorHandlingAsync_WithGenericReturnType_ReturnsValueOnSuccess()
        {
            // Arrange
            int expectedValue = 42;
            Func<Task<int>> func = () => Task.FromResult(expectedValue);
            string source = "TestSource";

            // Act
            int result = await ErrorHandler.ExecuteWithErrorHandlingAsync(func, 0, source, _mockLoggingService.Object);

            // Assert
            Assert.That(result, Is.EqualTo(expectedValue));
            _mockLoggingService.Verify(m => m.LogError(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Exception>()), Times.Never);
        }

        [Test]
        public async Task ExecuteWithRetryAsync_WithTransientException_RetriesAndEventuallySucceeds()
        {
            // Arrange
            int expectedValue = 42;
            int attemptCount = 0;
            int maxRetries = 3;

            Func<Task<int>> func = () =>
            {
                attemptCount++;
                if (attemptCount < 3)
                {
                    return Task.FromException<int>(new TimeoutException("Timeout"));
                }
                return Task.FromResult(expectedValue);
            };

            // Act
            int result = await ErrorHandler.ExecuteWithRetryAsync(func, maxRetries, 10, "TestSource", _mockLoggingService.Object);

            // Assert
            Assert.That(result, Is.EqualTo(expectedValue));
            Assert.That(attemptCount, Is.EqualTo(3));
            _mockLoggingService.Verify(m => m.LogWarning(It.IsAny<string>(), "TestSource"), Times.Exactly(2));
        }

        [Test]
        public void ExecuteWithRetryAsync_WithNonTransientException_ThrowsExceptionImmediately()
        {
            // Arrange
            int attemptCount = 0;
            int maxRetries = 3;

            Func<Task<int>> func = () =>
            {
                attemptCount++;
                return Task.FromException<int>(new ArgumentException("Invalid argument"));
            };

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(async () =>
                await ErrorHandler.ExecuteWithRetryAsync(func, maxRetries, 10, "TestSource", _mockLoggingService.Object));

            Assert.That(ex.Message, Is.EqualTo("Invalid argument"));
            Assert.That(attemptCount, Is.EqualTo(1));
        }

        [Test]
        public void ExecuteWithRetryAsync_WithTooManyFailures_ThrowsLastException()
        {
            // Arrange
            int attemptCount = 0;
            int maxRetries = 2;

            Func<Task<int>> func = () =>
            {
                attemptCount++;
                return Task.FromException<int>(new TimeoutException($"Timeout attempt {attemptCount}"));
            };

            // Act & Assert
            var ex = Assert.ThrowsAsync<TimeoutException>(async () =>
                await ErrorHandler.ExecuteWithRetryAsync(func, maxRetries, 10, "TestSource", _mockLoggingService.Object));

            Assert.That(ex.Message, Is.EqualTo("Timeout attempt 3"));
            Assert.That(attemptCount, Is.EqualTo(3));
            _mockLoggingService.Verify(m => m.LogWarning(It.IsAny<string>(), "TestSource"), Times.Exactly(2));
            _mockLoggingService.Verify(m => m.LogError(It.IsAny<string>(), "TestSource", It.IsAny<Exception>()), Times.Once);
        }

        [Test]
        public void GetUserFriendlyErrorMessage_ReturnsUserFriendlyMessage()
        {
            // Arrange
            var exception = new FileNotFoundException("File not found", "test.txt");

            // Act
            string message = ErrorHandler.GetUserFriendlyErrorMessage(exception);

            // Assert
            Assert.That(message, Is.Not.Null);
            Assert.That(message, Does.Contain("file was not found").Or.Contain("file is accessible"));
            Assert.That(message, Does.Not.Contain("FileNotFoundException"));
        }

        [Test]
        public void IsCriticalException_WithCriticalException_ReturnsTrue()
        {
            // Arrange
            var exception = new OutOfMemoryException("Out of memory");

            // Act
            bool isCritical = ErrorHandler.IsCriticalException(exception);

            // Assert
            Assert.That(isCritical, Is.True);
        }

        [Test]
        public void IsCriticalException_WithNonCriticalException_ReturnsFalse()
        {
            // Arrange
            var exception = new ArgumentException("Argument exception");

            // Act
            bool isCritical = ErrorHandler.IsCriticalException(exception);

            // Assert
            Assert.That(isCritical, Is.False);
        }
    }
}

