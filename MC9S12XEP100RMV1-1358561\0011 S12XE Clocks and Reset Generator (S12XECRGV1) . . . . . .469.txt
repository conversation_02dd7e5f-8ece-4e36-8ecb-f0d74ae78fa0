﻿Chapter 11
S12XE Clocks and Reset Generator (S12XECRGV1)

Table 11-1. Revision History

Revision Revision Sections
Description of Changes

Number Date Affected

V01.03 1 Sep. 2008 Table 11-14 added 100MHz example for PLL
V01.04 20 Nov. 2008 ********/11-475 S12XECRG Flags Register: corrected address to Module Base + 0x0003
V01.05 19. Sep 2009 11.5.1/11-495 Modified Note below Table 11-17./11-495

Added footnote concerning maximum clock frequencies to table
Table 11-14

V01.06 18. Sep 2012 Removed redundant examples from table
11.5.1

Replaced reference to MMC documentation

11.1 Introduction
This specification describes the function of the Clocks and Reset Generator (S12XECRG).

11.1.1 Features
The main features of this block are:

• Phase Locked Loop (IPLL) frequency multiplier with internal filter
— Reference divider
— Post divider
— Configurable internal filter (no external pin)
— Optional frequency modulation for defined jitter and reduced emission
— Automatic frequency lock detector
— Interrupt request on entry or exit from locked condition
— Self Clock Mode in absence of reference clock

• System Clock Generator
— Clock Quality Check
— User selectable fast wake-up from Stop in Self-Clock Mode for power saving and immediate

program execution
— Clock switch for either Oscillator or PLL based system clocks

• Computer Operating Properly (COP) watchdog timer with time-out clear window.
• System Reset generation from the following possible sources:

— Power on reset
— Low voltage reset

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 469



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

— Illegal address reset
— COP reset
— Loss of clock reset
— External pin reset

• Real-Time Interrupt (RTI)

11.1.2 Modes of Operation
This subsection lists and briefly describes all operating modes supported by the S12XECRG.

• Run Mode
All functional parts of the S12XECRG are running during normal Run Mode. If RTI or COP
functionality is required the individual bits of the associated rate select registers (COPCTL,
RTICTL) have to be set to a non zero value.

• Wait Mode
In this mode the IPLL can be disabled automatically depending on the PLLWAI bit.

• Stop Mode
Depending on the setting of the PSTP bit Stop Mode can be differentiated between Full Stop Mode
(PSTP = 0) and Pseudo Stop Mode (PSTP = 1).
— Full Stop Mode

The oscillator is disabled and thus all system and core clocks are stopped. The COP and the
RTI remain frozen.

— Pseudo Stop Mode
The oscillator continues to run and most of the system and core clocks are stopped. If the
respective enable bits are set the COP and RTI will continue to run, else they remain frozen.

• Self Clock Mode
Self Clock Mode will be entered if the Clock Monitor Enable Bit (CME) and the Self Clock Mode
Enable Bit (SCME) are both asserted and the clock monitor in the oscillator block detects a loss of
clock. As soon as Self Clock Mode is entered the S12XECRG starts to perform a clock quality
check. Self Clock Mode remains active until the clock quality check indicates that the required
quality of the incoming clock signal is met (frequency and amplitude). Self Clock Mode should be
used for safety purposes only. It provides reduced functionality to the MCU in case a loss of clock
is causing severe system conditions.

11.1.3 Block Diagram
Figure 11-1 shows a block diagram of the S12XECRG.

MC9S12XE-Family Reference Manual  Rev. 1.25

470 Freescale Semiconductor



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

Illegal Address Reset
S12X_MMC

Power on Reset
Voltage

Regulator Low Voltage Reset

ICRG

RESET
Reset System Reset

CM Fail Generator
Clock

Monitor
XCLKS

OSCCLK Clock Quality
EXTAL Checker

Bus Clock
Oscillator

XTAL
Core Clock

COP RTI

Oscillator Clock

Registers

PLLCLK
V Real Time Interrupt

DDPLL
IPLL Clock and Reset Control

VSSPLL PLL Lock Interrupt

Self Clock Mode
Interrupt

Figure 11-1. Block diagram of S12XECRG

11.2 Signal Description
This section lists and describes the signals that connect off chip.

11.2.1 VDDPLL, VSSPLL
These pins provides operating voltage (VDDPLL) and ground (VSSPLL) for the IPLL circuitry. This allows
the supply voltage to the IPLL to be independently bypassed. Even if IPLL usage is not required VDDPLL
and VSSPLL must be connected to properly.

11.2.2 RESET
RESET is an active low bidirectional reset pin. As an input it initializes the MCU asynchronously to a
known start-up state. As an open-drain output it indicates that an system reset (internal to MCU) has been
triggered.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 471

COP Timeout



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

11.3 Memory Map and Registers
This section provides a detailed description of all registers accessible in the S12XECRG.

11.3.1 Module Memory Map
Figure 11-2 gives an overview on all S12XECRG registers.

Address Name Bit 7 6 5 4 3 2 1 Bit 0
R

0x0000 SYNR VCOFRQ[1:0] SYNDIV[5:0]
W
R

0x0001 REFDV REFFRQ[1:0] REFDIV[5:0]
W
R 0 0 0

0x0002 POSTDIV POSTDIV[4:0]
W
R LOCK SCM

0x0003 CRGFLG RTIF PORF LVRF LOCKIF ILAF SCMIF
W
R 0 0 0 0 0

0x0004 CRGINT RTIE LOCKIE SCMIE
W
R XCLKS 0 0

0x0005 CLKSEL PLLSEL PSTP PLLWAI RTIWAI COPWAI
W
R

0x0006 PLLCTL CME PLLON FM1 FM0 FSTWKP PRE PCE SCME
W
R

0x0007 RTICTL RTDEC RTR6 RTR5 RTR4 RTR3 RTR2 RTR1 RTR0
W
R 0 0 0

0x0008 COPCTL WCOP RSBCK CR2 CR1 CR0
W WRTMASK

0x0009 FORBYP2 R 0 0 0 0 0 0 0 0
W

0x000A CTCTL2 R 0 0 0 0 0 0 0 0
W
R 0 0 0 0 0 0 0 0

0x000B ARMCOP
W Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

2. FORBYP and CTCTL are intended for factory test purposes only.
= Unimplemented or Reserved

Figure 11-2. CRG Register Summary

NOTE
Register Address = Base Address + Address Offset, where the Base Address
is defined at the MCU level and the Address Offset is defined at the module
level.

MC9S12XE-Family Reference Manual  Rev. 1.25

472 Freescale Semiconductor



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

11.3.2 Register Descriptions
This section describes in address order all the S12XECRG registers and their individual bits.

******** S12XECRG Synthesizer Register (SYNR)
The SYNR register controls the multiplication factor of the IPLL and selects the VCO frequency range.

 Module Base + 0x0000

7 6 5 4 3 2 1 0

R
VCOFRQ[1:0] SYNDIV[5:0]

W

Reset 0 0 0 0 0 0 0 0

Figure 11-3. S12XECRG Synthesizer Register (SYNR)

Read: Anytime

Write: Anytime except if PLLSEL = 1

NOTE
Write to this register initializes the lock detector bit.

fVCO 2 × f (SYNDIV + 1)
= OSC × -(--R----E----F----D----I---V----------+ 1----)-

f
f VCO
PLL =

2-----×-----P---O-----S---T-----D----I---V--

f
f = PLL
BUS ------2------

NOTE
fVCO must be within the specified VCO frequency lock range. F.BUS (Bus
Clock) must not exceed the specified maximum. If POSTDIV = $00 then
fPLL is same as fVCO (divide by one).

The VCOFRQ[1:0] bit are used to configure the VCO gain for optimal stability and lock time. For correct
IPLL operation the VCOFRQ[1:0] bits have to be selected according to the actual target VCOCLK
frequency as shown in Table 11-2. Setting the VCOFRQ[1:0] bits wrong can result in a non functional
IPLL (no locking and/or insufficient stability).

Table 11-2. VCO Clock Frequency Selection

VCOCLK Frequency Ranges VCOFRQ[1:0]

32MHz <= fVCO<= 48MHz 00
48MHz < fVCO<= 80MHz 01

Reserved 10
80MHz < fVCO <= 120MHz 11

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 473



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

******** S12XECRG Reference Divider Register (REFDV)
The REFDV register provides a finer granularity for the IPLL multiplier steps.

 Module Base + 0x0001

7 6 5 4 3 2 1 0

R
REFFRQ[1:0] REFDIV[5:0]

W

Reset 0 0 0 0 0 0 0 0

Figure 11-4. S12XECRG Reference Divider Register (REFDV)

Read: Anytime

Write: Anytime except when PLLSEL = 1

NOTE
Write to this register initializes the lock detector bit.

f
f = OSC
REF (---R----E----F---D-----I--V---------------+ 1)

The REFFRQ[1:0] bit are used to configure the internal PLL filter for optimal stability and lock time. For
correct IPLL operation the REFFRQ[1:0] bits have to be selected according to the actual REFCLK
frequency as shown in Figure 11-3. Setting the REFFRQ[1:0] bits wrong can result in a non functional
IPLL (no locking and/or insufficient stability).

Table 11-3. Reference Clock Frequency Selection

REFCLK Frequency Ranges REFFRQ[1:0]

1MHz <= fREF <= 2MHz 00

2MHz < fREF <= 6MHz 01

6MHz < fREF <= 12MHz 10

fREF >12MHz 11

******** S12XECRG Post Divider Register (POSTDIV)
The POSTDIV register controls the frequency ratio between the VCOCLK and PLLCLK. The count in the
final divider divides VCOCLK frequency by 1 or 2*POSTDIV. Note that if POSTDIV = $00 fPLL= fVCO
(divide by one).

MC9S12XE-Family Reference Manual  Rev. 1.25

474 Freescale Semiconductor



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

 Module Base + 0x0002

7 6 5 4 3 2 1 0

R 0 0 0
POSTDIV[4:0]

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 11-5. S12XECRG Post Divider Register (POSTDIV)

Read: Anytime

Write: Anytime except if PLLSEL = 1

f
f = VCO
PLL (---2---x---P----O-----S---T----D-----I--V-----)

NOTE
If POSTDIV = $00 then fPLL is identical to fVCO (divide by one).

******** S12XECRG Flags Register (CRGFLG)
This register provides S12XECRG status bits and flags.

 Module Base + 0x0003

7 6 5 4 3 2 1 0

R LOCK SCM
RTIF PORF LVRF LOCKIF ILAF SCMIF

W

Reset 0 Note 1 Note 2 Note 3 0 0 0 0

1. PORF is set to 1 when a power on reset occurs. Unaffected by system reset.
2. LVRF is set to 1 when a low voltage reset occurs. Unaffected by system reset.
3. ILAF is set to 1 when an illegal address reset occurs. Unaffected by system reset. Cleared by power on or low voltage reset.

= Unimplemented or Reserved

Figure 11-6. S12XECRG Flags Register (CRGFLG)

Read: Anytime

Write: Refer to each bit for individual write conditions

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 475



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

Table 11-4. CRGFLG Field Descriptions

Field Description

7 Real Time Interrupt Flag — RTIF is set to 1 at the end of the RTI period. This flag can only be cleared by writing
RTIF a 1. Writing a 0 has no effect. If enabled (RTIE=1), RTIF causes an interrupt request.

0 RTI time-out has not yet occurred.
1 RTI time-out has occurred.

6 Power on Reset Flag — PORF is set to 1 when a power on reset occurs. This flag can only be cleared by writing
PORF a 1. Writing a 0 has no effect.

0 Power on reset has not occurred.
1 Power on reset has occurred.

5 Low Voltage Reset Flag — LVRF is set to 1 when a low voltage reset occurs. This flag can only be cleared by
LVRF writing a 1. Writing a 0 has no effect.

0 Low voltage reset has not occurred.
1 Low voltage reset has occurred.

4 IPLL Lock Interrupt Flag — LOCKIF is set to 1 when LOCK status bit changes. This flag can only be cleared
LOCKIF by writing a 1. Writing a 0 has no effect.If enabled (LOCKIE=1), LOCKIF causes an interrupt request.

0 No change in LOCK bit.
1 LOCK bit has changed.

3 Lock Status Bit — LOCK reflects the current state of IPLL lock condition. This bit is cleared in Self Clock Mode.
LOCK Writes have no effect.

0 VCOCLK is not within the desired tolerance of the target frequency.
1 VCOCLK is within the desired tolerance of the target frequency.

2 Illegal Address Reset Flag — ILAF is set to 1 when an illegal address reset occurs. Refer to S12XMMC Block
ILAF Guide for details. This flag can only be cleared by writing a 1. Writing a 0 has no effect.

0 Illegal address reset has not occurred.
1 Illegal address reset has occurred.

1 Self Clock Mode Interrupt Flag — SCMIF is set to 1 when SCM status bit changes. This flag can only be
SCMIF cleared by writing a 1. Writing a 0 has no effect. If enabled (SCMIE=1), SCMIF causes an interrupt request.

0 No change in SCM bit.
1 SCM bit has changed.

0 Self Clock Mode Status Bit — SCM reflects the current clocking mode. Writes have no effect.
SCM 0 MCU is operating normally with OSCCLK available.

1 MCU is operating in Self Clock Mode with OSCCLK in an unknown state. All clocks are derived from PLLCLK
running at its minimum frequency fSCM.

******** S12XECRG Interrupt Enable Register (CRGINT)
This register enables S12XECRG interrupt requests.

 Module Base + 0x0004

7 6 5 4 3 2 1 0

R 0 0 0 0 0
RTIE LOCKIE SCMIE

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 11-7. S12XECRG Interrupt Enable Register (CRGINT)

MC9S12XE-Family Reference Manual  Rev. 1.25

476 Freescale Semiconductor



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

Read: Anytime

Write: Anytime
Table 11-5. CRGINT Field Descriptions

Field Description

7 Real Time Interrupt Enable Bit
RTIE 0 Interrupt requests from RTI are disabled.

1 Interrupt will be requested whenever RTIF is set.

4 Lock Interrupt Enable Bit
LOCKIE 0 LOCK interrupt requests are disabled.

1 Interrupt will be requested whenever LOCKIF is set.

1 Self Clock Mode Interrupt Enable Bit
SCMIE 0 SCM interrupt requests are disabled.

1 Interrupt will be requested whenever SCMIF is set.

******** S12XECRG Clock Select Register (CLKSEL)
This register controls S12XECRG clock selection. Refer toFigure 11-16 for more details on the effect of
each bit.

 Module Base + 0x0005

7 6 5 4 3 2 1 0

R XCLKS 0 0
PLLSEL PSTP PLLWAI RTIWAI COPWAI

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 11-8. S12XECRG Clock Select Register (CLKSEL)

Read: Anytime

Write: Refer to each bit for individual write conditions

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 477



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

Table 11-6. CLKSEL Field Descriptions

Field Description

7 PLL Select Bit
PLLSEL Write: Anytime.

Writing a one when LOCK=0 has no effect. This prevents the selection of an unstable PLLCLK as SYSCLK.
PLLSEL bit is cleared when the MCU enters Self Clock Mode, Stop Mode or Wait Mode with PLLWAI bit set.
It is recommended to read back the PLLSEL bit to make sure PLLCLK has really been selected as
SYSCLK, as LOCK status bit could theoretically change at the very moment writing the PLLSEL bit.
0 System clocks are derived from OSCCLK (fBUS = fOSC / 2).
1 System clocks are derived from PLLCLK (fBUS = fPLL / 2).

6 Pseudo Stop Bit
PSTP Write: Anytime

This bit controls the functionality of the oscillator during Stop Mode.
0 Oscillator is disabled in Stop Mode.
1 Oscillator continues to run in Stop Mode (Pseudo Stop).
Note: Pseudo Stop Mode allows for faster STOP recovery and reduces the mechanical stress and aging of the

resonator in case of frequent STOP conditions at the expense of a slightly increased power consumption.
5 Oscillator Configuration Status Bit — This read-only bit shows the oscillator configuration status.

XCLKS 0 Loop controlled Pierce Oscillator is selected.
1 External clock / full swing Pierce Oscillator is selected.

3 PLL Stops in Wait Mode Bit
PLLWAI Write: Anytime

If PLLWAI is set, the S12XECRG will clear the PLLSEL bit before entering Wait Mode. The PLLON bit remains
set during Wait Mode but the IPLL is powered down. Upon exiting Wait Mode, the PLLSEL bit has to be set
manually if PLL clock is required.
0 IPLL keeps running in Wait Mode.
1 IPLL stops in Wait Mode.

1 RTI Stops in Wait Mode Bit
RTIWAI Write: Anytime

0 RTI keeps running in Wait Mode.
1 RTI stops and initializes the RTI dividers whenever the part goes into Wait Mode.

0 COP Stops in Wait Mode Bit
COPWAI Normal modes: Write once

Special modes: Write anytime
0 COP keeps running in Wait Mode.
1 COP stops and initializes the COP counter whenever the part goes into Wait Mode.

******** S12XECRG IPLL Control Register (PLLCTL)
This register controls the IPLL functionality.

 Module Base + 0x0006

7 6 5 4 3 2 1 0

R
CME PLLON FM1 FM0 FSTWKP PRE PCE SCME

W

Reset 1 1 0 0 0 0 0 1

Figure 11-9. S12XECRG IPLL Control Register (PLLCTL)

MC9S12XE-Family Reference Manual  Rev. 1.25

478 Freescale Semiconductor



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

Read: Anytime

Write: Refer to each bit for individual write conditions
Table 11-7. PLLCTL Field Descriptions

Field Description

7 Clock Monitor Enable Bit — CME enables the clock monitor. Write anytime except when SCM = 1.
CME 0 Clock monitor is disabled.

1 Clock monitor is enabled. Slow or stopped clocks will cause a clock monitor reset sequence or Self Clock
Mode.

Note: Operating with CME=0 will not detect any loss of clock. In case of poor clock quality this could cause
unpredictable operation of the MCU!
In Stop Mode (PSTP=0) the clock monitor is disabled independently of the CME bit setting and any loss
of external clock will not be detected.
Also after wake-up from stop mode (PSTP = 0) with fast wake-up enabled (FSTWKP = 1) the clock monitor
is disabled independently of the CME bit setting and any loss of external clock will not be detected.

6 Phase Lock Loop On Bit — PLLON turns on the IPLL circuitry. In Self Clock Mode, the IPLL is turned on, but
PLLON the PLLON bit reads the last written value. Write anytime except when PLLSEL = 1.

0 IPLL is turned off.
1 IPLL is turned on.

5, 4 IPLL Frequency Modulation Enable Bit — FM1 and FM0 enable additional frequency modulation on the
FM1, FM0 VCOCLK. This is to reduce noise emission. The modulation frequency is fref divided by 16. Write anytime except

when PLLSEL = 1. See Table 11-8 for coding.

3 Fast Wake-up from Full Stop Bit — FSTWKP enables fast wake-up from full stop mode. Write anytime. If Self-
FSTWKP Clock Mode is disabled (SCME = 0) this bit has no effect.

0 Fast wake-up from full stop mode is disabled.
1 Fast wake-up from full stop mode is enabled. When waking up from full stop mode the system will immediately

resume operation in Self-Clock Mode (see Section ********, “Clock Quality Checker”). The SCMIF flag will not
be set. The system will remain in Self-Clock Mode with oscillator and clock monitor disabled until FSTWKP bit
is cleared. The clearing of FSTWKP will start the oscillator, the clock monitor and the clock quality check. If
the clock quality check is successful, the S12XECRG will switch all system clocks to OSCCLK. The SCMIF
flag will be set. See application examples in Figure 11-19 and Figure 11-20.

2 RTI Enable During Pseudo Stop Bit — PRE enables the RTI during Pseudo Stop Mode.
PRE Write anytime.

0 RTI stops running during Pseudo Stop Mode.
1 RTI continues running during Pseudo Stop Mode.
Note: If the PRE bit is cleared the RTI dividers will go static while Pseudo Stop Mode is active. The RTI dividers

will not initialize like in Wait Mode with RTIWAI bit set.

1 COP Enable During Pseudo Stop Bit — PCE enables the COP during Pseudo Stop Mode.
PCE Write anytime.

0 COP stops running during Pseudo Stop Mode
1 COP continues running during Pseudo Stop Mode
Note: If the PCE bit is cleared the COP dividers will go static while Pseudo Stop Mode is active. The COP

dividers will not initialize like in Wait Mode with COPWAI bit set.

0 Self Clock Mode Enable Bit
SCME Normal modes: Write once

Special modes: Write anytime
SCME can not be cleared while operating in Self Clock Mode (SCM = 1).
0 Detection of crystal clock failure causes clock monitor reset (see Section 11.5.1.1, “Clock Monitor Reset”).
1 Detection of crystal clock failure forces the MCU in Self Clock Mode (see Section ********, “Self Clock Mode”).

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 479



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

Table 11-8. FM Amplitude selection

FM Amplitude /
FM1 FM0

fVCO Variation

0 0 FM off

0 1 ±1%

1 0 ±2%

1 1 ±4%

******** S12XECRG RTI Control Register (RTICTL)
This register selects the timeout period for the Real Time Interrupt.

 Module Base + 0x0007

7 6 5 4 3 2 1 0

R
RTDEC RTR6 RTR5 RTR4 RTR3 RTR2 RTR1 RTR0

W

Reset 0 0 0 0 0 0 0 0

Figure 11-10. S12XECRG RTI Control Register (RTICTL)

Read: Anytime

Write: Anytime

NOTE
A write to this register initializes the RTI counter.

Table 11-9. RTICTL Field Descriptions

Field Description

7 Decimal or Binary Divider Select Bit — RTDEC selects decimal or binary based prescaler values.
RTDEC 0 Binary based divider value. See Table 11-10

1 Decimal based divider value. See Table 11-11

6–4 Real Time Interrupt Prescale Rate Select Bits — These bits select the prescale rate for the RTI. See Table 11-
RTR[6:4] 10 and Table 11-11.

3–0 Real Time Interrupt Modulus Counter Select Bits — These bits select the modulus counter target value to
RTR[3:0] provide additional granularity.Table 11-10 and Table 11-11 show all possible divide values selectable by the

RTICTL register. The source clock for the RTI is OSCCLK.

Table 11-10. RTI Frequency Divide Rates for RTDEC = 0

RTR[6:4] =
RTR[3:0] 000 001 010 011 100 101 110 111

(OFF) (210) (211) (212) (213) (214) (215) (216)

0000 (÷1) OFF(1) 210 211 212 213 214 215 216

MC9S12XE-Family Reference Manual  Rev. 1.25

480 Freescale Semiconductor



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

Table 11-10. RTI Frequency Divide Rates for RTDEC = 0

RTR[6:4] =
RTR[3:0] 000 001 010 011 100 101 110 111

(OFF) (210) (211) (212) (213) (214) (215) (216)

0001 (÷2) OFF 2x210 2x211 2x212 2x213 2x214 2x215 2x216

0010 (÷3) OFF 3x210 3x211 3x212 3x213 3x214 3x215 3x216

0011 (÷4) OFF 4x210 4x211 4x212 4x213 4x214 4x215 4x216

0100 (÷5) OFF 5x210 5x211 5x212 5x213 5x214 5x215 5x216

0101 (÷6) OFF 6x210 6x211 6x212 6x213 6x214 6x215 6x216

0110 (÷7) OFF 7x210 7x211 7x212 7x213 7x214 7x215 7x216

0111 (÷8) OFF 8x210 8x211 8x212 8x213 8x214 8x215 8x216

1000 (÷9) OFF 9x210 9x211 9x212 9x213 9x214 9x215 9x216

1001 (÷10) OFF 10x210 10x211 10x212 10x213 10x214 10x215 10x216

1010 (÷11) OFF 11x210 11x211 11x212 11x213 11x214 11x215 11x216

1011 (÷12) OFF 12x210 12x211 12x212 12x213 12x214 12x215 12x216

1100 (÷13) OFF 13x210 13x211 13x212 13x213 13x214 13x215 13x216

1101 (÷14) OFF 14x210 14x211 14x212 14x213 14x214 14x215 14x216

1110 (÷15) OFF 15x210 15x211 15x212 15x213 15x214 15x215 15x216

1111 (÷16) OFF 16x210 16x211 16x212 16x213 16x214 16x215 16x216

1. Denotes the default value out of reset.This value should be used to disable the RTI to ensure future backwards compatibility.

Table 11-11. RTI Frequency Divide Rates for RTDEC=1

RTR[6:4] =
RTR[3:0] 000 001 010 011 100 101 110 111

(1x103) (2x103) (5x103) (10x103) (20x103) (50x103) (100x103) (200x103)

0000 (÷1) 1x103 2x103 5x103 10x103 20x103 50x103 100x103 200x103

0001 (÷2) 2x103 4x103 10x103 20x103 40x103 100x103 200x103 400x103

0010 (÷3) 3x103 6x103 15x103 30x103 60x103 150x103 300x103 600x103

0011 (÷4) 4x103 8x103 20x103 40x103 80x103 200x103 400x103 800x103

0100 (÷5) 5x103 10x103 25x103 50x103 100x103 250x103 500x103 1x106

0101 (÷6) 6x103 12x103 30x103 60x103 120x103 300x103 600x103 1.2x106

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 481



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

Table 11-11. RTI Frequency Divide Rates for RTDEC=1

RTR[6:4] =
RTR[3:0] 000 001 010 011 100 101 110 111

(1x103) (2x103) (5x103) (10x103) (20x103) (50x103) (100x103) (200x103)

0110 (÷7) 7x103 14x103 35x103 70x103 140x103 350x103 700x103 1.4x106

0111 (÷8) 8x103 16x103 40x103 80x103 160x103 400x103 800x103 1.6x106

1000 (÷9) 9x103 18x103 45x103 90x103 180x103 450x103 900x103 1.8x106

1001 (÷10) 10 x103 20x103 50x103 100x103 200x103 500x103 1x106 2x106

1010 (÷11) 11 x103 22x103 55x103 110x103 220x103 550x103 1.1x106 2.2x106

1011 (÷12) 12x103 24x103 60x103 120x103 240x103 600x103 1.2x106 2.4x106

1100 (÷13) 13x103 26x103 65x103 130x103 260x103 650x103 1.3x106 2.6x106

1101 (÷14) 14x103 28x103 70x103 140x103 280x103 700x103 1.4x106 2.8x106

1110 (÷15) 15x103 30x103 75x103 150x103 300x103 750x103 1.5x106 3x106

1111 (÷16) 16x103 32x103 80x103 160x103 320x103 800x103 1.6x106 3.2x106

11.3.2.9 S12XECRG COP Control Register (COPCTL)
This register controls the COP (Computer Operating Properly) watchdog.

 Module Base + 0x0008

7 6 5 4 3 2 1 0

R 0 0 0
WCOP RSBCK CR2 CR1 CR0

W WRTMASK

Reset1 0 0 0 0 0 0 0 0

1. Refer to Device User Guide (Section: S12XECRG) for reset values of WCOP, CR2, CR1 and CR0.

= Unimplemented or Reserved

Figure 11-11. S12XECRG COP Control Register (COPCTL)

Read: Anytime

Write:
1. RSBCK: anytime in special modes; write to “1” but not to “0” in all other modes
2. WCOP, CR2, CR1, CR0:

— Anytime in special modes
— Write once in all other modes

– Writing CR[2:0] to “000” has no effect, but counts for the “write once” condition.
– Writing WCOP to “0” has no effect, but counts for the “write once” condition.

MC9S12XE-Family Reference Manual  Rev. 1.25

482 Freescale Semiconductor



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

The COP time-out period is restarted if one these two conditions is true:
1. Writing a non zero value to CR[2:0] (anytime in special modes, once in all other modes) with

WRTMASK = 0.
or

2. Changing RSBCK bit from “0” to “1”.
Table 11-12. COPCTL Field Descriptions

Field Description

7 Window COP Mode Bit — When set, a write to the ARMCOP register must occur in the last 25% of the selected
WCOP period. A write during the first 75% of the selected period will reset the part. As long as all writes occur during

this window, $55 can be written as often as desired. Once $AA is written after the $55, the time-out logic restarts
and the user must wait until the next window before writing to ARMCOP. Table 11-13 shows the duration of this
window for the seven available COP rates.
0 Normal COP operation
1 Window COP operation

6 COP and RTI Stop in Active BDM Mode Bit
RSBCK 0 Allows the COP and RTI to keep running in Active BDM mode.

1 Stops the COP and RTI counters whenever the part is in Active BDM mode.

5 Write Mask for WCOP and CR[2:0] Bit — This write-only bit serves as a mask for the WCOP and CR[2:0] bits
WRTMASK while writing the COPCTL register. It is intended for BDM writing the RSBCK without touching the contents of

WCOP and CR[2:0].
0 Write of WCOP and CR[2:0] has an effect with this write of COPCTL
1 Write of WCOP and CR[2:0] has no effect with this write of COPCTL.

(Does not count for “write once”.)

2–0 COP Watchdog Timer Rate Select — These bits select the COP time-out rate (see Table 11-13). Writing a
CR[2:0] nonzero value to CR[2:0] enables the COP counter and starts the time-out period. A COP counter time-out

causes a system reset. This can be avoided by periodically (before time-out) reinitialize the COP counter via the
ARMCOP register.
While all of the following four conditions are true the CR[2:0], WCOP bits are ignored and the COP operates at
highest time-out period (2 24 cycles) in normal COP mode (Window COP mode disabled):

1) COP is enabled (CR[2:0] is not 000)
2) BDM mode active
3) RSBCK = 0
4) Operation in emulation or special modes

Table 11-13. COP Watchdog Rates(1)

OSCCLK
CR2 CR1 CR0

Cycles to Timeout

0 0 0 COP disabled

0 0 1 2 14

0 1 0 2 16

0 1 1 2 18

1 0 0 2 20

1 0 1 2 22

1 1 0 2 23

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 483



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

Table 11-13. COP Watchdog Rates(1)

OSCCLK
CR2 CR1 CR0

Cycles to Timeout

1 1 1 2 24

1. OSCCLK cycles are referenced from the previous COP time-out reset
(writing $55/$AA to the ARMCOP register)

********0 Reserved Register (FORBYP)
NOTE

This reserved register is designed for factory test purposes only, and is not
intended for general user access. Writing to this register when in special
modes can alter the S12XECRG’s functionality.

 Module Base + 0x0009

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 11-12. Reserved Register (FORBYP)

Read: Always read $00 except in special modes

Write: Only in special modes

********1 Reserved Register (CTCTL)
NOTE

This reserved register is designed for factory test purposes only, and is not
intended for general user access. Writing to this register when in special test
modes can alter the S12XECRG’s functionality.

 Module Base + 0x000A

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W

Reset 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 11-13. Reserved Register (CTCTL)

Read: Always read $00 except in special modes

MC9S12XE-Family Reference Manual  Rev. 1.25

484 Freescale Semiconductor



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

Write: Only in special modes

********2 S12XECRG COP Timer Arm/Reset Register (ARMCOP)
This register is used to restart the COP time-out period.

 Module Base + 0x000B

7 6 5 4 3 2 1 0

R 0 0 0 0 0 0 0 0

W Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

Reset 0 0 0 0 0 0 0 0

Figure 11-14. S12XECRG ARMCOP Register Diagram

Read: Always reads $00

Write: Anytime

When the COP is disabled (CR[2:0] = “000”) writing to this register has no effect.

When the COP is enabled by setting CR[2:0] nonzero, the following applies:
Writing any value other than $55 or $AA causes a COP reset. To restart the COP time-out period
you must write $55 followed by a write of $AA. Other instructions may be executed between these
writes but the sequence ($55, $AA) must be completed prior to COP end of time-out period to
avoid a COP reset. Sequences of $55 writes or sequences of $AA writes are allowed. When the
WCOP bit is set, $55 and $AA writes must be done in the last 25% of the selected time-out period;
writing any value in the first 75% of the selected period will cause a COP reset.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 485



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

11.4 Functional Description

11.4.1 Functional Blocks

******** Phase Locked Loop with Internal Filter (IPLL)
The IPLL is used to run the MCU from a different time base than the incoming OSCCLK. Figure 11-15
shows a block diagram of the IPLL.

REFCLK
LOCK LOCK

REFDIV[5:0]
EXTAL FBCLK DETECTOR

REDUCED
CONSUMPTION OSCCLK

REFERENCE V
OSCILLATOR DDPLL/VSSPLL

PROGRAMMABLE
DIVIDER PDET UP CPUMP

XTAL PHASE DOWN AND VCO
DETECTOR FILTER

CLOCK VCOCLK
MONITOR LOOP

PROGRAMMABLE
DIVIDER POST

PROGRAMMABLE PLLCLK
DIVIDER

Supplied by: SYNDIV[5:0]
VDDPLL/VSSPLL POSTDIV[4:0]

VDD/VSS

Figure 11-15. IPLL Functional Diagram

For increased flexibility, OSCCLK can be divided in a range of 1 to 64 to generate the reference frequency
REFCLK using the REFDIV[5:0] bits. This offers a finer multiplication granularity. Based on the
SYNDIV[5:0] bits the IPLL generates the VCOCLK by multiplying the reference clock by a multiple of
2, 4, 6,... 126, 128. Based on the POSTDIV[4:0] bits the VCOCLK can be divided in a range of 1,2,4,6,8,...
to 62 to generate the PLLCLK.

.

f SY D V +
PLL = 2 × f × N I 1

OSC [---R----E----F---D-----I--V--------------------------------------------------------+ 1][2 × POSTDIV]
NOTE

Although it is possible to set the dividers to command a very high clock
frequency, do not exceed the specified bus frequency limit for the MCU.

If (PLLSEL = 1) then fBUS = fPLL / 2.
IF POSTDIV = $00 the fPLL is identical to fVCO (divide by one)

Several examples of IPLL divider settings are shown in Table 11-14. Shaded rows indicated that these
settings are not recommended. The following rules help to achieve optimum stability and shortest lock
time:

• Use lowest possible fVCO / fREF ratio (SYNDIV value).
• Use highest possible REFCLK frequency fREF.

MC9S12XE-Family Reference Manual  Rev. 1.25

486 Freescale Semiconductor



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

Table 11-14. Examples of IPLL Divider Settings(1)

fOSC REFDIV[5:0] fREF REFFRQ[1:0] SYNDIV[5:0] fVCO VCOFRQ[1:0] POSTDIV[4:0] fPLL fBUS

4MHz $01 2MHz 01 $18 100MHz 11 $00 100MHz 50 MHz

8MHz $03 2MHz 01 $18 100MHz 11 $00 100MHz 50 MHz

4MHz $00 4MHz 01 $09 80MHz 01 $00 80MHz 40MHz

8MHz $00 8MHz 10 $04 80MHz 01 $00 80MHz 40MHz

4MHz $00 4MHz 01 $03 32MHz 00 $01 16MHz 8MHz

4MHz $01 2MHz 01 $18 100MHz 11 $01 50MHz 25MHz
1. fPLL and fBUS values in this table may exceed maximum allowed frequencies for some devices.

Refer to device information for maximum values.

********.1 IPLL Operation
The oscillator output clock signal (OSCCLK) is fed through the reference programmable divider and is
divided in a range of 1 to 64 (REFDIV+1) to output the REFCLK. The VCO output clock, (VCOCLK) is
fed back through the programmable loop divider and is divided in a range of 2 to 128 in increments of [2
x (SYNDIV +1)] to output the FBCLK. The VCOCLK is fed to the final programmable divider and is
divided in a range of 1,2,4,6,8,... to 62 (2*POSTDIV) to output the PLLCLK. See Figure 11-15.

The phase detector then compares the FBCLK, with the REFCLK. Correction pulses are generated based
on the phase difference between the two signals. The loop filter then slightly alters the DC voltage on the
internal filter capacitor, based on the width and direction of the correction pulse.

The user must select the range of the REFCLK frequency and the range of the VCOCLK frequency to
ensure that the correct IPLL loop bandwidth is set.

The lock detector compares the frequencies of the FBCLK, and the REFCLK. Therefore, the speed of the
lock detector is directly proportional to the reference clock frequency. The circuit determines the lock
condition based on this comparison.

If IPLL LOCK interrupt requests are enabled, the software can wait for an interrupt request and then check
the LOCK bit. If interrupt requests are disabled, software can poll the LOCK bit continuously (during
IPLL start-up, usually) or at periodic intervals. In either case, only when the LOCK bit is set, the PLLCLK
can be selected as the source for the system and core clocks. If the IPLL is selected as the source for the
system and core clocks and the LOCK bit is clear, the IPLL has suffered a severe noise hit and the software
must take appropriate action, depending on the application.

• The LOCK bit is a read-only indicator of the locked state of the IPLL.
• The LOCK bit is set when the VCO frequency is within a certain tolerance, ∆Lock, and is cleared

when the VCO frequency is out of a certain tolerance, ∆unl.
• Interrupt requests can occur if enabled (LOCKIE = 1) when the lock condition changes, toggling

the LOCK bit.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 487



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

******** System Clocks Generator

PLLSEL or SCM

OP
PHASE PLLCLK ST

1
LOCK SYSCLK

Core Clock
LOOP (IIPLL) 0

÷2 CLOCK PHASE
SCM ock

WAIT(RTIWAI), GENERATOR Bus Cl

STOP(PSTP, PRE),
EXTAL

1 RTI ENABLE

OSCCLK RTI
OSCILLATOR 0

WAIT(COPWAI),

XTAL STOP(PSTP, PCE),
COP ENABLE

COP
Clock

Monitor

STOP

Gating Oscillator
Clock

Condition

= Clock Gate

Figure 11-16. System Clocks Generator

The clock generator creates the clocks used in the MCU (see Figure 11-16). The gating condition placed
on top of the individual clock gates indicates the dependencies of different modes (STOP, WAIT) and the
setting of the respective configuration bits.

The peripheral modules use the Bus Clock. Some peripheral modules also use the Oscillator Clock. If the
MCU enters Self Clock Mode (see Section ********, “Self Clock Mode”) Oscillator clock source is
switched to PLLCLK running at its minimum frequency fSCM. The Bus Clock is used to generate the clock
visible at the ECLK pin. The Core Clock signal is the clock for the CPU. The Core Clock is twice the Bus
Clock. But note that a CPU cycle corresponds to one Bus Clock.

IPLL clock mode is selected with PLLSEL bit in the CLKSEL register. When selected, the IPLL output
clock drives SYSCLK for the main system including the CPU and peripherals. The IPLL cannot be turned
off by clearing the PLLON bit, if the IPLL clock is selected. When PLLSEL is changed, it takes a
maximum of 4 OSCCLK plus 4 PLLCLK cycles to make the transition. During the transition, all clocks
freeze and CPU activity ceases.

MC9S12XE-Family Reference Manual  Rev. 1.25

488 Freescale Semiconductor



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

******** Clock Monitor (CM)
If no OSCCLK edges are detected within a certain time, the clock monitor within the oscillator block
generates a clock monitor fail event. The S12XECRG then asserts self clock mode or generates a system
reset depending on the state of SCME bit. If the clock monitor is disabled or the presence of clocks is
detected no failure is indicated by the oscillator block.The clock monitor function is enabled/disabled by
the CME control bit.

******** Clock Quality Checker
The clock monitor performs a coarse check on the incoming clock signal. The clock quality checker
provides a more accurate check in addition to the clock monitor.

A clock quality check is triggered by any of the following events:
• Power on reset (POR)
• Low voltage reset (LVR)
• Wake-up from Full Stop Mode (exit full stop)
• Clock Monitor fail indication (CM fail)

A time window of 50000 PLLCLK cycles1 is called check window.

A number greater equal than 4096 rising OSCCLK edges within a check window is called osc ok. Note that
osc ok immediately terminates the current check window. See Figure 11-17 as an example.

CHECK WINDOW

1 2 3 49999 50000

PLLCLK

1 2 3 4 5 4096
OSCCLK

4095

OSC OK

Figure 11-17. Check Window Example

1. IPLL is running at self clock mode frequency fSCM.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 489



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

The Sequence for clock quality check is shown in Figure 11-18.

CM FAIL
CLOCK OK

NO
EXIT FULL STOP

POR
SCME=1 & YES

NUM FSTWKP = 0
LVR FSTWKP=1  = 0 ENTER SCM

?
?

NO YES

CLOCK MONITOR RESET

ENTER SCM NUM = 0
NUM = 50 NO

YES SCM
ACTIVE?

CHECK WINDOW NUM = NUM-1

YES
YES

OSC OK NO NO NO
NUM > 0 SCME = 1

? ? ?
YES

SCM YES SWITCH TO OSCCLK
ACTIVE?

NO
EXIT SCM

Figure 11-18. Sequence for Clock Quality Check

NOTE
Remember that in parallel to additional actions caused by Self Clock Mode
or Clock Monitor Reset1 handling the clock quality checker continues to
check the OSCCLK signal.

NOTE
The Clock Quality Checker enables the IPLL and the voltage regulator
(VREG) anytime a clock check has to be performed. An ongoing clock
quality check could also cause a running IPLL (fSCM) and an active VREG
during Pseudo Stop Mode.

1. A Clock Monitor Reset will always set the SCME bit to logical’1’.

MC9S12XE-Family Reference Manual  Rev. 1.25

490 Freescale Semiconductor



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

******** Computer Operating Properly Watchdog (COP)
The COP (free running watchdog timer) enables the user to check that a program is running and
sequencing properly. When the COP is being used, software is responsible for keeping the COP from
timing out. If the COP times out it is an indication that the software is no longer being executed in the
intended sequence; thus a system reset is initiated (see Section ********, “Computer Operating Properly
Watchdog (COP)”). The COP runs with a gated OSCCLK. Three control bits in the COPCTL register
allow selection of seven COP time-out periods.

When COP is enabled, the program must write $55 and $AA (in this order) to the ARMCOP register
during the selected time-out period. Once this is done, the COP time-out period is restarted. If the program
fails to do this and the COP times out, the part will reset. Also, if any value other than $55 or $AA is
written, the part is immediately reset.

Windowed COP operation is enabled by setting WCOP in the COPCTL register. In this mode, writes to
the ARMCOP register to clear the COP timer must occur in the last 25% of the selected time-out period.
A premature write will immediately reset the part.

If PCE bit is set, the COP will continue to run in Pseudo Stop Mode.

11.4.1.6 Real Time Interrupt (RTI)
The RTI can be used to generate a hardware interrupt at a fixed periodic rate. If enabled (by setting
RTIE=1), this interrupt will occur at the rate selected by the RTICTL register. The RTI runs with a gated
OSCCLK. At the end of the RTI time-out period the RTIF flag is set to one and a new RTI time-out period
starts immediately.

A write to the RTICTL register restarts the RTI time-out period.

If the PRE bit is set, the RTI will continue to run in Pseudo Stop Mode.

11.4.2 Operation Modes

11.4.2.1 Normal Mode
The S12XECRG block behaves as described within this specification in all normal modes.

******** Self Clock Mode
If the external clock frequency is not available due to a failure or due to long crystal start-up time, the Bus
Clock and the Core Clock are derived from the PLLCLK running at self clock mode frequency fSCM; this
mode of operation is called Self Clock Mode. This requires CME = 1 and SCME = 1, which is the default
after reset. If the MCU was clocked by the PLLCLK prior to entering Self Clock Mode, the PLLSEL bit
will be cleared. If the external clock signal has stabilized again, the S12XECRG will automatically select
OSCCLK to be the system clock and return to normal mode. See Section ********, “Clock Quality
Checker” for more information on entering and leaving Self Clock Mode.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 491



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

NOTE
In order to detect a potential clock loss the CME bit should always be
enabled (CME = 1).

If CME bit is disabled and the MCU is configured to run on PLLCLK, a loss
of external clock (OSCCLK) will not be detected and will cause the system
clock to drift towards lower frequencies. As soon as the external clock is
available again the system clock ramps up to its IPLL target frequency. If
the MCU is running on external clock any loss of clock will cause the
system to go static.

11.4.3 Low Power Options
This section summarizes the low power options available in the S12XECRG.

******** Run Mode
This is the default mode after reset.

The RTI can be stopped by setting the associated rate select bits to zero.

The COP can be stopped by setting the associated rate select bits to zero.

******** Wait Mode
The WAI instruction puts the MCU in a low power consumption stand-by mode depending on setting of
the individual bits in the CLKSEL register. All individual Wait Mode configuration bits can be superposed.
This provides enhanced granularity in reducing the level of power consumption during Wait Mode.
Table 11-15 lists the individual configuration bits and the parts of the MCU that are affected in Wait Mode.

Table 11-15. MCU Configuration During Wait Mode

PLLWAI RTIWAI COPWAI

IPLL Stopped — —
RTI — Stopped —

COP — — Stopped

After executing the WAI instruction the core requests the S12XECRG to switch MCU into Wait Mode.
The S12XECRG then checks whether the PLLWAI bit is asserted. Depending on the configuration the
S12XECRG switches the system and core clocks to OSCCLK by clearing the PLLSEL bit and disables
the IPLL.

There are two ways to restart the MCU from Wait Mode:
1. Any reset
2. Any interrupt

MC9S12XE-Family Reference Manual  Rev. 1.25

492 Freescale Semiconductor



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

******** Stop Mode
All clocks are stopped in STOP mode, dependent of the setting of the PCE, PRE and PSTP bit. The
oscillator is disabled in STOP mode unless the PSTP bit is set. If the PRE or PCE bits are set, the RTI or
COP continues to run in Pseudo Stop Mode. In addition to disabling system and core clocks the
S12XECRG requests other functional units of the MCU (e.g. voltage-regulator) to enter their individual
power saving modes (if available).

If the PLLSEL bit is still set when entering Stop Mode, the S12XECRG will switch the system and core
clocks to OSCCLK by clearing the PLLSEL bit. Then the S12XECRG disables the IPLL, disables the core
clock and finally disables the remaining system clocks.

If Pseudo Stop Mode is entered from Self-Clock Mode the S12XECRG will continue to check the clock
quality until clock check is successful. In this case the IPLL and the voltage regulator (VREG) will remain
enabled. If Full Stop Mode (PSTP = 0) is entered from Self-Clock Mode the ongoing clock quality check
will be stopped. A complete timeout window check will be started when Stop Mode is left again.

There are two ways to restart the MCU from Stop Mode:
1. Any reset
2. Any interrupt

If the MCU is woken-up from Full Stop Mode by an interrupt and the fast wake-up feature is enabled
(FSTWKP=1 and SCME=1), the system will immediately (no clock quality check) resume operation in
Self-Clock Mode (see Section ********, “Clock Quality Checker”). The SCMIF flag will not be set for this
special case. The system will remain in Self-Clock Mode with oscillator disabled until FSTWKP bit is
cleared. The clearing of FSTWKP will start the oscillator and the clock quality check. If the clock quality
check is successful, the S12XECRG will switch all system clocks to oscillator clock. The SCMIF flag will
be set. See application examples in Figure 11-19 and Figure 11-20.

Because the IPLL has been powered-down during Stop Mode the PLLSEL bit is cleared and the MCU runs
on OSCCLK after leaving Stop-Mode. The software must manually set the PLLSEL bit again, in order to
switch system and core clocks to the PLLCLK.

NOTE
In Full Stop Mode or Self-Clock Mode caused by the fast wake-up feature
the clock monitor and the oscillator are disabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 493



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

CPU resumes program execution immediately
Instruction

STOP STOP STOP IRQ service
FSTWKP=1 SCME=1 IRQ service IRQ service

Interrupt Interrupt Interrupt

Power Saving

Oscillator Clock

Oscillator Disabled

PLL Clock

Core Clock Self-Clock Mode

Figure 11-19. Fast Wake-up from Full Stop Mode: Example 1
.

CPU resumes program execution immediately Frequent Uncritical Frequent Critical
Instructions Instructions Possible

Instruction IRQ Service
STOP

FSTWKP=1 SCME=1 IRQ Interrupt FSTWKP=0 SCMIE=1
SCM Interrupt

Oscillator Clock Clock Quality Check

Oscillator Disabled Osc Startup

PLL Clock

Core Clock Self-Clock Mode

Figure 11-20. Fast Wake-up from Full Stop Mode: Example 2

11.5 Resets
All reset sources are listed in Table 11-16. Refer to MCU specification for related vector addresses and
priorities.

Table 11-16. Reset Summary

Reset Source Local Enable

Power on Reset None
Low Voltage Reset None

External Reset None
Illegal Address Reset None
Clock Monitor Reset PLLCTL (CME=1, SCME=0)

MC9S12XE-Family Reference Manual  Rev. 1.25

494 Freescale Semiconductor



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

Table 11-16. Reset Summary

Reset Source Local Enable

COP Watchdog Reset COPCTL (CR[2:0] nonzero)

11.5.1 Description of Reset Operation
The reset sequence is initiated by any of the following events:

• Low level is detected at the RESET pin (External Reset).
• Power on is detected.
• Low voltage is detected.
• Illegal Address Reset is detected (refer to device MMC information for details).
• COP watchdog times out.
• Clock monitor failure is detected and Self-Clock Mode was disabled (SCME=0).

Upon detection of any reset event, an internal circuit drives the RESET pin low for 128 SYSCLK cycles
(see Figure 11-21). Since entry into reset is asynchronous it does not require a running SYSCLK.
However, the internal reset circuit of the S12XECRG cannot sequence out of current reset condition
without a running SYSCLK. The number of 128 SYSCLK cycles might be increased by n = 3 to 6
additional SYSCLK cycles depending on the internal synchronization latency. After 128+n SYSCLK
cycles the RESET pin is released. The reset generator of the S12XECRG waits for additional 64 SYSCLK
cycles and then samples the RESET pin to determine the originating source. Table 11-17 shows which
vector will be fetched.

Table 11-17. Reset Vector Selection

Sampled RESET Pin Clock Monitor COP
Vector Fetch

(64 cycles after release) Reset Pending Reset Pending

1 0 0 POR / LVR /
Illegal Address Reset/

External Reset
1 1 X Clock Monitor Reset
1 0 1 COP Reset
0 X X POR / LVR /

Illegal Address Reset/ External Reset
with rise of RESET pin

NOTE
External circuitry connected to the RESET pin should be able to raise the
signal to a valid logic one within 64 SYSCLK cycles after the low drive is
released by the MCU. If this requirement is not adhered to the reset source
will always be recognized as “External Reset” even if the reset was initially
caused by an other reset source.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 495



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

The internal reset of the MCU remains asserted while the reset generator completes the 192 SYSCLK long
reset sequence. In case the RESET pin is externally driven low for more than these 192 SYSCLK cycles
(External Reset), the internal reset remains asserted longer.

Figure 11-21. RESET Timing

RESET
) ( ) (

ICRG drives RESET pin low RESET pin
released

) ) )
SYSCLK ( ( (

128+n cycles 64 cycles

possibly with n being possibly
SYSCLK min 3 / max 6 RESET
not cycles depending driven low
running on internal externally

synchronization
delay

11.5.1.1 Clock Monitor Reset
The S12XECRG generates a Clock Monitor Reset in case all of the following conditions are true:

• Clock monitor is enabled (CME = 1)
• Loss of clock is detected
• Self-Clock Mode is disabled (SCME = 0).

The reset event asynchronously forces the configuration registers to their default settings. In detail the
CME and the SCME are reset to logical ‘1’ (which changes the state of the SCME bit. As a consequence
the S12XECRG immediately enters Self Clock Mode and starts its internal reset sequence. In parallel the
clock quality check starts. As soon as clock quality check indicates a valid Oscillator Clock the
S12XECRG switches to OSCCLK and leaves Self Clock Mode. Since the clock quality checker is running
in parallel to the reset generator, the S12XECRG may leave Self Clock Mode while still completing the
internal reset sequence.

******** Computer Operating Properly Watchdog (COP) Reset
When COP is enabled, the S12XECRG expects sequential write of $55 and $AA (in this order) to the
ARMCOP register during the selected time-out period. Once this is done, the COP time-out period restarts.
If the program fails to do this the S12XECRG will generate a reset.

******** Power On Reset, Low Voltage Reset
The on-chip voltage regulator detects when VDD to the MCU has reached a certain level and asserts power
on reset or low voltage reset or both. As soon as a power on reset or low voltage reset is triggered the

MC9S12XE-Family Reference Manual  Rev. 1.25

496 Freescale Semiconductor



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

S12XECRG performs a quality check on the incoming clock signal. As soon as clock quality check
indicates a valid Oscillator Clock signal the reset sequence starts using the Oscillator clock. If after 50
check windows the clock quality check indicated a non-valid Oscillator Clock the reset sequence starts
using Self-Clock Mode.

Figure 11-22 and Figure 11-23 show the power-up sequence for cases when the RESET pin is tied to VDD
and when the RESET pin is held low.

Clock Quality Check
RESET (no Self-Clock Mode)

) (

Internal POR
) (
128 SYSCLK

Internal RESET
) ( 64 SYSCLK

Figure 11-22. RESET Pin Tied to VDD (by a Pull-up Resistor)

Clock Quality Check
RESET (no Self Clock Mode)

) (

Internal POR
) (

128 SYSCLK

Internal RESET 64 SYSCLK
) (

Figure 11-23. RESET Pin Held Low Externally

11.6 Interrupts
The interrupts/reset vectors requested by the S12XECRG are listed in Table 11-18. Refer to MCU
specification for related vector addresses and priorities.

Table 11-18. S12XECRG Interrupt Vectors

Interrupt Source CCR
Mask Local Enable

Real time interrupt I bit CRGINT (RTIE)

LOCK interrupt I bit CRGINT (LOCKIE)

SCM interrupt I bit CRGINT (SCMIE)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 497



Chapter 11 S12XE Clocks and Reset Generator (S12XECRGV1)

11.6.1 Description of Interrupt Operation

******** Real Time Interrupt
The S12XECRG generates a real time interrupt when the selected interrupt time period elapses. RTI
interrupts are locally disabled by setting the RTIE bit to zero. The real time interrupt flag (RTIF) is set to1
when a timeout occurs, and is cleared to 0 by writing a 1 to the RTIF bit.

The RTI continues to run during Pseudo Stop Mode if the PRE bit is set to 1. This feature can be used for
periodic wakeup from Pseudo Stop if the RTI interrupt is enabled.

******** IPLL Lock Interrupt
The S12XECRG generates a IPLL Lock interrupt when the LOCK condition of the IPLL has changed,
either from a locked state to an unlocked state or vice versa. Lock interrupts are locally disabled by setting
the LOCKIE bit to zero. The IPLL Lock interrupt flag (LOCKIF) is set to1 when the LOCK condition has
changed, and is cleared to 0 by writing a 1 to the LOCKIF bit.

******** Self Clock Mode Interrupt
The S12XECRG generates a Self Clock Mode interrupt when the SCM condition of the system has
changed, either entered or exited Self Clock Mode. SCM conditions are caused by a failing clock quality
check after power on reset (POR) or low voltage reset (LVR) or recovery from Full Stop Mode (PSTP =
0) or Clock Monitor failure. For details on the clock quality check refer to Section ********, “Clock Quality
Checker”. If the clock monitor is enabled (CME = 1) a loss of external clock will also cause a SCM
condition (SCME = 1).

SCM interrupts are locally disabled by setting the SCMIE bit to zero. The SCM interrupt flag (SCMIF) is
set to1 when the SCM condition has changed, and is cleared to 0 by writing a 1 to the SCMIF bit.

MC9S12XE-Family Reference Manual  Rev. 1.25

498 Freescale Semiconductor