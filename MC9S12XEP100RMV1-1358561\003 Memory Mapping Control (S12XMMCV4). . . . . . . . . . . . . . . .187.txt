﻿Chapter 3
Memory Mapping Control (S12XMMCV4)

Table 3-1. Revision History

Revision Sections
Revision Date Description of Changes

Number Affected
V04.04 26 Oct 2005 - Reorganization of MEMCTL0 register bits.

V04.05 26 Jul 2006 *******/3-212 - Updated XGATE Memory Map

V04.06 15 Nov 2006 - Adding AUTOSAR Compliance concerning illegal CPU accesses

3.1 Introduction
This section describes the functionality of the module mapping control (MMC) sub-block of the S12X
platform. The block diagram of the MMC is shown in Figure 3-1.

The MMC module controls the multi-master priority accesses, the selection of internal resources and
external space. Internal buses, including internal memories and peripherals, are controlled in this module.
The local address space for each master is translated to a global memory space.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 187



Chapter 3 Memory Mapping Control (S12XMMCV4)

3.1.1 Terminology
Table 3-2. Acronyms and Abbreviations

Logic level “1” Voltage that corresponds to Boolean true state
Logic level “0” Voltage that corresponds to Boolean false state

0x Represents hexadecimal number
x Represents logic level ’don’t care’

byte 8-bit data
word 16-bit data

local address based on the 64 KBytes Memory Space (16-bit address)
global address based on the 8 MBytes Memory Space (23-bit address)

Aligned address Address on even boundary
Mis-aligned address Address on odd boundary

Bus Clock System Clock. Refer to CRG Block Guide.
Normal Expanded Mode
Emulation Single-Chip Mode

expanded modes
Emulation Expanded Mode
Special Test Mode
Normal Single-Chip Mode

single-chip modes
Special Single-Chip Mode
Emulation Single-Chip Mode

emulation modes
Emulation Expanded Mode
Normal Single-Chip Mode

normal modes
Normal Expanded Mode
Special Single-Chip Mode

special modes
Special Test Mode

NS Normal Single-Chip Mode
SS Special Single-Chip Mode
NX Normal Expanded Mode
ES Emulation Single-Chip Mode
EX Emulation Expanded Mode
ST Special Test Mode

Unimplemented areas Areas which are accessible by the pages (RPAGE,PPAGE,EPAGE) and not implemented
External Space Area which is accessible in the global address range 14_0000 to 3F_FFFF

Resources (Emulator, Application) connected to the MCU via the external bus on
external resource

expanded modes (Unimplemented areas and External Space)
PRR Port Replacement Registers
PRU Port Replacement Unit located on the emulator side
MCU MicroController Unit
NVM Non-volatile Memory; Flash EEPROM or ROM

3.1.2 Features
The main features of this block are:

• Paging capability to support a global 8 Mbytes memory address space
• Bus arbitration between the masters CPU, BDM and XGATE

MC9S12XE-Family Reference Manual  Rev. 1.25

188 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

• Simultaneous accesses to different resources1 (internal, external, and peripherals) (see Figure 3-1 )
• Resolution of target bus access collision
• MCU operation mode control
• MCU security control
• Separate memory map schemes for each master CPU, BDM and XGATE
• ROM control bits to enable the on-chip FLASH or ROM selection
• Port replacement registers access control
• Generation of system reset when CPU accesses an unimplemented address (i.e., an address which

does not belong to any of the on-chip modules) in single-chip modes

3.1.3 S12X Memory Mapping
The S12X architecture implements a number of memory mapping schemes including

• a CPU 8 MByte global map, defined using a global page (GPAGE) register and dedicated 23-bit
address load/store instructions.

• a BDM 8 MByte global map, defined using a global page (BDMGPR) register and dedicated 23-
bit address load/store instructions.

• a (CPU or BDM) 64 KByte local map, defined using specific resource page (RPAGE, EPAGE and
PPAGE) registers and the default instruction set. The 64 KBytes visible at any instant can be
considered as the local map accessed by the 16-bit (CPU or BDM) address.

• The XGATE 64 Kbyte local map.

The MMC module performs translation of the different memory mapping schemes to the specific global
(physical) memory implementation.

3.1.4 Modes of Operation
This subsection lists and briefly describes all operating modes supported by the MMC.

******* Power Saving Modes
• Run mode

MMC is functional during normal run mode.
• Wait mode

MMC is functional during wait mode.
• Stop mode

MMC is inactive during stop mode.

******* Functional Modes
• Single chip modes

In normal and special single chip mode the internal memory is used. External bus is not active.
1. Resources are also called targets.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 189



Chapter 3 Memory Mapping Control (S12XMMCV4)

• Expanded modes
Address, data, and control signals are activated in normal expanded and special test modes when
accessing the external bus. Access to internal resources will not cause activity on the external bus.

• Emulation modes
External bus is active to emulate, via an external tool, the normal expanded or the normal single
chip mode.}

3.1.5 Block Diagram
Figure 3-11   shows a block diagram of the MMC.

BDM CPU XGATE FLEXRAY

EEEPROM

MMC

FLASH Address Decoder & Priority DBG

Target Bus Controller

EBI RAM Peripherals

Figure 3-1. MMC Block Diagram

3.2 External Signal Description
The user is advised to refer to the device overview for port configuration and location of external bus
signals. Some pins may not be bonded out in all implementations.
Table 3-3 and Table 3-4 outline the pin names and functions. It also provides a brief description of their
operation.

1. Doted blocks and lines are optional. Please refer to the Device User Guide for their availlibilities.

MC9S12XE-Family Reference Manual  Rev. 1.25

190 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

Table 3-3. External Input Signals Associated with the MMC

Signal I/O Description Availability

MODC I Mode input Latched after
RESET (active low)

MODB I Mode input Latched after
RESET (active low)

MODA I Mode input Latched after
RESET (active low)

EROMCTL I EROM control input Latched after
RESET (active low)

ROMCTL I ROM control input Latched after
RESET (active low)

Table 3-4. External Output Signals Associated with the MMC

Available in Modes
Signal I/O Description

NS SS NX ES EX ST

CS0 O Chip select line 0  (see Table 3-5)

CS1 O Chip select line 1

CS2 O Chip select line 2

CS3 O Chip select line 3

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 191



Chapter 3 Memory Mapping Control (S12XMMCV4)

3.3 Memory Map and Registers

3.3.1 Module Memory Map
A summary of the registers associated with the MMC block is shown in Figure 3-2. Detailed descriptions
of the registers and bits are given in the subsections that follow.

Register
Address Bit 7 6 5 4 3 2 1 Bit 0

Name

0x000A MMCCTL0 R
CS3E1 CS3E0 CS2E1 CS2E0 CS1E1 CS1E0 CS0E1 CS0E0

W

0x000B MODE R 0 0 0 0 0
MODC MODB MODA

W

0x0010 GPAGE R 0
GP6 GP5 GP4 GP3 GP2 GP1 GP0

W

0x0011 DIRECT R
DP15 DP14 DP13 DP12 DP11 DP10 DP9 DP8

W

0x0012 Reserved R 0 0 0 0 0 0 0 0
W

0x0013 MMCCTL1 R 0
TGMRAMON EEEIFRON PGMIFRON RAMHM EROMON ROMHM ROMON

W

0x0014 Reserved R 0 0 0 0 0 0 0 0
W

0x0015 PPAGE R
PIX7 PIX6 PIX5 PIX4 PIX3 PIX2 PIX1 PIX0

W

0x0016 RPAGE R
RP7 RP6 RP5 RP4 RP3 RP2 RP1 RP0

W

0x0017 EPAGE R
EP7 EP6 EP5 EP4 EP3 EP2 EP1 EP0

W

= Unimplemented or Reserved

Figure 3-2. MMC Register Summary

MC9S12XE-Family Reference Manual  Rev. 1.25

192 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

3.3.2 Register Descriptions

******* MMC Control Register (MMCCTL0)

Address: 0x000A PRR

7 6 5 4 3 2 1 0
R

CS3E1 CS3E0 CS2E1 CS2E0 CS1E1 CS1E0 CS0E1 CS0E0
W

Reset 0 0 0 0 0 0 0 ROMON1

1. ROMON is bit[0] of the register MMCTL1 (see Figure 3-10)
= Unimplemented or Reserved

Figure 3-3. MMC Control Register (MMCCTL0)

Read: Anytime. In emulation modes read operations will return the data from the external bus. In all other
modes the data is read from this register.

Write: Anytime. In emulation modes write operations will also be directed to the external bus.
Table 3-5. Chip Selects Function Activity

Chip Modes
Register Bit

NS SS NX ES EX ST

CS0E[1:0], CS1E[1:0], Disabled(1) Disabled Enabled(2) Disabled Enabled Disabled
CS2E[1:0], CS3E[1:0]

1. Disabled: feature always inactive.
2. Enabled: activity is controlled by the appropriate register bit value.

The MMCCTL0 register is used to control external bus functions, like:
• Availability of chip selects. (See Table 3-5 and Table 3-6)
• Control of different external stretch mechanism. For more detail refer to the S12X_EBI

BlockGuide.

CAUTION
XGATE write access to this register during an CPU access which makes use
of this register could lead to unexpected results.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 193



Chapter 3 Memory Mapping Control (S12XMMCV4)

Table 3-6. MMCCTL0 Field Descriptions

Field Description

7–6 Chip Select 3 Enables — These bits enable the external chip select CS3 output which is asserted during
CS3E[1:0] accesses to specific external addresses. The associated global address range is shown in Table 3-7 and

Figure 3-17.
Chip select 3 is only active if enabled in Normal Expanded mode, Emulation Expanded mode.
The function disabled in all other operating modes.
00 Chip select 3 is disabled
01,10,11 Chip select 3 is enabled

5–4 Chip Select 2 Enables — These bits enable the external chip select CS2 output which is asserted during
CS2E[1:0] accesses to specific external addresses. The associated global address range is shown in Table 3-7 and

Figure 3-17.
Chip select 2 is only active if enabled in Normal Expanded mode, Emulation Expanded mode.
The function disabled in all other operating modes.
00 Chip select 2 is disabled
01,10,11 Chip select 2 is enabled

3–2 Chip Select 1 Enables — These bits enable the external chip select CS1 output which is asserted during
CS1E[1:0] accesses to specific external addresses. The associated global address range is shown in Table 3-7 and

Figure 3-17.
Chip select 1 is only active if enabled in Normal Expanded mode, Emulation Expanded mode.
The function disabled in all other operating modes.
00 Chip select 1 is disabled
01,10,11 Chip select 1 is enabled

1–0 Chip Select 0 Enables — These bits enable the external chip select CS0 output which is asserted during
CS0E[1:0] accesses to specific external addresses. The associated global address range is shown in Table 3-7 and

Figure 3-17.
Chip select 0 is only active if enabled in Normal Expanded mode, Emulation Expanded mode.
The function disabled in all other operating modes.
00 Chip select 0 is disabled
01,10,11 Chip select 0 is enabled

Table 3-7 shows the address boundaries of each chip select and the relationship with the implemented
resources (internal) parameters.

Table 3-7. Global Chip Selects Memory Space

Chip Selects Bottom Address Top Address

CS3 0x00_0800 0x0F_FFFF minus RAMSIZE(1)

CS2(2) 0x14_0000 0x1F_FFFF

CS1 0x20_0000 0x3F_FFFF

CS0(3) 0x40_0000 0x7F_FFFF minus FLASHSIZE(4)

1. External RPAGE accesses in (NX, EX)
2. When ROMHM is set (see ROMHM in Table 3-16) the CS2 is asserted in the space occupied by this on-

chip memory block.
3. When the internal NVM is enabled (see ROMON in Section *******, “MMC Control Register (MMCCTL1))

the CS0 is not asserted in the space occupied by this on-chip memory block.
4. External PPAGE accesses in (NX, EX)

MC9S12XE-Family Reference Manual  Rev. 1.25

194 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

******* Mode Register (MODE)

Address: 0x000B PRR

7 6 5 4 3 2 1 0
R 0 0 0 0 0

MODC MODB MODA
W

Reset MODC1 MODB1 MODA1 0 0 0 0 0
1. External signal (see Table 3-3).

= Unimplemented or Reserved

Figure 3-4. Mode Register (MODE)

Read: Anytime. In emulation modes read operations will return the data read from the external bus. In all
other modes the data are read from this register.

Write: Only if a transition is allowed (see Figure 3-5). In emulation modes write operations will be also
directed to the external bus.

The MODE bits of the MODE register are used to establish the MCU operating mode.

CAUTION
XGATE write access to this register during an CPU access which makes use
of this register could lead to unexpected results.

Table 3-8. MODE Field Descriptions

Field Description

7–5 Mode Select Bits — These bits control the current operating mode during RESET high (inactive). The external
MODC, mode pins MODC, MODB, and MODA determine the operating mode during RESET low (active). The state of
MODB, the pins is latched into the respective register bits after the RESET signal goes inactive (see Figure 3-4).
MODA Write restrictions exist to disallow transitions between certain modes. Figure 3-5 illustrates all allowed mode

changes. Attempting non authorized transitions will not change the MODE bits, but it will block further writes to
these register bits except in special modes.
Both transitions from normal single-chip mode to normal expanded mode and from emulation single-chip to
emulation expanded mode are only executed by writing a value of 3’b101 (write once). Writing any other value
will not change the MODE bits, but will block further writes to these register bits.
Changes of operating modes are not allowed when the device is secured, but it will block further writes to these
register bits except in special modes.
In emulation modes reading this address returns data from the external bus which has to be driven by the
emulator. It is therefore responsibility of the emulator hardware to provide the expected value (i.e. a value
corresponding to normal single chip mode while the device is in emulation single-chip mode or a value
corresponding to normal expanded mode while the device is in emulation expanded mode).

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 195



Chapter 3 Memory Mapping Control (S12XMMCV4)

RESET

010

Special
Test
(ST)
010

10
10

0 1

Normal Normal
100 Single-Chip 101 Expanded 101

RESET RESET
110 (NS) (NX)
111 100 101

Emulation Emulation
001 101 011

RESET Single-Chip Expanded RESET
(ES) (EX)
001 011

001 01
1

Special
Single-Chip

(SS)
000

000

RESET

Transition done by external pins (MODC, MODB, MODA)

RESET

Transition done by write access to the MODE register

110 Illegal (MODC, MODB, MODA) pin values.
111

Do not use. (Reserved for future use).

Figure 3-5. Mode Transition Diagram when MCU is Unsecured

MC9S12XE-Family Reference Manual  Rev. 1.25

196 Freescale Semiconductor

001

010

101

011

000

100



Chapter 3 Memory Mapping Control (S12XMMCV4)

******* Global Page Index Register (GPAGE)

Address: 0x0010

7 6 5 4 3 2 1 0
R 0

GP6 GP5 GP4 GP3 GP2 GP1 GP0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 3-6. Global Page Index Register (GPAGE)

Read: Anytime

Write: Anytime

The global page index register is used to construct a 23 bit address in the global map format. It is only used
when the CPU is executing a global instruction (GLDAA, GLDAB, GLDD, GLDS, GLDX,
GLDY,GSTAA, GSTAB, GSTD, GSTS, GSTX, GSTY) (see CPU Block Guide). The generated global
address is the result of concatenation of the CPU local address [15:0] with the GPAGE register [22:16] (see
Figure 3-7).

CAUTION
XGATE write access to this register during an CPU access which makes use
of this register could lead to unexpected results.

Global Address [22:0]

Bit22 Bit16 Bit15 Bit 0

GPAGE Register [6:0] CPU Address [15:0]

Figure 3-7. GPAGE Address Mapping

Table 3-9. GPAGE Field Descriptions

Field Description

6–0 Global Page Index Bits 6–0 — These page index bits are used to select which of the 128 64-kilobyte pages is
GP[6:0] to be accessed.

Example 3-1. This example demonstrates usage of the GPAGE register

LDX #0x5000 ;Set GPAGE offset to the value of 0x5000
MOVB #0x14, GPAGE ;Initialize GPAGE register with the value of 0x14
GLDAA X ;Load Accu A from the global address 0x14_5000

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 197



Chapter 3 Memory Mapping Control (S12XMMCV4)

******* Direct Page Register (DIRECT)

Address: 0x0011

7 6 5 4 3 2 1 0
R

DP15 DP14 DP13 DP12 DP11 DP10 DP9 DP8
W

Reset 0 0 0 0 0 0 0 0

Figure 3-8. Direct Register (DIRECT)

Read: Anytime

Write: anytime in special modes, one time only in other modes.

This register determines the position of the 256 Byte direct page within the memory map.It is valid for both
global and local mapping scheme.

Table 3-10. DIRECT Field Descriptions

Field Description

7–0 Direct Page Index Bits 15–8 — These bits are used by the CPU when performing accesses using the direct
DP[15:8] addressing mode. The bits from this register form bits [15:8] of the address (see Figure 3-9).

CAUTION
XGATE write access to this register during an CPU access which makes use
of this register could lead to unexpected results.

Global Address [22:0]

Bit22 Bit16 Bit15 Bit8 Bit7 Bit0

DP [15:8]

CPU Address [15:0]
Figure 3-9. DIRECT Address Mapping

Bits [22:16] of the global address will be formed by the GPAGE[6:0] bits in case the CPU executes a global
instruction in direct addressing mode or by the appropriate local address to the global address expansion
(refer to Section *******.1, “Expansion of the Local Address Map).

Example 3-2. This example demonstrates usage of the Direct Addressing Mode

MOVB #0x80,DIRECT ;Set DIRECT register to 0x80. Write once only.
;Global data accesses to the range 0xXX_80XX can be direct.
;Logical data accesses to the range 0x80XX are direct.

LDY <00 ;Load the Y index register from 0x8000 (direct access).
;< operator forces direct access on some assemblers but in

MC9S12XE-Family Reference Manual  Rev. 1.25

198 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

;many cases assemblers are “direct page aware” and can
;automatically select direct mode.

******* MMC Control Register (MMCCTL1)

Address: 0x0013 PRR

7 6 5 4 3 2 1 0
R 0

TGMRAMON EEEIFRON PGMIFRON RAMHM EROMON ROMHM ROMON
W

Reset 0 0 0 0 0 EROMCTL 0 ROMCTL
= Unimplemented or Reserved

Figure 3-10. MMC Control Register (MMCCTL1)

Read: Anytime. In emulation modes read operations will return the data from the external bus. In all other
modes the data are read from this register.

Write: Refer to each bit description. In emulation modes write operations will also be directed to the
external bus.

CAUTION
XGATE write access to this register during an CPU access which makes use
of this register could lead to unexpected results.

Table 3-11. MMCCTL1 Field Descriptions

Field Description

7 EEE Tag RAM and FTM SCRATCH RAM visible in the memory map
TGMRAMON Write: Anytime

This bit is used to made the EEE Tag RAM nd FTM SCRATCH RAM visible in the global memory map.
0 Not visible in the memory map.
1 Visible in the memory map.

5 EEE IFR visible in the memory map
EEEIFRON Write: Anytime

This bit is used to made the IFR sector of EEE DATA FLASH visible in the global memory map.
0 Not visible in the memory map.
1 Visible in the memory map.

4 Program IFR visible in the memory map
PGMIFRON Write: Anytime

This bit is used to made the IFR sector of the Program Flash visible in the global memory map.
0 Not visible in the memory map.
1 Visible in the memory map.

3 RAM only in higher Half of the memory map
RAMHM Write: Once in normal and emulation modes and anytime in special modes

0 Accesses to $4000–$7FFF will be mapped to $14_4000-$14_7FFF in the global memory space (external
access).

1 Accesses to $4000–$7FFF will be mapped to $0F_C000-$0F_FFFF in the global memory space (RAM area).

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 199



Chapter 3 Memory Mapping Control (S12XMMCV4)

Table 3-11. MMCCTL1 Field Descriptions (continued)

Field Description

2 Enables emulated Flash or ROM memory in the memory map
EROMON Write: Never

This bit is used in some modes to define the placement of the Emulated Flash or ROM (Refer to Table 3-12)
0 Disables the emulated Flash or ROM in the memory map.
1 Enables the emulated Flash or ROM in the memory map.

1 FLASH or ROM only in higher Half of Memory Map
ROMHM Write: Once in normal and emulation modes and anytime in special modes

0 The fixed page of Flash or ROM can be accessed in the lower half of the memory map. Accesses to
0x4000–0x7FFF will be mapped to 0x7F_4000-0x7F_7FFF in the global memory space.

1 Disables access to the Flash or ROM in the lower half of the memory map.These physical locations of the
Flash or ROM can still be accessed through the program page window. Accesses to 0x4000–0x7FFF will be
mapped to 0x14_4000-0x14_7FFF in the global memory space (external access).

0 Enable FLASH or ROM in the memory map
ROMON Write: Once in normal and emulation modes and anytime in special modes.

This bit is used in some modes to define the placement of the ROM (Refer to Table 3-12)
0 Disables the Flash or ROM from the memory map.
1 Enables the Flash or ROM in the memory map.

EROMON and ROMON control the visibility of the Flash in the memory map for CPU or BDM (not for
XGATE). Both local and global memory maps are affected.

Table 3-12. Data Sources when CPU or BDM is Accessing Flash Area

Chip Modes ROMON EROMON DATA SOURCE(1) Stretch(2)

Normal Single Chip X X Internal Flash N

Special Single Chip

Emulation Single Chip X 0 Emulation Memory N

X 1 Internal Flash

Normal Expanded 0 X External Application Y

1 X Internal Flash N

Emulation Expanded 0 X External Application Y

1 0 Emulation Memory N

1 1 Internal Flash

Special Test 0 X External Application N

1 X Internal Flash
1. Internal Flash means Flash resources inside the MCU are read/written.

Emulation memory means resources inside the emulator are read/written (PRU registers, flash
replacement, RAM, EEPROM and register space are always considered internal).
External application means resources residing outside the MCU are read/written.

2. The external access stretch mechanism is part of the EBI module (refer to EBI Block Guide for details).

MC9S12XE-Family Reference Manual  Rev. 1.25

200 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

******* Program Page Index Register (PPAGE)

Address: 0x0015

7 6 5 4 3 2 1 0
R

PIX7 PIX6 PIX5 PIX4 PIX3 PIX2 PIX1 PIX0
W

Reset 1 1 1 1 1 1 1 0

Figure 3-11. Program Page Index Register (PPAGE)

Read: Anytime

Write: Anytime

These eight index bits are used to page 16 KByte blocks into the Flash page window located in the local
(CPU or BDM) memory map from address 0x8000 to address 0xBFFF (see Figure 3-12). This supports
accessing up to 4 Mbytes of Flash (in the Global map) within the 64 KByte Local map. The PPAGE register
is effectively used to construct paged Flash addresses in the Local map format. The CPU has special access
to read and write this register directly during execution of CALL and RTC instructions..

CAUTION
XGATE write access to this register during an CPU access which makes use
of this register could lead to unexpected results.

Global Address [22:0]

1 Bit21 Bit14 Bit13 Bit0

PPAGE Register [7:0] Address [13:0]

Address: CPU Local Address
or BDM Local Address

Figure 3-12. PPAGE Address Mapping

NOTE
Writes to this register using the special access of the CALL and RTC
instructions will be complete before the end of the instruction execution.

Table 3-13. PPAGE Field Descriptions

Field Description

7–0 Program Page Index Bits 7–0 — These page index bits are used to select which of the 256 FLASH or ROM
PIX[7:0] array pages is to be accessed in the Program Page Window.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 201



Chapter 3 Memory Mapping Control (S12XMMCV4)

The fixed 16K page from 0x4000–0x7FFF (when ROMHM = 0) is the page number 0xFD.

The reset value of 0xFE ensures that there is linear Flash space available between addresses 0x4000 and
0xFFFF out of reset.

The fixed 16K page from 0xC000-0xFFFF is the page number 0xFF.

******* RAM Page Index Register (RPAGE)

Address: 0x0016

7 6 5 4 3 2 1 0
R

RP7 RP6 RP5 RP4 RP3 RP2 RP1 RP0
W

Reset 1 1 1 1 1 1 0 1

Figure 3-13. RAM Page Index Register (RPAGE)

Read: Anytime

Write: Anytime

These eight index bits are used to page 4 KByte blocks into the RAM page window located in the local
(CPU or BDM) memory map from address 0x1000 to address 0x1FFF (see Figure 3-14). This supports
accessing up to 1022 KByte of RAM (in the Global map) within the 64 KByte Local map. The RAM page
index register is effectively used to construct paged RAM addresses in the Local map format.

CAUTION
XGATE write access to this register during an CPU access which makes use
of this register could lead to unexpected results.

Global Address [22:0]

0 0 0 Bit19 Bit18 Bit12 Bit11 Bit0

RPAGE Register [7:0] Address [11:0]

Address: CPU Local Address
or BDM Local Address

Figure 3-14. RPAGE Address Mapping

NOTE
Because RAM page 0 has the same global address as the register space, it is
possible to write to registers through the RAM space when RPAGE = 0x00.

MC9S12XE-Family Reference Manual  Rev. 1.25

202 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

Table 3-14. RPAGE Field Descriptions

Field Description

7–0 RAM Page Index Bits 7–0 — These page index bits are used to select which of the 256 RAM array pages is to
RP[7:0] be accessed in the RAM Page Window.

The reset value of 0xFD ensures that there is a linear RAM space available between addresses 0x1000 and
0x3FFF out of reset.

The fixed 4K page from 0x2000–0x2FFF of RAM is equivalent to page 254 (page number 0xFE).

The fixed 4K page from 0x3000–0x3FFF of RAM is equivalent to page 255 (page number 0xFF).

******* EEPROM Page Index Register (EPAGE)

Address: 0x0017

7 6 5 4 3 2 1 0
R

EP7 EP6 EP5 EP4 EP3 EP2 EP1 EP0
W

Reset 1 1 1 1 1 1 1 0

Figure 3-15. EEPROM Page Index Register (EPAGE)

Read: Anytime

Write: Anytime

These eight index bits are used to page 1 KByte blocks into the EEPROM page window located in the local
(CPU or BDM) memory map from address 0x0800 to address 0x0BFF (see Figure 3-16). This supports
accessing up to 256 KByte of EEPROM (in the Global map) within the 64 KByte Local map. The
EEPROM page index register is effectively used to construct paged EEPROM addresses in the Local map
format.

CAUTION
XGATE write access to this register during an CPU access which makes use
of this register could lead to unexpected results.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 203



Chapter 3 Memory Mapping Control (S12XMMCV4)

Global Address [22:0]

0 0 1 0 0 Bit17 Bit16 Bit10 Bit9 Bit0

EPAGE Register [7:0] Address [9:0]

Address: CPU Local Address
or BDM Local Address

Figure 3-16. EPAGE Address Mapping

Table 3-15. EPAGE Field Descriptions

Field Description

7–0 EEPROM Page Index Bits 7–0 — These page index bits are used to select which of the 256 EEPROM array
EP[7:0] pages is to be accessed in the EEPROM Page Window.

The reset value of 0xFE ensures that there is a linear EEPROM space available between addresses 0x0800
and 0x0FFF out of reset.

The fixed 1K page 0x0C00–0x0FFF of EEPROM is equivalent to page 255 (page number 0xFF).

3.4 Functional Description
The MMC block performs several basic functions of the S12X sub-system operation: MCU operation
modes, priority control, address mapping, select signal generation and access limitations for the system.
Each aspect is described in the following subsections.

3.4.1 MCU Operating Mode
• Normal single-chip mode

There is no external bus in this mode. The MCU program is executed from the internal memory
and no external accesses are allowed.

• Special single-chip mode
This mode is generally used for debugging single-chip operation, boot-strapping or security related
operations. The active background debug mode is in control of the CPU code execution and the
BDM firmware is waiting for serial commands sent through the BKGD pin. There is no external
bus in this mode.

MC9S12XE-Family Reference Manual  Rev. 1.25

204 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

• Emulation single-chip mode
Tool vendors use this mode for emulation systems in which the user’s target application is normal
single-chip mode. Code is executed from external or internal memory depending on the set-up of
the EROMON bit (see Section *******, “MMC Control Register (MMCCTL1)). The external bus
is active in both cases to allow observation of internal operations (internal visibility).

• Normal expanded mode
The external bus interface is configured as an up to 23-bit address bus, 8 or 16-bit data bus with
dedicated bus control and status signals. This mode allows 8 or 16-bit external memory and
peripheral devices to be interfaced to the system. The fastest external bus rate is half of the internal
bus rate. An external signal can be used in this mode to cause the external bus to wait as desired by
the external logic.

• Emulation expanded mode
Tool vendors use this mode for emulation systems in which the user’s target application is normal
expanded mode.

• Special test mode
This mode is an expanded mode for factory test.

3.4.2 Memory Map Scheme

******* CPU and BDM Memory Map Scheme
The BDM firmware lookup tables and BDM register memory locations share addresses with other
modules; however they are not visible in the memory map during user’s code execution. The BDM
memory resources are enabled only during the READ_BD and WRITE_BD access cycles to distinguish
between accesses to the BDM memory area and accesses to the other modules. (Refer to BDM Block
Guide for further details).

When the MCU enters active BDM mode, the BDM firmware lookup tables and the BDM registers
become visible in the local memory map in the range 0xFF00-0xFFFF (global address 0x7F_FF00 -
0x7F_FFFF) and the CPU begins execution of firmware commands or the BDM begins execution of
hardware commands. The resources which share memory space with the BDM module will not be visible
in the memory map during active BDM mode.

Please note that after the MCU enters active BDM mode the BDM firmware lookup tables and the BDM
registers will also be visible between addresses 0xBF00 and 0xBFFF if the PPAGE register contains value
of 0xFF.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 205



Chapter 3 Memory Mapping Control (S12XMMCV4)

CPU and BDM Global Memory Map
Local Memory Map

0x00_0000
2K REGISTERS

0x00_0800 2K RAM
0x00_1000

RAM
253*4K paged

0x0F_E000
0x0000

2K REGISTERS 8K RAM
0x0800

1K EEPROM window EPAGE
0x10_0000

0x0C00
1K EEPROM

0x1000
4K RAM window RPAGE EEPROM

0x2000 255*1K paged

8K RAM

0x4000 0x13_FC00
1K EEPROM

0x14_0000
Unpaged

16K FLASH

External
0x8000 Space

16K FLASH window PPAGE
0x40_0000

0xC000

FLASH
Unpaged 253 *16K paged

16K FLASH

0xFFFF Reset Vectors

0x7F_4000
16K FLASH

(PPAGE 0xFD)

0x7F_8000
16K FLASH

(PPAGE 0xFE)

0x7F_C000
16K FLASH

(PPAGE 0xFF)
0x7F_FFFF

Figure 3-17. Expansion of the Local Address Map

MC9S12XE-Family Reference Manual  Rev. 1.25

206 Freescale Semiconductor

4 Mbytes 2.75 Mbytes 256 Kilobytes 1M minus 2 Kilobytes



Chapter 3 Memory Mapping Control (S12XMMCV4)

*******.1 Expansion of the Local Address Map

Expansion of the CPU Local Address Map
The program page index register in MMC allows accessing up to 4 Mbyte of FLASH or ROM in the global
memory map by using the eight page index bits to page 256 16 Kbyte blocks into the program page
window located from address 0x8000 to address 0xBFFF in the local CPU memory map.

The page value for the program page window is stored in the PPAGE register. The value of the PPAGE
register can be read or written by normal memory accesses as well as by the CALL and RTC instructions
(see Section 3.5.1, “CALL and RTC Instructions).

Control registers, vector space and parts of the on-chip memories are located in unpaged portions of the
64-kilobyte local CPU address space.

The starting address of an interrupt service routine must be located in unpaged memory unless the user is
certain that the PPAGE register will be set to the appropriate value when the service routine is called.
However an interrupt service routine can call other routines that are in paged memory. The upper 16-
kilobyte block of the local CPU memory space (0xC000–0xFFFF) is unpaged. It is recommended that all
reset and interrupt vectors point to locations in this area or to the other unpaged sections of the local CPU
memory map.

Table 3-16  summarizes mapping of the address bus in Flash/External space based on the address, the
PPAGE register value and value of the ROMHM bit in the MMCCTL1 register.

Table 3-16. Global FLASH/ROM Allocated

Local External
ROMHM Global Address

CPU Address Access

0x4000–0x7FFF 0 No 0x7F_4000 –0x7F_7FFF

1 Yes 0x14_4000–0x14_7FFF

0x8000–0xBFFF N/A No(1) 0x40_0000–0x7F_FFFF

N/A Yes1

0xC000–0xFFFF N/A No 0x7F_C000–0x7F_FFFF
1. The internal or the external bus is accessed based on the size of the memory resources

implemented on-chip. Please refer to Figure 1-23 for further details.

The RAM page index register allows accessing up to 1 Mbyte –2 Kbytes of RAM in the global memory
map by using the eight RPAGE index bits to page 4 Kbyte blocks into the RAM page window located in
the local CPU memory space from address 0x1000 to address 0x1FFF. The EEPROM page index register
EPAGE allows accessing up to 256 Kbytes of EEPROM in the system by using the eight EPAGE index
bits to page 1 Kbyte blocks into the EEPROM page window located in the local CPU memory space from
address 0x0800 to address 0x0BFF.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 207



Chapter 3 Memory Mapping Control (S12XMMCV4)

Expansion of the BDM Local Address Map
PPAGE, RPAGE, and EPAGE registers are also used for the expansion of the BDM local address to the
global address. These registers can be read and written by the BDM.

The BDM expansion scheme is the same as the CPU expansion scheme.

******* Global Addresses Based on the Global Page

CPU Global Addresses Based on the Global Page
The seven global page index bits allow access to the full 8 Mbyte address map that can be accessed with
23 address bits. This provides an alternative way to access all of the various pages of FLASH, RAM and
EEE as well as additional external memory.

The GPAGE Register is used only when the CPU is executing a global instruction (see Section *******,
“Global Page Index Register (GPAGE)). The generated global address is the result of concatenation of the
CPU local address [15:0] with the GPAGE register [22:16] (see Figure 3-7).

BDM Global Addresses Based on the Global Page
The seven BDMGPR Global Page index bits allow access to the full 8 Mbyte address map that can be
accessed with 23 address bits. This provides an alternative way to access all of the various pages of
FLASH, RAM and EEE as well as additional external memory.

The BDM global page index register (BDMGPR) is used only in the case the CPU is executing a firmware
command which uses a global instruction (like GLDD, GSTD) or by a BDM hardware command (like
WRITE_W, WRITE_BYTE, READ_W, READ_BYTE). See the BDM Block Guide for further details.

The generated global address is a result of concatenation of the BDM local address with the BDMGPR
register [22:16] in the case of a hardware command or concatenation of the CPU local address and the
BDMGPR register [22:16] in the case of a firmware command (see Figure 3-18).

MC9S12XE-Family Reference Manual  Rev. 1.25

208 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

BDM HARDWARE COMMAND

Global Address [22:0]

Bit22 Bit16 Bit15 Bit0

BDMGPR Register [6:0] BDM Local Address

BDM FIRMWARE COMMAND

Global Address [22:0]

Bit22 Bit16 Bit15 Bit0

BDMGPR Register [6:0] CPU Local Address
Figure 3-18. BDMGPR Address Mapping

******* Implemented Memory Map
The global memory spaces reserved for the internal resources (RAM, EEE, and FLASH) are not
determined by the MMC module. Size of the individual internal resources are however fixed in the design
of the device cannot be changed by the user. Please refer to the Device User Guide for further details.
Figure 3-19 and Table 3-17 show the memory spaces occupied by the on-chip resources. Please note that
the memory spaces have fixed top addresses.

Table 3-17. Global Implemented Memory Space

Internal Resource $Address

RAM RAM_LOW = 0x10_0000 minus RAMSIZE(1)

FLASH FLASH_LOW = 0x80_0000 minus FLASHSIZE(2)

1. RAMSIZE is the hexadecimal value of RAM SIZE in bytes
2. FLASHSIZE is the hexadecimal value of FLASH SIZE in bytes

When the device is operating in expanded modes except emulation single-chip mode, accesses to global
addresses which are not occupied by the on-chip resources (unimplemented areas or external memory
space) result in accesses to the external bus (see Figure 3-19).

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 209



Chapter 3 Memory Mapping Control (S12XMMCV4)

In emulation single-chip mode, accesses to global addresses which are not occupied by the on-chip
resources (unimplemented areas) result in accesses to the external bus. CPU accesses to global addresses
which are occupied by external memory space result in an illegal access reset (system reset) in case of no
MPU error.  BDM accesses to the external space are performed but the data will be undefined.

In single-chip modes accesses by the CPU (except for firmware commands) to any of the unimplemented
areas (see Figure 3-19) will result in an illegal access reset (system reset) in case of no MPU error. BDM
accesses to the unimplemented areas are allowed but the data will be undefined.

No misaligned word access from the BDM module will occur; these accesses are blocked in the BDM
module (Refer to BDM Block Guide).

Misaligned word access to the last location of RAM is performed but the data will be undefined.

Misaligned word access to the last location of any global page (64 Kbyte) by any global instruction, is
performed by accessing the last byte of the page and the first byte of the same page, considering the above
mentioned misaligned access cases.

The non-internal resources (unimplemented areas or external space) are used to generate the chip selects
(CS0,CS1,CS2 and CS3) (see Figure 3-19), which are only active in normal expanded, emulation
expanded (see Section *******, “MMC Control Register (MMCCTL0)).

MC9S12XE-Family Reference Manual  Rev. 1.25

210 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

CPU and BDM Global Memory Map
Local Memory Map

0x00_0000
2K REGISTERS

0x00_07FF

Unimplemented
RAM

RAM_LOW

0x0000 RAM
2K REGISTERS

0x0800
1K EEPROM window EPAGE 0x0F_FFFF

0x0C00
1K EEPROM

0x1000
4K RAM window RPAGE

0x2000 256 K EEEPROM
8K RAM

0x4000
0x13_FFFF

Unpaged
16K FLASH

0x1F_FFFF External
0x8000 Space

16K FLASH window PPAGE 0x3F_FFFF

0xC000
Unimplemented

FLASH
Unpaged

16K FLASH

0xFFFF Reset Vectors FLASH_LOW

FLASH

0x7F_FFFF

Figure 3-19. S12XE CPU & BDM Global Address Mapping

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 211

FLASHSIZE RAMSIZE

CS0 CS1 CS2 CS3



Chapter 3 Memory Mapping Control (S12XMMCV4)

******* XGATE Memory Map Scheme

*******.1 Expansion of the XGATE Local Address Map
The XGATE 64 Kbyte memory space allows access to internal resources only (Registers, RAM, and
FLASH). The 2 Kilobyte register address range is the same register address range as for the CPU and the
BDM module (see Table 3-18).

XGATE can access the FLASH in single chip modes, even when the MCU is secured. In expanded modes,
XGATE can not access the FLASH when MCU is secured.

The local address of the XGATE RAM access is translated to the global RAM address range. The XGATE
shares the RAM resource with the CPU and the BDM module (see Table 3-18).

XGATE RAM size (XGRAMSIZE) may be lower or equal to the MCU RAM size (RAMSIZE).In case of
XGATE RAM size less than 32 Kbytes (see Figure 3-20), the gap in the xgate local memory map will
result in an illegal RAM access (see Section *******, “Illegal XGATE Accesses)

The local address of the XGATE FLASH access is always translated to the global address 0x78_0800 -
0x78_7FFF.

Example 3-3. is a general example of the XGATE memory map implementation.

Table 3-18. XGATE Implemented Memory Space

Internal Resource $Address

XGATE RAM XGRAM_LOW = 0x0F_0000 plus (0x1_0000 minus XGRAMSIZE)(1)

1. XGRAMSIZE is the hexadecimal value of XGATE RAM SIZE in bytes.

Example 3-3.

The MCU FLASHSIZE is 64 Kbytes (0x10000) and MCU RAMSIZE is 32 Kbytes (0x8000).
The XGATE RAMSIZE is 16 Kbytes (0x4000).
The space occupied by the XGATE RAM in the global address space will be:

Bottom address: (0x10_0000 minus 0x4000) = 0x0F_C000
Top address: 0x0F_FFFF

XGATE accesses to local address range 0x0800–0x7FFF will result always in accesses to the
following FLASH block in the global address space:

Bottom address: 0x78_0800
Top address: 0x78_7FFF

The gap range in the local memory map 0x8000–0xBFFF will be translated in the global address
space:

0x0F_8000 - 0x0F_BFFF (illegal xgate access to system RAM).

MC9S12XE-Family Reference Manual  Rev. 1.25

212 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

XGATE Global Memory Map
Local Memory Map

0x00_0000
Registers

0x00_07FF

0x0000

Registers XGRAM_LOW
0x0800

RAM

0x0F_FFFF

FLASH

0x7FFF
Unimplemented

area

RAM

0x78_0800
FLASH

0x78_7FFF
0xFFFF

0x7F_FFFF
Figure 3-20. XGATE Global Address Mapping

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 213

XGRAMSIZE

XGRAMSIZE

FLASHSIZE
RAMSIZE



Chapter 3 Memory Mapping Control (S12XMMCV4)

******* Memory Configuration
Two bits in the MMCCTL1 register (ROMHM, RAMHM) configure the mapping of the local address
(0x4000-0x7FFF) in the global memory map.

ROMHM, RAMHM are write once in normal and emulation modes and anytime in special modes.

Three areas are identified (See Figure 3-21):
• Program FLASH (0x7F_4000-0x7F_7FFF) when ROMHM = 0.
• External Space (0x14_4000-0x14_7FFF) when ROMHM = 1 and RAMHM = 0.
• XSRAM Space (0x0F_C000-0x0F_FFFF) when ROMHM = 1 and RAMHM = 1.

Table 3-19 shows the translation from the local memory map to the global memory map taking in
consideration the different configurations of ROMHM and RAMHM.

Table 3-19. ROMHM and RAMHM Address Location

Local Address ROMHM RAMHM Global Address Location

0 X 0x7F_4000 - 0x7F_7FFF Internal Flash

0x4000 - 0x7FFF 1 0 0x14_4000 - 0x14_7FFF External Space

0x0F_C000 - 0x0F_FFFF Bottom of the Implemented RAM
1 1

0x2000 - 0x3FFF 0x0F_A000 - 0x0F_BFFF Fixed up to 8K RAM

0x2000 - 0x3FFF 1 0 0x0F_E000 - 0x0F_FFFF Fixed up to 8K RAM

Table 3-20 describes the application note of the RAM configuration and its dedicated global address.

Table 3-20. RAM Configuration

phase RPAGE ROMHM RAMHM RAM AREA Global Address

After reset RPAGE = 0xFD 0 0 12 Kilobytes 0x0F_D000 - 0x0F_FFFF
(Reset value)

During setup RPAGE = 0xFD 1 1 24 Kilobytes 0x0F_A000 - 0x0F_FFFF
(Reset value)

(0x00 <= RPAGE <= 0xF9) 1 1 28 Kilobytes 0x00_0000 - 0x0F_9FFF
Normal Operation

(0xFA <= RPAGE <= 0xFF) 1 1 24 Kilobytes 0x0F_A000 - 0x0F_FFFF

MC9S12XE-Family Reference Manual  Rev. 1.25

214 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

CPU and BDM Global Memory Map
Local Memory Map

0x00_0000
2K REGISTERS

0x00_0800 2K RAM
0x00_1000

RAM
251*4K paged

0x0F_A000

8K RAM
ROMHM RAMHM

0x0F_C000
0x0000

2K REGISTERS 1 1 16K RAM
0x0800

1K EEPROM window
0x10_0000

0x0C00
1K EEPROM

0x1000
4K RAM window EEPROM

0x2000 255*1K paged

8K RAM

0x4000 0x13_FC00
1K EEPROM

0x14_0000
ROMHM RAMHM 0x14_4000

1 0 16K External

0x8000 External
Space

16K FLASH window

0x40_0000

0xC000

FLASH
Unpaged 253 *16K paged

16K FLASH

0xFFFF Reset Vectors
ROMHM RAMHM

0x7F_4000
0 x 16K FLASH

0x7F_8000
16K FLASH

(PPAGE 0xFE)

0x7F_C000
16K FLASH

(PPAGE 0xFF)
0x7F_FFFF

Figure 3-21. ROMHM, RAMHM Memory Configuration

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 215

4 Mbytes 2.75 Mbytes 256 Kilobytes 1M minus 2 Kilobytes



Chapter 3 Memory Mapping Control (S12XMMCV4)

*******.1 System XSRAM
System XSRAM has two ways to be accessed by the CPU. One is by the programming of RPAGE and the
fixed XSRAM areas configured by the values of ROMHM, RAMHM, or by the usage of the global
instruction and the usage of GPAGE.

Figure 3-22 shows the memory map for the implemented XSRAM. The size of the implemented XSRAM
is done by the device definition and denoted by RAMSIZE.

RAM Area in the Memory Map
ROMHM = 1 RAMHM = 0

ROMHM = 0 RAMHM = X ROMHM = 1 RAMHM = 1
0x00_0000

REG. Area
0x00_07FF

0x00_0800 0x00_0800

Unimplemented Unimplemented
RAM Area RAM RAM

0x0F_FFFF

EEPROM Area 0x0F_A000

8K RAM

0x0F_C000
0x13_FFFF

0x0F_E000 16K RAM
External

Space Area 8K RAM
0x0F_FFFF 0x0F_FFFF

0x3F_FFFF

FLASH Area

0x7F_FFFF

Figure 3-22. S12XE System RAM in the Memory Map

MC9S12XE-Family Reference Manual  Rev. 1.25

216 Freescale Semiconductor

RAMSIZE



Chapter 3 Memory Mapping Control (S12XMMCV4)

3.4.3 Chip Access Restrictions
CPU and XGATE accesses are watched in the memory protection unit (See MPU Block Guide). In case of
access violation, the suspect master is acknowledged with an indication of an error; the victim target will
not be accessed.

Other violations MPU is not handling are listed below.

******* Illegal XGATE Accesses
A possible access error is flagged by the MMC and signalled to XGATE under the following conditions:

• XGATE performs misaligned word (in case of load-store or opcode or vector fetch accesses).
• XGATE accesses the register space (in case of opcode or vector fetch).
• XGATE performs a write to Flash in any modes (in case of load-store access).
• XGATE performs an access to a secured Flash in expanded modes (in case of load-store or opcode

or vector fetch accesses).

For further details refer to the XGATE Block Guide.

3.4.4 Chip Bus Control
The MMC controls the address buses and the data buses that interface the S12X masters (CPU, BDM and
XGATE) with the rest of the system (master buses). In addition the MMC handles all CPU read data bus
swapping operations. All internal and external resources are connected to specific target buses (see
Figure 3-231).

1. Doted blocks and lines are optional. Please refer to the Device User Guide for their availlibilities.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 217



Chapter 3 Memory Mapping Control (S12XMMCV4)

XGATE DBG CPU BDM FLEXRAY

XGATE S12X0 S12X1 S12X2

MMC “Crossbar Switch”

XBUS3 XBUS1 XBUS0 XRAM XBUS2

BDM
EBI FLFATSMH EEE XSRAM IPBI

resources

Figure 3-23. MMC Block Diagram

******* Master Bus Prioritization regarding access conflicts on Target Buses
The arbitration scheme allows only one master to be connected to a target at any given time. The following
rules apply when prioritizing accesses from different masters to the same target bus:

• CPU always has priority over BDM and XGATE.
• XGATE access to PRU registers constitutes a special case. It is always granted and stalls the CPU

for its duration.
• XGATE has priority over BDM.
• BDM has priority over CPU and XGATE when its access is stalled for more than 128 cycles. In the

later case the suspect master will be stalled after finishing the current operation and the BDM will
gain access to the bus.

• In emulation modes all internal accesses are visible on the external bus as well and the external bus
is used during access to the PRU registers.

3.5 Initialization/Application Information

3.5.1 CALL and RTC Instructions
CALL and RTC instructions are uninterruptable CPU instructions that automate page switching in the
program page window. The CALL instruction is similar to the JSR instruction, but the subroutine that is

MC9S12XE-Family Reference Manual  Rev. 1.25

218 Freescale Semiconductor

BLKX



Chapter 3 Memory Mapping Control (S12XMMCV4)

called can be located anywhere in the local address space or in any Flash or ROM page visible through the
program page window. The CALL instruction calculates and stacks a return address, stacks the current
PPAGE value and writes a new instruction-supplied value to the PPAGE register. The PPAGE value
controls which of the 256 possible pages is visible through the 16 Kbyte program page window in the
64 Kbyte local CPU memory map. Execution then begins at the address of the called subroutine.

During the execution of the CALL instruction, the CPU performs the following steps:
1. Writes the current PPAGE value into an internal temporary register and writes the new instruction-

supplied PPAGE value into the PPAGE register
2. Calculates the address of the next instruction after the CALL instruction (the return address) and

pushes this 16-bit value onto the stack
3. Pushes the temporarily stored PPAGE value onto the stack
4. Calculates the effective address of the subroutine, refills the queue and begins execution at the new

address

This sequence is uninterruptable. There is no need to inhibit interrupts during the CALL instruction
execution. A CALL instruction can be performed from any address to any other address in the local CPU
memory space.

The PPAGE value supplied by the instruction is part of the effective address of the CPU. For all addressing
mode variations (except indexed-indirect modes) the new page value is provided by an immediate operand
in the instruction. In indexed-indirect variations of the CALL instruction a pointer specifies memory
locations where the new page value and the address of the called subroutine are stored. Using indirect
addressing for both the new page value and the address within the page allows usage of values calculated
at run time rather than immediate values that must be known at the time of assembly.

The RTC instruction terminates subroutines invoked by a CALL instruction. The RTC instruction unstacks
the PPAGE value and the return address and refills the queue. Execution resumes with the next instruction
after the CALL instruction.

During the execution of an RTC instruction the CPU performs the following steps:
1. Pulls the previously stored PPAGE value from the stack
2. Pulls the 16-bit return address from the stack and loads it into the PC
3. Writes the PPAGE value into the PPAGE register
4. Refills the queue and resumes execution at the return address

This sequence is uninterruptable. The RTC can be executed from anywhere in the local CPU memory
space.

The CALL and RTC instructions behave like JSR and RTS instruction, they however require more
execution cycles. Usage of JSR/RTS instructions is therefore recommended when possible and
CALL/RTC instructions should only be used when needed. The JSR and RTS instructions can be used to
access subroutines that are already present in the local CPU memory map (i.e. in the same page in the
program memory page window for example). However calling a function located in a different page
requires usage of the CALL instruction. The function must be terminated by the RTC instruction. Because
the RTC instruction restores contents of the PPAGE register from the stack, functions terminated with the
RTC instruction must be called using the CALL instruction even when the correct page is already present

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 219



Chapter 3 Memory Mapping Control (S12XMMCV4)

in the memory map. This is to make sure that the correct PPAGE value will be present on stack at the time
of the RTC instruction execution.

3.5.2 Port Replacement Registers (PRRs)
Registers used for emulation purposes must be rebuilt by the in-circuit emulator hardware to achieve full
emulation of single chip mode operation. These registers are called port replacement registers (PRRs) (see
Table 1-25). PRRs are accessible from CPU, BDM and XGATE using different access types (word
aligned, word-misaligned and byte).

Each access to PRRs will be extended to 2 bus cycles for write or read accesses independent of the
operating mode. In emulation modes all write operations result in simultaneous writing to the internal
registers (peripheral access) and to the emulated registers (external access) located in the PRU in the
emulator. All read operations are performed from external registers (external access) in emulation modes.
In all other modes the read operations are performed from the internal registers (peripheral access).

Due to internal visibility of CPU accesses the CPU will be halted during XGATE or BDM access to any
PRR. This rule applies also in normal modes to ensure that operation of the device is the same as in
emulation modes.

A summary of PRR accesses:
• An aligned word access to a PRR will take 2 bus cycles.
• A misaligned word access to a PRRs will take 4 cycles. If one of the two bytes accessed by the

misaligned word access is not a PRR, the access will take only 3 cycles.
• A byte access to a PRR will take 2 cycles.

MC9S12XE-Family Reference Manual  Rev. 1.25

220 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

Table 3-21. PRR Listing

PRR Name PRR Local Address PRR Location

PORTA 0x0000 PIM

PORTB 0x0001 PIM

DDRA 0x0002 PIM

DDRB 0x0003 PIM

PORTC 0x0004 PIM

PORTD 0x0005 PIM

DDRC 0x0006 PIM

DDRD 0x0007 PIM

PORTE 0x0008 PIM

DDRE 0x0009 PIM

MMCCTL0 0x000A MMC

MODE 0x000B MMC

PUCR 0x000C PIM

RDRIV 0x000D PIM

EBICTL0 0x000E EBI

EBICTL1 0x000F EBI

Reserved 0x0012 MMC

MMCCTL1 0x0013 MMC

ECLKCTL 0x001C PIM

Reserved 0x001D PIM

PORTK 0x0032 PIM

DDRK 0x0033 PIM

3.5.3 On-Chip ROM Control
The MCU offers two modes to support emulation. In the first mode (called generator) the emulator
provides the data instead of the internal FLASH and traces the CPU actions. In the other mode (called
observer) the internal FLASH provides the data and all internal actions are made visible to the emulator.

******* ROM Control in Single-Chip Modes
In single-chip modes the MCU has no external bus. All memory accesses and program fetches are internal
(see Figure 3-24).

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 221



Chapter 3 Memory Mapping Control (S12XMMCV4)

No External Bus
MCU

Flash

Figure 3-24. ROM in Single Chip Modes

******* ROM Control in Emulation Single-Chip Mode
In emulation single-chip mode the external bus is connected to the emulator. If the EROMON bit is set,
the internal FLASH provides the data and the emulator can observe all internal CPU actions on the external
bus. If the EROMON bit is cleared, the emulator provides the data (generator) and traces the all CPU
actions (see Figure 3-25).

Observer

MCU Emulator

Flash

EROMON = 1

Generator

MCU Emulator

Flash

EROMON = 0

Figure 3-25. ROM in Emulation Single-Chip Mode

******* ROM Control in Normal Expanded Mode
In normal expanded mode the external bus will be connected to the application. If the ROMON bit is set,
the internal FLASH provides the data. If the ROMON bit is cleared, the application memory provides the
data (see Figure 3-26).

MC9S12XE-Family Reference Manual  Rev. 1.25

222 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

MCU Application

Flash Memory

ROMON = 1

MCU Application

Memory

ROMON = 0

Figure 3-26. ROM in Normal Expanded Mode

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 223



Chapter 3 Memory Mapping Control (S12XMMCV4)

******* ROM Control in Emulation Expanded Mode
In emulation expanded mode the external bus will be connected to the emulator and to the application. If
the ROMON bit is set, the internal FLASH provides the data. If the EROMON bit is set as well the
emulator observes all CPU internal actions, otherwise the emulator provides the data and traces all CPU
actions (see Figure 3-27). When the ROMON bit is cleared, the application memory provides the data and
the emulator will observe the CPU internal actions (see Figure 3-28).

Observer
MCU Emulator

Flash

Application

Memory

EROMON = 1

Generator
MCU Emulator

Flash

Application

Memory

EROMON = 0

Figure 3-27. ROMON = 1 in Emulation Expanded Mode

MC9S12XE-Family Reference Manual  Rev. 1.25

224 Freescale Semiconductor



Chapter 3 Memory Mapping Control (S12XMMCV4)

Observer
MCU Emulator

Application

Memory

Figure 3-28. ROMON = 0 in Emulation Expanded Mode

3.5.3.5 ROM Control in Special Test Mode
In special test mode the external bus is connected to the application. If the ROMON bit is set, the internal
FLASH provides the data, otherwise the application memory provides the data (see Figure 3-29).

MCU Application

Memory

ROMON = 0

MCU Application

Flash Memory

ROMON = 1
Figure 3-29. ROM in Special Test Mode

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 225



Chapter 3 Memory Mapping Control (S12XMMCV4)

MC9S12XE-Family Reference Manual  Rev. 1.25

226 Freescale Semiconductor