using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Protocols
{
    /// <summary>
    /// J1939 protocol handler for ECU communication
    /// Based on MC9S12XEP100 microcontroller specifications
    /// </summary>
    public class J1939ProtocolHandler : BaseECUProtocolHandler
    {
        #region Private Constants

        // J1939 protocol constants
        private const int J1939_DEFAULT_PRIORITY = 6;
        private const int J1939_DEFAULT_SOURCE_ADDRESS = 0xF9; // Default source address for diagnostic tool
        private const int J1939_BROADCAST_ADDRESS = 0xFF;
        private const int J1939_GLOBAL_ADDRESS = 0xFE;

        // J1939 PGN (Parameter Group Number) constants
        private const int J1939_PGN_REQUEST = 0xEA00;
        private const int J1939_PGN_ACKNOWLEDGEMENT = 0xE800;
        private const int J1939_PGN_DATA_TRANSFER = 0xEB00;
        private const int J1939_PGN_CONNECTION_MANAGEMENT = 0xEC00;
        private const int J1939_PGN_DIAGNOSTIC_MESSAGE = 0xFECA;

        // J1939 diagnostic commands
        private const byte J1939_DM_READ_ACTIVE_FAULTS = 0x01;
        private const byte J1939_DM_READ_INACTIVE_FAULTS = 0x02;
        private const byte J1939_DM_CLEAR_FAULTS = 0x03;
        private const byte J1939_DM_READ_PARAMETERS = 0x04;
        private const byte J1939_DM_WRITE_PARAMETERS = 0x05;
        private const byte J1939_DM_READ_EEPROM = 0x06;
        private const byte J1939_DM_WRITE_EEPROM = 0x07;
        private const byte J1939_DM_READ_MCU_CODE = 0x08;
        private const byte J1939_DM_WRITE_MCU_CODE = 0x09;

        // Timeout constants
        private const int J1939_DEFAULT_TIMEOUT_MS = 1000;
        private const int J1939_EXTENDED_TIMEOUT_MS = 5000;

        #endregion

        #region Constructor

        /// <summary>
        /// Gets the protocol type
        /// </summary>
        public override ECUProtocolType ProtocolType => ECUProtocolType.J1939;

        /// <summary>
        /// Initializes a new instance of the J1939ProtocolHandler class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="vocomService">The Vocom service</param>
        public J1939ProtocolHandler(ILoggingService logger, IVocomService vocomService)
            : base(logger, vocomService)
        {
        }

        #endregion

        #region IECUProtocolHandler Implementation

        /// <summary>
        /// Initializes the protocol handler
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public override async Task<bool> InitializeAsync()
        {
            try
            {
                _logger?.LogInformation("Initializing J1939 protocol handler", "J1939ProtocolHandler");

                // Call base initialization
                if (!await base.InitializeAsync())
                {
                    return false;
                }

                // Get the protocol configuration from the factory
                var protocolHandlerFactory = new ProtocolHandlerFactory(_logger);
                await protocolHandlerFactory.InitializeAsync(_vocomService);
                var canConfig = protocolHandlerFactory.GetProtocolConfiguration(ECUProtocolType.CAN);

                // Log the configuration
                _logger?.LogInformation($"Loaded CAN configuration with {canConfig.Count} parameters for J1939", "J1939ProtocolHandler");
                foreach (var kvp in canConfig)
                {
                    _logger?.LogInformation($"J1939 config: {kvp.Key} = {kvp.Value}", "J1939ProtocolHandler");
                }

                // J1939-specific initialization
                // In a real implementation, this would involve configuring the CAN controller for J1939 operation
                // For now, we'll just simulate this
                await Task.Delay(100); // Simulate initialization delay

                _logger?.LogInformation("J1939 protocol handler initialized successfully", "J1939ProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error initializing J1939 protocol handler", "J1939ProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Connects to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        public override async Task<bool> ConnectAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Connecting to ECU {ecu?.Name} via J1939", "J1939ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                // In a real implementation, this would involve establishing a J1939 connection with the ECU
                // For now, we'll just simulate this
                await Task.Delay(200); // Simulate connection delay

                _logger?.LogInformation($"Connected to ECU {ecu.Name} via J1939", "J1939ProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error connecting to ECU {ecu?.Name} via J1939", "J1939ProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Disconnects from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public override async Task<bool> DisconnectAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Disconnecting from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                // In a real implementation, this would involve closing the J1939 connection with the ECU
                // For now, we'll just simulate this
                await Task.Delay(100); // Simulate disconnection delay

                _logger?.LogInformation($"Disconnected from ECU {ecu.Name} via J1939", "J1939ProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error disconnecting from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Reads EEPROM data from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>EEPROM data as byte array, or null if read fails</returns>
        public override async Task<byte[]> ReadEEPROMAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading EEPROM from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return null;
                }

                // In a real implementation, this would involve sending J1939 messages to read the EEPROM
                // For now, we'll just simulate this
                await Task.Delay(500); // Simulate EEPROM read delay

                // Create simulated EEPROM data
                byte[] eepromData = new byte[ecu.EEPROMSize];
                Random random = new Random();
                random.NextBytes(eepromData);

                _logger?.LogInformation($"Read {eepromData.Length} bytes of EEPROM data from ECU {ecu.Name} via J1939", "J1939ProtocolHandler");
                return eepromData;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading EEPROM from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler", ex);
                return null;
            }
        }

        /// <summary>
        /// Writes EEPROM data to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="data">The data to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public override async Task<bool> WriteEEPROMAsync(ECUDevice ecu, byte[] data)
        {
            try
            {
                _logger?.LogInformation($"Writing EEPROM to ECU {ecu?.Name} via J1939", "J1939ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                if (data == null || data.Length == 0)
                {
                    _logger?.LogError("EEPROM data is null or empty", "J1939ProtocolHandler");
                    return false;
                }

                // In a real implementation, this would involve sending J1939 messages to write the EEPROM
                // For now, we'll just simulate this
                await Task.Delay(1000); // Simulate EEPROM write delay

                _logger?.LogInformation($"Wrote {data.Length} bytes of EEPROM data to ECU {ecu.Name} via J1939", "J1939ProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing EEPROM to ECU {ecu?.Name} via J1939", "J1939ProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Reads microcontroller code from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Microcontroller code as byte array, or null if read fails</returns>
        public override async Task<byte[]> ReadMicrocontrollerCodeAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading microcontroller code from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return null;
                }

                // In a real implementation, this would involve sending J1939 messages to read the microcontroller code
                // For now, we'll just simulate this
                await Task.Delay(1000); // Simulate microcontroller code read delay

                // Create simulated microcontroller code
                byte[] mcuCode = new byte[ecu.FlashSize];
                Random random = new Random();
                random.NextBytes(mcuCode);

                _logger?.LogInformation($"Read {mcuCode.Length} bytes of microcontroller code from ECU {ecu.Name} via J1939", "J1939ProtocolHandler");
                return mcuCode;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading microcontroller code from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler", ex);
                return null;
            }
        }

        /// <summary>
        /// Writes microcontroller code to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="code">The code to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public override async Task<bool> WriteMicrocontrollerCodeAsync(ECUDevice ecu, byte[] code)
        {
            try
            {
                _logger?.LogInformation($"Writing microcontroller code to ECU {ecu?.Name} via J1939", "J1939ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                if (code == null || code.Length == 0)
                {
                    _logger?.LogError("Microcontroller code is null or empty", "J1939ProtocolHandler");
                    return false;
                }

                // In a real implementation, this would involve sending J1939 messages to write the microcontroller code
                // For now, we'll just simulate this
                await Task.Delay(2000); // Simulate microcontroller code write delay

                _logger?.LogInformation($"Wrote {code.Length} bytes of microcontroller code to ECU {ecu.Name} via J1939", "J1939ProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing microcontroller code to ECU {ecu?.Name} via J1939", "J1939ProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Reads active faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>List of active faults, or null if read fails</returns>
        public override async Task<List<ECUFault>> ReadActiveFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading active faults from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return null;
                }

                // In a real implementation, this would involve sending J1939 messages to read the active faults
                // For now, we'll just simulate this
                await Task.Delay(300); // Simulate active faults read delay

                // Create simulated active faults
                List<ECUFault> activeFaults = new List<ECUFault>();
                Random random = new Random();
                int faultCount = random.Next(0, 5); // 0-4 faults

                for (int i = 0; i < faultCount; i++)
                {
                    activeFaults.Add(new ECUFault
                    {
                        Code = $"P{random.Next(1000, 9999)}",
                        Description = $"Simulated active fault {i + 1}",
                        Severity = (FaultSeverity)random.Next(0, 4), // Random severity
                        Timestamp = DateTime.Now.AddMinutes(-random.Next(1, 60)), // Random time in the last hour
                        IsActive = true
                    });
                }

                _logger?.LogInformation($"Read {activeFaults.Count} active faults from ECU {ecu.Name} via J1939", "J1939ProtocolHandler");
                return activeFaults;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading active faults from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler", ex);
                return null;
            }
        }

        /// <summary>
        /// Reads inactive faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>List of inactive faults, or null if read fails</returns>
        public override async Task<List<ECUFault>> ReadInactiveFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading inactive faults from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return null;
                }

                // In a real implementation, this would involve sending J1939 messages to read the inactive faults
                // For now, we'll just simulate this
                await Task.Delay(300); // Simulate inactive faults read delay

                // Create simulated inactive faults
                List<ECUFault> inactiveFaults = new List<ECUFault>();
                Random random = new Random();
                int faultCount = random.Next(0, 10); // 0-9 faults

                for (int i = 0; i < faultCount; i++)
                {
                    inactiveFaults.Add(new ECUFault
                    {
                        Code = $"P{random.Next(1000, 9999)}",
                        Description = $"Simulated inactive fault {i + 1}",
                        Severity = (FaultSeverity)random.Next(0, 4), // Random severity
                        Timestamp = DateTime.Now.AddDays(-random.Next(1, 30)), // Random time in the last month
                        IsActive = false
                    });
                }

                _logger?.LogInformation($"Read {inactiveFaults.Count} inactive faults from ECU {ecu.Name} via J1939", "J1939ProtocolHandler");
                return inactiveFaults;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading inactive faults from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler", ex);
                return null;
            }
        }

        /// <summary>
        /// Clears faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <returns>True if clearing is successful, false otherwise</returns>
        public override async Task<bool> ClearFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Clearing faults from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                // In a real implementation, this would involve sending J1939 messages to clear the faults
                // For now, we'll just simulate this
                await Task.Delay(500); // Simulate fault clearing delay

                _logger?.LogInformation($"Cleared all faults from ECU {ecu.Name} via J1939", "J1939ProtocolHandler");

                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error clearing faults from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Clears all faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <returns>True if clearing is successful, false otherwise</returns>
        public override async Task<bool> ClearAllFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Clearing all faults from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                // In a real implementation, this would involve sending J1939 messages to clear all faults
                // For now, we'll just simulate this
                await Task.Delay(500); // Simulate fault clearing delay

                _logger?.LogInformation($"Cleared all faults from ECU {ecu.Name} via J1939", "J1939ProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error clearing all faults from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Clears specific faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <param name="faultCodes">The specific fault codes to clear</param>
        /// <returns>True if clearing is successful, false otherwise</returns>
        public override async Task<bool> ClearSpecificFaultsAsync(ECUDevice ecu, List<string> faultCodes)
        {
            try
            {
                _logger?.LogInformation($"Clearing specific faults from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                if (faultCodes == null || faultCodes.Count == 0)
                {
                    // If no specific fault codes are provided, clear all faults
                    return await ClearFaultsAsync(ecu);
                }

                // In a real implementation, this would involve sending J1939 messages to clear the specific faults
                // For now, we'll just simulate this
                await Task.Delay(500); // Simulate fault clearing delay

                _logger?.LogInformation($"Cleared {faultCodes.Count} specific faults from ECU {ecu.Name} via J1939", "J1939ProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error clearing specific faults from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Reads parameters from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Dictionary of parameter names and values, or null if read fails</returns>
        public override async Task<Dictionary<string, object>> ReadParametersAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Reading parameters from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return null;
                }

                // In a real implementation, this would involve sending J1939 messages to read the parameters
                // For now, we'll just simulate this
                await Task.Delay(400); // Simulate parameters read delay

                // Create simulated parameters
                Dictionary<string, object> parameters = new Dictionary<string, object>();
                Random random = new Random();

                // Add some standard J1939 parameters
                parameters.Add("EngineSpeed", random.Next(600, 3000)); // RPM
                parameters.Add("EngineLoad", random.Next(0, 100)); // %
                parameters.Add("EngineCoolantTemp", random.Next(70, 95)); // °C
                parameters.Add("EngineOilTemp", random.Next(80, 110)); // °C
                parameters.Add("EngineOilPressure", random.Next(200, 400) / 10.0); // bar
                parameters.Add("FuelLevel", random.Next(0, 100)); // %
                parameters.Add("BatteryVoltage", random.Next(120, 148) / 10.0); // V
                parameters.Add("AmbientAirTemp", random.Next(-10, 40)); // °C
                parameters.Add("VehicleSpeed", random.Next(0, 120)); // km/h
                parameters.Add("TotalEngineHours", random.Next(0, 10000)); // hours

                _logger?.LogInformation($"Read {parameters.Count} parameters from ECU {ecu.Name} via J1939", "J1939ProtocolHandler");
                return parameters;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading parameters from ECU {ecu?.Name} via J1939", "J1939ProtocolHandler", ex);
                return null;
            }
        }

        /// <summary>
        /// Writes parameters to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="parameters">The parameters to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public override async Task<bool> WriteParametersAsync(ECUDevice ecu, Dictionary<string, object> parameters)
        {
            try
            {
                _logger?.LogInformation($"Writing parameters to ECU {ecu?.Name} via J1939", "J1939ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                if (parameters == null || parameters.Count == 0)
                {
                    _logger?.LogError("Parameters are null or empty", "J1939ProtocolHandler");
                    return false;
                }

                // In a real implementation, this would involve sending J1939 messages to write the parameters
                // For now, we'll just simulate this
                await Task.Delay(600); // Simulate parameters write delay

                _logger?.LogInformation($"Wrote {parameters.Count} parameters to ECU {ecu.Name} via J1939", "J1939ProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing parameters to ECU {ecu?.Name} via J1939", "J1939ProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Performs a diagnostic session on an ECU
        /// </summary>
        /// <param name="ecu">The ECU to diagnose</param>
        /// <returns>Diagnostic data</returns>
        public override async Task<DiagnosticData> PerformDiagnosticSessionAsync(ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Performing diagnostic session on ECU {ecu?.Name} via J1939", "J1939ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return new DiagnosticData
                    {
                        ECUId = ecu?.Id,
                        ECUName = ecu?.Name,
                        Timestamp = DateTime.Now,
                        IsSuccessful = false,
                        ErrorMessage = "J1939 protocol handler not initialized or ECU validation failed"
                    };
                }

                // In a real implementation, this would involve sending J1939 messages to perform a diagnostic session
                // For now, we'll just simulate this
                await Task.Delay(1000); // Simulate diagnostic session delay

                // Create a simulated diagnostic data
                DiagnosticData diagnosticData = new DiagnosticData
                {
                    ECUId = ecu.Id,
                    ECUName = ecu.Name,
                    Timestamp = DateTime.Now,
                    ActiveFaults = await ReadActiveFaultsAsync(ecu),
                    InactiveFaults = await ReadInactiveFaultsAsync(ecu),
                    Parameters = await ReadParametersAsync(ecu),
                    OperatingMode = _currentOperatingMode,
                    ConnectionType = _vocomService.CurrentDevice?.ConnectionType ?? VocomConnectionType.USB,
                    IsSuccessful = true,
                    SessionDurationMs = 1000 // Simulated duration
                };

                _logger?.LogInformation($"Performed diagnostic session on ECU {ecu.Name} via J1939", "J1939ProtocolHandler");
                return diagnosticData;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error performing diagnostic session on ECU {ecu?.Name} via J1939", "J1939ProtocolHandler", ex);
                return new DiagnosticData
                {
                    ECUId = ecu?.Id,
                    ECUName = ecu?.Name,
                    Timestamp = DateTime.Now,
                    IsSuccessful = false,
                    ErrorMessage = $"Error performing diagnostic session: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Cancels the current operation
        /// </summary>
        /// <returns>True if cancellation is successful, false otherwise</returns>
        public override async Task<bool> CancelOperationAsync()
        {
            try
            {
                _logger?.LogInformation("Cancelling current operation for J1939 protocol handler", "J1939ProtocolHandler");

                if (!ValidateInitialization())
                {
                    return false;
                }

                // In a real implementation, this would involve sending J1939 messages to cancel the current operation
                // For now, we'll just simulate this
                await Task.Delay(100); // Simulate cancellation delay

                _logger?.LogInformation("Operation cancelled for J1939 protocol handler", "J1939ProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error cancelling operation for J1939 protocol handler", "J1939ProtocolHandler", ex);
                return false;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Validates the ECU
        /// </summary>
        /// <param name="ecu">The ECU to validate</param>
        /// <returns>True if the ECU is valid, false otherwise</returns>
        private new bool ValidateECU(ECUDevice? ecu)
        {
            if (ecu == null)
            {
                _logger?.LogError("ECU is null", "J1939ProtocolHandler");
                return false;
            }

            if (ecu.ProtocolType != ECUProtocolType.J1939)
            {
                _logger?.LogError($"ECU {ecu.Name} does not use J1939 protocol", "J1939ProtocolHandler");
                return false;
            }

            return true;
        }

        #endregion
    }
}
