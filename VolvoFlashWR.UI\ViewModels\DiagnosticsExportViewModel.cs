using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using VolvoFlashWR.Core.Diagnostics;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.UI.Commands;

namespace VolvoFlashWR.UI.ViewModels
{
    /// <summary>
    /// ViewModel for the diagnostics export functionality
    /// </summary>
    public class DiagnosticsExportViewModel : ViewModelBase
    {
        private readonly ILoggingService? _logger;
        private readonly FlashDiagnostics _flashDiagnostics;
        private ObservableCollection<FlashDiagnosticData> _diagnosticRecords = new ObservableCollection<FlashDiagnosticData>();
        private FlashDiagnosticData? _selectedDiagnosticRecord;
        private DateTime _startDate = DateTime.Now.AddDays(-30);
        private DateTime _endDate = DateTime.Now;
        private DiagnosticStatistics? _statistics;
        private string _exportPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "VolvoFlashWR_Diagnostics.csv");
        private bool _isExporting;
        private bool _showPerformanceData;

        /// <summary>
        /// Initializes a new instance of the DiagnosticsExportViewModel class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="flashDiagnostics">The flash diagnostics</param>
        public DiagnosticsExportViewModel(ILoggingService? logger = null, FlashDiagnostics? flashDiagnostics = null)
        {
            _logger = logger;
            _flashDiagnostics = flashDiagnostics ?? new FlashDiagnostics(logger);

            // Initialize commands
            ExportCommand = new RelayCommand(ExecuteExport, CanExecuteExport);
            ExportPerformanceDataCommand = new RelayCommand(ExecuteExportPerformanceData, CanExecuteExportPerformanceData);
            RefreshCommand = new RelayCommand(ExecuteRefresh);
            BrowseCommand = new RelayCommand(ExecuteBrowse);
            ClearDataCommand = new RelayCommand(ExecuteClearData, CanExecuteClearData);

            // Load diagnostic records
            LoadDiagnosticRecords();
            CalculateStatistics();
        }

        /// <summary>
        /// Gets or sets the diagnostic records
        /// </summary>
        public ObservableCollection<FlashDiagnosticData> DiagnosticRecords
        {
            get => _diagnosticRecords;
            set
            {
                _diagnosticRecords = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Gets or sets the selected diagnostic record
        /// </summary>
        public FlashDiagnosticData? SelectedDiagnosticRecord
        {
            get => _selectedDiagnosticRecord;
            set
            {
                _selectedDiagnosticRecord = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasSelectedRecord));
                OnPropertyChanged(nameof(HasPerformanceData));
                ((RelayCommand)ExportPerformanceDataCommand).RaiseCanExecuteChanged();
            }
        }

        /// <summary>
        /// Gets or sets the start date for filtering
        /// </summary>
        public DateTime StartDate
        {
            get => _startDate;
            set
            {
                _startDate = value;
                OnPropertyChanged();
                LoadDiagnosticRecords();
                CalculateStatistics();
            }
        }

        /// <summary>
        /// Gets or sets the end date for filtering
        /// </summary>
        public DateTime EndDate
        {
            get => _endDate;
            set
            {
                _endDate = value;
                OnPropertyChanged();
                LoadDiagnosticRecords();
                CalculateStatistics();
            }
        }

        /// <summary>
        /// Gets or sets the statistics
        /// </summary>
        public DiagnosticStatistics? Statistics
        {
            get => _statistics;
            set
            {
                _statistics = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Gets or sets the export path
        /// </summary>
        public string ExportPath
        {
            get => _exportPath;
            set
            {
                _exportPath = value;
                OnPropertyChanged();
                ((RelayCommand)ExportCommand).RaiseCanExecuteChanged();
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether export is in progress
        /// </summary>
        public bool IsExporting
        {
            get => _isExporting;
            set
            {
                _isExporting = value;
                OnPropertyChanged();
                ((RelayCommand)ExportCommand).RaiseCanExecuteChanged();
                ((RelayCommand)ExportPerformanceDataCommand).RaiseCanExecuteChanged();
                ((RelayCommand)ClearDataCommand).RaiseCanExecuteChanged();
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether to show performance data
        /// </summary>
        public bool ShowPerformanceData
        {
            get => _showPerformanceData;
            set
            {
                _showPerformanceData = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Gets a value indicating whether a record is selected
        /// </summary>
        public bool HasSelectedRecord => SelectedDiagnosticRecord != null;

        /// <summary>
        /// Gets a value indicating whether the selected record has performance data
        /// </summary>
        public bool HasPerformanceData => SelectedDiagnosticRecord?.PerformanceData?.Count > 0;

        /// <summary>
        /// Gets the export command
        /// </summary>
        public ICommand ExportCommand { get; }

        /// <summary>
        /// Gets the export performance data command
        /// </summary>
        public ICommand ExportPerformanceDataCommand { get; }

        /// <summary>
        /// Gets the refresh command
        /// </summary>
        public ICommand RefreshCommand { get; }

        /// <summary>
        /// Gets the browse command
        /// </summary>
        public ICommand BrowseCommand { get; }

        /// <summary>
        /// Gets the clear data command
        /// </summary>
        public ICommand ClearDataCommand { get; }

        /// <summary>
        /// Loads diagnostic records
        /// </summary>
        private void LoadDiagnosticRecords()
        {
            try
            {
                DiagnosticRecords.Clear();

                var filteredRecords = _flashDiagnostics.DiagnosticData
                    .Where(d => d.StartTime >= StartDate && (d.EndTime == null || d.EndTime <= EndDate.AddDays(1)))
                    .OrderByDescending(d => d.StartTime)
                    .ToList();

                foreach (var record in filteredRecords)
                {
                    DiagnosticRecords.Add(record);
                }

                _logger?.LogInformation($"Loaded {DiagnosticRecords.Count} diagnostic records", "DiagnosticsExportViewModel");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error loading diagnostic records: {ex.Message}", "DiagnosticsExportViewModel");
            }
        }

        /// <summary>
        /// Calculates statistics
        /// </summary>
        private void CalculateStatistics()
        {
            try
            {
                Statistics = _flashDiagnostics.GetDiagnosticStatistics(StartDate, EndDate.AddDays(1));
                _logger?.LogInformation("Calculated diagnostic statistics", "DiagnosticsExportViewModel");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error calculating statistics: {ex.Message}", "DiagnosticsExportViewModel");
            }
        }

        /// <summary>
        /// Determines whether the export command can execute
        /// </summary>
        private bool CanExecuteExport(object? parameter)
        {
            return !IsExporting && DiagnosticRecords.Count > 0 && !string.IsNullOrEmpty(ExportPath);
        }

        /// <summary>
        /// Executes the export command
        /// </summary>
        private async void ExecuteExport(object? parameter)
        {
            try
            {
                IsExporting = true;
                _logger?.LogInformation($"Exporting diagnostic data to {ExportPath}", "DiagnosticsExportViewModel");

                await Task.Run(() =>
                {
                    bool success = _flashDiagnostics.ExportDiagnosticsToCsv(ExportPath);

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        if (success)
                        {
                            MessageBox.Show($"Diagnostic data exported successfully to {ExportPath}", "Export Successful", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show("Failed to export diagnostic data", "Export Failed", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    });
                });
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error exporting diagnostic data: {ex.Message}", "DiagnosticsExportViewModel");
                MessageBox.Show($"Error exporting diagnostic data: {ex.Message}", "Export Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsExporting = false;
            }
        }

        /// <summary>
        /// Determines whether the export performance data command can execute
        /// </summary>
        private bool CanExecuteExportPerformanceData(object? parameter)
        {
            return !IsExporting && HasSelectedRecord && HasPerformanceData;
        }

        /// <summary>
        /// Executes the export performance data command
        /// </summary>
        private async void ExecuteExportPerformanceData(object? parameter)
        {
            try
            {
                if (SelectedDiagnosticRecord == null)
                {
                    return;
                }

                IsExporting = true;

                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "CSV Files (*.csv)|*.csv",
                    DefaultExt = ".csv",
                    FileName = $"VolvoFlashWR_Performance_{SelectedDiagnosticRecord.OperationId}.csv"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    string filePath = saveFileDialog.FileName;
                    _logger?.LogInformation($"Exporting performance data for operation {SelectedDiagnosticRecord.OperationId} to {filePath}", "DiagnosticsExportViewModel");

                    await Task.Run(() =>
                    {
                        bool success = _flashDiagnostics.ExportPerformanceDataToCsv(SelectedDiagnosticRecord.OperationId, filePath);

                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            if (success)
                            {
                                MessageBox.Show($"Performance data exported successfully to {filePath}", "Export Successful", MessageBoxButton.OK, MessageBoxImage.Information);
                            }
                            else
                            {
                                MessageBox.Show("Failed to export performance data", "Export Failed", MessageBoxButton.OK, MessageBoxImage.Error);
                            }
                        });
                    });
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error exporting performance data: {ex.Message}", "DiagnosticsExportViewModel");
                MessageBox.Show($"Error exporting performance data: {ex.Message}", "Export Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsExporting = false;
            }
        }

        /// <summary>
        /// Executes the refresh command
        /// </summary>
        private void ExecuteRefresh(object? parameter)
        {
            LoadDiagnosticRecords();
            CalculateStatistics();
        }

        /// <summary>
        /// Executes the browse command
        /// </summary>
        private void ExecuteBrowse(object? parameter)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "CSV Files (*.csv)|*.csv",
                    DefaultExt = ".csv",
                    FileName = Path.GetFileName(ExportPath)
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    ExportPath = saveFileDialog.FileName;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error browsing for export path: {ex.Message}", "DiagnosticsExportViewModel");
            }
        }

        /// <summary>
        /// Determines whether the clear data command can execute
        /// </summary>
        private bool CanExecuteClearData(object? parameter)
        {
            return !IsExporting && DiagnosticRecords.Count > 0;
        }

        /// <summary>
        /// Executes the clear data command
        /// </summary>
        private void ExecuteClearData(object? parameter)
        {
            try
            {
                var result = MessageBox.Show("Are you sure you want to clear all diagnostic data? This action cannot be undone.", "Confirm Clear", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    _flashDiagnostics.ClearDiagnosticData();
                    LoadDiagnosticRecords();
                    CalculateStatistics();
                    _logger?.LogInformation("Cleared all diagnostic data", "DiagnosticsExportViewModel");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error clearing diagnostic data: {ex.Message}", "DiagnosticsExportViewModel");
                MessageBox.Show($"Error clearing diagnostic data: {ex.Message}", "Clear Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
