using System;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Tests.Models
{
    [TestFixture]
    public class FlashHealthStatusErrorPatternTests
    {
        private FlashHealthStatus _status;

        [SetUp]
        public void Setup()
        {
            _status = new FlashHealthStatus();
        }

        [Test]
        public void Constructor_InitializesErrorPatternDictionaries()
        {
            // Assert
            Assert.That(_status.BytePositionErrorCounts, Is.Not.Null);
            Assert.That(_status.BitPositionErrorCounts, Is.Not.Null);
            Assert.That(_status.DetailedErrorPatterns, Is.Not.Null);
            Assert.That(_status.BytePositionErrorCounts, Is.Empty);
            Assert.That(_status.BitPositionErrorCounts, Is.Empty);
            Assert.That(_status.DetailedErrorPatterns, Is.Empty);
        }

        [Test]
        public void UpdateErrorPatternStatistics_SingleError_UpdatesCounters()
        {
            // Arrange
            int bytePosition = 3;
            int bitPosition = 5;

            // Act
            _status.UpdateErrorPatternStatistics(bytePosition, bitPosition);

            // Assert
            Assert.That(_status.BytePositionErrorCounts.Count, Is.EqualTo(1));
            Assert.That(_status.BitPositionErrorCounts.Count, Is.EqualTo(1));
            Assert.That(_status.DetailedErrorPatterns.Count, Is.EqualTo(1));
            Assert.That(_status.BytePositionErrorCounts[bytePosition], Is.EqualTo(1));
            Assert.That(_status.BitPositionErrorCounts[bitPosition], Is.EqualTo(1));
            Assert.That(_status.DetailedErrorPatterns[$"{bytePosition}:{bitPosition}"], Is.EqualTo(1));
        }

        [Test]
        public void UpdateErrorPatternStatistics_MultipleErrors_UpdatesCounters()
        {
            // Arrange
            int bytePosition1 = 3;
            int bitPosition1 = 5;
            int bytePosition2 = 4;
            int bitPosition2 = 6;

            // Act
            _status.UpdateErrorPatternStatistics(bytePosition1, bitPosition1);
            _status.UpdateErrorPatternStatistics(bytePosition1, bitPosition1); // Same position again
            _status.UpdateErrorPatternStatistics(bytePosition2, bitPosition2); // Different position

            // Assert
            Assert.That(_status.BytePositionErrorCounts.Count, Is.EqualTo(2));
            Assert.That(_status.BitPositionErrorCounts.Count, Is.EqualTo(2));
            Assert.That(_status.DetailedErrorPatterns.Count, Is.EqualTo(2));

            Assert.That(_status.BytePositionErrorCounts[bytePosition1], Is.EqualTo(2));
            Assert.That(_status.BytePositionErrorCounts[bytePosition2], Is.EqualTo(1));

            Assert.That(_status.BitPositionErrorCounts[bitPosition1], Is.EqualTo(2));
            Assert.That(_status.BitPositionErrorCounts[bitPosition2], Is.EqualTo(1));

            Assert.That(_status.DetailedErrorPatterns[$"{bytePosition1}:{bitPosition1}"], Is.EqualTo(2));
            Assert.That(_status.DetailedErrorPatterns[$"{bytePosition2}:{bitPosition2}"], Is.EqualTo(1));
        }

        [Test]
        public void UpdateErrorPatternStatistics_SameBitDifferentBytes_UpdatesCounters()
        {
            // Arrange
            int bytePosition1 = 3;
            int bytePosition2 = 4;
            int bitPosition = 5; // Same bit position in different bytes

            // Act
            _status.UpdateErrorPatternStatistics(bytePosition1, bitPosition);
            _status.UpdateErrorPatternStatistics(bytePosition2, bitPosition);

            // Assert
            Assert.That(_status.BytePositionErrorCounts.Count, Is.EqualTo(2));
            Assert.That(_status.BitPositionErrorCounts.Count, Is.EqualTo(1));
            Assert.That(_status.DetailedErrorPatterns.Count, Is.EqualTo(2));

            Assert.That(_status.BytePositionErrorCounts[bytePosition1], Is.EqualTo(1));
            Assert.That(_status.BytePositionErrorCounts[bytePosition2], Is.EqualTo(1));

            Assert.That(_status.BitPositionErrorCounts[bitPosition], Is.EqualTo(2));

            Assert.That(_status.DetailedErrorPatterns[$"{bytePosition1}:{bitPosition}"], Is.EqualTo(1));
            Assert.That(_status.DetailedErrorPatterns[$"{bytePosition2}:{bitPosition}"], Is.EqualTo(1));
        }

        [Test]
        public void UpdateErrorPatternStatistics_SameBytePositionDifferentBits_UpdatesCounters()
        {
            // Arrange
            int bytePosition = 3; // Same byte position
            int bitPosition1 = 5;
            int bitPosition2 = 6;

            // Act
            _status.UpdateErrorPatternStatistics(bytePosition, bitPosition1);
            _status.UpdateErrorPatternStatistics(bytePosition, bitPosition2);

            // Assert
            Assert.That(_status.BytePositionErrorCounts.Count, Is.EqualTo(1));
            Assert.That(_status.BitPositionErrorCounts.Count, Is.EqualTo(2));
            Assert.That(_status.DetailedErrorPatterns.Count, Is.EqualTo(2));

            Assert.That(_status.BytePositionErrorCounts[bytePosition], Is.EqualTo(2));

            Assert.That(_status.BitPositionErrorCounts[bitPosition1], Is.EqualTo(1));
            Assert.That(_status.BitPositionErrorCounts[bitPosition2], Is.EqualTo(1));

            Assert.That(_status.DetailedErrorPatterns[$"{bytePosition}:{bitPosition1}"], Is.EqualTo(1));
            Assert.That(_status.DetailedErrorPatterns[$"{bytePosition}:{bitPosition2}"], Is.EqualTo(1));
        }

        [Test]
        public void HealthStatus_CanBeManuallyOverridden()
        {
            // Arrange - default should be Good with no errors
            Assert.That(_status.HealthStatusLevel, Is.EqualTo(FlashHealthStatusLevel.Good));

            // Act - manually override to Critical
            _status.HealthStatusLevel = FlashHealthStatusLevel.Critical;

            // Assert
            Assert.That(_status.HealthStatusLevel, Is.EqualTo(FlashHealthStatusLevel.Critical));
            Assert.That(_status.HealthStatusDescription, Does.Contain("critical condition"));
        }
    }
}

