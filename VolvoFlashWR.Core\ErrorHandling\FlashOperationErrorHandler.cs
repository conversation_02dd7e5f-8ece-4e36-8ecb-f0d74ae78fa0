using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Diagnostics;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Utilities;

namespace VolvoFlashWR.Core.ErrorHandling
{
    /// <summary>
    /// Handles errors in flash operations with recovery strategies
    /// </summary>
    public class FlashOperationErrorHandler
    {
        private readonly ILoggingService _logger;
        private readonly FlashOperationMonitor _monitor;
        private readonly Dictionary<FlashErrorType, IFlashErrorRecoveryStrategy> _recoveryStrategies;

        /// <summary>
        /// Initializes a new instance of the FlashOperationErrorHandler class
        /// </summary>
        /// <param name="logger">The logger to use</param>
        /// <param name="monitor">The flash operation monitor</param>
        public FlashOperationErrorHandler(ILoggingService logger, FlashOperationMonitor monitor)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _monitor = monitor ?? throw new ArgumentNullException(nameof(monitor));

            // Initialize recovery strategies
            _recoveryStrategies = new Dictionary<FlashErrorType, IFlashErrorRecoveryStrategy>
            {
                { FlashErrorType.Timeout, new TimeoutRecoveryStrategy(logger) },
                { FlashErrorType.VerificationFailed, new VerificationRecoveryStrategy(logger) },
                { FlashErrorType.AddressError, new AddressErrorRecoveryStrategy(logger) },
                { FlashErrorType.ProtectionViolation, new ProtectionViolationRecoveryStrategy(logger) },
                { FlashErrorType.CommunicationError, new CommunicationErrorRecoveryStrategy(logger) },
                { FlashErrorType.StalledOperation, new StalledOperationRecoveryStrategy(logger, monitor) }
            };

            // Subscribe to monitor events
            _monitor.OperationFailed += Monitor_OperationFailed;
            _monitor.OperationUpdated += Monitor_OperationUpdated;
        }

        /// <summary>
        /// Handles an error in a flash operation
        /// </summary>
        /// <param name="operationId">The operation ID</param>
        /// <param name="errorType">The type of error</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="errorData">Additional error data</param>
        /// <returns>A recovery result indicating the outcome of the recovery attempt</returns>
        public async Task<FlashErrorRecoveryResult> HandleErrorAsync(Guid operationId, FlashErrorType errorType, string errorMessage, object? errorData = null)
        {
            try
            {
                // Get the operation
                var operation = _monitor.GetOperation(operationId);
                if (operation == null)
                {
                    _logger.LogError($"Cannot handle error for operation {operationId}: Operation not found", "FlashOperationErrorHandler");
                    return new FlashErrorRecoveryResult(false, "Operation not found");
                }

                // Log the error
                _logger.LogError($"Handling error in operation {operationId} ({operation.OperationType} at 0x{operation.Address:X8}): {errorType} - {errorMessage}", "FlashOperationErrorHandler");

                // Get the recovery strategy for this error type
                if (_recoveryStrategies.TryGetValue(errorType, out var strategy))
                {
                    // Attempt recovery
                    var recoveryResult = await strategy.RecoverAsync(operation, errorMessage, errorData);

                    // Log the recovery result
                    if (recoveryResult.Success)
                    {
                        _logger.LogInformation($"Successfully recovered from {errorType} in operation {operationId}: {recoveryResult.Message}", "FlashOperationErrorHandler");

                        // Update the operation status
                        _monitor.UpdateOperationProgress(operationId, operation.BytesProcessed, FlashOperationStatus.InProgress);
                    }
                    else
                    {
                        _logger.LogError($"Failed to recover from {errorType} in operation {operationId}: {recoveryResult.Message}", "FlashOperationErrorHandler");

                        // Mark the operation as failed
                        _monitor.CompleteOperation(operationId, false, $"{errorType}: {errorMessage}. Recovery failed: {recoveryResult.Message}");
                    }

                    return recoveryResult;
                }
                else
                {
                    _logger.LogWarning($"No recovery strategy found for error type {errorType}", "FlashOperationErrorHandler");

                    // Mark the operation as failed
                    _monitor.CompleteOperation(operationId, false, $"{errorType}: {errorMessage}. No recovery strategy available.");

                    return new FlashErrorRecoveryResult(false, "No recovery strategy available");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error handling flash operation error: {ex.Message}", "FlashOperationErrorHandler");
                return new FlashErrorRecoveryResult(false, $"Error handler exception: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles the OperationFailed event of the monitor
        /// </summary>
        private void Monitor_OperationFailed(object sender, FlashOperationEventArgs e)
        {
            // Analyze the error message to determine the error type
            var errorType = AnalyzeErrorMessage(e.Operation.ErrorMessage);

            // Handle the error asynchronously
            _ = HandleErrorAsync(e.Operation.Id, errorType, e.Operation.ErrorMessage);
        }

        /// <summary>
        /// Handles the OperationUpdated event of the monitor
        /// </summary>
        private void Monitor_OperationUpdated(object sender, FlashOperationEventArgs e)
        {
            // Check if the operation is stalled
            if (e.Operation.Status == FlashOperationStatus.Stalled)
            {
                // Handle the stalled operation asynchronously
                _ = HandleErrorAsync(e.Operation.Id, FlashErrorType.StalledOperation, "Operation appears to be stalled");
            }
        }

        /// <summary>
        /// Analyzes an error message to determine the error type
        /// </summary>
        /// <param name="errorMessage">The error message</param>
        /// <returns>The determined error type</returns>
        private FlashErrorType AnalyzeErrorMessage(string errorMessage)
        {
            if (string.IsNullOrEmpty(errorMessage))
            {
                return FlashErrorType.Unknown;
            }

            // Check for common error patterns
            if (errorMessage.Contains("timeout", StringComparison.OrdinalIgnoreCase))
            {
                return FlashErrorType.Timeout;
            }
            else if (errorMessage.Contains("verification failed", StringComparison.OrdinalIgnoreCase))
            {
                return FlashErrorType.VerificationFailed;
            }
            else if (errorMessage.Contains("address", StringComparison.OrdinalIgnoreCase) &&
                     (errorMessage.Contains("invalid", StringComparison.OrdinalIgnoreCase) ||
                      errorMessage.Contains("error", StringComparison.OrdinalIgnoreCase)))
            {
                return FlashErrorType.AddressError;
            }
            else if (errorMessage.Contains("protection", StringComparison.OrdinalIgnoreCase) &&
                     errorMessage.Contains("violation", StringComparison.OrdinalIgnoreCase))
            {
                return FlashErrorType.ProtectionViolation;
            }
            else if (errorMessage.Contains("communication", StringComparison.OrdinalIgnoreCase) &&
                     errorMessage.Contains("error", StringComparison.OrdinalIgnoreCase))
            {
                return FlashErrorType.CommunicationError;
            }
            else if (errorMessage.Contains("stalled", StringComparison.OrdinalIgnoreCase))
            {
                return FlashErrorType.StalledOperation;
            }

            return FlashErrorType.Unknown;
        }

        /// <summary>
        /// Disposes the error handler
        /// </summary>
        public void Dispose()
        {
            // Unsubscribe from monitor events
            _monitor.OperationFailed -= Monitor_OperationFailed;
            _monitor.OperationUpdated -= Monitor_OperationUpdated;

            // Dispose recovery strategies
            foreach (var strategy in _recoveryStrategies.Values)
            {
                if (strategy is IDisposable disposable)
                {
                    disposable.Dispose();
                }
            }
        }
    }

    /// <summary>
    /// Represents the result of a flash error recovery attempt
    /// </summary>
    public class FlashErrorRecoveryResult
    {
        /// <summary>
        /// Gets whether the recovery was successful
        /// </summary>
        public bool Success { get; }

        /// <summary>
        /// Gets a message describing the recovery result
        /// </summary>
        public string Message { get; }

        /// <summary>
        /// Gets additional data about the recovery
        /// </summary>
        public object Data { get; }

        /// <summary>
        /// Initializes a new instance of the FlashErrorRecoveryResult class
        /// </summary>
        /// <param name="success">Whether the recovery was successful</param>
        /// <param name="message">A message describing the recovery result</param>
        /// <param name="data">Additional data about the recovery</param>
        public FlashErrorRecoveryResult(bool success, string message, object? data = null)
        {
            Success = success;
            Message = message;
            Data = data;
        }
    }

    /// <summary>
    /// Represents the type of error that occurred in a flash operation
    /// </summary>
    public enum FlashErrorType
    {
        /// <summary>
        /// The error type is unknown
        /// </summary>
        Unknown,

        /// <summary>
        /// A timeout occurred while waiting for an operation to complete
        /// </summary>
        Timeout,

        /// <summary>
        /// Verification of programmed data failed
        /// </summary>
        VerificationFailed,

        /// <summary>
        /// An error occurred with the address used in the operation
        /// </summary>
        AddressError,

        /// <summary>
        /// A protection violation occurred
        /// </summary>
        ProtectionViolation,

        /// <summary>
        /// A communication error occurred
        /// </summary>
        CommunicationError,

        /// <summary>
        /// The operation appears to be stalled
        /// </summary>
        StalledOperation
    }
}
