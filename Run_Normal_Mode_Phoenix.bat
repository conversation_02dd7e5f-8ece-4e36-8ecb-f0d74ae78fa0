@echo off
title VolvoFlashWR - Normal Mode with Phoenix APCI
color 0A

echo ===============================================================================
echo                    VolvoFlashWR - Normal Mode with Phoenix APCI
echo ===============================================================================
echo Starting VolvoFlashWR application in Normal Mode with real hardware support...

REM Clear any existing environment variables first
set USE_DUMMY_IMPLEMENTATIONS=
set VERBOSE_LOGGING=
set LOG_LEVEL=
set SAFE_MODE=
set DEMO_MODE=
set PHOENIX_VOCOM_ENABLED=

REM Set environment variables for normal mode with Phoenix APCI real hardware
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=true
set LOG_LEVEL=Debug
set PHOENIX_VOCOM_ENABLED=true
set PHOENIX_DIAG_PATH=C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021

echo === Environment Configuration ===
echo USE_DUMMY_IMPLEMENTATIONS=%USE_DUMMY_IMPLEMENTATIONS%
echo PHOENIX_VOCOM_ENABLED=%PHOENIX_VOCOM_ENABLED%
echo VERBOSE_LOGGING=%VERBOSE_LOGGING%
echo LOG_LEVEL=%LOG_LEVEL%

echo.
echo === Building Solution ===
echo Building solution in Release mode for optimal performance...
dotnet build VolvoFlashWR.sln --configuration Release

REM Check if build was successful
if %ERRORLEVEL% NEQ 0 (
    echo Build failed! Please fix the build errors and run again.
    echo Press any key to exit...
    pause >nul
    exit /b %ERRORLEVEL%
)

echo Build completed successfully!

echo.
echo === Copying Essential Phoenix APCI Libraries ===

REM Change to the output directory
cd /d "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64"

REM Copy critical Phoenix APCI libraries from root Libraries folder
echo Copying essential libraries for Phoenix APCI real hardware communication...

REM Core APCI libraries (CRITICAL)
copy "..\..\..\..\..\Libraries\apci.dll" "." >nul 2>&1
if exist "apci.dll" (echo   ✓ apci.dll) else (echo   ✗ apci.dll - MISSING!)
copy "..\..\..\..\..\Libraries\apcidb.dll" "." >nul 2>&1
if exist "apcidb.dll" (echo   ✓ apcidb.dll) else (echo   ✗ apcidb.dll - MISSING!)
copy "..\..\..\..\..\Libraries\Rpci.dll" "." >nul 2>&1
if exist "Rpci.dll" (echo   ✓ Rpci.dll) else (echo   ✗ Rpci.dll - MISSING!)
copy "..\..\..\..\..\Libraries\Pc2.dll" "." >nul 2>&1
if exist "Pc2.dll" (echo   ✓ Pc2.dll) else (echo   ✗ Pc2.dll - MISSING!)

REM Vocom driver (CRITICAL)
copy "..\..\..\..\..\Libraries\WUDFPuma.dll" "." >nul 2>&1
if exist "WUDFPuma.dll" (echo   ✓ WUDFPuma.dll) else (echo   ✗ WUDFPuma.dll - MISSING!)
copy "..\..\..\..\..\Libraries\WUDFUpdate_01009.dll" "." >nul 2>&1
if exist "WUDFUpdate_01009.dll" (echo   ✓ WUDFUpdate_01009.dll) else (echo   ✗ WUDFUpdate_01009.dll - MISSING!)

REM Phoenix libraries (ESSENTIAL)
copy "..\..\..\..\..\Libraries\PhoenixESW.dll" "." >nul 2>&1
if exist "PhoenixESW.dll" (echo   ✓ PhoenixESW.dll) else (echo   ✗ PhoenixESW.dll - MISSING!)
copy "..\..\..\..\..\Libraries\PhoenixGeneral.dll" "." >nul 2>&1
if exist "PhoenixGeneral.dll" (echo   ✓ PhoenixGeneral.dll) else (echo   ✗ PhoenixGeneral.dll - MISSING!)

REM Volvo APCI libraries (REQUIRED)
copy "..\..\..\..\..\Libraries\Volvo.ApciPlus.dll" "." >nul 2>&1
if exist "Volvo.ApciPlus.dll" (echo   ✓ Volvo.ApciPlus.dll) else (echo   ✗ Volvo.ApciPlus.dll - MISSING!)
copy "..\..\..\..\..\Libraries\Volvo.ApciPlusData.dll" "." >nul 2>&1
if exist "Volvo.ApciPlusData.dll" (echo   ✓ Volvo.ApciPlusData.dll) else (echo   ✗ Volvo.ApciPlusData.dll - MISSING!)

REM Volvo protocol libraries (REQUIRED)
copy "..\..\..\..\..\Libraries\Volvo.NVS.Core.dll" "." >nul 2>&1
if exist "Volvo.NVS.Core.dll" (echo   ✓ Volvo.NVS.Core.dll) else (echo   ✗ Volvo.NVS.Core.dll - MISSING!)
copy "..\..\..\..\..\Libraries\Volvo.NAMS.AC.Services.Interface.dll" "." >nul 2>&1
if exist "Volvo.NAMS.AC.Services.Interface.dll" (echo   ✓ Volvo.NAMS.AC.Services.Interface.dll) else (echo   ✗ Volvo.NAMS.AC.Services.Interface.dll - MISSING!)

REM Vodia libraries (REQUIRED)
copy "..\..\..\..\..\Libraries\Vodia.CommonDomain.Model.dll" "." >nul 2>&1
if exist "Vodia.CommonDomain.Model.dll" (echo   ✓ Vodia.CommonDomain.Model.dll) else (echo   ✗ Vodia.CommonDomain.Model.dll - MISSING!)
copy "..\..\..\..\..\Libraries\Vodia.Contracts.Common.dll" "." >nul 2>&1
if exist "Vodia.Contracts.Common.dll" (echo   ✓ Vodia.Contracts.Common.dll) else (echo   ✗ Vodia.Contracts.Common.dll - MISSING!)

REM Essential dependencies
copy "..\..\..\..\..\Libraries\log4net.dll" "." >nul 2>&1
if exist "log4net.dll" (echo   ✓ log4net.dll) else (echo   ✗ log4net.dll - MISSING!)
copy "..\..\..\..\..\Libraries\Newtonsoft.Json.dll" "." >nul 2>&1
if exist "Newtonsoft.Json.dll" (echo   ✓ Newtonsoft.Json.dll) else (echo   ✗ Newtonsoft.Json.dll - MISSING!)

echo.
echo === Verifying Critical Libraries ===

set MISSING_CRITICAL=0

if not exist "WUDFPuma.dll" (
    echo ERROR: WUDFPuma.dll not found - Critical for Vocom communication
    set /a MISSING_CRITICAL+=1
)

if not exist "Volvo.ApciPlus.dll" (
    echo ERROR: Volvo.ApciPlus.dll not found - Critical for APCI communication
    set /a MISSING_CRITICAL+=1
)

if not exist "apci.dll" (
    echo ERROR: apci.dll not found - Critical for APCI core functionality
    set /a MISSING_CRITICAL+=1
)

if not exist "PhoenixESW.dll" (
    echo ERROR: PhoenixESW.dll not found - Critical for Phoenix integration
    set /a MISSING_CRITICAL+=1
)

if %MISSING_CRITICAL% GTR 0 (
    echo.
    echo CRITICAL ERROR: %MISSING_CRITICAL% essential libraries are missing!
    echo The application may not work properly with real Vocom adapters.
    echo Please check the Libraries folder and library copying.
    echo.
    echo Press any key to continue anyway or Ctrl+C to exit...
    pause >nul
) else (
    echo All critical libraries found - Ready for Phoenix APCI real hardware mode!
)

echo.
echo === Starting Application ===
echo.
echo Starting VolvoFlashWR Launcher with Phoenix APCI enabled...
echo Environment: USE_DUMMY_IMPLEMENTATIONS=%USE_DUMMY_IMPLEMENTATIONS%
echo Environment: PHOENIX_VOCOM_ENABLED=%PHOENIX_VOCOM_ENABLED%

REM Run the application and wait for it to complete
"VolvoFlashWR.Launcher.exe" --mode=Normal

REM Return to original directory
cd /d "%~dp0"

echo.
echo Application has exited.
echo Press any key to close this window...
pause >nul
