@echo off
title VolvoFlashWR - Normal Mode with Phoenix APCI
color 0A

echo ===============================================================================
echo                    VolvoFlashWR - Normal Mode with Phoenix APCI
echo ===============================================================================
echo Starting VolvoFlashWR application in Normal Mode with real hardware support...

REM Clear any existing environment variables first
set USE_DUMMY_IMPLEMENTATIONS=
set VERBOSE_LOGGING=
set LOG_LEVEL=
set SAFE_MODE=
set DEMO_MODE=
set PHOENIX_VOCOM_ENABLED=

REM Set environment variables for normal mode with Phoenix APCI real hardware
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=false
set LOG_LEVEL=Information
set PHOENIX_VOCOM_ENABLED=true
set PHOENIX_DIAG_PATH=C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021

echo === Environment Configuration ===
echo USE_DUMMY_IMPLEMENTATIONS=%USE_DUMMY_IMPLEMENTATIONS%
echo PHOENIX_VOCOM_ENABLED=%PHOENIX_VOCOM_ENABLED%
echo VERBOSE_LOGGING=%VERBOSE_LOGGING%
echo LOG_LEVEL=%LOG_LEVEL%

echo.
echo === Quick Build Check ===
echo Checking if rebuild is needed...

REM Check if executable exists and is recent
if not exist "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe" (
    echo Executable not found - performing full build...
    goto :FULL_BUILD
)

REM Check if any source files are newer than the executable
for /f %%i in ('dir /s /b *.cs *.csproj 2^>nul ^| find /c /v ""') do set SOURCE_COUNT=%%i
if %SOURCE_COUNT% EQU 0 (
    echo No source changes detected - skipping build...
    goto :SKIP_BUILD
)

echo Source files detected - performing incremental build...
dotnet build VolvoFlashWR.sln --configuration Release --no-restore --verbosity minimal

REM Check if build was successful
if %ERRORLEVEL% NEQ 0 (
    echo Build failed! Please fix the build errors and run again.
    echo Press any key to exit...
    pause >nul
    exit /b %ERRORLEVEL%
)
goto :BUILD_COMPLETE

:FULL_BUILD
echo Performing full build...
dotnet build VolvoFlashWR.sln --configuration Release --verbosity minimal

REM Check if build was successful
if %ERRORLEVEL% NEQ 0 (
    echo Build failed! Please fix the build errors and run again.
    echo Press any key to exit...
    pause >nul
    exit /b %ERRORLEVEL%
)
goto :BUILD_COMPLETE

:SKIP_BUILD
echo Build skipped - using existing executable.

:BUILD_COMPLETE
echo Build process completed!

echo.
echo === Fast Library Setup ===

REM Change to the output directory
cd /d "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64"

REM Check if libraries are already present and up-to-date
if exist "WUDFPuma.dll" if exist "Volvo.ApciPlus.dll" if exist "apci.dll" if exist "PhoenixESW.dll" (
    echo Libraries already present - skipping copy operation...
    goto :VERIFY_LIBRARIES
)

echo Copying essential libraries for Phoenix APCI real hardware communication...

REM Batch copy all critical libraries at once for speed
echo Copying core libraries...
copy "..\..\..\..\..\Libraries\apci.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\apcidb.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\Rpci.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\Pc2.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\WUDFPuma.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\WUDFUpdate_01009.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\PhoenixESW.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\PhoenixGeneral.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\Volvo.ApciPlus.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\Volvo.ApciPlusData.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\Volvo.NVS.Core.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\Volvo.NAMS.AC.Services.Interface.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\Vodia.CommonDomain.Model.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\Vodia.Contracts.Common.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\log4net.dll" "." >nul 2>&1
copy "..\..\..\..\..\Libraries\Newtonsoft.Json.dll" "." >nul 2>&1

echo Library copy completed.

:VERIFY_LIBRARIES

echo.
echo === Quick Verification ===

REM Quick check for critical libraries only
if not exist "WUDFPuma.dll" (
    echo WARNING: WUDFPuma.dll missing - Vocom communication may fail
)
if not exist "Volvo.ApciPlus.dll" (
    echo WARNING: Volvo.ApciPlus.dll missing - APCI communication may fail
)
if not exist "apci.dll" (
    echo WARNING: apci.dll missing - Core APCI functionality may fail
)
if not exist "PhoenixESW.dll" (
    echo WARNING: PhoenixESW.dll missing - Phoenix integration may fail
)

echo Ready for Phoenix APCI real hardware mode!

echo.
echo === Starting Application ===
echo Starting VolvoFlashWR Launcher...

REM Start the application in the background for faster startup
start "" "VolvoFlashWR.Launcher.exe" --mode=Normal

REM Return to original directory
cd /d "%~dp0"

echo.
echo Application launched successfully!
echo The VolvoFlashWR application is now running.
echo You can close this window or press any key to exit...
pause >nul
