using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for logging service
    /// </summary>
    public interface ILoggingService
    {
        /// <summary>
        /// Event triggered when a log entry is added
        /// </summary>
        event EventHandler<LogEntry> LogEntryAdded;

        /// <summary>
        /// Gets the log file path
        /// </summary>
        string LogFilePath { get; }

        /// <summary>
        /// Gets whether detailed logging is enabled
        /// </summary>
        bool DetailedLoggingEnabled { get; }

        /// <summary>
        /// Initializes the logging service
        /// </summary>
        /// <param name="logFilePath">Path to the log file</param>
        /// <param name="enableDetailedLogging">Whether to enable detailed logging</param>
        /// <returns>True if initialization is successful, false otherwise</returns>
        Task<bool> InitializeAsync(string logFilePath, bool enableDetailedLogging);

        /// <summary>
        /// Logs an information message
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        void LogInformation(string message, string source = "");

        /// <summary>
        /// Logs a warning message
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        void LogWarning(string message, string source = "");

        /// <summary>
        /// Logs an error message
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        /// <param name="exception">The exception associated with the error</param>
        void LogError(string message, string source = "", Exception? exception = null);

        /// <summary>
        /// Logs a debug message (only if detailed logging is enabled)
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        void LogDebug(string message, string source = "");

        /// <summary>
        /// Gets all log entries
        /// </summary>
        /// <returns>List of all log entries</returns>
        Task<List<LogEntry>> GetAllLogEntriesAsync();

        /// <summary>
        /// Gets log entries filtered by log level
        /// </summary>
        /// <param name="level">The log level to filter by</param>
        /// <returns>List of filtered log entries</returns>
        Task<List<LogEntry>> GetLogEntriesByLevelAsync(LogLevel level);

        /// <summary>
        /// Gets log entries filtered by source
        /// </summary>
        /// <param name="source">The source to filter by</param>
        /// <returns>List of filtered log entries</returns>
        Task<List<LogEntry>> GetLogEntriesBySourceAsync(string source);

        /// <summary>
        /// Gets log entries filtered by date range
        /// </summary>
        /// <param name="startDate">The start date</param>
        /// <param name="endDate">The end date</param>
        /// <returns>List of filtered log entries</returns>
        Task<List<LogEntry>> GetLogEntriesByDateRangeAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Clears all log entries
        /// </summary>
        /// <returns>True if clearing is successful, false otherwise</returns>
        Task<bool> ClearLogEntriesAsync();

        /// <summary>
        /// Exports log entries to a file
        /// </summary>
        /// <param name="filePath">The path to export to</param>
        /// <returns>True if export is successful, false otherwise</returns>
        Task<bool> ExportLogEntriesAsync(string filePath);
    }

    /// <summary>
    /// Represents a log entry
    /// </summary>
    public class LogEntry
    {
        /// <summary>
        /// Unique identifier for the log entry
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Timestamp of the log entry
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// Level of the log entry
        /// </summary>
        public LogLevel Level { get; set; }

        /// <summary>
        /// Message of the log entry
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Source of the log entry
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// Exception associated with the log entry (if any)
        /// </summary>
        public Exception Exception { get; set; }
    }

    /// <summary>
    /// Represents the level of a log entry
    /// </summary>
    public enum LogLevel
    {
        Debug,
        Information,
        Warning,
        Error
    }
}
