using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for backup service
    /// </summary>
    public interface IBackupService
    {
        /// <summary>
        /// Event triggered when a backup is created
        /// </summary>
        event EventHandler<BackupData> BackupCreated;

        /// <summary>
        /// Event triggered when a backup is restored
        /// </summary>
        event EventHandler<BackupData> BackupRestored;

        /// <summary>
        /// Event triggered when an error occurs during backup operations
        /// </summary>
        event EventHandler<string> BackupError;

        /// <summary>
        /// Gets the backup directory path
        /// </summary>
        string BackupDirectoryPath { get; }

        /// <summary>
        /// Gets or sets whether to use compression for backups
        /// </summary>
        bool UseCompression { get; set; }

        /// <summary>
        /// Gets or sets whether to use encryption for backups
        /// </summary>
        bool UseEncryption { get; set; }

        /// <summary>
        /// Initializes the backup service
        /// </summary>
        /// <param name="ecuService">The ECU communication service to use</param>
        /// <returns>True if initialization is successful, false otherwise</returns>
        Task<bool> InitializeAsync(IECUCommunicationService ecuService);

        /// <summary>
        /// Creates a backup of an ECU
        /// </summary>
        /// <param name="ecu">The ECU to backup</param>
        /// <param name="description">Optional description for the backup</param>
        /// <param name="category">Optional category for the backup</param>
        /// <param name="tags">Optional tags for the backup</param>
        /// <param name="includeEEPROM">Whether to include EEPROM data in the backup</param>
        /// <param name="includeMicrocontrollerCode">Whether to include microcontroller code in the backup</param>
        /// <param name="includeParameters">Whether to include parameters in the backup</param>
        /// <returns>The created backup data</returns>
        Task<BackupData> CreateBackupAsync(ECUDevice ecu, string description = "", string category = "", List<string> tags = null, bool includeEEPROM = true, bool includeMicrocontrollerCode = true, bool includeParameters = true);

        /// <summary>
        /// Creates a new version of an existing backup
        /// </summary>
        /// <param name="parentBackup">The parent backup</param>
        /// <param name="ecu">The ECU to backup</param>
        /// <param name="versionNotes">Notes describing what changed in this version</param>
        /// <param name="includeEEPROM">Whether to include EEPROM data</param>
        /// <param name="includeMicrocontrollerCode">Whether to include microcontroller code</param>
        /// <param name="includeParameters">Whether to include parameters</param>
        /// <returns>The new backup version</returns>
        Task<BackupData> CreateBackupVersionAsync(BackupData parentBackup, ECUDevice ecu, string versionNotes = "", bool includeEEPROM = true, bool includeMicrocontrollerCode = true, bool includeParameters = true);

        /// <summary>
        /// Restores a backup to an ECU
        /// </summary>
        /// <param name="backup">The backup to restore</param>
        /// <param name="ecu">The ECU to restore to</param>
        /// <param name="restoreEEPROM">Whether to restore EEPROM data</param>
        /// <param name="restoreMicrocontrollerCode">Whether to restore microcontroller code</param>
        /// <param name="restoreParameters">Whether to restore parameters</param>
        /// <returns>True if restoration is successful, false otherwise</returns>
        Task<bool> RestoreBackupAsync(BackupData backup, ECUDevice ecu, bool restoreEEPROM = true, bool restoreMicrocontrollerCode = true, bool restoreParameters = true);

        /// <summary>
        /// Gets a list of all available backups
        /// </summary>
        /// <returns>List of available backups</returns>
        Task<List<BackupData>> GetAllBackupsAsync();

        /// <summary>
        /// Gets backups for a specific ECU
        /// </summary>
        /// <param name="ecuId">The ID of the ECU</param>
        /// <returns>List of backups for the specified ECU</returns>
        Task<List<BackupData>> GetBackupsForECUAsync(string ecuId);

        /// <summary>
        /// Gets all versions of a backup
        /// </summary>
        /// <param name="backupId">The ID of the backup</param>
        /// <returns>List of all versions of the backup</returns>
        Task<List<BackupData>> GetBackupVersionsAsync(string backupId);

        /// <summary>
        /// Loads a backup from a file
        /// </summary>
        /// <param name="filePath">The path to the backup file</param>
        /// <returns>The loaded backup data</returns>
        Task<BackupData> LoadBackupFromFileAsync(string filePath);

        /// <summary>
        /// Saves a backup to a file
        /// </summary>
        /// <param name="backup">The backup to save</param>
        /// <param name="filePath">The path to save the backup to</param>
        /// <returns>True if save is successful, false otherwise</returns>
        Task<bool> SaveBackupToFileAsync(BackupData backup, string filePath);

        /// <summary>
        /// Adds a tag to a backup
        /// </summary>
        /// <param name="backup">The backup to tag</param>
        /// <param name="tag">The tag to add</param>
        /// <returns>True if the tag was added successfully, false otherwise</returns>
        Task<bool> AddTagToBackupAsync(BackupData backup, string tag);

        /// <summary>
        /// Deletes a backup
        /// </summary>
        /// <param name="backup">The backup to delete</param>
        /// <returns>True if deletion is successful, false otherwise</returns>
        Task<bool> DeleteBackupAsync(BackupData backup);

        /// <summary>
        /// Verifies the integrity of a backup
        /// </summary>
        /// <param name="backup">The backup to verify</param>
        /// <returns>True if the backup is valid, false otherwise</returns>
        Task<bool> VerifyBackupIntegrityAsync(BackupData backup);

        /// <summary>
        /// Compares two backups
        /// </summary>
        /// <param name="backup1">The first backup</param>
        /// <param name="backup2">The second backup</param>
        /// <returns>Dictionary of differences between the backups</returns>
        Task<Dictionary<string, object>> CompareBackupsAsync(BackupData backup1, BackupData backup2);

        /// <summary>
        /// Gets backups by category
        /// </summary>
        /// <param name="category">The category to filter by</param>
        /// <returns>List of backups in the specified category</returns>
        Task<List<BackupData>> GetBackupsByCategoryAsync(string category);

        /// <summary>
        /// Gets backups by tag
        /// </summary>
        /// <param name="tag">The tag to filter by</param>
        /// <returns>List of backups with the specified tag</returns>
        Task<List<BackupData>> GetBackupsByTagAsync(string tag);

        /// <summary>
        /// Removes a tag from a backup
        /// </summary>
        /// <param name="backup">The backup to remove the tag from</param>
        /// <param name="tag">The tag to remove</param>
        /// <returns>True if the tag was removed successfully, false otherwise</returns>
        Task<bool> RemoveTagFromBackupAsync(BackupData backup, string tag);

        /// <summary>
        /// Sets the category of a backup
        /// </summary>
        /// <param name="backup">The backup to categorize</param>
        /// <param name="category">The category to set</param>
        /// <returns>True if the category was set successfully, false otherwise</returns>
        Task<bool> SetBackupCategoryAsync(BackupData backup, string category);

        /// <summary>
        /// Gets all unique categories used across backups
        /// </summary>
        /// <returns>List of unique categories</returns>
        Task<List<string>> GetAllCategoriesAsync();

        /// <summary>
        /// Gets predefined categories for backups
        /// </summary>
        /// <returns>List of predefined categories</returns>
        Task<List<string>> GetPredefinedCategoriesAsync();

        /// <summary>
        /// Gets all unique tags used across backups
        /// </summary>
        /// <returns>List of unique tags</returns>
        Task<List<string>> GetAllTagsAsync();

        /// <summary>
        /// Gets the version history of a backup as a tree structure
        /// </summary>
        /// <param name="backupId">The ID of the backup</param>
        /// <returns>The root backup with its version tree</returns>
        Task<BackupVersionTree> GetBackupVersionTreeAsync(string backupId);

        /// <summary>
        /// Gets the latest version of a backup
        /// </summary>
        /// <param name="backupId">The ID of any version of the backup</param>
        /// <returns>The latest version of the backup</returns>
        Task<BackupData> GetLatestBackupVersionAsync(string backupId);

        /// <summary>
        /// Merges changes from one backup version into another
        /// </summary>
        /// <param name="sourceBackupId">The source backup ID</param>
        /// <param name="targetBackupId">The target backup ID</param>
        /// <param name="mergeOptions">Options for the merge operation</param>
        /// <returns>The merged backup data</returns>
        Task<BackupData> MergeBackupVersionsAsync(string sourceBackupId, string targetBackupId, BackupMergeOptions mergeOptions);

        /// <summary>
        /// Backs up an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to backup</param>
        /// <param name="progress">Progress reporter</param>
        /// <param name="description">Optional description for the backup</param>
        /// <param name="category">Optional category for the backup</param>
        /// <param name="tags">Optional tags for the backup</param>
        /// <returns>The created backup data</returns>
        Task<BackupData> BackupECUAsync(ECUDevice ecu, IProgress<int> progress, string description = "", string category = "", List<string> tags = null);

        /// <summary>
        /// Restores an ECU from a backup with progress reporting
        /// </summary>
        /// <param name="backup">The backup to restore from</param>
        /// <param name="ecu">The ECU to restore</param>
        /// <param name="progress">Progress reporter</param>
        /// <param name="restoreEEPROM">Whether to restore EEPROM data</param>
        /// <param name="restoreMicrocontrollerCode">Whether to restore microcontroller code</param>
        /// <param name="restoreParameters">Whether to restore parameters</param>
        /// <returns>True if restoration is successful, false otherwise</returns>
        Task<bool> RestoreECUAsync(BackupData backup, ECUDevice ecu, IProgress<int> progress, bool restoreEEPROM = true, bool restoreMicrocontrollerCode = true, bool restoreParameters = true);

        /// <summary>
        /// Exports a backup to a specified format
        /// </summary>
        /// <param name="backup">The backup to export</param>
        /// <param name="exportPath">The path to export to</param>
        /// <param name="exportFormat">The format to export to</param>
        /// <returns>True if export is successful, false otherwise</returns>
        Task<bool> ExportBackup(BackupData backup, string exportPath, BackupExportFormat exportFormat);

        /// <summary>
        /// Gets all categories used in backups
        /// </summary>
        /// <returns>List of categories</returns>
        Task<List<string>> GetCategoriesAsync();
    }
}
