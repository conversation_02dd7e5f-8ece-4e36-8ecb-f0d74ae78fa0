using System;
using System.Threading.Tasks;
using Moq;
using Moq.Language.Flow;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Microcontroller;
using VolvoFlashWR.Communication.Tests.Helpers;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using Moq.Protected;

namespace VolvoFlashWR.Communication.Tests.Microcontroller
{
    [TestFixture]
    public class EEPROMEmulationTests
    {
        private Mock<ILoggingService> _mockLogger;
        private Mock<IRegisterAccess> _mockRegisterAccess;
        private MC9S12XEP100Helper _helper;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _mockRegisterAccess = new Mock<IRegisterAccess>();
            _helper = new MC9S12XEP100Helper(_mockLogger.Object, _mockRegisterAccess.Object);
        }

        [Test]
        public async Task EnableEEPROMEmulationAsync_SetsControlRegister_ReturnsTrue()
        {
            // Arrange
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL, 0x01))
                .Returns(Task.FromResult(true));

            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL))
                .Returns(Task.FromResult<byte>(0x01)); // EEON bit is set

            // Act
            bool result = await _helper.EnableEEPROMEmulationAsync();

            // Assert
            Assert.That(result, Is.True);
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL, 0x01), Times.Once);
            _mockRegisterAccess.Verify(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL), Times.Once);
        }

        [Test]
        public async Task DisableEEPROMEmulationAsync_ClearsControlRegister_ReturnsTrue()
        {
            // Arrange
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL, 0x00))
                .Returns(Task.FromResult(true));

            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL))
                .Returns(Task.FromResult<byte>(0x00)); // EEON bit is cleared

            // Act
            bool result = await _helper.DisableEEPROMEmulationAsync();

            // Assert
            Assert.That(result, Is.True);
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL, 0x00), Times.Once);
            _mockRegisterAccess.Verify(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL), Times.Once);
        }

        [Test]
        public async Task ReadEmulatedEEPROMAsync_ReadsDataFromDFlash_ReturnsData()
        {
            // Arrange
            uint address = 0x1000;
            int size = 4;
            byte[] expectedData = new byte[] { 0x01, 0x02, 0x03, 0x04 };

            // Set up EEPROM emulation enabled
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL))
                .Returns(Task.FromResult<byte>(0x01)); // EEON bit is set

            // Set up D-Flash read
            uint dFlashAddress = MC9S12XEP100Configuration.MemoryMap.D_FLASH_START + address;
            for (int i = 0; i < size; i++)
            {
                _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(dFlashAddress + (uint)i))
                    .ReturnsAsync(expectedData[i]);
            }

            // Act
            byte[] result = await _helper.ReadEmulatedEEPROMAsync(address, size);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Length, Is.EqualTo(size));
            for (int i = 0; i < size; i++)
            {
                Assert.That(result[i], Is.EqualTo(expectedData[i]));
            }
        }

        [Test]
        public async Task WriteEmulatedEEPROMAsync_WritesDataToDFlash_ReturnsTrue()
        {
            // Arrange
            uint address = 0x1000;
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04 };

            // Set up EEPROM emulation enabled
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL))
                .Returns(Task.FromResult<byte>(0x01)); // EEON bit is set

            // Set up D-Flash write
            uint dFlashAddress = MC9S12XEP100Configuration.MemoryMap.D_FLASH_START + address;
            for (int i = 0; i < data.Length; i++)
            {
                _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(dFlashAddress + (uint)i, data[i]))
                    .ReturnsAsync(true);

                _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(dFlashAddress + (uint)i))
                    .ReturnsAsync(data[i]);
            }

            // Act
            bool result = await _helper.WriteEmulatedEEPROMAsync(address, data);

            // Assert
            Assert.That(result, Is.True);
            for (int i = 0; i < data.Length; i++)
            {
                _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(dFlashAddress + (uint)i, data[i]), Times.Once);
                _mockRegisterAccess.Verify(r => r.ReadRegisterByteAsync(dFlashAddress + (uint)i), Times.Once);
            }
        }

        [Test]
        public async Task EraseEmulatedEEPROMSectorAsync_ErasesDFlashSector_ReturnsTrue()
        {
            // Arrange
            uint address = 0x1000;

            // Set up EEPROM emulation enabled
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL))
                .Returns(Task.FromResult<byte>(0x01)); // EEON bit is set

            // Set up flash command registers
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsAny<uint>(), It.IsAny<byte>()))
                .Returns(Task.FromResult(true));

            // Set up flash status register for operation complete
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_STAT))
                .Returns(Task.FromResult<byte>(0x40)); // CCIF = 1

            // Act
            bool result = await _helper.EraseEmulatedEEPROMSectorAsync(address);

            // Assert
            Assert.That(result, Is.True);

            // Verify flash command register was set correctly
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_CMD, 0x0A), Times.Once);

            // Verify flash status register was set to start the operation
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_STAT, 0x80), Times.Once);
        }

        [Test]
        public async Task ReadEmulatedEEPROMAsync_WithEEPROMEmulationDisabled_EnablesEmulation()
        {
            // Arrange
            uint address = 0x1000;
            int size = 4;
            byte[] expectedData = new byte[] { 0x01, 0x02, 0x03, 0x04 };

            // Set up EEPROM emulation disabled, then enabled
            var sequence = _mockRegisterAccess.SetupSequence(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL));
            sequence.Returns(Task.FromResult<byte>(0x00)); // First call: EEON bit is cleared
            sequence.Returns(Task.FromResult<byte>(0x01)); // Second call: EEON bit is set

            // Set up enable EEPROM emulation
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL, 0x01))
                .Returns(Task.FromResult(true));

            // Set up D-Flash read
            uint dFlashAddress = MC9S12XEP100Configuration.MemoryMap.D_FLASH_START + address;
            for (int i = 0; i < size; i++)
            {
                _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(dFlashAddress + (uint)i))
                    .Returns(Task.FromResult(expectedData[i]));
            }

            // Act
            byte[] result = await _helper.ReadEmulatedEEPROMAsync(address, size);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Length, Is.EqualTo(size));

            // Verify EEPROM emulation was enabled
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.EEPROM_CONTROL, 0x01), Times.Once);
        }
    }
}

