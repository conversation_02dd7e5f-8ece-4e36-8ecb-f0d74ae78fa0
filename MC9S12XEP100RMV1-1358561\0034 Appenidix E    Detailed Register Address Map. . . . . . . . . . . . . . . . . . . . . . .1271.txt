﻿Appendix E Detailed Register Address Map

Appendix E
Detailed Register Address Map
The following tables show the detailed register map of the S12XE-Family.

NOTE
Smaller derivatives within the S12XE-Family feature a subset of the listed
modules. Refer to Appendix D Derivative Differences for more information
about derivative device module subsets.

0x0000–0x0009 Port Integration Module (PIM) Map 1 of 6
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0000 PORTA PA7 PA6 PA5 PA4 PA3 PA2 PA1 PA 0

W
R

0x0001 PORTB PB7 PB6 PB5 PB4 PB3 PB2 PB1 PB0
W
R

0x0002 DDRA DDRA7 DDRA6 DDRA5 DDRA4 DDRA3 DDRA2 DDRA1  DDRA0
W
R

0x0003 DDRB DDRB7 DDRB6 DDRB5 DDRB4 DDRB3 DDRB2 DDRB1 DDRB0
W
R

0x0004 PORTC PC7 PC6 PC5 PC4 PC3 PC2 PC1  PC0
W
R

0x0005 PORTD PD7 PD6 PD5 PD4 PD3 PD2 PD1  PD0
W
R

0x0006 DDRC DDRC7 DDRC6 DDRC5 DDRC4 DDRC3 DDRC2 DDRC1 DDRC0
W
R

0x0007 DDRD DDRD7 DDRD6 DDRD5 DDRD4 DDRD3 DDRD2 DDRD1 DDRD0
W
R PE1 PE0

0x0008 PORTE PE7 PE6 PE5 PE4 PE3 PE2
W
R 0 0

0x0009 DDRE DDRE7 DDRE6 DDRE5 DDRE4 DDRE3 DDRE2
W

0x000A–0x000B Module Mapping Control (S12XMMC) Map 1 of 2
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x000A MMCCTL0 CS3E1 CS2E1 CS1E1 CS0E1 CS3E0 CS2E0 CS1E0 CS0E0

W
R 0 0 0 0 0

0x000B MODE MODC MODB MODA
W

0x000C–0x000D Port Integration Module (PIM) Map 2 of 6
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0
0x000C PUCR PUPKE BKPUE PUPEE PUPDE PUPCE PUPBE PUPAE

W
R 0 0

0x000D RDRIV RDPK RDPE RDPD RDPC RDPB RDPA
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1271



Appendix E Detailed Register Address Map

0x000E–0x000F External Bus Interface (S12XEBI) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0
0x000E EBICTL0 ITHRS HDBE ASIZ4 ASIZ3 ASIZ2 ASIZ1 ASIZ0

W
R 0 0

0x000F EBICTL1 EXSTR12 EXSTR11 EXSTR10 EXSTR02 EXSTR01 EXSTR00
W

0x0010–0x0017 Module Mapping Control (S12XMMC) Map 2 of 2
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0
0x0010 GPAGE GP6 GP5 GP4 GP3 GP2 GP1 GP0

W
R

0x0011 DIRECT DP15 DP14 DP13 DP12 DP11 DP10 DP9 DP8
W
R 0 0 0 0 0 0 0 0

0x0012 Reserved
W
R TGMRAM MGROMO EEEIFRO PGMIFRO

0x0013 MMCCTL1 RAMHM EROMON ROMHM ROMON
W ON N N N
R 0 0 0 0 0 0 0 0

0x0014 Reserved
W
R

0x0015 PPAGE PIX7 PIX6 PIX5 PIX4 PIX3 PIX2 PIX1 PIX0
W
R

0x0016 RPAGE RP7 RP6 RP5 RP4 RP3 RP2 RP1 RP0
W
R

0x0017 EPAGE EP7 EP6 EP5 EP4 EP3 EP2 EP1 EP0
W

0x0018–0x001B Miscellaneous Peripheral
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 0 0 0 0 0 0
0x0018 Reserved

W
R 0 0 0 0 0 0 0 0

0x0019 Reserved
W

0x001A PARTIDH(1) R 1 1 0 0 1 1 0 0
W

0x001B PARTIDL1 R 1 0 0 1 0 0 1 0
W

1. Refer to Part ID assignments in the device description section for a full list of S12XE-FamilyPart ID values.

0x001C–0x001D Port Integration Module (PIM) Map 3 of 6
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x001C ECLKCTL NECLK NCLKX2 DIV16 EDIV4 EDIV3 EDIV2 EDIV1 EDIV0

W
R 0 0 0 0 0 0 0 0

0x001D Reserved
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1272 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x001E–0x001F Port Integration Module (PIM) Map 3 of 6
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 0 0 0 0
0x001E IRQCR IRQE IRQEN

W
R 0 0 0 0 0 0 0 0

0x001F Reserved
W

0x0020–0x0027 Debug Module (S12XDBG) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0
0x0020 DBGC1 ARM XGSBPE BDM DBGBRK COMRV

W TRIG
R TBF EXTF 0 0 0 SSF2 SSF1 SSF0

0x0021 DBGSR
W
R

0x0022 DBGTCR TSOURCE TRANGE TRCMOD TALIGN
W
R 0 0 0 0

0x0023 DBGC2 CDCM ABCM
W
R Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8

0x0024 DBGTBH
W
R Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

0x0025 DBGTBL
W
R 0 CNT

0x0026 DBGCNT
W
R 0 0 0 0

0x0027 DBGSCRX SC3 SC2 SC1 SC0
W
R 0 0 0 0 MC3 MC2 MC1 MC0

0x0027 DBGMFR
W

0x0028 DBGXCTL R 0
(1) NDB TAG BRK RW RWE SRC COMPE

(COMPA/C) W
0x0028 DBGXCTL R

(2) SZE SZ TAG BRK RW RWE SRC COMPE
(COMPB/D) W

R 0
0x0029 DBGXAH Bit 22 21 20 19 18 17 Bit 16

W
R

0x002A DBGXAM Bit 15 14 13 12 11 10 9 Bit 8
W
R

0x002B DBGXAL Bit 7  6 5 4 3 2 1 Bit 0
W
R

0x002C DBGXDH Bit 15 14 13 12 11 10 9 Bit 8
W
R

0x002D DBGXDL Bit 7 6 5 4 3 2 1 Bit 0
W
R

0x002E DBGXDHM Bit 15 14 13 12 11 10 9 Bit 8
W
R

0x002F DBGXDLM Bit 7 6 5 4 3 2 1 Bit 0
W

1. This represents the contents if the Comparator A or C control register is blended into this address
2. This represents the contents if the Comparator B or D control register is blended into this address

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1273



Appendix E Detailed Register Address Map

0x0030–0x0031 Reserved Register Space
R 0 0 0 0 0 0 0 0

0x0030 Reserved
W
R 0 0 0 0 0 0 0 0

0x0031 Reserved
W

0x0032–0x0033 Port Integration Module (PIM) Map 4 of 6
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0032 PORTK PK7 PK6 PK5 PK4 PK3 PK2 PK1 PK0

W
R

0x0033 DDRK DDRK7 DDRK6 DDRK5 DDRK4 DDRK3 DDRK2 DDRK1 DDRK0
W

0x0034–0x003F Clock and Reset Generator (CRG) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0034 SYNR VCOFRQ[1:0] SYNDIV5 SYNDIV4 SYNDIV3 SYNDIV2 SYNDIV1 SYNDIV0

W
R

0x0035 REFDV REFFRQ[1:0] REFDIV5 REFDIV4 REFDIV3 REFDIV2 REFDIV1 REFDIV0
W
R 0 0 0

0x0036 POSTDIV POSTDIV[4:0]]
W
R LOCK SCM

0x0037 CRGFLG RTIF PORF LVRF LOCKIF ILAF SCMIF
W
R 0 0 0 0 0

0x0038 CRGINT RTIE LOCKIE SCMIE
W
R XCLKS 0 0

0x0039 CLKSEL PLLSEL PSTP PLLWAI RTIWAI COPWAI
W
R

0x003A PLLCTL CME PLLON FM1 FM0 FSTWKP PRE PCE SCME
W
R

0x003B RTICTL RTDEC RTR6 RTR5 RTR4 RTR3 RTR2 RTR1 RTR0
W
R 0 0 0

0x003C COPCTL WCOP RSBCK CR2 CR1 CR0
W WRTMASK
R 0 0 0 0 0 0 0 0

0x003D FORBYP
W Reserved For Factory Test
R 0 0 0 0 0 0 0

0x003E CTCTL
W Reserved For Factory Test
R 0 0 0 0 0 0 0 0

0x003F ARMCOP
W Bit 7 6 5 4 3 2 1 Bit 0

MC9S12XE-Family Reference Manual  Rev. 1.25

1274 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x0040–0x007F Enhanced Capture Timer 16-Bit 8-Channels (ECT) Map (Sheet 1 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0040 TIOS IOS7 IOS6 IOS5 IOS4 IOS3 IOS2 IOS1 IOS0

W
R 0 0 0 0 0 0 0 0

0x0041 CFORC
W FOC7 FOC6 FOC5 FOC4 FOC3 FOC2 FOC1 FOC0
R

0x0042 OC7M OC7M7 OC7M6 OC7M5 OC7M4 OC7M3 OC7M2 OC7M1 OC7M0
W
R

0x0043 OC7D OC7D7 OC7D6 OC7D5 OC7D4 OC7D3 OC7D2 OC7D1 OC7D0
W
R

0x0044 TCNT (high) TCNT15 TCNT14 TCNT13 TCNT12 TCNT11 TCNT10 TCNT9 TCNT8
W
R

0x0045 TCNT (low) TCNT7 TCNT6 TCNT5 TCNT4 TCNT3 TCNT2 TCNT1 TCNT0
W
R 0 0 0

0x0046 TSCR1 TEN TSWAI TSFRZ TFFCA PRNT
W
R

0x0047 TTOV TOV7 TOV6 TOV5 TOV4 TOV3 TOV2 TOV1 TOV0
W
R

0x0048 TCTL1 OM7 OL7 OM6 OL6 OM5 OL5 OM4 OL4
W
R

0x0049 TCTL2 OM3 OL3 OM2 OL2 OM1 OL1 OM0 OL0
W
R

0x004A TCTL3 EDG7B EDG7A EDG6B EDG6A EDG5B EDG5A EDG4B EDG4A
W
R

0x004B TCTL4 EDG3B EDG3A EDG2B EDG2A EDG1B EDG1A EDG0B EDG0A
W
R

0x004C TIE C7I C6I C5I C4I C3I C2I C1I C0I
W
R 0 0 0

0x004D TSCR2 TOI TCRE PR2 PR1 PR0
W
R

0x004E TFLG1 C7F C6F C5F C4F C3F C2F C1F C0F
W
R 0 0 0 0 0 0 0

0x004F TFLG2 TOF
W
R

0x0050 TC0 (hi) Bit 15 14 13 12 11 10 9 Bit 8
W
R

0x0051 TC0 (lo) Bit 7 6 5 4 3 2 1 Bit 0
W
R

0x0052 TC1 (hi) Bit 15 14 13 12 11 10 9 Bit 8
W
R

0x0053 TC1 (lo) Bit 7 6 5 4 3 2 1 Bit 0
W
R

0x0054 TC2 (hi) Bit 15 14 13 12 11 10 9 Bit 8
W
R

0x0055 TC2 (lo) Bit 7 6 5 4 3 2 1 Bit 0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1275



Appendix E Detailed Register Address Map

0x0040–0x007F Enhanced Capture Timer 16-Bit 8-Channels (ECT) Map (Sheet 2 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0056 TC3 (hi) Bit 15 14 13 12 11 10 9 Bit 8

W
R

0x0057 TC3 (lo) Bit 7 6 5 4 3 2 1 Bit 0
W
R

0x0058 TC4 (hi) Bit 15 14 13 12 11 10 9 Bit 8
W
R

0x0059 TC4 (lo) Bit 7 6 5 4 3 2 1 Bit 0
W
R

0x005A TC5 (hi) Bit 15 14 13 12 11 10 9 Bit 8
W
R

0x005B TC5 (lo) Bit 7 6 5 4 3 2 1 Bit 0
W
R

0x005C TC6 (hi) Bit 15 14 13 12 11 10 9 Bit 8
W
R

0x005D TC6 (lo) Bit 7 6 5 4 3 2 1 Bit 0
W
R

0x005E TC7 (hi) Bit 15 14 13 12 11 10 9 Bit 8
W
R

0x005F TC7 (lo) Bit 7 6 5 4 3 2 1 Bit 0
W
R 0

0x0060 PACTL PAEN PAMOD PEDGE CLK1 CLK0 PAOVI PAI
W
R 0 0 0 0 0 0

0x0061 PAFLG PAOVF PAIF
W
R PACNT7 PACNT6 PACNT5 PACNT4 PACNT3 PACNT2 PACNT1 PACNT0

0x0062 PACN3 (hi)
W (15) (14) (13) (12) (11) (10) (9) (8)
R

0x0063 PACN2 (lo) PACNT7 PACNT6 PACNT5 PACNT4 PACNT3 PACNT2 PACNT1 PACNT0
W
R PACNT7 PACNT6 PACNT5 PACNT4 PACNT3 PACNT2 PACNT1 PACNT0

0x0064 PACN1 (hi)
W (15) (14) (13) (12) (11) (10) (9) (8)
R

0x0065 PACN0 (lo) PACNT7 PACNT6 PACNT5 PACNT4 PACNT3 PACNT2 PACNT1 PACNT0
W
R 0 0

0x0066 MCCTL MCZI MODMC RDMCL MCEN MCPR1 MCPR0
W ICLAT FLMC
R 0 0 0 POLF3 POLF2 POLF1 POLF0

0x0067 MCFLG MCZF
W
R 0 0 0 0

0x0068 ICPAR PA3EN PA2EN PA1EN PA0EN
W
R

0x0069 DLYCT DLY7 DLY6 DLY5 DLY4 DLY3 DLY2 DLY1 DLY0
W
R

0x006A ICOVW NOVW7 NOVW6 NOVW5 NOVW4 NOVW3 NOVW2 NOVW1 NOVW0
W
R

0x006B ICSYS SH37 SH26 SH15 SH04 TFMOD PACMX BUFEN LATQ
W
R

0x006C OCPD OCPD7 OCPD6 OCPD5 OCPD4 OCPD3 OCPD2 OCPD1 OCPD0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1276 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x0040–0x007F Enhanced Capture Timer 16-Bit 8-Channels (ECT) Map (Sheet 3 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 0 0 0 0 0 0
0x006D TIMTST

W Reserved For Factory Test
R

0x006E PTPSR PTPS7 PTPS6 PTPS5 PTPS4 PTPS3 PTPS2 PTPS1 PTPS0
W
R

0x006F PTMCPSR PTMPS7 PTMPS6 PTMPS5 PTMPS4 PTMPS3 PTMPS2 PTMPS1 PTMPS0
W
R 0 0 0 0 0 0

0x0070 PBCTL PBEN PBOVI
W
R 0 0 0 0 0 0 0

0x0071 PBFLG PBOVF
W
R PA3H7 PA3H6 PA3H5 PA3H4 PA3H3 PA3H2 PA3H1 PA3H0

0x0072 PA3H
W
R PA2H7 PA2H6 PA2H5 PA2H4 PA2H3 PA2H2 PA2H1 PA2H0

0x0073 PA2H
W
R PA1H7 PA1H6 PA1H5 PA1H4 PA1H3 PA1H2 PA1H1 PA1H 0

0x0074 PA1H
W
R PA0H7 PA0H6 PA0H5 PA0H4 PA0H3 PA0H2 PA0H1 PA0H0

0x0075 PA0H
W
R

0x0076 MCCNT (hi) MCCNT15 MCCNT14 MCCNT13 MCCNT12 MCCNT11 MCCNT10 MCCNT9 MCCNT8
W
R

0x0077 MCCNT (lo) MCCNT7 MCCNT6 MCCNT5 MCCNT4 MCCNT3 MCCNT2 MCCNT1 MCCNT0
W
R TC15 TC14 TC13 TC12 TC11 TC10 TC9 TC8

0x0078 TC0H (hi)
W
R TC7 TC6 TC5 TC4 TC3 TC2 TC1 TC0

0x0079 TC0H (lo)
W
R TC15 TC14 TC13 TC12 TC11 TC10 TC9 TC8

0x007A TC1H (hi)
W
R TC7 TC6 TC5 TC4 TC3 TC2 TC1 TC0

0x007B TC1H (lo)
W
R TC15 TC14 TC13 TC12 TC11 TC10 TC9 TC8

0x007C TC2H (hi)
W
R TC7 TC6 TC5 TC4 TC3 TC2 TC1 TC0

0x007D TC2H (lo)
W
R TC15 TC14 TC13 TC12 TC11 TC10 TC9 TC8

0x007E TC3H (hi)
W
R TC7 TC6 TC5 TC4 TC3 TC2 TC1 TC0

0x007F TC3H (lo)
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1277



Appendix E Detailed Register Address Map

0x0080–0x00AF Analog-to-Digital Converter 12-bit 16-Channels (ATD1) Map (Sheet 1 of
3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 0 0
0x0080 ATD1CTL0 WRAP3 WRAP2 WRAP1 WRAP0

W
R ETRIG ETRIG ETRIG ETRIG ETRIG

0x0081 ATD1CTL1 SRES1 SRES0 SMP_DIS
W SEL CH3 CH2 CH1 CH0
R 0

0x0082 ATD1CTL2 AFFC ICLKSTP ETRIGLE ETRIGP ETRIGE ASCIE ACMPIE
W
R

0x0083 ATD1CTL3 DJM S8C S4C S2C S1C FIFO FRZ1 FRZ0
W
R

0x0084 ATD1CTL4 SMP2 SMP1 SMP0 PRS4 PRS3 PRS2 PRS1 PRS0
W
R 0

0x0085 ATD1CTL5 SC SCAN MULT CD CC CB CA
W
R 0 CC3 CC2 CC1 CC0

0x0086 ATD1STAT0 SCF ETORF FIFOR
W
R 0 0 0 0 0 0 0 0

0x0087 Reserved
W
R

0x0088 ATD1CMPEH CMPE15 CMPE14 CMPE13 CMPE12 CMPE11 CMPE10 CMPE9 CMPE8
W
R

0x0089 ATD1CMPEL CMPE7 CMPE6 CMPE5 CMPE4 CMPE3 CMPE2 CMPE1 CMPE0
W
R CCF15 CCF14 CCF13 CCF12 CCF11 CCF10 CCF9 CCF8

0x008A ATD1STAT2H
W
R CCF7 CCF6 CCF5 CCF4 CCF3 CCF2 CCF1 CCF0

0x008B ATD1STATL
W
R

0x008C ATD1DIENH IEN15 IEN14 IEN13 IEN12 IEN11 IEN10 IEN9 IEN8
W
R

0x008D ATD1DIENL IEN7 IEN6 IEN5 IEN4 IEN3 IEN2 IEN1 IEN0
W
R

0x008E ATD1CMPHTH CMPHT15 CMPHT14 CMPHT13 CMPHT12 CMPHT11 CMPHT10 CMPHT9 CMPHT8
W
R

0x008F ATD1CMPHTL CMPHT7 CMPHT6 CMPHT5 CMPHT4 CMPHT3 CMPHT2 CMPHT1 CMPHT0
W
R Bit15 14 13 12 11 10 9 Bit8

0x0090 ATD1DR0H
W
R Bit7 Bit6 0 0 0 0 0 0

0x0091 ATD1DR0L
W
R Bit15 14 13 12 11 10 9 Bit8

0x0092 ATD1DR1H
W
R Bit7 Bit6 0 0 0 0 0 0

0x0093 ATD1DR1L
W
R Bit15 14 13 12 11 10 9 Bit8

0x0094 ATD1DR2H
W
R Bit7 Bit6 0 0 0 0 0 0

0x0095 ATD1DR2L
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1278 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x0080–0x00AF Analog-to-Digital Converter 12-bit 16-Channels (ATD1) Map (Sheet 2 of
3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R Bit15 14 13 12 11 10 9 Bit8
0x0096 ATD1DR3H

W
R Bit7 Bit6 0 0 0 0 0 0

0x0097 ATD1DR3L
W
R Bit15 14 13 12 11 10 9 Bit8

0x0098 ATD1DR4H
W
R Bit7 Bit6 0 0 0 0 0 0

0x0099 ATD1DR4L
W
R Bit15 14 13 12 11 10 9 Bit8

0x009A ATD1DR5H
W
R Bit7 Bit6 0 0 0 0 0 0

0x009B ATD1DR5L
W
R Bit15 14 13 12 11 10 9 Bit8

0x009C ATD1DR6H
W
R Bit7 Bit6 0 0 0 0 0 0

0x009D ATD1DR6L
W
R Bit15 14 13 12 11 10 9 Bit8

0x009E ATD1DR7H
W
R Bit7 Bit6 0 0 0 0 0 0

0x009F ATD1DR7L
W
R Bit15 14 13 12 11 10 9 Bit8

0x00A0 ATD1DR8H
W
R Bit7 Bit6 0 0 0 0 0 0

0x00A1 ATD1DR8L
W
R Bit15 14 13 12 11 10 9 Bit8

0x00A2 ATD1DR9H
W
R Bit7 Bit6 0 0 0 0 0 0

0x00A3 ATD1DR9L
W
R Bit15 14 13 12 11 10 9 Bit8

0x00A4 ATD1DR10H
W
R Bit7 Bit6 0 0 0 0 0 0

0x00A5 ATD1DR10L
W
R Bit15 14 13 12 11 10 9 Bit8

0x00A6 ATD1DR11H
W
R Bit7 Bit6 0 0 0 0 0 0

0x00A7 ATD1DR11L
W
R Bit15 14 13 12 11 10 9 Bit8

0x00A8 ATD1DR12H
W
R Bit7 Bit6 0 0 0 0 0 0

0x00A9 ATD1DR12L
W
R Bit15 14 13 12 11 10 9 Bit8

0x00AA ATD1DR13H
W
R Bit7 Bit6 0 0 0 0 0 0

0x00AB ATD1DR13L
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1279



Appendix E Detailed Register Address Map

0x0080–0x00AF Analog-to-Digital Converter 12-bit 16-Channels (ATD1) Map (Sheet 3 of
3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R Bit15 14 13 12 11 10 9 Bit8
0x00AC ATD1DR14H

W
R Bit7 Bit6 0 0 0 0 0 0

0x00AD ATD1DR14L
W
R Bit15 14 13 12 11 10 9 Bit8

0x00AE ATD1DR15H
W
R Bit7 Bit6 0 0 0 0 0 0

0x00AF ATD1DR15L
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1280 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x00B0–0x00B7 Inter IC Bus (IIC1) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0
0x00B0 IBAD ADR7 ADR6  ADR5 ADR4 ADR3 ADR2 ADR1

W
R

0x00B1 IBFD IBC7 IBC6 IBC5 IBC4 IBC3 IBC2 IBC1 IBC0
W
R 0 0

0x00B2 IBCR IBEN IBIE MS/SL TX/RX TXAK IBSWAI
W RSTA
R TCF IAAS IBB 0 SRW RXAK

0x00B3 IBSR IBAL IBIF
W
R

0x00B4 IBDR D7  D6  D5  D4  D3  D2  D1  D 0
W
R 0 0 0

0x00B5 IBCR2 GCEN ADTYPE ADR10 ADR9 ADR8
W
R 0 0 0 0 0 0 0 0

0x00B6 Reserved
W
R 0 0 0 0 0 0 0 0

0x00B7 Reserved
W

0x00B8–0x00BF Asynchronous Serial Interface (SCI2) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x00B8 SCI2BDH(1) IREN TNP1 TNP0 SBR12 SBR11 SBR10 SBR9 SBR8

W
R

0x00B9 SCI2BDL1 SBR7 SBR6 SBR5 SBR4 SBR3 SBR2 SBR1 SBR0
W
R

0x00BA SCI2CR11 LOOPS SCISWAI RSRC M WAKE ILT PE PT
W

0 0
0x00B8 SCI2ASR1(2) R 0 0

RXEDGIF BERRV BERRIF BKDIF
W

0 0 0 0 0
0x00B9 SCI2ACR12 R

RXEDGIE BERRIE BKDIE
W
R 0 0 0 0 0

0x00BA SCI2ACR22 BERRM1 BERRM0 BKDFE
W
R

0x00BB SCI2CR2 TIE TCIE RIE ILIE TE RE RWU SBK
W
R TDRE TC RDRF IDLE OR NF FE PF

0x00BC SCI2SR1
W
R 0 0 RAF

0x00BD SCI2SR2 AMAP TXPOL RXPOL BRK13 TXDIR
W
R R8 0 0 0 0 0 0

0x00BE SCI2DRH T8
W
R R7 R6 R5 R4 R3 R2 R1 R0

0x00BF SCI2DRL
W T7 T6 T5 T4 T3 T2 T1 T0

1. Those registers are accessible if the AMAP bit in the SCI2SR2 register is set to zero
2. Those registers are accessible if the AMAP bit in the SCI2SR2 register is set to one

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1281



Appendix E Detailed Register Address Map

0x00C0–0x00C7 Asynchronous Serial Interface (SCI3) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

0x00C0 SCI3BDH(1) R
IREN TNP1 TNP0 SBR12 SBR11 SBR10 SBR9 SBR8

W

0x00C1 SCI3BDL1 R
SBR7 SBR6 SBR5 SBR4 SBR3 SBR2 SBR1 SBR0

W

0x00C2 SCI3CR11 R
LOOPS SCISWAI RSRC M WAKE ILT PE PT

W
R 0 0 0 0

0x00C0 SCI3ASR1(2) RXEDGIF BERRV BERRIF BKDIF
W

0 0 0
0x00C1 SCI3ACR12 R 0 0

RXEDGIE BERRIE BKDIE
W

0x00C2 SCI3ACR22 R 0 0 0 0 0
BERRM1 BERRM0 BKDFE

W
R

0x00C3 SCI3CR2 TIE TCIE RIE ILIE TE RE RWU SBK
W
R TDRE TC RDRF IDLE OR NF FE PF

0x00C4 SCI3SR1
W
R 0 0 RAF

0x00C5 SCI3SR2 AMAP TXPOL RXPOL BRK13 TXDIR
W
R R8 0 0 0 0 0 0

0x00C6 SCI3DRH T8
W
R R7 R6 R5 R4 R3 R2 R1 R0

0x00C7 SCI3DRL
W T7 T6 T5 T4 T3 T2 T1 T0

1. Those registers are accessible if the AMAP bit in the SCI3SR2 register is set to zero
2. Those registers are accessible if the AMAP bit in the SCI3SR2 register is set to one

MC9S12XE-Family Reference Manual  Rev. 1.25

1282 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x00C8–0x00CF Asynchronous Serial Interface (SCI0) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x00C8 SCI0BDH(1) IREN TNP1 TNP0 SBR12 SBR11 SBR10 SBR9 SBR8

W

0x00C9 SCI0BDL1 R
SBR7 SBR6 SBR5 SBR4 SBR3 SBR2 SBR1 SBR0

W

0x00CA SCI0CR11 R
LOOPS SCISWAI RSRC M WAKE ILT PE PT

W
R 0 0 0 0

0x00C8 SCI0ASR1(2) RXEDGIF BERRV BERRIF BKDIF
W

0 0 0 0 0
0x00C9 SCI0ACR12 R

RXEDGIE BERRIE BKDIE
W
R 0 0 0 0 0

0x00CA SCI0ACR22 BERRM1 BERRM0 BKDFE
W
R

0x00CB SCI0CR2 TIE TCIE RIE ILIE TE RE RWU SBK
W
R TDRE TC RDRF IDLE OR NF FE PF

0x00CC SCI0SR1
W
R 0 0 RAF

0x00CD SCI0SR2 AMAP TXPOL RXPOL BRK13 TXDIR
W
R R8 0 0 0 0 0 0

0x00CE SCI0DRH T8
W
R R7 R6 R5 R4 R3 R2 R1 R0

0x00CF SCI0DRL
W T7 T6 T5 T4 T3 T2 T1 T0

1. Those registers are accessible if the AMAP bit in the SCI0SR2 register is set to zero
2. Those registers are accessible if the AMAP bit in the SCI0SR2 register is set to one

0x00D0–0x00D7 Asynchronous Serial Interface (SCI1) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x00D0 SCI1BDH(1) IREN TNP1 TNP0 SBR12 SBR11 SBR10 SBR9 SBR8

W

0x00D1 SCI1BDL1 R
SBR7 SBR6 SBR5 SBR4 SBR3 SBR2 SBR1 SBR0

W

0x00D2 SCI1CR11 R
LOOPS SCISWAI RSRC M WAKE ILT PE PT

W
0 0 0 0

0x00D0 SCI1ASR1(2) R
RXEDGIF BERRV BERRIF BKDIF

W
R 0 0 0 0 0

0x00D1 SCI1ACR12 RXEDGIE BERRIE BKDIE
W
R 0 0 0 0 0

0x00D2 SCI1ACR22 BERRM1 BERRM0 BKDFE
W
R

0x00D3 SCI1CR2 TIE TCIE RIE ILIE TE RE RWU SBK
W
R TDRE TC RDRF IDLE OR NF FE PF

0x00D4 SCI1SR1
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1283



Appendix E Detailed Register Address Map

0x00D0–0x00D7 Asynchronous Serial Interface (SCI1) Map (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 RAF
0x00D5 SCI1SR2 AMAP TXPOL RXPOL BRK13 TXDIR

W
R R8 0 0 0 0 0 0

0x00D6 SCI1DRH T8
W
R R7 R6 R5 R4 R3 R2 R1 R0

0x00D7 SCI1DRL
W T7 T6 T5 T4 T3 T2 T1 T0

1. Those registers are accessible if the AMAP bit in the SCI1SR2 register is set to zero
2. Those registers are accessible if the AMAP bit in the SCI1SR2 register is set to one

0x00D8–0x00DF Serial Peripheral Interface (SPI0) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x00D8 SPI0CR1 SPIE SPE SPTIE MSTR CPOL CPHA SSOE LSBFE

W
R 0 0 0

0x00D9 SPI0CR2 XFRW MODFEN BIDIROE SPISWAI SPC0
W
R 0 0

0x00DA SPI0BR SPPR2 SPPR1 SPPR0 SPR2 SPR1 SPR0
W
R SPIF 0 SPTEF MODF 0 0 0 0

0x00DB SPI0SR
W
R R15 R14 R13 R12 R11 R10 R9 R8

0x00DC SPI0DRH
W T15 T14 T13 T12 T11 T10 T9 T8
R R7 R6 R5 R4 R3 R2 R1 R0

0x00DD SPI0DRL
W T7 T6 T5 T4 T3 T2 T1 T0
R 0 0 0 0 0 0 0 0

0x00DE Reserved
W
R 0 0 0 0 0 0 0 0

0x00DF Reserved
W

0x00E0–0x00E7 Inter IC Bus (IIC0) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0
0x00E0 IBAD ADR7 ADR6  ADR5 ADR4 ADR3 ADR2 ADR1

W
R

0x00E1 IBFD IBC7 IBC6 IBC5 IBC4 IBC3 IBC2 IBC1 IBC0
W
R 0 0

0x00E2 IBCR IBEN IBIE MS/SL TX/RX TXAK IBSWAI
W RSTA
R TCF IAAS IBB 0 SRW RXAK

0x00E3 IBSR IBAL IBIF
W
R

0x00E4 IBDR D7  D6  D5  D4  D3  D2  D1  D 0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1284 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x00E0–0x00E7 Inter IC Bus (IIC0) Map (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 0
0x00E5 IBCR2 GCEN ADTYPE ADR10 ADR9 ADR8

W
R 0 0 0 0 0 0 0 0

0x00E6 Reserved
W
R 0 0 0 0 0 0 0 0

0x00E7 Reserved
W

0x00E8–0x00EF Reserved
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 0 0 0 0 0 0
0x00E8 Reserved

W
R 0 0 0 0 0 0 0 0

0x00E9 Reserved
W
R 0 0 0 0 0 0 0 0

0x00EA Reserved
W
R 0 0 0 0 0 0 0 0

0x00EB Reserved
W
R 0 0 0 0 0 0 0 0

0x00EC Reserved
W
R 0 0 0 0 0 0 0 0

0x00ED Reserved
W
R 0 0 0 0 0 0 0 0

0x00EE Reserved
W
R 0 0 0 0 0 0 0 0

0x00EF Reserved
W

0x00F0–0x00F7 Serial Peripheral Interface (SPI1) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x00F0 SPI1CR1 SPIE SPE SPTIE MSTR CPOL CPHA SSOE LSBFE

W
R 0 0 0

0x00F1 SPI1CR2 XFRW MODFEN BIDIROE SPISWAI SPC0
W
R 0 0

0x00F2 SPI1BR SPPR2 SPPR1 SPPR0 SPR2 SPR1 SPR0
W
R SPIF 0 SPTEF MODF 0 0 0 0

0x00F3 SPI1SR
W
R R15 R14 R13 R12 R11 R10 R9 R8

0x00F4 SPI1DRH
W T15 T14 T13 T12 T11 T10 T9 T8
R R7 R6 R5 R4 R3 R2 R1 R0

0x00F5 SPI1DRL
W T7 T6 T5 T4 T3 T2 T1 T0
R 0 0 0 0 0 0 0 0

0x00F6 Reserved
W
R 0 0 0 0 0 0 0 0

0x00F7 Reserved
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1285



Appendix E Detailed Register Address Map

0x00F8–0x00FF Serial Peripheral Interface (SPI2) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x00F8 SPI2CR1 SPIE SPE SPTIE MSTR CPOL CPHA SSOE LSBFE

W
R 0 0 0

0x00F9 SPI2CR2 XFRW MODFEN BIDIROE SPISWAI SPC0
W
R 0 0

0x00FA SPI2BR SPPR2 SPPR1 SPPR0 SPR2 SPR1 SPR0
W
R SPIF 0 SPTEF MODF 0 0 0 0

0x00FB SPI2SR
W
R R15 R14 R13 R12 R11 R10 R9 R8

0x00FC SPI2DRH
W T15 T14 T13 T12 T11 T10 T9 T8
R R7 R6 R5 R4 R3 R2 R1 R0

0x00FD SPI2DRL
W T7 T6 T5 T4 T3 T2 T1 T0
R 0 0 0 0 0 0 0 0

0x00FE Reserved
W
R 0 0 0 0 0 0 0 0

0x00FF Reserved
W

0x0100–0x0113 NVM Control Register (FTM) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R FDIVLD
0x0100 FCLKDIV FDIV6 FDIV5 FDIV4 FDIV3 FDIV2 FDIV1 FDIV0

W
R KEYEN1 KEYEN0 RNV5 RNV4 RNV3 RNV2 SEC1 SEC0

0x0101 FSEC
W
R 0 0 0 0 0

0x0102 FCCOBIX CCOBIX2 CCOBIX1 CCOBIX0
W
R 0 0 0 0 0

0x0103 FECCRIX ECCRIX2 ECCRIX1 ECCRIX0
W
R 0 0 0 0

0x0104 FCNFG CCIE IGNSF FDFD FSFD
W
R

0x0105 FERCNFG ERSERIE PGMERIE EACCEIE EPVIOLIE ERSVIE1 ERSVIE0 DFDIE SFDIE
W
R 0 MGBUSY RSVD MGSTAT1 MGSTAT0

0x0106 FSTAT CCIF ACCERR FPVIOL
W
R 0

0x0107 FERSTAT ERSERIF PGMERIF EPVIOLIF ERSVIF1 ERSVIF0 DFDIF SFDIF
W
R RNV6

0x0108 FPROT FPOPEN FPHDIS FPHS1 FPHS0 FPLDIS FPLS1 FPLS0
W
R RNV6 RNV5 RNV4

0x0109 EPROT EPOPEN EPDIS EPS2 EPS1 EPS0
W
R

0x010A FCCOBHI CCOB15 CCOB14 CCOB13 CCOB12 CCOB11 CCOB10 CCOB9 CCOB8
W
R

0x010B FCCOBLO CCOB7 CCOB6 CCOB5 CCOB4 CCOB3 CCOB2 CCOB1 CCOB0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1286 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x0100–0x0113 NVM Control Register (FTM) Map (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R ETAG15 ETAG14 ETAG13 ETAG12 ETAG11 ETAG10 ETAG9 ETAG8
0x010C ETAGHI

W
R ETAG7 ETAG6 ETAG5 ETAG4 ETAG3 ETAG2 ETAG1 ETAG0

0x010D ETAGLO
W
R ECCR15 ECCR14 ECCR13 ECCR12 ECCR11 ECCR10 ECCR9 ECCR8

0x010E FECCRHI
W
R ECCR7 ECCR6 ECCR5 ECCR4 ECCR3 ECCR2 ECCR1 ECCR0

0x010F FECCRLO
W
R NV7 NV6 NV5 NV4 NV3 NV2 NV1 NV0

0x0110 FOPT
W
R 0 0 0 0 0 0 0 0

0x0111 Reserved
W
R 0 0 0 0 0 0 0 0

0x0112 Reserved
W
R 0 0 0 0 0 0 0 0

0x0113 Reserved
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1287



Appendix E Detailed Register Address Map

0x0114–0x011F Memory Protection Unit (MPU) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R WPF NEXF 0 0 0 0 SVSF
0x0114 MPUFLG AEF

W
R 0 ADDR[22:16]

0x0115 MPUASTAT0
W
R ADDR[15:8]

0x0116 MPUASTAT1
W
R ADDR[7:0]

0x0117 MPUASTAT2
W
R 0 0 0 0 0 0 0 0

0x0118 Reserved
W
R 0 0 0 0

0x0119 MPUSEL SVSEN SEL[2:0]
W

0x011A MPUDESC0(1) R
MSTR0 MSTR1 MSTR2 MSTR3 LOW_ADDR[22:19]

W

0x011B MPUDESC11 R
LOW_ADDR[18:11]

W

0x011C MPUDESC21 R
LOW_ADDR[10:3]

W
0

0x011D MPUDESC31 R 0
WP NEX HIGH_ADDR[22:19]

W

0x011E MPUDESC41 R
HIGH_ADDR[18:11]

W

0x011F MPUDESC51 R
HIGH_ADDR[10:3]

W
1. The module addresses 0x03C6−0x03CB represent a window in the register map through which different descriptor registers

are visible.

0x0120–0x012F Interrupt Module (S12XINT) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 0 0 0 0 0 0
0x0120 Reserved

W
R

0x0121 IVBR IVB_ADDR[7:0]
W
R 0 0 0 0 0 0 0 0

0x0122 Reserved
W
R 0 0 0 0 0 0 0 0

0x0123 Reserved
W
R 0 0 0 0 0 0 0 0

0x0124 Reserved
W
R 0 0 0 0 0 0 0 0

0x0125 Reserved
W
R 0 0 0 0 0

0x0126 INT_XGPRIO XILVL[2:0]
W
R 0 0 0 0

0x0127 INT_CFADDR INT_CFADDR[7:4]
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1288 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x0120–0x012F Interrupt Module (S12XINT) Map (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 0 0
0x0128 INT_CFDATA0 RQST PRIOLVL[2:0]

W
R 0 0 0 0

0x0129 INT_CFDATA1 RQST PRIOLVL[2:0]
W
R 0 0 0 0

0x012A INT_CFDATA2 RQST PRIOLVL[2:0]
W
R 0 0 0 0

0x012B INT_CFDATA3 RQST PRIOLVL[2:0]
W
R 0 0 0 0

0x012C INT_CFDATA4 RQST PRIOLVL[2:0]
W
R 0 0 0 0

0x012D INT_CFDATA5 RQST PRIOLVL[2:0]
W
R 0 0 0 0

0x012E INT_CFDATA6 RQST PRIOLVL[2:0]
W
R 0 0 0 0

0x012F INT_CFDATA7 RQST PRIOLVL[2:0]
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1289



Appendix E Detailed Register Address Map

0x00130–0x0137 Asynchronous Serial Interface (SCI4) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

0x0130 SCI4BDH(1) R
IREN TNP1 TNP0 SBR12 SBR11 SBR10 SBR9 SBR8

W
R

0x0131 SCI4BDL1 SBR7 SBR6 SBR5 SBR4 SBR3 SBR2 SBR1 SBR0
W
R

0x0132 SCI4CR11 LOOPS SCISWAI RSRC M WAKE ILT PE PT
W
R 0 0 0 0

0x0130 SCI4ASR1(2) RXEDGIF BERRV BERRIF BKDIF
W
R 0 0 0 0 0

0x0131 SCI4ACR12 RXEDGIE BERRIE BKDIE
W

0 0 0 0 0
0x0132 SCI4ACR22 R

BERRM1 BERRM0 BKDFE
W
R

0x0133 SCI4CR2 TIE TCIE RIE ILIE TE RE RWU SBK
W
R TDRE TC RDRF IDLE OR NF FE PF

0x0134 SCI4SR1
W
R 0 0 RAF

0x0135 SCI4SR2 AMAP TXPOL RXPOL BRK13 TXDIR
W
R R8 0 0 0 0 0 0

0x0136 SCI4DRH T8
W
R R7 R6 R5 R4 R3 R2 R1 R0

0x0137 SCI4DRL
W T7 T6 T5 T4 T3 T2 T1 T0

1. Those registers are accessible if the AMAP bit in the SCI4SR2 register is set to zero
2. Those registers are accessible if the AMAP bit in the SCI4SR2 register is set to one

0x0138–0x013F Asynchronous Serial Interface (SCI5) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

0x0138 SCI5BDH(1) R
IREN TNP1 TNP0 SBR12 SBR11 SBR10 SBR9 SBR8

W
R

0x0139 SCI5BDL1 SBR7 SBR6 SBR5 SBR4 SBR3 SBR2 SBR1 SBR0
W
R

0x013A SCI5CR11 LOOPS SCISWAI RSRC M WAKE ILT PE PT
W

0 0 0 0
0x0138 SCI5ASR1(2) R

RXEDGIF BERRV BERRIF BKDIF
W

0 0 0 0 0
0x0139 SCI5ACR12 R

RXEDGIE BERRIE BKDIE
W

0 0 0 0 0
0x013A SCI5ACR22 R

BERRM1 BERRM0 BKDFE
W
R

0x013B SCI5CR2 TIE TCIE RIE ILIE TE RE RWU SBK
W
R TDRE TC RDRF IDLE OR NF FE PF

0x013C SCI5SR1
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1290 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x0138–0x013F Asynchronous Serial Interface (SCI5) Map (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 RAF
0x013D SCI5SR2 AMAP TXPOL RXPOL BRK13 TXDIR

W
R R8 0 0 0 0 0 0

0x013E SCI5DRH T8
W
R R7 R6 R5 R4 R3 R2 R1 R0

0x013F SCI5DRL
W T7 T6 T5 T4 T3 T2 T1 T0

1. Those registers are accessible if the AMAP bit in the SCI5SR2 register is set to zero
2. Those registers are accessible if the AMAP bit in the SCI5SR2 register is set to one

0x0140–0x017F MSCAN (CAN0) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R RXACT SYNCH
0x0140 CAN0CTL0 RXFRM CSWAI TIME WUPE SLPRQ INITRQ

W
R SLPAK INITAK

0x0141 CAN0CTL1 CANE CLKSRC LOOPB LISTEN BORM WUPM
W
R

0x0142 CAN0BTR0 SJW1 SJW0 BRP5 BRP4 BRP3 BRP2 BRP1 BRP0
W
R

0x0143 CAN0BTR1 SAMP TSEG22 TSEG21 TSEG20 TSEG13 TSEG12 TSEG11 TSEG10
W
R RSTAT1 RSTAT0 TSTAT1 TSTAT0

0x0144 CAN0RFLG WUPIF CSCIF OVRIF RXF
W
R

0x0145 CAN0RIER WUPIE CSCIE RSTATE1 RSTATE0 TSTATE1 TSTATE0 OVRIE RXFIE
W
R 0 0 0 0 0

0x0146 CAN0TFLG TXE2 TXE1 TXE0
W
R 0 0 0 0 0

0x0147 CAN0TIER TXEIE2 TXEIE1 TXEIE0
W
R 0 0 0 0 0

0x0148 CAN0TARQ ABTRQ2 ABTRQ1 ABTRQ0
W
R 0 0 0 0 0 ABTAK2 ABTAK1 ABTAK0

0x0149 CAN0TAAK
W
R 0 0 0 0 0

0x014A CAN0TBSEL TX2 TX1 TX0
W
R 0 0 0 IDHIT2 IDHIT1 IDHIT0

0x014B CAN0IDAC IDAM1 IDAM0
W
R 0 0 0 0 0 0 0 0

0x014C Reserved
W
R 0 0 0 0 0 0 0

0x014D CAN0MISC BOHOLD
W
R RXERR7 RXERR6 RXERR5 RXERR4 RXERR3 RXERR2 RXERR1 RXERR0

0x014E CAN0RXERR
W
R TXERR7 TXERR6 TXERR5 TXERR4 TXERR3 TXERR2 TXERR1 TXERR0

0x014F CAN0TXERR
W

0x0150– CAN0IDAR0– R
AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0

0x0153 CAN0IDAR3 W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1291



Appendix E Detailed Register Address Map

0x0140–0x017F MSCAN (CAN0) Map (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
0x0154– CAN0IDMR0– R

AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
0x0157 CAN0IDMR3 W

0x0158– CAN0IDAR4– R
AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0

0x015B CAN0IDAR7 W
0x015C R

CAN0IDMR4–
– AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0

CAN0IDMR7 W
0x015F

R FOREGROUND RECEIVE BUFFER
0x0160–

CAN0RXFG (See Detailed MSCAN Foreground Receive and Transmit Buffer Layout)
0x016F

W
0x0170– R FOREGROUND TRANSMIT BUFFER

CAN0TXFG
0x017F W (See Detailed MSCAN Foreground Receive and Transmit Buffer Layout)

 Detailed MSCAN Foreground Receive and Transmit Buffer Layout
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

Extended ID R ID28 ID27 ID26 ID25 ID24 ID23 ID22 ID21
0xXXX0 Standard ID R ID10 ID9 ID8 ID7 ID6 ID5 ID4 ID3

CANxRIDR0 W
Extended ID R ID20 ID19 ID18 SRR=1 IDE=1 ID17 ID16 ID15

0xXXX1 Standard ID R ID2 ID1 ID0 RTR IDE=0
CANxRIDR1 W
Extended ID R ID14 ID13 ID12 ID11 ID10 ID9 ID8 ID7

0xXXX2 Standard ID R
CANxRIDR2 W
Extended ID R ID6 ID5 ID4 ID3 ID2 ID1 ID0 RTR

0xXXX3 Standard ID R
CANxRIDR3 W

0xXXX4 R DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0
CANxRDSR0–

–
CANxRDSR7 W

0xXXXB
R DLC3 DLC2 DLC1 DLC0

0xXXXC CANRxDLR
W
R

0xXXXD Reserved
W
R TSR15 TSR14 TSR13 TSR12 TSR11 TSR10 TSR9 TSR8

0xXXXE CANxRTSRH
W
R TSR7 TSR6 TSR5 TSR4 TSR3 TSR2 TSR1 TSR0

0xXXXF CANxRTSRL
W

Extended ID R
ID28 ID27 ID26 ID25 ID24 ID23 ID22 ID21

CANxTIDR0 W
0xXX10

Standard ID R
ID10 ID9 ID8 ID7 ID6 ID5 ID4 ID3

W

MC9S12XE-Family Reference Manual  Rev. 1.25

1292 Freescale Semiconductor



Appendix E Detailed Register Address Map

 Detailed MSCAN Foreground Receive and Transmit Buffer Layout (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

Extended ID R
ID20 ID19 ID18 SRR=1 IDE=1 ID17 ID16 ID15

0xXX0x CANxTIDR1 W
XX10 Standard ID R

ID2 ID1 ID0 RTR IDE=0
W

Extended ID R
ID14 ID13 ID12 ID11 ID10 ID9 ID8 ID7

CANxTIDR2 W
0xXX12

Standard ID R
W

Extended ID R
ID6 ID5 ID4 ID3 ID2 ID1 ID0 RTR

CANxTIDR3 W
0xXX13

Standard ID R
W

0xXX14 R
CANxTDSR0–

– DB7
CANxTDSR7 W DB6 DB5 DB4 DB3 DB2 DB1 DB0

0xXX1B
R

0xXX1C CANxTDLR DLC3 DLC2 DLC1 DLC0
W
R

0xXX1D CANxTTBPR PRIO7 PRIO6 PRIO5 PRIO4 PRIO3 PRIO2 PRIO1 PRIO0
W
R TSR15 TSR14 TSR13 TSR12 TSR11 TSR10 TSR9 TSR8

0xXX1E CANxTTSRH
W
R TSR7 TSR6 TSR5 TSR4 TSR3 TSR2 TSR1 TSR0

0xXX1F CANxTTSRL
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1293



Appendix E Detailed Register Address Map

0x0180–0x01BF MSCAN (CAN1) Map (Sheet 1 of 2)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R RXACT SYNCH
0x0180 CAN1CTL0 RXFRM CSWAI TIME WUPE SLPRQ INITRQ

W
R SLPAK INITAK

0x0181 CAN1CTL1 CANE CLKSRC LOOPB LISTEN BORM WUPM
W
R

0x0182 CAN1BTR0 SJW1 SJW0 BRP5 BRP4 BRP3 BRP2 BRP1 BRP0
W
R

0x0183 CAN1BTR1 SAMP TSEG22 TSEG21 TSEG20 TSEG13 TSEG12 TSEG11 TSEG10
W
R RSTAT1 RSTAT0 TSTAT1 TSTAT0

0x0184 CAN1RFLG WUPIF CSCIF OVRIF RXF
W
R

0x0185 CAN1RIER WUPIE CSCIE RSTATE1 RSTATE0 TSTATE1 TSTATE0 OVRIE RXFIE
W
R 0 0 0 0 0

0x0186 CAN1TFLG TXE2 TXE1 TXE0
W
R 0 0 0 0 0

0x0187 CAN1TIER TXEIE2 TXEIE1 TXEIE0
W
R 0 0 0 0 0

0x0188 CAN1TARQ ABTRQ2 ABTRQ1 ABTRQ0
W
R 0 0 0 0 0 ABTAK2 ABTAK1 ABTAK0

0x0189 CAN1TAAK
W
R 0 0 0 0 0

0x018A CAN1TBSEL TX2 TX1 TX0
W
R 0 0 0 IDHIT2 IDHIT1 IDHIT0

0x018B CAN1IDAC IDAM1 IDAM0
W
R 0 0 0 0 0 0 0 0

0x018C Reserved
W
R 0 0 0 0 0 0 0

0x018D CAN1MISC BOHOLD
W
R RXERR7 RXERR6 RXERR5 RXERR4 RXERR3 RXERR2 RXERR1 RXERR0

0x018E CAN1RXERR
W
R TXERR7 TXERR6 TXERR5 TXERR4 TXERR3 TXERR2 TXERR1 TXERR0

0x018F CAN1TXERR
W
R

0x0190 CAN1IDAR0 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0191 CAN1IDAR1 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0192 CAN1IDAR2 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0193 CAN1IDAR3 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0194 CAN1IDMR0 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x0195 CAN1IDMR1 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1294 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x0180–0x01BF MSCAN (CAN1) Map (Sheet 2 of 2)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0196 CAN1IDMR2 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0

W
R

0x0197 CAN1IDMR3 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x0198 CAN1IDAR4 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0199 CAN1IDAR5 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x019A CAN1IDAR6 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x019B CAN1IDAR7 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x019C CAN1IDMR4 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x019D CAN1IDMR5 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x019E CAN1IDMR6 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x019F CAN1IDMR7 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R FOREGROUND RECEIVE BUFFER

0x01A0–
CAN1RXFG (See Detailed MSCAN Foreground Receive and Transmit Buffer Layout)

0x01AF
W

0x01B0– R FOREGROUND TRANSMIT BUFFER
CAN1TXFG

0x01BF W (See Detailed MSCAN Foreground Receive and Transmit Buffer Layout)

0x01C0–0x01FF MSCAN (CAN2) Map (Sheet 1 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R RXACT SYNCH
0x01C0 CAN2CTL0 RXFRM CSWAI TIME WUPE SLPRQ INITRQ

W
R SLPAK INITAK

0x01C1 CAN2CTL1 CANE CLKSRC LOOPB LISTEN BORM WUPM
W
R

0x01C2 CAN2BTR0 SJW1 SJW0 BRP5 BRP4 BRP3 BRP2 BRP1 BRP0
W
R

0x01C3 CAN2BTR1 SAMP TSEG22 TSEG21 TSEG20 TSEG13 TSEG12 TSEG11 TSEG10
W
R RSTAT1 RSTAT0 TSTAT1 TSTAT0

0x01C4 CAN2RFLG WUPIF CSCIF OVRIF RXF
W
R

0x01C5 CAN2RIER WUPIE CSCIE RSTATE1 RSTATE0 TSTATE1 TSTATE0 OVRIE RXFIE
W
R 0 0 0 0 0

0x01C6 CAN2TFLG TXE2 TXE1 TXE0
W
R 0 0 0 0 0

0x01C7 CAN2TIER TXEIE2 TXEIE1 TXEIE0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1295



Appendix E Detailed Register Address Map

0x01C0–0x01FF MSCAN (CAN2) Map (Sheet 2 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 0 0 0
0x01C8 CAN2TARQ ABTRQ2 ABTRQ1 ABTRQ0

W
R 0 0 0 0 0 ABTAK2 ABTAK1 ABTAK0

0x01C9 CAN2TAAK
W
R 0 0 0 0 0

0x01CA CAN2TBSEL TX2 TX1 TX0
W
R 0 0 0 IDHIT2 IDHIT1 IDHIT0

0x01CB CAN2IDAC IDAM1 IDAM0
W
R 0 0 0 0 0 0 0 0

0x01CC Reserved
W
R 0 0 0 0 0 0 0

0x01CD CAN2MISC BOHOLD
W
R RXERR7 RXERR6 RXERR5 RXERR4 RXERR3 RXERR2 RXERR1 RXERR0

0x01CE CAN2RXERR
W
R TXERR7 TXERR6 TXERR5 TXERR4 TXERR3 TXERR2 TXERR1 TXERR0

0x01CF CAN2TXERR
W
R

0x01D0 CAN2IDAR0 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x01D1 CAN2IDAR1 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x01D2 CAN2IDAR2 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x01D3 CAN2IDAR3 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x01D4 CAN2IDMR0 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x01D5 CAN2IDMR1 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x01D6 CAN2IDMR2 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x01D7 CAN2IDMR3 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x01D8 CAN2IDAR4 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x01D9 CAN2IDAR5 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x01DA CAN2IDAR6 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x01DB CAN2IDAR7 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x01DC CAN2IDMR4 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x01DD CAN2IDMR5 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x01DE CAN2IDMR6 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1296 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x01C0–0x01FF MSCAN (CAN2) Map (Sheet 3 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x01DF CAN2IDMR7 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0

W
R FOREGROUND RECEIVE BUFFER

0x01E0–
CAN2RXFG (See Detailed MSCAN Foreground Receive and Transmit Buffer Layout)

0x01EF
W

0x01F0– R FOREGROUND TRANSMIT BUFFER
CAN2TXFG

0x01FF W (See Detailed MSCAN Foreground Receive and Transmit Buffer Layout)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1297



Appendix E Detailed Register Address Map

0x0200–0x023F MSCAN (CAN3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R RXACT SYNCH
0x0200 CAN3CTL0 RXFRM CSWAI TIME WUPE SLPRQ INITRQ

W
R SLPAK INITAK

0x0201 CAN3CTL1 CANE CLKSRC LOOPB LISTEN BORM WUPM
W
R

0x0202 CAN3BTR0 SJW1 SJW0 BRP5 BRP4 BRP3 BRP2 BRP1 BRP0
W
R

0x0203 CAN3BTR1 SAMP TSEG22 TSEG21 TSEG20 TSEG13 TSEG12 TSEG11 TSEG10
W
R RSTAT1 RSTAT0 TSTAT1 TSTAT0

0x0204 CAN3RFLG WUPIF CSCIF OVRIF RXF
W
R

0x0205 CAN3RIER WUPIE CSCIE RSTATE1 RSTATE0 TSTATE1 TSTATE0 OVRIE RXFIE
W
R 0 0 0 0 0

0x0206 CAN3TFLG TXE2 TXE1 TXE0
W
R 0 0 0 0 0

0x0207 CAN3TIER TXEIE2 TXEIE1 TXEIE0
W
R 0 0 0 0 0

0x0208 CAN3TARQ ABTRQ2 ABTRQ1 ABTRQ0
W
R 0 0 0 0 0 ABTAK2 ABTAK1 ABTAK0

0x0209 CAN3TAAK
W
R 0 0 0 0 0

0x020A CAN3TBSEL TX2 TX1 TX0
W
R 0 0 0 IDHIT2 IDHIT1 IDHIT0

0x020B CAN3IDAC IDAM1 IDAM0
W
R 0 0 0 0 0 0 0 0

0x020C Reserved
W
R 0 0 0 0 0 0 0

0x020D CAN3MISC BOHOLD
W
R RXERR7 RXERR6 RXERR5 RXERR4 RXERR3 RXERR2 RXERR1 RXERR0

0x020E CAN3RXERR
W
R TXERR7 TXERR6 TXERR5 TXERR4 TXERR3 TXERR2 TXERR1 TXERR0

0x020F CAN3TXERR
W
R

0x0210 CAN3IDAR0 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0211 CAN3IDAR1 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0212 CAN3IDAR2 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0213 CAN3IDAR3 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0214 CAN3IDMR0 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x0215 CAN3IDMR1 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1298 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x0200–0x023F MSCAN (CAN3) (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0216 CAN3IDMR2 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0

W
R

0x0217 CAN3IDMR3 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x0218 CAN3IDAR4 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0219 CAN3IDAR5 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x021A CAN3IDAR6 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x021B CAN3IDAR7 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x021C CAN3IDMR4 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x021D CAN3IDMR5 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x021E CAN3IDMR6 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x021F CAN3IDMR7 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R FOREGROUND RECEIVE BUFFER

0x0220–
CAN3RXFG (See Detailed MSCAN Foreground Receive and Transmit Buffer Layout)

0x022F
W

0x0230– R FOREGROUND TRANSMIT BUFFER
CAN3TXFG

0x023F W (See Detailed MSCAN Foreground Receive and Transmit Buffer Layout)

0x0240–0x027F Port Integration Module (PIM) Map 5 of 6
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0240 PTT PTT7 PTT6 PTT5 PTT4 PTT3 PTT2 PTT1 PTT0

W
R PTIT7 PTIT6 PTIT5 PTIT4 PTIT3 PTIT2 PTIT1 PTIT0

0x0241 PTIT
W
R

0x0242 DDRT DDRT7 DDRT7 DDRT5 DDRT4 DDRT3 DDRT2 DDRT1 DDRT0
W
R

0x0243 RDRT RDRT7 RDRT6 RDRT5 RDRT4 RDRT3 RDRT2 RDRT1 RDRT0
W
R

0x0244 PERT PERT7 PERT6 PERT5 PERT4 PERT3 PERT2 PERT1 PERT0
W
R

0x0245 PPST PPST7 PPST6 PPST5 PPST4 PPST3 PPST2 PPST1 PPST0
W
R 0 0 0 0 0 0 0 0

0x0246 Reserved
W
R 0 0 0 0 0 0 0 0

0x0247 Reserved
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1299



Appendix E Detailed Register Address Map

0x0240–0x027F Port Integration Module (PIM) Map 5 of 6 (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0248 PTS PTS7 PTS6 PTS5 PTS4 PTS3 PTS2 PTS1 PTS0

W
R PTIS7 PTIS6 PTIS5 PTIS4 PTIS3 PTIS2 PTIS1 PTIS0

0x0249 PTIS
W
R

0x024A DDRS DDRS7 DDRS7 DDRS5 DDRS4 DDRS3 DDRS2 DDRS1 DDRS0
W
R

0x024B RDRS RDRS7 RDRS6 RDRS5 RDRS4 RDRS3 RDRS2 RDRS1 RDRS0
W
R

0x024C PERS PERS7 PERS6 PERS5 PERS4 PERS3 PERS2 PERS1 PERS0
W
R

0x024D PPSS PPSS7 PPSS6 PPSS5 PPSS4 PPSS3 PPSS2 PPSS1 PPSS0
W
R

0x024E WOMS WOMS7 WOMS6 WOMS5 WOMS4 WOMS3 WOMS2 WOMS1 WOMS0
W
R 0 0 0 0 0 0 0 0

0x024F Reserved
W
R

0x0250 PTM PTM7 PTM6 PTM5 PTM4 PTM3 PTM2 PTM1 PTM0
W
R PTIM7 PTIM6 PTIM5 PTIM4 PTIM3 PTIM2 PTIM1 PTIM0

0x0251 PTIM
W
R

0x0252 DDRM DDRM7 DDRM7 DDRM5 DDRM4 DDRM3 DDRM2 DDRM1 DDRM0
W
R

0x0253 RDRM RDRM7 RDRM6 RDRM5 RDRM4 RDRM3 RDRM2 RDRM1 RDRM0
W
R

0x0254 PERM PERM7 PERM6 PERM5 PERM4 PERM3 PERM2 PERM1 PERM0
W
R

0x0255 PPSM PPSM7 PPSM6 PPSM5 PPSM4 PPSM3 PPSM2 PPSM1 PPSM0
W
R

0x0256 WOMM WOMM7 WOMM6 WOMM5 WOMM4 WOMM3 WOMM2 WOMM1 WOMM0
W
R 0

0x0257 MODRR MODRR6 MODRR5 MODRR4 MODRR3 MODRR2 MODRR1 MODRR0
W
R

0x0258 PTP PTP7 PTP6 PTP5 PTP4 PTP3 PTP2 PTP1 PTP0
W
R PTIP7 PTIP6 PTIP5 PTIP4 PTIP3 PTIP2 PTIP1 PTIP0

0x0259 PTIP
W
R

0x025A DDRP DDRP7 DDRP7 DDRP5 DDRP4 DDRP3 DDRP2 DDRP1 DDRP0
W
R

0x025B RDRP RDRP7 RDRP6 RDRP5 RDRP4 RDRP3 RDRP2 RDRP1 RDRP0
W
R

0x025C PERP PERP7 PERP6 PERP5 PERP4 PERP3 PERP2 PERP1 PERP0
W
R

0x025D PPSP PPSP7 PPSP6 PPSP5 PPSP4 PPSP3 PPSP2 PPSP1 PPSS0
W
R

0x025E PIEP PIEP7 PIEP6 PIEP5 PIEP4 PIEP3 PIEP2 PIEP1 PIEP0
W
R

0x025F PIFP PIFP7 PIFP6 PIFP5 PIFP4 PIFP3 PIFP2 PIFP1 PIFP0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1300 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x0240–0x027F Port Integration Module (PIM) Map 5 of 6 (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0260 PTH PTH7 PTH6 PTH5 PTH4 PTH3 PTH2 PTH1 PTH0

W
R PTIH7 PTIH6 PTIH5 PTIH4 PTIH3 PTIH2 PTIH1 PTIH0

0x0261 PTIH
W
R

0x0262 DDRH DDRH7 DDRH7 DDRH5 DDRH4 DDRH3 DDRH2 DDRH1 DDRH0
W
R

0x0263 RDRH RDRH7 RDRH6 RDRH5 RDRH4 RDRH3 RDRH2 RDRH1 RDRH0
W
R

0x0264 PERH PERH7 PERH6 PERH5 PERH4 PERH3 PERH2 PERH1 PERH0
W
R

0x0265 PPSH PPSH7 PPSH6 PPSH5 PPSH4 PPSH3 PPSH2 PPSH1 PPSH0
W
R

0x0266 PIEH PIEH7 PIEH6 PIEH5 PIEH4 PIEH3 PIEH2 PIEH1 PIEH0
W
R

0x0267 PIFH PIFH7 PIFH6 PIFH5 PIFH4 PIFH3 PIFH2 PIFH1 PIFH0
W
R

0x0268 PTJ PTJ7 PTJ6 PTJ5 PTJ4 PTJ3 PTJ2 PTJ1 PTJ0
W
R PTIJ7 PTIJ6 PTIJ5 PTIJ4 PTIJ3 PTIJ2 PTIJ1 PTIJ0

0x0269 PTIJ
W
R

0x026A DDRJ DDRJ7 DDRJ7 DDRJ5 DDRJ4 DDRJ3 DDRJ2 DDRJ1 DDRJ0
W
R

0x026B RDRJ RDRJ7 RDRJ6 RDRJ5 RDRJ4 RDRJ3 RDRJ2 RDRJ1 RDRJ0
W
R

0x026C PERJ PERJ7 PERJ6 PERJ5 PERJ4 PERJ3 PERJ2 PERJ1 PERJ0
W
R

0x026D PPSJ PPSJ7 PPSJ6 PPSJ5 PPSJ4 PPSJ3 PPSJ2 PPSJ1 PPSJ0
W
R

0x026E PIEJ PIEJ7 PIEJ6 PIEJ5 PIEJ4 PIEJ3 PIEJ2 PIEJ1 PIEJ0
W
R

0x026f PIFJ PIFJ7 PIFJ6 PIFJ5 PIFJ4 PIFJ3 PIFJ2 PIFJ1 PIFJ0
W
R PT0AD0 PT0AD0 PT0AD0 PT0AD0 PT0AD0 PT0AD0 PT0AD0 PT0AD0

0x0270 PT0AD0
W 7 6 5 4 3 2 1 0
R PT1AD0 PT1AD0 PT1AD0 PT1AD0 PT1AD0 PT1AD0 PT1AD0 PT1AD0

0x0271 PT1AD0
W 7 6 5 4 3 2 1 0
R DDR0AD0 DDR0AD0 DDR0AD0 DDR0AD0 DDR0AD0 DDR0AD0 DDR0AD0 DDR0AD0

0x0272 DDR0AD0
W 7 6 5 4 3 2 1 0
R DDR1AD0 DDR1AD0 DDR1AD0 DDR1AD0 DDR1AD0 DDR1AD0 DDR1AD0 DDR1AD0

0x0273 DDR1AD0
W 7 6 5 4 3 2 1 0
R RDR0AD0 RDR0AD0 RDR0AD0 RDR0AD0 RDR0AD0 RDR0AD0 RDR0AD0 RDR0AD0

0x0274 RDR0AD0
W 7 6 5 4 3 2 1 0
R RDR1AD0 RDR1AD0 RDR1AD0 RDR1AD0 RDR1AD0 RDR1AD0 RDR1AD0 RDR1AD0

0x0275 RDR1AD0
W 7 6 5 4 3 2 1 0
R PER0AD0 PER0AD0 PER0AD0 PER0AD0 PER0AD0 PER0AD0 PER0AD0 PER0AD0

0x0276 PER0AD0
W 7 6 5 4 3 2 1 0
R PER1AD0 PER1AD0 PER1AD0 PER1AD0 PER1AD0 PER1AD0 PER1AD0 PER1AD0

0x0277 PER1AD0
W 7 6 5 4 3 2 1 0

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1301



Appendix E Detailed Register Address Map

0x0240–0x027F Port Integration Module (PIM) Map 5 of 6 (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R PT0AD1 PT0AD1 PT0AD1 PT0AD1 PT0AD1 PT0AD1 PT0AD1 PT0AD1
0x0278 PT0AD1

W 7 6 5 4 3 2 1 0
R PT1AD1 PT1AD1 PT1AD1 PT1AD1 PT1AD1 PT1AD1 PT1AD1 PT1AD1

0x0279 PT1AD1
W 7 6 5 4 3 2 1 0
R DDR0AD1 DDR0AD1 DDR0AD1 DDR0AD1 DDR0AD1 DDR0AD1 DDR0AD1 DDR0AD1

0x027A DDR0AD1
W 7 6 5 4 3 2 1 0
R DDR1AD1 DDR1AD1 DDR1AD1 DDR1AD1 DDR1AD1 DDR1AD1 DDR1AD1 DDR1AD1

0x027B DDR1AD1
W 7 6 5 4 3 2 1 0
R RDR0AD1 RDR0AD1 RDR0AD1 RDR0AD1 RDR0AD1 RDR0AD1 RDR0AD1 RDR0AD1

0x027C RDR0AD1
W 7 6 5 4 3 2 1 0
R RDR1AD1 RDR1AD1 RDR1AD1 RDR1AD1 RDR1AD1 RDR1AD1 RDR1AD1 RDR1AD1

0x027D RDR1AD1
W 7 6 5 4 3 2 1 0
R PER0AD1 PER0AD1 PER0AD1 PER0AD1 PER0AD1 PER0AD1 PER0AD1 PER0AD1

0x027E PER0AD1
W 7 6 5 4 3 2 1 0
R PER1AD1 PER1AD1 PER1AD1 PER1AD1 PER1AD1 PER1A1D PER1AD1 PER1AD1

0x027F PER1AD1
W 7 6 5 4 3 2 1 0

0x0280–0x02BF MSCAN (CAN4) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R RXACT SYNCH
0x0280 CAN4CTL0 RXFRM CSWAI TIME WUPE SLPRQ INITRQ

W
R SLPAK INITAK

0x0281 CAN4CTL1 CANE CLKSRC LOOPB LISTEN BORM WUPM
W
R

0x0282 CAN4BTR0 SJW1 SJW0 BRP5 BRP4 BRP3 BRP2 BRP1 BRP0
W
R

0x0283 CAN4BTR1 SAMP TSEG22 TSEG21 TSEG20 TSEG13 TSEG12 TSEG11 TSEG10
W
R RSTAT1 RSTAT0 TSTAT1 TSTAT0

0x0284 CAN4RFLG WUPIF CSCIF OVRIF RXF
W
R

0x0285 CAN4RIER WUPIE CSCIE RSTATE1 RSTATE0 TSTATE1 TSTATE0 OVRIE RXFIE
W
R 0 0 0 0 0

0x0286 CAN4TFLG TXE2 TXE1 TXE0
W
R 0 0 0 0 0

0x0287 CAN4TIER TXEIE2 TXEIE1 TXEIE0
W
R 0 0 0 0 0

0x0288 CAN4TARQ ABTRQ2 ABTRQ1 ABTRQ0
W
R 0 0 0 0 0 ABTAK2 ABTAK1 ABTAK0

0x0289 CAN4TAAK
W
R 0 0 0 0 0

0x028A CAN4TBSEL TX2 TX1 TX0
W
R 0 0 0 IDHIT2 IDHIT1 IDHIT0

0x028B CAN4IDAC IDAM1 IDAM0
W
R 0 0 0 0 0 0 0 0

0x028C Reserved
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1302 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x0280–0x02BF MSCAN (CAN4) Map (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 0 0 0 0 0
0x028D CAN4MISC BOHOLD

W
R RXERR7 RXERR6 RXERR5 RXERR4 RXERR3 RXERR2 RXERR1 RXERR0

0x028E CAN4RXERR
W
R TXERR7 TXERR6 TXERR5 TXERR4 TXERR3 TXERR2 TXERR1 TXERR0

0x028F CAN4TXERR
W
R

0x0290 CAN4IDAR0 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0291 CAN4IDAR1 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0292 CAN4IDAR2 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0293 CAN4IDAR3 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0294 CAN4IDMR0 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x0295 CAN4IDMR1 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x0296 CAN4IDMR2 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x0297 CAN4IDMR3 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x0298 CAN4IDAR4 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x0299 CAN4IDAR5 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x029A CAN4IDAR6 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x029B CAN4IDAR7 AC7 AC6 AC5 AC4 AC3 AC2 AC1 AC0
W
R

0x029C CAN4IDMR4 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x029D CAN4IDMR5 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x029E CAN4IDMR6 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R

0x029F CAN4IDMR7 AM7 AM6 AM5 AM4 AM3 AM2 AM1 AM0
W
R FOREGROUND RECEIVE BUFFER

0x02A0–
CAN4RXFG (See Detailed MSCAN Foreground Receive and Transmit Buffer Layout)

0x02AF
W

0x02B0– R FOREGROUND TRANSMIT BUFFER
CAN4TXFG

0x02BF W (See Detailed MSCAN Foreground Receive and Transmit Buffer Layout)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1303



Appendix E Detailed Register Address Map

0x02C0–0x02EF Analog-to-Digital Converter 12-Bit 16-Channel (ATD0) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 0 0
0x02C0 ATD0CTL0 WRAP3 WRAP2 WRAP1 WRAP0

W
R ETRIG ETRIG ETRIG ETRIG ETRIG

0x02C1 ATD0CTL1 SRES1 SRES0 SMP_DIS
W SEL CH3 CH2 CH1 CH0
R 0

0x02C2 ATD0CTL2 AFFC ICLKSTP ETRIGLE ETRIGP ETRIGE ASCIE ACMPIE
W
R

0x02C3 ATD0CTL3 DJM S8C S4C S2C S1C FIFO FRZ1 FRZ0
W
R

0x02C4 ATD0CTL4 SMP2 SMP1 SMP0 PRS4 PRS3 PRS2 PRS1 PRS0
W
R 0

0x02C5 ATD0CTL5 SC SCAN MULT CD CC CB CA
W
R 0 CC3 CC2 CC1 CC0

0x02C6 ATD0STAT0 SCF ETORF FIFOR
W
R 0 0 0 0 0 0 0 0

0x02C7 Reserved
W
R

0x02C8 ATD0CMPEH CMPE15 CMPE14 CMPE13 CMPE12 CMPE11 CMPE10 CMPE9 CMPE8
W
R

0x02C9 ATD0CMPEL CMPE7 CMPE6 CMPE5 CMPE4 CMPE3 CMPE2 CMPE1 CMPE0
W
R CCF15 CCF14 CCF13 CCF12 CCF11 CCF10 CCF9 CCF8

0x02CA ATD0STAT2H
W
R CCF7 CCF6 CCF5 CCF4 CCF3 CCF2 CCF1 CCF0

0x02CB ATD0STAT2L
W
R

0x02CC ATD0DIENH IEN15 IEN14 IEN13 IEN12 IEN11 IEN10 IEN9 IEN8
W
R

0x02CD ATD0DIENL IEN7 IEN6 IEN5 IEN4 IEN3 IEN2 IEN1 IEN0
W
R

0x02CE ATD0CMPHTH CMPHT15 CMPHT14 CMPHT13 CMPHT12 CMPHT11 CMPHT10 CMPHT9 CMPHT8
W
R

0x02CF ATD0CMPHTL CMPHT7 CMPHT6 CMPHT5 CMPHT4 CMPHT3 CMPHT2 CMPHT1 CMPHT0
W
R Bit15 14 13 12 11 10 9 Bit8

0x02D0 ATD0DR0H
W
R Bit7 Bit6 0 0 0 0 0 0

0x02D1 ATD0DR0L
W
R Bit15 14 13 12 11 10 9 Bit8

0x02D2 ATD0DR1H
W
R Bit7 Bit6 0 0 0 0 0 0

0x02D3 ATD0DR1L
W
R Bit15 14 13 12 11 10 9 Bit8

0x02D4 ATD0DR2H
W
R Bit7 Bit6 0 0 0 0 0 0

0x02D5 ATD0DR2L
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1304 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x02C0–0x02EF Analog-to-Digital Converter 12-Bit 16-Channel (ATD0) Map (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R Bit15 14 13 12 11 10 9 Bit8
0x02D6 ATD0DR3H

W
R Bit7 Bit6 0 0 0 0 0 0

0x02D7 ATD0DR3L
W
R Bit15 14 13 12 11 10 9 Bit8

0x02D8 ATD0DR4H
W
R Bit7 Bit6 0 0 0 0 0 0

0x02D9 ATD0DR4L
W
R Bit15 14 13 12 11 10 9 Bit8

0x02DA ATD0DR5H
W
R Bit7 Bit6 0 0 0 0 0 0

0x02DB ATD0DR5L
W
R Bit15 14 13 12 11 10 9 Bit8

0x02DC ATD0DR6H
W
R Bit7 Bit6 0 0 0 0 0 0

0x02DD ATD0DR6L
W
R Bit15 14 13 12 11 10 9 Bit8

0x02DE ATD0DR7H
W
R Bit7 Bit6 0 0 0 0 0 0

0x02DF ATD0DR7L
W
R Bit15 14 13 12 11 10 9 Bit8

0x02E0 ATD0DR8H
W
R Bit7 Bit6 0 0 0 0 0 0

0x02E1 ATD0DR8L
W
R Bit15 14 13 12 11 10 9 Bit8

0x02E2 ATD0DR9H
W
R Bit7 Bit6 0 0 0 0 0 0

0x02E3 ATD0DR9L
W
R Bit15 14 13 12 11 10 9 Bit8

0x02E4 ATD0DR10H
W
R Bit7 Bit6 0 0 0 0 0 0

0x02E5 ATD0DR10L
W
R Bit15 14 13 12 11 10 9 Bit8

0x02E6 ATD0DR11H
W
R Bit7 Bit6 0 0 0 0 0 0

0x02E7 ATD0DR11L
W
R Bit15 14 13 12 11 10 9 Bit8

0x02E8 ATD0DR12H
W
R Bit7 Bit6 0 0 0 0 0 0

0x02E9 ATD0DR12L
W
R Bit15 14 13 12 11 10 9 Bit8

0x02EA ATD0DR13H
W
R Bit7 Bit6 0 0 0 0 0 0

0x02EB ATD0DR13L
W
R Bit15 14 13 12 11 10 9 Bit8

0x02EC ATD0DR14H
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1305



Appendix E Detailed Register Address Map

0x02C0–0x02EF Analog-to-Digital Converter 12-Bit 16-Channel (ATD0) Map (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R Bit7 Bit6 0 0 0 0 0 0
0x02ED ATD0DR14L

W
R Bit15 14 13 12 11 10 9 Bit8

0x02EE ATD0DR15H
W
R Bit7 Bit6 0 0 0 0 0 0

0x02EF ATD0DR15L
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1306 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x02F0–0x02F7 Voltage Regulator (VREG_3V3) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 HTDS
0x02F0 VREGHTCL VSEL VAE HTEN HTIE HTIF

W
R 0 0 0 0 0 LVDS

0x02F1 VREGCTRL LVIE LVIF
W
R 0 0

0x02F2 VREGAPICL APICLK APIFES APIEA APIFE APIE APIF
W
R 0 0

0x02F3 VREGAPITR APITR5 APITR4 APITR3 APITR2 APITR1 APITR0
W
R

0x02F4 VREGAPIRH APIR15 APIR14 APIR13 APIR12 APIR11 APIR10 APIR9 APIR8
W
R

0x02F5 VREGAPIRL APIR7 APIR6 APIR5 APIR4 APIR3 APIR2 APIR1 APIR0
W
R 0 0 0 0 0 0 0 0

0x02F6 Reserved
W
R 0 0 0

0x02F7 VREGHTTR HTOEN HTTR3 HTTR2 HTTR1 HTTR0
W

0x02F8–0x02FF Reserved
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
0x02F8– R 0 0 0 0 0 0 0 0

Reserved
0x02FF W

0x0300–0x0327 Pulse Width Modulator 8-Bit 8-Channel (PWM) Map (Sheet 1 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0300 PWME PWME7 PWME6 PWME5 PWME4 PWME3 PWME2 PWME1 PWME0

W
R

0x0301 PWMPOL PPOL7 PPOL6 PPOL5 PPOL4 PPOL3 PPOL2 PPOL1 PPOL0
W
R

0x0302 PWMCLK PCLK7 PCLK6 PCLK5 PCLK4 PCLK3 PCLK2 PCLK1 PCLK0
W
R 0 0

0x0303 PWMPRCLK PCKB2 PCKB1 PCKB0 PCKA2 PCKA1 PCKA0
W
R

0x0304 PWMCAE CAE7 CAE6 CAE5 CAE4 CAE3 CAE2 CAE1 CAE0
W
R 0 0

0x0305 PWMCTL CON67 CON45 CON23 CON01 PSWAI PFRZ
W

PWMTST R 0 0 0 0 0 0 0 0
0x0306

Test Only W
R 0 0 0 0 0 0 0 0

0x0307 PWMPRSC
W
R

0x0308 PWMSCLA Bit 7  6  5  4  3  2  1  Bit 0
W
R

0x0309 PWMSCLB Bit 7  6  5  4  3  2  1  Bit 0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1307



Appendix E Detailed Register Address Map

0x0300–0x0327 Pulse Width Modulator 8-Bit 8-Channel (PWM) Map (Sheet 2 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 0 0 0 0 0 0
0x030A PWMSCNTA

W
R 0 0 0 0 0 0 0 0

0x030B PWMSCNTB
W
R Bit 7 6 5 4  3  2  1 Bit 0

0x030C PWMCNT0
W 0 0 0 0 0 0 0 0
R Bit 7 6 5 4 3 2 1 Bit 0

0x030D PWMCNT1
W 0 0 0 0 0 0 0 0
R Bit 7 6 5 4 3 2 1 Bit 0

0x030E PWMCNT2
W 0 0 0 0 0 0 0 0
R Bit 7 6 5 4 3 2 1 Bit 0

0x030F PWMCNT3
W 0 0 0 0 0 0 0 0
R Bit 7 6 5 4 3 2 1 Bit 0

0x0310 PWMCNT4
W 0 0 0 0 0 0 0 0
R Bit 7 6 5 4 3 2 1 Bit 0

0x0311 PWMCNT5
W 0 0 0 0 0 0 0 0
R Bit 7 6 5 4 3 2 1 Bit 0

0x0312 PWMCNT6
W 0 0 0 0 0 0 0 0
R Bit 7 6 5 4 3 2 1 Bit 0

0x0313 PWMCNT7
W 0 0 0 0 0 0 0 0
R

0x0314 PWMPER0 Bit 7  6  5  4  3  2  1  Bit 0
W
R

0x0315 PWMPER1 Bit 7  6  5  4  3  2  1  Bit 0
W
R

0x0316 PWMPER2 Bit 7  6  5  4  3  2  1  Bit 0
W
R

0x0317 PWMPER3 Bit 7  6  5  4  3  2  1  Bit 0
W
R

0x0318 PWMPER4 Bit 7  6  5  4  3  2  1  Bit 0
W
R

0x0319 PWMPER5 Bit 7  6  5  4  3  2  1  Bit 0
W
R

0x031A PWMPER6 Bit 7  6  5  4  3  2  1  Bit 0
W
R

0x031B PWMPER7 Bit 7  6  5  4  3  2  1  Bit 0
W
R

0x031C PWMDTY0 Bit 7  6  5  4  3  2  1  Bit 0
W
R

0x031D PWMDTY1 Bit 7  6  5  4  3  2  1  Bit 0
W
R

0x031E PWMDTY2 Bit 7  6  5  4  3  2  1  Bit 0
W
R

0x031F PWMDTY3 Bit 7  6  5  4  3  2  1  Bit 0
W
R

0x0320 PWMDTY4 Bit 7  6  5  4  3  2  1  Bit 0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1308 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x0300–0x0327 Pulse Width Modulator 8-Bit 8-Channel (PWM) Map (Sheet 3 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0321 PWMDTY5 Bit 7  6  5  4  3  2  1  Bit 0

W
R

0x0322 PWMDTY6 Bit 7  6  5  4  3  2  1  Bit 0
W
R

0x0323 PWMDTY7 Bit 7  6  5  4  3  2  1  Bit 0
W
R 0 0 PWM7IN

PWM7
0x0324 PWMSDN W PWMIF PWMIE PWM PWMLVL PWM7INL

ENA
RSTRT

R 0 0 0 0 0 0 0 0
0x0325 Reserved

W
R 0 0 0 0 0 0 0 0

0x0326 Reserved
W
R 0 0 0 0 0 0 0 0

0x0327 Reserved
W

0x0328–0x032F Reserved
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
0x0328– R 0 0 0 0 0 0 0 0

Reserved
0x032F W

0x00330–0x0337 Asynchronous Serial Interface (SCI6) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

0x0330 SCI6BDH(1) R
IREN TNP1 TNP0 SBR12 SBR11 SBR10 SBR9 SBR8

W

0x0331 SCI6BDL1 R
SBR7 SBR6 SBR5 SBR4 SBR3 SBR2 SBR1 SBR0

W

0x0332 SCI6CR11 R
LOOPS SCISWAI RSRC M WAKE ILT PE PT

W

0x0330 SCI6ASR1(2) R 0 0 0 0
RXEDGIF BERRV BERRIF BKDIF

W
0

0x0331 SCI6ACR12 R 0 0 0 0
RXEDGIE BERRIE BKDIE

W

0x0332 SCI6ACR22 R 0 0 0 0 0
BERRM1 BERRM0 BKDFE

W
R

0x0333 SCI6CR2 TIE TCIE RIE ILIE TE RE RWU SBK
W
R TDRE TC RDRF IDLE OR NF FE PF

0x0334 SCI6SR1
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1309



Appendix E Detailed Register Address Map

0x00330–0x0337 Asynchronous Serial Interface (SCI6) Map (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 RAF
0x0335 SCI6SR2 AMAP TXPOL RXPOL BRK13 TXDIR

W
R R8 0 0 0 0 0 0

0x0336 SCI6DRH T8
W
R R7 R6 R5 R4 R3 R2 R1 R0

0x0337 SCI6DRL
W T7 T6 T5 T4 T3 T2 T1 T0

1. Those registers are accessible if the AMAP bit in the SCI6SR2 register is set to zero
2. Those registers are accessible if the AMAP bit in the SCI6SR2 register is set to one

MC9S12XE-Family Reference Manual  Rev. 1.25

1310 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x00338–0x033F Asynchronous Serial Interface (SCI7) Map
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

0x0338 SCI7BDH(1) R
IREN TNP1 TNP0 SBR12 SBR11 SBR10 SBR9 SBR8

W

0x0339 SCI7BDL1 R
SBR7 SBR6 SBR5 SBR4 SBR3 SBR2 SBR1 SBR0

W

0x033A SCI7CR11 R
LOOPS SCISWAI RSRC M WAKE ILT PE PT

W
R 0 0 0 0

0x0338 SCI7ASR1(2) RXEDGIF BERRV BERRIF BKDIF
W

0
0x0339 SCI7ACR12 R 0 0 0 0

RXEDGIE BERRIE BKDIE
W

0x033A SCI7ACR22 R 0 0 0 0 0
BERRM1 BERRM0 BKDFE

W
R

0x033B SCI7CR2 TIE TCIE RIE ILIE TE RE RWU SBK
W
R TDRE TC RDRF IDLE OR NF FE PF

0x033C SCI7SR1
W
R 0 0 RAF

0x033D SCI7SR2 AMAP TXPOL RXPOL BRK13 TXDIR
W
R R8 0 0 0 0 0 0

0x033E SCI7DRH T8
W
R R7 R6 R5 R4 R3 R2 R1 R0

0x033F SCI7DRL
W T7 T6 T5 T4 T3 T2 T1 T0

1. Those registers are accessible if the AMAP bit in the SCI7SR2 register is set to zero
2. Those registers are accessible if the AMAP bit in the SCI7SR2 register is set to one

0x00340–0x0367 – Periodic Interrupt Timer (PIT) Map (Sheet 1 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 0 0 0
0x0340 PITCFLMT PITE PITSWAI PITFRZ

W PFLMT1 PFLMT0
R 0 0 0 0 0 0 0 0

0x0341 PITFLT
W PFLT7 PFLT6 PFLT5 PFLT4 PFLT3 PFLT2 PFLT1 PFLT0
R

0x0342 PITCE PCE7 PCE6 PCE5 PCE4 PCE3 PCE2 PCE1 PCE0
W
R

0x0343 PITMUX PMUX7 PMUX6 PMUX5 PMUX4 PMUX3 PMUX2 PMUX1 PMUX0
W
R

0x0344 PITINTE PINTE7 PINTE6 PINTE5 PINTE4 PINTE3 PINTE2 PINTE1 PINTE0
W
R

0x0345 PITTF PTF7 PTF6 PTF5 PTF4 PTF3 PTF2 PTF1 PTF0
W
R

0x0346 PITMTLD0 PMTLD7 PMTLD6 PMTLD5 PMTLD4 PMTLD3 PMTLD2 PMTLD1 PMTLD0
W
R

0x0347 PITMTLD1 PMTLD7 PMTLD6 PMTLD5 PMTLD4 PMTLD3 PMTLD2 PMTLD1 PMTLD0
W
R

0x0348 PITLD0 (hi) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1311



Appendix E Detailed Register Address Map

0x00340–0x0367 – Periodic Interrupt Timer (PIT) Map (Sheet 2 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0349 PITLD0 (lo) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0

W
R

0x034A PITCNT0 (hi) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8
W
R

0x034B PITCNT0 (lo) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0
W
R

0x034C PITLD1 (hi) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8
W
R

0x034D PITLD1 (lo) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W
R

0x034E PITCNT1 (hi) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8
W
R

0x034F PITCNT1 (lo) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0
W
R

0x0350 PITLD2 (hi) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8
W
R

0x0351 PITLD2 (lo) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W
R

0x0352 PITCNT2 (hi) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8
W
R

0x0353 PITCNT2 (lo) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0
W
R

0x0354 PITLD3 (hi) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8
W
R

0x0355 PITLD3 (lo) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W
R

0x0356 PITCNT3 (hi) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8
W
R

0x0357 PITCNT3 (lo) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0
W
R

0x0358 PITLD4 (hi) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8
W
R

0x0359 PITLD4 (lo) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W
R

0x035A PITCNT4 (hi) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8
W
R

0x035B PITCNT4 (lo) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0
W
R

0x035C PITLD5 (hi) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8
W
R

0x035D PITLD5 (lo) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W
R

0x035E PITCNT5 (hi) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8
W
R

0x035F PITCNT5 (lo) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1312 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x00340–0x0367 – Periodic Interrupt Timer (PIT) Map (Sheet 3 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0360 PITLD6 (hi) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8

W
R

0x0361 PITLD6 (lo) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W
R

0x0362 PITCNT6 (hi) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8
W
R

0x0363 PITCNT6 (lo) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0
W
R

0x0364 PITLD7 (hi) PLD15 PLD14 PLD13 PLD12 PLD11 PLD10 PLD9 PLD8
W
R

0x0365 PITLD7 (lo) PLD7 PLD6 PLD5 PLD4 PLD3 PLD2 PLD1 PLD0
W
R

0x0366 PITCNT7 (hi) PCNT15 PCNT14 PCNT13 PCNT12 PCNT11 PCNT10 PCNT9 PCNT8
W
R

0x0367 PITCNT7 (lo) PCNT7 PCNT6 PCNT5 PCNT4 PCNT3 PCNT2 PCNT1 PCNT0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1313



Appendix E Detailed Register Address Map

0x0368–0x037F Port Integration Module (PIM) Map 6 of 6
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x0368 PTR PTR7 PTR6 PTR5 PTR4 PTR3 PTR2 PTR1 PTR0

W
R PTIR7 PTIR6 PTIR5 PTIR4 PTIR3 PTIR2 PTIR1 PTIR0

0x0369 PTIR
W
R

0x036A DDRR DDRR7 DDRR7 DDRR5 DDRR4 DDRR3 DDRR2 DDRR1 DDRR0
W
R

0x036B RDRR RDRR7 RDRR6 RDRR5 RDRR4 RDRR3 RDRR2 RDRR1 RDRR0
W
R

0x036C PERR PERR7 PERR6 PERR5 PERR4 PERR3 PERR2 PERR1 PERR0
W
R

0x036D PPSR PPSR7 PPSR6 PPSR5 PPSR4 PPSR3 PPSR2 PPSR1 PPSR0
W
R 0 0 0 0 0 0 0 0

0x036E Reserved
W
R

0x036F PTRRR PTRRR7 PTRRR6 PTRRR5 PTRRR4 PTRRR3 PTRRR2 PTRRR1 PTRRR0
W
R

0x0370 PTL PTL7 PTL6 PTL5 PTL4 PTL3 PTL2 PTL1 PTL0
W
R PTIL7 PTIL6 PTIL5 PTIL4 PTIL3 PTIL2 PTIL1 PTIL0

0x0371 PTIL
W
R

0x0372 DDRL DDRL7 DDRL7 DDRL5 DDRL4 DDRL3 DDRL2 DDRL1 DDRL0
W
R

0x0373 RDRL RDRL7 RDRL6 RDRL5 RDRL4 RDRL3 RDRL2 RDRL1 RDRL0
W
R

0x0374 PERL PERL7 PERL6 PERL5 PERL4 PERL3 PERL2 PERL1 PERL0
W
R

0x0375 PPSL PPSL7 PPSL6 PPSL5 PPSL4 PPSL3 PPSL2 PPSL1 PPSL0
W
R

0x0376 WOML WOML7 WOML6 WOML5 WOML4 WOML3 WOML2 WOML1 WOML0
W
R 0 0 0 0

0x0377 PTLRR PTLRR7 PTLRR6 PTLRR5 PTLRR4
W
R

0x0378 PTF PTF7 PTF6 PTF5 PTF4 PTF3 PTF2 PTF1 PTF0
W
R PTIF7 PTIF6 PTIF5 PTIF4 PTIF3 PTIF2 PTIF1 PTIF0

0x0379 PTIF
W
R

0x037A DDRF DDRF7 DDRF7 DDRF5 DDRF4 DDRF3 DDRF2 DDRF1 DDRF0
W
R

0x037B RDRF RDRF7 RDRF6 RDRF5 RDRF4 RDRF3 RDRF2 RDRF1 RDRF0
W
R

0x037C PERF PERF7 PERF6 PERF5 PERF4 PERF3 PERF2 PERF1 PERF0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1314 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x0368–0x037F Port Integration Module (PIM) Map 6 of 6 (continued)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x037D PPSF PPSF7 PPSF6 PPSF5 PPSF4 PPSF3 PPSF2 PPSF1 PPSF0

W
R 0 0 0 0 0 0 0 0

0x037E Reserved
W
R 0 0

0x037F PTFRR PTFRR5 PTFRR4 PTFRR3 PTFRR2 PTFRR1 PTFRR0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1315



Appendix E Detailed Register Address Map

0x0380–0x03BF XGATE Map (Sheet 1 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0 0 0 0 0 0 0
0x0380 XGMCTL W XGS XGIEM

XGEM XGFRZM XGDBGM XGSSM XGFACTM
WEFM

R 0
0x0381 XGMCTL XGE XGFRZ XGDBG XGSS XGFACT XGSWEF XGIE

W
R 0 XGCHID[6:0]

0x0382 XGCHID
W
R 0 0 0 0 0 XGCHPL 0 0

0x0383 XGCHPL
W

0x0384 Reserved

R 0 0 0 0 0 0
0x0385 XGISPSEL XGISPSEL[1:0]

W
R

0x0386 XGVBR XGVBR[15:8]
W
R 0

0x0387 XGVBR XGVBR[7:1]
W
R 0 0 0 0 0 0 0

0x0388 XGIF XGIF_78
W
R

0x0389 XGIF XGIF_77 XGIF_76 XGIF_75 XGIF_74 XGIF_73 XGIF_72 XGIF_71 XGIF_70
W
R

0x038A XGIF XGIF_6F XGIF_6E XGIF_6D XGIF_6C XGIF_6B XGIF_6A XGIF_69 XGIF_68
W
R

0x023B XGIF XGIF_67 XGIF_66 XGIF_65 XGIF_64 XGIF_63 XGIF_62 XGIF_61 XGIF_60
W
R

0x023C XGIF XGIF_5F XGIF_5E XGIF_5D XGIF_5C XGIF_5B XGIF_5A XGIF_59 XGIF_58
W
R

0x038D XGIF XGIF_57 XGIF_56 XGIF_55 XGIF_54 XGIF_53 XGIF_52 XGIF_51 XGIF_50
W
R

0x038E XGIF XGIF_4F XGIF_4E XGIF_4D XGIF_4C XGIF_4B XGIF_4A XGIF_49 XGIF_48
W
R

0x038F XGIF XGIF_47 XGIF_46 XGIF_45 XGIF_44 XGIF_43 XGIF_42 XGIF_41 XGIF_40
W
R

0x0390 XGIF XGIF_3F XGIF_3E XGIF_3D XGIF_3C XGIF_3B XGIF_3A XGIF_39 XGIF_38
W
R

0x0391 XGIF XGIF_37 XGIF_36 XGIF_35 XGIF_34 XGIF_33 XGIF_32 XGIF_31 XGIF_30
W
R

0x0392 XGIF XGIF_2F XGIF_2E XGIF_2D XGIF_2C XGIF_2B XGIF_2A XGIF_29 XGIF_28
W
R

0x0393 XGIF XGIF_27 XGIF_26 XGIF_25 XGIF_24 XGIF_23 XGIF_22 XGIF_21 XGIF_20
W
R

0x0394 XGIF XGIF_1F XGIF_1E XGIF_1D XGIF_1C XGIF_1B XGIF_1A XGIF_19 XGIF_18
W
R

0x0395 XGIF XGIF_17 XGIF_16 XGIF_15 XGIF_14 XGIF_13 XGIF_12 XGIF_11 XGIF_10
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1316 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x0380–0x03BF XGATE Map (Sheet 2 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R 0
0x0396 XGIF XGIF_0F XGIF_0E XGIF_0D XGIF_0C XGIF_0B XGIF_0A XGIF_09

W
R 0 0 0 0 0 0 0 0

0x0397 XGIF
W
R 0 0 0 0 0 0 0 0

0x0398 XGSWTM
W XGSWTM[7:0]
R

0x0399 XGSWT XGSWT[7:0]
W
R 0 0 0 0 0 0 0 0

0x039A XGSEMM
W XGSEMM[7:0]
R

0x039B XGSEM XGSEM[7:0]
W
R 0 0 0 0 0 0 0 0

0x039C Reserved
W
R 0 0 0 0

0x039D XGCCR XGN XGZ XGV XGC
W
R

0x039E XGPC (hi) XGPC[15:8]
W
R

0x039F XGPC (lo) XGPC[7:0]
W
R 0 0 0 0 0 0 0 0

0x03A0 Reserved
W
R 0 0 0 0 0 0 0 0

0x03A1 Reserved
W
R

0x03A2 XGR1 (hi) XGR1[15:8]
W
R

0x03A3 XGR1 (lo) XGR1[7:0]
W
R

0x03A4 XGR2 (hi) XGR2[15:8]
W
R

0x03A5 XGR2 (lo) XGR2[7:0]
W
R

0x03A6 XGR3 (hi) XGR3[15:8]
W
R

0x03A7 XGR3 (lo) XGR3[7:0]
W
R

0x03A8 XGR4 (hi) XGR4[15:8]
W
R

0x03A9 XGR4 (lo) XGR4[7:0]
W
R

0x03AA XGR5 (hi) XGR5[15:8]
W
R

0x03AB XGR5(lo) XGR5[7:0]
W
R

0x03AC XGR6 (hi) XGR6[15:8]
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1317



Appendix E Detailed Register Address Map

0x0380–0x03BF XGATE Map (Sheet 3 of 3)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x03AD XGR6 (lo) XGR6[7:0]

W
R

0x03AE XGR7 (hi) XGR7[15:8]
W
R

0x03AF XGR7 (lo) XGR7[7:0]
W

0x03B0– R 0 0 0 0 0 0 0 0
Reserved

0x03BF W

MC9S12XE-Family Reference Manual  Rev. 1.25

1318 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x03C0–0x03CF Reserved
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
0x03C0 R 0 0 0 0 0 0 0 0

Reserved
-0x03CF W

0x03D0–0x03FF Timer Module (TIM) Map (Sheet 1 of 2)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x03D0 TIOS IOS7 IOS6 IOS5 IOS4 IOS3 IOS2 IOS1 IOS0

W
R 0 0 0 0 0 0 0 0

0x03D1 CFORC
W FOC7 FOC6 FOC5 FOC4 FOC3 FOC2 FOC1 FOC0
R

0x03D2 OC7M OC7M7 OC7M6 OC7M5 OC7M4 OC7M3 OC7M2 OC7M1 OC7M0
W
R

0x03D3 OC7D OC7D7 OC7D6 OC7D5 OC7D4 OC7D3 OC7D2 OC7D1 OC7D0
W
R

0x03D4 TCNTH TCNT15 TCNT14 TCNT13 TCNT12 TCNT11 TCNT10 TCNT9 TCNT8
W
R

0x03D5 TCNTL TCNT7 TCNT6 TCNT5 TCNT4 TCNT3 TCNT2 TCNT1 TCNT0
W
R 0 0 0

0x03D6 TSCR1 TEN TSWAI TSFRZ TFFCA PRNT
W
R

0x03D7 TTOV TOV7 TOV6 TOV5 TOV4 TOV3 TOV2 TOV1 TOV0
W
R

0x03D8 TCTL1 OM7 OL7 OM6 OL6 OM5 OL5 OM4 OL4
W
R

0x03D9 TCTL2 OM3 OL3 OM2 OL2 OM1 OL1 OM0 OL0
W
R

0x03DA TCTL3 EDG7B EDG7A EDG6B EDG6A EDG5B EDG5A EDG4B EDG4A
W
R

0x03DB TCTL4 EDG3B EDG3A EDG2B EDG2A EDG1B EDG1A EDG0B EDG0A
W
R

0x03DC TIE C7I C6I C5I C4I C3I C2I C1I C0I
W
R 0 0 0

0x03DD TSCR2 TOI TCRE PR2 PR1 PR0
W
R

0x03DE TFLG1 C7F C6F C5F C4F C3F C2F C1F C0F
W
R 0 0 0 0 0 0 0

0x03DF TFLG2 TOF
W
R

0x03E0 TC0H Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W
R

0x03E1 TC0L Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W
R

0x03E2 TC1H Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W
R

0x03E3 TC1L Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1319



Appendix E Detailed Register Address Map

0x03D0–0x03FF Timer Module (TIM) Map (Sheet 2 of 2)
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

R
0x03E4 TC2H Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8

W
R

0x03E5 TC2L Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W
R

0x03E6 TC3H Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W
R

0x03E7 TC3L Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W
R

0x03E8 TC4H Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W
R

0x03E9 TC4L Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W
R

0x03EA TC5H Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W
R

0x03EB TC5L Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W
R

0x03EC TC6H Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W
R

0x03ED TC6L Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W
R

0x03EE TC7H Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W
R

0x03EF TC7L Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W
R 0

0x03F0 PACTL PAEN PAMOD PEDGE CLK1 CLK0 PAOVI PAI
W
R 0 0 0 0 0 0

0x03F1 PAFLG PAOVF PAIF
W
R

0x03F2 PACNTH PACNT15 PACNT14 PACNT13 PACNT12 PACNT11 PACNT10 PACNT9 PACNT8
W
R

0x03F3 PACNTL PACNT7 PACNT6 PACNT5 PACNT4 PACNT3 PACNT2 PACNT1 PACNT0
W

0x03F4– R 0 0 0 0 0 0 0 0
Reserved

0x03FB W
R

0x03FC OCPD OCPD7 OCPD6 OCPD5 OCPD4 OCPD3 OCPD2 OCPD1 OCPD0
W
R

0x03FD Reserved
W
R

0x03FE PTPSR PTPSR7 PTPSR6 PTPSR5 PTPSR4 PTPSR3 PTPSR2 PTPSR1 PTPSR0
W
R

0x03FF Reserved
W

MC9S12XE-Family Reference Manual  Rev. 1.25

1320 Freescale Semiconductor



Appendix E Detailed Register Address Map

0x0400–0x07FF Reserved
Address Name Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
0x0400– R 0 0 0 0 0 0 0 0

Reserved
0x07FF W

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1321



Appendix F Ordering Information

Appendix F
Ordering Information
Customers can choose between ordering either the mask-specific partnumber or the generic / mask-
independent partnumber. Ordering the mask-specific partnumber enables the customer to specify which
particular maskset they receive whereas ordering the generic maskset means that the currently preferred
maskset (which may change over time) is shipped. In either case, the marking on the device will always
show the generic / mask-independent partnumber and the mask set number.

NOTE
The  mask identifier suffix and the Tape & Reel suffix are always both omitted from the

partnumber which is actually marked on the device.
For specific partnumbers to order, please contact your local sales office. The below figure illustrates the
structure of a typical mask-specific ordering number for the MC9S12XE-Family devices
S 9 12X EP100 J1 C AG R

Tape & Reel R = Tape & Reel
No R = No Tape & Reel

AA = 80  QFP
Package Option AL = 112 LQFP

AG = 144 LQFP
VL = 208 MAPBGA

Temperature Option C = -40˚C to 85˚C
V = -40˚C to 105˚C
M = -40˚C to 125˚C

Maskset identifier Suffix
First digit references fab
J=TSMC3, F=ATMC,
W=TSMC11, Blank= flexible fab
Second digit refers to mask or firmware revision
Numeric second digit = mask rev. (e.g.1=1M48H)
A=firmware revA, version ID=0xFFFF
B=firmware revB, version ID=0x0004
(This suffix is omitted in generic partnumbers)
Device Title
Controller Family
Main Memory Type:
9 = Flash
3 = ROM (if available)
Status / Partnumber type:
S or SC = Maskset specific partnumber
MC = Generic / mask-independent partnumber
P or PC = prototype status (pre qualification)

Figure F-1. Order Part Number Example

MC9S12XE-Family Reference Manual  Rev. 1.25

1322 Freescale Semiconductor