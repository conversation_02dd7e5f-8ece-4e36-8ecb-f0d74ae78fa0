@echo off
echo === Enabling Phoenix APCI Real Hardware Mode ===

REM Set environment variable to enable Phoenix APCI adapter
set PHOENIX_VOCOM_ENABLED=true
set PHOENIX_DIAG_PATH=C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021

echo Phoenix APCI mode enabled
echo PHOENIX_VOCOM_ENABLED=%PHOENIX_VOCOM_ENABLED%
echo PHOENIX_DIAG_PATH=%PHOENIX_DIAG_PATH%

REM Copy essential Phoenix APCI libraries
echo.
echo === Copying Essential Phoenix APCI Libraries ===

REM Create directories
if not exist "Phoenix_Libraries" mkdir Phoenix_Libraries

REM Copy core APCI libraries (CRITICAL for real hardware)
copy "Libraries\apci.dll" "." >nul 2>&1
if exist "apci.dll" (echo   + apci.dll) else (echo   X apci.dll - MISSING!)
copy "Libraries\apcidb.dll" "." >nul 2>&1
if exist "apcidb.dll" (echo   + apcidb.dll) else (echo   X apcidb.dll - MISSING!)
copy "Libraries\Rpci.dll" "." >nul 2>&1
if exist "Rpci.dll" (echo   + Rpci.dll) else (echo   X Rpci.dll - MISSING!)
copy "Libraries\Pc2.dll" "." >nul 2>&1
if exist "Pc2.dll" (echo   + Pc2.dll) else (echo   X Pc2.dll - MISSING!)

REM Copy Phoenix libraries (ESSENTIAL for Phoenix integration)
copy "Libraries\PhoenixESW.dll" "." >nul 2>&1
if exist "PhoenixESW.dll" (echo   + PhoenixESW.dll) else (echo   X PhoenixESW.dll - MISSING!)
copy "Libraries\PhoenixGeneral.dll" "." >nul 2>&1
if exist "PhoenixGeneral.dll" (echo   + PhoenixGeneral.dll) else (echo   X PhoenixGeneral.dll - MISSING!)
copy "Libraries\PhoenixProducInformation.dll" "." >nul 2>&1
if exist "PhoenixProducInformation.dll" (echo   + PhoenixProducInformation.dll) else (echo   X PhoenixProducInformation.dll - MISSING!)

REM Copy Volvo APCI libraries (REQUIRED for Volvo-specific communication)
copy "Libraries\Volvo.ApciPlus.dll" "." >nul 2>&1
if exist "Volvo.ApciPlus.dll" (echo   + Volvo.ApciPlus.dll) else (echo   X Volvo.ApciPlus.dll - MISSING!)
copy "Libraries\Volvo.ApciPlusData.dll" "." >nul 2>&1
if exist "Volvo.ApciPlusData.dll" (echo   + Volvo.ApciPlusData.dll) else (echo   X Volvo.ApciPlusData.dll - MISSING!)
copy "Libraries\Volvo.ApciPlusTea2Data.dll" "." >nul 2>&1
if exist "Volvo.ApciPlusTea2Data.dll" (echo   + Volvo.ApciPlusTea2Data.dll) else (echo   X Volvo.ApciPlusTea2Data.dll - MISSING!)

REM Copy Vocom driver (CRITICAL for Vocom 1 adapter)
copy "Libraries\WUDFPuma.dll" "." >nul 2>&1
if exist "WUDFPuma.dll" (echo   + WUDFPuma.dll) else (echo   X WUDFPuma.dll - MISSING!)
copy "Libraries\WUDFUpdate_01009.dll" "." >nul 2>&1
if exist "WUDFUpdate_01009.dll" (echo   + WUDFUpdate_01009.dll) else (echo   X WUDFUpdate_01009.dll - MISSING!)
copy "Libraries\WdfCoInstaller01009.dll" "." >nul 2>&1
if exist "WdfCoInstaller01009.dll" (echo   + WdfCoInstaller01009.dll) else (echo   X WdfCoInstaller01009.dll - MISSING!)

REM Copy essential dependencies
copy "Libraries\log4net.dll" "." >nul 2>&1
if exist "log4net.dll" (echo   + log4net.dll) else (echo   X log4net.dll - MISSING!)
copy "Libraries\Newtonsoft.Json.dll" "." >nul 2>&1
if exist "Newtonsoft.Json.dll" (echo   + Newtonsoft.Json.dll) else (echo   X Newtonsoft.Json.dll - MISSING!)

REM Copy Volvo NVS and NAMS libraries (REQUIRED for Volvo communication)
copy "Libraries\Volvo.NVS.Core.dll" "." >nul 2>&1
if exist "Volvo.NVS.Core.dll" (echo   + Volvo.NVS.Core.dll) else (echo   X Volvo.NVS.Core.dll - MISSING!)
copy "Libraries\Volvo.NVS.Logging.dll" "." >nul 2>&1
if exist "Volvo.NVS.Logging.dll" (echo   + Volvo.NVS.Logging.dll) else (echo   X Volvo.NVS.Logging.dll - MISSING!)
copy "Libraries\Volvo.NAMS.AC.Services.Interface.dll" "." >nul 2>&1
if exist "Volvo.NAMS.AC.Services.Interface.dll" (echo   + Volvo.NAMS.AC.Services.Interface.dll) else (echo   X Volvo.NAMS.AC.Services.Interface.dll - MISSING!)

REM Copy Vodia libraries (REQUIRED for communication protocols)
copy "Libraries\Vodia.CommonDomain.Model.dll" "." >nul 2>&1
if exist "Vodia.CommonDomain.Model.dll" (echo   + Vodia.CommonDomain.Model.dll) else (echo   X Vodia.CommonDomain.Model.dll - MISSING!)
copy "Libraries\Vodia.Contracts.Common.dll" "." >nul 2>&1
if exist "Vodia.Contracts.Common.dll" (echo   + Vodia.Contracts.Common.dll) else (echo   X Vodia.Contracts.Common.dll - MISSING!)

echo.
echo === Essential Phoenix APCI libraries copied successfully! ===

REM Launch application in Phoenix mode
echo.
echo === Launching Application in Phoenix APCI Mode ===
start "" "VolvoFlashWR.Launcher.exe"

echo.
echo *** Phoenix APCI mode is now active! ***
echo The application will attempt to connect to real Vocom hardware using Phoenix APCI libraries.
echo.
echo Critical libraries for real hardware communication:
echo   - apci.dll + apcidb.dll (Core APCI)
echo   - WUDFPuma.dll (Vocom driver)
echo   - PhoenixESW.dll (Phoenix integration)
echo   - Volvo.ApciPlus.dll (Volvo APCI)
echo.
pause
