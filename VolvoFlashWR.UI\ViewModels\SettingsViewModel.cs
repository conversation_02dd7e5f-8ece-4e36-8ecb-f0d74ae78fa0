using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.UI.Commands;

namespace VolvoFlashWR.UI.ViewModels
{
    /// <summary>
    /// View model for the settings view
    /// </summary>
    public class SettingsViewModel : INotifyPropertyChanged
    {
        #region Private Fields

        private readonly ILoggingService _loggingService;
        private readonly IAppConfigurationService _configurationService;
        private bool _isBusy;
        private string _statusMessage;

        // General settings
        private string _uiTheme;
        private string _uiLanguage;
        private bool _detailedLogging;

        // Backup settings
        private bool _useCompression;
        private bool _useEncryption;
        private int _maxBackupsToKeep;

        // Communication settings
        private bool _autoConnectVocom;
        private bool _useWiFiFallback;
        private int _connectionTimeout;
        private int _retryAttempts;

        // ECU settings
        private bool _autoScanECUs;
        private string _operatingMode;

        #endregion

        #region Properties

        public bool IsBusy
        {
            get => _isBusy;
            set
            {
                _isBusy = value;
                OnPropertyChanged();
                UpdateCommandStates();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        // General settings
        public string UITheme
        {
            get => _uiTheme;
            set
            {
                if (_uiTheme != value)
                {
                    _uiTheme = value;
                    OnPropertyChanged();
                }
            }
        }

        public string UILanguage
        {
            get => _uiLanguage;
            set
            {
                if (_uiLanguage != value)
                {
                    _uiLanguage = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool DetailedLogging
        {
            get => _detailedLogging;
            set
            {
                if (_detailedLogging != value)
                {
                    _detailedLogging = value;
                    OnPropertyChanged();
                }
            }
        }

        // Backup settings
        public bool UseCompression
        {
            get => _useCompression;
            set
            {
                if (_useCompression != value)
                {
                    _useCompression = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool UseEncryption
        {
            get => _useEncryption;
            set
            {
                if (_useEncryption != value)
                {
                    _useEncryption = value;
                    OnPropertyChanged();
                }
            }
        }

        public int MaxBackupsToKeep
        {
            get => _maxBackupsToKeep;
            set
            {
                if (_maxBackupsToKeep != value)
                {
                    _maxBackupsToKeep = value;
                    OnPropertyChanged();
                }
            }
        }

        // Communication settings
        public bool AutoConnectVocom
        {
            get => _autoConnectVocom;
            set
            {
                if (_autoConnectVocom != value)
                {
                    _autoConnectVocom = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool UseWiFiFallback
        {
            get => _useWiFiFallback;
            set
            {
                if (_useWiFiFallback != value)
                {
                    _useWiFiFallback = value;
                    OnPropertyChanged();
                }
            }
        }

        public int ConnectionTimeout
        {
            get => _connectionTimeout;
            set
            {
                if (_connectionTimeout != value)
                {
                    _connectionTimeout = value;
                    OnPropertyChanged();
                }
            }
        }

        public int RetryAttempts
        {
            get => _retryAttempts;
            set
            {
                if (_retryAttempts != value)
                {
                    _retryAttempts = value;
                    OnPropertyChanged();
                }
            }
        }

        // ECU settings
        public bool AutoScanECUs
        {
            get => _autoScanECUs;
            set
            {
                if (_autoScanECUs != value)
                {
                    _autoScanECUs = value;
                    OnPropertyChanged();
                }
            }
        }

        public string OperatingMode
        {
            get => _operatingMode;
            set
            {
                if (_operatingMode != value)
                {
                    _operatingMode = value;
                    OnPropertyChanged();
                }
            }
        }

        // Available options
        public List<string> AvailableThemes => new List<string> { "Light", "Dark", "System" };
        public List<string> AvailableLanguages => new List<string> { "en-US", "fr-FR", "de-DE", "es-ES", "sv-SE" };
        public List<string> AvailableOperatingModes => new List<string> { "Bench", "Vehicle", "Diagnostic" };

        #endregion

        #region Commands

        public ICommand SaveCommand { get; private set; }
        public ICommand CancelCommand { get; private set; }
        public ICommand ResetToDefaultsCommand { get; private set; }

        #endregion

        #region Constructor

        public SettingsViewModel(ILoggingService loggingService, IAppConfigurationService configurationService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));

            // Initialize default values first to ensure properties are never null
            UITheme = "Light";
            UILanguage = "en-US";
            DetailedLogging = false;
            UseCompression = true;
            UseEncryption = false;
            MaxBackupsToKeep = 10;
            AutoConnectVocom = true;
            UseWiFiFallback = false;
            ConnectionTimeout = 5000;
            RetryAttempts = 3;
            AutoScanECUs = true;
            OperatingMode = "Bench";

            // Initialize commands
            SaveCommand = new RelayCommand(
                _ => SaveSettingsAsync().ConfigureAwait(false),
                _ => CanSaveSettings());

            CancelCommand = new RelayCommand(
                _ => CloseWindow(),
                _ => true);

            ResetToDefaultsCommand = new RelayCommand(
                _ => ResetToDefaultsAsync().ConfigureAwait(false),
                _ => !IsBusy);

            // Load settings
            LoadSettingsAsync().ConfigureAwait(false);
        }

        #endregion

        #region Methods

        private async Task LoadSettingsAsync()
        {
            try
            {
                // Add a delay to make this method truly async
                await Task.Delay(1);

                IsBusy = true;
                StatusMessage = "Loading settings...";

                // Initialize default values first to ensure properties are never null
                UITheme = "Light";
                UILanguage = "en-US";
                DetailedLogging = false;
                UseCompression = true;
                UseEncryption = false;
                MaxBackupsToKeep = 10;
                AutoConnectVocom = true;
                UseWiFiFallback = false;
                ConnectionTimeout = 5000;
                RetryAttempts = 3;
                AutoScanECUs = true;
                OperatingMode = "Bench";

                // Load general settings
                UITheme = _configurationService.GetValue<string>("UI.Theme", "Light");
                UILanguage = _configurationService.GetValue<string>("UI.Language", "en-US");
                DetailedLogging = _configurationService.GetValue<bool>("Logging.DetailedLogging", false);

                // Load backup settings
                UseCompression = _configurationService.GetValue<bool>("Backup.UseCompression", true);
                UseEncryption = _configurationService.GetValue<bool>("Backup.UseEncryption", false);
                MaxBackupsToKeep = _configurationService.GetValue<int>("Backup.MaxBackupsToKeep", 10);

                // Load communication settings
                AutoConnectVocom = _configurationService.GetValue<bool>("Vocom.AutoConnect", true);
                UseWiFiFallback = _configurationService.GetValue<bool>("Vocom.UseWiFiFallback", false);
                ConnectionTimeout = _configurationService.GetValue<int>("Vocom.ConnectionTimeoutMs", 5000);
                RetryAttempts = _configurationService.GetValue<int>("Vocom.RetryAttempts", 3);

                // Load ECU settings
                AutoScanECUs = _configurationService.GetValue<bool>("ECU.AutoScan", true);
                OperatingMode = _configurationService.GetValue<string>("ECU.OperatingMode", "Bench");

                StatusMessage = "Settings loaded successfully";
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error loading settings", "SettingsViewModel", ex);
                StatusMessage = $"Error loading settings: {ex.Message}";
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task SaveSettingsAsync()
        {
            try
            {
                IsBusy = true;
                StatusMessage = "Saving settings...";

                // Save general settings
                await _configurationService.SetValueAsync("UI.Theme", UITheme);
                await _configurationService.SetValueAsync("UI.Language", UILanguage);
                await _configurationService.SetValueAsync("Logging.DetailedLogging", DetailedLogging);

                // Save backup settings
                await _configurationService.SetValueAsync("Backup.UseCompression", UseCompression);
                await _configurationService.SetValueAsync("Backup.UseEncryption", UseEncryption);
                await _configurationService.SetValueAsync("Backup.MaxBackupsToKeep", MaxBackupsToKeep);

                // Save communication settings
                await _configurationService.SetValueAsync("Vocom.AutoConnect", AutoConnectVocom);
                await _configurationService.SetValueAsync("Vocom.UseWiFiFallback", UseWiFiFallback);
                await _configurationService.SetValueAsync("Vocom.ConnectionTimeoutMs", ConnectionTimeout);
                await _configurationService.SetValueAsync("Vocom.RetryAttempts", RetryAttempts);

                // Save ECU settings
                await _configurationService.SetValueAsync("ECU.AutoScan", AutoScanECUs);
                await _configurationService.SetValueAsync("ECU.OperatingMode", OperatingMode);

                // Save configuration to file
                bool saved = await _configurationService.SaveConfigurationAsync();
                if (saved)
                {
                    _loggingService.LogInformation("Settings saved successfully", "SettingsViewModel");
                    StatusMessage = "Settings saved successfully";

                    // Close the window after saving
                    CloseWindow();
                }
                else
                {
                    _loggingService.LogWarning("Failed to save settings", "SettingsViewModel");
                    StatusMessage = "Failed to save settings";
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error saving settings", "SettingsViewModel", ex);
                StatusMessage = $"Error saving settings: {ex.Message}";
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task ResetToDefaultsAsync()
        {
            try
            {
                // Confirm with the user before resetting
                var result = MessageBox.Show(
                    "Are you sure you want to reset all settings to their default values?",
                    "Reset Settings",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                {
                    return;
                }

                IsBusy = true;
                StatusMessage = "Resetting settings to defaults...";

                // Reset the configuration
                bool reset = await _configurationService.ResetToDefaultsAsync();
                if (reset)
                {
                    // Reload settings
                    await LoadSettingsAsync();

                    _loggingService.LogInformation("Settings reset to defaults", "SettingsViewModel");
                    StatusMessage = "Settings reset to defaults";
                }
                else
                {
                    _loggingService.LogWarning("Failed to reset settings", "SettingsViewModel");
                    StatusMessage = "Failed to reset settings";
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error resetting settings", "SettingsViewModel", ex);
                StatusMessage = $"Error resetting settings: {ex.Message}";
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void CloseWindow()
        {
            // Find the window that contains this view model and close it
            foreach (Window window in Application.Current.Windows)
            {
                if (window.DataContext == this)
                {
                    window.DialogResult = true;
                    window.Close();
                    return;
                }
            }
        }

        private bool CanSaveSettings()
        {
            // Add validation logic here if needed
            return !IsBusy;
        }

        private void UpdateCommandStates()
        {
            // Update command can execute states
            (SaveCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (ResetToDefaultsCommand as RelayCommand)?.RaiseCanExecuteChanged();
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}

