using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Tests.Models
{
    [TestFixture]
    public class ConnectionSettingsTests
    {
        [Test]
        public void ConnectionSettings_DefaultValues_AreCorrect()
        {
            // Arrange & Act
            var settings = new ConnectionSettings();

            // Assert
            Assert.That(settings.PreferredConnectionType, Is.EqualTo(VocomConnectionType.USB));
            Assert.That(settings.ConnectionTimeoutMs, Is.EqualTo(5000));
            Assert.That(settings.CommunicationTimeoutMs, Is.EqualTo(10000));
            Assert.That(settings.RetryAttempts, Is.EqualTo(3));
            Assert.That(settings.EnableDetailedLogging, Is.True);
            Assert.That(settings.LogFilePath, Is.EqualTo("Logs"));
            Assert.That(settings.BackupFilePath, Is.EqualTo("Backups"));
        }

        [Test]
        public void ConnectionSettings_CustomValues_AreCorrect()
        {
            // Arrange & Act
            var settings = new ConnectionSettings
            {
                PreferredConnectionType = VocomConnectionType.Bluetooth,
                ConnectionTimeoutMs = 10000,
                CommunicationTimeoutMs = 20000,
                RetryAttempts = 5,
                EnableDetailedLogging = false,
                LogFilePath = "CustomLogs",
                BackupFilePath = "CustomBackups"
            };

            // Assert
            Assert.That(settings.PreferredConnectionType, Is.EqualTo(VocomConnectionType.Bluetooth));
            Assert.That(settings.ConnectionTimeoutMs, Is.EqualTo(10000));
            Assert.That(settings.CommunicationTimeoutMs, Is.EqualTo(20000));
            Assert.That(settings.RetryAttempts, Is.EqualTo(5));
            Assert.That(settings.EnableDetailedLogging, Is.False);
            Assert.That(settings.LogFilePath, Is.EqualTo("CustomLogs"));
            Assert.That(settings.BackupFilePath, Is.EqualTo("CustomBackups"));
        }
    }
}

