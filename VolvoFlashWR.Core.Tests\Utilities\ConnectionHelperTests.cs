using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Utilities;

namespace VolvoFlashWR.Core.Tests.Utilities
{
    [TestFixture]
    public class ConnectionHelperTests
    {
        [Test]
        public void IsProcessRunning_WithNonExistentProcess_ReturnsFalse()
        {
            // Arrange
            string nonExistentProcessName = "ThisProcessDoesNotExist" + Guid.NewGuid().ToString("N");

            // Act
            bool result = ConnectionHelper.IsProcessRunning(nonExistentProcessName);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void IsProcessRunning_WithCurrentProcess_ReturnsTrue()
        {
            // Arrange
            string currentProcessName = Process.GetCurrentProcess().ProcessName;

            // Act
            bool result = ConnectionHelper.IsProcessRunning(currentProcessName);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public void IsPTTApplicationRunning_WithNonExistentProcess_ReturnsFalse()
        {
            // Arrange
            string nonExistentProcessName = "ThisProcessDoesNotExist" + Guid.NewGuid().ToString("N");

            // Act
            bool result = ConnectionHelper.IsPTTApplicationRunning(nonExistentProcessName);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public async Task DisconnectPTTApplicationAsync_WithNonExistentProcess_ReturnsTrue()
        {
            // Arrange
            string nonExistentProcessName = "ThisProcessDoesNotExist" + Guid.NewGuid().ToString("N");

            // Act
            bool result = await ConnectionHelper.DisconnectPTTApplicationAsync(nonExistentProcessName);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public void FindPTTInstallationPath_WithNonExistentProcess_ReturnsNull()
        {
            // Arrange
            string nonExistentProcessName = "ThisProcessDoesNotExist" + Guid.NewGuid().ToString("N");

            // Act
            string result = ConnectionHelper.FindPTTInstallationPath(nonExistentProcessName);

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public async Task ExecuteWithRetryAsync_WhenOperationSucceeds_ReturnsResult()
        {
            // Arrange
            int expectedResult = 42;
            Func<Task<int>> operation = () => Task.FromResult(expectedResult);

            // Act
            int result = await ConnectionHelper.ExecuteWithRetryAsync(operation, 3, 100);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test]
        public void ExecuteWithRetryAsync_WhenOperationFailsAllRetries_ThrowsException()
        {
            // Arrange
            Func<Task<int>> operation = () => throw new InvalidOperationException("Test exception");

            // Act & Assert
            Assert.That(async () => await ConnectionHelper.ExecuteWithRetryAsync(operation, 3, 100),
                Throws.TypeOf<InvalidOperationException>());
        }

        [Test]
        public async Task ExecuteWithRetryAsync_WhenOperationFailsThenSucceeds_ReturnsResult()
        {
            // Arrange
            int expectedResult = 42;
            int attempts = 0;
            Func<Task<int>> operation = () =>
            {
                attempts++;
                if (attempts < 2)
                {
                    throw new InvalidOperationException("Test exception");
                }
                return Task.FromResult(expectedResult);
            };

            // Act
            int result = await ConnectionHelper.ExecuteWithRetryAsync(operation, 3, 100);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
            Assert.That(attempts, Is.EqualTo(2));
        }

        [Test]
        public void KillProcess_WithNonExistentProcess_ReturnsTrue()
        {
            // Arrange
            string nonExistentProcessName = "ThisProcessDoesNotExist" + Guid.NewGuid().ToString("N");

            // Act
            bool result = ConnectionHelper.KillProcess(nonExistentProcessName);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public void GracefullyCloseProcess_WithNonExistentProcess_ReturnsTrue()
        {
            // Arrange
            string nonExistentProcessName = "ThisProcessDoesNotExist" + Guid.NewGuid().ToString("N");

            // Act
            bool result = ConnectionHelper.GracefullyCloseProcess(nonExistentProcessName);

            // Assert
            Assert.That(result, Is.True);
        }
    }
}
