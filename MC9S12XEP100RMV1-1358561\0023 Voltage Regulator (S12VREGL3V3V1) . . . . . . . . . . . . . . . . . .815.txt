﻿Chapter 23
Voltage Regulator (S12VREGL3V3V1)

Table 23-1. Revision History

Revision Sections
Revision Date Description of Changes

Number Affected

V01.02 09 Sep 2005 ********/23-822 - Updates for API external access and LVR flags.

V01.03 23 Sep 2005 ********/23-820 - VAE reset value is 1.

V01.04 08 Jun 2007 23.4.6/23-827 - Added temperature sensor to customer information

23.1 Introduction
Module VREG_3V3 is a tri output voltage regulator that provides two separate 1.84V (typical) supplies
differing in the amount of current that can be sourced and a 2.82V (typical) supply. The regulator input
voltage range is from 3.3V up to 5V (typical).

23.1.1 Features
Module VREG_3V3 includes these distinctive features:

• Three parallel, linear voltage regulators with bandgap reference
• Low-voltage detect (LVD) with low-voltage interrupt (LVI)
• Power-on reset (POR)
• Low-voltage reset (LVR)
• High Temperature Detect (HTD) with High Temperature Interrupt (HTI)
• Autonomous periodical interrupt (API)

23.1.2 Modes of Operation
There are three modes VREG_3V3 can operate in:

1. Full performance mode (FPM) (MCU is not in stop mode)
The regulator is active, providing the nominal supply voltages with full current sourcing capability.
Features LVD (low-voltage detect), LVR (low-voltage reset), and POR (power-on reset) and HTD
(High Temperature Detect) are available. The API is available.

2. Reduced power mode (RPM) (MCU is in stop mode)
The purpose is to reduce power consumption of the device. The output voltage may degrade to a
lower value than in full performance mode, additionally the current sourcing capability is
substantially reduced. Only the POR is available in this mode, LVD, LVR and HTD are disabled.
The API is available.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 815



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

3. Shutdown mode
Controlled by VREGEN (see device level specification for connectivity of VREGEN).
This mode is characterized by minimum power consumption. The regulator outputs are in a high-
impedance state, only the POR feature is available, LVD, LVR and HTD are disabled. The API
internal RC oscillator clock is not available.
This mode must be used to disable the chip internal regulator VREG_3V3, i.e., to bypass the
VREG_3V3 to use external supplies.

23.1.3 Block Diagram
Figure 23-1 shows the function principle of VREG_3V3 by means of a block diagram. The regulator core
REG consists of three parallel subblocks, REG1, REG2 and REG3, providing three independent output
voltages.

MC9S12XE-Family Reference Manual  Rev. 1.25

816 Freescale Semiconductor



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

VBG

VDDPLL
REG3

VSSPLL
VDDR

VDDF
REG2

VDDA

VSSA VDD
REG1

VSS

LVD LVR LVR POR POR

VDDX

C

HTD
VREGEN CTRL HTI

LVI
API API
Rate API
Select

Bus Clock

LVD: Low Voltage Detect REG: Regulator Core
LVR: Low Voltage Reset CTRL: Regulator Control
POR: Power-on Reset API: Auto. Periodical Interrupt
HTD: High Temperature Detect PIN

Figure 23-1. VREG_3V3 Block Diagram

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 817

REG



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

23.2 External Signal Description
Due to the nature of VREG_3V3 being a voltage regulator providing the chip internal power supply
voltages, most signals are power supply signals connected to pads.

Table 23-2 shows all signals of VREG_3V3 associated with pins.

Table 23-2. Signal Properties

Name Function Reset State Pull Up

VDDR Power input (positive supply) — —
VDDA Quiet input (positive supply) — —
VSSA Quiet input (ground) — —
VDDX Power input (positive supply) — —
VDD Primary output (positive supply) —  —
VSS Primary output (ground) — —

VDDF Secondary output (positive supply) — —
VDDPLL Tertiary output (positive supply) — —
VSSPLL Tertiary output (ground) —  —

VREGEN (optional) Optional Regulator Enable — —
VREG_API VREG Autonomous Periodical — —
(optional) Interrupt output

NOTE
Check device level specification for connectivity of the signals.

23.2.1 VDDR — Regulator Power Input Pins
Signal VDDR is the power input of VREG_3V3. All currents sourced into the regulator loads flow through
this pin. A chip external decoupling capacitor (100 nF...220 nF, X7R ceramic) between VDDR and VSSR
(if VSSR is not available VSS) can smooth ripple on VDDR.

For entering Shutdown Mode, pin VDDR should also be tied to ground on devices without VREGEN pin.

23.2.2 VDDA, VSSA — Regulator Reference Supply Pins
Signals VDDA/VSSA, which are supposed to be relatively quiet, are used to supply the analog parts of the
regulator. Internal precision reference circuits are supplied from these signals. A chip external decoupling
capacitor (100 nF...220 nF, X7R ceramic) between VDDA and VSSA can further improve the quality of
this supply.

23.2.3 VDD, VSS — Regulator Output1 (Core Logic) Pins
Signals VDD/VSS are the primary outputs of VREG_3V3 that provide the power supply for the core logic.
These signals are connected to device pins to allow external decoupling capacitors (220 nF, X7R ceramic).

In Shutdown Mode an external supply driving VDD/VSS can replace the voltage regulator.

MC9S12XE-Family Reference Manual  Rev. 1.25

818 Freescale Semiconductor



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

23.2.4 VDDF — Regulator Output2 (NVM Logic) Pins
Signals VDDF/VSS are the secondary outputs of VREG_3V3 that provide the power supply for the NVM
logic. These signals are connected to device pins to allow external decoupling capacitors (220 nF, X7R
ceramic).

In Shutdown Mode an external supply driving VDDF/VSS can replace the voltage regulator.

23.2.5 VDDPLL, VSSPLL — Regulator Output3 (PLL) Pins
Signals VDDPLL/VSSPLL are the secondary outputs of VREG_3V3 that provide the power supply for
the PLL and oscillator. These signals are connected to device pins to allow external decoupling capacitors
(100 nF...220 nF, X7R ceramic).

In Shutdown Mode, an external supply driving VDDPLL/VSSPLL can replace the voltage regulator.

23.2.6 VDDX — Power Input Pin
Signals VDDX/VSS are monitored by VREG_3V3 with the LVR feature.

23.2.7 VREGEN — Optional Regulator Enable Pin
This optional signal is used to shutdown VREG_3V3. In that case, VDD/VSS and VDDPLL/VSSPLL
must be provided externally. Shutdown mode is entered with VREGEN being low. If VREGEN is high,
the VREG_3V3 is either in Full Performance Mode or in Reduced Power Mode.

For the connectivity of VREGEN, see device specification.

NOTE
 Switching from FPM or RPM to shutdown of VREG_3V3 and vice versa
is not supported while MCU is powered.

23.2.8 VREG_API — Optional Autonomous Periodical Interrupt Output Pin
This pin provides the signal selected via APIEA if system is set accordingly. See ********, “Autonomous
Periodical Interrupt Control Register (VREGAPICL) and 23.4.8, “Autonomous Periodical Interrupt (API)
for details.

For the connectivity of VREG_API, see device specification.

23.3 Memory Map and Register Definition
This section provides a detailed description of all registers accessible in VREG_3V3.

If enabled in the system, the VREG_3V3 will abort all read and write accesses to reserved registers within
it’s memory slice. See device level specification for details.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 819



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

23.3.1 Module Memory Map
A summary of the registers associated with the VREG_3V3 sub-block is shown in Figure 23-2. Detailed
descriptions of the registers and bits are given in the subsections that follow

Figure 23-2. Register Summary

Address Name Bit 7 6 5 4 3 2 1 Bit 0
R 0 0 HTDS

0x02F0 VREGHTCL VSEL VAE HTEN HTIE HTIF
W

R 0 0 0 0 0 LVDS
0x02F1 VREGCTRL LVIE LVIF

W

VREGAPIC R 0 0
0x02F2 APICLK APIFES APIEA APIFE APIE APIF

L W

VREGAPIT R 0 0
0x02F3 APITR5 APITR4 APITR3 APITR2 APITR1 APITR0

R W

VREGAPIR R
0x02F4 APIR15 APIR14 APIR13 APIR12 APIR11 APIR10 APIR9 APIR8

H W

VREGAPIR R
0x02F5 APIR7 APIR6 APIR5 APIR4 APIR3 APIR2 APIR1 APIR0

L W

Reserved R 0 0 0 0 0 0 0 0
0x02F6

06 W

R 0 0 0
0x02F7 VREGHTTR HTOEN HTTR3 HTTR2 HTTR1 HTTR0

W

23.3.2 Register Descriptions
This section describes all the VREG_3V3 registers and their individual bits.

******** High Temperature Control Register (VREGHTCL)
The VREGHTCL register allows to configure the VREG temperature sense features.
0x02F0

7 6 5 4 3 2 1 0
R 0 0 HTDS
W VSEL VAE HTEN HTIE HTIF

Reset 0 0 0 1 0 0 0 0
= Unimplemented or Reserved

MC9S12XE-Family Reference Manual  Rev. 1.25

820 Freescale Semiconductor



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

Table 23-3. VREGHTCL Field Descriptions

Field Description

7, 6 These reserved bits are used for test purposes and writable only in special modes.
Reserved They must remain clear for correct temperature sensor operation.

5 Voltage Access Select Bit — If set, the bandgap reference voltage VBG can be accessed internally (i.e.
VSEL multiplexed to an internal Analog to Digital Converter channel). The internal access must be enabled by bit VAE.

See device level specification for connectivity.
0 An internal temperature proportional voltage VHT can be accessed internally if VAE is set.
1 Bandgap reference voltage VBG can be accessed internally if VAE is set.

4 Voltage Access Enable Bit — If set, the voltage selected by bit VSEL can be accessed internally (i.e.
VAE multiplexed to an internal Analog to Digital Converter channel). See device level specification for connectivity.

0 Voltage selected by VSEL can not be accessed internally (i.e. External analog input is connected to Analog
to Digital Converter channel).

1 Voltage selected by VSEL can be accessed internally.

3 High Temperature Enable Bit — If set the temperature sense is enabled.
HTEN 0 The temperature sense is disabled.

1 The temperature sense is enabled.

2 High Temperature Detect Status Bit —
HTDS This read-only status bit reflects the temperature status. Writes have no effect.

0 Temperature TDIE is below level THTID or RPM or Shutdown Mode.
1 Temperature TDIE is above level THTIA and FPM.

1 High Temperature Interrupt Enable Bit
HTIE 0 Interrupt request is disabled.

1 Interrupt will be requested whenever HTIF is set.

0 High Temperature Interrupt Flag — HTIF — High Temperature Interrupt Flag
HTIF HTIF is set to 1 when HTDS status bit changes. This flag can only be cleared by writing a 1. Writing a 0 has no

effect. If enabled (HTIE=1), HTIF causes an interrupt request.
0 No change in HTDS bit.
1 HTDS bit has changed.
Note: On entering the reduced power mode the HTIF is not cleared by the VREG.

******** Control Register (VREGCTRL)
The VREGCTRL register allows the configuration of the VREG_3V3 low-voltage detect features.

0x02F1

7 6 5 4 3 2 1 0
R 0 0 0 0 0 LVDS

LVIE LVIF
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 23-3. Control Register (VREGCTRL)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 821



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

Table 23-4. VREGCTRL Field Descriptions

Field Description

2 Low-Voltage Detect Status Bit — This read-only status bit reflects the input voltage. Writes have no effect.
LVDS 0 Input voltage VDDA is above level VLVID or RPM or shutdown mode.

1 Input voltage VDDA is below level VLVIA and FPM.

1 Low-Voltage Interrupt Enable Bit
LVIE 0 Interrupt request is disabled.

1 Interrupt will be requested whenever LVIF is set.

0 Low-Voltage Interrupt Flag — LVIF is set to 1 when LVDS status bit changes. This flag can only be cleared by
LVIF writing a 1. Writing a 0 has no effect. If enabled (LVIE = 1), LVIF causes an interrupt request.

0 No change in LVDS bit.
1 LVDS bit has changed.
Note: On entering the Reduced Power Mode the LVIF is not cleared by the VREG_3V3.

******** Autonomous Periodical Interrupt Control Register (VREGAPICL)
The VREGAPICL register allows the configuration of the VREG_3V3 autonomous periodical interrupt
features.

0x02F2

7 6 5 4 3 2 1 0
R 0 0

APICLK APIES APIEA APIFE APIE APIF
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 23-4. Autonomous Periodical Interrupt Control Register (VREGAPICL)

Table 23-5. VREGAPICL Field Descriptions

Field Description

7 Autonomous Periodical Interrupt Clock Select Bit — Selects the clock source for the API. Writable only if
APICLK APIFE = 0; APICLK cannot be changed if APIFE is set by the same write operation.

0 Autonomous periodical interrupt clock used as source.
1 Bus clock used as source.

4 Autonomous Periodical Interrupt External Select Bit — Selects the waveform at the external pin.If set, at the
APIES external pin a clock is visible with 2 times the selected API Period (Table 23-9). If not set, at the external pin will

be a high pulse at the end of every selected period with the size of half of the min period (Table 23-9). See device
level specification for connectivity.
0 At the external periodic high pulses are visible, if APIEA and APIFE is set.
1 At the external pin a clock is visible, if APIEA and APIFE is set.

3 Autonomous Periodical Interrupt External Access Enable Bit — If set, the waveform selected by bit APIES
APIEA can be accessed externally. See device level specification for connectivity.

0 Waveform selected by APIES can not be accessed externally.
1 Waveform selected by APIES can be accessed externally, if APIFE is set.

2 Autonomous Periodical Interrupt Feature Enable Bit — Enables the API feature and starts the API timer
APIFE when set.

0 Autonomous periodical interrupt is disabled.
1 Autonomous periodical interrupt is enabled and timer starts running.

MC9S12XE-Family Reference Manual  Rev. 1.25

822 Freescale Semiconductor



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

Table 23-5. VREGAPICL Field Descriptions (continued)

Field Description

1 Autonomous Periodical Interrupt Enable Bit
APIE 0 API interrupt request is disabled.

1 API interrupt will be requested whenever APIF is set.

0 Autonomous Periodical Interrupt Flag — APIF is set to 1 when the in the API configured time has elapsed.
APIF This flag can only be cleared by writing a 1 to it. Clearing of the flag has precedence over setting.

Writing a 0 has no effect. If enabled (APIE = 1), APIF causes an interrupt request.
0 API timeout has not yet occurred.
1 API timeout has occurred.

******** Autonomous Periodical Interrupt Trimming Register (VREGAPITR)
The VREGAPITR register allows to trim the API timeout period.

0x02F3

7 6 5 4 3 2 1 0
R 0 0

APITR5 APITR4 APITR3 APITR2 APITR1 APITR0
W

Reset 01 01 01 01 01 01 0 0
1. Reset value is either 0 or preset by factory. See Section 1 (Device Overview) for details.

= Unimplemented or Reserved

Figure 23-5. Autonomous Periodical Interrupt Trimming Register (VREGAPITR)

Table 23-6. VREGAPITR Field Descriptions

Field Description

7–2 Autonomous Periodical Interrupt Period Trimming Bits — See Table 23-7 for trimming effects.
APITR[5:0]

Table 23-7. Trimming Effect of APIT

Bit Trimming Effect

APITR[5] Increases period
APITR[4] Decreases period less than APITR[5] increased it
APITR[3] Decreases period less than APITR[4]
APITR[2] Decreases period less than APITR[3]
APITR[1] Decreases period less than APITR[2]
APITR[0] Decreases period less than APITR[1]

******** Autonomous Periodical Interrupt Rate High and Low Register
(VREGAPIRH / VREGAPIRL)

The VREGAPIRH and VREGAPIRL register allows the configuration of the VREG_3V3 autonomous
periodical interrupt rate.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 823



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

0x02F4

7 6 5 4 3 2 1 0
R

APIR15 APIR14 APIR13 APIR12 APIR11 APIR10 APIR9 APIR8
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 23-6. Autonomous Periodical Interrupt Rate High Register (VREGAPIRH)

0x02F5

7 6 5 4 3 2 1 0
R

APIR7 APIR6 APIR5 APIR4 APIR3 APIR2 APIR1 APIR0
W

Reset 0 0 0 0 0 0 0 0

Figure 23-7. Autonomous Periodical Interrupt Rate Low Register (VREGAPIRL)

Table 23-8. VREGAPIRH / VREGAPIRL Field Descriptions

Field Description

15-0 Autonomous Periodical Interrupt Rate Bits — These bits define the timeout period of the API. See Table 23-
APIR[15:0] 9 for details of the effect of the autonomous periodical interrupt rate bits. Writable only if APIFE = 0 of

VREGAPICL register.

Table 23-9. Selectable Autonomous Periodical Interrupt Periods

APICLK APIR[15:0] Selected Period

0 0000 0.2 ms(1)

0 0001 0.4 ms1

0 0002 0.6 ms1

0 0003 0.8 ms1

0 0004 1.0 ms1

0 0005 1.2 ms1

0 .....        .....
0 FFFD 13106.8 ms1

0 FFFE 13107.0 ms1

0 FFFF 13107.2 ms1

1 0000 2 * bus clock period
1 0001 4 * bus clock period
1 0002 6 * bus clock period
1 0003 8 * bus clock period
1 0004 10 * bus clock period
1 0005 12 * bus clock period
1 ..... .....
1 FFFD  131068 * bus clock period
1 FFFE  131070 * bus clock period

MC9S12XE-Family Reference Manual  Rev. 1.25

824 Freescale Semiconductor



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

Table 23-9. Selectable Autonomous Periodical Interrupt Periods (continued)

APICLK APIR[15:0] Selected Period

1 FFFF  131072 * bus clock period
1. When trimmed within specified accuracy. See electrical specifications for details.

The period can be calculated as follows depending of APICLK:
Period = 2*(APIR[15:0] + 1) * 0.1 ms   or   period = 2*(APIR[15:0] + 1) * bus clock period

******** Reserved 06
The Reserved 06 is reserved for test purposes.
0x02F6

7 6 5 4 3 2 1 0
R 0 0 0 0 0 0 0 0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 23-8. Reserved 06

******** High Temperature Trimming Register (VREGHTTR)
The VREGHTTR register allows to trim the VREG temperature sense.

Fiption

0x02F7

7 6 5 4 3 2 1 0
R 0 0 0

HTOEN HTTR3 HTTR2 HTTR1 HTTR0
W

Reset 0 0 0 0 01 01 01 01

1. Reset value is either 0 or preset by factory. See Section 1 (Device Overview) for details.
= Unimplemented or Reserved

Figure 23-9. VREGHTTR

Table 23-10. VREGHTTR field descriptions

Field Description

7 High Temperature Offset Enable Bit — If set the temperature sense offset is enabled
HTOEN 0 The temperature sense offset is disabled

1 The temperature sense offset is enabled

3–0  High Temperature Trimming Bits — See Table 23-11 for trimming effects.
HTTR[3:0]

Table 23-11. Trimming Effect

Bit Trimming Effect

HTTR[3] Increases VHT twice of HTTR[2]

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 825



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

Table 23-11. Trimming Effect (continued)

Bit Trimming Effect

HTTR[2] Increases VHT twice of HTTR[1]
HTTR[1] Increases VHT twice of HTTR[0]
HTTR[0] Increases VHT (to compensate Temperature Offset)

23.4 Functional Description

23.4.1 General
Module VREG_3V3 is a voltage regulator, as depicted in Figure 23-1. The regulator functional elements
are the regulator core (REG), a low-voltage detect module (LVD), a control block (CTRL), a power-on
reset module (POR), and a low-voltage reset module (LVR)and a high temperature sensor (HTD).

23.4.2 Regulator Core (REG)
Respectively its regulator core has three parallel, independent regulation loops (REG1,REG2 and REG3).
REG1 and REG3 differ only in the amount of current that can be delivered.

The regulators are linear regulator with a bandgap reference when operated in Full Performance Mode.
They act as a voltage clamp in Reduced Power Mode. All load currents flow from input VDDR to VSS or
VSSPLL. The reference circuits are supplied by VDDA and VSSA.

******** Full Performance Mode
In Full Performance Mode, the output voltage is compared with a reference voltage by an operational
amplifier. The amplified input voltage difference drives the gate of an output transistor.

23.4.2.2 Reduced Power Mode
In Reduced Power Mode, the gate of the output transistor is connected directly to a reference voltage to
reduce power consumption. Mode switching from reduced power to full performance requires a transition
time of tvup, if the voltage regulator is enabled.

23.4.3 Low-Voltage Detect (LVD)
Subblock LVD is responsible for generating the low-voltage interrupt (LVI). LVD monitors the input
voltage (VDDA–VSSA) and continuously updates the status flag LVDS. Interrupt flag LVIF is set whenever
status flag LVDS changes its value. The LVD is available in FPM and is inactive in Reduced Power Mode
or Shutdown Mode.

MC9S12XE-Family Reference Manual  Rev. 1.25

826 Freescale Semiconductor



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

23.4.4 Power-On Reset (POR)
This functional block monitors VDD. If VDD is below VPORD, POR is asserted; if VDD exceeds VPORD,
the POR is deasserted. POR asserted forces the MCU into Reset. POR Deasserted will trigger the power-
on sequence.

23.4.5 Low-Voltage Reset (LVR)
Block LVR monitors the supplies VDD, VDDX and VDDF. If one (or more) drops below it’s
corresponding assertion level, signal LVR asserts; if all VDD,VDDX and VDDF supplies are above their
corresponding deassertion levels, signal LVR deasserts. The LVR function is available only in Full
Performance Mode.

23.4.6 HTD - High Temperature Detect
Subblock HTD is responsible for generating the high temperature interrupt (HTI). HTD monitors the die
temperature TDIE and continuously updates the status flag HTDS.
Interrupt flag HTIF is set whenever status flag HTDS changes its value.

The HTD is available in FPM and is inactive in Reduced Power Mode and Shutdown Mode.

The HT Trimming bits HTTR[3:0] can be set so that the temperature offset is zero, if accurate temperature
measurement is desired.

See Table 23-11 for the trimming effect of APITR.

23.4.7 Regulator Control (CTRL)
This part contains the register block of VREG_3V3 and further digital functionality needed to control the
operating modes. CTRL also represents the interface to the digital core logic.

23.4.8 Autonomous Periodical Interrupt (API)
Subblock API can generate periodical interrupts independent of the clock source of the MCU. To enable
the timer, the bit APIFE needs to be set.

The API timer is either clocked by a trimmable internal RC oscillator or the bus clock. Timer operation
will freeze when MCU clock source is selected and bus clock is turned off. See CRG specification for
details. The clock source can be selected with bit APICLK. APICLK can only be written when APIFE is
not set.

The APIR[15:0] bits determine the interrupt period. APIR[15:0] can only be written when APIFE is
cleared. As soon as APIFE is set, the timer starts running for the period selected by APIR[15:0] bits. When
the configured time has elapsed, the flag APIF is set. An interrupt, indicated by flag APIF = 1, is triggered
if interrupt enable bit APIE = 1. The timer is started automatically again after it has set APIF.

The procedure to change APICLK or APIR[15:0] is first to clear APIFE, then write to APICLK or
APIR[15:0], and afterwards set APIFE.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 827



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

The API Trimming bits APITR[5:0] must be set so the minimum period equals 0.2 ms if stable frequency
is desired.

See Table 23-7 for the trimming effect of APITR.

NOTE
The first period after enabling the counter by APIFE might be reduced by
API start up delay tsdel. The API internal RC oscillator clock is not available
if VREG_3V3 is in Shutdown Mode.

It is possible to generate with the API a waveform at an external pin by enabling the API by setting APIFE
and enabling the external access with setting APIEA. By setting APIES the waveform can be selected. If
APIES is set, then at the external pin a clock is visible with 2 times the selected API Period (Table 23-9).
If APIES is not set, then at the external pin will be a high pulse at the end of every selected period with the
size of half of the min period (Table 23-9). See device level specification for connectivity.

23.4.9 Resets
This section describes how VREG_3V3 controls the reset of the MCU.The reset values of registers and
signals are provided in Section 23.3, “Memory Map and Register Definition”. Possible reset sources are
listed in Table 23-12.

Table 23-12. Reset Sources

Reset Source Local Enable

Power-on reset Always active
Low-voltage reset Available only in Full Performance Mode

23.4.10 Description of Reset Operation

********* Power-On Reset (POR)
During chip power-up the digital core may not work if its supply voltage VDD is below the POR
deassertion level (VPORD). Therefore, signal POR, which forces the other blocks of the device into reset,
is kept high until VDD exceeds VPORD. The MCU will run the start-up sequence after POR deassertion.
The power-on reset is active in all operation modes of VREG_3V3.

********* Low-Voltage Reset (LVR)
For details on low-voltage reset, see Section 23.4.5, “Low-Voltage Reset (LVR)”.

23.4.11 Interrupts
This section describes all interrupts originated by VREG_3V3.

The interrupt vectors requested by VREG_3V3 are listed in Table 23-13. Vector addresses and interrupt
priorities are defined at MCU level.

MC9S12XE-Family Reference Manual  Rev. 1.25

828 Freescale Semiconductor



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

Table 23-13. Interrupt Vectors

Interrupt Source Local Enable

Low-voltage interrupt (LVI) LVIE = 1; available only in Full Performance
Mode

HTIE=1;
High Temperature Interrupt (HTI)

available only in Full Performance Mode

Autonomous periodical interrupt (API) APIE = 1

********* Low-Voltage Interrupt (LVI)
In FPM, VREG_3V3 monitors the input voltage VDDA. Whenever VDDA drops below level VLVIA, the
status bit LVDS is set to 1. On the other hand, LVDS is reset to 0 when VDDA rises above level VLVID. An
interrupt, indicated by flag LVIF = 1, is triggered by any change of the status bit LVDS if interrupt enable
bit LVIE = 1.

NOTE
On entering the Reduced Power Mode, the LVIF is not cleared by the
VREG_3V3.

********* HTI - High Temperature Interrupt
In FPM VREG monitors the die temperature TDIE. Whenever TDIE exceeds level THTIA the status bit
HTDS is set to 1. Vice versa, HTDS is reset to 0 when TDIE get below level THTID. An interrupt, indicated
by flag HTIF=1, is triggered by any change of the status bit HTDS if interrupt enable bit HTIE=1.

NOTE
On entering the Reduced Power Mode the HTIF is not cleared by the VREG.

23.4.11.3 Autonomous Periodical Interrupt (API)
As soon as the configured timeout period of the API has elapsed, the APIF bit is set. An interrupt, indicated
by flag APIF = 1, is triggered if interrupt enable bit APIE = 1.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 829



Chapter 23 Voltage Regulator (S12VREGL3V3V1)

MC9S12XE-Family Reference Manual  Rev. 1.25

830 Freescale Semiconductor



Chapter 24
128 KByte Flash Module (S12XFTM128K2V1)

Table 24-1. Revision History

Revision Revision Sections
Description of Changes

Number Date Affected
V01.10 30 Nov 2007 24.1.3/24-834 - Correction toTable 24-6
V01.11 19 Dec 2007 24.4.2/24-867 - Removed Load Data Field command 0x05

24.4.2/24-867 - Updated Command Error Handling tables based on parent-child relationship
with FTM256K2

24.4.2/24-867 - Corrected Error Handling table for Full Partition D-Flash, Partition D-Flash,
and EEPROM Emulation Query commands

24.4.2/24-867 - Corrected maximum allowed ERPART for Full Partition D-Flash and Partition
D-Flash commands

24.3.1/24-836 - Corrected P-Flash IFR Accessibility table
24.1.3/24-834 - Corrected Buffer RAM size in Feature List
24.3.1/24-836 - Corrected EEE Resource Memory Map

********/24-833 - Changed D-Flash size from 16Kbytes to 32Kbytes
24.3.1/24-836 - Corrected P-Flash Memory Map

V01.12 25 Sep 2009 - Change references for D-Flash from 16 Kbytes to 32 Kbytes
24.1/24-832 - Clarify single bit fault correction for P-Flash phrase

********/24-843 - Expand FDIV vs OSCCLK Frequency table
********/24-870 - Add statement concerning code runaway when executing Read Once

command from Flash block containing associated fields
********/24-871 - Add statement concerning code runaway when executing Program Once

command from Flash block containing associated fields
*********/24- - Add statement concerning code runaway when executing Verify Backdoor

875 Access Key command from Flash block containing associated fields
- Relate Key 0 to associated Backdoor Comparison Key address

*********/24- - Change “power down reset” to “reset”
875 - Add ACCERR condition for Disable EEPROM Emulation command

*********/24- The following changes were made to clarify module behavior related to Flash
875 register access during reset sequence and while Flash commands are active:

*********/24- - Add caution concerning register writes while command is active
884 - Writes to FCLKDIV are allowed during reset sequence while CCIF is clear

- Add caution concerning register writes while command is active
- Writes to FCCOBIX, FCCOBHI, FCCOBLO registers are ignored during

24.3.2/24-841 reset sequence
********/24-843
********/24-862

24.6/24-890

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 831