using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;

using VolvoFlashWR.Communication.ECU;
using VolvoFlashWR.Communication.Protocols;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.ECU
{
    [TestFixture]
    public class ECUCommunicationServiceTests
    {
        private Mock<ILoggingService> _loggerMock;
        private Mock<IVocomService> _vocomServiceMock;
        private ECUCommunicationService _ecuService;
        private VocomDevice _mockVocomDevice;

        [SetUp]
        public void Setup()
        {
            // Create mocks
            _loggerMock = new Mock<ILoggingService>();
            _vocomServiceMock = new Mock<IVocomService>();

            // Create a mock Vocom device
            _mockVocomDevice = new VocomDevice
            {
                Id = Guid.NewGuid().ToString(),
                SerialNumber = "88890300",
                ConnectionStatus = VocomConnectionStatus.Connected,
                ConnectionType = VocomConnectionType.USB
            };

            // Set up the Vocom service mock
            _vocomServiceMock.Setup(v => v.CurrentDevice).Returns(_mockVocomDevice);
            _vocomServiceMock.Setup(v => v.ConnectionSettings).Returns(new ConnectionSettings());

            // Create the ECU service
            _ecuService = new ECUCommunicationService(_loggerMock.Object);
        }

        [Test]
        public async Task InitializeAsync_WithValidVocomService_ReturnsTrue()
        {
            // Arrange
            _vocomServiceMock.Setup(v => v.InitializeAsync()).ReturnsAsync(true);

            // Act
            bool result = await _ecuService.InitializeAsync(_vocomServiceMock.Object);

            // Assert
            ClassicAssert.That(result, Is.True);
            ClassicAssert.That(_ecuService.IsInitialized, Is.EqualTo(true));
        }

        [Test]
        public async Task ScanForECUsAsync_WhenInitialized_ReturnsECUs()
        {
            // Arrange
            await _ecuService.InitializeAsync(_vocomServiceMock.Object);

            // Act
            List<ECUDevice> ecus = await _ecuService.ScanForECUsAsync();

            // Assert
            ClassicAssert.That(ecus, Is.Not.Null);
            ClassicAssert.That(ecus.Count > 0, Is.True);
        }

        [Test]
        public async Task ConnectToECUAsync_WithValidECU_ReturnsTrue()
        {
            // Arrange
            await _ecuService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _ecuService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];

            // Act
            bool result = await _ecuService.ConnectToECUAsync(ecu);

            // Assert
            ClassicAssert.That(result, Is.True);
            ClassicAssert.That(ecu.ConnectionStatus, Is.EqualTo(ECUConnectionStatus.Connected));
            ClassicAssert.That(_ecuService.ConnectedECUs.Contains(ecu), Is.True, "Connected ECUs list should contain the connected ECU");
        }

        [Test]
        public async Task DisconnectFromECUAsync_WithConnectedECU_ReturnsTrue()
        {
            // Arrange
            await _ecuService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _ecuService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _ecuService.ConnectToECUAsync(ecu);

            // Act
            bool result = await _ecuService.DisconnectFromECUAsync(ecu);

            // Assert
            ClassicAssert.That(result, Is.True);
            ClassicAssert.That(ecu.ConnectionStatus, Is.EqualTo(ECUConnectionStatus.Disconnected));
            ClassicAssert.That(_ecuService.ConnectedECUs.Contains(ecu), Is.False, "Connected ECUs list should not contain the disconnected ECU");
        }

        [Test]
        public async Task SetCommunicationSpeedModeAsync_WithHighSpeed_ReturnsTrue()
        {
            // Arrange
            await _ecuService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _ecuService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _ecuService.ConnectToECUAsync(ecu);

            // Ensure the ECU supports high-speed communication
            ecu.SupportsHighSpeedCommunication = true;

            // Act
            bool result = await _ecuService.SetCommunicationSpeedModeAsync(ecu, CommunicationSpeedMode.High);

            // Assert
            ClassicAssert.That(result, Is.True);
            ClassicAssert.That(ecu.ConnectionStatus, Is.EqualTo(ECUConnectionStatus.Connected));
            ClassicAssert.That(_ecuService.ConnectedECUs.Count, Is.EqualTo(1));
        }

        [Test]
        public async Task SetCommunicationSpeedModeAsync_WithLowSpeed_ReturnsTrue()
        {
            // Arrange
            await _ecuService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _ecuService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _ecuService.ConnectToECUAsync(ecu);

            // Ensure the ECU supports low-speed communication
            ecu.SupportsLowSpeedCommunication = true;

            // Act
            bool result = await _ecuService.SetCommunicationSpeedModeAsync(ecu, CommunicationSpeedMode.Low);

            // Assert
            ClassicAssert.That(result, Is.True);
            ClassicAssert.That(ecu.ConnectionStatus, Is.EqualTo(ECUConnectionStatus.Connected));
            ClassicAssert.That(_ecuService.ConnectedECUs.Count, Is.EqualTo(1));
        }

        [Test]
        public async Task SetCommunicationSpeedModeAsync_WithUnsupportedHighSpeed_ReturnsFalse()
        {
            // Arrange
            await _ecuService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _ecuService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _ecuService.ConnectToECUAsync(ecu);

            // Ensure the ECU does not support high-speed communication
            ecu.SupportsHighSpeedCommunication = false;

            // Act
            bool result = await _ecuService.SetCommunicationSpeedModeAsync(ecu, CommunicationSpeedMode.High);

            // Assert
            ClassicAssert.That(result, Is.False);
            ClassicAssert.That(ecu.ConnectionStatus, Is.EqualTo(ECUConnectionStatus.Connected));
        }

        [Test]
        public async Task SetOperatingModeAsync_ToOpenMode_ReturnsTrue()
        {
            // Arrange
            await _ecuService.InitializeAsync(_vocomServiceMock.Object);

            // Act
            bool result = await _ecuService.SetOperatingModeAsync(OperatingMode.Open);

            // Assert
            ClassicAssert.That(result, Is.True);
            ClassicAssert.That(_ecuService.CurrentOperatingMode, Is.EqualTo(OperatingMode.Open));
        }

        [Test]
        public async Task ReadEEPROMAsync_WithConnectedECU_ReturnsData()
        {
            // Arrange
            await _ecuService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _ecuService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _ecuService.ConnectToECUAsync(ecu);

            // Act
            byte[] eepromData = await _ecuService.ReadEEPROMAsync(ecu);

            // Assert
            ClassicAssert.That(eepromData, Is.Not.Null);
            ClassicAssert.That(eepromData.Length > 0, Is.True);
        }

        [Test]
        public async Task ReadMicrocontrollerCodeAsync_WithConnectedECU_ReturnsData()
        {
            // Arrange
            await _ecuService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _ecuService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _ecuService.ConnectToECUAsync(ecu);

            // Act
            byte[] mcuCode = await _ecuService.ReadMicrocontrollerCodeAsync(ecu);

            // Assert
            ClassicAssert.That(mcuCode, Is.Not.Null);
            ClassicAssert.That(mcuCode.Length > 0, Is.True);
        }

        [Test]
        public async Task ReadActiveFaultsAsync_WithConnectedECU_ReturnsFaults()
        {
            // Arrange
            await _ecuService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _ecuService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _ecuService.ConnectToECUAsync(ecu);

            // Act
            List<ECUFault> faults = await _ecuService.ReadActiveFaultsAsync(ecu);

            // Assert
            ClassicAssert.That(faults, Is.Not.Null);
        }

        [Test]
        public async Task PerformDiagnosticSessionAsync_WithConnectedECU_ReturnsDiagnosticData()
        {
            // Arrange
            await _ecuService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _ecuService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _ecuService.ConnectToECUAsync(ecu);

            // Act
            DiagnosticData diagnosticData = await _ecuService.PerformDiagnosticSessionAsync(ecu);

            // Assert
            ClassicAssert.That(diagnosticData, Is.Not.Null);
            ClassicAssert.That(diagnosticData.IsValid, Is.True);
            ClassicAssert.That(diagnosticData.ECUId, Is.EqualTo(ecu.Id));
            ClassicAssert.That(diagnosticData.Timestamp, Is.Not.EqualTo(default(DateTime)));
        }
    }
}

