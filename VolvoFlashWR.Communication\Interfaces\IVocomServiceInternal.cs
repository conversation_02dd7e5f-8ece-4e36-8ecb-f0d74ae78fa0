using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Interfaces
{
    /// <summary>
    /// Internal interface for Vocom communication service with additional methods
    /// </summary>
    public interface IVocomServiceInternal : IVocomService
    {
        /// <summary>
        /// Connects to a Vocom device via USB
        /// </summary>
        /// <param name="device">The device to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        Task<bool> ConnectViaUSBAsync(VocomDevice device);

        /// <summary>
        /// Connects to a Vocom device via Bluetooth
        /// </summary>
        /// <param name="device">The device to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        Task<bool> ConnectViaBluetoothAsync(VocomDevice device);

        /// <summary>
        /// Connects to a Vocom device via WiFi
        /// </summary>
        /// <param name="device">The device to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        Task<bool> ConnectViaWiFiAsync(VocomDevice device);

        /// <summary>
        /// Disconnects from a Vocom device connected via USB
        /// </summary>
        /// <param name="device">The device to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        Task<bool> DisconnectFromUSBAsync(VocomDevice device);

        /// <summary>
        /// Disconnects from a Vocom device connected via Bluetooth
        /// </summary>
        /// <param name="device">The device to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        Task<bool> DisconnectFromBluetoothAsync(VocomDevice device);

        /// <summary>
        /// Disconnects from a Vocom device connected via WiFi
        /// </summary>
        /// <param name="device">The device to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        Task<bool> DisconnectFromWiFiAsync(VocomDevice device);
    }
}
