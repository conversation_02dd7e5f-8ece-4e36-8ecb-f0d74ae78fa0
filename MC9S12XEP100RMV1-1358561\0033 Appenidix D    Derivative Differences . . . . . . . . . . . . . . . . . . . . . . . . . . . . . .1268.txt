﻿Appendix D Derivative Differences

Appendix D
Derivative Differences

D.1 Memory Sizes and Package Options S12XE - Family
Table D-1. Package and Memory Options of MC9S12XE-Family

Device Package Flash RAM EEPROM D-Flash
208 MAPBGA

9S12XEP100 144 LQFP 1M 64K
112 LQFP

208 MAPBGA
9S12XEP768 144 LQFP 768K 48K

112 LQFP
144 LQFP

9S12XEQ512 112 LQFP 512K 32K
80 QFP

144 LQFP 4K 32K
9S12XEQ384

112 LQFP 384K 24K
9S12XEG384

80 QFP
144 LQFP

9S12XES384 112 LQFP 384K 16K
80 QFP

9S12XET256 144 LQFP
9S12XEA256(1) 112 LQFP

256K 16K
80 QFP

9S12XEG256 112 LQFP
9S12XEG128 112 LQFP

9S12XEA1281 128K 12K 2K 32K
80 QFP

1. The 9S12XEA128/256 are a special bondouts for access to extra ADC
channels in 80QFP.Available in 80QFP / 256K/128K memory sizes only.
WARNING: NOT PIN-CO<PERSON><PERSON><PERSON>LE WITH REST OF FAMILY.
The 9S12XET256/9S12XEG128 use the standard 80QFP bondout,
compatible with other family members.

MC9S12XE-Family Reference Manual  Rev. 1.25

1268 Freescale Semiconductor



Appendix D Derivative Differences

Table D-2. Peripheral Options of MC9S12XE-Family Members

Device Package XGATE CAN SCI SPI IIC ECT TIM(1) PIT A/D I/O

208 MAPBGA 5 8 3 2 8ch 8ch 8ch 2/32 152
9S12XEP100 144LQFP 5 8 3 2 8ch 8ch 8ch 2/24 119

112LQFP 5 8 3 1 8ch 8ch 8ch 2/162 91
208 MAPBGA 5 8 3 2 8ch 8ch 8ch 2/32 152

144LQFP 5 8 3 2 8ch 8ch 8ch 2/24 119
9S12XEP768

2/16
112LQFP 5 8 3 1 8ch 8ch 8ch (2) 91

144LQFP 4 6 3 2 8ch 0 4ch 2/24 119
yes

9S12XEQ512 112LQFP 4 6 3 1 8ch 0 4ch 2/162 91
80QFP 4 2 3 1 8ch 0 4ch 2/82 59

144LQFP 4 6 3 2 8ch 0 4ch 2/24 119
9S12XEQ384 112LQFP 4 6 3 1 8ch 0 4ch 2/162 91

80QFP 4 2 3 1 8ch 0 4ch 2/82 59
144LQFP 2 6 3 2 8ch 0 4ch 2/24 119

9S12XEG384 112LQFP 2 6 3 1 8ch 0 4ch 2/162 91
80QFP 2 2 3 1 8ch 0 4ch 2/82 59

144LQFP 1 2 1 2 8ch 0 4ch 2/24 119
9S12XES384 112LQFP no 1 2 1 1 8ch 0 4ch 2/162 91

80QFP 1 2 1 1 8ch 0 4ch 2/82 59
144LQFP 3 4 3 1 8ch 0 4ch 2/24 119

9S12XET256 112LQFP 3 4 3 1 8ch 0 4ch 2/162 91
80QFP 3 2 3 1 8ch 0 4ch 2/82 59

9S12XEA256
(3) 80QFP 3 2 3 1 8ch 0 4ch 2/122 59

yes
9S12XEG256 112LQFP 2 4 3 1 8ch 0 4ch 2/162 91

112LQFP 2 2 2 1 8ch 0 2ch 1/16 91
9S12XEG128

80QFP 2 2 2 1 8ch 0 2ch 1/8 59

9S12XEA1283 80QFP 2 2 2 1 8ch 0 2ch 2/122 59
1. TIM available via rerouting on EP100,EP768 devices 112/144 pinout options.

TIM not available on EG128,ET256,EA256, EQ384,EQ512 devices.
2. The device features 2 16-channel ATD modules, only one of which is bonded out in this package option
3. This is a special bondout for access to extra ADC channels in 80QFP.

WARNING: NOT PIN-COMPATIBLE WITH REST OF FAMILY.
The 9S12XET256/9S12XEG128 use the standard 80QFP bondouts, compatible with other family members.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 1269



Appendix D Derivative Differences

D.2 Pinout explanations:
• Pinout compatibility is maintained throughout the device family
• A/D is the number of modules/total number of A/D channels.
• I/O is the sum of ports capable to act as digital input or output. .
• For additional flexibility especially for the low pin count packages several I/O functions can be

routed under software control to different pins. For details refer to the device overview section..

• Versions with 5 CAN modules will have CAN0, CAN1, CAN2, CAN3 and CAN4.
• Versions with 4 CAN modules will have CAN0, CAN1, CAN2 and CAN4.
• Versions with 3 CAN modules will have CAN0, CAN1 and CAN4.
• Versions with 2 CAN modules will have CAN0 and CAN4.
• Versions with 1 CAN module will have CAN0.

• Versions with 3 SPI modules will have SPI0, SPI1 and SPI2.
• Versions with 2 SPI modules will have SPI0 and SPI1.
• Versions with 1 SPI modules will have SPI0.

• Versions with 8 SCI modules will have SCI0, SCI1, SCI2, SCI3, SCI4, SCI5, SCI6 and SCI7.
• Versions with 7 SCI modules will have SCI0, SCI1, SCI2, SCI3, SCI4, SCI5, and SCI6.
• Versions with 6 SCI modules will have SCI0, SCI1, SCI2, SCI3, SCI4 and SCI5.
• Versions with 5 SCI modules will have SCI0, SCI1, SCI2, SCI3 and SCI4.
• Versions with 4 SCI modules will have SCI0, SCI1, SCI2 and SCI4.
• Versions with 3 SCI modules will have SCI0, SCI1 and SCI2.
• Versions with 2 SCI modules will have SCI0 and SCI1.
• Versions with 1 SCI module will have SCI0.

• Versions with 2 IIC modules will have IIC0 and IIC1.
• Versions with 1 IIC module will have IIC0.

• Versions with 1 ATD module will have ATD0.

MC9S12XE-Family Reference Manual  Rev. 1.25

1270 Freescale Semiconductor