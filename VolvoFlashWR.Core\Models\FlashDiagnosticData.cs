using System;
using System.Collections.Generic;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents diagnostic data for flash operations
    /// </summary>
    public class FlashDiagnosticData
    {
        /// <summary>
        /// Gets or sets the operation ID
        /// </summary>
        public Guid OperationId { get; set; }

        /// <summary>
        /// Gets or sets the operation type
        /// </summary>
        public string OperationType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the start time
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Gets or sets the end time
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// Gets or sets the status
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the address
        /// </summary>
        public uint Address { get; set; }

        /// <summary>
        /// Gets or sets the size
        /// </summary>
        public int Size { get; set; }

        /// <summary>
        /// Gets or sets the bytes processed
        /// </summary>
        public int BytesProcessed { get; set; }

        /// <summary>
        /// Gets or sets the progress percentage
        /// </summary>
        public double ProgressPercentage { get; set; }

        /// <summary>
        /// Gets or sets the elapsed time in milliseconds
        /// </summary>
        public double ElapsedTime { get; set; }

        /// <summary>
        /// Gets or sets the current throughput in bytes per second
        /// </summary>
        public double CurrentThroughput { get; set; }

        /// <summary>
        /// Gets or sets the average throughput in bytes per second
        /// </summary>
        public double AverageThroughput { get; set; }

        /// <summary>
        /// Gets or sets the peak throughput in bytes per second
        /// </summary>
        public double PeakThroughput { get; set; }

        /// <summary>
        /// Gets or sets the success rate as a percentage
        /// </summary>
        public double SuccessRate { get; set; }

        /// <summary>
        /// Gets or sets the error message
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets the ECU ID
        /// </summary>
        public string ECUId { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the ECU name
        /// </summary>
        public string ECUName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the performance data
        /// </summary>
        public List<PerformanceDataPoint> PerformanceData { get; set; } = new List<PerformanceDataPoint>();
    }

    /// <summary>
    /// Represents a performance data point
    /// </summary>
    public class PerformanceDataPoint
    {
        /// <summary>
        /// Gets or sets the timestamp
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the number of bytes processed
        /// </summary>
        public int BytesProcessed { get; set; }

        /// <summary>
        /// Gets or sets the elapsed time in milliseconds
        /// </summary>
        public double ElapsedTime { get; set; }

        /// <summary>
        /// Gets or sets the throughput in bytes per second
        /// </summary>
        public double Throughput { get; set; }
    }
}
