using System;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents a Vocom communication adapter device
    /// </summary>
    public class VocomDevice
    {
        /// <summary>
        /// Unique identifier for the Vocom device
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Name of the Vocom device
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Serial number of the Vocom device
        /// </summary>
        public string SerialNumber { get; set; }

        /// <summary>
        /// Firmware version of the Vocom device
        /// </summary>
        public string FirmwareVersion { get; set; }

        /// <summary>
        /// Current connection status of the Vocom device
        /// </summary>
        public VocomConnectionStatus ConnectionStatus { get; set; }

        /// <summary>
        /// Current connection type being used
        /// </summary>
        public VocomConnectionType ConnectionType { get; set; }

        /// <summary>
        /// Timestamp of the last successful connection
        /// </summary>
        public DateTime LastConnectionTime { get; set; }

        /// <summary>
        /// Indicates if the Vocom device is currently in use by another application
        /// </summary>
        public bool IsInUseByOtherApplication { get; set; }

        /// <summary>
        /// Name of the application currently using the Vocom device (if any)
        /// </summary>
        public string UsingApplicationName { get; set; }

        /// <summary>
        /// Bluetooth MAC address (if connected via Bluetooth)
        /// </summary>
        public string BluetoothAddress { get; set; }

        /// <summary>
        /// WiFi IP address (if connected via WiFi)
        /// </summary>
        public string WiFiIPAddress { get; set; }

        /// <summary>
        /// IP address for network connections
        /// </summary>
        public string IPAddress { get; set; }

        /// <summary>
        /// USB port information (if connected via USB)
        /// </summary>
        public string USBPortInfo { get; set; }
    }

    /// <summary>
    /// Represents the connection status of a Vocom device
    /// </summary>
    public enum VocomConnectionStatus
    {
        Disconnected,
        Connecting,
        Connected,
        Error
    }

    /// <summary>
    /// Represents the connection type used for the Vocom device
    /// </summary>
    public enum VocomConnectionType
    {
        USB,
        Bluetooth,
        WiFi
    }
}
