# VolvoFlashWR - Real Hardware Testing Package

## Package Contents

- **Application/**: Main application files with all required libraries
- **Tools/**: Additional tools (Key Generator)
- **Documentation/**: This file and other documentation

## Quick Start

### For Real Hardware Testing:
1. Install Vocom driver: CommunicationUnitInstaller-*******.msi
2. Connect Vocom 1 adapter via USB
3. Make sure PTT (Premium Tech Tool) is NOT running
4. Run: **Start_Real_Hardware_Mode.bat**

### For Testing Without Hardware:
1. Run: **Start_Test_Mode.bat**

## System Requirements

- Windows 10/11 (x86 or x64)
- .NET 8.0 Runtime (included in self-contained package)
- Vocom 1 adapter (for real hardware mode)
- Vocom driver installed (for real hardware mode)

## Critical Libraries Included

✅ **WUDFPuma.dll** - Main Vocom 1 adapter driver
✅ **Volvo.ApciPlus.dll** - APCI communication library
✅ **apci.dll** - APCI core functionality
✅ **All 31 required libraries** for real hardware communication

## Troubleshooting

### "WUDFPuma.dll not found" Error:
- Install Vocom driver: CommunicationUnitInstaller-*******.msi
- Make sure driver is properly installed

### "Cannot connect to Vocom adapter" Error:
- Check USB connection
- Make sure PTT is not running
- Try different USB port
- Restart application

### Application won't start:
- Make sure .NET 8.0 is installed
- Run as Administrator if needed
- Check Windows Event Viewer for errors

## Support

For technical support, check the log files in the Logs/ directory.

---
**Package created:** Sat 05/31/2025 19:51:20.98
**Version:** 1.0.0
**Target:** Real Hardware Testing
