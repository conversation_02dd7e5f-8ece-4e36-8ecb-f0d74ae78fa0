using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace VolvoFlashWR.UI.ViewModels
{
    /// <summary>
    /// Simple view model for displaying loading status
    /// </summary>
    public class LoadingViewModel : INotifyPropertyChanged
    {
        private string _statusMessage = "Loading...";
        private bool _isBusy = true;

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Gets or sets whether the application is busy
        /// </summary>
        public bool IsBusy
        {
            get => _isBusy;
            set
            {
                _isBusy = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Gets the Vocom status (placeholder for loading)
        /// </summary>
        public string VocomStatus => "Initializing...";

        /// <summary>
        /// Gets the connection status (placeholder for loading)
        /// </summary>
        public string ConnectionStatus => "Initializing...";

        /// <summary>
        /// Gets the connected ECU count (placeholder for loading)
        /// </summary>
        public int ConnectedECUCount => 0;

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
