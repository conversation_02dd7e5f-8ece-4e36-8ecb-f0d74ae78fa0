using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;

namespace VolvoFlashWR.Core.Utilities
{
    /// <summary>
    /// Helper methods for working with nullable reference types.
    /// </summary>
    public static class NullableHelper
    {
        /// <summary>
        /// Throws an ArgumentNullException if the value is null.
        /// </summary>
        /// <typeparam name="T">The type of the value.</typeparam>
        /// <param name="value">The value to check.</param>
        /// <param name="paramName">The name of the parameter.</param>
        /// <returns>The non-null value.</returns>
        /// <exception cref="ArgumentNullException">Thrown if the value is null.</exception>
        public static T ThrowIfNull<T>([NotNull] T? value, string paramName)
        {
            if (value == null)
            {
                throw new ArgumentNullException(paramName);
            }

            return value;
        }

        /// <summary>
        /// Returns the default value if the value is null.
        /// </summary>
        /// <typeparam name="T">The type of the value.</typeparam>
        /// <param name="value">The value to check.</param>
        /// <param name="defaultValue">The default value to return if the value is null.</param>
        /// <returns>The value if it's not null, otherwise the default value.</returns>
        public static T DefaultIfNull<T>(T? value, T defaultValue) where T : class
        {
            return value ?? defaultValue;
        }

        /// <summary>
        /// Returns an empty string if the value is null.
        /// </summary>
        /// <param name="value">The string value to check.</param>
        /// <returns>The string value if it's not null, otherwise an empty string.</returns>
        public static string EmptyIfNull(string? value)
        {
            return value ?? string.Empty;
        }

        /// <summary>
        /// Returns an empty collection if the value is null.
        /// </summary>
        /// <typeparam name="T">The type of the collection elements.</typeparam>
        /// <param name="value">The collection to check.</param>
        /// <returns>The collection if it's not null, otherwise an empty collection.</returns>
        public static IEnumerable<T> EmptyIfNull<T>(IEnumerable<T>? value)
        {
            return value ?? Enumerable.Empty<T>();
        }

        /// <summary>
        /// Returns an empty list if the value is null.
        /// </summary>
        /// <typeparam name="T">The type of the list elements.</typeparam>
        /// <param name="value">The list to check.</param>
        /// <returns>The list if it's not null, otherwise an empty list.</returns>
        public static List<T> EmptyListIfNull<T>(List<T>? value)
        {
            return value ?? new List<T>();
        }

        /// <summary>
        /// Returns an empty dictionary if the value is null.
        /// </summary>
        /// <typeparam name="TKey">The type of the dictionary keys.</typeparam>
        /// <typeparam name="TValue">The type of the dictionary values.</typeparam>
        /// <param name="value">The dictionary to check.</param>
        /// <returns>The dictionary if it's not null, otherwise an empty dictionary.</returns>
        public static Dictionary<TKey, TValue> EmptyDictionaryIfNull<TKey, TValue>(Dictionary<TKey, TValue>? value)
            where TKey : notnull
        {
            return value ?? new Dictionary<TKey, TValue>();
        }

        /// <summary>
        /// Returns an empty array if the value is null.
        /// </summary>
        /// <typeparam name="T">The type of the array elements.</typeparam>
        /// <param name="value">The array to check.</param>
        /// <returns>The array if it's not null, otherwise an empty array.</returns>
        public static T[] EmptyArrayIfNull<T>(T[]? value)
        {
            return value ?? Array.Empty<T>();
        }

        /// <summary>
        /// Executes an action if the value is not null.
        /// </summary>
        /// <typeparam name="T">The type of the value.</typeparam>
        /// <param name="value">The value to check.</param>
        /// <param name="action">The action to execute if the value is not null.</param>
        public static void IfNotNull<T>(T? value, Action<T> action) where T : class
        {
            if (value != null)
            {
                action(value);
            }
        }

        /// <summary>
        /// Maps a value to another value using a mapping function, returning a default value if the input is null.
        /// </summary>
        /// <typeparam name="TInput">The type of the input value.</typeparam>
        /// <typeparam name="TOutput">The type of the output value.</typeparam>
        /// <param name="value">The input value.</param>
        /// <param name="mapper">The mapping function.</param>
        /// <param name="defaultValue">The default value to return if the input is null.</param>
        /// <returns>The mapped value if the input is not null, otherwise the default value.</returns>
        public static TOutput MapOrDefault<TInput, TOutput>(TInput? value, Func<TInput, TOutput> mapper, TOutput defaultValue)
            where TInput : class
        {
            return value != null ? mapper(value) : defaultValue;
        }

        /// <summary>
        /// Throws an ArgumentNullException if the value is null, with a custom message.
        /// </summary>
        /// <typeparam name="T">The type of the value.</typeparam>
        /// <param name="value">The value to check.</param>
        /// <param name="paramName">The name of the parameter.</param>
        /// <param name="message">The custom message.</param>
        /// <returns>The non-null value.</returns>
        /// <exception cref="ArgumentNullException">Thrown if the value is null.</exception>
        public static T ThrowIfNull<T>([NotNull] T? value, string paramName, string message) where T : class
        {
            if (value == null)
            {
                throw new ArgumentNullException(paramName, message);
            }

            return value;
        }
    }
}
