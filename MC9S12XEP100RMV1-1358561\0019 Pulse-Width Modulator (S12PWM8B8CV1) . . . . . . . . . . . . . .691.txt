﻿Chapter 19
Pulse-Width Modulator (S12PWM8B8CV1)

Table 19-1. Revision History

Revision Sections
Revision Date Description of Changes

Number Affected
1.1 28 Sepr 2012 ******* deleted the blank in the doc

19.1 Introduction
The PWM definition is based on the HC12 PWM definitions. It contains the basic features from the HC11
with some of the enhancements incorporated on the HC12: center aligned output mode and four available
clock sources.The PWM module has eight channels with independent control of left and center aligned
outputs on each channel.

Each of the eight channels has a programmable period and duty cycle as well as a dedicated counter. A
flexible clock select scheme allows a total of four different clock sources to be used with the counters. Each
of the modulators can create independent continuous waveforms with software-selectable duty rates from
0% to 100%. The PWM outputs can be programmed as left aligned outputs or center aligned outputs.

19.1.1 Features
The PWM block includes these distinctive features:

• Eight independent PWM channels with programmable period and duty cycle
• Dedicated counter for each PWM channel
• Programmable PWM enable/disable for each channel
• Software selection of PWM duty pulse polarity for each channel
• Period and duty cycle are double buffered. Change takes effect when the end of the effective period

is reached (PWM counter reaches zero) or when the channel is disabled.
• Programmable center or left aligned outputs on individual channels
• Eight 8-bit channel or four 16-bit channel PWM resolution
• Four clock sources (A, B, SA, and SB) provide for a wide range of frequencies
• Programmable clock select logic
• Emergency shutdown

19.1.2 Modes of Operation
There is a software programmable option for low power consumption in wait mode that disables the input
clock to the prescaler.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 691



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

In freeze mode there is a software programmable option to disable the input clock to the prescaler. This is
useful for emulation.

19.1.3 Block Diagram
Figure 19-1 shows the block diagram for the 8-bit 8-channel PWM block.

PWM8B8C PWM Channels
Channel 7

PWM7
Period and Duty Counter

Channel 6
PWM6

Period and Duty Counter
Bus Clock Clock Select PWM Clock

Channel 5
PWM5

Period and Duty Counter

Control
Channel 4

PWM4
Period and Duty Counter

Channel 3
PWM3

Enable Period and Duty Counter

Polarity Channel 2
PWM2

Period and Duty Counter

Alignment
Channel 1

PWM1
Period and Duty Counter

Channel 0

Period and Duty Counter PWM0

Figure 19-1. PWM Block Diagram

19.2 External Signal Description
The PWM module has a total of 8 external pins.

19.2.1 PWM7 — PWM Channel 7
This pin serves as waveform output of PWM channel 7 and as an input for the emergency shutdown
feature.

MC9S12XE-Family Reference Manual  Rev. 1.25

692 Freescale Semiconductor



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

19.2.2 PWM6 — PWM Channel 6
This pin serves as waveform output of PWM channel 6.

19.2.3 PWM5 — PWM Channel 5
This pin serves as waveform output of PWM channel 5.

19.2.4 PWM4 — PWM Channel 4
This pin serves as waveform output of PWM channel 4.

19.2.5 PWM3 — PWM Channel 3
This pin serves as waveform output of PWM channel 3.

19.2.6 PWM3 — PWM Channel 2
This pin serves as waveform output of PWM channel 2.

19.2.7 PWM3 — PWM Channel 1
This pin serves as waveform output of PWM channel 1.

19.2.8 PWM3 — PWM Channel 0
This pin serves as waveform output of PWM channel 0.

19.3 Memory Map and Register Definition
This section describes in detail all the registers and register bits in the PWM module.

The special-purpose registers and register bit functions that are not normally available to device end users,
such as factory test control registers and reserved registers, are clearly identified by means of shading the
appropriate portions of address maps and register diagrams. Notes explaining the reasons for restricting
access to the registers and functions are also explained in the individual register descriptions.

19.3.1 Module Memory Map
This section describes the content of the registers in the PWM module. The base address of the PWM
module is determined at the MCU level when the MCU is defined. The register decode map is fixed and
begins at the first address of the module address offset. The figure below shows the registers associated
with the PWM and their relative offset from the base address. The register detail description follows the
order they appear in the register map.

Reserved bits within a register will always read as 0 and the write will be unimplemented. Unimplemented
functions are indicated by shading the bit. .

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 693



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

NOTE
Register Address = Base Address + Address Offset, where the Base Address
is defined at the MCU level and the Address Offset is defined at the module
level.

19.3.2 Register Descriptions
This section describes in detail all the registers and register bits in the PWM module.

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0000 R
PWME PWME7 PWME6 PWME5 PWME4 PWME3 PWME2 PWME1 PWME0

W

0x0001 R
PWMPOL PPOL7 PPOL6 PPOL5 PPOL4 PPOL3 PPOL2 PPOL1 PPOL0

W

0x0002 R
PWMCLK PCLK7 PCLKL6 PCLK5 PCLK4 PCLK3 PCLK2 PCLK1 PCLK0

W

0x0003 R 0 0
PWMPRCLK PCKB2 PCKB1 PCKB0 PCKA2 PCKA1 PCKA0

W

0x0004 R
PWMCAE CAE7 CAE6 CAE5 CAE4 CAE3 CAE2 CAE1 CAE0

W

0x0005 R 0 0
PWMCTL CON67 CON45 CON23 CON01 PSWAI PFRZ

W

0x0006 R 0 0 0 0 0 0 0 0
PWMTST(1)

W

0x0007 R 0 0 0 0 0 0 0 0
PWMPRSC1

W

0x0008 R
PWMSCLA Bit 7  6  5  4  3  2  1  Bit 0

W

0x0009 R
PWMSCLB Bit 7  6  5  4  3  2  1  Bit 0

W

0x000A R 0 0 0 0 0 0 0 0
PWMSCNTA

1 W

= Unimplemented or Reserved

Figure 19-2. PWM Register Summary (Sheet 1 of 3)

MC9S12XE-Family Reference Manual  Rev. 1.25

694 Freescale Semiconductor



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x000B R 0 0 0 0 0 0 0 0
PWMSCNTB

1 W

0x000C R Bit 7  6  5  4  3  2  1  Bit 0
PWMCNT0 W 0 0 0 0 0 0 0 0

0x000D R Bit 7  6  5  4  3  2  1  Bit 0
PWMCNT1 W 0 0 0 0 0 0 0 0

0x000E R Bit 7  6  5  4  3  2  1  Bit 0
PWMCNT2 W 0 0 0 0 0 0 0 0

0x000F R Bit 7  6  5  4  3  2  1  Bit 0
PWMCNT3 W 0 0 0 0 0 0 0 0

0x0010 R Bit 7  6  5  4  3  2  1  Bit 0
PWMCNT4 W 0 0 0 0 0 0 0 0

0x0011 R Bit 7  6  5  4  3  2  1  Bit 0
PWMCNT5 W 0 0 0 0 0 0 0 0

0x0012 R Bit 7  6  5  4  3  2  1  Bit 0
PWMCNT6 W 0 0 0 0 0 0 0 0

0x0013 R Bit 7  6  5  4  3  2  1  Bit 0
PWMCNT7 W 0 0 0 0 0 0 0 0

0x0014 R
PWMPER0 Bit 7  6  5  4  3  2  1  Bit 0

W

0x0015 R
PWMPER1 Bit 7  6  5  4  3  2  1  Bit 0

W

0x0016 R
PWMPER2 Bit 7  6  5  4  3  2  1  Bit 0

W

0x0017 R
PWMPER3 Bit 7  6  5  4  3  2  1  Bit 0

W

0x0018 R
PWMPER4 Bit 7  6  5  4  3  2  1  Bit 0

W

0x0019 R
PWMPER5 Bit 7  6  5  4  3  2  1  Bit 0

W

= Unimplemented or Reserved

Figure 19-2. PWM Register Summary (Sheet 2 of 3)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 695



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x001A R
PWMPER6 Bit 7  6  5  4  3  2  1  Bit 0

W

0x001B R
PWMPER7 Bit 7  6  5  4  3  2  1  Bit 0

W

0x001C R
PWMDTY0 Bit 7  6  5  4  3  2  1  Bit 0

W

0x001D R
PWMDTY1 Bit 7  6  5  4  3  2  1  Bit 0

W

0x001E R
PWMDTY2 Bit 7  6  5  4  3  2  1  Bit 0

W

0x001F R
PWMDTY3 Bit 7  6  5  4  3  2  1  Bit 0

W

0x0020 R
PWMDTY4 Bit 7  6  5  4  3  2  1  Bit 0

W

0x0021 R
PWMDTY5 Bit 7  6  5  4  3  2  1  Bit 0

W

0x0022 R
PWMDTY6 Bit 7  6  5  4  3  2  1  Bit 0

W

0x0023 R
PWMDTY7 Bit 7  6  5  4  3  2  1  Bit 0

W

0x0024 R 0 0 PWM7IN
PWMSDN PWMIF PWMIE PWMLVL PWM7INL PWM7ENA

W PWMRSTRT

= Unimplemented or Reserved

Figure 19-2. PWM Register Summary (Sheet 3 of 3)
1. Intended for factory test purposes only.

19.3.2.1 PWM Enable Register (PWME)
Each PWM channel has an enable bit (PWMEx) to start its waveform output. When any of the PWMEx
bits are set (PWMEx = 1), the associated PWM output is enabled immediately. However, the actual PWM
waveform is not available on the associated PWM output until its clock source begins its next cycle due to
the synchronization of PWMEx and the clock source.

NOTE
The first PWM cycle after enabling the channel can be irregular.

MC9S12XE-Family Reference Manual  Rev. 1.25

696 Freescale Semiconductor



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

An exception to this is when channels are concatenated. Once concatenated mode is enabled (CONxx bits
set in PWMCTL register), enabling/disabling the corresponding 16-bit PWM channel is controlled by the
low order PWMEx bit.In this case, the high order bytes PWMEx bits have no effect and their
corresponding PWM output lines are disabled.

While in run mode, if all eight PWM channels are disabled (PWME7–0 = 0), the prescaler counter shuts
off for power savings.

Module Base + 0x0000

7 6 5 4 3 2 1 0
R

PWME7 PWME6 PWME5 PWME4 PWME3 PWME2 PWME1 PWME0
W

Reset 0 0 0 0 0 0 0 0

Figure 19-3. PWM Enable Register (PWME)

Read: Anytime

Write: Anytime
Table 19-2. PWME Field Descriptions

Field Description

7 Pulse Width Channel 7 Enable
PWME7 0 Pulse width channel 7 is disabled.

1 Pulse width channel 7 is enabled. The pulse modulated signal becomes available at PWM output bit 7 when
its clock source begins its next cycle.

6 Pulse Width Channel 6 Enable
PWME6 0 Pulse width channel 6 is disabled.

1 Pulse width channel 6 is enabled. The pulse modulated signal becomes available at PWM output bit6 when
its clock source begins its next cycle. If CON67=1, then bit has no effect and PWM output line 6 is disabled.

5 Pulse Width Channel 5 Enable
PWME5 0 Pulse width channel 5 is disabled.

1 Pulse width channel 5 is enabled. The pulse modulated signal becomes available at PWM output bit 5 when
its clock source begins its next cycle.

4 Pulse Width Channel 4 Enable
PWME4 0 Pulse width channel 4 is disabled.

1 Pulse width channel 4 is enabled. The pulse modulated signal becomes available at PWM, output bit 4 when
its clock source begins its next cycle. If CON45 = 1, then bit has no effect and PWM output bit4 is disabled.

3 Pulse Width Channel 3 Enable
PWME3 0 Pulse width channel 3 is disabled.

1 Pulse width channel 3 is enabled. The pulse modulated signal becomes available at PWM, output bit 3 when
its clock source begins its next cycle.

2 Pulse Width Channel 2 Enable
PWME2 0 Pulse width channel 2 is disabled.

1 Pulse width channel 2 is enabled. The pulse modulated signal becomes available at PWM, output bit 2 when
its clock source begins its next cycle. If CON23 = 1, then bit has no effect and PWM output bit2 is disabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 697



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

Table 19-2. PWME Field Descriptions (continued)

Field Description

1 Pulse Width Channel 1 Enable
PWME1 0 Pulse width channel 1 is disabled.

1 Pulse width channel 1 is enabled. The pulse modulated signal becomes available at PWM, output bit 1 when
its clock source begins its next cycle.

0 Pulse Width Channel 0 Enable
PWME0 0 Pulse width channel 0 is disabled.

1 Pulse width channel 0 is enabled. The pulse modulated signal becomes available at PWM, output bit 0 when
its clock source begins its next cycle. If CON01 = 1, then bit has no effect and PWM output line0 is disabled.

******** PWM Polarity Register (PWMPOL)
The starting polarity of each PWM channel waveform is determined by the associated PPOLx bit in the
PWMPOL register. If the polarity bit is one, the PWM channel output is high at the beginning of the cycle
and then goes low when the duty count is reached. Conversely, if the polarity bit is zero, the output starts
low and then goes high when the duty count is reached.

Module Base + 0x0001

7 6 5 4 3 2 1 0
R

PPOL7 PPOL6 PPOL5 PPOL4 PPOL3 PPOL2 PPOL1 PPOL0
W

Reset 0 0 0 0 0 0 0 0

Figure 19-4. PWM Polarity Register (PWMPOL)

Read: Anytime

Write: Anytime

NOTE
PPOLx register bits can be written anytime. If the polarity is changed while
a PWM signal is being generated, a truncated or stretched pulse can occur
during the transition

Table 19-3. PWMPOL Field Descriptions

Field Description

7–0 Pulse Width Channel 7–0 Polarity Bits
PPOL[7:0] 0 PWM channel 7–0 outputs are low at the beginning of the period, then go high when the duty count is

reached.
1 PWM channel 7–0 outputs are high at the beginning of the period, then go low when the duty count is

reached.

******** PWM Clock Select Register (PWMCLK)
Each PWM channel has a choice of two clocks to use as the clock source for that channel as described
below.

MC9S12XE-Family Reference Manual  Rev. 1.25

698 Freescale Semiconductor



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

Module Base + 0x0002

7 6 5 4 3 2 1 0
R

PCLK7 PCLKL6 PCLK5 PCLK4 PCLK3 PCLK2 PCLK1 PCLK0
W

Reset 0 0 0 0 0 0 0 0

Figure 19-5. PWM Clock Select Register (PWMCLK)

Read: Anytime

Write: Anytime

NOTE
Register bits PCLK0 to PCLK7 can be written anytime. If a clock select is
changed while a PWM signal is being generated, a truncated or stretched
pulse can occur during the transition.

Table 19-4. PWMCLK Field Descriptions

Field Description

7 Pulse Width Channel 7 Clock Select
PCLK7 0 Clock B is the clock source for PWM channel 7.

1 Clock SB is the clock source for PWM channel 7.

6 Pulse Width Channel 6 Clock Select
PCLK6 0 Clock B is the clock source for PWM channel 6.

1 Clock SB is the clock source for PWM channel 6.

5 Pulse Width Channel 5 Clock Select
PCLK5 0 Clock A is the clock source for PWM channel 5.

1 Clock SA is the clock source for PWM channel 5.

4 Pulse Width Channel 4 Clock Select
PCLK4 0 Clock A is the clock source for PWM channel 4.

1 Clock SA is the clock source for PWM channel 4.

3 Pulse Width Channel 3 Clock Select
PCLK3 0 Clock B is the clock source for PWM channel 3.

1 Clock SB is the clock source for PWM channel 3.

2 Pulse Width Channel 2 Clock Select
PCLK2 0 Clock B is the clock source for PWM channel 2.

1 Clock SB is the clock source for PWM channel 2.

1 Pulse Width Channel 1 Clock Select
PCLK1 0 Clock A is the clock source for PWM channel 1.

1 Clock SA is the clock source for PWM channel 1.

0 Pulse Width Channel 0 Clock Select
PCLK0 0 Clock A is the clock source for PWM channel 0.

1 Clock SA is the clock source for PWM channel 0.

19.3.2.4 PWM Prescale Clock Select Register (PWMPRCLK)
This register selects the prescale clock source for clocks A and B independently.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 699



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

Module Base + 0x0003

7 6 5 4 3 2 1 0
R 0 0

PCKB2 PCKB1 PCKB0 PCKA2 PCKA1 PCKA0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 19-6. PWM Prescale Clock Select Register (PWMPRCLK)

Read: Anytime

Write: Anytime

NOTE
PCKB2–0 and PCKA2–0 register bits can be written anytime. If the clock
pre-scale is changed while a PWM signal is being generated, a truncated or
stretched pulse can occur during the transition.

Table 19-5. PWMPRCLK Field Descriptions

Field Description

6–4 Prescaler Select for Clock B — Clock B is one of two clock sources which can be used for channels 2, 3, 6, or
PCKB[2:0] 7. These three bits determine the rate of clock B, as shown in Table 19-6.

2–0 Prescaler Select for Clock A — Clock A is one of two clock sources which can be used for channels 0, 1, 4 or
PCKA[2:0] 5. These three bits determine the rate of clock A, as shown in Table 19-7.

s

Table 19-6. Clock B Prescaler Selects

PCKB2 PCKB1 PCKB0 Value of Clock B
0 0 0 Bus clock
0 0 1 Bus clock / 2
0 1 0 Bus clock / 4
0 1 1 Bus clock / 8
1 0 0 Bus clock / 16
1 0 1 Bus clock / 32
1 1 0  Bus clock / 64
1 1 1 Bus clock / 128

Table 19-7. Clock A Prescaler Selects

PCKA2 PCKA1 PCKA0 Value of Clock A
0 0 0 Bus clock
0 0 1 Bus clock / 2
0 1 0 Bus clock / 4
0 1 1 Bus clock / 8
1 0 0 Bus clock / 16
1 0 1 Bus clock / 32
1 1 0  Bus clock / 64
1 1 1 Bus clock / 128

MC9S12XE-Family Reference Manual  Rev. 1.25

700 Freescale Semiconductor



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

******** PWM Center Align Enable Register (PWMCAE)
The PWMCAE register contains eight control bits for the selection of center aligned outputs or left aligned
outputs for each PWM channel. If the CAEx bit is set to a one, the corresponding PWM output will be
center aligned. If the CAEx bit is cleared, the corresponding PWM output will be left aligned. See
Section ********, “Left Aligned Outputs” and Section ********, “Center Aligned Outputs” for a more
detailed description of the PWM output modes.

Module Base + 0x0004

7 6 5 4 3 2 1 0
R

CAE7 CAE6 CAE5 CAE4 CAE3 CAE2 CAE1 CAE0
W

Reset 0 0 0 0 0 0 0 0

Figure 19-7. PWM Center Align Enable Register (PWMCAE)

Read: Anytime

Write: Anytime

NOTE
Write these bits only when the corresponding channel is disabled.

Table 19-8. PWMCAE Field Descriptions

Field Description

7–0 Center Aligned Output Modes on Channels 7–0
CAE[7:0] 0 Channels 7–0 operate in left aligned output mode.

1 Channels 7–0 operate in center aligned output mode.

******** PWM Control Register (PWMCTL)
The PWMCTL register provides for various control of the PWM module.

Module Base + 0x0005

7 6 5 4 3 2 1 0
R 0 0

CON67 CON45 CON23 CON01 PSWAI PFRZ
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 19-8. PWM Control Register (PWMCTL)

Read: Anytime

Write: Anytime

There are three control bits for concatenation, each of which is used to concatenate a pair of PWM
channels into one 16-bit channel. When channels 6 and 7are concatenated, channel 6 registers become the
high order bytes of the double byte channel. When channels 4 and 5 are concatenated, channel 4 registers
become the high order bytes of the double byte channel. When channels 2 and 3 are concatenated, channel

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 701



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

2 registers become the high order bytes of the double byte channel. When channels 0 and 1 are
concatenated, channel 0 registers become the high order bytes of the double byte channel.

See Section ********, “PWM 16-Bit Functions” for a more detailed description of the concatenation PWM
Function.

NOTE
Change these bits only when both corresponding channels are disabled.

Table 19-9. PWMCTL Field Descriptions

Field Description

7 Concatenate Channels 6 and 7
CON67 0 Channels 6 and 7 are separate 8-bit PWMs.

1 Channels 6 and 7 are concatenated to create one 16-bit PWM channel. Channel 6 becomes the high order
byte and channel 7 becomes the low order byte. Channel 7 output pin is used as the output for this 16-bit
PWM (bit 7 of port PWMP). Channel 7 clock select control-bit determines the clock source, channel 7 polarity
bit determines the polarity, channel 7 enable bit enables the output and channel 7 center aligned enable bit
determines the output mode.

6 Concatenate Channels 4 and 5
CON45 0 Channels 4 and 5 are separate 8-bit PWMs.

1 Channels 4 and 5 are concatenated to create one 16-bit PWM channel. Channel 4 becomes the high order
byte and channel 5 becomes the low order byte. Channel 5 output pin is used as the output for this 16-bit
PWM (bit 5 of port PWMP). Channel 5 clock select control-bit determines the clock source, channel 5 polarity
bit determines the polarity, channel 5 enable bit enables the output and channel 5 center aligned enable bit
determines the output mode.

5 Concatenate Channels 2 and 3
CON23 0 Channels 2 and 3 are separate 8-bit PWMs.

1 Channels 2 and 3 are concatenated to create one 16-bit PWM channel. Channel 2 becomes the high order
byte and channel 3 becomes the low order byte. Channel 3 output pin is used as the output for this 16-bit
PWM (bit 3 of port PWMP). Channel 3 clock select control-bit determines the clock source, channel 3 polarity
bit determines the polarity, channel 3 enable bit enables the output and channel 3 center aligned enable bit
determines the output mode.

4 Concatenate Channels 0 and 1
CON01 0 Channels 0 and 1 are separate 8-bit PWMs.

1 Channels 0 and 1 are concatenated to create one 16-bit PWM channel. Channel 0 becomes the high order
byte and channel 1 becomes the low order byte. Channel 1 output pin is used as the output for this 16-bit
PWM (bit 1 of port PWMP). Channel 1 clock select control-bit determines the clock source, channel 1 polarity
bit determines the polarity, channel 1 enable bit enables the output and channel 1 center aligned enable bit
determines the output mode.

3 PWM Stops in Wait Mode — Enabling this bit allows for lower power consumption in wait mode by disabling
PSWAI the input clock to the prescaler.

0 Allow the clock to the prescaler to continue while in wait mode.
1 Stop the input clock to the prescaler whenever the MCU is in wait mode.

2 PWM Counters Stop in Freeze Mode — In freeze mode, there is an option to disable the input clock to the
PFREZ prescaler by setting the PFRZ bit in the PWMCTL register. If this bit is set, whenever the MCU is in freeze mode,

the input clock to the prescaler is disabled. This feature is useful during emulation as it allows the PWM function
to be suspended. In this way, the counters of the PWM can be stopped while in freeze mode so that once normal
program flow is continued, the counters are re-enabled to simulate real-time operations. Since the registers can
still be accessed in this mode, to re-enable the prescaler clock, either disable the PFRZ bit or exit freeze mode.
0 Allow PWM to continue while in freeze mode.
1 Disable PWM input clock to the prescaler whenever the part is in freeze mode. This is useful for emulation.

MC9S12XE-Family Reference Manual  Rev. 1.25

702 Freescale Semiconductor



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

19.3.2.7 Reserved Register (PWMTST)
This register is reserved for factory testing of the PWM module and is not available in normal modes.

Module Base + 0x0006

7 6 5 4 3 2 1 0
R 0 0 0 0 0 0 0 0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 19-9. Reserved Register (PWMTST)

Read: Always read $00 in normal modes

Write: Unimplemented in normal modes

NOTE
Writing to this register when in special modes can alter the PWM
functionality.

19.3.2.8 Reserved Register (PWMPRSC)
This register is reserved for factory testing of the PWM module and is not available in normal modes.

Module Base + 0x0007

7 6 5 4 3 2 1 0
R 0 0 0 0 0 0 0 0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 19-10. Reserved Register (PWMPRSC)

Read: Always read $00 in normal modes

Write: Unimplemented in normal modes

NOTE
Writing to this register when in special modes can alter the PWM
functionality.

19.3.2.9 PWM Scale A Register (PWMSCLA)
PWMSCLA is the programmable scale value used in scaling clock A to generate clock SA. Clock SA is
generated by taking clock A, dividing it by the value in the PWMSCLA register and dividing that by two.

Clock SA = Clock A / (2 * PWMSCLA)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 703



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

NOTE
When PWMSCLA = $00, PWMSCLA value is considered a full scale value
of 256. Clock A is thus divided by 512.

Any value written to this register will cause the scale counter to load the new scale value (PWMSCLA).

Module Base + 0x0008

7 6 5 4 3 2 1 0
R

Bit 7  6  5  4  3  2  1  Bit 0
W

Reset 0 0 0 0 0 0 0 0

Figure 19-11. PWM Scale A Register (PWMSCLA)

Read: Anytime

Write: Anytime (causes the scale counter to load the PWMSCLA value)

19.3.2.10 PWM Scale B Register (PWMSCLB)
PWMSCLB is the programmable scale value used in scaling clock B to generate clock SB. Clock SB is
generated by taking clock B, dividing it by the value in the PWMSCLB register and dividing that by two.

Clock SB = Clock B / (2 * PWMSCLB)

NOTE
When PWMSCLB = $00, PWMSCLB value is considered a full scale value
of 256. Clock B is thus divided by 512.

Any value written to this register will cause the scale counter to load the new scale value (PWMSCLB).

Module Base + 0x0009

7 6 5 4 3 2 1 0
R

Bit 7  6  5  4  3  2  1  Bit 0
W

Reset 0 0 0 0 0 0 0 0

Figure 19-12. PWM Scale B Register (PWMSCLB)

Read: Anytime

Write: Anytime (causes the scale counter to load the PWMSCLB value).

19.3.2.11 Reserved Registers (PWMSCNTx)
The registers PWMSCNTA and PWMSCNTB are reserved for factory testing of the PWM module and are
not available in normal modes.

MC9S12XE-Family Reference Manual  Rev. 1.25

704 Freescale Semiconductor



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

Module Base + 0x000A, 0x000B

7 6 5 4 3 2 1 0
R 0 0 0 0 0 0 0 0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 19-13. Reserved Registers (PWMSCNTx)

Read: Always read $00 in normal modes

Write: Unimplemented in normal modes

NOTE
Writing to these registers when in special modes can alter the PWM
functionality.

19.3.2.12 PWM Channel Counter Registers (PWMCNTx)
Each channel has a dedicated 8-bit up/down counter which runs at the rate of the selected clock source.
The counter can be read at any time without affecting the count or the operation of the PWM channel. In
left aligned output mode, the counter counts from 0 to the value in the period register - 1. In center aligned
output mode, the counter counts from 0 up to the value in the period register and then back down to 0.

Any value written to the counter causes the counter to reset to $00, the counter direction to be set to up,
the immediate load of both duty and period registers with values from the buffers, and the output to change
according to the polarity bit. The counter is also cleared at the end of the effective period (see
Section ********, “Left Aligned Outputs” and Section ********, “Center Aligned Outputs” for more
details). When the channel is disabled (PWMEx = 0), the PWMCNTx register does not count. When a
channel becomes enabled (PWMEx = 1), the associated PWM counter starts at the count in the
PWMCNTx register. For more detailed information on the operation of the counters, see Section ********,
“PWM Timer Counters”.

In concatenated mode, writes to the 16-bit counter by using a 16-bit access or writes to either the low or
high order byte of the counter will reset the 16-bit counter. Reads of the 16-bit counter must be made by
16-bit access to maintain data coherency.

NOTE
Writing to the counter while the channel is enabled can cause an irregular PWM cycle to occur.

Module Base + 0x000C = PWMCNT0, 0x000D = PWMCNT1, 0x000E = PWMCNT2, 0x000F = PWMCNT3
Module Base + 0x0010 = PWMCNT4, 0x0011 = PWMCNT5, 0x0012 = PWMCNT6, 0x0013 = PWMCNT7

7 6 5 4 3 2 1 0
R Bit 7  6  5  4  3  2  1  Bit 0
W 0 0 0 0 0 0 0 0

Reset 0 0 0 0 0 0 0 0

Figure 19-14. PWM Channel Counter Registers (PWMCNTx)

Read: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 705



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

Write: Anytime (any value written causes PWM counter to be reset to $00).

19.3.2.13 PWM Channel Period Registers (PWMPERx)
There is a dedicated period register for each channel. The value in this register determines the period of
the associated PWM channel.

The period registers for each channel are double buffered so that if they change while the channel is
enabled, the change will NOT take effect until one of the following occurs:

• The effective period ends
• The counter is written (counter resets to $00)
• The channel is disabled

In this way, the output of the PWM will always be either the old waveform or the new waveform, not some
variation in between. If the channel is not enabled, then writes to the period register will go directly to the
latches as well as the buffer.

NOTE
Reads of this register return the most recent value written. Reads do not
necessarily return the value of the currently active period due to the double
buffering scheme.

See Section ********, “PWM Period and Duty” for more information.

To calculate the output period, take the selected clock source period for the channel of interest (A, B, SA,
or SB) and multiply it by the value in the period register for that channel:

• Left aligned output (CAEx = 0)
• PWMxPeriod=ChannelClockPeriod*PWMPERxCenterAlignedOutput(CAEx=1)

PWMx Period = Channel Clock Period * (2 * PWMPERx)

For boundary case programming values, please refer to Section ********, “PWM Boundary Cases”.

Module Base + 0x0014 = PWMPER0, 0x0015 = PWMPER1, 0x0016 = PWMPER2, 0x0017 = PWMPER3
Module Base + 0x0018 = PWMPER4, 0x0019 = PWMPER5, 0x001A = PWMPER6, 0x001B = PWMPER7

7 6 5 4 3 2 1 0
R

Bit 7  6  5  4  3  2  1  Bit 0
W

Reset 1 1 1 1 1 1 1 1

Figure 19-15. PWM Channel Period Registers (PWMPERx)

Read: Anytime

Write: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

706 Freescale Semiconductor



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

********* PWM Channel Duty Registers (PWMDTYx)
There is a dedicated duty register for each channel. The value in this register determines the duty of the
associated PWM channel. The duty value is compared to the counter and if it is equal to the counter value
a match occurs and the output changes state.

The duty registers for each channel are double buffered so that if they change while the channel is enabled,
the change will NOT take effect until one of the following occurs:

• The effective period ends
• The counter is written (counter resets to $00)
• The channel is disabled

In this way, the output of the PWM will always be either the old duty waveform or the new duty waveform,
not some variation in between. If the channel is not enabled, then writes to the duty register will go directly
to the latches as well as the buffer.

NOTE
Reads of this register return the most recent value written. Reads do not
necessarily return the value of the currently active duty due to the double
buffering scheme.

See Section ********, “PWM Period and Duty” for more information.

NOTE
Depending on the polarity bit, the duty registers will contain the count of
either the high time or the low time. If the polarity bit is one, the output starts
high and then goes low when the duty count is reached, so the duty registers
contain a count of the high time. If the polarity bit is zero, the output starts
low and then goes high when the duty count is reached, so the duty registers
contain a count of the low time.

To calculate the output duty cycle (high time as a% of period) for a particular channel:
• Polarity = 0 (PPOL x =0)

Duty Cycle = [(PWMPERx-PWMDTYx)/PWMPERx] * 100%
• Polarity = 1 (PPOLx = 1)

Duty Cycle = [PWMDTYx / PWMPERx] * 100%

For boundary case programming values, please refer to Section ********, “PWM Boundary Cases”.

Module Base + 0x001C = PWMDTY0, 0x001D = PWMDTY1, 0x001E = PWMDTY2, 0x001F = PWMDTY3
Module Base + 0x0020 = PWMDTY4, 0x0021 = PWMDTY5, 0x0022 = PWMDTY6, 0x0023 = PWMDTY7

7 6 5 4 3 2 1 0
R

Bit 7  6  5  4  3  2  1  Bit 0
W

Reset 1 1 1 1 1 1 1 1

Figure 19-16. PWM Channel Duty Registers (PWMDTYx)

Read: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 707



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

Write: Anytime

********* PWM Shutdown Register (PWMSDN)
The PWMSDN register provides for the shutdown functionality of the PWM module in the emergency
cases. For proper operation, channel 7 must be driven to the active level for a minimum of two bus clocks.

Module Base + 0x0024

7 6 5 4 3 2 1 0
R 0 0 PWM7IN

PWMIF PWMIE PWMLVL PWM7INL PWM7ENA
W PWMRSTRT

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 19-17. PWM Shutdown Register (PWMSDN)

Read: Anytime

Write: Anytime
Table 19-10. PWMSDN Field Descriptions

Field Description

7 PWM Interrupt Flag — Any change from passive to asserted (active) state or from active to passive state will
PWMIF be flagged by setting the PWMIF flag = 1. The flag is cleared by writing a logic 1 to it. Writing a 0 has no effect.

0 No change on PWM7IN input.
1 Change on PWM7IN input

6 PWM Interrupt Enable — If interrupt is enabled an interrupt to the CPU is asserted.
PWMIE 0 PWM interrupt is disabled.

1 PWM interrupt is enabled.

5 PWM Restart — The PWM can only be restarted if the PWM channel input 7 is de-asserted. After writing a logic
PWMRSTRT 1 to the PWMRSTRT bit (trigger event) the PWM channels start running after the corresponding counter passes

next “counter == 0” phase. Also, if the PWM7ENA bit is reset to 0, the PWM do not start before the counter
passes $00. The bit is always read as “0”.

4 PWM Shutdown Output Level If active level as defined by the PWM7IN input, gets asserted all enabled PWM
PWMLVL channels are immediately driven to the level defined by PWMLVL.

0 PWM outputs are forced to 0
1 Outputs are forced to 1.

2 PWM Channel 7 Input Status — This reflects the current status of the PWM7 pin.
PWM7IN

1 PWM Shutdown Active Input Level for Channel 7 — If the emergency shutdown feature is enabled
PWM7INL (PWM7ENA = 1), this bit determines the active level of the PWM7channel.

0 Active level is low
1 Active level is high

0 PWM Emergency Shutdown Enable — If this bit is logic 1, the pin associated with channel 7 is forced to input
PWM7ENA and the emergency shutdown feature is enabled. All the other bits in this register are meaningful only if

PWM7ENA = 1.
0 PWM emergency feature disabled.
1 PWM emergency feature is enabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

708 Freescale Semiconductor



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

19.4 Functional Description

19.4.1 PWM Clock Select
There are four available clocks: clock A, clock B, clock SA (scaled A), and clock SB (scaled B). These
four clocks are based on the bus clock.

Clock A and B can be software selected to be 1, 1/2, 1/4, 1/8,..., 1/64, 1/128 times the bus clock. Clock SA
uses clock A as an input and divides it further with a reloadable counter. Similarly, clock SB uses clock B
as an input and divides it further with a reloadable counter. The rates available for clock SA are software
selectable to be clock A divided by 2, 4, 6, 8,..., or 512 in increments of divide by 2. Similar rates are
available for clock SB. Each PWM channel has the capability of selecting one of two clocks, either the
pre-scaled clock (clock A or B) or the scaled clock (clock SA or SB).

The block diagram in Figure 19-18 shows the four different clocks and how the scaled clocks are created.

******** Prescale
The input clock to the PWM prescaler is the bus clock. It can be disabled whenever the part is in freeze
mode by setting the PFRZ bit in the PWMCTL register. If this bit is set, whenever the MCU is in freeze
mode (freeze mode signal active) the input clock to the prescaler is disabled. This is useful for emulation
in order to freeze the PWM. The input clock can also be disabled when all eight PWM channels are
disabled (PWME7-0 = 0). This is useful for reducing power by disabling the prescale counter.

Clock A and clock B are scaled values of the input clock. The value is software selectable for both clock
A and clock B and has options of 1, 1/2, 1/4, 1/8, 1/16, 1/32, 1/64, or 1/128 times the bus clock. The value
selected for clock A is determined by the PCKA2, PCKA1, PCKA0 bits in the PWMPRCLK register. The
value selected for clock B is determined by the PCKB2, PCKB1, PCKB0 bits also in the PWMPRCLK
register.

19.4.1.2 Clock Scale
The scaled A clock uses clock A as an input and divides it further with a user programmable value and
then divides this by 2. The scaled B clock uses clock B as an input and divides it further with a user
programmable value and then divides this by 2. The rates available for clock SA are software selectable to
be clock A divided by 2, 4, 6, 8,..., or 512 in increments of divide by 2. Similar rates are available for clock
SB.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 709



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

Clock A M
U Clock to

Clock A/2, A/4, A/6,....A/512 X PWM Ch 0

PCLK0
8-Bit Down Count = 1

Counter M
U Clock to
X PWM Ch 1

Load

PWMSCLA DIV 2 Clock SA PCLK1

M
M U Clock to

X PWM Ch 2
U

X PCLK2

M
U Clock to
X PWM Ch 3

PCLK3
Clock B

M
Clock B/2, B/4, B/6,....B/512 U Clock to

X PWM Ch 4

M PCLK4
8-Bit Down Count = 1

U Counter M
U Clock to

X X PWM Ch 5
Load

Clock SB PCLK5
PWMSCLB DIV 2

M
U Clock to
X PWM Ch 6

PCLK6

M
U Clock to
X PWM Ch 7

PCLK7

Prescale Scale Clock Select

Figure 19-18.  PWM Clock Select Block Diagram

MC9S12XE-Family Reference Manual  Rev. 1.25

710 Freescale Semiconductor

Bus Clock
PFRZ Divide by

Freeze Mode Signal Prescaler Taps:

PWME7-0 2 4 8 16 32 64 128

PCKB2 PCKA2
PCKB1 PCKA1
PCKB0 PCKA0



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

Clock A is used as an input to an 8-bit down counter. This down counter loads a user programmable scale
value from the scale register (PWMSCLA). When the down counter reaches one, a pulse is output and the
8-bit counter is re-loaded. The output signal from this circuit is further divided by two. This gives a greater
range with only a slight reduction in granularity. Clock SA equals clock A divided by two times the value
in the PWMSCLA register.

NOTE
Clock SA = Clock A / (2 * PWMSCLA)

When PWMSCLA = $00, PWMSCLA value is considered a full scale value
of 256. Clock A is thus divided by 512.

Similarly, clock B is used as an input to an 8-bit down counter followed by a divide by two producing clock
SB. Thus, clock SB equals clock B divided by two times the value in the PWMSCLB register.

NOTE
Clock SB = Clock B / (2 * PWMSCLB)

When PWMSCLB = $00, PWMSCLB value is considered a full scale value
of 256. Clock B is thus divided by 512.

As an example, consider the case in which the user writes $FF into the PWMSCLA register. Clock A for
this case will be E divided by 4. A pulse will occur at a rate of once every 255x4 E cycles. Passing this
through the divide by two circuit produces a clock signal at an E divided by 2040 rate. Similarly, a value
of $01 in the PWMSCLA register when clock A is E divided by 4 will produce a clock at an E divided by
8 rate.

 Writing to PWMSCLA or PWMSCLB causes the associated 8-bit down counter to be re-loaded.
Otherwise, when changing rates the counter would have to count down to $01 before counting at the proper
rate. Forcing the associated counter to re-load the scale register value every time PWMSCLA or
PWMSCLB is written prevents this.

NOTE
Writing to the scale registers while channels are operating can cause
irregularities in the PWM outputs.

19.4.1.3 Clock Select
Each PWM channel has the capability of selecting one of two clocks. For channels 0, 1, 4, and 5 the clock
choices are clock A or clock SA. For channels 2, 3, 6, and 7 the choices are clock B or clock SB. The clock
selection is done with the PCLKx control bits in the PWMCLK register.

NOTE
Changing clock control bits while channels are operating can cause
irregularities in the PWM outputs.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 711



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

19.4.2 PWM Channel Timers
The main part of the PWM module are the actual timers. Each of the timer channels has a counter, a period
register and a duty register (each are 8-bit). The waveform output period is controlled by a match between
the period register and the value in the counter. The duty is controlled by a match between the duty register
and the counter value and causes the state of the output to change during the period. The starting polarity
of the output is also selectable on a per channel basis. Shown below in Figure 19-19 is the block diagram
for the PWM timer.

Clock Source

From Port PWMP
8-Bit Counter Data Register

Gate
PWMCNTx

(Clock Edge
Sync)

Up/Down Reset 8-bit Compare =
T Q M M

PWMDTYx U U
Q X X To Pin

R Driver
8-bit Compare =

PWMPERx
PPOLx

Q T
CAEx

Q
R

PWMEx
Figure 19-19.  PWM Timer Channel Block Diagram

19.4.2.1 PWM Enable
Each PWM channel has an enable bit (PWMEx) to start its waveform output. When any of the PWMEx
bits are set (PWMEx = 1), the associated PWM output signal is enabled immediately. However, the actual
PWM waveform is not available on the associated PWM output until its clock source begins its next cycle
due to the synchronization of PWMEx and the clock source. An exception to this is when channels are
concatenated. Refer to Section ********, “PWM 16-Bit Functions” for more detail.

NOTE
The first PWM cycle after enabling the channel can be irregular.

MC9S12XE-Family Reference Manual  Rev. 1.25

712 Freescale Semiconductor



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

On the front end of the PWM timer, the clock is enabled to the PWM circuit by the PWMEx bit being high.
There is an edge-synchronizing circuit to guarantee that the clock will only be enabled or disabled at an
edge. When the channel is disabled (PWMEx = 0), the counter for the channel does not count.

******** PWM Polarity
Each channel has a polarity bit to allow starting a waveform cycle with a high or low signal. This is shown
on the block diagram as a mux select of either the Q output or the Q output of the PWM output flip flop.
When one of the bits in the PWMPOL register is set, the associated PWM channel output is high at the
beginning of the waveform, then goes low when the duty count is reached. Conversely, if the polarity bit
is zero, the output starts low and then goes high when the duty count is reached.

******** PWM Period and Duty
Dedicated period and duty registers exist for each channel and are double buffered so that if they change
while the channel is enabled, the change will NOT take effect until one of the following occurs:

• The effective period ends
• The counter is written (counter resets to $00)
• The channel is disabled

In this way, the output of the PWM will always be either the old waveform or the new waveform, not some
variation in between. If the channel is not enabled, then writes to the period and duty registers will go
directly to the latches as well as the buffer.

A change in duty or period can be forced into effect “immediately” by writing the new value to the duty
and/or period registers and then writing to the counter. This forces the counter to reset and the new duty
and/or period values to be latched. In addition, since the counter is readable, it is possible to know where
the count is with respect to the duty value and software can be used to make adjustments

NOTE
When forcing a new period or duty into effect immediately, an irregular
PWM cycle can occur.

Depending on the polarity bit, the duty registers will contain the count of
either the high time or the low time.

******** PWM Timer Counters
Each channel has a dedicated 8-bit up/down counter which runs at the rate of the selected clock source (see
Section 19.4.1, “PWM Clock Select” for the available clock sources and rates). The counter compares to
two registers, a duty register and a period register as shown in Figure 19-19. When the PWM counter
matches the duty register, the output flip-flop changes state, causing the PWM waveform to also change
state. A match between the PWM counter and the period register behaves differently depending on what
output mode is selected as shown in Figure 19-19 and described in Section ********, “Left Aligned
Outputs” and Section ********, “Center Aligned Outputs”.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 713



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

Each channel counter can be read at anytime without affecting the count or the operation of the PWM
channel.

Any value written to the counter causes the counter to reset to $00, the counter direction to be set to up,
the immediate load of both duty and period registers with values from the buffers, and the output to change
according to the polarity bit. When the channel is disabled (PWMEx = 0), the counter stops. When a
channel becomes enabled (PWMEx = 1), the associated PWM counter continues from the count in the
PWMCNTx register. This allows the waveform to continue where it left off when the channel is re-
enabled. When the channel is disabled, writing “0” to the period register will cause the counter to reset on
the next selected clock.

NOTE
If the user wants to start a new “clean” PWM waveform without any
“history” from the old waveform, the user must write to channel counter
(PWMCNTx) prior to enabling the PWM channel (PWMEx = 1).

Generally, writes to the counter are done prior to enabling a channel in order to start from a known state.
However, writing a counter can also be done while the PWM channel is enabled (counting). The effect is
similar to writing the counter when the channel is disabled, except that the new period is started
immediately with the output set according to the polarity bit.

NOTE
Writing to the counter while the channel is enabled can cause an irregular
PWM cycle to occur.

The counter is cleared at the end of the effective period (see Section ********, “Left Aligned Outputs” and
Section ********, “Center Aligned Outputs” for more details).

Table 19-11. PWM Timer Counter Conditions

Counter Clears ($00) Counter Counts Counter Stops

When PWMCNTx register written to When PWM channel is enabled When PWM channel is disabled
any value (PWMEx = 1). Counts from last value in (PWMEx = 0)

Effective period ends PWMCNTx.

******** Left Aligned Outputs
The PWM timer provides the choice of two types of outputs, left aligned or center aligned. They are
selected with the CAEx bits in the PWMCAE register. If the CAEx bit is cleared (CAEx = 0), the
corresponding PWM output will be left aligned.

In left aligned output mode, the 8-bit counter is configured as an up counter only. It compares to two
registers, a duty register and a period register as shown in the block diagram in Figure 19-19. When the
PWM counter matches the duty register the output flip-flop changes state causing the PWM waveform to
also change state. A match between the PWM counter and the period register resets the counter and the
output flip-flop, as shown in Figure 19-19, as well as performing a load from the double buffer period and
duty register to the associated registers, as described in Section ********, “PWM Period and Duty”. The
counter counts from 0 to the value in the period register – 1.

MC9S12XE-Family Reference Manual  Rev. 1.25

714 Freescale Semiconductor



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

NOTE
Changing the PWM output mode from left aligned to center aligned output
(or vice versa) while channels are operating can cause irregularities in the
PWM output. It is recommended to program the output mode before
enabling the PWM channel.

PPOLx = 0

PPOLx = 1
PWMDTYx

Period = PWMPERx

Figure 19-20.  PWM Left Aligned Output Waveform

To calculate the output frequency in left aligned output mode for a particular channel, take the selected
clock source frequency for the channel (A, B, SA, or SB) and divide it by the value in the period register
for that channel.

• PWMx Frequency = Clock (A, B, SA, or SB) / PWMPERx
• PWMx Duty Cycle (high time as a% of period):

— Polarity = 0 (PPOLx = 0)
• Duty Cycle = [(PWMPERx-PWMDTYx)/PWMPERx] * 100%

— Polarity = 1 (PPOLx = 1)
Duty Cycle = [PWMDTYx / PWMPERx] * 100%

As an example of a left aligned output, consider the following case:
Clock Source = E, where E = 10 MHz (100 ns period)

PPOLx = 0
PWMPERx = 4
PWMDTYx = 1

PWMx Frequency = 10 MHz/4 = 2.5 MHz
PWMx Period = 400 ns
PWMx Duty Cycle = 3/4 *100% = 75%

The output waveform generated is shown in Figure 19-21.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 715



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

E = 100 ns

Duty Cycle = 75%

Period = 400 ns

Figure 19-21.  PWM Left Aligned Output Example Waveform

******** Center Aligned Outputs
For center aligned output mode selection, set the CAEx bit (CAEx = 1) in the PWMCAE register and the
corresponding PWM output will be center aligned.

The 8-bit counter operates as an up/down counter in this mode and is set to up whenever the counter is
equal to $00. The counter compares to two registers, a duty register and a period register as shown in the
block diagram in Figure 19-19. When the PWM counter matches the duty register, the output flip-flop
changes state, causing the PWM waveform to also change state. A match between the PWM counter and
the period register changes the counter direction from an up-count to a down-count. When the PWM
counter decrements and matches the duty register again, the output flip-flop changes state causing the
PWM output to also change state. When the PWM counter decrements and reaches zero, the counter
direction changes from a down-count back to an up-count and a load from the double buffer period and
duty registers to the associated registers is performed, as described in Section ********, “PWM Period and
Duty”. The counter counts from 0 up to the value in the period register and then back down to 0. Thus the
effective period is PWMPERx*2.

NOTE
Changing the PWM output mode from left aligned to center aligned output
(or vice versa) while channels are operating can cause irregularities in the
PWM output. It is recommended to program the output mode before
enabling the PWM channel.

PPOLx = 0

PPOLx = 1

PWMDTYx PWMDTYx

PWMPERx PWMPERx

Period = PWMPERx*2

Figure 19-22.  PWM Center Aligned Output Waveform

MC9S12XE-Family Reference Manual  Rev. 1.25

716 Freescale Semiconductor



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

To calculate the output frequency in center aligned output mode for a particular channel, take the selected
clock source frequency for the channel (A, B, SA, or SB) and divide it by twice the value in the period
register for that channel.

• PWMx Frequency = Clock (A, B, SA, or SB) / (2*PWMPERx)
• PWMx Duty Cycle (high time as a% of period):

— Polarity = 0 (PPOLx = 0)
Duty Cycle = [(PWMPERx-PWMDTYx)/PWMPERx] * 100%
— Polarity = 1 (PPOLx = 1)
Duty Cycle = [PWMDTYx / PWMPERx] * 100%

As an example of a center aligned output, consider the following case:
Clock Source = E, where E = 10 MHz (100 ns period)

PPOLx = 0
PWMPERx = 4
PWMDTYx = 1

PWMx Frequency = 10 MHz/8 = 1.25 MHz
PWMx Period = 800 ns
PWMx Duty Cycle = 3/4 *100% = 75%

Shown in Figure 19-23 is the output waveform generated.

E = 100 ns E = 100 ns

DUTY CYCLE = 75%

PERIOD = 800 ns

Figure 19-23.  PWM Center Aligned Output Example Waveform

******** PWM 16-Bit Functions
The PWM timer also has the option of generating 8-channels of 8-bits or 4-channels of 16-bits for greater
PWM resolution. This 16-bit channel option is achieved through the concatenation of two 8-bit channels.

The PWMCTL register contains four control bits, each of which is used to concatenate a pair of PWM
channels into one 16-bit channel. Channels 6 and 7 are concatenated with the CON67 bit, channels 4 and
5 are concatenated with the CON45 bit, channels 2 and 3 are concatenated with the CON23 bit, and
channels 0 and 1 are concatenated with the CON01 bit.

NOTE
Change these bits only when both corresponding channels are disabled.

When channels 6 and 7 are concatenated, channel 6 registers become the high order bytes of the double
byte channel, as shown in Figure 19-24. Similarly, when channels 4 and 5 are concatenated, channel 4
registers become the high order bytes of the double byte channel. When channels 2 and 3 are concatenated,

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 717



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

channel 2 registers become the high order bytes of the double byte channel. When channels 0 and 1 are
concatenated, channel 0 registers become the high order bytes of the double byte channel.

When using the 16-bit concatenated mode, the clock source is determined by the low order 8-bit channel
clock select control bits. That is channel 7 when channels 6 and 7 are concatenated, channel 5 when
channels 4 and 5 are concatenated, channel 3 when channels 2 and 3 are concatenated, and channel 1 when
channels 0 and 1 are concatenated. The resulting PWM is output to the pins of the corresponding low order
8-bit channel as also shown in Figure 19-24. The polarity of the resulting PWM output is controlled by the
PPOLx bit of the corresponding low order 8-bit channel as well.

MC9S12XE-Family Reference Manual  Rev. 1.25

718 Freescale Semiconductor



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

Clock Source 7
High Low

PWMCNT6 PWCNT7

Period/Duty Compare PWM7

Clock Source 5
High Low

PWMCNT4 PWCNT5

Period/Duty Compare PWM5

Clock Source 3
High Low

PWMCNT2 PWCNT3

Period/Duty Compare PWM3

Clock Source 1
High Low

PWMCNT0 PWCNT1

Period/Duty Compare PWM1

Figure 19-24.  PWM 16-Bit Mode

Once concatenated mode is enabled (CONxx bits set in PWMCTL register), enabling/disabling the
corresponding 16-bit PWM channel is controlled by the low order PWMEx bit. In this case, the high order
bytes PWMEx bits have no effect and their corresponding PWM output is disabled.

In concatenated mode, writes to the 16-bit counter by using a 16-bit access or writes to either the low or
high order byte of the counter will reset the 16-bit counter. Reads of the 16-bit counter must be made by
16-bit access to maintain data coherency.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 719



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

Either left aligned or center aligned output mode can be used in concatenated mode and is controlled by
the low order CAEx bit. The high order CAEx bit has no effect.

Table 19-12 is used to summarize which channels are used to set the various control bits when in 16-bit
mode.

Table 19-12. 16-bit Concatenation Mode Summary

PWMx
CONxx PWMEx PPOLx PCLKx CAEx

Output

CON67 PWME7 PPOL7 PCLK7 CAE7 PWM7
CON45 PWME5 PPOL5 PCLK5 CAE5 PWM5
CON23 PWME3 PPOL3 PCLK3 CAE3 PWM3
CON01 PWME1 PPOL1 PCLK1 CAE1 PWM1

******** PWM Boundary Cases
Table 19-13 summarizes the boundary conditions for the PWM regardless of the output mode (left aligned
or center aligned) and 8-bit (normal) or 16-bit (concatenation).

Table 19-13. PWM Boundary Cases

PWMDTYx PWMPERx PPOLx PWMx Output

$00 >$00 1 Always low
(indicates no duty)

$00 >$00 0 Always high
(indicates no duty)

XX $00(1) 1 Always high
(indicates no period)

XX $001 0 Always low
(indicates no period)

>= PWMPERx XX 1 Always high
>= PWMPERx XX 0 Always low

1. Counter = $00 and does not count.

19.5 Resets
The reset state of each individual bit is listed within the Section 19.3.2, “Register Descriptions” which
details the registers and their bit-fields. All special functions or modes which are initialized during or just
following reset are described within this section.

• The 8-bit up/down counter is configured as an up counter out of reset.
• All the channels are disabled and all the counters do not count.

MC9S12XE-Family Reference Manual  Rev. 1.25

720 Freescale Semiconductor



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

19.6 Interrupts
The PWM module has only one interrupt which is generated at the time of emergency shutdown, if the
corresponding enable bit (PWMIE) is set. This bit is the enable for the interrupt. The interrupt flag PWMIF
is set whenever the input level of the PWM7 channel changes while PWM7ENA = 1 or when PWMENA
is being asserted while the level at PWM7 is active.

In stop mode or wait mode (with the PSWAI bit set), the emergency shutdown feature will drive the PWM
outputs to their shutdown output levels but the PWMIF flag will not be set.

A description of the registers involved and affected due to this interrupt is explained in Section *********,
“PWM Shutdown Register (PWMSDN)”.

The PWM block only generates the interrupt and does not service it. The interrupt signal name is PWM
interrupt signal.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 721



Chapter 19 Pulse-Width Modulator (S12PWM8B8CV1)

MC9S12XE-Family Reference Manual  Rev. 1.25

722 Freescale Semiconductor