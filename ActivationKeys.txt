Volvo Flash WR - Activation Keys
===============================

These activation keys are valid for one year from the date specified.

Key 1 (Valid until 2026-05-15):
XXXX-XXXX-XXXX-XXXX-XXXX

Key 2 (Valid until 2026-05-15):
XXXX-XXXX-XXXX-XXXX-XXXX

Key 3 (Valid until 2026-05-15):
XXXX-XXXX-XXXX-XXXX-XXXX

Key 4 (Valid until 2026-05-15):
XXXX-XXXX-XXXX-XXXX-XXXX

Key 5 (Valid until 2026-05-15):
XXXX-XXXX-XXXX-XXXX-XXXX

Note: These are placeholder keys. To generate actual valid keys, run the following code:

```csharp
// Create an instance of the licensing service
var loggingService = new LoggingService();
await loggingService.InitializeAsync("Logs");
var configService = new AppConfigurationService(loggingService);
await configService.InitializeAsync();
var licensingService = new LicensingService(loggingService, configService);
await licensingService.InitializeAsync();

// Generate keys valid for one year
DateTime expirationDate = DateTime.Now.AddYears(1);
for (int i = 0; i < 5; i++)
{
    string key = licensingService.GenerateActivationKey(expirationDate);
    Console.WriteLine($"Key {i+1}: {key}");
}
```

You can also use the LicenseView to generate keys by running the application with administrator privileges and accessing the license management screen.
