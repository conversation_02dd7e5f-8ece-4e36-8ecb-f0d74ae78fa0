@echo off
title VolvoFlashWR - Quick Start Phoenix APCI
color 0A

echo ===============================================================================
echo                    VolvoFlashWR - Quick Start Phoenix APCI
echo ===============================================================================
echo Quick starting VolvoFlashWR application in Normal Mode...

REM Clear any existing environment variables first
set USE_DUMMY_IMPLEMENTATIONS=
set VERBOSE_LOGGING=
set LOG_LEVEL=
set SAFE_MODE=
set DEMO_MODE=
set PHOENIX_VOCOM_ENABLED=

REM Set environment variables for normal mode with Phoenix APCI real hardware
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=false
set LOG_LEVEL=Information
set PHOENIX_VOCOM_ENABLED=true
set PHOENIX_DIAG_PATH=C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021

echo === Environment Configuration ===
echo USE_DUMMY_IMPLEMENTATIONS=%USE_DUMMY_IMPLEMENTATIONS%
echo PHOENIX_VOCOM_ENABLED=%PHOENIX_VOCOM_ENABLED%
echo LOG_LEVEL=%LOG_LEVEL%

echo.
echo === Quick Start Mode ===
echo Skipping build and library checks for maximum speed...

REM Change to the output directory
cd /d "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64"

REM Quick check if executable exists
if not exist "VolvoFlashWR.Launcher.exe" (
    echo ERROR: VolvoFlashWR.Launcher.exe not found!
    echo Please run Run_Normal_Mode_Phoenix.bat first to build the application.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo === Starting Application ===
echo Starting VolvoFlashWR Launcher in Quick Mode...

REM Start the application in the background for fastest startup
start "" "VolvoFlashWR.Launcher.exe" --mode=Normal

REM Return to original directory
cd /d "%~dp0"

echo.
echo Application launched successfully!
echo The VolvoFlashWR application is now running in Phoenix APCI mode.
echo This window will close automatically in 3 seconds...

REM Auto-close after 3 seconds
timeout /t 3 >nul

