using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Win32;
using Microsoft.VisualBasic;
using MessageBox = System.Windows.MessageBox;
using SaveFileDialog = Microsoft.Win32.SaveFileDialog;
using OpenFileDialog = Microsoft.Win32.OpenFileDialog;
using CoreDialogService = VolvoFlashWR.Core.Interfaces.IDialogService;

namespace VolvoFlashWR.UI.Services
{
    /// <summary>
    /// Implementation of the IDialogService interfaces
    /// </summary>
    public class DialogService : IDialogService, CoreDialogService
    {
        /// <summary>
        /// Shows an information message to the user
        /// </summary>
        public void ShowInformation(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// Shows an error message to the user
        /// </summary>
        public void ShowError(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// Shows a warning message to the user
        /// </summary>
        public void ShowWarning(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        /// <summary>
        /// Shows a confirmation dialog to the user
        /// </summary>
        public bool ShowConfirmation(string title, string message)
        {
            return MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes;
        }

        /// <summary>
        /// Shows an input dialog to the user
        /// </summary>
        public string ShowInputDialog(string title, string message, string defaultValue = "")
        {
            // Simple implementation using a prompt dialog
            // In a real application, you would create a custom dialog
            return Microsoft.VisualBasic.Interaction.InputBox(message, title, defaultValue);
        }

        /// <summary>
        /// Shows a file open dialog to the user
        /// </summary>
        /// <returns>The selected file path, or null if canceled</returns>
        public string? ShowOpenFileDialog(string title, string filter, string defaultExt = "")
        {
            var dialog = new OpenFileDialog
            {
                Title = title,
                Filter = filter,
                DefaultExt = defaultExt,
                CheckFileExists = true
            };

            return dialog.ShowDialog() == true ? dialog.FileName : null;
        }

        /// <summary>
        /// Shows a file save dialog to the user
        /// </summary>
        /// <returns>The selected file path, or null if canceled</returns>
        public string? ShowSaveFileDialog(string title, string filter, string defaultExt = "", string defaultFileName = "")
        {
            var dialog = new SaveFileDialog
            {
                Title = title,
                Filter = filter,
                DefaultExt = defaultExt,
                FileName = defaultFileName,
                OverwritePrompt = true
            };

            return dialog.ShowDialog() == true ? dialog.FileName : null;
        }

        /// <summary>
        /// Shows a folder browser dialog to the user
        /// </summary>
        public string ShowFolderBrowserDialog(string title, string initialFolder = "")
        {
            // Use the Windows API Code Pack for a modern folder browser
            // For simplicity in this example, we'll use a simple input dialog
            return Interaction.InputBox(title, "Enter folder path:", initialFolder);
        }

        /// <summary>
        /// Shows a progress dialog to the user
        /// </summary>
        public async Task<bool> ShowProgressDialog(string title, string message, Func<IProgress<double>, Task> operation, bool canCancel = true)
        {
            // In a real application, you would create a custom progress dialog
            // For now, we'll just execute the operation without a dialog
            var progress = new Progress<double>();
            try
            {
                await operation(progress);
                return true;
            }
            catch
            {
                return false;
            }
        }

        #region Core.Interfaces.IDialogService Implementation

        /// <summary>
        /// Shows a message dialog
        /// </summary>
        public Task ShowMessageAsync(string message, string title)
        {
            return Task.Run(() => ShowInformation(title, message));
        }

        /// <summary>
        /// Shows a confirmation dialog
        /// </summary>
        public Task<bool> ShowConfirmationAsync(string message, string title)
        {
            return Task.FromResult(ShowConfirmation(title, message));
        }

        /// <summary>
        /// Shows a confirmation dialog (synchronous version)
        /// </summary>
        bool CoreDialogService.ShowConfirmation(string message, string title)
        {
            return ShowConfirmation(title, message);
        }

        /// <summary>
        /// Shows an error dialog
        /// </summary>
        public Task ShowErrorAsync(string message, string title)
        {
            return Task.Run(() => ShowError(title, message));
        }

        /// <summary>
        /// Shows an error dialog (synchronous version)
        /// </summary>
        void CoreDialogService.ShowError(string message, string title)
        {
            ShowError(title, message);
        }

        /// <summary>
        /// Shows an information dialog
        /// </summary>
        void CoreDialogService.ShowInformation(string message, string title)
        {
            ShowInformation(title, message);
        }

        /// <summary>
        /// Shows a dialog to select a file to open
        /// </summary>
        /// <returns>The selected file path, or null if canceled</returns>
        public Task<string?> ShowOpenFileDialogAsync(string filter, string title)
        {
            return Task.FromResult(ShowOpenFileDialog(title, filter));
        }

        /// <summary>
        /// Shows a dialog to select multiple files to open
        /// </summary>
        public Task<List<string>> ShowOpenFilesDialogAsync(string filter, string title)
        {
            var dialog = new OpenFileDialog
            {
                Title = title,
                Filter = filter,
                CheckFileExists = true,
                Multiselect = true
            };

            if (dialog.ShowDialog() == true)
            {
                return Task.FromResult(new List<string>(dialog.FileNames));
            }

            return Task.FromResult(new List<string>());
        }

        /// <summary>
        /// Shows a dialog to select a file to save
        /// </summary>
        /// <returns>The selected file path, or null if canceled</returns>
        public Task<string?> ShowSaveFileDialogAsync(string filter, string title, string defaultFileName = "")
        {
            return Task.FromResult(ShowSaveFileDialog(title, filter, "", defaultFileName));
        }

        /// <summary>
        /// Shows a dialog to select a folder
        /// </summary>
        public Task<string?> ShowFolderBrowserDialogAsync(string title)
        {
            return Task.FromResult(ShowFolderBrowserDialog(title));
        }

        /// <summary>
        /// Shows an input dialog
        /// </summary>
        public Task<string?> ShowInputDialogAsync(string message, string title, string defaultValue = "")
        {
            return Task.FromResult(ShowInputDialog(title, message, defaultValue));
        }

        /// <summary>
        /// Shows an input dialog (synchronous version)
        /// </summary>
        string CoreDialogService.ShowInputDialog(string message, string title, string defaultValue)
        {
            return ShowInputDialog(title, message, defaultValue);
        }

        #endregion
    }
}
