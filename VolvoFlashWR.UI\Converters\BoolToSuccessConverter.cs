using System;
using System.Globalization;
using System.Windows.Data;

namespace VolvoFlashWR.UI.Converters
{
    public class BoolToSuccessConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? "Success" : "Failure";
            }

            return "Unknown";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                return stringValue.Equals("Success", StringComparison.OrdinalIgnoreCase);
            }

            return false;
        }
    }
}
