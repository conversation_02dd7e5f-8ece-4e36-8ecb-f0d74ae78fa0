namespace VolvoFlashWR.Core.Enums
{
    /// <summary>
    /// Represents the communication protocol used for ECU communication
    /// </summary>
    public enum CommunicationProtocol
    {
        /// <summary>
        /// Controller Area Network protocol
        /// </summary>
        CAN,

        /// <summary>
        /// Serial Peripheral Interface protocol
        /// </summary>
        SPI,

        /// <summary>
        /// Serial Communication Interface protocol
        /// </summary>
        SCI,

        /// <summary>
        /// Inter-Integrated Circuit protocol
        /// </summary>
        IIC,

        /// <summary>
        /// J1939 protocol (higher-level protocol that uses CAN)
        /// </summary>
        J1939
    }
}
