using System;
using Microsoft.Extensions.Logging;
using MSLogLevel = Microsoft.Extensions.Logging.LogLevel;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Utilities
{
    /// <summary>
    /// Adapter to convert between ILoggingService and Microsoft.Extensions.Logging.ILogger
    /// </summary>
    public static class LoggerAdapter
    {
        /// <summary>
        /// Creates a Microsoft.Extensions.Logging.ILogger from an ILoggingService
        /// </summary>
        /// <param name="loggingService">The logging service</param>
        /// <returns>The Microsoft.Extensions.Logging.ILogger</returns>
        public static ILogger CreateLogger(ILoggingService loggingService)
        {
            return new LoggingServiceAdapter(loggingService);
        }

        /// <summary>
        /// Adapter class to convert ILoggingService to Microsoft.Extensions.Logging.ILogger
        /// </summary>
        private class LoggingServiceAdapter : ILogger
        {
            private readonly ILoggingService _loggingService;

            /// <summary>
            /// Initializes a new instance of the LoggingServiceAdapter class
            /// </summary>
            /// <param name="loggingService">The logging service</param>
            public LoggingServiceAdapter(ILoggingService loggingService)
            {
                _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            }

            /// <summary>
            /// Begins a logical operation scope
            /// </summary>
            /// <typeparam name="TState">The type of the state</typeparam>
            /// <param name="state">The identifier for the scope</param>
            /// <returns>A disposable object that ends the logical operation scope on dispose</returns>
            public IDisposable BeginScope<TState>(TState state)
            {
                return new NoopDisposable();
            }

            /// <summary>
            /// Checks if the given LogLevel is enabled
            /// </summary>
            /// <param name="logLevel">The log level</param>
            /// <returns>True if enabled, false otherwise</returns>
            public bool IsEnabled(MSLogLevel logLevel)
            {
                return true;
            }

            /// <summary>
            /// Writes a log entry
            /// </summary>
            /// <typeparam name="TState">The type of the state</typeparam>
            /// <param name="logLevel">The log level</param>
            /// <param name="eventId">The event ID</param>
            /// <param name="state">The entry to be written</param>
            /// <param name="exception">The exception related to this entry</param>
            /// <param name="formatter">Function to create a string message of the state and exception</param>
            public void Log<TState>(MSLogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
            {
                if (!IsEnabled(logLevel))
                {
                    return;
                }

                string message = formatter(state, exception);
                string source = "LoggerAdapter";

                switch (logLevel)
                {
                    case MSLogLevel.Trace:
                    case MSLogLevel.Debug:
                        _loggingService.LogDebug(message, source);
                        break;
                    case MSLogLevel.Information:
                        _loggingService.LogInformation(message, source);
                        break;
                    case MSLogLevel.Warning:
                        _loggingService.LogWarning(message, source);
                        break;
                    case MSLogLevel.Error:
                    case MSLogLevel.Critical:
                        _loggingService.LogError(message, source, exception);
                        break;
                    default:
                        _loggingService.LogInformation(message, source);
                        break;
                }
            }

            /// <summary>
            /// No-op disposable for scope
            /// </summary>
            private class NoopDisposable : IDisposable
            {
                /// <summary>
                /// Disposes the object
                /// </summary>
                public void Dispose()
                {
                    // No-op
                }
            }
        }
    }
}
