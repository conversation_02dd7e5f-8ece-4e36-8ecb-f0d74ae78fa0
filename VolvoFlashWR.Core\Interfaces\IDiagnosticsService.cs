using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for diagnostics service
    /// </summary>
    public interface IDiagnosticsService
    {
        /// <summary>
        /// Event triggered when a diagnostic event occurs
        /// </summary>
        event EventHandler<DiagnosticEvent> DiagnosticEventOccurred;

        /// <summary>
        /// Initializes the diagnostics service
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        Task<bool> InitializeAsync();

        /// <summary>
        /// Records a diagnostic event
        /// </summary>
        /// <param name="eventType">The type of the event</param>
        /// <param name="source">The source of the event</param>
        /// <param name="message">The event message</param>
        /// <param name="data">Additional event data</param>
        void RecordEvent(DiagnosticEventType eventType, string source, string message, Dictionary<string, object> data = null);

        /// <summary>
        /// Gets all diagnostic events
        /// </summary>
        /// <returns>List of all diagnostic events</returns>
        Task<List<DiagnosticEvent>> GetAllEventsAsync();

        /// <summary>
        /// Gets diagnostic events filtered by event type
        /// </summary>
        /// <param name="eventType">The event type to filter by</param>
        /// <returns>List of filtered diagnostic events</returns>
        Task<List<DiagnosticEvent>> GetEventsByTypeAsync(DiagnosticEventType eventType);

        /// <summary>
        /// Gets diagnostic events filtered by source
        /// </summary>
        /// <param name="source">The source to filter by</param>
        /// <returns>List of filtered diagnostic events</returns>
        Task<List<DiagnosticEvent>> GetEventsBySourceAsync(string source);

        /// <summary>
        /// Gets diagnostic events filtered by date range
        /// </summary>
        /// <param name="startDate">The start date</param>
        /// <param name="endDate">The end date</param>
        /// <returns>List of filtered diagnostic events</returns>
        Task<List<DiagnosticEvent>> GetEventsByDateRangeAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets system diagnostics information
        /// </summary>
        /// <returns>Dictionary of system diagnostics information</returns>
        Task<Dictionary<string, object>> GetSystemDiagnosticsAsync();

        /// <summary>
        /// Gets application diagnostics information
        /// </summary>
        /// <returns>Dictionary of application diagnostics information</returns>
        Task<Dictionary<string, object>> GetApplicationDiagnosticsAsync();

        /// <summary>
        /// Gets hardware diagnostics information
        /// </summary>
        /// <returns>Dictionary of hardware diagnostics information</returns>
        Task<Dictionary<string, object>> GetHardwareDiagnosticsAsync();

        /// <summary>
        /// Gets network diagnostics information
        /// </summary>
        /// <returns>Dictionary of network diagnostics information</returns>
        Task<Dictionary<string, object>> GetNetworkDiagnosticsAsync();

        /// <summary>
        /// Runs a diagnostic test
        /// </summary>
        /// <param name="testName">The name of the test to run</param>
        /// <returns>The test result</returns>
        Task<DiagnosticTestResult> RunDiagnosticTestAsync(string testName);

        /// <summary>
        /// Runs all diagnostic tests
        /// </summary>
        /// <returns>Dictionary of test results</returns>
        Task<Dictionary<string, DiagnosticTestResult>> RunAllDiagnosticTestsAsync();

        /// <summary>
        /// Clears all diagnostic events
        /// </summary>
        /// <returns>True if clearing is successful, false otherwise</returns>
        Task<bool> ClearEventsAsync();

        /// <summary>
        /// Exports diagnostic data to a file
        /// </summary>
        /// <param name="filePath">The file path</param>
        /// <returns>True if export is successful, false otherwise</returns>
        Task<bool> ExportDiagnosticsAsync(string filePath);
    }

    /// <summary>
    /// Represents a diagnostic event
    /// </summary>
    public class DiagnosticEvent
    {
        /// <summary>
        /// Gets or sets the event ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the timestamp of the event
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the event type
        /// </summary>
        public DiagnosticEventType EventType { get; set; }

        /// <summary>
        /// Gets or sets the source of the event
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// Gets or sets the event message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Gets or sets additional event data
        /// </summary>
        public Dictionary<string, object> Data { get; set; }
    }

    /// <summary>
    /// Represents the type of a diagnostic event
    /// </summary>
    public enum DiagnosticEventType
    {
        /// <summary>
        /// Information event
        /// </summary>
        Information,

        /// <summary>
        /// Warning event
        /// </summary>
        Warning,

        /// <summary>
        /// Error event
        /// </summary>
        Error,

        /// <summary>
        /// Performance event
        /// </summary>
        Performance,

        /// <summary>
        /// Security event
        /// </summary>
        Security,

        /// <summary>
        /// System event
        /// </summary>
        System,

        /// <summary>
        /// Hardware event
        /// </summary>
        Hardware,

        /// <summary>
        /// Network event
        /// </summary>
        Network
    }

    /// <summary>
    /// Represents the result of a diagnostic test
    /// </summary>
    public class DiagnosticTestResult
    {
        /// <summary>
        /// Gets or sets the test name
        /// </summary>
        public string TestName { get; set; }

        /// <summary>
        /// Gets or sets whether the test passed
        /// </summary>
        public bool Passed { get; set; }

        /// <summary>
        /// Gets or sets the test message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Gets or sets the test details
        /// </summary>
        public Dictionary<string, object> Details { get; set; }

        /// <summary>
        /// Gets or sets the timestamp of the test
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
}
