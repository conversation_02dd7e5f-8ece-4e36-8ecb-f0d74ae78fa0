# Simple Vocom Library Verification Script
Write-Host "=== Vocom Library Verification ===" -ForegroundColor Green

# Core libraries needed for Vocom communication
$CoreLibraries = @(
    "WUDFPuma.dll",
    "apci.dll", 
    "apcidb.dll",
    "Volvo.ApciPlus.dll",
    "Volvo.ApciPlusData.dll",
    "PhoenixESW.dll",
    "PhoenixGeneral.dll"
)

$LibrariesPath = ".\Libraries"
Write-Host "Checking libraries in: $LibrariesPath" -ForegroundColor Cyan

$Present = 0
$Missing = 0

foreach ($lib in $CoreLibraries) {
    $path = Join-Path $LibrariesPath $lib
    if (Test-Path $path) {
        Write-Host "✓ $lib" -ForegroundColor Green
        $Present++
    } else {
        Write-Host "✗ $lib" -ForegroundColor Red
        $Missing++
    }
}

Write-Host "`nSummary: $Present present, $Missing missing" -ForegroundColor Yellow

if ($Missing -eq 0) {
    Write-Host "✓ All core libraries are present!" -ForegroundColor Green
} else {
    Write-Host "⚠ Some libraries are missing. The application may fall back to dummy mode." -ForegroundColor Yellow
}

Write-Host "`nTotal libraries in folder:" -ForegroundColor Cyan
$allLibs = Get-ChildItem $LibrariesPath -Filter "*.dll" | Measure-Object
Write-Host "$($allLibs.Count) DLL files found" -ForegroundColor Cyan
