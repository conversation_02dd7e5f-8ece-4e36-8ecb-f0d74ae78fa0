using System;
using System.Globalization;
using System.Windows.Data;

namespace VolvoFlashWR.UI.Controls
{
    /// <summary>
    /// Converts bytes to kilobytes
    /// </summary>
    public class BytesToKBConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double bytes)
            {
                return bytes / 1024.0;
            }
            
            return 0.0;
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
