using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Utilities;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Service for managing application licensing
    /// </summary>
    public class LicensingService : ILicensingService
    {
        private readonly ILoggingService _logger;
        private readonly IAppConfigurationService _configurationService;
        private LicenseInfo _licenseInfo;
        private string _licenseFilePath;
        private readonly string _encryptionKey;
        private const int TrialPeriodDays = 30;

        /// <summary>
        /// Event triggered when license status changes
        /// </summary>
        public event EventHandler<LicenseInfo> LicenseStatusChanged;

        /// <summary>
        /// Initializes a new instance of the LicensingService class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="configurationService">The configuration service</param>
        public LicensingService(ILoggingService logger, IAppConfigurationService configurationService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));
            _licenseInfo = new LicenseInfo();
            
            // Create a unique encryption key based on hardware information
            _encryptionKey = GenerateEncryptionKey();
            
            // Set the license file path
            _licenseFilePath = Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory, 
                "Config", 
                "license.dat");
        }

        /// <summary>
        /// Initializes the licensing service
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing licensing service", "LicensingService");

                // Ensure the directory exists
                string licenseDirectory = Path.GetDirectoryName(_licenseFilePath);
                if (!string.IsNullOrEmpty(licenseDirectory))
                {
                    Directory.CreateDirectory(licenseDirectory);
                }

                // Load license information if file exists
                if (File.Exists(_licenseFilePath))
                {
                    await LoadLicenseInfoAsync();
                }
                else
                {
                    // Create new license info with trial period
                    _licenseInfo = CreateTrialLicense();
                    await SaveLicenseInfoAsync();
                }

                // Update license status
                UpdateLicenseStatus();

                _logger.LogInformation($"Licensing service initialized. Status: {_licenseInfo.Status}", "LicensingService");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error initializing licensing service", "LicensingService", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets the current license information
        /// </summary>
        /// <returns>The current license information</returns>
        public LicenseInfo GetLicenseInfo()
        {
            return _licenseInfo;
        }

        /// <summary>
        /// Checks if the application is licensed
        /// </summary>
        /// <returns>True if the application is licensed, false otherwise</returns>
        public bool IsLicensed()
        {
            return _licenseInfo.Status == LicenseStatus.Licensed;
        }

        /// <summary>
        /// Checks if the application is in trial period
        /// </summary>
        /// <returns>True if the application is in trial period, false otherwise</returns>
        public bool IsInTrialPeriod()
        {
            return _licenseInfo.Status == LicenseStatus.Trial;
        }

        /// <summary>
        /// Gets the number of days remaining in the trial period
        /// </summary>
        /// <returns>The number of days remaining in the trial period, or 0 if the trial has expired</returns>
        public int GetTrialDaysRemaining()
        {
            if (_licenseInfo.Status != LicenseStatus.Trial)
            {
                return 0;
            }

            return _licenseInfo.TrialDaysRemaining;
        }

        /// <summary>
        /// Activates the application with the provided activation key
        /// </summary>
        /// <param name="activationKey">The activation key</param>
        /// <returns>The result of the activation attempt</returns>
        public async Task<ActivationResult> ActivateWithKeyAsync(string activationKey)
        {
            try
            {
                _logger.LogInformation($"Attempting to activate with key: {MaskActivationKey(activationKey)}", "LicensingService");

                // Validate the activation key
                if (string.IsNullOrWhiteSpace(activationKey))
                {
                    return ActivationResult.Failed("Activation key cannot be empty.");
                }

                // Verify the key format and extract expiration date
                if (!TryExtractExpirationDate(activationKey, out DateTime expirationDate))
                {
                    return ActivationResult.Failed("Invalid activation key format.");
                }

                // Check if the key has expired
                if (expirationDate < DateTime.Now)
                {
                    return ActivationResult.Failed("The activation key has expired.");
                }

                // Update license information
                _licenseInfo.Status = LicenseStatus.Licensed;
                _licenseInfo.LicenseKey = activationKey;
                _licenseInfo.ActivationDate = DateTime.Now;
                _licenseInfo.ExpirationDate = expirationDate;
                _licenseInfo.HardwareId = GetHardwareId();

                // Save the updated license information
                await SaveLicenseInfoAsync();

                // Notify subscribers
                LicenseStatusChanged?.Invoke(this, _licenseInfo);

                _logger.LogInformation("Activation successful", "LicensingService");
                return ActivationResult.Successful(_licenseInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error activating license", "LicensingService", ex);
                return ActivationResult.Failed($"Activation failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Deactivates the current license
        /// </summary>
        /// <returns>True if deactivation is successful, false otherwise</returns>
        public async Task<bool> DeactivateAsync()
        {
            try
            {
                _logger.LogInformation("Deactivating license", "LicensingService");

                // Reset license information to trial
                _licenseInfo = CreateTrialLicense();

                // Save the updated license information
                await SaveLicenseInfoAsync();

                // Notify subscribers
                LicenseStatusChanged?.Invoke(this, _licenseInfo);

                _logger.LogInformation("License deactivated successfully", "LicensingService");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error deactivating license", "LicensingService", ex);
                return false;
            }
        }

        /// <summary>
        /// Generates a valid activation key for testing purposes
        /// </summary>
        /// <param name="expirationDate">The expiration date for the key</param>
        /// <returns>A valid activation key</returns>
        public string GenerateActivationKey(DateTime expirationDate)
        {
            try
            {
                // Format: XXXX-XXXX-XXXX-XXXX-XXXX
                // Where the key encodes the expiration date and a checksum
                
                // Create a seed based on expiration date
                string dateSeed = expirationDate.ToString("yyyyMMdd");
                
                // Generate a random component
                using var rng = RandomNumberGenerator.Create();
                byte[] randomBytes = new byte[8];
                rng.GetBytes(randomBytes);
                string randomComponent = Convert.ToBase64String(randomBytes).Replace("/", "X").Replace("+", "Y").Replace("=", "Z").Substring(0, 8);
                
                // Combine date and random component
                string baseString = dateSeed + randomComponent;
                
                // Calculate checksum
                string checksum = CalculateChecksum(baseString);
                
                // Combine all parts
                string combinedString = baseString + checksum;
                
                // Format as XXXX-XXXX-XXXX-XXXX-XXXX
                return $"{combinedString.Substring(0, 4)}-{combinedString.Substring(4, 4)}-{combinedString.Substring(8, 4)}-{combinedString.Substring(12, 4)}-{combinedString.Substring(16, 4)}";
            }
            catch (Exception ex)
            {
                _logger.LogError("Error generating activation key", "LicensingService", ex);
                throw;
            }
        }

        #region Private Methods

        /// <summary>
        /// Creates a new trial license
        /// </summary>
        /// <returns>A new trial license</returns>
        private LicenseInfo CreateTrialLicense()
        {
            DateTime now = DateTime.Now;
            return new LicenseInfo
            {
                Status = LicenseStatus.Trial,
                TrialStartDate = now,
                TrialEndDate = now.AddDays(TrialPeriodDays),
                TrialDaysRemaining = TrialPeriodDays,
                HardwareId = GetHardwareId()
            };
        }

        /// <summary>
        /// Updates the license status based on current dates
        /// </summary>
        private void UpdateLicenseStatus()
        {
            DateTime now = DateTime.Now;

            // Check if hardware ID has changed
            if (_licenseInfo.HardwareId != null && _licenseInfo.HardwareId != GetHardwareId())
            {
                _licenseInfo.Status = LicenseStatus.Unlicensed;
                _logger.LogWarning("Hardware ID has changed, license invalidated", "LicensingService");
                return;
            }

            switch (_licenseInfo.Status)
            {
                case LicenseStatus.Trial:
                    // Check if trial has expired
                    if (_licenseInfo.TrialEndDate.HasValue && now > _licenseInfo.TrialEndDate.Value)
                    {
                        _licenseInfo.Status = LicenseStatus.TrialExpired;
                        _licenseInfo.TrialDaysRemaining = 0;
                    }
                    else if (_licenseInfo.TrialEndDate.HasValue)
                    {
                        // Calculate days remaining
                        _licenseInfo.TrialDaysRemaining = Math.Max(0, (int)(_licenseInfo.TrialEndDate.Value - now).TotalDays + 1);
                    }
                    break;

                case LicenseStatus.Licensed:
                    // Check if license has expired
                    if (_licenseInfo.ExpirationDate.HasValue && now > _licenseInfo.ExpirationDate.Value)
                    {
                        _licenseInfo.Status = LicenseStatus.LicenseExpired;
                    }
                    break;
            }
        }

        /// <summary>
        /// Loads license information from the file
        /// </summary>
        private async Task LoadLicenseInfoAsync()
        {
            try
            {
                if (!File.Exists(_licenseFilePath))
                {
                    _logger.LogWarning($"License file not found: {_licenseFilePath}", "LicensingService");
                    _licenseInfo = CreateTrialLicense();
                    return;
                }

                // Read encrypted data
                byte[] encryptedData = await File.ReadAllBytesAsync(_licenseFilePath);
                
                // Decrypt the data
                string json = DecryptData(encryptedData);
                
                // Deserialize the license information
                var loadedInfo = JsonSerializer.Deserialize<LicenseInfo>(json);
                
                if (loadedInfo != null)
                {
                    _licenseInfo = loadedInfo;
                    _logger.LogInformation("License information loaded successfully", "LicensingService");
                }
                else
                {
                    _logger.LogWarning("Failed to deserialize license information, creating new trial", "LicensingService");
                    _licenseInfo = CreateTrialLicense();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error loading license information", "LicensingService", ex);
                _licenseInfo = CreateTrialLicense();
            }
        }

        /// <summary>
        /// Saves license information to the file
        /// </summary>
        private async Task SaveLicenseInfoAsync()
        {
            try
            {
                // Ensure the directory exists
                string licenseDirectory = Path.GetDirectoryName(_licenseFilePath);
                if (!string.IsNullOrEmpty(licenseDirectory))
                {
                    Directory.CreateDirectory(licenseDirectory);
                }

                // Serialize the license information
                string json = JsonSerializer.Serialize(_licenseInfo);
                
                // Encrypt the data
                byte[] encryptedData = EncryptData(json);
                
                // Write to file
                await File.WriteAllBytesAsync(_licenseFilePath, encryptedData);
                
                _logger.LogInformation("License information saved successfully", "LicensingService");
            }
            catch (Exception ex)
            {
                _logger.LogError("Error saving license information", "LicensingService", ex);
                throw;
            }
        }

        /// <summary>
        /// Encrypts the data using AES
        /// </summary>
        /// <param name="plainText">The plain text to encrypt</param>
        /// <returns>The encrypted data</returns>
        private byte[] EncryptData(string plainText)
        {
            using Aes aes = Aes.Create();
            aes.Key = DeriveKeyFromString(_encryptionKey, 32); // 256 bits
            aes.IV = DeriveKeyFromString(_encryptionKey, 16);  // 128 bits
            
            using MemoryStream ms = new MemoryStream();
            using CryptoStream cs = new CryptoStream(ms, aes.CreateEncryptor(), CryptoStreamMode.Write);
            using (StreamWriter sw = new StreamWriter(cs))
            {
                sw.Write(plainText);
            }
            
            return ms.ToArray();
        }

        /// <summary>
        /// Decrypts the data using AES
        /// </summary>
        /// <param name="encryptedData">The encrypted data</param>
        /// <returns>The decrypted plain text</returns>
        private string DecryptData(byte[] encryptedData)
        {
            using Aes aes = Aes.Create();
            aes.Key = DeriveKeyFromString(_encryptionKey, 32); // 256 bits
            aes.IV = DeriveKeyFromString(_encryptionKey, 16);  // 128 bits
            
            using MemoryStream ms = new MemoryStream(encryptedData);
            using CryptoStream cs = new CryptoStream(ms, aes.CreateDecryptor(), CryptoStreamMode.Read);
            using StreamReader sr = new StreamReader(cs);
            
            return sr.ReadToEnd();
        }

        /// <summary>
        /// Derives a key from a string using PBKDF2
        /// </summary>
        /// <param name="input">The input string</param>
        /// <param name="keyLength">The desired key length in bytes</param>
        /// <returns>The derived key</returns>
        private byte[] DeriveKeyFromString(string input, int keyLength)
        {
            byte[] salt = Encoding.UTF8.GetBytes("VolvoFlashWRSalt");
            using var pbkdf2 = new Rfc2898DeriveBytes(input, salt, 10000, HashAlgorithmName.SHA256);
            return pbkdf2.GetBytes(keyLength);
        }

        /// <summary>
        /// Generates an encryption key based on hardware information
        /// </summary>
        /// <returns>The encryption key</returns>
        private string GenerateEncryptionKey()
        {
            // Use hardware ID as the base for the encryption key
            string hardwareId = GetHardwareId();
            
            // Add a fixed salt to make it more secure
            return $"VolvoFlashWR_{hardwareId}_SecureKey";
        }

        /// <summary>
        /// Gets a unique hardware ID for the current machine
        /// </summary>
        /// <returns>A hardware ID</returns>
        private string GetHardwareId()
        {
            try
            {
                // Get processor ID
                string processorId = GetWMIValue("Win32_Processor", "ProcessorId");
                
                // Get motherboard serial number
                string motherboardSerial = GetWMIValue("Win32_BaseBoard", "SerialNumber");
                
                // Get BIOS serial number
                string biosSerial = GetWMIValue("Win32_BIOS", "SerialNumber");
                
                // Combine and hash
                string combined = $"{processorId}|{motherboardSerial}|{biosSerial}";
                using var sha256 = SHA256.Create();
                byte[] hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(combined));
                
                return BitConverter.ToString(hashBytes).Replace("-", "").Substring(0, 16);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error getting hardware ID", "LicensingService", ex);
                
                // Fallback to a machine name based ID
                string machineName = Environment.MachineName;
                using var sha256 = SHA256.Create();
                byte[] hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(machineName));
                
                return BitConverter.ToString(hashBytes).Replace("-", "").Substring(0, 16);
            }
        }

        /// <summary>
        /// Gets a value from WMI
        /// </summary>
        /// <param name="wmiClass">The WMI class</param>
        /// <param name="property">The property to get</param>
        /// <returns>The property value</returns>
        private string GetWMIValue(string wmiClass, string property)
        {
            try
            {
                // Use System.Management to get WMI values
                // This is a simplified version - in a real implementation, you would use System.Management
                
                // For now, return a placeholder based on the machine name and class/property
                return $"{Environment.MachineName}_{wmiClass}_{property}";
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Tries to extract the expiration date from an activation key
        /// </summary>
        /// <param name="activationKey">The activation key</param>
        /// <param name="expirationDate">The extracted expiration date</param>
        /// <returns>True if extraction was successful, false otherwise</returns>
        private bool TryExtractExpirationDate(string activationKey, out DateTime expirationDate)
        {
            expirationDate = DateTime.MinValue;
            
            try
            {
                // Remove dashes
                string key = activationKey.Replace("-", "");
                
                // Check length
                if (key.Length != 20)
                {
                    return false;
                }
                
                // Extract date part (first 8 characters)
                string datePart = key.Substring(0, 8);
                
                // Parse date
                if (DateTime.TryParseExact(datePart, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out expirationDate))
                {
                    // Verify checksum
                    string baseString = key.Substring(0, 16);
                    string checksum = key.Substring(16, 4);
                    
                    return checksum == CalculateChecksum(baseString);
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Calculates a checksum for the given string
        /// </summary>
        /// <param name="input">The input string</param>
        /// <returns>A 4-character checksum</returns>
        private string CalculateChecksum(string input)
        {
            using var md5 = MD5.Create();
            byte[] hashBytes = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
            return BitConverter.ToString(hashBytes).Replace("-", "").Substring(0, 4);
        }

        /// <summary>
        /// Masks an activation key for logging
        /// </summary>
        /// <param name="key">The activation key</param>
        /// <returns>The masked key</returns>
        private string MaskActivationKey(string key)
        {
            if (string.IsNullOrEmpty(key) || key.Length < 10)
            {
                return "****";
            }
            
            return key.Substring(0, 4) + "-****-****-****-" + key.Substring(key.Length - 4);
        }

        #endregion
    }
}
