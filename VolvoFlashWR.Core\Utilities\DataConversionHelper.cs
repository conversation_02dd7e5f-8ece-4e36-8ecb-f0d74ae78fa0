using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace VolvoFlashWR.Core.Utilities
{
    /// <summary>
    /// Helper class for data conversion operations
    /// </summary>
    public static class DataConversionHelper
    {
        /// <summary>
        /// Converts a byte array to a hexadecimal string
        /// </summary>
        /// <param name="bytes">The byte array to convert</param>
        /// <returns>Hexadecimal string representation of the byte array</returns>
        public static string BytesToHexString(byte[] bytes)
        {
            if (bytes == null || bytes.Length == 0)
                return string.Empty;

            StringBuilder hex = new StringBuilder(bytes.Length * 2);
            foreach (byte b in bytes)
                hex.AppendFormat("{0:X2}", b);

            return hex.ToString();
        }

        /// <summary>
        /// Converts a hexadecimal string to a byte array
        /// </summary>
        /// <param name="hex">The hexadecimal string to convert</param>
        /// <returns>Byte array representation of the hexadecimal string</returns>
        public static byte[] HexStringToBytes(string hex)
        {
            if (string.IsNullOrEmpty(hex))
                return new byte[0];

            // Remove any non-hex characters (like spaces)
            hex = new string(hex.Where(c => char.IsDigit(c) || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F')).ToArray());

            // If the string has an odd length, pad with a leading zero
            if (hex.Length % 2 != 0)
                hex = "0" + hex;

            byte[] bytes = new byte[hex.Length / 2];
            for (int i = 0; i < hex.Length; i += 2)
                bytes[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);

            return bytes;
        }

        /// <summary>
        /// Calculates the MD5 checksum of a byte array
        /// </summary>
        /// <param name="data">The data to calculate the checksum for</param>
        /// <returns>MD5 checksum as a string</returns>
        public static string CalculateMD5Checksum(byte[] data)
        {
            if (data == null || data.Length == 0)
                return string.Empty;

            using (MD5 md5 = MD5.Create())
            {
                byte[] hash = md5.ComputeHash(data);
                return BytesToHexString(hash);
            }
        }

        /// <summary>
        /// Calculates the SHA256 checksum of a byte array
        /// </summary>
        /// <param name="data">The data to calculate the checksum for</param>
        /// <returns>SHA256 checksum as a string</returns>
        public static string CalculateSHA256Checksum(byte[] data)
        {
            if (data == null || data.Length == 0)
                return string.Empty;

            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] hash = sha256.ComputeHash(data);
                return BytesToHexString(hash);
            }
        }

        /// <summary>
        /// Combines multiple byte arrays into a single byte array
        /// </summary>
        /// <param name="arrays">The byte arrays to combine</param>
        /// <returns>Combined byte array</returns>
        public static byte[] CombineByteArrays(params byte[][] arrays)
        {
            if (arrays == null || arrays.Length == 0)
                return new byte[0];

            int totalLength = arrays.Sum(a => a?.Length ?? 0);
            byte[] result = new byte[totalLength];
            int offset = 0;

            foreach (byte[] array in arrays)
            {
                if (array != null && array.Length > 0)
                {
                    Buffer.BlockCopy(array, 0, result, offset, array.Length);
                    offset += array.Length;
                }
            }

            return result;
        }

        /// <summary>
        /// Extracts a portion of a byte array
        /// </summary>
        /// <param name="source">The source byte array</param>
        /// <param name="startIndex">The start index</param>
        /// <param name="length">The length of the portion to extract</param>
        /// <returns>Extracted byte array</returns>
        public static byte[] ExtractByteArray(byte[] source, int startIndex, int length)
        {
            if (source == null || startIndex < 0 || length <= 0 || startIndex + length > source.Length)
                return new byte[0];

            byte[] result = new byte[length];
            Buffer.BlockCopy(source, startIndex, result, 0, length);
            return result;
        }

        /// <summary>
        /// Serializes an object to a byte array
        /// </summary>
        /// <param name="obj">The object to serialize</param>
        /// <returns>Serialized byte array</returns>
        public static byte[] SerializeObject(object obj)
        {
            if (obj == null)
                return new byte[0];

            // This is a placeholder implementation
            // In a real implementation, this would use a proper serialization method
            return Encoding.UTF8.GetBytes(obj.ToString());
        }

        /// <summary>
        /// Deserializes a byte array to an object
        /// </summary>
        /// <typeparam name="T">The type of object to deserialize to</typeparam>
        /// <param name="data">The byte array to deserialize</param>
        /// <returns>Deserialized object</returns>
        public static T DeserializeObject<T>(byte[] data) where T : class
        {
            if (data == null || data.Length == 0)
                return null;

            // This is a placeholder implementation
            // In a real implementation, this would use a proper deserialization method
            return null;
        }

        /// <summary>
        /// Converts a byte array to a Base64 string
        /// </summary>
        /// <param name="bytes">The byte array to convert</param>
        /// <returns>Base64 string representation of the byte array</returns>
        public static string BytesToBase64String(byte[] bytes)
        {
            if (bytes == null || bytes.Length == 0)
                return string.Empty;

            return Convert.ToBase64String(bytes);
        }

        /// <summary>
        /// Converts a Base64 string to a byte array
        /// </summary>
        /// <param name="base64">The Base64 string to convert</param>
        /// <returns>Byte array representation of the Base64 string</returns>
        public static byte[] Base64StringToBytes(string base64)
        {
            if (string.IsNullOrEmpty(base64))
                return Array.Empty<byte>();

            return Convert.FromBase64String(base64);
        }
    }
}
