namespace VolvoFlashWR.UI.Models
{
    /// <summary>
    /// Represents the result of comparing a parameter between two backup versions
    /// </summary>
    public class ParameterComparisonResult
    {
        /// <summary>
        /// The name of the parameter being compared
        /// </summary>
        public string ParameterName { get; set; } = string.Empty;

        /// <summary>
        /// The value of the parameter in version A
        /// </summary>
        public string ValueA { get; set; } = string.Empty;

        /// <summary>
        /// The value of the parameter in version B
        /// </summary>
        public string ValueB { get; set; } = string.Empty;

        /// <summary>
        /// The difference between the values (if applicable)
        /// </summary>
        public string Difference { get; set; } = string.Empty;

        /// <summary>
        /// The status of the comparison (Same, Changed, Added, Removed)
        /// </summary>
        public string Status { get; set; } = "Unknown";

        /// <summary>
        /// Default constructor
        /// </summary>
        public ParameterComparisonResult()
        {
            // Properties already initialized with default values
        }

        /// <summary>
        /// Constructor with parameter name
        /// </summary>
        /// <param name="parameterName">The name of the parameter</param>
        public ParameterComparisonResult(string parameterName)
        {
            ParameterName = parameterName;
        }

        /// <summary>
        /// Constructor with parameter name and values
        /// </summary>
        /// <param name="parameterName">The name of the parameter</param>
        /// <param name="valueA">The value in version A</param>
        /// <param name="valueB">The value in version B</param>
        public ParameterComparisonResult(string parameterName, string valueA, string valueB)
        {
            ParameterName = parameterName;
            ValueA = valueA;
            ValueB = valueB;

            if (valueA == valueB)
            {
                Status = "Same";
                Difference = "0";
            }
            else
            {
                Status = "Changed";
                Difference = "N/A";
            }
        }
    }
}
