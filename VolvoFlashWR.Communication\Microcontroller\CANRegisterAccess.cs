using System;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Microcontroller
{
    /// <summary>
    /// CAN protocol implementation of register access
    /// </summary>
    public class CANRegisterAccess : BaseRegisterAccess
    {
        private const byte CAN_READ_REGISTER_CMD = 0x01;
        private const byte CAN_WRITE_REGISTER_CMD = 0x02;
        private const uint DEFAULT_REQUEST_ID = 0x7E0;
        private const uint DEFAULT_RESPONSE_ID = 0x7E8;
        private const int DEFAULT_TIMEOUT_MS = 1000;

        /// <summary>
        /// Initializes a new instance of the CANRegisterAccess class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="vocomService">The Vocom service</param>
        public CANRegisterAccess(ILoggingService logger, IVocomService vocomService)
            : base(logger, vocomService)
        {
        }

        /// <summary>
        /// Reads a byte from a register
        /// </summary>
        /// <param name="register">The register address</param>
        /// <returns>The register value</returns>
        public override async Task<byte> ReadRegisterByteAsync(uint register)
        {
            try
            {
                // Create a command to read the register
                // Format: [Command] [Register High Byte] [Register Low Byte]
                byte[] command = new byte[3];
                command[0] = CAN_READ_REGISTER_CMD;
                command[1] = (byte)((register >> 8) & 0xFF); // Register high byte
                command[2] = (byte)(register & 0xFF); // Register low byte

                // Send the command to the Vocom device
                byte[] response = await SendCommandToVocomAsync(command);
                if (response == null || response.Length < 1)
                {
                    _logger?.LogError($"Failed to read register 0x{register:X4}", "CANRegisterAccess");
                    return 0;
                }

                return response[0];
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading register 0x{register:X4}: {ex.Message}", "CANRegisterAccess");
                return 0;
            }
        }

        /// <summary>
        /// Writes a byte to a register
        /// </summary>
        /// <param name="register">The register address</param>
        /// <param name="value">The value to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        public override async Task<bool> WriteRegisterByteAsync(uint register, byte value)
        {
            try
            {
                // Create a command to write the register
                // Format: [Command] [Register High Byte] [Register Low Byte] [Value]
                byte[] command = new byte[4];
                command[0] = CAN_WRITE_REGISTER_CMD;
                command[1] = (byte)((register >> 8) & 0xFF); // Register high byte
                command[2] = (byte)(register & 0xFF); // Register low byte
                command[3] = value; // Value to write

                // Send the command to the Vocom device
                byte[] response = await SendCommandToVocomAsync(command);
                if (response == null || response.Length < 1 || response[0] != 0x00) // 0x00 indicates success
                {
                    _logger?.LogError($"Failed to write value 0x{value:X2} to register 0x{register:X4}", "CANRegisterAccess");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error writing value 0x{value:X2} to register 0x{register:X4}: {ex.Message}", "CANRegisterAccess");
                return false;
            }
        }

        /// <summary>
        /// Sends a command to the Vocom device
        /// </summary>
        /// <param name="command">The command to send</param>
        /// <returns>The response from the Vocom device</returns>
        protected override async Task<byte[]> SendCommandToVocomAsync(byte[] command)
        {
            try
            {
                if (_vocomService == null)
                {
                    _logger?.LogError("Vocom service is null", "CANRegisterAccess");
                    return null;
                }

                // Get the current Vocom device
                VocomDevice device = await _vocomService.GetCurrentDeviceAsync();
                if (device == null)
                {
                    _logger?.LogError("No Vocom device available", "CANRegisterAccess");
                    return null;
                }

                // Send the command as a CAN frame
                byte[] response = await _vocomService.SendCANFrameAsync(
                    device,
                    DEFAULT_REQUEST_ID,
                    command,
                    DEFAULT_TIMEOUT_MS);

                return response;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error sending command to Vocom device: {ex.Message}", "CANRegisterAccess");
                return null;
            }
        }

        /// <summary>
        /// Waits for a specific bit in a register to be set or cleared
        /// </summary>
        /// <param name="register">The register address</param>
        /// <param name="bitMask">The bit mask</param>
        /// <param name="bitValue">The expected bit value (true for set, false for cleared)</param>
        /// <param name="timeoutMs">The timeout in milliseconds</param>
        /// <returns>True if the bit is set/cleared within the timeout, false otherwise</returns>
        public async Task<bool> WaitForRegisterBitAsync(uint register, byte bitMask, bool bitValue, int timeoutMs)
        {
            DateTime startTime = DateTime.Now;
            while ((DateTime.Now - startTime).TotalMilliseconds < timeoutMs)
            {
                byte value = await ReadRegisterByteAsync(register);
                bool currentBitValue = (value & bitMask) != 0;
                if (currentBitValue == bitValue)
                {
                    return true;
                }
                await Task.Delay(10); // Small delay to avoid hammering the bus
            }
            return false;
        }
    }
}
