using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Vocom;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Vocom
{
    [TestFixture]
    public class VocomDeviceDriverTests
    {
        private Mock<ILoggingService> _mockLogger;
        private VocomDeviceDriver _driver;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _driver = new VocomDeviceDriver(_mockLogger.Object);
        }

        [Test]
        public void Constructor_WithNullLogger_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.That(() => new VocomDeviceDriver(null), Throws.TypeOf<ArgumentNullException>());
        }

        [Test]
        public async Task InitializeAsync_WhenNativeInteropFails_ReturnsFalse()
        {
            // Act
            bool result = await _driver.InitializeAsync();

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task DetectDevicesAsync_WhenNotInitialized_ReturnsEmptyList()
        {
            // Act
            var devices = await _driver.DetectDevicesAsync();

            // Assert
            Assert.That(devices, Is.Not.Null);
            Assert.That(devices, Is.Empty);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task ConnectToDeviceAsync_WithNullDevice_ReturnsFalse()
        {
            // Act
            bool result = await _driver.ConnectToDeviceAsync(null);

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task ConnectToDeviceAsync_WhenNotInitialized_ReturnsFalse()
        {
            // Arrange
            var device = new VocomDevice
            {
                SerialNumber = "88890300",
                Name = "Test Device",
                ConnectionType = VocomConnectionType.USB
            };

            // Act
            bool result = await _driver.ConnectToDeviceAsync(device);

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task DisconnectFromDeviceAsync_WithNullDevice_ReturnsFalse()
        {
            // Act
            bool result = await _driver.DisconnectFromDeviceAsync(null);

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task DisconnectFromDeviceAsync_WhenNotInitialized_ReturnsFalse()
        {
            // Arrange
            var device = new VocomDevice
            {
                SerialNumber = "88890300",
                Name = "Test Device",
                ConnectionType = VocomConnectionType.USB
            };

            // Act
            bool result = await _driver.DisconnectFromDeviceAsync(device);

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task UpdateFirmwareAsync_WithNullDevice_ReturnsFalse()
        {
            // Act
            bool result = await _driver.UpdateFirmwareAsync(null, new byte[] { 1, 2, 3 });

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task UpdateFirmwareAsync_WithNullFirmwareData_ReturnsFalse()
        {
            // Arrange
            var device = new VocomDevice
            {
                SerialNumber = "88890300",
                Name = "Test Device",
                ConnectionType = VocomConnectionType.USB
            };

            // Act
            bool result = await _driver.UpdateFirmwareAsync(device, null);

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task UpdateFirmwareAsync_WithEmptyFirmwareData_ReturnsFalse()
        {
            // Arrange
            var device = new VocomDevice
            {
                SerialNumber = "88890300",
                Name = "Test Device",
                ConnectionType = VocomConnectionType.USB
            };

            // Act
            bool result = await _driver.UpdateFirmwareAsync(device, new byte[0]);

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task UpdateFirmwareAsync_WhenNotInitialized_ReturnsFalse()
        {
            // Arrange
            var device = new VocomDevice
            {
                SerialNumber = "88890300",
                Name = "Test Device",
                ConnectionType = VocomConnectionType.USB
            };

            // Act
            bool result = await _driver.UpdateFirmwareAsync(device, new byte[] { 1, 2, 3 });

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task SendCANFrameAsync_WithNullDevice_ReturnsNull()
        {
            // Act
            var result = await _driver.SendCANFrameAsync(null, new byte[] { 1, 2, 3 }, 10);

            // Assert
            Assert.That(result, Is.Null);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task SendCANFrameAsync_WithNullData_ReturnsNull()
        {
            // Arrange
            var device = new VocomDevice
            {
                SerialNumber = "88890300",
                Name = "Test Device",
                ConnectionType = VocomConnectionType.USB
            };

            // Act
            var result = await _driver.SendCANFrameAsync(device, null, 10);

            // Assert
            Assert.That(result, Is.Null);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task SendCANFrameAsync_WithEmptyData_ReturnsNull()
        {
            // Arrange
            var device = new VocomDevice
            {
                SerialNumber = "88890300",
                Name = "Test Device",
                ConnectionType = VocomConnectionType.USB
            };

            // Act
            var result = await _driver.SendCANFrameAsync(device, new byte[0], 10);

            // Assert
            Assert.That(result, Is.Null);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task SendCANFrameAsync_WhenNotInitialized_ReturnsNull()
        {
            // Arrange
            var device = new VocomDevice
            {
                SerialNumber = "88890300",
                Name = "Test Device",
                ConnectionType = VocomConnectionType.USB
            };

            // Act
            var result = await _driver.SendCANFrameAsync(device, new byte[] { 1, 2, 3 }, 10);

            // Assert
            Assert.That(result, Is.Null);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task IsPTTRunningAsync_WhenNotInitialized_ReturnsFalse()
        {
            // Act
            bool result = await _driver.IsPTTRunningAsync();

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task DisconnectPTTAsync_WhenNotInitialized_ReturnsFalse()
        {
            // Act
            bool result = await _driver.DisconnectPTTAsync();

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task TerminatePTTAsync_WhenNotInitialized_ReturnsFalse()
        {
            // Act
            bool result = await _driver.TerminatePTTAsync();

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>(), It.IsAny<string>(), null), Times.Once);
        }

        [Test]
        public async Task DisposeAsync_WhenNotInitialized_ReturnsTrue()
        {
            // Act
            bool result = await _driver.DisposeAsync();

            // Assert
            Assert.That(result, Is.True);
            _mockLogger.Verify(l => l.LogInformation(It.IsAny<string>(), It.IsAny<string>()), Times.AtLeastOnce);
        }
    }
}
