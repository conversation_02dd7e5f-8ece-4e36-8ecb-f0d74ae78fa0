namespace VolvoFlashWR.Core.Enums
{
    /// <summary>
    /// Represents the health status level of flash memory
    /// </summary>
    public enum FlashHealthStatusLevel
    {
        /// <summary>
        /// Unknown status
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Good status - no issues detected
        /// </summary>
        Good = 1,

        /// <summary>
        /// Fair status - very minor issues detected but fully operational
        /// </summary>
        Fair = 2,

        /// <summary>
        /// Warning status - minor issues detected but still operational
        /// </summary>
        Warning = 3,

        /// <summary>
        /// Critical status - serious issues detected, may not be reliable
        /// </summary>
        Critical = 4,

        /// <summary>
        /// Failed status - flash memory has failed and is not operational
        /// </summary>
        Failed = 5
    }
}
