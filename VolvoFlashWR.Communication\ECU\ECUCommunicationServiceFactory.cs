using System;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Communication.ECU
{
    /// <summary>
    /// Factory for creating and initializing ECU communication services
    /// </summary>
    public class ECUCommunicationServiceFactory
    {
        private readonly ILoggingService _logger;
        private readonly IVocomService _vocomService;

        /// <summary>
        /// Initializes a new instance of the ECUCommunicationServiceFactory class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="vocomService">The Vocom service</param>
        public ECUCommunicationServiceFactory(ILoggingService logger, IVocomService vocomService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _vocomService = vocomService ?? throw new ArgumentNullException(nameof(vocomService));
        }

        /// <summary>
        /// Creates and initializes a new ECU communication service
        /// </summary>
        /// <returns>The initialized ECU communication service</returns>
        public async Task<IECUCommunicationService> CreateServiceAsync()
        {
            try
            {
                _logger.LogInformation("Creating ECU communication service", "ECUCommunicationServiceFactory");

                // Create the service
                var service = new ECUCommunicationService(_logger);

                // Initialize the service with retry logic
                bool initialized = false;
                int maxRetries = 3;
                int retryCount = 0;
                Exception lastException = null;

                while (!initialized && retryCount < maxRetries)
                {
                    try
                    {
                        _logger.LogInformation($"Initializing ECU communication service (attempt {retryCount + 1}/{maxRetries})", "ECUCommunicationServiceFactory");
                        initialized = await service.InitializeAsync(_vocomService);

                        if (!initialized)
                        {
                            retryCount++;
                            _logger.LogWarning($"Failed to initialize ECU communication service on attempt {retryCount}/{maxRetries}", "ECUCommunicationServiceFactory");
                            await Task.Delay(1000 * retryCount); // Increasing delay between retries
                        }
                    }
                    catch (Exception ex)
                    {
                        lastException = ex;
                        retryCount++;
                        _logger.LogWarning($"Error initializing ECU communication service on attempt {retryCount}/{maxRetries}: {ex.Message}", "ECUCommunicationServiceFactory");
                        await Task.Delay(1000 * retryCount); // Increasing delay between retries
                    }
                }

                if (!initialized)
                {
                    if (lastException != null)
                    {
                        _logger.LogError($"Failed to initialize ECU communication service after {maxRetries} attempts: {lastException.Message}", "ECUCommunicationServiceFactory");
                    }
                    else
                    {
                        _logger.LogError($"Failed to initialize ECU communication service after {maxRetries} attempts", "ECUCommunicationServiceFactory");
                    }

                    // Create and initialize a dummy service as fallback
                    _logger.LogWarning("Creating DummyECUCommunicationService as fallback", "ECUCommunicationServiceFactory");
                    var dummyService = new DummyECUCommunicationService(_logger);
                    bool dummyInitialized = await dummyService.InitializeAsync(_vocomService);

                    if (dummyInitialized)
                    {
                        _logger.LogInformation("DummyECUCommunicationService initialized successfully as fallback", "ECUCommunicationServiceFactory");
                        return dummyService;
                    }
                    else
                    {
                        _logger.LogError("Failed to initialize DummyECUCommunicationService as fallback", "ECUCommunicationServiceFactory");
                        return null;
                    }
                }

                _logger.LogInformation("ECU communication service created and initialized successfully", "ECUCommunicationServiceFactory");
                return service;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error creating ECU communication service", "ECUCommunicationServiceFactory", ex);

                // Create and initialize a dummy service as fallback
                try
                {
                    _logger.LogWarning("Creating DummyECUCommunicationService as fallback after exception", "ECUCommunicationServiceFactory");
                    var dummyService = new DummyECUCommunicationService(_logger);
                    bool dummyInitialized = await dummyService.InitializeAsync(_vocomService);

                    if (dummyInitialized)
                    {
                        _logger.LogInformation("DummyECUCommunicationService initialized successfully as fallback", "ECUCommunicationServiceFactory");
                        return dummyService;
                    }
                    else
                    {
                        _logger.LogError("Failed to initialize DummyECUCommunicationService as fallback", "ECUCommunicationServiceFactory");
                    }
                }
                catch (Exception dummyEx)
                {
                    _logger.LogError("Error creating DummyECUCommunicationService as fallback", "ECUCommunicationServiceFactory", dummyEx);
                }

                return null;
            }
        }
    }
}
