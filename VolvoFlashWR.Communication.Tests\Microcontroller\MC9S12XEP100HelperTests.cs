using System;
using System.Threading.Tasks;
using Moq;
using Moq.Language.Flow;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Tests.Helpers;
using VolvoFlashWR.Communication.Microcontroller;
using VolvoFlashWR.Core.Diagnostics;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Microcontroller
{
    [TestFixture]
    public class MC9S12XEP100HelperTests
    {
        private Mock<ILoggingService> _mockLogger;
        private Mock<IRegisterAccess> _mockRegisterAccess;
        private MC9S12XEP100Helper _helper;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _mockRegisterAccess = new Mock<IRegisterAccess>();
            _helper = new MC9S12XEP100Helper(_mockLogger.Object, _mockRegisterAccess.Object);
        }

        [Test]
        public void CalculateCRC16_WithValidData_ReturnsCorrectChecksum()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04 };

            // Act
            ushort crc = _helper.CalculateCRC16(data);

            // Assert
            Assert.That(crc, Is.Not.EqualTo(0));
        }

        [Test]
        public void CalculateECC_WithValidData_ReturnsECCByte()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };

            // Act
            byte ecc = _helper.CalculateECC(data);

            // Assert
            Assert.That(ecc, Is.Not.EqualTo(0));
        }

        [Test]
        public void VerifyECC_WithValidData_ReturnsTrue()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            byte ecc = _helper.CalculateECC(data);

            // Act
            bool result = _helper.VerifyECC(data, ecc);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public void VerifyECC_WithInvalidECC_ReturnsFalse()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            byte ecc = 0xFF; // Invalid ECC

            // Act
            bool result = _helper.VerifyECC(data, ecc);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void CheckECCErrors_NoError_ReturnsNoError()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            byte ecc = _helper.CalculateECC(data);

            // Act
            MC9S12XEP100Helper.ECCErrorStatus status = _helper.CheckECCErrors(data, ecc, out byte[] correctedData);

            // Assert
            Assert.That(status, Is.EqualTo(MC9S12XEP100Helper.ECCErrorStatus.NoError), "Should detect no error");
            Assert.That(correctedData, Is.EqualTo(data), "Corrected data should match original data when no error");
        }

        [Test]
        public void CheckECCErrors_SingleBitError_ReturnsSingleBitError()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            byte ecc = _helper.CalculateECC(data);

            // Introduce a single-bit error in the data
            byte[] corruptedData = new byte[data.Length];
            Array.Copy(data, corruptedData, data.Length);
            corruptedData[3] ^= 0x01; // Flip one bit in the 4th byte

            // Act
            MC9S12XEP100Helper.ECCErrorStatus status = _helper.CheckECCErrors(corruptedData, ecc, out byte[] correctedData);

            // Assert
            Assert.That(status, Is.EqualTo(MC9S12XEP100Helper.ECCErrorStatus.SingleBitError), "Should detect single-bit error");
            Assert.That(correctedData, Is.Not.EqualTo(corruptedData), "Corrected data should not match corrupted data");
        }

        [Test]
        public void CheckECCErrors_MultiBitError_ReturnsMultiBitError()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            byte ecc = _helper.CalculateECC(data);

            // Introduce a multi-bit error in the data
            byte[] corruptedData = new byte[data.Length];
            Array.Copy(data, corruptedData, data.Length);
            corruptedData[3] ^= 0x03; // Flip two bits in the 4th byte

            // Act
            MC9S12XEP100Helper.ECCErrorStatus status = _helper.CheckECCErrors(corruptedData, ecc, out byte[] correctedData);

            // Assert
            Assert.That(status, Is.EqualTo(MC9S12XEP100Helper.ECCErrorStatus.MultiBitError), "Should detect multi-bit error");
        }

        [Test]
        public void AlignToPhraseAddress_WithUnalignedAddress_ReturnsAlignedAddress()
        {
            // Arrange
            uint address = 0x1234;
            uint expected = 0x1230; // Aligned to 8-byte boundary

            // Act
            uint result = _helper.AlignToPhraseAddress(address);

            // Assert
            Assert.That(result, Is.EqualTo(expected));
        }

        [Test]
        public void AlignToSectorAddress_WithUnalignedAddress_ReturnsAlignedAddress()
        {
            // Arrange
            uint address = 0x1234;
            uint expected = 0x1000; // Aligned to 1024-byte boundary

            // Act
            uint result = _helper.AlignToSectorAddress(address);

            // Assert
            Assert.That(result, Is.EqualTo(expected));
        }

        [Test]
        public void PadToPhraseSize_WithUnalignedData_ReturnsPaddedData()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x02, 0x03 };
            int expectedLength = 8; // Padded to 8 bytes

            // Act
            byte[] result = _helper.PadToPhraseSize(data);

            // Assert
            Assert.That(result.Length, Is.EqualTo(expectedLength));
            Assert.That(result[0], Is.EqualTo(0x01));
            Assert.That(result[1], Is.EqualTo(0x02));
            Assert.That(result[2], Is.EqualTo(0x03));
            Assert.That(result[3], Is.EqualTo(0xFF)); // Padding should be 0xFF
        }

        [Test]
        public void ConvertDTCToBytes_WithValidDTC_ReturnsCorrectBytes()
        {
            // Arrange
            string dtc = "P0123";
            byte[] expected = new byte[] { 0x00, 0x01, 0x23 };

            // Act
            byte[] result = _helper.ConvertDTCToBytes(dtc);

            // Assert
            Assert.That(result.Length, Is.EqualTo(3));
            Assert.That(result[0], Is.EqualTo(expected[0]));
            Assert.That(result[1], Is.EqualTo(expected[1]));
            Assert.That(result[2], Is.EqualTo(expected[2]));
        }

        [Test]
        public void ConvertBytesToDTC_WithValidBytes_ReturnsCorrectDTC()
        {
            // Arrange
            byte[] dtcBytes = new byte[] { 0x00, 0x01, 0x23 };
            string expected = "P0123";

            // Act
            string result = _helper.ConvertBytesToDTC(dtcBytes);

            // Assert
            Assert.That(result, Is.EqualTo(expected));
        }

        [Test]
        public void CalculateSCIBaudRateDivisor_WithValidBaudRate_ReturnsCorrectDivisor()
        {
            // Arrange
            int baudRate = 9600;
            int busFrequency = 50_000_000;
            byte expectedHigh = 0x01;
            byte expectedLow = 0xA0;

            // Act
            var result = _helper.CalculateSCIBaudRateDivisor(baudRate, busFrequency);

            // Assert
            Assert.That(result.High, Is.EqualTo(expectedHigh));
            Assert.That(result.Low, Is.EqualTo(expectedLow));
        }

        [Test]
        public async Task EraseFlashSectorAsync_WithValidAddress_ReturnsTrue()
        {
            // Arrange
            uint address = 0x1000;

            // Set up mock register access
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsAny<uint>(), It.IsAny<byte>()))
                .Returns(Task.FromResult(true));

            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsAny<uint>()))
                .Returns(Task.FromResult<byte>(0x40)); // CCIF = 1

            // Act
            bool result = await _helper.EraseFlashSectorAsync(address);

            // Assert
            Assert.That(result, Is.True);

            // Verify that the correct registers were written
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_CMD, 0x0A), Times.Once);
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_STAT, 0x80), Times.Once);
        }

        [Test]
        public async Task ProgramFlashPhrasePublicAsync_WithValidData_ReturnsTrue()
        {
            // Arrange
            uint address = 0x1000;
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };

            // Set up mock register access
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsAny<uint>(), It.IsAny<byte>()))
                .Returns(Task.FromResult(true));

            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsAny<uint>()))
                .Returns(Task.FromResult<byte>(0x40)); // CCIF = 1

            // Act
            bool result = await _helper.ProgramFlashPhrasePublicAsync(address, data);

            // Assert
            Assert.That(result, Is.True);

            // Verify that the correct registers were written
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_CMD, 0x06), Times.Once);
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_STAT, 0x80), Times.Once);
        }

        [Test]
        public async Task ReadFlashPhraseAsync_WithValidAddress_ReturnsData()
        {
            // Arrange
            uint address = 0x1000;
            byte[] expectedData = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };

            // Set up mock register access
            for (int i = 0; i < 8; i++)
            {
                _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(address + (uint)i))
                    .Returns(Task.FromResult(expectedData[i]));
            }

            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(address + 8))
                .Returns(Task.FromResult(_helper.CalculateECC(expectedData))); // ECC byte

            // Act
            var (result, _) = await _helper.ReadFlashPhraseAsync(address);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Length, Is.EqualTo(8));
            for (int i = 0; i < 8; i++)
            {
                Assert.That(result[i], Is.EqualTo(expectedData[i]));
            }
        }

        [Test]
        public async Task PerformSecurityAccessAsync_WithValidKey_ReturnsTrue()
        {
            // Arrange
            byte[] key = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };

            // Set up mock register access
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsAny<uint>(), It.IsAny<byte>()))
                .Returns(Task.FromResult(true));

            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsAny<uint>()))
                .Returns(Task.FromResult<byte>(0x40)); // CCIF = 1, no errors

            // Act
            bool result = await _helper.PerformSecurityAccessAsync(key);

            // Assert
            Assert.That(result, Is.True);

            // Verify that the correct registers were written
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_CMD, 0x0C), Times.Once);
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_STAT, 0x80), Times.Once);
        }

        [Test]
        public async Task GetFlashProtectionAsync_ReturnsProtectionByte()
        {
            // Arrange
            byte expectedProtection = 0x42;
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.MemoryMap.FPROT_ADDRESS))
                .Returns(Task.FromResult(expectedProtection));

            // Act
            byte? protection = await _helper.GetFlashProtectionAsync();

            // Assert
            Assert.That(protection, Is.EqualTo(expectedProtection));
            _mockRegisterAccess.Verify(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.MemoryMap.FPROT_ADDRESS), Times.Once);
        }

        [Test]
        public async Task SetFlashProtectionAsync_WithValidProtection_ReturnsTrue()
        {
            // Arrange
            byte protection = 0x42;

            // Set up mock register access for ProgramFlashByteAsync
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsAny<uint>(), It.IsAny<byte>()))
                .Returns(Task.FromResult(true));

            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsAny<uint>()))
                .Returns(Task.FromResult<byte>(0x40)); // CCIF = 1, no errors

            // Set up mock for verification
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.MemoryMap.FPROT_ADDRESS))
                .Returns(Task.FromResult(protection));

            // Act
            bool result = await _helper.SetFlashProtectionAsync(protection);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task EnableFlashProtectionAsync_WithValidParameters_ReturnsTrue()
        {
            // Arrange
            bool protectLowerRange = true;
            bool protectHigherRange = true;
            byte lowerRangeSize = 3;
            byte higherRangeSize = 2;

            // Set up mock register access for ProgramFlashByteAsync
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsAny<uint>(), It.IsAny<byte>()))
                .Returns(Task.FromResult(true));

            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsAny<uint>()))
                .Returns(Task.FromResult<byte>(0x40)); // CCIF = 1, no errors

            // Set up mock for verification
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.MemoryMap.FPROT_ADDRESS))
                .Returns(Task.FromResult<byte>(0x23)); // Expected protection byte

            // Act
            bool result = await _helper.EnableFlashProtectionAsync(protectLowerRange, protectHigherRange, lowerRangeSize, higherRangeSize);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task GetEEPROMProtectionAsync_ReturnsProtectionByte()
        {
            // Arrange
            byte expectedProtection = 0x42;
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.MemoryMap.EPROT_ADDRESS))
                .Returns(Task.FromResult(expectedProtection));

            // Act
            byte? protection = await _helper.GetEEPROMProtectionAsync();

            // Assert
            Assert.That(protection, Is.EqualTo(expectedProtection));
            _mockRegisterAccess.Verify(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.MemoryMap.EPROT_ADDRESS), Times.Once);
        }

        [Test]
        public async Task InitializeMPUAsync_ReturnsTrue()
        {
            // Arrange
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsAny<uint>(), It.IsAny<byte>()))
                .Returns(Task.FromResult(true));

            // Act
            bool result = await _helper.InitializeMPUAsync();

            // Assert
            Assert.That(result, Is.True);

            // Verify that the MPU registers were written
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.MPU.MPUFLG, 0xFF), Times.Once);
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.MPU.MPUSEL, 0x00), Times.Once);
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.MPU.MPUDESC0, 0xF0), Times.Once);
        }

        [Test]
        public async Task ConfigureMPUDescriptorAsync_WithValidParameters_ReturnsTrue()
        {
            // Arrange
            byte descriptorIndex = 1;
            uint startAddress = 0x100000;
            uint endAddress = 0x101000;

            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsAny<uint>(), It.IsAny<byte>()))
                .Returns(Task.FromResult(true));

            // Act
            bool result = await _helper.ConfigureMPUDescriptorAsync(
                descriptorIndex,
                startAddress,
                endAddress,
                true,  // writeProtect
                true); // nonExecutable

            // Assert
            Assert.That(result, Is.True);

            // Verify that the MPU registers were written
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.MPU.MPUSEL, descriptorIndex), Times.Once);
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.MPU.MPUDESC0, It.IsAny<byte>()), Times.Once);
        }

        [Test]
        public void GetFlashHealthStatus_ReturnsValidStatus()
        {
            // Act
            var status = _helper.GetFlashHealthStatus();

            // Assert
            Assert.That(status, Is.Not.Null);
            Assert.That(status.IsHealthy, Is.True);
        }

        [Test]
        public async Task PerformFlashHealthCheckAsync_ReturnsTrue()
        {
            // Arrange
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsAny<uint>()))
                .Returns(Task.FromResult<byte>(0x00)); // No errors

            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsAny<uint>(), It.IsAny<byte>()))
                .Returns(Task.FromResult(true));

            // Act
            bool result = await _helper.PerformFlashHealthCheckAsync();

            // Assert
            Assert.That(result, Is.True);

            // Get the updated status
            var status = _helper.GetFlashHealthStatus();
            Assert.That(status.IsHealthy, Is.True);
        }
    }
}

