@echo off
echo ========================================
echo VolvoFlashWR - Real Hardware Mode
echo ========================================
echo.
echo IMPORTANT: Make sure you have:
echo 1. Vocom 1 adapter connected via USB
echo 2. Vocom driver installed (CommunicationUnitInstaller-2.5.0.0.msi)
echo 3. PTT (Premium Tech Tool) is NOT running
echo.
echo Press any key to continue or Ctrl+C to exit...
pause >nul

REM Set environment for real hardware
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=true
set LOG_LEVEL=Debug
set PHOENIX_VOCOM_ENABLED=true

echo Starting VolvoFlashWR in Real Hardware Mode...
echo Environment: USE_DUMMY_IMPLEMENTATIONS=%USE_DUMMY_IMPLEMENTATIONS%
echo Environment: VERBOSE_LOGGING=%VERBOSE_LOGGING%
echo.

REM Verify critical libraries
echo Verifying critical libraries...
if exist "WUDFPuma.dll" (
    echo [OK] WUDFPuma.dll found
) else (
    echo [ERROR] WUDFPuma.dll not found - Install Vocom driver first!
    pause
    exit /b 1
)

if exist "Volvo.ApciPlus.dll" (
    echo [OK] Volvo.ApciPlus.dll found
) else (
    echo [ERROR] Volvo.ApciPlus.dll not found!
    pause
    exit /b 1
)

echo All libraries verified - Starting application...
"VolvoFlashWR.Launcher.exe" --mode=Normal

echo Application has exited.
pause
