using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.UI.Converters
{
    public class ConnectionStatusToBrushConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ECUConnectionStatus status)
            {
                switch (status)
                {
                    case ECUConnectionStatus.Connected:
                        return new SolidColorBrush(Colors.Green);
                    case ECUConnectionStatus.Connecting:
                        return new SolidColorBrush(Colors.Orange);
                    case ECUConnectionStatus.Disconnected:
                        return new SolidColorBrush(Colors.Red);
                    case ECUConnectionStatus.Error:
                        return new SolidColorBrush(Colors.DarkRed);
                    default:
                        return new SolidColorBrush(Colors.Gray);
                }
            }

            return new SolidColorBrush(Colors.Gray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
