using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Protocols
{
    /// <summary>
    /// Interface for ECU protocol handlers
    /// </summary>
    public interface IECUProtocolHandler
    {
        /// <summary>
        /// Gets the protocol type
        /// </summary>
        ECUProtocolType ProtocolType { get; }

        /// <summary>
        /// Initializes the protocol handler
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        Task<bool> InitializeAsync();

        /// <summary>
        /// Connects to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        Task<bool> ConnectAsync(ECUDevice ecu);

        /// <summary>
        /// Disconnects from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        Task<bool> DisconnectAsync(ECUDevice ecu);

        /// <summary>
        /// Sets the operating mode
        /// </summary>
        /// <param name="mode">The operating mode to set</param>
        /// <returns>True if mode change is successful, false otherwise</returns>
        Task<bool> SetOperatingModeAsync(OperatingMode mode);

        /// <summary>
        /// Sets the communication speed mode (High or Low)
        /// </summary>
        /// <param name="ecu">The ECU to set the speed mode for</param>
        /// <param name="speedMode">The speed mode to set</param>
        /// <returns>True if speed mode change is successful, false otherwise</returns>
        Task<bool> SetCommunicationSpeedModeAsync(ECUDevice ecu, CommunicationSpeedMode speedMode);

        /// <summary>
        /// Reads EEPROM data from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>EEPROM data as byte array</returns>
        Task<byte[]> ReadEEPROMAsync(ECUDevice ecu);

        /// <summary>
        /// Reads a chunk of EEPROM data from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <param name="offset">The offset to start reading from</param>
        /// <param name="size">The number of bytes to read</param>
        /// <returns>EEPROM data chunk as byte array</returns>
        Task<byte[]> ReadEEPROMChunkAsync(ECUDevice ecu, int offset, int size);

        /// <summary>
        /// Writes EEPROM data to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="data">The data to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteEEPROMAsync(ECUDevice ecu, byte[] data);

        /// <summary>
        /// Writes a chunk of EEPROM data to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="offset">The offset to start writing at</param>
        /// <param name="data">The data chunk to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteEEPROMChunkAsync(ECUDevice ecu, int offset, byte[] data);

        /// <summary>
        /// Reads microcontroller code from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Microcontroller code as byte array</returns>
        Task<byte[]> ReadMicrocontrollerCodeAsync(ECUDevice ecu);

        /// <summary>
        /// Reads a chunk of microcontroller code from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <param name="offset">The offset to start reading from</param>
        /// <param name="size">The number of bytes to read</param>
        /// <returns>Microcontroller code chunk as byte array</returns>
        Task<byte[]> ReadMicrocontrollerCodeChunkAsync(ECUDevice ecu, int offset, int size);

        /// <summary>
        /// Writes microcontroller code to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="code">The code to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteMicrocontrollerCodeAsync(ECUDevice ecu, byte[] code);

        /// <summary>
        /// Writes a chunk of microcontroller code to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="offset">The offset to start writing at</param>
        /// <param name="code">The code chunk to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteMicrocontrollerCodeChunkAsync(ECUDevice ecu, int offset, byte[] code);

        /// <summary>
        /// Reads active faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>List of active faults</returns>
        Task<List<ECUFault>> ReadActiveFaultsAsync(ECUDevice ecu);

        /// <summary>
        /// Reads inactive faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>List of inactive faults</returns>
        Task<List<ECUFault>> ReadInactiveFaultsAsync(ECUDevice ecu);

        /// <summary>
        /// Clears faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <returns>True if clearing is successful, false otherwise</returns>
        Task<bool> ClearFaultsAsync(ECUDevice ecu);

        /// <summary>
        /// Clears all faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <returns>True if clearing is successful, false otherwise</returns>
        Task<bool> ClearAllFaultsAsync(ECUDevice ecu);

        /// <summary>
        /// Clears specific faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <param name="faultCodes">The specific fault codes to clear</param>
        /// <returns>True if clearing is successful, false otherwise</returns>
        Task<bool> ClearSpecificFaultsAsync(ECUDevice ecu, List<string> faultCodes);

        /// <summary>
        /// Reads parameters from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Dictionary of parameter names and values</returns>
        Task<Dictionary<string, object>> ReadParametersAsync(ECUDevice ecu);

        /// <summary>
        /// Writes parameters to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="parameters">The parameters to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteParametersAsync(ECUDevice ecu, Dictionary<string, object> parameters);

        /// <summary>
        /// Writes a single parameter to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="parameterName">The name of the parameter to write</param>
        /// <param name="parameterValue">The value to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteParameterAsync(ECUDevice ecu, string parameterName, object parameterValue);

        /// <summary>
        /// Performs a diagnostic session on an ECU
        /// </summary>
        /// <param name="ecu">The ECU to diagnose</param>
        /// <returns>Diagnostic data</returns>
        Task<DiagnosticData> PerformDiagnosticSessionAsync(ECUDevice ecu);

        /// <summary>
        /// Cancels the current operation
        /// </summary>
        /// <returns>True if cancellation is successful, false otherwise</returns>
        Task<bool> CancelOperationAsync();
    }
}
