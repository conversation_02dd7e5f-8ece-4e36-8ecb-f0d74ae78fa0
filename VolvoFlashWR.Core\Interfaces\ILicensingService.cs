using System;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for the licensing service
    /// </summary>
    public interface ILicensingService
    {
        /// <summary>
        /// Event triggered when license status changes
        /// </summary>
        event EventHandler<LicenseInfo> LicenseStatusChanged;

        /// <summary>
        /// Initializes the licensing service
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        Task<bool> InitializeAsync();

        /// <summary>
        /// Gets the current license information
        /// </summary>
        /// <returns>The current license information</returns>
        LicenseInfo GetLicenseInfo();

        /// <summary>
        /// Checks if the application is licensed
        /// </summary>
        /// <returns>True if the application is licensed, false otherwise</returns>
        bool IsLicensed();

        /// <summary>
        /// Checks if the application is in trial period
        /// </summary>
        /// <returns>True if the application is in trial period, false otherwise</returns>
        bool IsInTrialPeriod();

        /// <summary>
        /// Gets the number of days remaining in the trial period
        /// </summary>
        /// <returns>The number of days remaining in the trial period, or 0 if the trial has expired</returns>
        int GetTrialDaysRemaining();

        /// <summary>
        /// Activates the application with the provided activation key
        /// </summary>
        /// <param name="activationKey">The activation key</param>
        /// <returns>The result of the activation attempt</returns>
        Task<ActivationResult> ActivateWithKeyAsync(string activationKey);

        /// <summary>
        /// Deactivates the current license
        /// </summary>
        /// <returns>True if deactivation is successful, false otherwise</returns>
        Task<bool> DeactivateAsync();

        /// <summary>
        /// Generates a valid activation key for testing purposes
        /// </summary>
        /// <param name="expirationDate">The expiration date for the key</param>
        /// <returns>A valid activation key</returns>
        string GenerateActivationKey(DateTime expirationDate);
    }
}
