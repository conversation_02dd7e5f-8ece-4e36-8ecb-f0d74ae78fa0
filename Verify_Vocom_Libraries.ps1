# Vocom Library Verification Script
# This script verifies that all necessary libraries for Vocom hardware communication are present

Write-Host "=== Vocom Library Verification Script ===" -ForegroundColor Green
Write-Host "Checking for required libraries for real Vocom hardware communication..." -ForegroundColor Yellow

# Define required libraries for Vocom communication
$RequiredLibraries = @(
    # Core Vocom Driver Libraries
    "WUDFPuma.dll",
    "WUDFUpdate_01009.dll", 
    "WdfCoInstaller01009.dll",
    "winusbcoinstaller2.dll",
    
    # APCI Communication Libraries
    "apci.dll",
    "apcidb.dll",
    
    # Phoenix Diag Libraries
    "PhoenixESW.dll",
    "PhoenixGeneral.dll",
    "PhoenixProducInformation.dll",
    "Rpci.dll",
    "Pc2.dll",
    
    # Volvo-specific Libraries
    "Volvo.ApciPlus.dll",
    "Volvo.ApciPlusData.dll",
    "Volvo.ApciPlusTea2Data.dll",
    "Volvo.NAMS.AC.Services.Interface.dll",
    "Volvo.NAMS.AC.Services.Interfaces.dll",
    "Volvo.NVS.Core.dll",
    "Volvo.NVS.Logging.dll",
    "Volvo.NVS.Persistence.dll",
    "Volvo.NVS.Persistence.NHibernate.dll",
    "VolvoIt.Baf.Utility.dll",
    "VolvoIt.Fido.Agent.Gateway.Contract.dll",
    "VolvoIt.Waf.ServiceContract.dll",
    "VolvoIt.Waf.Utility.dll",
    
    # Vodia Libraries
    "Vodia.CommonDomain.Model.dll",
    "Vodia.Contracts.Common.dll",
    "Vodia.UtilityComponent.dll",
    
    # Supporting Libraries
    "log4net.dll",
    "NHibernate.dll",
    "Newtonsoft.Json.dll",
    "AutoMapper.dll",
    "DotNetZip.dll",
    "Ionic.Zip.Reduced.dll",
    "SharpCompress.dll",
    "SystemInterface.dll",
    "WinSCPnet.dll"
)

# Check Libraries folder
$LibrariesPath = ".\Libraries"
if (-not (Test-Path $LibrariesPath)) {
    Write-Host "ERROR: Libraries folder not found!" -ForegroundColor Red
    exit 1
}

Write-Host "`nChecking required libraries in $LibrariesPath..." -ForegroundColor Cyan

$MissingLibraries = @()
$PresentLibraries = @()

foreach ($library in $RequiredLibraries) {
    $libraryPath = Join-Path $LibrariesPath $library
    if (Test-Path $libraryPath) {
        $PresentLibraries += $library
        Write-Host "✓ $library" -ForegroundColor Green
    } else {
        $MissingLibraries += $library
        Write-Host "✗ $library" -ForegroundColor Red
    }
}

# Summary
Write-Host "`n=== SUMMARY ===" -ForegroundColor Yellow
Write-Host "Present Libraries: $($PresentLibraries.Count)" -ForegroundColor Green
Write-Host "Missing Libraries: $($MissingLibraries.Count)" -ForegroundColor Red

if ($MissingLibraries.Count -gt 0) {
    Write-Host "`nMissing Libraries:" -ForegroundColor Red
    foreach ($missing in $MissingLibraries) {
        Write-Host "  - $missing" -ForegroundColor Red
    }
    
    Write-Host "`nAttempting to copy missing libraries from system..." -ForegroundColor Yellow
    
    # Try to copy from Phoenix Diag installation
    $PhoenixPath = "C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021"
    if (Test-Path $PhoenixPath) {
        foreach ($missing in $MissingLibraries) {
            $sourcePath = Join-Path $PhoenixPath $missing
            if (Test-Path $sourcePath) {
                try {
                    Copy-Item $sourcePath $LibrariesPath -Force
                    Write-Host "✓ Copied $missing from Phoenix Diag" -ForegroundColor Green
                } catch {
                    Write-Host "✗ Failed to copy $missing`: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
    }
    
    # Try to copy from Vocom driver installation
    $VocomPath = "C:\Program Files (x86)\88890020 Adapter\UMDF"
    if (Test-Path $VocomPath) {
        foreach ($missing in $MissingLibraries) {
            $sourcePath = Join-Path $VocomPath $missing
            if (Test-Path $sourcePath) {
                try {
                    Copy-Item $sourcePath $LibrariesPath -Force
                    Write-Host "✓ Copied $missing from Vocom driver" -ForegroundColor Green
                } catch {
                    Write-Host "✗ Failed to copy $missing`: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
    }
} else {
    Write-Host "`n✓ All required libraries are present!" -ForegroundColor Green
}

# Check library versions and dependencies
Write-Host "`n=== LIBRARY INFORMATION ===" -ForegroundColor Yellow
$CoreLibraries = @("WUDFPuma.dll", "apci.dll", "Volvo.ApciPlus.dll")

foreach ($coreLib in $CoreLibraries) {
    $libPath = Join-Path $LibrariesPath $coreLib
    if (Test-Path $libPath) {
        try {
            $fileInfo = Get-Item $libPath
            $versionInfo = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($libPath)
            Write-Host "$coreLib - Version: $($versionInfo.FileVersion) - Size: $($fileInfo.Length) bytes" -ForegroundColor Cyan
        } catch {
            Write-Host "$coreLib - Unable to read version information" -ForegroundColor Yellow
        }
    }
}

Write-Host "`n=== VERIFICATION COMPLETE ===" -ForegroundColor Green
Write-Host "The application should now be ready for real Vocom hardware communication." -ForegroundColor Green
Write-Host "Use 'Run_Normal_Mode.bat' to start the application in normal mode." -ForegroundColor Cyan
