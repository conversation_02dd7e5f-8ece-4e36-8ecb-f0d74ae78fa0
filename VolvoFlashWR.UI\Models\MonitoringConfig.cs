using System.Collections.Generic;

namespace VolvoFlashWR.UI.Models
{
    /// <summary>
    /// Configuration for the real-time monitoring system
    /// </summary>
    public class MonitoringConfig
    {
        /// <summary>
        /// The name of the ECU being monitored
        /// </summary>
        public string ECUName { get; set; } = string.Empty;

        /// <summary>
        /// The refresh interval for monitoring (e.g., "100ms", "1s")
        /// </summary>
        public string RefreshInterval { get; set; } = "1s";

        /// <summary>
        /// The maximum number of data points to keep in memory
        /// </summary>
        public int MaxDataPoints { get; set; }

        /// <summary>
        /// The list of parameters to monitor
        /// </summary>
        public List<string> Parameters { get; set; } = new List<string>();
    }
}
