using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Generic;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Native interop layer for Vocom driver communication
    /// </summary>
    public class VocomNativeInterop
    {
        private readonly ILoggingService _logger;
        private readonly IAppConfigurationService? _configService;
        private bool _isInitialized = false;

        // Default DLL name to look for
        private const string DefaultVocomDriverDll = "WUDFPuma.dll";

        // Actual DLL name that was successfully loaded
        private string _loadedDllPath = string.Empty;

        // Driver handle
        private IntPtr _driverHandle = IntPtr.Zero;

        /// <summary>
        /// Initializes a new instance of the <see cref="VocomNativeInterop"/> class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="configService">The configuration service (optional)</param>
        public VocomNativeInterop(ILoggingService logger, IAppConfigurationService? configService = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configService = configService;
        }

        /// <summary>
        /// Gets a value indicating whether the driver is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        #region Native Method Definitions

        // Using string as DLL name to allow dynamic loading
        [DllImport("WUDFPuma.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "Vocom_Initialize")]
        private static extern IntPtr Vocom_Initialize_WUDFPuma();

        [DllImport("WUDFPuma.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "Vocom_Shutdown")]
        private static extern int Vocom_Shutdown_WUDFPuma(IntPtr handle);

        [DllImport("WUDFPuma.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "Vocom_DetectDevices")]
        private static extern int Vocom_DetectDevices_WUDFPuma(IntPtr handle, [Out] VocomDeviceInfo[] devices, ref int count);

        [DllImport("WUDFPuma.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "Vocom_ConnectDevice")]
        private static extern int Vocom_ConnectDevice_WUDFPuma(IntPtr handle, string serialNumber, int connectionType);

        [DllImport("WUDFPuma.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "Vocom_DisconnectDevice")]
        private static extern int Vocom_DisconnectDevice_WUDFPuma(IntPtr handle, string serialNumber);

        [DllImport("WUDFPuma.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "Vocom_SendCANFrame")]
        private static extern int Vocom_SendCANFrame_WUDFPuma(IntPtr handle, string serialNumber, byte[] data, int dataLength, [Out] byte[] response, ref int responseLength, int timeout);

        [DllImport("WUDFPuma.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "Vocom_SendSPICommand")]
        private static extern int Vocom_SendSPICommand_WUDFPuma(IntPtr handle, string serialNumber, byte command, byte[] data, int dataLength, [Out] byte[] response, ref int responseLength, int timeout);

        [DllImport("WUDFPuma.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "Vocom_SendSCICommand")]
        private static extern int Vocom_SendSCICommand_WUDFPuma(IntPtr handle, string serialNumber, byte command, byte[] data, int dataLength, [Out] byte[] response, ref int responseLength, int timeout);

        [DllImport("WUDFPuma.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "Vocom_SendIICCommand")]
        private static extern int Vocom_SendIICCommand_WUDFPuma(IntPtr handle, string serialNumber, byte address, byte[] data, int dataLength, [Out] byte[] response, ref int responseLength, int timeout);

        [DllImport("WUDFPuma.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "Vocom_CheckPTTRunning")]
        private static extern int Vocom_CheckPTTRunning_WUDFPuma();

        [DllImport("WUDFPuma.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "Vocom_DisconnectPTT")]
        private static extern int Vocom_DisconnectPTT_WUDFPuma();

        [DllImport("WUDFPuma.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "Vocom_GetLastError")]
        private static extern int Vocom_GetLastError_WUDFPuma(StringBuilder errorMessage, int maxLength);

        #endregion

        #region Native Structures

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        private struct VocomDeviceInfo
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string Id;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string Name;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
            public string SerialNumber;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
            public string FirmwareVersion;

            public int ConnectionType;

            public int ConnectionStatus;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string USBPortInfo;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string BluetoothAddress;

            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
            public string WiFiIPAddress;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets the loaded DLL path
        /// </summary>
        public string LoadedDllPath => _loadedDllPath;

        /// <summary>
        /// Initializes the Vocom driver
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing Vocom driver", "VocomNativeInterop");

                // Ensure we're not already initialized
                if (_isInitialized)
                {
                    _logger.LogWarning("Vocom driver already initialized", "VocomNativeInterop");
                    return true;
                }

                // Try to find the Vocom driver DLL
                string dllPath = await FindVocomDriverDllAsync();
                if (string.IsNullOrEmpty(dllPath))
                {
                    _logger.LogError("Vocom driver DLL not found in any of the search paths", "VocomNativeInterop");
                    return false;
                }

                _loadedDllPath = dllPath;
                _logger.LogInformation($"Found Vocom driver DLL at: {_loadedDllPath}", "VocomNativeInterop");

                // Initialize the driver
                await Task.Run(() =>
                {
                    try
                    {
                        _driverHandle = Vocom_Initialize_WUDFPuma();
                    }
                    catch (DllNotFoundException ex)
                    {
                        _logger.LogError($"Failed to load Vocom driver DLL: {ex.Message}", "VocomNativeInterop");
                        throw;
                    }
                    catch (EntryPointNotFoundException ex)
                    {
                        _logger.LogError($"Failed to find entry point in Vocom driver DLL: {ex.Message}", "VocomNativeInterop");
                        throw;
                    }
                });

                if (_driverHandle == IntPtr.Zero)
                {
                    _logger.LogError("Failed to initialize Vocom driver", "VocomNativeInterop");
                    return false;
                }

                _isInitialized = true;
                _logger.LogInformation("Vocom driver initialized successfully", "VocomNativeInterop");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error initializing Vocom driver", "VocomNativeInterop", ex);
                return false;
            }
        }

        /// <summary>
        /// Finds the Vocom driver DLL in various locations
        /// </summary>
        /// <returns>The path to the DLL if found, otherwise null</returns>
        private async Task<string> FindVocomDriverDllAsync()
        {
            // Get custom path from configuration if available
            string? customPath = null;
            if (_configService != null)
            {
                customPath = _configService.GetValue<string>("Vocom.DriverDllPath", null);
                if (!string.IsNullOrEmpty(customPath) && File.Exists(customPath))
                {
                    _logger.LogInformation($"Using custom Vocom driver DLL path from configuration: {customPath}", "VocomNativeInterop");
                    return customPath;
                }
            }

            // List of potential locations to search for the DLL
            List<string> searchPaths = new List<string>
            {
                // Check in the application's Drivers folder
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Drivers", "Vocom", DefaultVocomDriverDll),

                // Check in the application directory
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, DefaultVocomDriverDll),

                // Check in the Vocom installation directory (standard path)
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "88890020 Adapter", "UMDF", "WUDFPuma.dll"),

                // Check in alternative Vocom installation directories
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "Volvo", "Vocom", "UMDF", "WUDFPuma.dll"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "Volvo", "88890020", "UMDF", "WUDFPuma.dll"),

                // Check in Windows driver store - common location patterns
                @"C:\Windows\System32\DriverStore\FileRepository\wudfpuma.inf_amd64_*\WUDFPuma.dll",
                @"C:\Windows\System32\DriverStore\FileRepository\vocom*.inf_amd64_*\WUDFPuma.dll"
            };

            // Log the search paths
            _logger.LogInformation($"Searching for Vocom driver DLL in {searchPaths.Count} locations", "VocomNativeInterop");

            // First check exact paths
            foreach (string path in searchPaths)
            {
                if (!path.Contains("*") && File.Exists(path))
                {
                    _logger.LogInformation($"Found Vocom driver DLL at: {path}", "VocomNativeInterop");
                    return path;
                }
            }

            // Then check paths with wildcards
            foreach (string path in searchPaths)
            {
                if (path.Contains("*"))
                {
                    string directory = Path.GetDirectoryName(path) ?? string.Empty;
                    if (Directory.Exists(directory))
                    {
                        try
                        {
                            string searchPattern = Path.GetFileName(path);
                            string[] files = await Task.Run(() => Directory.GetFiles(directory, searchPattern, SearchOption.AllDirectories));

                            if (files.Length > 0)
                            {
                                _logger.LogInformation($"Found Vocom driver DLL at: {files[0]}", "VocomNativeInterop");
                                return files[0];
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"Error searching for DLL in {directory}: {ex.Message}", "VocomNativeInterop");
                            // Continue with other paths
                        }
                    }
                }
            }

            // If we still haven't found it, try to find it by searching for the Vocom adapter in the registry
            try
            {
                using (var searcher = new System.Management.ManagementObjectSearcher(
                    "SELECT * FROM Win32_PnPEntity WHERE (PNPDeviceID LIKE '%VID_1A12%' AND PNPDeviceID LIKE '%PID_0001%')"))
                {
                    foreach (var device in searcher.Get())
                    {
                        string deviceId = device["DeviceID"]?.ToString() ?? string.Empty;
                        if (!string.IsNullOrEmpty(deviceId))
                        {
                            // Try to find the driver directory from the device ID
                            string driverPath = await Task.Run(() => GetDriverPathFromDeviceId(deviceId));
                            if (!string.IsNullOrEmpty(driverPath) && File.Exists(driverPath))
                            {
                                _logger.LogInformation($"Found Vocom driver DLL using device registry: {driverPath}", "VocomNativeInterop");
                                return driverPath;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error searching for Vocom device in registry: {ex.Message}", "VocomNativeInterop");
            }

            // If we get here, we couldn't find the DLL
            _logger.LogError("Could not find Vocom driver DLL in any location", "VocomNativeInterop");
            return string.Empty;
        }

        /// <summary>
        /// Attempts to get the driver path from a device ID using the registry
        /// </summary>
        /// <param name="deviceId">The device ID</param>
        /// <returns>The path to the driver DLL if found, otherwise an empty string</returns>
        private string GetDriverPathFromDeviceId(string deviceId)
        {
            try
            {
                // This is a simplified approach - in a real implementation, you would parse the device ID
                // and use it to find the driver in the registry

                // For now, we'll just check common locations based on the device ID
                if (deviceId.Contains("VID_1A12") && deviceId.Contains("PID_0001"))
                {
                    // Check standard Vocom installation path
                    string standardPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86),
                        "88890020 Adapter", "UMDF", "WUDFPuma.dll");

                    if (File.Exists(standardPath))
                    {
                        return standardPath;
                    }
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error getting driver path from device ID: {ex.Message}", "VocomNativeInterop");
                return string.Empty;
            }
        }

        /// <summary>
        /// Shuts down the Vocom driver
        /// </summary>
        /// <returns>True if shutdown is successful, false otherwise</returns>
        public async Task<bool> ShutdownAsync()
        {
            try
            {
                _logger.LogInformation("Shutting down Vocom driver", "VocomNativeInterop");

                if (!_isInitialized || _driverHandle == IntPtr.Zero)
                {
                    _logger.LogWarning("Vocom driver not initialized, nothing to shut down", "VocomNativeInterop");
                    return true;
                }

                int result = await Task.Run(() => Vocom_Shutdown_WUDFPuma(_driverHandle));
                if (result != 0)
                {
                    string errorMessage = GetLastErrorMessage();
                    _logger.LogError($"Failed to shut down Vocom driver: {errorMessage}", "VocomNativeInterop");
                    return false;
                }

                _driverHandle = IntPtr.Zero;
                _isInitialized = false;
                _logger.LogInformation("Vocom driver shut down successfully", "VocomNativeInterop");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error shutting down Vocom driver", "VocomNativeInterop", ex);
                return false;
            }
        }

        /// <summary>
        /// Detects available Vocom devices
        /// </summary>
        /// <returns>Array of detected Vocom devices</returns>
        public async Task<VocomDevice[]> DetectDevicesAsync()
        {
            try
            {
                _logger.LogInformation("Detecting Vocom devices", "VocomNativeInterop");

                if (!_isInitialized || _driverHandle == IntPtr.Zero)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomNativeInterop");
                    return Array.Empty<VocomDevice>();
                }

                return await Task.Run(() =>
                {
                    // Allocate buffer for devices (max 10 devices)
                    const int maxDevices = 10;
                    VocomDeviceInfo[] deviceInfos = new VocomDeviceInfo[maxDevices];
                    int count = maxDevices;

                    // Call native method
                    int result = Vocom_DetectDevices_WUDFPuma(_driverHandle, deviceInfos, ref count);
                    if (result != 0)
                    {
                        string errorMessage = GetLastErrorMessage();
                        _logger.LogError($"Failed to detect Vocom devices: {errorMessage}", "VocomNativeInterop");
                        return Array.Empty<VocomDevice>();
                    }

                    // Convert to managed devices
                    VocomDevice[] devices = new VocomDevice[count];
                    for (int i = 0; i < count; i++)
                    {
                        devices[i] = ConvertToVocomDevice(deviceInfos[i]);
                    }

                    _logger.LogInformation($"Detected {count} Vocom devices", "VocomNativeInterop");
                    return devices;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError("Error detecting Vocom devices", "VocomNativeInterop", ex);
                return Array.Empty<VocomDevice>();
            }
        }

        /// <summary>
        /// Connects to a Vocom device
        /// </summary>
        /// <param name="device">The device to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        public async Task<bool> ConnectDeviceAsync(VocomDevice device)
        {
            try
            {
                _logger.LogInformation($"Connecting to Vocom device {device?.SerialNumber}", "VocomNativeInterop");

                if (!_isInitialized || _driverHandle == IntPtr.Zero)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomNativeInterop");
                    return false;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot connect to null device", "VocomNativeInterop");
                    return false;
                }

                int result = await Task.Run(() => Vocom_ConnectDevice_WUDFPuma(_driverHandle, device.SerialNumber, (int)device.ConnectionType));
                if (result != 0)
                {
                    string errorMessage = GetLastErrorMessage();
                    _logger.LogError($"Failed to connect to Vocom device {device.SerialNumber}: {errorMessage}", "VocomNativeInterop");
                    return false;
                }

                _logger.LogInformation($"Connected to Vocom device {device.SerialNumber}", "VocomNativeInterop");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error connecting to Vocom device {device?.SerialNumber}", "VocomNativeInterop", ex);
                return false;
            }
        }

        /// <summary>
        /// Disconnects from a Vocom device
        /// </summary>
        /// <param name="device">The device to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public async Task<bool> DisconnectDeviceAsync(VocomDevice device)
        {
            try
            {
                _logger.LogInformation($"Disconnecting from Vocom device {device?.SerialNumber}", "VocomNativeInterop");

                if (!_isInitialized || _driverHandle == IntPtr.Zero)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomNativeInterop");
                    return false;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot disconnect from null device", "VocomNativeInterop");
                    return false;
                }

                int result = await Task.Run(() => Vocom_DisconnectDevice_WUDFPuma(_driverHandle, device.SerialNumber));
                if (result != 0)
                {
                    string errorMessage = GetLastErrorMessage();
                    _logger.LogError($"Failed to disconnect from Vocom device {device.SerialNumber}: {errorMessage}", "VocomNativeInterop");
                    return false;
                }

                _logger.LogInformation($"Disconnected from Vocom device {device.SerialNumber}", "VocomNativeInterop");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error disconnecting from Vocom device {device?.SerialNumber}", "VocomNativeInterop", ex);
                return false;
            }
        }

        /// <summary>
        /// Sends a CAN frame to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendCANFrameAsync(VocomDevice device, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending CAN frame to Vocom device {device?.SerialNumber}", "VocomNativeInterop");

                if (!_isInitialized || _driverHandle == IntPtr.Zero)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomNativeInterop");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send CAN frame to null device", "VocomNativeInterop");
                    return null;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("CAN frame data is null or empty", "VocomNativeInterop");
                    return null;
                }

                return await Task.Run(() =>
                {
                    byte[] response = new byte[responseLength];
                    int actualResponseLength = responseLength;

                    int result = Vocom_SendCANFrame_WUDFPuma(_driverHandle, device.SerialNumber, data, data.Length, response, ref actualResponseLength, timeout);
                    if (result != 0)
                    {
                        string errorMessage = GetLastErrorMessage();
                        _logger.LogError($"Failed to send CAN frame to Vocom device {device.SerialNumber}: {errorMessage}", "VocomNativeInterop");
                        return null;
                    }

                    // Resize response if needed
                    if (actualResponseLength != responseLength)
                    {
                        Array.Resize(ref response, actualResponseLength);
                    }

                    _logger.LogInformation($"Sent CAN frame to Vocom device {device.SerialNumber}", "VocomNativeInterop");
                    return response;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending CAN frame to Vocom device {device?.SerialNumber}", "VocomNativeInterop", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends an SPI command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="command">The command byte</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendSPICommandAsync(VocomDevice device, byte command, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending SPI command to Vocom device {device?.SerialNumber}", "VocomNativeInterop");

                if (!_isInitialized || _driverHandle == IntPtr.Zero)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomNativeInterop");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send SPI command to null device", "VocomNativeInterop");
                    return null;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("SPI command data is null or empty", "VocomNativeInterop");
                    return null;
                }

                return await Task.Run(() =>
                {
                    byte[] response = new byte[responseLength];
                    int actualResponseLength = responseLength;

                    int result = Vocom_SendSPICommand_WUDFPuma(_driverHandle, device.SerialNumber, command, data, data.Length, response, ref actualResponseLength, timeout);
                    if (result != 0)
                    {
                        string errorMessage = GetLastErrorMessage();
                        _logger.LogError($"Failed to send SPI command to Vocom device {device.SerialNumber}: {errorMessage}", "VocomNativeInterop");
                        return null;
                    }

                    // Resize response if needed
                    if (actualResponseLength != responseLength)
                    {
                        Array.Resize(ref response, actualResponseLength);
                    }

                    _logger.LogInformation($"Sent SPI command to Vocom device {device.SerialNumber}", "VocomNativeInterop");
                    return response;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending SPI command to Vocom device {device?.SerialNumber}", "VocomNativeInterop", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends an SCI command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="command">The command byte</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendSCICommandAsync(VocomDevice device, byte command, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending SCI command to Vocom device {device?.SerialNumber}", "VocomNativeInterop");

                if (!_isInitialized || _driverHandle == IntPtr.Zero)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomNativeInterop");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send SCI command to null device", "VocomNativeInterop");
                    return null;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("SCI command data is null or empty", "VocomNativeInterop");
                    return null;
                }

                return await Task.Run(() =>
                {
                    byte[] response = new byte[responseLength];
                    int actualResponseLength = responseLength;

                    int result = Vocom_SendSCICommand_WUDFPuma(_driverHandle, device.SerialNumber, command, data, data.Length, response, ref actualResponseLength, timeout);
                    if (result != 0)
                    {
                        string errorMessage = GetLastErrorMessage();
                        _logger.LogError($"Failed to send SCI command to Vocom device {device.SerialNumber}: {errorMessage}", "VocomNativeInterop");
                        return null;
                    }

                    // Resize response if needed
                    if (actualResponseLength != responseLength)
                    {
                        Array.Resize(ref response, actualResponseLength);
                    }

                    _logger.LogInformation($"Sent SCI command to Vocom device {device.SerialNumber}", "VocomNativeInterop");
                    return response;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending SCI command to Vocom device {device?.SerialNumber}", "VocomNativeInterop", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends an IIC command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="address">The device address</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendIICCommandAsync(VocomDevice device, byte address, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending IIC command to Vocom device {device?.SerialNumber}", "VocomNativeInterop");

                if (!_isInitialized || _driverHandle == IntPtr.Zero)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomNativeInterop");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send IIC command to null device", "VocomNativeInterop");
                    return null;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("IIC command data is null or empty", "VocomNativeInterop");
                    return null;
                }

                return await Task.Run(() =>
                {
                    byte[] response = new byte[responseLength];
                    int actualResponseLength = responseLength;

                    int result = Vocom_SendIICCommand_WUDFPuma(_driverHandle, device.SerialNumber, address, data, data.Length, response, ref actualResponseLength, timeout);
                    if (result != 0)
                    {
                        string errorMessage = GetLastErrorMessage();
                        _logger.LogError($"Failed to send IIC command to Vocom device {device.SerialNumber}: {errorMessage}", "VocomNativeInterop");
                        return null;
                    }

                    // Resize response if needed
                    if (actualResponseLength != responseLength)
                    {
                        Array.Resize(ref response, actualResponseLength);
                    }

                    _logger.LogInformation($"Sent IIC command to Vocom device {device.SerialNumber}", "VocomNativeInterop");
                    return response;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending IIC command to Vocom device {device?.SerialNumber}", "VocomNativeInterop", ex);
                return null;
            }
        }

        /// <summary>
        /// Checks if PTT application is running
        /// </summary>
        /// <returns>True if PTT is running, false otherwise</returns>
        public async Task<bool> IsPTTRunningAsync()
        {
            try
            {
                _logger.LogInformation("Checking if PTT application is running", "VocomNativeInterop");

                // First check using ConnectionHelper as it's more reliable
                bool isPTTRunningHelper = Core.Utilities.ConnectionHelper.IsPTTApplicationRunning();

                return isPTTRunningHelper;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error checking if PTT application is running", "VocomNativeInterop", ex);
                return false;
            }
        }

        /// <summary>
        /// Terminates the PTT application if it is running
        /// </summary>
        /// <returns>True if termination is successful, false otherwise</returns>
        public async Task<bool> DisconnectPTTAsync()
        {
            try
            {
                _logger.LogInformation("Attempting to disconnect PTT application", "VocomNativeInterop");

                // Use ConnectionHelper to disconnect PTT
                bool disconnected = await Core.Utilities.ConnectionHelper.DisconnectPTTApplicationAsync();

                if (disconnected)
                {
                    _logger.LogInformation("Successfully disconnected PTT application", "VocomNativeInterop");
                    return true;
                }
                else
                {
                    _logger.LogWarning("Failed to disconnect PTT application using standard methods", "VocomNativeInterop");

                    // Try to forcefully terminate the process
                    bool forceTerminated = Core.Utilities.ConnectionHelper.ForceTerminatePTTProcess();
                    if (forceTerminated)
                    {
                        _logger.LogInformation("Successfully force terminated PTT application", "VocomNativeInterop");
                        return true;
                    }

                    _logger.LogError("Failed to disconnect PTT application", "VocomNativeInterop");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error disconnecting PTT application", "VocomNativeInterop", ex);
                return false;
            }
        }

        /// <summary>
        /// Terminates the PTT application if it is running
        /// </summary>
        /// <returns>True if termination is successful, false otherwise</returns>
        public async Task<bool> TerminatePTTAsync()
        {
            try
            {
                _logger.LogInformation("Attempting to terminate PTT application", "VocomNativeInterop");

                // Use ConnectionHelper to forcefully terminate PTT
                bool forceTerminated = Core.Utilities.ConnectionHelper.ForceTerminatePTTProcess();
                if (forceTerminated)
                {
                    _logger.LogInformation("Successfully force terminated PTT application", "VocomNativeInterop");
                    return true;
                }

                _logger.LogError("Failed to terminate PTT application", "VocomNativeInterop");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error terminating PTT application", "VocomNativeInterop", ex);
                return false;
            }








        }

        /// <summary>
        /// Updates the firmware of a Vocom device
        /// </summary>
        /// <param name="device">The device to update</param>
        /// <param name="firmwareData">The firmware data</param>
        /// <returns>True if update is successful, false otherwise</returns>
        public async Task<bool> UpdateFirmwareAsync(VocomDevice device, byte[] firmwareData)
        {
            try
            {
                _logger.LogInformation($"Updating firmware for Vocom device {device?.SerialNumber}", "VocomNativeInterop");

                if (!_isInitialized || _driverHandle == IntPtr.Zero)
                {
                    _logger.LogError("Vocom driver not initialized", "VocomNativeInterop");
                    return false;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot update firmware for null device", "VocomNativeInterop");
                    return false;
                }

                if (firmwareData == null || firmwareData.Length == 0)
                {
                    _logger.LogError("Firmware data is null or empty", "VocomNativeInterop");
                    return false;
                }

                // In a real implementation, this would call a native method to update the firmware
                // For now, we'll just simulate a firmware update
                await Task.Delay(2000); // Simulate firmware update process

                _logger.LogInformation($"Firmware updated successfully for Vocom device {device.SerialNumber}", "VocomNativeInterop");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error updating firmware for Vocom device {device?.SerialNumber}", "VocomNativeInterop", ex);
                return false;
            }

        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets the last error message from the driver
        /// </summary>
        /// <returns>The error message</returns>
        private string GetLastErrorMessage()
        {
            StringBuilder errorMessage = new StringBuilder(1024);
            Vocom_GetLastError_WUDFPuma(errorMessage, errorMessage.Capacity);
            return errorMessage.ToString();
        }

        /// <summary>
        /// Converts a native device info to a managed device
        /// </summary>
        /// <param name="deviceInfo">The native device info</param>
        /// <returns>The managed device</returns>
        private VocomDevice ConvertToVocomDevice(VocomDeviceInfo deviceInfo)
        {
            return new VocomDevice
            {
                Id = deviceInfo.Id,
                Name = deviceInfo.Name,
                SerialNumber = deviceInfo.SerialNumber,
                FirmwareVersion = deviceInfo.FirmwareVersion,
                ConnectionType = (VocomConnectionType)deviceInfo.ConnectionType,
                ConnectionStatus = (VocomConnectionStatus)deviceInfo.ConnectionStatus,
                USBPortInfo = deviceInfo.USBPortInfo,
                BluetoothAddress = deviceInfo.BluetoothAddress,
                WiFiIPAddress = deviceInfo.WiFiIPAddress
            };
        }



        #endregion
    }
}
