عنوان المشروع: تطبيق شاحنات فولفو – إدارة وحدات التحكم والـ ECU
الهدف العام:
تطوير تطبيق يعمل على نظام ويندوز (10 وما فوق) يُستخدم مع شاحنات فولفو يقوم بعمليات قراءة وكتابة لوحدات التحكم (ECU) بما يشمل نسخ الـ EEPROM ونسخ رمز المايكروكونترولر. التطبيق سيكون قادرًا على الاتصال بأجهزة التحكم عبر وقوفي بيانات (خطّ سريع للتواصل وواحد بطيء – High/Low) وسيتكامل مع شاحنات تحتوي على وحدات مثل EMS APM ووحدات تحكم أخرى بداخلها سوفت وير مدمج.

المتطلبات التقنية:
المجال البرمجي ولغة التطوير:

استخدام WPF لتطوير واجهة المستخدم.

استخدام .NET Core 8 مع لغة C# في تطوير منطق التطبيق والاتصالات.

التواصل مع الأجهزة والعتاد:

المحول Vocom:

الاتصال عبر USB بحيث يظهر الجهاز في قائمة الأجهزة تحت اسم "Vocom - 88890300".

استخدام التعريف المحدد: Driver Vocom1 CommunicationUnitInstaller-2.5.0.0.msi.

دعم بدائل الاتصال مثل WiFi وضرورة تفعيل البلوتوث قبل بدء الاتصال.

ضرورة إنشاء طبقة اتصال مستقلة تتولى إدارة الاتصال مع Vocom (محاكاة عمل ptt بشكل خاص)؛ حيث يجب على التطبيق عند بدء التشغيل أن يقوم بفصل أداة ptt المتواجدة لضمان الحصرية.

وحدات التحكم والمكونات الإلكترونية:

قراءة وكتابة بيانات وحدات ECU التي تحتوي على خطين لنقل البيانات:

الخط السريع: لنقل البيانات بسرعة.

الخط البطيء: لنقل البيانات على وضعيات High/Low.

نسخ بيانات الـ EEPROM ونسخ micro controller من خلال بروتوكول محدد.

دعم الاتصال مع منصات ECU متعددة في الشاحنة، والاستفادة من المعلومات التفصيلية للـ ECU (تنشيط/تعطيل الأعطال، قراءة الـ parameters، وعمل Backup للبيانات).

الأنماط التشغيلية:

Open Mode: وضع تشغيل بديل قد يستخدم في ظروف معينة.

Bench Mode: الوضع الأساسي والمفضل. يجب التركيز على هذا الوضع لضمان الأداء الأمثل أثناء التشغيل على المنصة الثابتة (Bench).

المواصفات الميكروكونترولر:

اعتماد المخطط التالي في عملية القراءة والكتابة:

MC9S12XEP768MAL من NXP Semiconductors.

يجب الرجوع إلى الداتا شيت الخاص به (وهو موجود في مجلد MC9S12XEP100RMV1-1358561) (768 Kb FLASH، 48 Kb Data RAM، 16-bit I/O، معدل تردد يصل إلى 50 MHz، وغيرها من المواصفات المذكورة) لتحديد طرق الاتصال المناسبة (مثل CAN، SCI، SPI).

يجب أن تكون آلية إعادة الاتصال وإعادة تفعيل الأنظمة (مثل استعادة اتصال ptt بعد إنهاء العمليات) مدروسة بعناية.

آليات وإجراءات الأمان والاتصال:

عند بدء التطبيق:

التأكد من فصل أي تطبيقات أخرى (مثل ptt) قد تستخدم محول Vocom.

التحقق من حالة البلوتوث وجاهزية الجهاز للاتصال.

تفعيل مسارات WiFi إذا دعت الحاجة لتوفير اتصال بديل.

إدارة أخطاء الاتصالات ونقل البيانات مع إنشاء سجلات (logs) مفصلة للمساعدة في التشخيص.

المهام الرئيسية لتطوير التطبيق:
تهيئة بيئة العمل:

إنشاء مشروع WPF جديد باستخدام .NET Core 8 وC#.

إعداد بيئة التطوير لإجراء اختبارات الوحدة (Unit Testing) لضمان استقرار كل مكون.

إنشاء واجهة المستخدم (UI):

تصميم واجهة استخدام تفاعلية تُظهر حالة الاتصال، قائمة بالوحدات ECU المتصلة، وتفاصيل الأعطال والـ parameters.

تضمين أدوات اختيار وضع التشغيل (Open Mode وBench Mode) مع تنبيهات توضيحية.

طبقة الاتصال (Connection Layer):

تطوير طبقة مخصصة لإدارة الاتصال مع محول Vocom:

الكشف عن الجهاز والتعامل مع تعريفه "Vocom - 88890300".

تنفيذ منطق فصل/إلغاء أي اتصال مِن ptt عند بدء تشغيل التطبيق.

إدارة حالات الاتصال المتغيرة (USB، WiFi)، مع التحقق من تفعيل البلوتوث.

التفاعل مع وحدات التحكم (ECU Communication):

تطوير بروتوكولات قراءة وكتابة البيانات:

قراءة البيانات من خطوط البيانات السريعة والبطيئة.

التعامل مع قراءة الـ EEPROM والكود الموجود بالمايكروكونترولر.

تنسيق عملية قراءة الأعطال النشطة وغير النشطة وقراءة المعاملات.

تنفيذ وظائف النسخ الاحتياطي (Backup):

إنشاء آلية متكاملة لعمل Backup لوحدات ECU تُخزن نسخ من البيانات المُقرأَة.

ضمان سلامة البيانات مع إمكانية استرجاعها في حالة حدوث خلل.


ضمان تزامن البيانات بين التطبيق والمحرك الخدمي لجلب التحديثات والتحقق من تكامل المعلومات.

اختبار النظام والتوثيق:

إجراء اختبارات عملية للمعدات (Vocom، وحدات ECU، وغيرها) لضمان عمل النظام بكفاءة.

توثيق كود التطبيق، تفاصيل واجهات الاستخدام، وبروتوكولات الاتصال لتسهيل الصيانة والتطوير لاحقًا.

تعليمات إضافية وملاحظات:
فصل تطبيق ptt: يعد أمر فصل تطبيق ptt أمرًا حيويًا لبدء الاتصال بـ Vocom؛ تأكد من تصميم آلية تحقق تتأكد من انتهاء اتصال ptt قبل الشروع بتفعيل وظائف التطبيق.

ضمان الاتصال الثابت: بما أن الشاحنة تحتوي على عدة وحدات ECU، يجب ضمان استقرار الاتصال مع كل وحدة والتعامل مع فقدان الاتصال أو التأخير بطريقة تضمن عدم تلف البيانات.

التوسع في المستقبل: فكر في تصميم طبقات قابلة للتوسع حيث يمكن إضافة أنواع اتصال جديدة أو دعم مكونات إضافية قد تظهر في إصدارات مستقبلية من الشاحنات.

واجهة المستخدم وتجربة المستخدم: نظّم المعلومات والعمليات في واجهة مستخدم بديهية تبسط عملية التعامل مع وحدات ECU المعقدة. استخدام المؤشرات البصرية وتنبيهات النظام يساهم في ضمان تجربة استخدام سلسة وموثوقة.