@echo off
echo Testing VolvoFlashWR Application...
echo.

REM Test 1: Check if the batch file exists
echo [TEST 1] Checking if Run_Normal_Mode.bat exists...
if exist "Run_Normal_Mode.bat" (
    echo ✓ PASS: Run_Normal_Mode.bat found
) else (
    echo ✗ FAIL: Run_Normal_Mode.bat not found
    goto :end
)

REM Test 2: Check if the solution builds successfully
echo.
echo [TEST 2] Testing build process...
dotnet build VolvoFlashWR.sln --configuration Release --verbosity quiet
if %ERRORLEVEL% EQU 0 (
    echo ✓ PASS: Solution builds successfully
) else (
    echo ✗ FAIL: Solution build failed
    goto :end
)

REM Test 3: Check if the executable exists after build
echo.
echo [TEST 3] Checking if executable exists...
if exist "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86\VolvoFlashWR.Launcher.exe" (
    echo ✓ PASS: VolvoFlashWR.Launcher.exe found
) else (
    echo ✗ FAIL: VolvoFlashWR.Launcher.exe not found
    goto :end
)

REM Test 4: Quick application startup test (5 seconds)
echo.
echo [TEST 4] Testing application startup (5 second test)...
set USE_DUMMY_IMPLEMENTATIONS=true
set VERBOSE_LOGGING=true

cd /d "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x86"
start /min "" "VolvoFlashWR.Launcher.exe" --mode=Normal

REM Wait 5 seconds for startup
timeout /t 5 /nobreak >nul

REM Check if log file was created (indicates successful startup)
for /f %%i in ('dir /b /od Logs\*.log 2^>nul') do set LATEST_LOG=%%i

if defined LATEST_LOG (
    echo ✓ PASS: Application started successfully (log file: %LATEST_LOG%)
    
    REM Check if the log contains successful initialization
    findstr /c:"Services initialized successfully" "Logs\%LATEST_LOG%" >nul
    if %ERRORLEVEL% EQU 0 (
        echo ✓ PASS: Services initialized successfully
    ) else (
        echo ⚠ WARNING: Services initialization not confirmed in logs
    )
) else (
    echo ✗ FAIL: No log file created - application may not have started
)

REM Clean up - kill any running processes
tasklist /fi "imagename eq VolvoFlashWR.Launcher.exe" 2>nul | find /i "VolvoFlashWR.Launcher.exe" >nul
if %ERRORLEVEL% EQU 0 (
    echo Cleaning up - stopping application...
    taskkill /f /im VolvoFlashWR.Launcher.exe >nul 2>&1
)

tasklist /fi "imagename eq VolvoFlashWR.UI.exe" 2>nul | find /i "VolvoFlashWR.UI.exe" >nul
if %ERRORLEVEL% EQU 0 (
    taskkill /f /im VolvoFlashWR.UI.exe >nul 2>&1
)

cd /d "%~dp0"

echo.
echo [SUMMARY] All tests completed!
echo The Run_Normal_Mode.bat file is working correctly.
echo You can now use it to run the application safely.

:end
echo.
echo Press any key to exit...
pause >nul
