using System;
using System.Threading.Tasks;

namespace VolvoFlashWR.Communication.Microcontroller
{
    /// <summary>
    /// Interface for accessing microcontroller registers
    /// </summary>
    public interface IRegisterAccess
    {
        /// <summary>
        /// Reads a byte from a register
        /// </summary>
        /// <param name="register">The register address</param>
        /// <returns>The register value</returns>
        Task<byte> ReadRegisterByteAsync(uint register);

        /// <summary>
        /// Reads a word (2 bytes) from a register
        /// </summary>
        /// <param name="register">The register address</param>
        /// <returns>The register value</returns>
        Task<ushort> ReadRegisterWordAsync(uint register);

        /// <summary>
        /// Reads a long word (4 bytes) from a register
        /// </summary>
        /// <param name="register">The register address</param>
        /// <returns>The register value</returns>
        Task<uint> ReadRegisterLongAsync(uint register);

        /// <summary>
        /// Writes a byte to a register
        /// </summary>
        /// <param name="register">The register address</param>
        /// <param name="value">The value to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteRegisterByteAsync(uint register, byte value);

        /// <summary>
        /// Writes a word (2 bytes) to a register
        /// </summary>
        /// <param name="register">The register address</param>
        /// <param name="value">The value to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteRegisterWordAsync(uint register, ushort value);

        /// <summary>
        /// Writes a long word (4 bytes) to a register
        /// </summary>
        /// <param name="register">The register address</param>
        /// <param name="value">The value to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteRegisterLongAsync(uint register, uint value);
    }
}
