using System;

namespace VolvoFlashWR.Communication.Microcontroller
{
    /// <summary>
    /// Represents the health status of flash memory
    /// </summary>
    public class FlashHealthStatus
    {
        /// <summary>
        /// Gets or sets whether the flash memory is healthy
        /// </summary>
        public bool IsHealthy { get; set; } = true;

        /// <summary>
        /// Gets or sets the time of the last health check
        /// </summary>
        public DateTime LastCheckTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// Gets or sets the time of the last error
        /// </summary>
        public DateTime LastErrorTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// Gets or sets the error count
        /// </summary>
        public int ErrorCount { get; set; } = 0;

        /// <summary>
        /// Gets or sets the last error code
        /// </summary>
        public byte LastErrorCode { get; set; } = 0;

        /// <summary>
        /// Gets or sets the number of single-bit errors detected
        /// </summary>
        public int SingleBitErrorCount { get; set; } = 0;

        /// <summary>
        /// Gets or sets the number of multi-bit errors detected
        /// </summary>
        public int MultiBitErrorCount { get; set; } = 0;

        /// <summary>
        /// Gets or sets the number of successful flash operations
        /// </summary>
        public int SuccessfulOperationCount { get; set; } = 0;

        /// <summary>
        /// Gets or sets the number of failed flash operations
        /// </summary>
        public int FailedOperationCount { get; set; } = 0;

        /// <summary>
        /// Gets or sets the number of retried flash operations
        /// </summary>
        public int RetriedOperationCount { get; set; } = 0;

        /// <summary>
        /// Gets or sets the number of sectors erased
        /// </summary>
        public int SectorsErasedCount { get; set; } = 0;

        /// <summary>
        /// Gets or sets the number of phrases programmed
        /// </summary>
        public int PhrasesProgrammedCount { get; set; } = 0;

        /// <summary>
        /// Gets or sets the number of phrases read
        /// </summary>
        public int PhrasesReadCount { get; set; } = 0;

        /// <summary>
        /// Gets or sets the total bytes read
        /// </summary>
        public long TotalBytesRead { get; set; } = 0;

        /// <summary>
        /// Gets or sets the total bytes written
        /// </summary>
        public long TotalBytesWritten { get; set; } = 0;

        /// <summary>
        /// Gets or sets the estimated remaining program/erase cycles
        /// </summary>
        public int EstimatedRemainingCycles { get; set; } = MC9S12XEP100Configuration.FlashPerformance.PROGRAM_ERASE_CYCLES;

        /// <summary>
        /// Gets a summary of the flash health status
        /// </summary>
        /// <returns>A summary string</returns>
        public string GetSummary()
        {
            string status = IsHealthy ? "Healthy" : "Unhealthy";
            string lastCheck = LastCheckTime == DateTime.MinValue ? "Never" : LastCheckTime.ToString("yyyy-MM-dd HH:mm:ss");
            string lastError = LastErrorTime == DateTime.MinValue ? "Never" : LastErrorTime.ToString("yyyy-MM-dd HH:mm:ss");

            return $"Flash Status: {status}\n" +
                   $"Last Check: {lastCheck}\n" +
                   $"Last Error: {lastError}\n" +
                   $"Error Count: {ErrorCount}\n" +
                   $"Single-Bit Errors: {SingleBitErrorCount}\n" +
                   $"Multi-Bit Errors: {MultiBitErrorCount}\n" +
                   $"Successful Operations: {SuccessfulOperationCount}\n" +
                   $"Failed Operations: {FailedOperationCount}\n" +
                   $"Retried Operations: {RetriedOperationCount}\n" +
                   $"Estimated Remaining Cycles: {EstimatedRemainingCycles}";
        }

        /// <summary>
        /// Updates the flash health status based on a successful operation
        /// </summary>
        /// <param name="bytesProcessed">The number of bytes processed</param>
        /// <param name="isRead">Whether the operation was a read operation</param>
        public void UpdateSuccessfulOperation(int bytesProcessed, bool isRead)
        {
            SuccessfulOperationCount++;
            
            if (isRead)
            {
                TotalBytesRead += bytesProcessed;
                PhrasesReadCount += (bytesProcessed + MC9S12XEP100Configuration.PHRASE_SIZE - 1) / MC9S12XEP100Configuration.PHRASE_SIZE;
            }
            else
            {
                TotalBytesWritten += bytesProcessed;
                PhrasesProgrammedCount += (bytesProcessed + MC9S12XEP100Configuration.PHRASE_SIZE - 1) / MC9S12XEP100Configuration.PHRASE_SIZE;
                
                // Update estimated remaining cycles based on program operations
                // Each program operation reduces the remaining cycles
                if (EstimatedRemainingCycles > 0)
                {
                    EstimatedRemainingCycles--;
                }
            }
        }

        /// <summary>
        /// Updates the flash health status based on a failed operation
        /// </summary>
        /// <param name="errorCode">The error code</param>
        public void UpdateFailedOperation(byte errorCode)
        {
            FailedOperationCount++;
            ErrorCount++;
            LastErrorTime = DateTime.Now;
            LastErrorCode = errorCode;
            IsHealthy = false;
        }

        /// <summary>
        /// Updates the flash health status based on an ECC error
        /// </summary>
        /// <param name="isSingleBit">Whether the error is a single-bit error</param>
        public void UpdateECCError(bool isSingleBit)
        {
            if (isSingleBit)
            {
                SingleBitErrorCount++;
            }
            else
            {
                MultiBitErrorCount++;
                IsHealthy = false;
            }
            
            ErrorCount++;
            LastErrorTime = DateTime.Now;
        }

        /// <summary>
        /// Updates the flash health status based on a sector erase operation
        /// </summary>
        public void UpdateSectorErase()
        {
            SectorsErasedCount++;
            
            // Update estimated remaining cycles based on erase operations
            // Each erase operation reduces the remaining cycles
            if (EstimatedRemainingCycles > 0)
            {
                EstimatedRemainingCycles--;
            }
        }

        /// <summary>
        /// Updates the flash health status based on a retried operation
        /// </summary>
        public void UpdateRetriedOperation()
        {
            RetriedOperationCount++;
        }
    }
}
