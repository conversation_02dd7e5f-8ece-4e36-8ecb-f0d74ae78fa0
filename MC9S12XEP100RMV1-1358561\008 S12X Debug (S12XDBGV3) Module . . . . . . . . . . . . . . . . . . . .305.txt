﻿Chapter 8
S12X Debug (S12XDBGV3) Module

Table 8-1. Revision History

Revision Sections
Revision Date Description of Changes

Number Affected

V03.20 14 Sep 2007 *******/8-317 - Clarified reserved State Sequencer encodings.

*******/8-329 - Added single databyte comparison limitation information
V03.21 23 Oct 2007

*******/8-330 - Added statement about interrupt vector fetches whilst tagging.

*******/8-334 - Removed LOOP1 tracing restriction NOTE.
V03.22 12 Nov 2007

*******/8-341 - Added pin reset effect NOTE.

V03.23 13 Nov 2007 General - Text readability improved, typo removed.

V03.24 04 Jan 2008 *******/8-336 - Corrected bit name.

V03.25 14 May 2008 General - Updated Revision History Table format. Corrected other paragraph formats.

V03.26 12 Sep 2012 General - Added missing full stops. Removed redundant quotation marks.

8.1 Introduction
The S12XDBG module provides an on-chip trace buffer with flexible triggering capability to allow non-
intrusive debug of application software. The S12XDBG module is optimized for the S12X 16-bit
architecture and allows debugging of CPU12Xand XGATE module operations.

Typically the S12XDBG module is used in conjunction with the S12XBDM module, whereby the user
configures the S12XDBG module for a debugging session over the BDM interface. Once configured the
S12XDBG module is armed and the device leaves BDM Mode returning control to the user program,
which is then monitored by the S12XDBG module. Alternatively the S12XDBG module can be configured
over a serial interface using SWI routines.

8.1.1 Glossary

Table 8-2.  Glossary Of Terms

Term Definition

COF Change Of Flow.
Change in the program flow due to a conditional branch, indexed jump or interrupt

BDM Background Debug Mode

DUG Device User Guide, describing the features of the device into which the DBG is integrated

WORD 16-bit data entity

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 305



Chapter 8 S12X Debug (S12XDBGV3) Module

Table 8-2.  Glossary Of Terms (continued)

Term Definition

Data Line 64-bit data entity

CPU CPU12X module

Tag Tags can be attached to XGATE or CPU opcodes as they enter the instruction pipe. If the tagged opcode
reaches the execution stage a tag hit occurs.

8.1.2 Overview
The comparators monitor the bus activity of the CPU12X and XGATE. When a match occurs the control
logic can trigger the state sequencer to a new state. On a transition to the Final State, bus tracing is triggered
and/or a breakpoint can be generated.

Independent of comparator matches a transition to Final State with associated tracing and breakpoint can
be triggered by the external TAGHI and TAGLO signals, or by an XGATE module S/W breakpoint request
or by writing to the TRIG control bit.

The trace buffer is visible through a 2-byte window in the register address map and can be read out using
standard 16-bit word reads. Tracing is disabled when the MCU system is secured.

8.1.3 Features
• Four comparators (A, B, C, and D)

— Comparators A and C compare the full address bus and full 16-bit data bus
— Comparators A and C feature a data bus mask register
— Comparators B and D compare the full address bus only
— Each comparator can be configured to monitor CPU12X or XGATE  buses
— Each comparator features selection of read or write access cycles
— Comparators B and D allow selection of byte or word access cycles
— Comparisons can be used as triggers for the state sequencer

• Three comparator modes
— Simple address/data comparator match mode
— Inside address range mode, Addmin ≤ Address ≤ Addmax
— Outside address range match mode, Address < Addmin or Address > Addmax

• Two types of triggers
— Tagged — This triggers just before a specific instruction begins execution
— Force — This triggers on the first instruction boundary after a match occurs.

• The following types of breakpoints
— CPU12X breakpoint entering BDM on breakpoint (BDM)
— CPU12X breakpoint executing SWI on breakpoint (SWI)
— XGATE breakpoint

• External CPU12X instruction tagging trigger independent of comparators

MC9S12XE-Family Reference Manual  Rev. 1.25

306 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

• XGATE S/W breakpoint request trigger independent of comparators
• TRIG Immediate software trigger independent of comparators
• Four trace modes

— Normal: change of flow (COF) PC information is stored (see Section *******.1) for change of
flow definition.

— Loop1: same as Normal but inhibits consecutive duplicate source address entries
— Detail: address and data for all cycles except free cycles and opcode fetches are stored
— Pure PC: All program counter addresses are stored.

• 4-stage state sequencer for trace buffer control
— Tracing session trigger linked to Final State of state sequencer
— Begin, End, and Mid alignment of tracing to trigger

8.1.4 Modes of Operation
The S12XDBG module can be used in all MCU functional modes.

During BDM hardware accesses and whilst the BDM module is active, CPU12X monitoring is disabled.
Thus breakpoints, comparators, and CPU12X bus tracing are disabled but XGATE bus monitoring
accessing the S12XDBG registers, including comparator registers, is still possible. While in active BDM
or during hardware BDM accesses, XGATE activity can still be compared, traced and can be used to
generate a breakpoint to the XGATE module. When the CPU12X enters active BDM Mode through a
BACKGROUND command, with the S12XDBG module armed, the S12XDBG remains armed.

The S12XDBG module tracing is disabled if the MCU is secure. However, breakpoints can still be
generated if the MCU is secure.

Table 8-3. Mode Dependent Restriction Summary

BDM BDM MCU  Comparator  Breakpoints Tagging Tracing
Enable Active Secure Matches Enabled Possible Possible Possible

x x 1 Yes Yes Yes No
0 0 0 Yes Only SWI Yes Yes
0 1 0                Active BDM not possible when not enabled
1 0 0 Yes Yes Yes Yes
1 1 0 XGATE only XGATE only XGATE only XGATE only

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 307



Chapter 8 S12X Debug (S12XDBGV3) Module

8.1.5 Block Diagram
TAGHITS TAGS

EXTERNAL TAGHI / TAGLO
BREAKPOINT REQUESTS

XGATE S/W BREAKPOINT REQUEST
CPU12X & XGATE

SECURE

MATCH0 TRIGGER
COMPARATOR A

CPU12X BUS TAG &
TRIGGER

COMPARATOR B MATCH1 CONTROL STATE
LOGIC STATE SEQUENCER

XGATE BUS
COMPARATOR C MATCH2 STATE

COMPARATOR D MATCH3

TRACE
CONTROL
TRIGGER

TRACE BUFFER
READ TRACE DATA (DBG READ DATA BUS)

Figure 8-1. Debug Module Block Diagram

8.2 External Signal Description
The S12XDBG sub-module features two external tag input signals. See Device User Guide (DUG) for the
mapping of these signals to device pins. These tag pins may be used for the external tagging in emulation
modes only.

Table 8-4. External System Pins Associated With S12XDBG

Pin Name Pin Functions Description
TAGHI TAGHI When instruction tagging is on, tags the high half of the instruction word being

(See DUG) read into the instruction queue.
TAGLO TAGLO When instruction tagging is on, tags the low half of the instruction word being

(See DUG) read into the instruction queue.
TAGLO Unconditional In emulation modes, a low assertion on this pin in the 7th or 8th cycle after the

(See DUG) Tagging Enable end of reset enables the Unconditional Tagging function.

8.3 Memory Map and Registers

8.3.1 Module Memory Map
A summary of the registers associated with the S12XDBG sub-block is shown in Table 8-2. Detailed
descriptions of the registers and bits are given in the subsections that follow.

MC9S12XE-Family Reference Manual  Rev. 1.25

308 Freescale Semiconductor

BUS INTERFACE

COMPARATOR
MATCH CONTROL



Chapter 8 S12X Debug (S12XDBGV3) Module

Address Name Bit 7 6 5 4 3 2 1 Bit 0
R 0

0x0020 DBGC1 ARM XGSBPE BDM DBGBRK COMRV
W TRIG

R TBF EXTF 0 0 0 SSF2 SSF1 SSF0
0x0021 DBGSR

W

R
0x0022 DBGTCR TSOURCE TRANGE TRCMOD TALIGN

W

R 0 0 0 0
0x0023 DBGC2 CDCM ABCM

W

R Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
0x0024 DBGTBH

W

R Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
0x0025 DBGTBL

W

R 0 CNT
0x0026 DBGCNT

W

R 0 0 0 0
0x0027 DBGSCRX SC3 SC2 SC1 SC0

W
R 0 0 0 0 MC3 MC2 MC1 MC0

0x0027 DBGMFR
W

0x00281 DBGXCTL R 0
NDB TAG BRK RW RWE SRC COMPE

(COMPA/C) W

0x00282 DBGXCTL R
SZE SZ TAG BRK RW RWE SRC COMPE

(COMPB/D) W

R 0
0x0029 DBGXAH Bit 22 21 20 19 18 17 Bit 16

W

R
0x002A DBGXAM Bit 15 14 13 12 11 10 9 Bit 8

W

R
0x002B DBGXAL Bit 7 6 5 4 3 2 1 Bit 0

W

R
0x002C DBGXDH Bit 15 14 13 12 11 10 9 Bit 8

W

R
0x002D DBGXDL Bit 7 6 5 4 3 2 1 Bit 0

W

R
0x002E DBGXDHM Bit 15 14 13 12 11 10 9 Bit 8

W

R
0x002F DBGXDLM Bit 7 6 5 4 3 2 1 Bit 0

W
1 This represents the contents if the Comparator A or C control register is blended into this address.
2 This represents the contents if the Comparator B or D control register is blended into this address

Figure 8-2. Quick Reference to S12XDBG Registers

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 309



Chapter 8 S12X Debug (S12XDBGV3) Module

8.3.2 Register Descriptions
This section consists of the S12XDBG control and trace buffer register descriptions in address order. Each
comparator has a bank of registers that are visible through an 8-byte window between 0x0028 and 0x002F
in the S12XDBG module register address map. When ARM is set in DBGC1, the only bits in the
S12XDBG module registers that can be written are ARM, TRIG, and COMRV[1:0].

******* Debug Control Register 1 (DBGC1)

Address: 0x0020

7 6 5 4 3 2 1 0
R 0

ARM XGSBPE BDM DBGBRK COMRV
W TRIG

Reset 0 0 0 0 0 0 0 0

Figure 8-3. Debug Control Register (DBGC1)

Read: Anytime

Write: Bits 7, 1, 0 anytime
Bit 6 can be written anytime but always reads back as 0.
Bits 5:2 anytime S12XDBG is not armed.

NOTE
If a write access to DBGC1 with the ARM bit position set occurs
simultaneously to a hardware disarm from an internal trigger event, then the
ARM bit is cleared due to the hardware disarm.

NOTE
When disarming the S12XDBG by clearing ARM with software, the
contents of bits[5:2] are not affected by the write, since up until the write
operation, ARM = 1 preventing these bits from being written. These bits
must be cleared using a second write if required.

Table 8-5. DBGC1 Field Descriptions

Field Description

7 Arm Bit — The ARM bit controls whether the S12XDBG module is armed. This bit can be set and cleared by
ARM user software and is automatically cleared on completion of a tracing session, or if a breakpoint is generated with

tracing not enabled. On setting this bit the state sequencer enters State1.
0 Debugger disarmed
1 Debugger armed

6 Immediate Trigger Request Bit — This bit when written to 1 requests an immediate trigger independent of
TRIG comparator or external tag signal status. When tracing is complete a forced breakpoint may be generated

depending upon DBGBRK and BDM bit settings. This bit always reads back a 0. Writing a 0 to this bit has no
effect. If TSOURCE are clear no tracing is carried out. If tracing has already commenced using BEGIN- or MID
trigger alignment, it continues until the end of the tracing session as defined by the TALIGN bit settings, thus
TRIG has no affect. In secure mode tracing is disabled and writing to this bit has no effect.
0 Do not trigger until the state sequencer enters the Final State.
1 Trigger immediately .

MC9S12XE-Family Reference Manual  Rev. 1.25

310 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

Table 8-5. DBGC1 Field Descriptions (continued)

Field Description

5 XGATE S/W Breakpoint Enable — The XGSBPE bit controls whether an XGATE S/W breakpoint request is
XGSBPE passed to the CPU12X. The XGATE S/W breakpoint request is handled by the S12XDBG module, which can

request an CPU12X breakpoint depending on the state of this bit.
0 XGATE S/W breakpoint request is disabled
1 XGATE S/W breakpoint request is enabled

4 Background Debug Mode Enable — This bit determines if an S12X breakpoint causes the system to enter
BDM Background Debug Mode (BDM) or initiate a Software Interrupt (SWI). If this bit is set but the BDM is not enabled

by the ENBDM bit in the BDM module, then breakpoints default to SWI.
0 Breakpoint to Software Interrupt if BDM inactive. Otherwise no breakpoint.
1 Breakpoint to BDM, if BDM enabled. Otherwise breakpoint to SWI

3–2 S12XDBG Breakpoint Enable Bits — The DBGBRK bits control whether the debugger will request a breakpoint
DBGBRK to either CPU12X or XGATE or both upon reaching the state sequencer Final State. If tracing is enabled, the

breakpoint is generated on completion of the tracing session. If tracing is not enabled, the breakpoint is
generated immediately. Please refer to Section 8.4.7 for further details. XGATE software breakpoints are
independent of the DBGBRK bits. XGATE software breakpoints force a breakpoint to the CPU12X independent
of the DBGBRK bit field configuration. See Table 8-6.

1–0 Comparator Register Visibility Bits — These bits determine which bank of comparator register is visible in the
COMRV 8-byte window of the S12XDBG module address map, located between 0x0028 to 0x002F. Furthermore these

bits determine which register is visible at the address 0x0027. See Table 8-7.

Table 8-6. DBGBRK Encoding

DBGBRK Resource Halted by Breakpoint
00 No breakpoint generated
01 XGATE breakpoint generated
10 CPU12X breakpoint generated
11 Breakpoints generated for CPU12X and XGATE

Table 8-7. COMRV Encoding

COMRV Visible Comparator Visible Register at 0x0027
00 Comparator A  DBGSCR1
01 Comparator B  DBGSCR2
10 Comparator C  DBGSCR3
11 Comparator D  DBGMFR

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 311



Chapter 8 S12X Debug (S12XDBGV3) Module

******* Debug Status Register (DBGSR)
Address: 0x0021

7 6 5 4 3 2 1 0
R TBF EXTF 0 0 0 SSF2 SSF1 SSF0
W

Reset — 0 0 0 0 0 0 0
POR 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 8-4. Debug Status Register (DBGSR)

Read: Anytime

Write: Never

Table 8-8. DBGSR Field Descriptions

Field Description

7 Trace Buffer Full — The TBF bit indicates that the trace buffer has stored 64 or more lines of data since it was
TBF last armed. If this bit is set, then all 64 lines will be valid data, regardless of the value of DBGCNT bits CNT[6:0].

The TBF bit is cleared when ARM in DBGC1 is written to a one. The TBF is cleared by the power on reset
initialization. Other system generated resets have no affect on this bit.

6 External Tag Hit Flag — The EXTF bit indicates if a tag hit condition from an external TAGHI/TAGLO tag was
EXTF met since arming. This bit is cleared when ARM in DBGC1 is written to a one.

0 External tag hit has not occurred
1 External tag hit has occurred

2–0 State Sequencer Flag Bits — The SSF bits indicate in which state the State Sequencer is currently in. During
SSF[2:0] a debug session on each transition to a new state these bits are updated. If the debug session is ended by

software clearing the ARM bit, then these bits retain their value to reflect the last state of the state sequencer
before disarming. If a debug session is ended by an internal trigger, then the state sequencer returns to state0
and these bits are cleared to indicate that state0 was entered during the session. On arming the module the state
sequencer enters state1 and these bits are forced to SSF[2:0] = 001. See Table 8-9.

Table 8-9. SSF[2:0] — State Sequence Flag Bit Encoding

SSF[2:0] Current State
000 State0 (disarmed)
001 State1
010 State2
011 State3
100 Final State

101,110,111 Reserved

MC9S12XE-Family Reference Manual  Rev. 1.25

312 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

******* Debug Trace Control Register (DBGTCR)
Address: 0x0022

7 6 5 4 3 2 1 0
R

TSOURCE TRANGE TRCMOD TALIGN
W

Reset 0 0 0 0 0 0 0 0

Figure 8-5. Debug Trace Control Register (DBGTCR)

Read: Anytime

Write: Bits 7:6 only when S12XDBG is neither secure nor armed.
Bits 5:0 anytime the module is disarmed.

Table 8-10. DBGTCR Field Descriptions

Field Description

7–6 Trace Source Control Bits — The TSOURCE bits select the data source for the tracing session. If the MCU
TSOURCE system is secured, these bits cannot be set and tracing is inhibited. See Table 8-11.

5–4 Trace Range Bits — The TRANGE bits allow filtering of trace information from a selected address range when
TRANGE tracing from the CPU12X in Detail Mode. The XGATE tracing range cannot be narrowed using these bits. To use

a comparator for range filtering, the corresponding COMPE and SRC bits must remain cleared. If the COMPE
bit is not clear then the comparator will also be used to generate state sequence triggers. If the corresponding
SRC bit is set the comparator is mapped to the XGATE buses, the TRANGE bits have no effect on the valid
address range, memory accesses within the whole memory map are traced. See Table 8-12.

3–2 Trace Mode Bits — See Section ******* for detailed Trace Mode descriptions. In Normal Mode, change of flow
TRCMOD information is stored. In Loop1 Mode, change of flow information is stored but redundant entries into trace

memory are inhibited. In Detail Mode, address and data for all memory and register accesses is stored. See
Table 8-13.

1–0 Trigger Align Bits — These bits control whether the trigger is aligned to the beginning, end or the middle of a
TALIGN tracing session. See Table 8-14.

Table 8-11. TSOURCE — Trace Source Bit Encoding

TSOURCE Tracing Source
00 No tracing requested
01 CPU12X

10(1) XGATE
111,(2) Both CPU12X and XGATE

1. No range limitations are allowed. Thus tracing operates as if TRANGE = 00.
2. No Detail Mode tracing supported. If TRCMOD = 10, no information is stored.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 313



Chapter 8 S12X Debug (S12XDBGV3) Module

Table 8-12. TRANGE Trace Range Encoding

TRANGE Tracing Range
00 Trace from all addresses (No filter)
01 Trace only in address range from $00000 to Comparator D
10 Trace only in address range from Comparator C to $7FFFFF
11 Trace only in range from Comparator C to Comparator D

Table 8-13. TRCMOD Trace Mode Bit Encoding

TRCMOD Description
00 Normal
01 Loop1
10 Detail
11 Pure PC

Table 8-14. TALIGN Trace Alignment Encoding

TALIGN Description
00 Trigger at end of stored data
01 Trigger before storing data
10 Trace buffer entries before and after trigger
11 Reserved

******* Debug Control Register2 (DBGC2)

Address: 0x0023

7 6 5 4 3 2 1 0
R 0 0 0 0

CDCM ABCM
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 8-6. Debug Control Register2 (DBGC2)

Read: Anytime

Write: Anytime the module is disarmed.

This register configures the comparators for range matching.

Table 8-15. DBGC2 Field Descriptions

Field Description

3–2 C and D Comparator Match Control — These bits determine the C and D comparator match mapping as
CDCM[1:0] described in Table 8-16.

1–0 A and B Comparator Match Control — These bits determine the A and B comparator match mapping as
ABCM[1:0] described in Table 8-17.

MC9S12XE-Family Reference Manual  Rev. 1.25

314 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

Table 8-16. CDCM Encoding

CDCM Description
00 Match2 mapped to comparator C match....... Match3 mapped to comparator D match.
01 Match2 mapped to comparator C/D inside range....... Match3 disabled.
10 Match2 mapped to comparator C/D outside range....... Match3 disabled.
11 Reserved(1)

1. Currently defaults to Match2 mapped to comparator C : Match3 mapped to comparator D

Table 8-17. ABCM Encoding

ABCM Description
00 Match0 mapped to comparator A match....... Match1 mapped to comparator B match.
01 Match 0 mapped to comparator A/B inside range....... Match1 disabled.
10 Match 0 mapped to comparator A/B outside range....... Match1 disabled.
11 Reserved(1)

1. Currently defaults to Match0 mapped to comparator A : Match1 mapped to comparator B

******* Debug Trace Buffer Register (DBGTBH:DBGTBL)

Address: 0x0024, 0x0025

15 14 13 12 11 10 9 8 7 6 5 4 3 2 1 0
R

Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8 Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

POR X X X X X X X X X X X X X X X X
Other

— — — — — — — — — — — — — — — —
Resets

Figure 8-7. Debug Trace Buffer Register (DBGTB)

Read: Only when unlocked AND not secured AND not armed AND with a  TSOURCE bit set.

Write: Aligned word writes when disarmed unlock the trace buffer for reading but do not affect trace buffer
contents.

Table 8-18. DBGTB Field Descriptions

Field Description

15–0 Trace Buffer Data Bits — The Trace Buffer Register is a window through which the 64-bit wide data lines of the
Bit[15:0] Trace Buffer may be read 16 bits at a time. Each valid read of DBGTB increments an internal trace buffer pointer

which points to the next address to be read. When the ARM bit is written to 1 the trace buffer is locked to prevent
reading. The trace buffer can only be unlocked for reading by writing to DBGTB with an aligned word write when
the module is disarmed. The DBGTB register can be read only as an aligned word, any byte reads or misaligned
access of these registers will return 0 and will not cause the trace buffer pointer to increment to the next trace
buffer address. The same is true for word reads while the debugger is armed. The POR state is undefined Other
resets do not affect the trace buffer contents. .

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 315



Chapter 8 S12X Debug (S12XDBGV3) Module

******* Debug Count Register (DBGCNT)

Address: 0x0026

7 6 5 4 3 2 1 0
R 0 CNT
W

Reset 0 — — — — — — —
POR 0 0 0 0 0 0 0 0

= Unimplemented or Reserved

Figure 8-8. Debug Count Register (DBGCNT)

Read: Anytime

Write: Never
Table 8-19. DBGCNT Field Descriptions

Field Description

6–0 Count Value — The CNT bits [6:0] indicate the number of valid data 64-bit data lines stored in the Trace Buffer.
CNT[6:0] Table 8-20 shows the correlation between the CNT bits and the number of valid data lines in the Trace Buffer.

When the CNT rolls over to zero, the TBF bit in DBGSR is set and incrementing of CNT will continue in end-
trigger or mid-trigger mode. The DBGCNT register is cleared when ARM in DBGC1 is written to a one. The
DBGCNT register is cleared by power-on-reset initialization but is not cleared by other system resets. Thus
should a reset occur during a debug session, the DBGCNT register still indicates after the reset, the number of
valid trace buffer entries stored before the reset occurred. The DBGCNT register is not decremented when
reading from the trace buffer.

Table 8-20. CNT Decoding Table

TBF (DBGSR) CNT[6:0] Description
0 0000000 No data valid
0 0000001  32 bits of one line valid(1)

0 0000010 1 line valid
0000100 2 lines valid
0000110 3 lines valid

.. ..
1111100 62 lines valid

0 1111110 63 lines valid
1 0000000 64 lines valid; if using Begin trigger alignment,

ARM bit will be cleared and the tracing session ends.
1 0000010 64 lines valid,

.. oldest data has been overwritten by most recent data

..
1111110

1. This applies to Normal/Loop1/PurePC Modes when tracing from either CPU12X or XGATE only.

MC9S12XE-Family Reference Manual  Rev. 1.25

316 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

******* Debug State Control Registers
There is a dedicated control register for each of the state sequencer states 1 to 3 that determines if
transitions from that state are allowed, depending upon comparator matches or tag hits, and defines the
next state for the state sequencer following a match. The three debug state control registers are located at
the same address in the register address map (0x0027). Each register can be accessed using the COMRV
bits in DBGC1 to blend in the required register. The COMRV = 11 value blends in the match flag register
(DBGMFR).

Table 8-21. State Control Register Access Encoding

COMRV Visible State Control Register
00  DBGSCR1
01  DBGSCR2
10  DBGSCR3
11  DBGMFR

*******.1 Debug State Control Register 1 (DBGSCR1)

Address: 0x0027

7 6 5 4 3 2 1 0
R 0 0 0 0

SC3 SC2 SC1 SC0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 8-9. Debug State Control Register 1 (DBGSCR1)

Read: If COMRV[1:0] = 00

Write: If COMRV[1:0] = 00 and S12XDBG is not armed.

This register is visible at 0x0027 only with COMRV[1:0] = 00. The state control register 1 selects the
targeted next state whilst in State1. The matches refer to the match channels of the comparator match
control logic as depicted in Figure 8-1 and described in Section *******.1. Comparators must be enabled
by setting the comparator enable bit in the associated DBGXCTL control register.

Table 8-22. DBGSCR1 Field Descriptions

Field Description

3–0 These bits select the targeted next state whilst in State1, based upon the match event.
SC[3:0]

Table 8-23. State1 Sequencer Next State Selection

SC[3:0] Description
0000 Any match triggers to state2
0001 Any match triggers to state3
0010 Any match triggers to Final State

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 317



Chapter 8 S12X Debug (S12XDBGV3) Module

Table 8-23. State1 Sequencer Next State Selection (continued)

SC[3:0] Description
0011 Match2 triggers to State2....... Other matches have no effect
0100  Match2 triggers to State3....... Other matches have no effect
0101 Match2 triggers to Final State....... Other matches have no effect
0110 Match0 triggers to State2....... Match1 triggers to State3....... Other matches have no effect
0111 Match1 triggers to State3....... Match0 triggers Final State....... Other matches have no effect
1000 Match0 triggers to State2....... Match2 triggers to State3....... Other matches have no effect
1001 Match2 triggers to State3....... Match0 triggers Final State....... Other matches have no effect
1010 Match1 triggers to State2....... Match3 triggers to State3....... Other matches have no effect
1011 Match3 triggers to State3....... Match1 triggers to Final State....... Other matches have no effect
1100 Match3 has no effect....... All other matches (M0,M1,M2) trigger to State2
1101 Reserved. (No match triggers state sequencer transition)
1110 Reserved. (No match triggers state sequencer transition)
1111 Reserved. (No match triggers state sequencer transition)

The trigger priorities described in Table 8-42 dictate that in the case of simultaneous matches, the match
on the lower channel number (0,1,2,3) has priority. The SC[3:0] encoding ensures that a match leading to
final state has priority over all other matches.

*******.2 Debug State Control Register 2 (DBGSCR2)
Address: 0x0027

7 6 5 4 3 2 1 0
R 0 0 0 0

SC3 SC2 SC1 SC0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 8-10. Debug State Control Register 2 (DBGSCR2)

Read: If COMRV[1:0] = 01

Write: If COMRV[1:0] = 01 and S12XDBG is not armed.

This register is visible at 0x0027 only with COMRV[1:0] = 01. The state control register 2 selects the
targeted next state whilst in State2. The matches refer to the match channels of the comparator match
control logic as depicted in Figure 8-1 and described in Section *******.1. Comparators must be enabled
by setting the comparator enable bit in the associated DBGXCTL control register.

Table 8-24. DBGSCR2 Field Descriptions

Field Description

3–0 These bits select the targeted next state whilst in State2, based upon the match event.
SC[3:0]

Table 8-25. State2 —Sequencer Next State Selection

SC[3:0] Description
0000 Any match triggers to state1

MC9S12XE-Family Reference Manual  Rev. 1.25

318 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

Table 8-25. State2 —Sequencer Next State Selection (continued)

SC[3:0] Description
0001 Any match triggers to state3
0010 Any match triggers to Final State
0011 Match3 triggers to State1....... Other matches have no effect
0100  Match3 triggers to State3....... Other matches have no effect
0101 Match3 triggers to Final State....... Other matches have no effect
0110 Match0 triggers to State1....... Match1 triggers to State3....... Other matches have no effect
0111 Match1 triggers to State3....... Match0 triggers Final State....... Other matches have no effect
1000 Match0 triggers to State1....... Match2 triggers to State3....... Other matches have no effect
1001 Match2 triggers to State3....... Match0 triggers Final State....... Other matches have no effect
1010 Match1 triggers to State1....... Match3 triggers to State3....... Other matches have no effect
1011 Match3 triggers to State3....... Match1 triggers Final State....... Other matches have no effect
1100 Match2 triggers to State1..... Match3 trigger to Final State
1101 Match2 has no affect, all other matches (M0,M1,M3) trigger to Final State
1110 Reserved. (No match triggers state sequencer transition)
1111 Reserved. (No match triggers state sequencer transition)

The trigger priorities described in Table 8-42 dictate that in the case of simultaneous matches, the match
on the lower channel number (0,1,2,3) has priority. The SC[3:0] encoding ensures that a match leading to
final state has priority over all other matches.

*******.3 Debug State Control Register 3 (DBGSCR3)

Address: 0x0027

7 6 5 4 3 2 1 0
R 0 0 0 0

SC3 SC2 SC1 SC0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 8-11. Debug State Control Register 3 (DBGSCR3)

Read: If COMRV[1:0] = 10

Write: If COMRV[1:0] = 10 and S12XDBG is not armed.

This register is visible at 0x0027 only with COMRV[1:0] = 10. The state control register three selects the
targeted next state whilst in State3. The matches refer to the match channels of the comparator match
control logic as depicted in Figure 8-1 and described in Section *******.1. Comparators must be enabled
by setting the comparator enable bit in the associated DBGXCTL control register.

Table 8-26. DBGSCR3 Field Descriptions

Field Description

3–0 These bits select the targeted next state whilst in State3, based upon the match event.
SC[3:0]

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 319



Chapter 8 S12X Debug (S12XDBGV3) Module

Table 8-27. State3 — Sequencer Next State Selection

SC[3:0] Description
0000 Any match triggers to state1
0001 Any match triggers to state2
0010 Any match triggers to Final State
0011 Match0 triggers to State1....... Other matches have no effect
0100  Match0 triggers to State2....... Other matches have no effect
0101 Match0 triggers to Final State.......Match1 triggers to State1...Other matches have no effect
0110 Match1 triggers to State1....... Other matches have no effect
0111  Match1 triggers to State2....... Other matches have no effect
1000 Match1 triggers to Final State....... Other matches have no effect
1001 Match2 triggers to State2....... Match0 triggers to Final State....... Other matches have no effect
1010 Match1 triggers to State1....... Match3 triggers to State2....... Other matches have no effect
1011 Match3 triggers to State2....... Match1 triggers to Final State....... Other matches have no effect
1100 Match2 triggers to Final State....... Other matches have no effect
1101 Match3 triggers to Final State....... Other matches have no effect
1110 Reserved. (No match triggers state sequencer transition)
1111 Reserved. (No match triggers state sequencer transition)

The trigger priorities described in Table 8-42 dictate that in the case of simultaneous matches, the match
on the lower channel number (0,1,2,3) has priority. The SC[3:0] encoding ensures that a match leading to
final state has priority over all other matches.

*******.4 Debug Match Flag Register (DBGMFR)

Address: 0x0027

7 6 5 4 3 2 1 0
R 0 0 0 0 MC3 MC2 MC1 MC0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 8-12. Debug Match Flag Register (DBGMFR)

Read: If COMRV[1:0] = 11

Write: Never

DBGMFR is visible at 0x0027 only with COMRV[1:0] = 11. It features four flag bits each mapped directly
to a channel. Should a match occur on the channel during the debug session, then the corresponding flag
is set and remains set until the next time the module is armed by writing to the ARM bit. Thus the contents
are retained after a debug session for evaluation purposes. These flags cannot be cleared by software, they
are cleared only when arming the module. A set flag does not inhibit the setting of other flags. Once a flag
is set, further triggers on the same channel have no affect.

MC9S12XE-Family Reference Manual  Rev. 1.25

320 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

******* Comparator Register Descriptions
Each comparator has a bank of registers that are visible through an 8-byte window in the S12XDBG
module register address map. Comparators A and C consist of 8 register bytes (3 address bus compare
registers, two data bus compare registers, two data bus mask registers and a control register).

Comparators B and D consist of four register bytes (three address bus compare registers and a control
register).

Each set of comparator registers is accessible in the same 8-byte window of the register address map and
can be accessed using the COMRV bits in the DBGC1 register. If the Comparators B or D are accessed
through the 8-byte window, then only the address and control bytes are visible, the 4 bytes associated with
data bus and data bus masking read as zero and cannot be written. Furthermore the control registers for
comparators B and D differ from those of comparators A and C.

Table 8-28. Comparator Register Layout

 0x0028 CONTROL Read/Write Comparators A,B,C,D
 0x0029 ADDRESS HIGH Read/Write Comparators A,B,C,D
 0x002A ADDRESS MEDIUM Read/Write Comparators A,B,C,D
 0x002B ADDRESS LOW Read/Write Comparators A,B,C,D
 0x002C DATA HIGH COMPARATOR Read/Write Comparator A and C only
 0x002D DATA LOW COMPARATOR Read/Write Comparator A and C only
 0x002E DATA HIGH MASK Read/Write Comparator A and C only
 0x002F DATA LOW MASK Read/Write Comparator A and C only

*******.1 Debug Comparator Control Register (DBGXCTL)
The contents of this register bits 7 and 6 differ depending upon which comparator registers are visible in
the 8-byte window of the DBG module register address map.

Address: 0x0028

7 6 5 4 3 2 1 0
R 0

NDB TAG BRK RW RWE SRC COMPE
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 8-13. Debug Comparator Control Register (Comparators A and C)

Address: 0x0028

7 6 5 4 3 2 1 0
R

SZE SZ TAG BRK RW RWE SRC COMPE
W

Reset 0 0 0 0 0 0 0 0

Figure 8-14. Debug Comparator Control Register (Comparators B and D)

Read: Anytime. See Table 8-29 for visible register encoding.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 321



Chapter 8 S12X Debug (S12XDBGV3) Module

Write: If DBG not armed. See Table 8-29 for visible register encoding.

The DBGC1_COMRV bits determine which comparator control, address, data and datamask registers are
visible in the 8-byte window from 0x0028 to 0x002F as shown in Section Table 8-29.

Table 8-29. Comparator Address Register Visibility

COMRV Visible Comparator
00 DBGACTL, DBGAAH ,DBGAAM, DBGAAL, DBGADH, DBGADL, DBGADHM, DBGADLM
01 DBGBCTL, DBGBAH, DBGBAM, DBGBAL
10 DBGCCTL, DBGCAH, DBGCAM, DBGCAL, DBGCDH, DBGCDL, DBGCDHM, DBGCDLM
11 DBGDCTL, DBGDAH, DBGDAM, DBGDAL

Table 8-30. DBGXCTL Field Descriptions

Field Description

7 Size Comparator Enable Bit — The SZE bit controls whether access size comparison is enabled for the
SZE associated comparator. This bit is ignored if the TAG bit in the same register is set.

(Comparators 0 Word/Byte access size is not used in comparison
B and D) 1 Word/Byte access size is used in comparison

6 Not Data Bus — The NDB bit controls whether the match occurs when the data bus matches the comparator
NDB register value or when the data bus differs from the register value. Furthermore data bus bits can be

(Comparators individually masked using the comparator data mask registers. This bit is only available for comparators A
A and C and C. This bit is ignored if the TAG bit in the same register is set. This bit position has an SZ functionality for

comparators B and D.
0 Match on data bus equivalence to comparator register contents
1 Match on data bus difference to comparator register contents

6 Size Comparator Value Bit — The SZ bit selects either word or byte access size in comparison for the
SZ associated comparator. This bit is ignored if the SZE bit is cleared or if the TAG bit in the same register is set.

(Comparators This bit position has NDB functionality for comparators A and C
B and D) 0 Word access size will be compared

1 Byte access size will be compared

5 Tag Select — This bit controls whether the comparator match will cause a trigger or tag the opcode at the
TAG matched address. Tagged opcodes trigger only if they reach the execution stage of the instruction queue.

0 Trigger immediately on match
1 On match, tag the opcode. If the opcode is about to be executed a trigger is generated

4 Break — This bit controls whether a channel match terminates a debug session immediately, independent
BRK of state sequencer state. To generate an immediate breakpoint the module breakpoints must be enabled

using DBGBRK.
0 The debug session termination is dependent upon the state sequencer and trigger conditions.
1 A match on this channel terminates the debug session immediately; breakpoints if active are generated,

tracing, if active, is terminated and the module disarmed.

3 Read/Write Comparator Value Bit — The RW bit controls whether read or write is used in compare for the
RW associated comparator . The RW bit is not used if RWE = 0.

0 Write cycle will be matched
1 Read cycle will be matched

2 Read/Write Enable Bit — The RWE bit controls whether read or write comparison is enabled for the
RWE associated comparator. This bit is not used for tagged operations.

0 Read/Write is not used in comparison
1 Read/Write is used in comparison

MC9S12XE-Family Reference Manual  Rev. 1.25

322 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

Table 8-30. DBGXCTL Field Descriptions (continued)

Field Description

1 Determines mapping of comparator to CPU12X or XGATE
SRC 0 The comparator is mapped to CPU12X buses

1 The comparator is mapped to XGATE address and data buses

0 Determines if comparator is enabled
COMPE 0 The comparator is not enabled

1 The comparator is enabled for state sequence triggers or tag generation

Table 8-31 shows the effect for RWE and RW on the comparison conditions. These bits are not useful for
tagged operations since the trigger occurs based on the tagged opcode reaching the execution stage of the
instruction queue. Thus these bits are ignored if tagged triggering is selected.

Table 8-31. Read or Write Comparison Logic Table

RWE Bit RW Bit RW Signal Comment
0 x 0 RW not used in comparison
0 x 1 RW not used in comparison
1 0 0 Write
1 0 1 No match
1 1 0 No match
1 1 1 Read

*******.2 Debug Comparator Address High Register (DBGXAH)

Address: 0x0029

7 6 5 4 3 2 1 0
R 0

Bit 22 Bit 21 Bit 20 Bit 19 Bit 18 Bit 17 Bit 16
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 8-15. Debug Comparator Address High Register (DBGXAH)

Read: Anytime. See Table 8-29 for visible register encoding.

Write: If DBG not armed. See Table 8-29 for visible register encoding.

Table 8-32. DBGXAH Field Descriptions

Field Description

6–0 Comparator Address High Compare Bits — The Comparator address high compare bits control whether the
Bit[22:16] selected comparator will compare the address bus bits [22:16] to a logic one or logic zero. This register byte is

ignored for XGATE compares.
0 Compare corresponding address bit to a logic zero
1 Compare corresponding address bit to a logic one

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 323



Chapter 8 S12X Debug (S12XDBGV3) Module

*******.3 Debug Comparator Address Mid Register (DBGXAM)

Address: 0x002A

7 6 5 4 3 2 1 0
R

Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W

Reset 0 0 0 0 0 0 0 0

Figure 8-16. Debug Comparator Address Mid Register (DBGXAM)

Read: Anytime. See Table 8-29 for visible register encoding.

Write: If DBG not armed. See Table 8-29 for visible register encoding.

Table 8-33. DBGXAM Field Descriptions

Field Description

7–0 Comparator Address Mid Compare Bits— The Comparator address mid compare bits control whether the
Bit[15:8] selected comparator will compare the address bus bits [15:8] to a logic one or logic zero.

0 Compare corresponding address bit to a logic zero
1 Compare corresponding address bit to a logic one

*******.4 Debug Comparator Address Low Register (DBGXAL)

Address: 0x002B

7 6 5 4 3 2 1 0
R

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

Reset 0 0 0 0 0 0 0 0

Figure 8-17. Debug Comparator Address Low Register (DBGXAL)

Read: Anytime. See Table 8-29 for visible register encoding.

Write: If DBG not armed. See Table 8-29 for visible register encoding.

Table 8-34. DBGXAL Field Descriptions

Field Description

7–0 Comparator Address Low Compare Bits — The Comparator address low compare bits control whether the
Bits[7:0] selected comparator will compare the address bus bits [7:0] to a logic one or logic zero.

0 Compare corresponding address bit to a logic zero
1 Compare corresponding address bit to a logic one

MC9S12XE-Family Reference Manual  Rev. 1.25

324 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

*******.5 Debug Comparator Data High Register (DBGXDH)

Address: 0x002C

7 6 5 4 3 2 1 0
R

Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W

Reset 0 0 0 0 0 0 0 0

Figure 8-18. Debug Comparator Data High Register (DBGXDH)

Read: Anytime. See Table 8-29 for visible register encoding.

Write: If DBG not armed. See Table 8-29 for visible register encoding.

Table 8-35. DBGXAH Field Descriptions

Field Description

7–0 Comparator Data High Compare Bits — The Comparator data high compare bits control whether the selected
Bits[15:8] comparator compares the data bus bits [15:8] to a logic one or logic zero. The comparator data compare bits are

only used in comparison if the corresponding data mask bit is logic 1. This register is available only for
comparators A and C.
0 Compare corresponding data bit to a logic zero
1 Compare corresponding data bit to a logic one

*******.6 Debug Comparator Data Low Register (DBGXDL)

Address: 0x002D

7 6 5 4 3 2 1 0
R

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

Reset 0 0 0 0 0 0 0 0

Figure 8-19. Debug Comparator Data Low Register (DBGXDL)

Read: Anytime. See Table 8-29 for visible register encoding.

Write: If DBG not armed. See Table 8-29 for visible register encoding.

Table 8-36. DBGXDL Field Descriptions

Field Description

7–0 Comparator Data Low Compare Bits — The Comparator data low compare bits control whether the selected
Bits[7:0] comparator compares the data bus bits [7:0] to a logic one or logic zero. The comparator data compare bits are

only used in comparison if the corresponding data mask bit is logic 1. This register is available only for
comparators A and C.
0 Compare corresponding data bit to a logic zero
1 Compare corresponding data bit to a logic one

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 325



Chapter 8 S12X Debug (S12XDBGV3) Module

*******.7 Debug Comparator Data High Mask Register (DBGXDHM)

Address: 0x002E

7 6 5 4 3 2 1 0
R

Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W

Reset 0 0 0 0 0 0 0 0

Figure 8-20. Debug Comparator Data High Mask Register (DBGXDHM)

Read: Anytime. See Table 8-29 for visible register encoding.

Write: If DBG not armed. See Table 8-29 for visible register encoding.

Table 8-37. DBGXDHM Field Descriptions

Field Description

7–0 Comparator Data High Mask Bits — The Comparator data high mask bits control whether the selected
Bits[15:8] comparator compares the data bus bits [15:8] to the corresponding comparator data compare bits. This register

is available only for comparators A and C.
0 Do not compare corresponding data bit
1 Compare corresponding data bit

*******.8 Debug Comparator Data Low Mask Register (DBGXDLM)

Address: 0x002F

7 6 5 4 3 2 1 0
R

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

Reset 0 0 0 0 0 0 0 0

Figure 8-21. Debug Comparator Data Low Mask Register (DBGXDLM)

Read: Anytime. See Table 8-29 for visible register encoding.

Write: If DBG not armed. See Table 8-29 for visible register encoding.

Table 8-38. DBGXDLM Field Descriptions

Field Description

7–0 Comparator Data Low Mask Bits — The Comparator data low mask bits control whether the selected
Bits[7:0] comparator compares the data bus bits [7:0] to the corresponding comparator data compare bits. This register

is available only for comparators A and C.
0 Do not compare corresponding data bit
1 Compare corresponding data bit

8.4 Functional Description
This section provides a complete functional description of the S12XDBG module. If the part is in secure
mode, the S12XDBG module can generate breakpoints but tracing is not possible.

MC9S12XE-Family Reference Manual  Rev. 1.25

326 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

8.4.1 S12XDBG Operation
Arming the S12XDBG module by setting ARM in DBGC1 allows triggering, and storing of data in the
trace buffer and can be used to cause breakpoints to the CPU12X or the XGATE module. The DBG module
is made up of four main blocks, the comparators, control logic, the state sequencer, and the trace buffer.

The comparators monitor the bus activity of the CPU12X and XGATE. Comparators can be configured to
monitor address and databus. Comparators can also be configured to mask out individual data bus bits
during a compare and to use R/W and word/byte access qualification in the comparison. When a match
with a comparator register value occurs the associated control logic can trigger the state sequencer to
another state (see Figure 8-22). Either forced or tagged triggers are possible. Using a forced trigger, the
trigger is generated immediately on a comparator match. Using a tagged trigger, at a comparator match,
the instruction opcode is tagged and only if the instruction reaches the execution stage of the instruction
queue is a trigger generated. In the case of a transition to Final State, bus tracing is triggered and/or a
breakpoint can be generated. Tracing of both CPU12X and/or XGATE bus activity is possible.

Independent of the state sequencer, a breakpoint can be triggered by the external TAGHI / TAGLO signals
or by an XGATE S/W breakpoint request or by writing to the TRIG bit in the DBGC1 control register.

The trace buffer is visible through a 2-byte window in the register address map and can be read out using
standard 16-bit word reads.

8.4.2 Comparator Modes
The S12XDBG contains four comparators, A, B, C, and D. Each comparator can be configured to monitor
CPU12X or XGATE buses. Each comparator compares the selected address bus with the address stored in
DBGXAH, DBGXAM, and DBGXAL. Furthermore, comparators A and C also compare the data buses
to the data stored in DBGXDH, DBGXDL and allow masking of individual data bus bits.

S12X comparator matches are disabled in BDM and during BDM accesses.

The comparator match control logic configures comparators to monitor the buses for an exact address or
an address range. The comparator configuration is controlled by the control register contents and the range
control by the DBGC2 contents.

On a match a trigger can initiate a transition to another state sequencer state (see Section 8.4.3”). The
comparator control register also allows the type of access to be included in the comparison through the use
of the RWE, RW, SZE, and SZ bits. The RWE bit controls whether read or write comparison is enabled
for the associated comparator and the RW bit selects either a read or write access for a valid match.
Similarly the SZE and SZ bits allows the size of access (word or byte) to be considered in the compare.
Only comparators B and D feature SZE and SZ.

The TAG bit in each comparator control register is used to determine the triggering condition. By setting
TAG, the comparator will qualify a match with the output of opcode tracking logic and a trigger occurs
before the tagged instruction executes (tagged-type trigger). Whilst tagging, the RW, RWE, SZE, and SZ
bits are ignored and the comparator register must be loaded with the exact opcode address.

If the TAG bit is clear (forced type trigger) a comparator match is generated when the selected address
appears on the system address bus. If the selected address is an opcode address, the match is generated

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 327



Chapter 8 S12X Debug (S12XDBGV3) Module

when the opcode is fetched from the memory. This precedes the instruction execution by an indefinite
number of cycles due to instruction pipe lining. For a comparator match of an opcode at an odd address
when TAG = 0, the corresponding even address must be contained in the comparator register. Thus for an
opcode at odd address (n), the comparator register must contain address (n–1).

Once a successful comparator match has occurred, the condition that caused the original match is not
verified again on subsequent matches. Thus if a particular data value is verified at a given address, this
address may not still contain that data value when a subsequent match occurs.

Comparators C and D can also be used to select an address range to trace from. This is determined by the
TRANGE bits in the DBGTCR register. The TRANGE encoding is shown in Table 8-12. If the TRANGE
bits select a range definition using comparator D, then comparator D is configured for trace range
definition and cannot be used for address bus comparisons. Similarly if the TRANGE bits select a range
definition using comparator C, then comparator C is configured for trace range definition and cannot be
used for address bus comparisons.

Match[0, 1, 2, 3] map directly to Comparators[A, B, C, D] respectively, except in range modes (see
Section *******). Comparator priority rules are described in the trigger priority section (Section *******).

******* Exact Address Comparator Match (Comparators A and C)
With range comparisons disabled, the match condition is an exact equivalence of address/data bus with the
value stored in the comparator address/data registers. Further qualification of the type of access (R/W,
word/byte) is possible.

Comparators A and C do not feature SZE or SZ control bits, thus the access size is not compared. Table 8-
40 lists access considerations without data bus compare. Table 8-39 lists access considerations with data
bus comparison. To compare byte accesses DBGxDH must be loaded with the data byte, the low byte must
be masked out using the DBGxDLM mask register. On word accesses the data byte of the lower address
is mapped to DBGxDH.

Table 8-39. Comparator A and C Data Bus Considerations

Access Address DBGxDH DBGxDL DBGxDHM DBGxDLM Example Valid Match
Word ADDR[n] Data[n] Data[n+1] $FF $FF MOVW #$WORD ADDR[n] config1
Byte ADDR[n] Data[n] x $FF $00 MOVB #$BYTE ADDR[n] config2
Word ADDR[n] Data[n] x $FF $00 MOVW #$WORD ADDR[n] config2
Word ADDR[n] x Data[n+1] $00 $FF MOVW #$WORD ADDR[n] config3

Code may contain various access forms of the same address, i.e. a word access of ADDR[n] or byte access
of ADDR[n+1] both access n+1. At a word access of ADDR[n], address ADDR[n+1] does not appear on
the address bus and so cannot cause a comparator match if the comparator contains ADDR[n]. Thus it is
not possible to monitor all data accesses of ADDR[n+1] with one comparator.

To detect an access of ADDR[n+1] through a word access of ADDR[n] the comparator can be configured
to ADDR[n], DBGxDL is loaded with the data pattern and DBGxDHM is cleared so only the data[n+1] is
compared on accesses of ADDR[n].

MC9S12XE-Family Reference Manual  Rev. 1.25

328 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

NOTE
Using this configuration, a byte access of ADDR[n] can cause a comparator match if the databus low byte
by chance contains the same value as ADDR[n+1] because the databus comparator does not feature access
size comparison and uses the mask as a “don’t care” function. Thus masked bits do not prevent a match.

Comparators A and C feature an NDB control bit to determine if a match occurs when the data bus differs
to comparator register contents or when the data bus is equivalent to the comparator register contents.

******* Exact Address Comparator Match (Comparators B and D)
Comparators B and D feature SZ and SZE control bits. If SZE is clear, then the comparator address match
qualification functions the same as for comparators A and C.

If the SZE bit is set the access size (word or byte) is compared with the SZ bit value such that only the
specified type of access causes a match. Thus if configured for a byte access of a particular address, a word
access covering the same address does not lead to match.

Table 8-40. Comparator Access Size Considerations

Comparator Address SZE SZ8 Condition For Valid Match
Comparators ADDR[n] — — Word and byte accesses of ADDR[n](1)

A and C MOVB #$BYTE ADDR[n]
MOVW #$WORD ADDR[n]

Comparators ADDR[n] 0 X Word and byte accesses of ADDR[n]1
B and D MOVB #$BYTE ADDR[n]

MOVW #$WORD ADDR[n]
Comparators ADDR[n] 1 0 Word accesses of ADDR[n]1

B and D MOVW #$WORD ADDR[n]
Comparators ADDR[n] 1 1 Byte accesses of ADDR[n]

B and D MOVB #$BYTE ADDR[n]
1. A word access of ADDR[n-1] also accesses ADDR[n] but does not generate a match.

The comparator address register must contain the exact address used in the code.

******* Data Bus Comparison NDB Dependency
Comparators A and C each feature an NDB control bit, which allows data bus comparators to be configured
to either trigger on equivalence or trigger on difference. This allows monitoring of a difference in the
contents of an address location from an expected value.

When matching on an equivalence (NDB=0), each individual data bus bit position can be masked out by
clearing the corresponding mask bit (DBGxDHM/DBGxDLM), so that it is ignored in the comparison. A
match occurs when all data bus bits with corresponding mask bits set are equivalent. If all mask register
bits are clear, then a match is based on the address bus only, the data bus is ignored.

When matching on a difference, mask bits can be cleared to ignore bit positions. A match occurs when any
data bus bit with corresponding mask bit set is different. Clearing all mask bits, causes all bits to be ignored
and prevents a match because no difference can be detected. In this case address bus equivalence does not
cause a match.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 329



Chapter 8 S12X Debug (S12XDBGV3) Module

Table 8-41. NDB and MASK bit dependency

DBGxDHM[n] /
NDB Comment

DBGxDLM[n]
0 0 Do not compare data bus bit.
0 1 Compare data bus bit. Match on equivalence.
1 0 Do not compare data bus bit.
1 1 Compare data bus bit. Match on difference.

******* Range Comparisons
When using the AB comparator pair for a range comparison, the data bus can also be used for qualification
by using the comparator A data and data mask registers. Furthermore the DBGACTL RW and RWE bits
can be used to qualify the range comparison on either a read or a write access. The corresponding
DBGBCTL bits are ignored. Similarly when using the CD comparator pair for a range comparison, the
data bus can also be used for qualification by using the comparator C data and data mask registers.
Furthermore the DBGCCTL RW and RWE bits can be used to qualify the range comparison on either a
read or a write access if tagging is not selected. The corresponding DBGDCTL bits are ignored. The SZE
and SZ control bits are ignored in range mode. The comparator A and C TAG bits are used to tag range
comparisons for the AB and CD ranges respectively. The comparator B and D TAG bits are ignored in
range modes. In order for a range comparison using comparators A and B, both COMPEA and COMPEB
must be set; to disable range comparisons both must be cleared. Similarly for a range CD comparison, both
COMPEC and COMPED must be set. If a range mode is selected SRCA and SRCC select the source
(S12X or XGATE), SRCB and SRCD are ignored. The comparator A and C BRK bits are used for the AB
and CD ranges respectively, the comparator B and D BRK bits are ignored in range mode. When
configured for range comparisons and tagging, the ranges are accurate only to word boundaries.

*******.1 Inside Range (CompAC_Addr ≤ address ≤ CompBD_Addr)
In the Inside Range comparator mode, either comparator pair A and B or comparator pair C and D can be
configured for range comparisons by the control register (DBGC2). The match condition requires that a
valid match for both comparators happens on the same bus cycle. A match condition on only one
comparator is not valid. An aligned word access which straddles the range boundary will cause a trigger
only if the aligned address is inside the range.

*******.2 Outside Range (address < CompAC_Addr or address > CompBD_Addr)
In the Outside Range comparator mode, either comparator pair A and B or comparator pair C and D can
be configured for range comparisons. A single match condition on either of the comparators is recognized
as valid. An aligned word access which straddles the range boundary will cause a trigger only if the aligned
address is outside the range.

Outside range mode in combination with tagged triggers can be used to detect if the opcode fetches are
from an unexpected range. In forced trigger modes the outside range trigger would typically be activated
at any interrupt vector fetch or register access. This can be avoided by setting the upper or lower range limit
to $7FFFFF or $000000 respectively. Interrupt vector fetches do not cause taghits

MC9S12XE-Family Reference Manual  Rev. 1.25

330 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

When comparing the XGATE address bus in outside range mode, the initial vector fetch as determined by
the vector contained in the XGATE XGVBR register should be taken into consideration. The XGVBR
register and hence vector address can be modified.

8.4.3 Trigger Modes
Trigger modes are used as qualifiers for a state sequencer change of state. The control logic determines the
trigger mode and provides a trigger to the state sequencer. The individual trigger modes are described in
the following sections.

******* Forced Trigger On Comparator Match
If a forced trigger comparator match occurs, the trigger immediately initiates a transition to the next state
sequencer state whereby the corresponding flags in DBGSR are set. The state control register for the
current state determines the next state for each trigger. Forced triggers are generated as soon as the
matching address appears on the address bus, which in the case of opcode fetches occurs several cycles
before the opcode execution. For this reason a forced trigger at an opcode address precedes a tagged trigger
at the same address by several cycles.

******* Trigger On Comparator Related Taghit
If a CPU12X or XGATE taghit occurs, a transition to another state sequencer state is initiated and the
corresponding DBGSR flags are set. For a comparator related taghit to occur, the S12XDBG must first
generate tags based on comparator matches. When the tagged instruction reaches the execution stage of
the instruction queue a taghit is generated by the CPU12X/XGATE. The state control register for the
current state determines the next state for each trigger.

******* External Tagging Trigger
The TAGLO and TAGHI pins (mapped to device pins) can be used to tag an instruction. This function can
be used as another breakpoint source. When the tagged opcode reaches the execution stage of the
instruction queue a transition to the disarmed state0 occurs, ending the debug session and generating a
breakpoint, if breakpoints are enabled. External tagging is only possible in device emulation modes.

******* Trigger On XGATE S/W Breakpoint Request
The XGATE S/W breakpoint request issues a forced breakpoint request to the CPU12X immediately and
triggers the state sequencer into the disarmed state. Active tracing sessions are terminated immediately,
thus if tracing has not yet begun, no trace information is stored. XGATE generated breakpoints are
independent of the DBGBRK bits. The XGSBPE bit in DBGC1 determines if the XGATE S/W breakpoint
function is enabled. The BDM bit in DBGC1 determines if the XGATE requested breakpoint causes the
system to enter BDM Mode or initiate a software interrupt (SWI).

8.4.3.5 TRIG Immediate Trigger
Independent of comparator matches or external tag signals it is possible to initiate a tracing session and/or
breakpoint by writing the TRIG bit in DBGC1 to a logic “1”. If configured for begin or mid aligned tracing,

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 331



Chapter 8 S12X Debug (S12XDBGV3) Module

this triggers the state sequencer into the Final State, if configured for end alignment, setting the TRIG bit
disarms the module, ending the session. If breakpoints are enabled, a forced breakpoint request is issued
immediately (end alignment) or when tracing has completed (begin or mid alignment).

******* Trigger Priorities
In case of simultaneous triggers, the priority is resolved according to Table 8-42. The lower priority trigger
is suppressed. It is thus possible to miss a lower priority trigger if it occurs simultaneously with a trigger
of a higher priority. The trigger priorities described in Table 8-42 dictate that in the case of simultaneous
matches, the match on the lower channel number (0,1,2,3) has priority. The SC[3:0] encoding ensures that
a match leading to final state has priority over all other matches in each state sequencer state. When
configured for range modes a simultaneous match of comparators A and C generates an active match0
whilst match2 is suppressed.

If a write access to DBGC1 with the ARM bit position set occurs simultaneously to a hardware disarm
from an internal trigger event, then the ARM bit is cleared due to the hardware disarm.

Table 8-42. Trigger Priorities

Priority Source Action
Highest XGATE BKP Immediate forced breakpoint......(Tracing terminated immediately).

TRIG Trigger immediately to final state (begin or mid aligned tracing enabled)
Trigger immediately to state 0 (end aligned or no tracing enabled)

External TAGHI/TAGLO Enter State0
Match0 (force or tag hit) Trigger to next state as defined by state control registers
Match1 (force or tag hit) Trigger to next state as defined by state control registers
Match2 (force or tag hit) Trigger to next state as defined by state control registers

Lowest Match3 (force or tag hit) Trigger to next state as defined by state control registers

8.4.4 State Sequence Control

ARM = 0

   State 0 ARM = 1
(Disarmed) State1 State2

ARM = 0

Session Complete
(Disarm)

Final State State3
ARM = 0

Figure 8-22. State Sequencer Diagram

The state sequencer allows a defined sequence of events to provide a trigger point for tracing of data in the
trace buffer. Once the S12XDBG module has been armed by setting the ARM bit in the DBGC1 register,

MC9S12XE-Family Reference Manual  Rev. 1.25

332 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

then state1 of the state sequencer is entered. Further transitions between the states are then controlled by
the state control registers and depend upon a selected trigger mode condition being met. From Final State
the only permitted transition is back to the disarmed state0. Transition between any of the states 1 to 3 is
not restricted. Each transition updates the SSF[2:0] flags in DBGSR accordingly to indicate the current
state.

Alternatively by setting the TRIG bit in DBGSC1, the state machine can be triggered to state0 or Final
State depending on tracing alignment.

A tag hit through TAGHI/TAGLO brings the state sequencer immediately into state0, causes a breakpoint,
if breakpoints are enabled, and ends tracing immediately independent of the trigger alignment bits
TALIGN[1:0].

Independent of the state sequencer, each comparator channel can be individually configured to generate an
immediate breakpoint when a match occurs through the use of the BRK bits in the DBGxCTL registers.
Thus it is possible to generate an immediate breakpoint on selected channels, whilst a state sequencer
transition can be initiated by a match on other channels. If a debug session is ended by a trigger on a
channel with BRK = 1, the state sequencer transitions through Final State for a clock cycle to state0. This
is independent of tracing and breakpoint activity, thus with tracing and breakpoints disabled, the state
sequencer enters state0 and the debug module is disarmed.

An XGATE S/W breakpoint request, if enabled causes a transition to the State0 and generates a breakpoint
request to the CPU12X immediately

8.4.4.1 Final State
On entering Final State a trigger may be issued to the trace buffer according to the trace position control
as defined by the TALIGN field (see Section *******). If TSOURCE in the trace control register DBGTCR
are cleared then the trace buffer is disabled and the transition to Final State can only generate a breakpoint
request. In this case or upon completion of a tracing session when tracing is enabled, the ARM bit in the
DBGC1 register is cleared, returning the module to the disarmed state0. If tracing is enabled, a breakpoint
request can occur at the end of the tracing session. If neither tracing nor breakpoints are enabled then when
the final state is reached it returns automatically to state0 and the debug module is disarmed.

8.4.5 Trace Buffer Operation
The trace buffer is a 64 lines deep by 64-bits wide RAM array. The S12XDBG module stores trace
information in the RAM array in a circular buffer format. The RAM array can be accessed through a
register window (DBGTBH:DBGTBL) using 16-bit wide word accesses. After each complete 64-bit trace
buffer line is read, an internal pointer into the RAM is incremented so that the next read will receive fresh
information. Data is stored in the format shown in Table 8-43. After each store the counter register bits
DBGCNT[6:0] are incremented. Tracing of CPU12X activity is disabled when the BDM is active but
tracing of XGATE activity is still possible. Reading the trace buffer whilst the DBG is armed returns
invalid data and the trace buffer pointer is not incremented.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 333



Chapter 8 S12X Debug (S12XDBGV3) Module

******* Trace Trigger Alignment
Using the TALIGN bits (see Section *******) it is possible to align the trigger with the end, the middle, or
the beginning of a tracing session.

If End or Mid tracing is selected, tracing begins when the ARM bit in DBGC1 is set and State1 is entered.
The transition to Final State if End is selected signals the end of the tracing session. The transition to Final
State if Mid is selected signals that another 32 lines will be traced before ending the tracing session.
Tracing with Begin-Trigger starts at the opcode of the trigger.

*******.1 Storing with Begin-Trigger
Storing with Begin-Trigger, data is not stored in the Trace Buffer until the Final State is entered. Once the
trigger condition is met the S12XDBG module will remain armed until 64 lines are stored in the Trace
Buffer. If the trigger is at the address of the change-of-flow instruction the change of flow associated with
the trigger will be stored in the Trace Buffer. Using Begin-trigger together with tagging, if the tagged
instruction is about to be executed then the trace is started. Upon completion of the tracing session the
breakpoint is generated, thus the breakpoint does not occur at the tagged instruction boundary.

*******.2 Storing with Mid-Trigger
Storing with Mid-Trigger, data is stored in the Trace Buffer as soon as the S12XDBG module is armed.
When the trigger condition is met, another 32 lines will be traced before ending the tracing session,
irrespective of the number of lines stored before the trigger occurred, then the S12XDBG module is
disarmed and no more data is stored. Using Mid-trigger with tagging, if the tagged instruction is about to
be executed then the trace is continued for another 32 lines. Upon tracing completion the breakpoint is
generated, thus the breakpoint does not occur at the tagged instruction boundary.

*******.3 Storing with End-Trigger
Storing with End-Trigger, data is stored in the Trace Buffer until the Final State is entered, at which point
the S12XDBG module will become disarmed and no more data will be stored. If the trigger is at the
address of a change of flow instruction the trigger event will not be stored in the Trace Buffer.

******* Trace Modes
The S12XDBG module can operate in four trace modes. The mode is selected using the TRCMOD bits in
the DBGTCR register. In each mode tracing of XGATE or CPU12X information is possible. The source
for the trace is selected using the TSOURCE bits in the DBGTCR register. The modes are described in the
following subsections. The trace buffer organization is shown in Table 8-43.

*******.1 Normal Mode
In Normal Mode, change of flow (COF) program counter (PC) addresses will be stored.

COF addresses are defined as follows for the CPU12X:
• Source address of taken conditional branches (long, short, bit-conditional, and loop primitives)
• Destination address of indexed JMP, JSR, and CALL instruction

MC9S12XE-Family Reference Manual  Rev. 1.25

334 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

• Destination address of RTI, RTS, and RTC instructions.
• Vector address of interrupts, except for SWI and BDM vectors

LBRA, BRA, BSR, BGND as well as non-indexed JMP, JSR, and CALL instructions are not classified as
change of flow and are not stored in the trace buffer.

COF addresses are defined as follows for the XGATE:
• Source address of taken conditional branches
• Destination address of indexed JAL instructions.
• First XGATE code address in a thread

Change-of-flow addresses stored include the full 23-bit address bus of CPU12X, the 16-bit address bus for
the XGATE module and an information byte, which contains a source/destination bit to indicate whether
the stored address was a source address or destination address.

NOTE
When an CPU12X COF instruction with destination address is executed, the
destination address is stored to the trace buffer on instruction completion,
indicating the COF has taken place. If an interrupt occurs simultaneously
then the next instruction carried out is actually from the interrupt service
routine. The instruction at the destination address of the original program
flow gets exectuted after the interrupt service routine.

In the following example an IRQ interrupt occurs during execution of the
indexed JMP at address MARK1. The BRN at the destination (SUB_1) is
not executed until after the IRQ service routine but the destination address
is entered into the trace buffer to indicate that the indexed JMP COF has
taken place.

LDX #SUB_1
MARK1 JMP 0,X ; IRQ interrupt occurs during execution of this
MARK2 NOP ;

SUB_1 BRN * ; JMP Destination address TRACE BUFFER ENTRY 1
; RTI Destination address TRACE BUFFER ENTRY 3

NOP ;
ADDR1 DBNE A,PART5 ; Source address TRACE BUFFER ENTRY 4

IRQ_ISR LDAB #$F0 ; IRQ Vector $FFF2 = TRACE BUFFER ENTRY 2
STAB VAR_C1
RTI ;

The execution flow taking into account the IRQ is as follows
LDX #SUB_1

MARK1 JMP 0,X ;
IRQ_ISR LDAB #$F0 ;

STAB VAR_C1
RTI ;

SUB_1 BRN *
NOP ;

ADDR1 DBNE A,PART5 ;

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 335



Chapter 8 S12X Debug (S12XDBGV3) Module

*******.2 Loop1 Mode
Loop1 Mode, similarly to Normal Mode also stores only COF address information to the trace buffer, it
however allows the filtering out of redundant information.

The intent of Loop1 Mode is to prevent the Trace Buffer from being filled entirely with duplicate
information from a looping construct such as delays using the DBNE instruction or polling loops using
BRSET/BRCLR instructions. Immediately after address information is placed in the Trace Buffer, the
S12XDBG module writes this value into a background register. This prevents consecutive duplicate
address entries in the Trace Buffer resulting from repeated branches.

Loop1 Mode only inhibits consecutive duplicate source address entries that would typically be stored in
most tight looping constructs. It does not inhibit repeated entries of destination addresses or vector
addresses, since repeated entries of these would most likely indicate a bug in the user’s code that the
S12XDBG module is designed to help find.

*******.3 Detail Mode
In Detail Mode, address and data for all memory and register accesses is stored in the trace buffer. In the
case of XGATE tracing this means that initialization of the R1 register during a vector fetch is not traced.
This mode also features information byte entries to the trace buffer, for each address byte entry. The
information byte indicates the size of access (word or byte) and the type of access (read or write).

When tracing CPU12X activity in Detail Mode, all cycles are traced except those when the CPU12X is
either in a free or opcode fetch cycle. In this mode the XGATE program counter is also traced to provide
a snapshot of the XGATE activity. CXINF information byte bits indicate the type of XGATE activity
occurring at the time of the trace buffer entry. When tracing CPU12X activity alone in Detail Mode, the
address range can be limited to a range specified by the TRANGE bits in DBGTCR. This function uses
comparators C and D to define an address range inside which CPU12X activity should be traced (see
Table 8-43). Thus the traced CPU12X activity can be restricted to particular register range accesses.

When tracing XGATE activity in Detail Mode, all load and store cycles are traced. Additionally the
CPU12X program counter is stored at the time of the XGATE trace buffer entry to provide a snapshot of
CPU12X activity.

*******.4 Pure PC Mode
In Pure PC Mode, tracing from the CPU the PC addresses of all executed opcodes, including illegal
opcodes, are stored. In Pure PC Mode, tracing from the XGATE the PC addresses of all executed opcodes
are stored.

******* Trace Buffer Organization
Referring to Table 8-43. An X prefix denotes information from the XGATE module, a C prefix denotes
information from the CPU12X. ADRH, ADRM, ADRL denote address high, middle and low byte
respectively. INF bytes contain control information (R/W, S/D etc.). The numerical suffix indicates which
tracing step. The information format for Loop1 Mode and PurePC Mode is the same as that of Normal
Mode. Whilst tracing from XGATE or CPU12X only, in Normal or Loop1 modes each array line contains

MC9S12XE-Family Reference Manual  Rev. 1.25

336 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

2 data entries, thus in this case the DBGCNT[0] is incremented after each separate entry. In Detail mode
DBGCNT[0] remains cleared whilst the other DBGCNT bits are incremented on each trace buffer entry.

XGATE and CPU12X COFs occur independently of each other and the profile of COFs for the two sources
is totally different. When both sources are being traced in Normal or Loop1 mode, for each COF from one
source, there may be many COFs from the other source, depending on user code. COF events could occur
far from each other in the time domain, on consecutive cycles or simultaneously. When a COF occurs in
either source (S12X or XGATE) a trace buffer entry is made and the corresponding CDV or XDV bit is
set. The current PC of the other source is simultaneously stored to the trace buffer even if no COF has
occurred, in which case CDV/XDV remains cleared indicating the address is not associated with a COF,
but is simply a snapshot of the PC contents at the time of the COF from the other source.

Single byte data accesses in Detail Mode are always stored to the low byte of the trace buffer (CDATAL
or XDATAL) and the high byte is cleared. When tracing word accesses, the byte at the lower address is
always stored to trace buffer byte3 and the byte at the higher address is stored to byte2.

Table 8-43. Trace Buffer Organization

8-Byte Wide Word Buffer
Mode

7 6 5 4 3 2 1 0

XGATE CXINF1 CADRH1 CADRM1 CADRL1 XDATAH1 XDATAL1 XADRM1 XADRL1
Detail CXINF2 CADRH2 CADRM2 CADRL2 XDATAH2 XDATAL2 XADRM2 XADRL2

CPU12X CXINF1 CADRH1 CADRM1 CADRL1 CDATAH1 CDATAL1 XADRM1 XADRL1
Detail CXINF2 CADRH2 CADRM2 CADRL2 CDATAH2 CDATAL2 XADRM2 XADRL2

Both XINF0 XPCM0 XPCL0 CINF0 CPCH0 CPCM0 CPCL0
Other Modes XINF1 XPCM1 XPCL1 CINF1 CPCH1 CPCM1 CPCL1

XGATE XINF1 XPCM1 XPCL1 XINF0 XPCM0 XPCL0
Other Modes XINF3 XPCM3 XPCL3 XINF2 XPCM2 XPCL2

CPU12X CINF1 CPCH1 CPCM1 CPCL1 CINF0 CPCH0 CPCM0 CPCL0
Other Modes CINF3 CPCH3 CPCM3 CPCL3 CINF2 CPCH2 CPCM2 CPCL2

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 337



Chapter 8 S12X Debug (S12XDBGV3) Module

*******.1  Information Byte Organization
The format of the control information byte is dependent upon the active trace mode as described below. In
Normal, Loop1, or Pure PC modes tracing of XGATE activity, XINF is used to store control information.
In Normal, Loop1, or Pure PC modes tracing of CPU12X activity, CINF is used to store control
information. In Detail Mode, CXINF contains the control information.

XGATE Information Byte

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
XSD XSOT XCOT XDV 0 0 0 0

Figure 8-23. XGATE Information Byte XINF

Table 8-44. XINF Field Descriptions

Field Description

7 Source Destination Indicator — This bit indicates if the corresponding stored address is a source or destination
XSD address. This is only used in Normal and Loop1 mode tracing.

0 Source address
1 Destination address or Start of Thread or Continuation of Thread

6 Start Of Thread Indicator — This bit indicates that the corresponding stored address is a start of thread
XSOT address. This is only used in Normal and Loop1 mode tracing.

NOTE. This bit only has effect on devices where the XGATE module supports multiple interrupt levels.
0 Stored address not from a start of thread
1 Stored address from a start of thread

5 Continuation Of Thread Indicator — This bit indicates that the corresponding stored address is the first
XCOT address following a return from a higher priority thread. This is only used in Normal and Loop1 mode tracing.

NOTE. This bit only has effect on devices where the XGATE module supports multiple interrupt levels.
0 Stored address not from a continuation of thread
1 Stored address from a continuation of thread

4 Data Invalid Indicator — This bit indicates if the trace buffer entry is invalid. It is only used when tracing from
XDV both sources in Normal, Loop1 and Pure PC modes, to indicate that the XGATE trace buffer entry is valid.

0 Trace buffer entry is invalid
1 Trace buffer entry is valid

XGATE info bit setting

XGATE FLOW SOT1 SOT2 JAL RTS COT1 RTS

XSD
XSOT
XCOT

Figure 8-24. XGATE info bit setting

Figure 8-24 indicates the XGATE information bit setting when switching between threads, the initial
thread starting at SOT1 and continuing at COT1 after the higher priority thread2 has ended.

MC9S12XE-Family Reference Manual  Rev. 1.25

338 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

CPU12X Information Byte

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
CSD CVA 0 CDV 0 0 0 0

Figure 8-25. CPU12X Information Byte CINF

Table 8-45. CINF Field Descriptions

Field Description

7 Source Destination Indicator — This bit indicates if the corresponding stored address is a source or destination
CSD address. This is only used in Normal and Loop1 mode tracing.

0 Source address
1 Destination address

6 Vector Indicator — This bit indicates if the corresponding stored address is a vector address. Vector addresses
CVA are destination addresses, thus if CVA is set, then the corresponding CSD is also set. This is only used in Normal

and Loop1 mode tracing. This bit has no meaning in Pure PC mode.
0 Indexed jump destination address
1 Vector destination address

4 Data Invalid Indicator — This bit indicates if the trace buffer entry is invalid. It is only used when tracing from
CDV both sources in Normal, Loop1 and Pure PC modes, to indicate that the CPU12X trace buffer entry is valid.

0 Trace buffer entry is invalid
1 Trace buffer entry is valid

CXINF Information Byte

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
CFREE CSZ CRW COCF XACK XSZ XRW XOCF

Figure 8-26. Information Byte CXINF

This describes the format of the information byte used only when tracing in Detail Mode. When tracing
from the CPU12X in Detail Mode, information is stored to the trace buffer on all cycles except opcode
fetch and free cycles. The XGATE entry stored on the same line is a snapshot of the XGATE program
counter. In this case the CSZ and CRW bits indicate the type of access being made by the CPU12X, whilst
the XACK and XOCF bits indicate if the simultaneous XGATE cycle is a free cycle (no bus acknowledge)
or opcode fetch cycle. Similarly when tracing from the XGATE in Detail Mode, information is stored to
the trace buffer on all cycles except opcode fetch and free cycles. The CPU12X entry stored on the same
line is a snapshot of the CPU12X program counter. In this case the XSZ and XRW bits indicate the type
of access being made by the XGATE, whilst the CFREE and COCF bits indicate if the simultaneous
CPU12X cycle is a free cycle or opcode fetch cycle.

Table 8-46. CXINF Field Descriptions

Field Description

7 CPU12X Free Cycle Indicator — This bit indicates if the stored CPU12X address corresponds to a free cycle.
CFREE This bit only contains valid information when tracing the XGATE accesses in Detail Mode.

0 Stored information corresponds to free cycle
1 Stored information does not correspond to free cycle

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 339



Chapter 8 S12X Debug (S12XDBGV3) Module

Table 8-46. CXINF Field Descriptions (continued)

Field Description

6 Access Type Indicator — This bit indicates if the access was a byte or word size access.This bit only contains
CSZ valid information when tracing CPU12X activity in Detail Mode.

0 Word Access
1 Byte Access

5 Read Write Indicator — This bit indicates if the corresponding stored address corresponds to a read or write
CRW access. This bit only contains valid information when tracing CPU12X activity in Detail Mode.

0 Write Access
1 Read Access

4 CPU12X Opcode Fetch Indicator — This bit indicates if the stored address corresponds to an opcode fetch
COCF cycle. This bit only contains valid information when tracing the XGATE accesses in Detail Mode.

0 Stored information does not correspond to opcode fetch cycle
1 Stored information corresponds to opcode fetch cycle

3 XGATE Access Indicator — This bit indicates if the stored XGATE address corresponds to a free cycle. This bit
XACK only contains valid information when tracing the CPU12X accesses in Detail Mode.

0 Stored information corresponds to free cycle
1 Stored information does not correspond to free cycle

2 Access Type Indicator — This bit indicates if the access was a byte or word size access. This bit only contains
XSZ valid information when tracing XGATE activity in Detail Mode.

0 Word Access
1 Byte Access

1 Read Write Indicator — This bit indicates if the corresponding stored address corresponds to a read or write
XRW access. This bit only contains valid information when tracing XGATE activity in Detail Mode.

0 Write Access
1 Read Access

0 XGATE Opcode Fetch Indicator — This bit indicates if the stored address corresponds to an opcode fetch
XOCF cycle.This bit only contains valid information when tracing the CPU12X accesses in Detail Mode.

0 Stored information does not correspond to opcode fetch cycle
1 Stored information corresponds to opcode fetch cycle

******* Reading Data from Trace Buffer
The data stored in the Trace Buffer can be read using either the background debug module (BDM) module,
the XGATE or the CPU12X provided the S12XDBG module is not armed, is configured for tracing and
the system not secured. When the ARM bit is written to 1 the trace buffer is locked to prevent reading. The
trace buffer can only be unlocked for reading by an aligned word write to DBGTB when the module is
disarmed.

The Trace Buffer can only be read through the DBGTB register using aligned word reads, any byte or
misaligned reads return 0 and do not cause the trace buffer pointer to increment to the next trace buffer
address. The Trace Buffer data is read out first-in first-out. By reading CNT in DBGCNT the number of
valid 64-bit lines can be determined. DBGCNT will not decrement as data is read.

Whilst reading an internal pointer is used to determine the next line to be read. After a tracing session, the
pointer points to the oldest data entry, thus if no overflow has occurred, the pointer points to line0,
otherwise it points to the line with the oldest entry. The pointer is initialized by each aligned write to
DBGTBH to point to the oldest data again. This enables an interrupted trace buffer read sequence to be
easily restarted from the oldest data entry.

MC9S12XE-Family Reference Manual  Rev. 1.25

340 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

The least significant word of each 64-bit wide array line is read out first. This corresponds to the bytes 1
and 0 of Table 8-43. The bytes containing invalid information (shaded in Table 8-43) are also read out.

Reading the Trace Buffer while the S12XDBG module is armed will return invalid data and no shifting of
the RAM pointer will occur.

******* Trace Buffer Reset State
The Trace Buffer contents are not initialized by a system reset. Thus should a system reset occur, the trace
session information from immediately before the reset occurred can be read out. The DBGCNT bits are
not cleared by a system reset. Thus should a reset occur, the number of valid lines in the trace buffer is
indicated by DBGCNT. The internal pointer to the current trace buffer address is initialized by unlocking
the trace buffer thus points to the oldest valid data even if a reset occurred during the tracing session.
Generally debugging occurrences of system resets is best handled using mid or end trigger alignment since
the reset may occur before the trace trigger, which in the begin trigger alignment case means no
information would be stored in the trace buffer.

NOTE
An external pin RESET that occurs simultaneous to a trace buffer entry can,
in very seldom cases, lead to either that entry being corrupted or the first
entry of the session being corrupted. In such cases the other contents of the
trace buffer still contain valid tracing information. The case occurs when the
reset assertion coincides with the trace buffer entry clock edge.

8.4.6  Tagging
A tag follows program information as it advances through the instruction queue. When a tagged instruction
reaches the head of the queue a tag hit occurs and triggers the state sequencer.

Each comparator control register features a TAG bit, which controls whether the comparator match will
cause a trigger immediately or tag the opcode at the matched address. If a comparator is enabled for tagged
comparisons, the address stored in the comparator match address registers must be an opcode address for
the trigger to occur.

Both CPU12X and XGATE opcodes can be tagged with the comparator register TAG bits.

Using Begin trigger together with tagging, if the tagged instruction is about to be executed then the
transition to the next state sequencer state occurs. If the transition is to the Final State, tracing is started.
Only upon completion of the tracing session can a breakpoint be generated. Similarly using Mid trigger
with tagging, if the tagged instruction is about to be executed then the trace is continued for another 32
lines. Upon tracing completion the breakpoint is generated. Using End trigger, when the tagged instruction
is about to be executed and the next transition is to Final State then a breakpoint is generated immediately,
before the tagged instruction is carried out.

Read/Write (R/W), access size (SZ) monitoring and data bus monitoring is not useful if tagged triggering
is selected, since the tag is attached to the opcode at the matched address and is not dependent on the data
bus nor on the type of access. Thus these bits are ignored if tagged triggering is selected.

When configured for range comparisons and tagging, the ranges are accurate only to word boundaries.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 341



Chapter 8 S12X Debug (S12XDBGV3) Module

S12X tagging is disabled when the BDM becomes active. XGATE tagging is possible when the BDM is
active.

******* External Tagging using TAGHI and TAGLO
External tagging using the external TAGHI and TAGLO pins can only be used to tag CPU12X opcodes;
tagging of XGATE code using these pins is not possible. An external tag triggers the state sequencer into
state0 when the tagged opcode reaches the execution stage of the instruction queue.

The pins operate independently, thus the state of one pin does not affect the function of the other. External
tagging is possible in emulation modes only. The presence of logic level 0 on either pin at the rising edge
of the external clock (ECLK) performs the function indicated in the Table 8-47. It is possible to tag both
bytes of an instruction word. If a taghit occurs, a breakpoint can be generated as defined by the DBGBRK
and BDM bits in DBGC1. Each time TAGHI or TAGLO are low on the rising edge of ECLK, the old tag
is replaced by a new one.

Table 8-47. Tag Pin Function

TAGHI TAGLO Tag
1 1 No tag
1 0 Low byte
0 1 High byte
0 0 Both bytes

8.4.6.2 Unconditional Tagging Function
In emulation modes a low assertion of PE5/TAGLO/MODA in the 7th or 8th bus cycle after reset enables
the unconditional tagging function, allowing immediate tagging via TAGHI/TAGLO with breakpoint to
BDM independent of the ARM, BDM and DBGBRK bits. Conversely these bits are not affected by
unconditional tagging. The unconditional tagging function remains enabled until the next reset. This
function allows an immediate entry to BDM in emulation modes before user code execution. The TAGLO
assertion must be in the 7th or 8th bus cycle following the end of reset, whereby the prior RESET pin
assertion lasts the full 192 bus cycles.

8.4.7 Breakpoints
Breakpoints can be generated as follows.

• Through XGATE software breakpoint requests.
• From comparator channel triggers to final state.
• Using software to write to the TRIG bit in the DBGC1 register.
• From taghits generated using the external TAGHI and TAGLO pins.

Breakpoints generated by the XGATE module or via the BDM BACKGROUND command have no affect
on the CPU12X in STOP or WAIT mode.

MC9S12XE-Family Reference Manual  Rev. 1.25

342 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

8.4.7.1 XGATE Software Breakpoints
The XGATE software breakpoint instruction BRK can request a CPU12X breakpoint, via the S12XDBG
module. In this case, if the XGSBPE bit is set, the S12XDBG module immediately generates a forced
breakpoint request to the CPU12X, the state sequencer is returned to state0 and tracing, if active, is
terminated. If configured for BEGIN trigger and tracing has not yet been triggered from another source,
the trace buffer contains no information. Breakpoint requests from the XGATE module do not depend
upon the state of the DBGBRK or ARM bits in DBGC1. They depend solely on the state of the XGSBPE
and BDM bits. Thus it is not necessary to ARM the DBG module to use XGATE software breakpoints to
generate breakpoints in the CPU12X program flow, but it is necessary to set XGSBPE. Furthermore, if a
breakpoint to BDM is required, the BDM bit must also be set. When the XGATE requests an CPU12X
breakpoint, the XGATE program flow stops by default, independent of the S12XDBG module.

8.4.7.2 Breakpoints From Internal Comparator Channel Final State Triggers
Breakpoints can be generated when internal comparator channels trigger the state sequencer to the Final
State. If configured for tagging, then the breakpoint is generated when the tagged opcode reaches the
execution stage of the instruction queue.

If a tracing session is selected by TSOURCE, breakpoints are requested when the tracing session has
completed, thus if Begin or Mid aligned triggering is selected, the breakpoint is requested only on
completion of the subsequent trace (see Table 8-48). If no tracing session is selected, breakpoints are
requested immediately.

If the BRK bit is set on the triggering channel, then the breakpoint is generated immediately independent
of tracing trigger alignment.

Table 8-48. Breakpoint Setup For Both XGATE and CPU12X Breakpoints

BRK TALIGN DBGBRK[n] Breakpoint Alignment
0 00 0 Fill Trace Buffer until trigger

(no breakpoints — keep running)
0 00 1 Fill Trace Buffer until trigger, then breakpoint request occurs
0 01 0 Start Trace Buffer at trigger

(no breakpoints — keep running)
0 01 1 Start Trace Buffer at trigger

A breakpoint request occurs when Trace Buffer is full
0 10 0 Store a further 32 Trace Buffer line entries after trigger

(no breakpoints — keep running)
0 10 1 Store a further 32 Trace Buffer line entries after trigger

Request breakpoint after the 32 further Trace Buffer entries
1 00,01,10 1 Terminate tracing and generate breakpoint immediately on trigger
1 00,01,10 0 Terminate tracing immediately on trigger
x 11 x Reserved

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 343



Chapter 8 S12X Debug (S12XDBGV3) Module

8.4.7.3 Breakpoints Generated Via The TRIG Bit
If a TRIG triggers occur, the Final State is entered. If a tracing session is selected by TSOURCE,
breakpoints are requested when the tracing session has completed, thus if Begin or Mid aligned triggering
is selected, the breakpoint is requested only on completion of the subsequent trace (see Table 8-48). If no
tracing session is selected, breakpoints are requested immediately. TRIG breakpoints are possible even if
the S12XDBG module is disarmed.

8.4.7.4 Breakpoints Via TAGHI Or TAGLO Pin Taghits
Tagging using the external TAGHI/TAGLO pins always ends the session immediately at the tag hit. It is
always end aligned, independent of internal channel trigger alignment configuration.

8.4.7.5 S12XDBG Breakpoint Priorities
XGATE software breakpoints have the highest priority. Active tracing sessions are terminated
immediately.

If a TRIG trigger occurs after Begin or Mid aligned tracing has already been triggered by a comparator
instigated transition to Final State, then TRIG no longer has an effect. When the associated tracing session
is complete, the breakpoint occurs. Similarly if a TRIG is followed by a subsequent trigger from a
comparator channel, it has no effect, since tracing has already started.

If a comparator tag hit occurs simultaneously with an external TAGHI/TAGLO hit, the state sequencer
enters state0. TAGHI/TAGLO triggers are always end aligned, to end tracing immediately, independent of
the tracing trigger alignment bits TALIGN[1:0].

8.4.7.5.1 S12XDBG Breakpoint Priorities And BDM Interfacing
Breakpoint operation is dependent on the state of the S12XBDM module. If the S12XBDM module is
active, the CPU12X is executing out of BDM firmware and S12X breakpoints are disabled. In addition,
while executing a BDM TRACE command, tagging into BDM is disabled. If BDM is not active, the
breakpoint will give priority to BDM requests over SWI requests if the breakpoint coincides with a SWI
instruction in the user’s code. On returning from BDM, the SWI from user code gets executed.

Table 8-49. Breakpoint Mapping Summary

DBGBRK[1] BDM Bit BDM BDM S12X Breakpoint
(DBGC1[3]) (DBGC1[4]) Enabled Active Mapping

0 X X X No Breakpoint
1 0 X 0 Breakpoint to SWI
1 0 X 1 No Breakpoint
1 1 0 X Breakpoint to SWI
1 1 1 0 Breakpoint to BDM
1 1 1 1 No Breakpoint

BDM cannot be entered from a breakpoint unless the ENABLE bit is set in the BDM. If entry to BDM via
a BGND instruction is attempted and the ENABLE bit in the BDM is cleared, the CPU12X actually

MC9S12XE-Family Reference Manual  Rev. 1.25

344 Freescale Semiconductor



Chapter 8 S12X Debug (S12XDBGV3) Module

executes the BDM firmware code. It checks the ENABLE and returns if ENABLE is not set. If not serviced
by the monitor then the breakpoint is re-asserted when the BDM returns to normal CPU12X flow.

If the comparator register contents coincide with the SWI/BDM vector address then an SWI in user code
and DBG breakpoint could occur simultaneously. The CPU12X ensures that BDM requests have a higher
priority than SWI requests. Returning from the BDM/SWI service routine care must be taken to avoid re
triggering a breakpoint.

NOTE
When program control returns from a tagged breakpoint using an RTI or
BDM GO command without program counter modification it will return to
the instruction whose tag generated the breakpoint. To avoid re triggering a
breakpoint at the same location reconfigure the S12XDBG module in the
SWI routine, if configured for an SWI breakpoint, or over the BDM
interface by executing a TRACE command before the GO to increment the
program flow past the tagged instruction.

An XGATE software breakpoint is forced immediately, the tracing session
terminated and the XGATE module execution stops. The user can thus
determine if an XGATE breakpoint has occurred by reading out the XGATE
program counter over the BDM interface.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 345



Chapter 8 S12X Debug (S12XDBGV3) Module

MC9S12XE-Family Reference Manual  Rev. 1.25

346 Freescale Semiconductor