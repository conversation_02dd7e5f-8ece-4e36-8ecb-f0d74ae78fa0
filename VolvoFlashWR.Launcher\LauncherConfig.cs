using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace VolvoFlashWR.Launcher
{
    /// <summary>
    /// Configuration for the launcher
    /// </summary>
    public class LauncherConfig
    {
        /// <summary>
        /// Gets or sets the default launch mode
        /// </summary>
        public LaunchMode DefaultLaunchMode { get; set; } = LaunchMode.Dummy;

        /// <summary>
        /// Gets or sets whether to show the launcher UI
        /// </summary>
        public bool ShowLauncherUI { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to remember the last selected mode
        /// </summary>
        public bool RememberLastMode { get; set; } = true;

        /// <summary>
        /// Gets or sets the last selected mode
        /// </summary>
        public LaunchMode LastSelectedMode { get; set; } = LaunchMode.Dummy;

        /// <summary>
        /// Gets or sets the custom configuration file path
        /// </summary>
        public string CustomConfigPath { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets additional environment variables to set
        /// </summary>
        public Dictionary<string, string> AdditionalEnvironmentVariables { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// Gets or sets the application configuration file path
        /// </summary>
        public string AppConfigPath { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the logging configuration
        /// </summary>
        public LoggingConfig Logging { get; set; } = new LoggingConfig();

        /// <summary>
        /// Gets or sets the backup configuration
        /// </summary>
        public BackupConfig Backup { get; set; } = new BackupConfig();

        /// <summary>
        /// Gets or sets the UI configuration
        /// </summary>
        public UIConfig UI { get; set; } = new UIConfig();

        /// <summary>
        /// Gets or sets the communication configuration
        /// </summary>
        public CommunicationConfig Communication { get; set; } = new CommunicationConfig();

        /// <summary>
        /// Gets or sets whether to use a graphical launcher UI instead of console
        /// </summary>
        public bool UseGraphicalUI { get; set; } = false;

        /// <summary>
        /// Gets or sets whether to show advanced options in the launcher UI
        /// </summary>
        public bool ShowAdvancedOptions { get; set; } = false;

        /// <summary>
        /// Gets or sets whether to automatically check for updates on startup
        /// </summary>
        public bool CheckForUpdates { get; set; } = true;

        /// <summary>
        /// Gets or sets the update check URL
        /// </summary>
        public string UpdateCheckUrl { get; set; } = string.Empty;

        /// <summary>
        /// Loads the configuration from a file
        /// </summary>
        /// <param name="filePath">The file path</param>
        /// <returns>The loaded configuration, or a default configuration if loading fails</returns>
        public static async Task<LauncherConfig> LoadAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    // Create default configuration
                    var defaultConfig = new LauncherConfig();
                    await SaveAsync(defaultConfig, filePath);
                    return defaultConfig;
                }

                string json = await File.ReadAllTextAsync(filePath);
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    AllowTrailingCommas = true,
                    ReadCommentHandling = JsonCommentHandling.Skip
                };

                var config = JsonSerializer.Deserialize<LauncherConfig>(json, options);
                return config ?? new LauncherConfig();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading launcher configuration: {ex.Message}");
                return new LauncherConfig();
            }
        }

        /// <summary>
        /// Saves the configuration to a file
        /// </summary>
        /// <param name="config">The configuration to save</param>
        /// <param name="filePath">The file path</param>
        /// <returns>True if saving is successful, false otherwise</returns>
        public static async Task<bool> SaveAsync(LauncherConfig config, string filePath)
        {
            try
            {
                string directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                };

                string json = JsonSerializer.Serialize(config, options);
                await File.WriteAllTextAsync(filePath, json);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving launcher configuration: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// Launch modes for the application
    /// </summary>
    public enum LaunchMode
    {
        /// <summary>
        /// Normal mode - requires hardware
        /// </summary>
        Normal = 1,

        /// <summary>
        /// Dummy mode - no hardware required
        /// </summary>
        Dummy = 2,

        /// <summary>
        /// Debug mode - verbose logging
        /// </summary>
        Debug = 3,

        /// <summary>
        /// Advanced mode - custom configuration
        /// </summary>
        Advanced = 4,

        /// <summary>
        /// Safe mode - minimal configuration with error recovery
        /// </summary>
        Safe = 5,

        /// <summary>
        /// Demo mode - pre-configured for demonstrations
        /// </summary>
        Demo = 6
    }

    /// <summary>
    /// Logging configuration
    /// </summary>
    public class LoggingConfig
    {
        /// <summary>
        /// Gets or sets whether to enable verbose logging
        /// </summary>
        public bool VerboseLogging { get; set; } = false;

        /// <summary>
        /// Gets or sets the log file path
        /// </summary>
        public string LogFilePath { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the maximum log file size in megabytes
        /// </summary>
        public int MaxLogFileSizeMB { get; set; } = 10;

        /// <summary>
        /// Gets or sets the maximum number of log files to keep
        /// </summary>
        public int MaxLogFileCount { get; set; } = 10;

        /// <summary>
        /// Gets or sets whether to log to console
        /// </summary>
        public bool LogToConsole { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to log to file
        /// </summary>
        public bool LogToFile { get; set; } = true;

        /// <summary>
        /// Gets or sets the minimum log level
        /// </summary>
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public LogLevel MinimumLogLevel { get; set; } = LogLevel.Information;
    }

    /// <summary>
    /// Log levels
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// Trace level
        /// </summary>
        Trace = 0,

        /// <summary>
        /// Debug level
        /// </summary>
        Debug = 1,

        /// <summary>
        /// Information level
        /// </summary>
        Information = 2,

        /// <summary>
        /// Warning level
        /// </summary>
        Warning = 3,

        /// <summary>
        /// Error level
        /// </summary>
        Error = 4,

        /// <summary>
        /// Critical level
        /// </summary>
        Critical = 5
    }

    /// <summary>
    /// Backup configuration
    /// </summary>
    public class BackupConfig
    {
        /// <summary>
        /// Gets or sets the backup directory path
        /// </summary>
        public string BackupDirectoryPath { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets whether to use compression for backups
        /// </summary>
        public bool UseCompression { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to use encryption for backups
        /// </summary>
        public bool UseEncryption { get; set; } = false;

        /// <summary>
        /// Gets or sets the maximum number of backups to keep
        /// </summary>
        public int MaxBackupsToKeep { get; set; } = 10;

        /// <summary>
        /// Gets or sets whether to create automatic backups
        /// </summary>
        public bool AutomaticBackups { get; set; } = true;

        /// <summary>
        /// Gets or sets the automatic backup interval in hours
        /// </summary>
        public int AutomaticBackupIntervalHours { get; set; } = 24;

        /// <summary>
        /// Gets or sets the predefined backup categories
        /// </summary>
        public List<string> PredefinedCategories { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the predefined backup tags
        /// </summary>
        public List<string> PredefinedTags { get; set; } = new List<string>();
    }

    /// <summary>
    /// UI configuration
    /// </summary>
    public class UIConfig
    {
        /// <summary>
        /// Gets or sets the UI theme
        /// </summary>
        public string Theme { get; set; } = "Light";

        /// <summary>
        /// Gets or sets the UI language
        /// </summary>
        public string Language { get; set; } = "en-US";

        /// <summary>
        /// Gets or sets whether to show tooltips
        /// </summary>
        public bool ShowTooltips { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to show the splash screen
        /// </summary>
        public bool ShowSplashScreen { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to confirm before exiting
        /// </summary>
        public bool ConfirmExit { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to show advanced UI options
        /// </summary>
        public bool ShowAdvancedOptions { get; set; } = false;

        /// <summary>
        /// Gets or sets the font size
        /// </summary>
        public int FontSize { get; set; } = 12;
    }

    /// <summary>
    /// Communication configuration
    /// </summary>
    public class CommunicationConfig
    {
        /// <summary>
        /// Gets or sets whether to auto-connect to Vocom devices
        /// </summary>
        public bool AutoConnectVocom { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to use WiFi fallback
        /// </summary>
        public bool UseWiFiFallback { get; set; } = false;

        /// <summary>
        /// Gets or sets the connection timeout in milliseconds
        /// </summary>
        public int ConnectionTimeoutMs { get; set; } = 5000;

        /// <summary>
        /// Gets or sets the number of retry attempts
        /// </summary>
        public int RetryAttempts { get; set; } = 3;

        /// <summary>
        /// Gets or sets the retry delay in milliseconds
        /// </summary>
        public int RetryDelayMs { get; set; } = 1000;

        /// <summary>
        /// Gets or sets whether to auto-scan for ECUs
        /// </summary>
        public bool AutoScanECUs { get; set; } = true;

        /// <summary>
        /// Gets or sets the default protocol type
        /// </summary>
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public ProtocolType DefaultProtocolType { get; set; } = ProtocolType.CAN;
    }

    /// <summary>
    /// Protocol types
    /// </summary>
    public enum ProtocolType
    {
        /// <summary>
        /// CAN protocol
        /// </summary>
        CAN = 1,

        /// <summary>
        /// J1939 protocol
        /// </summary>
        J1939 = 2,

        /// <summary>
        /// KWP2000 protocol
        /// </summary>
        KWP2000 = 3,

        /// <summary>
        /// ISO9141 protocol
        /// </summary>
        ISO9141 = 4
    }
}
