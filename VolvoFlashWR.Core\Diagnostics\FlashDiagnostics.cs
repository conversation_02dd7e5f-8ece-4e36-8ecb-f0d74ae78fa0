using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Utilities;
using VolvoFlashWR.Core.Enums;

namespace VolvoFlashWR.Core.Diagnostics
{
    /// <summary>
    /// Provides detailed diagnostics for flash operations
    /// </summary>
    public class FlashDiagnostics
    {
        private readonly ILoggingService? _logger;
        private readonly FlashOperationMonitor _operationMonitor;
        private readonly List<FlashDiagnosticData> _diagnosticData = new List<FlashDiagnosticData>();
        private readonly string _diagnosticsFilePath;

        /// <summary>
        /// Initializes a new instance of the FlashDiagnostics class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="operationMonitor">The flash operation monitor</param>
        public FlashDiagnostics(ILoggingService? logger = null, FlashOperationMonitor? operationMonitor = null)
        {
            _logger = logger;
            _operationMonitor = operationMonitor ?? new FlashOperationMonitor(logger);

            // Set up the diagnostics file path
            string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            string diagnosticsDir = Path.Combine(appDataPath, "VolvoFlashWR", "Diagnostics");

            // Create the directory if it doesn't exist
            if (!Directory.Exists(diagnosticsDir))
            {
                Directory.CreateDirectory(diagnosticsDir);
            }

            _diagnosticsFilePath = Path.Combine(diagnosticsDir, "flash_diagnostics.json");

            // Load existing diagnostic data if available
            LoadDiagnosticData();

            // Subscribe to flash operation events
            if (_operationMonitor != null)
            {
                _operationMonitor.OperationStarted += OnOperationStarted;
                _operationMonitor.OperationCompleted += OnOperationCompleted;
                _operationMonitor.OperationFailed += OnOperationFailed;
                _operationMonitor.OperationUpdated += OnOperationProgress;
            }
        }

        /// <summary>
        /// Gets the diagnostic data
        /// </summary>
        public IReadOnlyList<FlashDiagnosticData> DiagnosticData => _diagnosticData.AsReadOnly();

        /// <summary>
        /// Gets the flash operation monitor
        /// </summary>
        public FlashOperationMonitor OperationMonitor => _operationMonitor;

        /// <summary>
        /// Loads diagnostic data from the diagnostics file
        /// </summary>
        private void LoadDiagnosticData()
        {
            try
            {
                if (File.Exists(_diagnosticsFilePath))
                {
                    string json = File.ReadAllText(_diagnosticsFilePath);
                    var data = JsonSerializer.Deserialize<List<FlashDiagnosticData>>(json);
                    if (data != null)
                    {
                        _diagnosticData.Clear();
                        _diagnosticData.AddRange(data);
                        _logger?.LogInformation($"Loaded {_diagnosticData.Count} diagnostic records from {_diagnosticsFilePath}", "FlashDiagnostics");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error loading diagnostic data: {ex.Message}", "FlashDiagnostics");
            }
        }

        /// <summary>
        /// Saves diagnostic data to the diagnostics file
        /// </summary>
        private void SaveDiagnosticData()
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                };

                string json = JsonSerializer.Serialize(_diagnosticData, options);
                File.WriteAllText(_diagnosticsFilePath, json);
                _logger?.LogInformation($"Saved {_diagnosticData.Count} diagnostic records to {_diagnosticsFilePath}", "FlashDiagnostics");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error saving diagnostic data: {ex.Message}", "FlashDiagnostics");
            }
        }

        /// <summary>
        /// Handles the OperationStarted event
        /// </summary>
        private void OnOperationStarted(object? sender, FlashOperationEventArgs e)
        {
            try
            {
                var operation = e.Operation;
                if (operation == null)
                {
                    return;
                }

                // Create a new diagnostic record
                var diagnostic = new FlashDiagnosticData
                {
                    OperationId = operation.Id,
                    OperationType = operation.OperationType.ToString(),
                    StartTime = operation.StartTime,
                    Address = operation.Address,
                    Size = operation.Size,
                    Status = operation.Status.ToString()
                };

                // Add the diagnostic record
                _diagnosticData.Add(diagnostic);
                SaveDiagnosticData();

                _logger?.LogInformation($"Created diagnostic record for operation {operation.Id}", "FlashDiagnostics");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error handling operation started event: {ex.Message}", "FlashDiagnostics");
            }
        }

        /// <summary>
        /// Handles the OperationCompleted event
        /// </summary>
        private void OnOperationCompleted(object? sender, FlashOperationEventArgs e)
        {
            try
            {
                var operation = e.Operation;
                if (operation == null)
                {
                    return;
                }

                // Find the diagnostic record
                var diagnostic = _diagnosticData.FirstOrDefault(d => d.OperationId == operation.Id);
                if (diagnostic == null)
                {
                    return;
                }

                // Update the diagnostic record
                diagnostic.EndTime = operation.EndTime;
                diagnostic.Status = operation.Status.ToString();
                diagnostic.BytesProcessed = operation.BytesProcessed;
                diagnostic.ElapsedTime = operation.ElapsedTime.TotalMilliseconds;
                diagnostic.AverageThroughput = operation.AverageThroughput;
                diagnostic.PeakThroughput = operation.PeakThroughput;
                diagnostic.SuccessRate = 100.0; // 100% success rate for completed operations

                // Add performance metrics
                foreach (var metric in operation.PerformanceMetrics)
                {
                    diagnostic.PerformanceData.Add(new Models.PerformanceDataPoint
                    {
                        Timestamp = metric.Timestamp,
                        BytesProcessed = metric.BytesProcessed,
                        ElapsedTime = metric.ElapsedTime,
                        Throughput = metric.Throughput
                    });
                }

                SaveDiagnosticData();

                _logger?.LogInformation($"Updated diagnostic record for completed operation {operation.Id}", "FlashDiagnostics");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error handling operation completed event: {ex.Message}", "FlashDiagnostics");
            }
        }

        /// <summary>
        /// Handles the OperationFailed event
        /// </summary>
        private void OnOperationFailed(object? sender, FlashOperationEventArgs e)
        {
            try
            {
                var operation = e.Operation;
                if (operation == null)
                {
                    return;
                }

                // Find the diagnostic record
                var diagnostic = _diagnosticData.FirstOrDefault(d => d.OperationId == operation.Id);
                if (diagnostic == null)
                {
                    return;
                }

                // Update the diagnostic record
                diagnostic.EndTime = operation.EndTime;
                diagnostic.Status = operation.Status.ToString();
                diagnostic.BytesProcessed = operation.BytesProcessed;
                diagnostic.ElapsedTime = operation.ElapsedTime.TotalMilliseconds;
                diagnostic.AverageThroughput = operation.AverageThroughput;
                diagnostic.PeakThroughput = operation.PeakThroughput;
                diagnostic.ErrorMessage = operation.ErrorMessage;
                diagnostic.SuccessRate = operation.Size > 0 ? (double)operation.BytesProcessed / operation.Size * 100 : 0;

                // Add performance metrics
                foreach (var metric in operation.PerformanceMetrics)
                {
                    diagnostic.PerformanceData.Add(new Models.PerformanceDataPoint
                    {
                        Timestamp = metric.Timestamp,
                        BytesProcessed = metric.BytesProcessed,
                        ElapsedTime = metric.ElapsedTime,
                        Throughput = metric.Throughput
                    });
                }

                SaveDiagnosticData();

                _logger?.LogInformation($"Updated diagnostic record for failed operation {operation.Id}", "FlashDiagnostics");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error handling operation failed event: {ex.Message}", "FlashDiagnostics");
            }
        }

        /// <summary>
        /// Handles the OperationUpdated event
        /// </summary>
        private void OnOperationProgress(object? sender, FlashOperationEventArgs e)
        {
            try
            {
                var operation = e.Operation;
                if (operation == null)
                {
                    return;
                }

                // Find the diagnostic record
                var diagnostic = _diagnosticData.FirstOrDefault(d => d.OperationId == operation.Id);
                if (diagnostic == null)
                {
                    return;
                }

                // Update the diagnostic record
                diagnostic.Status = operation.Status.ToString();
                diagnostic.BytesProcessed = operation.BytesProcessed;
                diagnostic.ProgressPercentage = operation.ProgressPercentage;
                diagnostic.CurrentThroughput = operation.CurrentThroughput;

                // We don't save the data on every progress update to avoid excessive disk I/O
                // The data will be saved when the operation completes or fails
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error handling operation progress event: {ex.Message}", "FlashDiagnostics");
            }
        }

        /// <summary>
        /// Exports diagnostic data to a CSV file
        /// </summary>
        /// <param name="filePath">The file path to export to</param>
        /// <returns>True if export is successful, false otherwise</returns>
        public bool ExportDiagnosticsToCsv(string filePath)
        {
            try
            {
                if (_diagnosticData.Count == 0)
                {
                    _logger?.LogWarning("No diagnostic data to export", "FlashDiagnostics");
                    return false;
                }

                using (var writer = new StreamWriter(filePath, false, Encoding.UTF8))
                {
                    // Write header
                    writer.WriteLine("OperationId,OperationType,StartTime,EndTime,Status,Address,Size,BytesProcessed,ElapsedTime,AverageThroughput,PeakThroughput,SuccessRate,ErrorMessage");

                    // Write data
                    foreach (var diagnostic in _diagnosticData)
                    {
                        writer.WriteLine(
                            $"{diagnostic.OperationId}," +
                            $"{diagnostic.OperationType}," +
                            $"{diagnostic.StartTime}," +
                            $"{diagnostic.EndTime}," +
                            $"{diagnostic.Status}," +
                            $"0x{diagnostic.Address:X8}," +
                            $"{diagnostic.Size}," +
                            $"{diagnostic.BytesProcessed}," +
                            $"{diagnostic.ElapsedTime}," +
                            $"{diagnostic.AverageThroughput}," +
                            $"{diagnostic.PeakThroughput}," +
                            $"{diagnostic.SuccessRate}," +
                            $"\"{diagnostic.ErrorMessage}\""
                        );
                    }
                }

                _logger?.LogInformation($"Exported {_diagnosticData.Count} diagnostic records to {filePath}", "FlashDiagnostics");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error exporting diagnostic data: {ex.Message}", "FlashDiagnostics");
                return false;
            }
        }

        /// <summary>
        /// Exports performance data for a specific operation to a CSV file
        /// </summary>
        /// <param name="operationId">The operation ID</param>
        /// <param name="filePath">The file path to export to</param>
        /// <returns>True if export is successful, false otherwise</returns>
        public bool ExportPerformanceDataToCsv(Guid operationId, string filePath)
        {
            try
            {
                var diagnostic = _diagnosticData.FirstOrDefault(d => d.OperationId == operationId);
                if (diagnostic == null || diagnostic.PerformanceData.Count == 0)
                {
                    _logger?.LogWarning($"No performance data found for operation {operationId}", "FlashDiagnostics");
                    return false;
                }

                using (var writer = new StreamWriter(filePath, false, Encoding.UTF8))
                {
                    // Write header
                    writer.WriteLine("Timestamp,BytesProcessed,ElapsedTime,Throughput");

                    // Write data
                    foreach (var dataPoint in diagnostic.PerformanceData)
                    {
                        writer.WriteLine(
                            $"{dataPoint.Timestamp}," +
                            $"{dataPoint.BytesProcessed}," +
                            $"{dataPoint.ElapsedTime}," +
                            $"{dataPoint.Throughput}"
                        );
                    }
                }

                _logger?.LogInformation($"Exported {diagnostic.PerformanceData.Count} performance data points for operation {operationId} to {filePath}", "FlashDiagnostics");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error exporting performance data: {ex.Message}", "FlashDiagnostics");
                return false;
            }
        }

        /// <summary>
        /// Gets diagnostic statistics for a specific time period
        /// </summary>
        /// <param name="startTime">The start time</param>
        /// <param name="endTime">The end time</param>
        /// <returns>The diagnostic statistics</returns>
        public DiagnosticStatistics GetDiagnosticStatistics(DateTime startTime, DateTime endTime)
        {
            try
            {
                var filteredData = _diagnosticData.Where(d => d.StartTime >= startTime && (d.EndTime == null || d.EndTime <= endTime)).ToList();

                if (filteredData.Count == 0)
                {
                    return new DiagnosticStatistics();
                }

                var stats = new DiagnosticStatistics
                {
                    TotalOperations = filteredData.Count,
                    SuccessfulOperations = filteredData.Count(d => d.Status == "Completed"),
                    FailedOperations = filteredData.Count(d => d.Status == "Failed"),
                    TotalBytesProcessed = filteredData.Sum(d => d.BytesProcessed),
                    AverageElapsedTime = filteredData.Average(d => d.ElapsedTime),
                    AverageThroughput = filteredData.Average(d => d.AverageThroughput),
                    PeakThroughput = filteredData.Max(d => d.PeakThroughput),
                    AverageSuccessRate = filteredData.Average(d => d.SuccessRate),
                    StartTime = startTime,
                    EndTime = endTime
                };

                // Calculate operation type statistics
                var operationTypes = filteredData.Select(d => d.OperationType).Distinct();
                foreach (var type in operationTypes)
                {
                    var typeData = filteredData.Where(d => d.OperationType == type).ToList();
                    stats.OperationTypeStatistics.Add(type, new OperationTypeStatistics
                    {
                        TotalOperations = typeData.Count,
                        SuccessfulOperations = typeData.Count(d => d.Status == "Completed"),
                        FailedOperations = typeData.Count(d => d.Status == "Failed"),
                        AverageElapsedTime = typeData.Average(d => d.ElapsedTime),
                        AverageThroughput = typeData.Average(d => d.AverageThroughput)
                    });
                }

                return stats;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error calculating diagnostic statistics: {ex.Message}", "FlashDiagnostics");
                return new DiagnosticStatistics();
            }
        }

        /// <summary>
        /// Clears all diagnostic data
        /// </summary>
        public void ClearDiagnosticData()
        {
            try
            {
                _diagnosticData.Clear();
                SaveDiagnosticData();
                _logger?.LogInformation("Cleared all diagnostic data", "FlashDiagnostics");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error clearing diagnostic data: {ex.Message}", "FlashDiagnostics");
            }
        }

        /// <summary>
        /// Disposes the FlashDiagnostics instance
        /// </summary>
        public void Dispose()
        {
            try
            {
                // Unsubscribe from events
                if (_operationMonitor != null)
                {
                    _operationMonitor.OperationStarted -= OnOperationStarted;
                    _operationMonitor.OperationCompleted -= OnOperationCompleted;
                    _operationMonitor.OperationFailed -= OnOperationFailed;
                    _operationMonitor.OperationUpdated -= OnOperationProgress;
                }

                // Save any pending diagnostic data
                SaveDiagnosticData();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error disposing FlashDiagnostics: {ex.Message}", "FlashDiagnostics");
            }
        }
    }

    /// <summary>
    /// Represents diagnostic statistics for a time period
    /// </summary>
    public class DiagnosticStatistics
    {
        /// <summary>
        /// Gets or sets the start time of the statistics period
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Gets or sets the end time of the statistics period
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Gets or sets the total number of operations
        /// </summary>
        public int TotalOperations { get; set; }

        /// <summary>
        /// Gets or sets the number of successful operations
        /// </summary>
        public int SuccessfulOperations { get; set; }

        /// <summary>
        /// Gets or sets the number of failed operations
        /// </summary>
        public int FailedOperations { get; set; }

        /// <summary>
        /// Gets or sets the total number of bytes processed
        /// </summary>
        public long TotalBytesProcessed { get; set; }

        /// <summary>
        /// Gets or sets the average elapsed time in milliseconds
        /// </summary>
        public double AverageElapsedTime { get; set; }

        /// <summary>
        /// Gets or sets the average throughput in bytes per second
        /// </summary>
        public double AverageThroughput { get; set; }

        /// <summary>
        /// Gets or sets the peak throughput in bytes per second
        /// </summary>
        public double PeakThroughput { get; set; }

        /// <summary>
        /// Gets or sets the average success rate as a percentage
        /// </summary>
        public double AverageSuccessRate { get; set; }

        /// <summary>
        /// Gets or sets the operation type statistics
        /// </summary>
        public Dictionary<string, OperationTypeStatistics> OperationTypeStatistics { get; set; } = new Dictionary<string, OperationTypeStatistics>();
    }

    /// <summary>
    /// Represents statistics for a specific operation type
    /// </summary>
    public class OperationTypeStatistics
    {
        /// <summary>
        /// Gets or sets the total number of operations
        /// </summary>
        public int TotalOperations { get; set; }

        /// <summary>
        /// Gets or sets the number of successful operations
        /// </summary>
        public int SuccessfulOperations { get; set; }

        /// <summary>
        /// Gets or sets the number of failed operations
        /// </summary>
        public int FailedOperations { get; set; }

        /// <summary>
        /// Gets or sets the average elapsed time in milliseconds
        /// </summary>
        public double AverageElapsedTime { get; set; }

        /// <summary>
        /// Gets or sets the average throughput in bytes per second
        /// </summary>
        public double AverageThroughput { get; set; }
    }

    /// <summary>
    /// Represents a performance data point
    /// </summary>
    public class PerformanceDataPoint
    {
        /// <summary>
        /// Gets or sets the timestamp
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the number of bytes processed
        /// </summary>
        public int BytesProcessed { get; set; }

        /// <summary>
        /// Gets or sets the elapsed time in milliseconds
        /// </summary>
        public double ElapsedTime { get; set; }

        /// <summary>
        /// Gets or sets the throughput in bytes per second
        /// </summary>
        public double Throughput { get; set; }
    }
}
