using System;
using System.Threading.Tasks;
using Moq;
using Moq.Language.Flow;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Tests.Helpers;
using VolvoFlashWR.Communication.Microcontroller;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Microcontroller
{
    [TestFixture]
    public class MC9S12XEP100IntegrationTests
    {
        private Mock<ILoggingService> _mockLogger;
        private Mock<IVocomService> _mockVocomService;
        private Mock<IRegisterAccess> _mockRegisterAccess;
        private MC9S12XEP100Integration _integration;
        private ECUDevice _ecuDevice;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _mockVocomService = new Mock<IVocomService>();
            _mockRegisterAccess = new Mock<IRegisterAccess>();

            // Create a test ECU device
            _ecuDevice = new ECUDevice
            {
                Id = "TEST_ECU",
                Name = "Test ECU",
                MicrocontrollerType = MC9S12XEP100Configuration.DeviceInfo.DEVICE_NAME,
                ProtocolType = ECUProtocolType.CAN,
                EEPROMSize = MC9S12XEP100Configuration.EEPROM_SIZE,
                FlashSize = MC9S12XEP100Configuration.FLASH_SIZE,
                RAMSize = MC9S12XEP100Configuration.RAM_SIZE
            };

            // Add backdoor key to ECU properties
            _ecuDevice.Properties.Add("BackdoorKey", new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 });

            // Create the integration with mocked dependencies
            _integration = new MC9S12XEP100Integration(_mockLogger.Object, _mockVocomService.Object, ECUProtocolType.CAN);
        }

        [Test]
        public async Task ReadEEPROMAsync_WithValidECU_ReturnsEEPROMData()
        {
            // Arrange
            byte[] expectedData = new byte[MC9S12XEP100Configuration.EEPROM_SIZE];
            for (int i = 0; i < expectedData.Length; i++)
            {
                expectedData[i] = (byte)(i & 0xFF);
            }

            // Set up mock register access
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsAny<uint>()))
                .Returns(Task.FromResult<byte>(0x02)); // 0x02 = Unsecured

            // Act
            byte[] result = await _integration.ReadEEPROMAsync(_ecuDevice);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Length, Is.EqualTo(MC9S12XEP100Configuration.EEPROM_SIZE));
        }

        [Test]
        public async Task WriteEEPROMAsync_WithValidECUAndData_ReturnsTrue()
        {
            // Arrange
            byte[] data = new byte[MC9S12XEP100Configuration.EEPROM_SIZE];
            for (int i = 0; i < data.Length; i++)
            {
                data[i] = (byte)(i & 0xFF);
            }

            // Set up mock register access
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsAny<uint>()))
                .Returns(Task.FromResult<byte>(0x02)); // 0x02 = Unsecured

            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsAny<uint>(), It.IsAny<byte>()))
                .Returns(Task.FromResult(true));

            // Act
            bool result = await _integration.WriteEEPROMAsync(_ecuDevice, data);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task ReadMicrocontrollerCodeAsync_WithValidECU_ReturnsMicrocontrollerCode()
        {
            // Arrange
            byte[] expectedData = new byte[MC9S12XEP100Configuration.FLASH_SIZE];
            for (int i = 0; i < expectedData.Length; i++)
            {
                expectedData[i] = (byte)(i & 0xFF);
            }

            // Set up mock register access
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsAny<uint>()))
                .Returns(Task.FromResult<byte>(0x02)); // 0x02 = Unsecured

            // Act
            byte[] result = await _integration.ReadMicrocontrollerCodeAsync(_ecuDevice);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Length, Is.EqualTo(MC9S12XEP100Configuration.FLASH_SIZE));
        }

        [Test]
        public async Task WriteMicrocontrollerCodeAsync_WithValidECUAndCode_ReturnsTrue()
        {
            // Arrange
            byte[] code = new byte[MC9S12XEP100Configuration.FLASH_SIZE];
            for (int i = 0; i < code.Length; i++)
            {
                code[i] = (byte)(i & 0xFF);
            }

            // Set up mock register access
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsAny<uint>()))
                .Returns(Task.FromResult<byte>(0x02)); // 0x02 = Unsecured

            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsAny<uint>(), It.IsAny<byte>()))
                .Returns(Task.FromResult(true));

            // Act
            bool result = await _integration.WriteMicrocontrollerCodeAsync(_ecuDevice, code);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task ReadEEPROMAsync_WithSecuredECU_PerformsSecurityAccess()
        {
            // Arrange
            byte[] expectedData = new byte[MC9S12XEP100Configuration.EEPROM_SIZE];

            // Set up mock register access
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsAny<uint>()))
                .Returns(Task.FromResult<byte>(0x00)); // 0x00 = Secured

            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsAny<uint>(), It.IsAny<byte>()))
                .Returns(Task.FromResult(true));

            // Act
            byte[] result = await _integration.ReadEEPROMAsync(_ecuDevice);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Length, Is.EqualTo(MC9S12XEP100Configuration.EEPROM_SIZE));

            // Verify that security access was performed
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_CMD, 0x0C), Times.Once);
        }
    }
}

