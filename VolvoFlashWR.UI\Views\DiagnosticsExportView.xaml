<UserControl x:Class="VolvoFlashWR.UI.Views.DiagnosticsExportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:VolvoFlashWR.UI.Views"
             xmlns:viewmodels="clr-namespace:VolvoFlashWR.UI.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800"
             d:DataContext="{d:DesignInstance Type=viewmodels:DiagnosticsExportViewModel}">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Flash Operation Diagnostics" FontSize="20" FontWeight="Bold" Margin="0,0,0,10"/>
        
        <!-- Filter Controls -->
        <Grid Grid.Row="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" Text="Start Date:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <DatePicker Grid.Column="1" SelectedDate="{Binding StartDate}" Width="120" Margin="0,0,10,0"/>
            
            <TextBlock Grid.Column="2" Text="End Date:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <DatePicker Grid.Column="3" SelectedDate="{Binding EndDate}" Width="120" Margin="0,0,10,0"/>
            
            <Button Grid.Column="5" Content="Refresh" Command="{Binding RefreshCommand}" Width="80"/>
        </Grid>
        
        <!-- Diagnostic Records -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Diagnostic Records List -->
            <Grid Grid.Column="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="Diagnostic Records" FontWeight="Bold" Margin="0,0,0,5"/>
                
                <DataGrid Grid.Row="1" ItemsSource="{Binding DiagnosticRecords}" SelectedItem="{Binding SelectedDiagnosticRecord}"
                          AutoGenerateColumns="False" IsReadOnly="True" SelectionMode="Single" 
                          CanUserSortColumns="True" CanUserResizeColumns="True" CanUserReorderColumns="False"
                          AlternatingRowBackground="#F5F5F5" GridLinesVisibility="Horizontal"
                          BorderThickness="1" BorderBrush="#CCCCCC">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Operation Type" Binding="{Binding OperationType}" Width="100"/>
                        <DataGridTextColumn Header="Start Time" Binding="{Binding StartTime, StringFormat=\{0:yyyy-MM-dd HH:mm:ss\}}" Width="150"/>
                        <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="80"/>
                        <DataGridTextColumn Header="Size (bytes)" Binding="{Binding Size}" Width="80"/>
                        <DataGridTextColumn Header="Progress" Binding="{Binding ProgressPercentage, StringFormat=\{0:F1\}%}" Width="80"/>
                        <DataGridTextColumn Header="Elapsed (ms)" Binding="{Binding ElapsedTime, StringFormat=\{0:F1\}}" Width="80"/>
                        <DataGridTextColumn Header="Throughput (B/s)" Binding="{Binding AverageThroughput, StringFormat=\{0:F1\}}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
            
            <!-- Splitter -->
            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Center" VerticalAlignment="Stretch" Background="#CCCCCC"/>
            
            <!-- Performance Data -->
            <Grid Grid.Column="2" IsEnabled="{Binding HasSelectedRecord}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="Operation Details" FontWeight="Bold" Margin="0,0,0,5"/>
                
                <StackPanel Grid.Row="1" Margin="0,0,0,10">
                    <CheckBox Content="Show Performance Data" IsChecked="{Binding ShowPerformanceData}" Margin="0,0,0,5"/>
                    <Button Content="Export Performance Data" Command="{Binding ExportPerformanceDataCommand}" 
                            HorizontalAlignment="Left" Width="180" Margin="0,5,0,0"/>
                </StackPanel>
                
                <TabControl Grid.Row="2">
                    <TabItem Header="Details">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <Grid Margin="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="Operation ID:" FontWeight="Bold" Margin="0,2,5,2"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding SelectedDiagnosticRecord.OperationId}" Margin="0,2,0,2"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="Operation Type:" FontWeight="Bold" Margin="0,2,5,2"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SelectedDiagnosticRecord.OperationType}" Margin="0,2,0,2"/>
                                
                                <TextBlock Grid.Row="2" Grid.Column="0" Text="Status:" FontWeight="Bold" Margin="0,2,5,2"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding SelectedDiagnosticRecord.Status}" Margin="0,2,0,2"/>
                                
                                <TextBlock Grid.Row="3" Grid.Column="0" Text="Start Time:" FontWeight="Bold" Margin="0,2,5,2"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding SelectedDiagnosticRecord.StartTime, StringFormat=\{0:yyyy-MM-dd HH:mm:ss\}}" Margin="0,2,0,2"/>
                                
                                <TextBlock Grid.Row="4" Grid.Column="0" Text="End Time:" FontWeight="Bold" Margin="0,2,5,2"/>
                                <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding SelectedDiagnosticRecord.EndTime, StringFormat=\{0:yyyy-MM-dd HH:mm:ss\}}" Margin="0,2,0,2"/>
                                
                                <TextBlock Grid.Row="5" Grid.Column="0" Text="Address:" FontWeight="Bold" Margin="0,2,5,2"/>
                                <TextBlock Grid.Row="5" Grid.Column="1" Text="{Binding SelectedDiagnosticRecord.Address, StringFormat=0x\{0:X8\}}" Margin="0,2,0,2"/>
                                
                                <TextBlock Grid.Row="6" Grid.Column="0" Text="Size:" FontWeight="Bold" Margin="0,2,5,2"/>
                                <TextBlock Grid.Row="6" Grid.Column="1" Text="{Binding SelectedDiagnosticRecord.Size, StringFormat=\{0:N0\} bytes}" Margin="0,2,0,2"/>
                                
                                <TextBlock Grid.Row="7" Grid.Column="0" Text="Bytes Processed:" FontWeight="Bold" Margin="0,2,5,2"/>
                                <TextBlock Grid.Row="7" Grid.Column="1" Text="{Binding SelectedDiagnosticRecord.BytesProcessed, StringFormat=\{0:N0\} bytes}" Margin="0,2,0,2"/>
                                
                                <TextBlock Grid.Row="8" Grid.Column="0" Text="Progress:" FontWeight="Bold" Margin="0,2,5,2"/>
                                <TextBlock Grid.Row="8" Grid.Column="1" Text="{Binding SelectedDiagnosticRecord.ProgressPercentage, StringFormat=\{0:F1\}%}" Margin="0,2,0,2"/>
                                
                                <TextBlock Grid.Row="9" Grid.Column="0" Text="Elapsed Time:" FontWeight="Bold" Margin="0,2,5,2"/>
                                <TextBlock Grid.Row="9" Grid.Column="1" Text="{Binding SelectedDiagnosticRecord.ElapsedTime, StringFormat=\{0:F1\} ms}" Margin="0,2,0,2"/>
                                
                                <TextBlock Grid.Row="10" Grid.Column="0" Text="Average Throughput:" FontWeight="Bold" Margin="0,2,5,2"/>
                                <TextBlock Grid.Row="10" Grid.Column="1" Text="{Binding SelectedDiagnosticRecord.AverageThroughput, StringFormat=\{0:F1\} bytes/sec}" Margin="0,2,0,2"/>
                                
                                <TextBlock Grid.Row="11" Grid.Column="0" Text="Peak Throughput:" FontWeight="Bold" Margin="0,2,5,2"/>
                                <TextBlock Grid.Row="11" Grid.Column="1" Text="{Binding SelectedDiagnosticRecord.PeakThroughput, StringFormat=\{0:F1\} bytes/sec}" Margin="0,2,0,2"/>
                                
                                <TextBlock Grid.Row="12" Grid.Column="0" Text="Error Message:" FontWeight="Bold" Margin="0,2,5,2"/>
                                <TextBlock Grid.Row="12" Grid.Column="1" Text="{Binding SelectedDiagnosticRecord.ErrorMessage}" Margin="0,2,0,2" TextWrapping="Wrap"/>
                            </Grid>
                        </ScrollViewer>
                    </TabItem>
                    <TabItem Header="Performance Data" IsEnabled="{Binding HasPerformanceData}">
                        <Grid Visibility="{Binding ShowPerformanceData, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <DataGrid ItemsSource="{Binding SelectedDiagnosticRecord.PerformanceData}" 
                                      AutoGenerateColumns="False" IsReadOnly="True"
                                      AlternatingRowBackground="#F5F5F5" GridLinesVisibility="Horizontal"
                                      BorderThickness="1" BorderBrush="#CCCCCC">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="Timestamp" Binding="{Binding Timestamp, StringFormat=\{0:HH:mm:ss.fff\}}" Width="100"/>
                                    <DataGridTextColumn Header="Bytes Processed" Binding="{Binding BytesProcessed}" Width="100"/>
                                    <DataGridTextColumn Header="Elapsed (ms)" Binding="{Binding ElapsedTime, StringFormat=\{0:F1\}}" Width="100"/>
                                    <DataGridTextColumn Header="Throughput (B/s)" Binding="{Binding Throughput, StringFormat=\{0:F1\}}" Width="120"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </TabItem>
                </TabControl>
            </Grid>
        </Grid>
        
        <!-- Statistics -->
        <Expander Grid.Row="3" Header="Diagnostic Statistics" IsExpanded="True" Margin="0,10,0,10">
            <Grid Margin="10,5,10,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Overall Statistics -->
                <Grid Grid.Column="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2" Text="Overall Statistics" FontWeight="Bold" Margin="0,0,0,5"/>
                    
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Total Operations:" Margin="0,2,5,2"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Statistics.TotalOperations}" Margin="0,2,0,2"/>
                    
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Successful Operations:" Margin="0,2,5,2"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding Statistics.SuccessfulOperations}" Margin="0,2,0,2"/>
                    
                    <TextBlock Grid.Row="3" Grid.Column="0" Text="Failed Operations:" Margin="0,2,5,2"/>
                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding Statistics.FailedOperations}" Margin="0,2,0,2"/>
                    
                    <TextBlock Grid.Row="4" Grid.Column="0" Text="Total Bytes Processed:" Margin="0,2,5,2"/>
                    <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding Statistics.TotalBytesProcessed, StringFormat=\{0:N0\} bytes}" Margin="0,2,0,2"/>
                    
                    <TextBlock Grid.Row="5" Grid.Column="0" Text="Average Elapsed Time:" Margin="0,2,5,2"/>
                    <TextBlock Grid.Row="5" Grid.Column="1" Text="{Binding Statistics.AverageElapsedTime, StringFormat=\{0:F1\} ms}" Margin="0,2,0,2"/>
                    
                    <TextBlock Grid.Row="6" Grid.Column="0" Text="Average Throughput:" Margin="0,2,5,2"/>
                    <TextBlock Grid.Row="6" Grid.Column="1" Text="{Binding Statistics.AverageThroughput, StringFormat=\{0:F1\} bytes/sec}" Margin="0,2,0,2"/>
                    
                    <TextBlock Grid.Row="7" Grid.Column="0" Text="Peak Throughput:" Margin="0,2,5,2"/>
                    <TextBlock Grid.Row="7" Grid.Column="1" Text="{Binding Statistics.PeakThroughput, StringFormat=\{0:F1\} bytes/sec}" Margin="0,2,0,2"/>
                    
                    <TextBlock Grid.Row="8" Grid.Column="0" Text="Average Success Rate:" Margin="0,2,5,2"/>
                    <TextBlock Grid.Row="8" Grid.Column="1" Text="{Binding Statistics.AverageSuccessRate, StringFormat=\{0:F1\}%}" Margin="0,2,0,2"/>
                </Grid>
                
                <!-- Operation Type Statistics -->
                <Grid Grid.Column="1">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Text="Operation Type Statistics" FontWeight="Bold" Margin="0,0,0,5"/>
                    
                    <ListView Grid.Row="1" ItemsSource="{Binding Statistics.OperationTypeStatistics}">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Expander Header="{Binding Key}" Margin="0,2,0,2">
                                    <Grid Margin="10,5,10,5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        
                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Total Operations:" Margin="0,2,5,2"/>
                                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding Value.TotalOperations}" Margin="0,2,0,2"/>
                                        
                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Successful Operations:" Margin="0,2,5,2"/>
                                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Value.SuccessfulOperations}" Margin="0,2,0,2"/>
                                        
                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Failed Operations:" Margin="0,2,5,2"/>
                                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding Value.FailedOperations}" Margin="0,2,0,2"/>
                                        
                                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Average Elapsed Time:" Margin="0,2,5,2"/>
                                        <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding Value.AverageElapsedTime, StringFormat=\{0:F1\} ms}" Margin="0,2,0,2"/>
                                        
                                        <TextBlock Grid.Row="4" Grid.Column="0" Text="Average Throughput:" Margin="0,2,5,2"/>
                                        <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding Value.AverageThroughput, StringFormat=\{0:F1\} bytes/sec}" Margin="0,2,0,2"/>
                                    </Grid>
                                </Expander>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </Grid>
            </Grid>
        </Expander>
        
        <!-- Export Controls -->
        <Grid Grid.Row="4">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" Text="Export Path:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Grid.Column="1" Text="{Binding ExportPath}" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <Button Grid.Column="2" Content="Browse..." Command="{Binding BrowseCommand}" Width="80" Margin="0,0,5,0"/>
            <Button Grid.Column="3" Content="Export" Command="{Binding ExportCommand}" Width="80" Margin="0,0,5,0"/>
            <Button Grid.Column="4" Content="Clear Data" Command="{Binding ClearDataCommand}" Width="80" Foreground="Red"/>
        </Grid>
    </Grid>
</UserControl>
