using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for ECU communication service
    /// </summary>
    public interface IECUCommunicationService
    {
        /// <summary>
        /// Event triggered when an ECU is connected
        /// </summary>
        event EventHandler<ECUDevice> ECUConnected;

        /// <summary>
        /// Event triggered when an ECU is disconnected
        /// </summary>
        event EventHandler<ECUDevice> ECUDisconnected;

        /// <summary>
        /// Event triggered when an error occurs during ECU communication
        /// </summary>
        event EventHandler<string> ECUError;

        /// <summary>
        /// Gets the list of currently connected ECUs
        /// </summary>
        List<ECUDevice> ConnectedECUs { get; }

        /// <summary>
        /// Gets the current operating mode
        /// </summary>
        OperatingMode CurrentOperatingMode { get; }

        /// <summary>
        /// Gets a value indicating whether the service is initialized
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Initializes the ECU communication service
        /// </summary>
        /// <param name="vocomService">The Vocom service to use for communication</param>
        /// <returns>True if initialization is successful, false otherwise</returns>
        Task<bool> InitializeAsync(IVocomService vocomService);

        /// <summary>
        /// Scans for available ECUs
        /// </summary>
        /// <returns>List of available ECUs</returns>
        Task<List<ECUDevice>> ScanForECUsAsync();

        /// <summary>
        /// Connects to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        Task<bool> ConnectToECUAsync(ECUDevice ecu);

        /// <summary>
        /// Disconnects from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        Task<bool> DisconnectFromECUAsync(ECUDevice ecu);

        /// <summary>
        /// Disconnects from all connected ECUs
        /// </summary>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        Task<bool> DisconnectAllECUsAsync();

        /// <summary>
        /// Sets the operating mode
        /// </summary>
        /// <param name="mode">The operating mode to set</param>
        /// <returns>True if mode change is successful, false otherwise</returns>
        Task<bool> SetOperatingModeAsync(OperatingMode mode);

        /// <summary>
        /// Sets the communication speed mode (High or Low) for an ECU
        /// </summary>
        /// <param name="ecu">The ECU to set the speed mode for</param>
        /// <param name="speedMode">The speed mode to set</param>
        /// <returns>True if speed mode change is successful, false otherwise</returns>
        Task<bool> SetCommunicationSpeedModeAsync(ECUDevice ecu, CommunicationSpeedMode speedMode);

        /// <summary>
        /// Reads EEPROM data from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>EEPROM data as byte array, or null if read fails</returns>
        Task<byte[]?> ReadEEPROMAsync(ECUDevice ecu);

        /// <summary>
        /// Reads EEPROM data from an ECU and returns a structured model
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>EEPROM data model, or null if read fails</returns>
        Task<EEPROMData?> ReadEEPROMDataAsync(ECUDevice ecu);

        /// <summary>
        /// Writes EEPROM data to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="data">The data to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteEEPROMAsync(ECUDevice ecu, byte[] data);

        /// <summary>
        /// Writes EEPROM data to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="eepromData">The EEPROM data model to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteEEPROMDataAsync(ECUDevice ecu, EEPROMData eepromData);

        /// <summary>
        /// Reads microcontroller code from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Microcontroller code as byte array, or null if read fails</returns>
        Task<byte[]?> ReadMicrocontrollerCodeAsync(ECUDevice ecu);

        /// <summary>
        /// Reads microcontroller code from an ECU and returns a structured model
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Microcontroller code model, or null if read fails</returns>
        Task<MicrocontrollerCode?> ReadMicrocontrollerCodeDataAsync(ECUDevice ecu);

        /// <summary>
        /// Writes microcontroller code to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="code">The code to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteMicrocontrollerCodeAsync(ECUDevice ecu, byte[] code);

        /// <summary>
        /// Writes microcontroller code to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="mcuCode">The microcontroller code model to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteMicrocontrollerCodeDataAsync(ECUDevice ecu, MicrocontrollerCode mcuCode);

        /// <summary>
        /// Reads active faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>List of active faults, or null if read fails</returns>
        Task<List<ECUFault>?> ReadActiveFaultsAsync(ECUDevice ecu);

        /// <summary>
        /// Reads inactive faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>List of inactive faults, or null if read fails</returns>
        Task<List<ECUFault>?> ReadInactiveFaultsAsync(ECUDevice ecu);

        /// <summary>
        /// Clears faults from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <returns>True if clearing is successful, false otherwise</returns>
        Task<bool> ClearFaultsAsync(ECUDevice ecu);

        /// <summary>
        /// Reads parameters from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Dictionary of parameter names and values, or null if read fails</returns>
        Task<Dictionary<string, object>?> ReadParametersAsync(ECUDevice ecu);

        /// <summary>
        /// Writes parameters to an ECU
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="parameters">The parameters to write</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteParametersAsync(ECUDevice ecu, Dictionary<string, object> parameters);

        /// <summary>
        /// Performs a diagnostic session on an ECU
        /// </summary>
        /// <param name="ecu">The ECU to diagnose</param>
        /// <returns>Diagnostic data</returns>
        Task<DiagnosticData> PerformDiagnosticSessionAsync(ECUDevice ecu);

        /// <summary>
        /// Refreshes the ECU data (parameters, faults, etc.)
        /// </summary>
        /// <param name="ecu">The ECU to refresh</param>
        /// <returns>True if refresh is successful, false otherwise</returns>
        Task<bool> RefreshECUAsync(ECUDevice ecu);

        /// <summary>
        /// Reads EEPROM data from an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>EEPROM data as byte array, or null if read fails</returns>
        Task<byte[]?> ReadEEPROMAsync(ECUDevice ecu, IProgress<int> progress);

        /// <summary>
        /// Writes EEPROM data to an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="data">The data to write</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteEEPROMAsync(ECUDevice ecu, byte[] data, IProgress<int> progress);

        /// <summary>
        /// Reads microcontroller code from an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>Microcontroller code as byte array, or null if read fails</returns>
        Task<byte[]?> ReadMicrocontrollerCodeAsync(ECUDevice ecu, IProgress<int> progress);

        /// <summary>
        /// Writes microcontroller code to an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="code">The code to write</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteMicrocontrollerCodeAsync(ECUDevice ecu, byte[] code, IProgress<int> progress);

        /// <summary>
        /// Reads all faults (active and inactive) from an ECU
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <returns>Tuple containing lists of active and inactive faults</returns>
        Task<(List<ECUFault>? Active, List<ECUFault>? Inactive)> ReadFaultsAsync(ECUDevice ecu);

        /// <summary>
        /// Clears faults from an ECU with specific fault codes
        /// </summary>
        /// <param name="ecu">The ECU to clear faults from</param>
        /// <param name="faultCodes">The specific fault codes to clear, or null to clear all</param>
        /// <returns>True if clearing is successful, false otherwise</returns>
        Task<bool> ClearFaultsAsync(ECUDevice ecu, List<string> faultCodes);

        /// <summary>
        /// Reads parameters from an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to read from</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>Dictionary of parameter names and values, or null if read fails</returns>
        Task<Dictionary<string, object>?> ReadParametersAsync(ECUDevice ecu, IProgress<int> progress);

        /// <summary>
        /// Writes parameters to an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to write to</param>
        /// <param name="parameters">The parameters to write</param>
        /// <param name="progress">Progress reporter</param>
        /// <returns>True if write is successful, false otherwise</returns>
        Task<bool> WriteParametersAsync(ECUDevice ecu, Dictionary<string, object> parameters, IProgress<int> progress);

        /// <summary>
        /// Cancels the current operation
        /// </summary>
        /// <returns>True if cancellation is successful, false otherwise</returns>
        Task<bool> CancelOperation();
    }
}
