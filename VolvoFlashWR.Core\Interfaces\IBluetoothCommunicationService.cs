using System;
using System.Threading.Tasks;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for Bluetooth communication service
    /// </summary>
    public interface IBluetoothCommunicationService
    {
        /// <summary>
        /// Event triggered when a Bluetooth connection is established
        /// </summary>
        event EventHandler<string> BluetoothConnected;

        /// <summary>
        /// Event triggered when a Bluetooth connection is lost
        /// </summary>
        event EventHandler<string> BluetoothDisconnected;

        /// <summary>
        /// Event triggered when an error occurs during Bluetooth communication
        /// </summary>
        event EventHandler<string> BluetoothError;

        /// <summary>
        /// Initializes the Bluetooth communication service
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        Task<bool> InitializeAsync();

        /// <summary>
        /// Checks if Bluetooth is available
        /// </summary>
        /// <returns>True if Bluetooth is available, false otherwise</returns>
        Task<bool> IsBluetoothAvailableAsync();

        /// <summary>
        /// Enables Bluetooth if it is disabled
        /// </summary>
        /// <returns>True if Bluetooth is successfully enabled, false otherwise</returns>
        Task<bool> EnableBluetoothAsync();

        /// <summary>
        /// Connects to a device via Bluetooth
        /// </summary>
        /// <param name="address">The Bluetooth address of the device</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        Task<bool> ConnectToDeviceAsync(string address);

        /// <summary>
        /// Disconnects from a device
        /// </summary>
        /// <param name="address">The Bluetooth address of the device</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        Task<bool> DisconnectFromDeviceAsync(string address);

        /// <summary>
        /// Sends data to a device
        /// </summary>
        /// <param name="address">The Bluetooth address of the device</param>
        /// <param name="data">The data to send</param>
        /// <returns>True if data is sent successfully, false otherwise</returns>
        Task<bool> SendDataAsync(string address, byte[] data);

        /// <summary>
        /// Receives data from a device
        /// </summary>
        /// <param name="address">The Bluetooth address of the device</param>
        /// <param name="timeout">The timeout in milliseconds</param>
        /// <returns>The received data</returns>
        Task<byte[]> ReceiveDataAsync(string address, int timeout);

        /// <summary>
        /// Scans for available Bluetooth devices
        /// </summary>
        /// <returns>List of available Bluetooth devices</returns>
        Task<string[]> ScanDevicesAsync();

        /// <summary>
        /// Pairs with a Bluetooth device
        /// </summary>
        /// <param name="address">The Bluetooth address of the device</param>
        /// <param name="pin">The PIN for pairing</param>
        /// <returns>True if pairing is successful, false otherwise</returns>
        Task<bool> PairDeviceAsync(string address, string pin);
    }
}
