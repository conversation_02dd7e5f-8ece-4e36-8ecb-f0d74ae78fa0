namespace VolvoFlashWR.Core.Enums
{
    /// <summary>
    /// Represents the status of a diagnostic session
    /// </summary>
    public enum DiagnosticStatus
    {
        /// <summary>
        /// Diagnostic session was successful
        /// </summary>
        Success,

        /// <summary>
        /// Diagnostic session failed
        /// </summary>
        Failed,

        /// <summary>
        /// Diagnostic session is in progress
        /// </summary>
        InProgress,

        /// <summary>
        /// Diagnostic session was cancelled
        /// </summary>
        Cancelled,

        /// <summary>
        /// Diagnostic session timed out
        /// </summary>
        Timeout
    }
}
