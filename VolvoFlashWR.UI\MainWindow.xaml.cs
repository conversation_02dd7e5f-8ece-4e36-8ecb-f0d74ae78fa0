﻿using System;
using System.Windows;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.UI.ViewModels;
using VolvoFlashWR.UI.Views;

namespace VolvoFlashWR.UI;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
    }

    /// <summary>
    /// Handles the Exit menu item click
    /// </summary>
    private void ExitMenuItem_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    /// <summary>
    /// Handles the About menu item click
    /// </summary>
    private void AboutMenuItem_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show(
            "Volvo Flash WR - ECU Management Tool\n\n" +
            "Version: 1.0.0\n" +
            "© 2025 All Rights Reserved For S.A.H Software Solutions Company\n\n" +
            "A comprehensive tool for ECU communication, diagnostics, and backup management.",
            "About Volvo Flash WR",
            MessageBoxButton.OK,
            MessageBoxImage.Information);
    }

    /// <summary>
    /// Handles the Flash Operation Monitor menu item click
    /// </summary>
    private void FlashMonitorMenuItem_Click(object sender, RoutedEventArgs e)
    {
        // Get the main view model
        if (DataContext is MainViewModel mainViewModel)
        {
            // Get the flash operation monitor service
            var flashOperationMonitorService = (App.Current as App)?.GetService<IFlashOperationMonitorService>();
            if (flashOperationMonitorService != null)
            {
                // Create and show the flash operation monitor view
                var monitorView = new FlashOperationMonitorView(flashOperationMonitorService.Monitor);
                monitorView.Owner = this;
                monitorView.Show();
            }
            else
            {
                MessageBox.Show(
                    "Flash operation monitor service is not available.",
                    "Service Not Available",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
            }
        }
    }

    /// <summary>
    /// Handles the License menu item click
    /// </summary>
    private void LicenseMenuItem_Click(object sender, RoutedEventArgs e)
    {
        // Get the licensing service
        var licensingService = (App.Current as App)?.GetService<ILicensingService>();
        var loggingService = (App.Current as App)?.GetService<ILoggingService>();

        if (licensingService != null && loggingService != null)
        {
            // Create and show the license view
            var licenseViewModel = new LicenseViewModel(loggingService, licensingService);
            var licenseView = new LicenseView(licenseViewModel);
            licenseView.Owner = this;
            licenseView.ShowDialog();
        }
        else
        {
            MessageBox.Show(
                "Licensing service is not available.",
                "Service Not Available",
                MessageBoxButton.OK,
                MessageBoxImage.Warning);
        }
    }
}