namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents the result of an activation attempt
    /// </summary>
    public class ActivationResult
    {
        /// <summary>
        /// Gets or sets whether the activation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Gets or sets the error message if activation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets the license information after activation
        /// </summary>
        public LicenseInfo? LicenseInfo { get; set; }

        /// <summary>
        /// Creates a successful activation result
        /// </summary>
        /// <param name="licenseInfo">The license information</param>
        /// <returns>A successful activation result</returns>
        public static ActivationResult Successful(LicenseInfo licenseInfo)
        {
            return new ActivationResult
            {
                Success = true,
                LicenseInfo = licenseInfo
            };
        }

        /// <summary>
        /// Creates a failed activation result
        /// </summary>
        /// <param name="errorMessage">The error message</param>
        /// <returns>A failed activation result</returns>
        public static ActivationResult Failed(string errorMessage)
        {
            return new ActivationResult
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }
    }
}
