using System;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Microcontroller;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Microcontroller
{
    [TestFixture]
    public class RegisterAccessTests
    {
        private Mock<ILoggingService> _mockLogger;
        private Mock<IVocomService> _mockVocomService;
        private Mock<VocomDevice> _mockVocomDevice;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _mockVocomService = new Mock<IVocomService>();
            _mockVocomDevice = new Mock<VocomDevice>();

            _mockVocomService.Setup(s => s.GetCurrentDeviceAsync())
                .ReturnsAsync(_mockVocomDevice.Object);
        }

        [Test]
        public async Task CANRegisterAccess_ReadRegisterByteAsync_ReturnsCorrectValue()
        {
            // Arrange
            uint register = 0x1234;
            byte expectedValue = 0x42;

            _mockVocomService.Setup(s => s.SendCANFrameAsync(
                    It.IsAny<VocomDevice>(),
                    It.IsAny<uint>(),
                    It.IsAny<byte[]>(),
                    It.IsAny<int>()))
                .ReturnsAsync(new byte[] { expectedValue });

            var registerAccess = new CANRegisterAccess(_mockLogger.Object, _mockVocomService.Object);

            // Act
            byte result = await registerAccess.ReadRegisterByteAsync(register);

            // Assert
            Assert.That(result, Is.EqualTo(expectedValue));
        }

        [Test]
        public async Task CANRegisterAccess_WriteRegisterByteAsync_ReturnsTrue()
        {
            // Arrange
            uint register = 0x1234;
            byte value = 0x42;

            _mockVocomService.Setup(s => s.SendCANFrameAsync(
                    It.IsAny<VocomDevice>(),
                    It.IsAny<uint>(),
                    It.IsAny<byte[]>(),
                    It.IsAny<int>()))
                .ReturnsAsync(new byte[] { 0x00 }); // 0x00 indicates success

            var registerAccess = new CANRegisterAccess(_mockLogger.Object, _mockVocomService.Object);

            // Act
            bool result = await registerAccess.WriteRegisterByteAsync(register, value);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task CANRegisterAccess_ReadRegisterWordAsync_ReturnsCorrectValue()
        {
            // Arrange
            uint register = 0x1234;
            byte lowByte = 0x42;
            byte highByte = 0x43;
            ushort expectedValue = 0x4342; // High byte first

            _mockVocomService.Setup(s => s.SendCANFrameAsync(
                    It.IsAny<VocomDevice>(),
                    It.IsAny<uint>(),
                    It.Is<byte[]>(b => b[0] == 0x01 && b[1] == 0x12 && b[2] == 0x34),
                    It.IsAny<int>()))
                .ReturnsAsync(new byte[] { lowByte });

            _mockVocomService.Setup(s => s.SendCANFrameAsync(
                    It.IsAny<VocomDevice>(),
                    It.IsAny<uint>(),
                    It.Is<byte[]>(b => b[0] == 0x01 && b[1] == 0x12 && b[2] == 0x35),
                    It.IsAny<int>()))
                .ReturnsAsync(new byte[] { highByte });

            var registerAccess = new CANRegisterAccess(_mockLogger.Object, _mockVocomService.Object);

            // Act
            ushort result = await registerAccess.ReadRegisterWordAsync(register);

            // Assert
            Assert.That(result, Is.EqualTo(expectedValue));
        }

        [Test]
        public async Task SPIRegisterAccess_ReadRegisterByteAsync_ReturnsCorrectValue()
        {
            // Arrange
            uint register = 0x1234;
            byte expectedValue = 0x42;

            _mockVocomService.Setup(s => s.SendSPIDataAsync(
                    It.IsAny<VocomDevice>(),
                    It.IsAny<byte[]>(),
                    It.IsAny<int>()))
                .ReturnsAsync(new byte[] { expectedValue });

            var registerAccess = new SPIRegisterAccess(_mockLogger.Object, _mockVocomService.Object);

            // Act
            byte result = await registerAccess.ReadRegisterByteAsync(register);

            // Assert
            Assert.That(result, Is.EqualTo(expectedValue));
        }

        [Test]
        public async Task SPIRegisterAccess_WriteRegisterByteAsync_ReturnsTrue()
        {
            // Arrange
            uint register = 0x1234;
            byte value = 0x42;

            _mockVocomService.Setup(s => s.SendSPIDataAsync(
                    It.IsAny<VocomDevice>(),
                    It.IsAny<byte[]>(),
                    It.IsAny<int>()))
                .ReturnsAsync(new byte[] { 0x00 }); // 0x00 indicates success

            var registerAccess = new SPIRegisterAccess(_mockLogger.Object, _mockVocomService.Object);

            // Act
            bool result = await registerAccess.WriteRegisterByteAsync(register, value);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task SCIRegisterAccess_ReadRegisterByteAsync_ReturnsCorrectValue()
        {
            // Arrange
            uint register = 0x1234;
            byte expectedValue = 0x42;

            _mockVocomService.Setup(s => s.SendSCIDataAsync(
                    It.IsAny<VocomDevice>(),
                    It.IsAny<byte[]>(),
                    It.IsAny<int>()))
                .ReturnsAsync(new byte[] { expectedValue });

            var registerAccess = new SCIRegisterAccess(_mockLogger.Object, _mockVocomService.Object);

            // Act
            byte result = await registerAccess.ReadRegisterByteAsync(register);

            // Assert
            Assert.That(result, Is.EqualTo(expectedValue));
        }

        [Test]
        public async Task SCIRegisterAccess_WriteRegisterByteAsync_ReturnsTrue()
        {
            // Arrange
            uint register = 0x1234;
            byte value = 0x42;

            _mockVocomService.Setup(s => s.SendSCIDataAsync(
                    It.IsAny<VocomDevice>(),
                    It.IsAny<byte[]>(),
                    It.IsAny<int>()))
                .ReturnsAsync(new byte[] { 0x00 }); // 0x00 indicates success

            var registerAccess = new SCIRegisterAccess(_mockLogger.Object, _mockVocomService.Object);

            // Act
            bool result = await registerAccess.WriteRegisterByteAsync(register, value);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task IICRegisterAccess_ReadRegisterByteAsync_ReturnsCorrectValue()
        {
            // Arrange
            uint register = 0x1234;
            byte expectedValue = 0x42;

            _mockVocomService.Setup(s => s.SendIICDataAsync(
                    It.IsAny<VocomDevice>(),
                    It.IsAny<byte>(),
                    It.IsAny<byte[]>(),
                    It.IsAny<int>()))
                .ReturnsAsync(new byte[] { expectedValue });

            var registerAccess = new IICRegisterAccess(_mockLogger.Object, _mockVocomService.Object);

            // Act
            byte result = await registerAccess.ReadRegisterByteAsync(register);

            // Assert
            Assert.That(result, Is.EqualTo(expectedValue));
        }

        [Test]
        public async Task IICRegisterAccess_WriteRegisterByteAsync_ReturnsTrue()
        {
            // Arrange
            uint register = 0x1234;
            byte value = 0x42;

            _mockVocomService.Setup(s => s.SendIICDataAsync(
                    It.IsAny<VocomDevice>(),
                    It.IsAny<byte>(),
                    It.IsAny<byte[]>(),
                    It.IsAny<int>()))
                .ReturnsAsync(new byte[] { 0x00 }); // 0x00 indicates success

            var registerAccess = new IICRegisterAccess(_mockLogger.Object, _mockVocomService.Object);

            // Act
            bool result = await registerAccess.WriteRegisterByteAsync(register, value);

            // Assert
            Assert.That(result, Is.True);
        }
    }
}

