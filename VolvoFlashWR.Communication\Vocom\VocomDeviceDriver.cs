using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Implementation of the Vocom device driver
    /// </summary>
    public class VocomDeviceDriver : IVocomDeviceDriver
    {
        private readonly ILoggingService _logger;
        private readonly VocomNativeInterop _nativeInterop;
        private bool _isInitialized = false;

        /// <summary>
        /// Initializes a new instance of the VocomDeviceDriver class
        /// </summary>
        /// <param name="logger">The logging service</param>
        public VocomDeviceDriver(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _nativeInterop = new VocomNativeInterop(logger);
        }

        /// <summary>
        /// Initializes the Vocom device driver
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing Vocom device driver", "VocomDeviceDriver");

                // Initialize the native interop layer
                bool nativeInitialized = await _nativeInterop.InitializeAsync();
                if (!nativeInitialized)
                {
                    _logger.LogError("Failed to initialize Vocom native interop layer", "VocomDeviceDriver");
                    return false;
                }

                _isInitialized = true;
                _logger.LogInformation("Vocom device driver initialized successfully", "VocomDeviceDriver");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to initialize Vocom device driver", "VocomDeviceDriver", ex);
                return false;
            }
        }

        /// <summary>
        /// Detects available Vocom devices
        /// </summary>
        /// <returns>List of available Vocom devices</returns>
        public async Task<List<VocomDevice>> DetectDevicesAsync()
        {
            try
            {
                _logger.LogInformation("Detecting Vocom devices", "VocomDeviceDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom device driver not initialized", "VocomDeviceDriver");
                    return new List<VocomDevice>();
                }

                // Use the native interop layer to detect devices
                VocomDevice[] detectedDevices = await _nativeInterop.DetectDevicesAsync();
                List<VocomDevice> devices = new List<VocomDevice>(detectedDevices);

                _logger.LogInformation($"Detected {devices.Count} Vocom devices", "VocomDeviceDriver");
                return devices;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error detecting Vocom devices", "VocomDeviceDriver", ex);
                return new List<VocomDevice>();
            }
        }

        /// <summary>
        /// Connects to a Vocom device
        /// </summary>
        /// <param name="device">The device to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        public async Task<bool> ConnectToDeviceAsync(VocomDevice device)
        {
            try
            {
                _logger.LogInformation($"Connecting to Vocom device {device?.Name}", "VocomDeviceDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom device driver not initialized", "VocomDeviceDriver");
                    return false;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot connect to null device", "VocomDeviceDriver");
                    return false;
                }

                // Use the native interop layer to connect to the device
                bool connected = await _nativeInterop.ConnectDeviceAsync(device);
                if (connected)
                {
                    // Update device status
                    device.ConnectionStatus = VocomConnectionStatus.Connected;
                    _logger.LogInformation($"Connected to Vocom device {device.Name}", "VocomDeviceDriver");
                }
                else
                {
                    _logger.LogError($"Failed to connect to Vocom device {device.Name}", "VocomDeviceDriver");
                }

                return connected;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error connecting to Vocom device {device?.Name}", "VocomDeviceDriver", ex);
                return false;
            }
        }

        /// <summary>
        /// Disconnects from a Vocom device
        /// </summary>
        /// <param name="device">The device to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public async Task<bool> DisconnectFromDeviceAsync(VocomDevice device)
        {
            try
            {
                _logger.LogInformation($"Disconnecting from Vocom device {device?.Name}", "VocomDeviceDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom device driver not initialized", "VocomDeviceDriver");
                    return false;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot disconnect from null device", "VocomDeviceDriver");
                    return false;
                }

                // Use the native interop layer to disconnect from the device
                bool disconnected = await _nativeInterop.DisconnectDeviceAsync(device);
                if (disconnected)
                {
                    // Update device status
                    device.ConnectionStatus = VocomConnectionStatus.Disconnected;
                    _logger.LogInformation($"Disconnected from Vocom device {device.Name}", "VocomDeviceDriver");
                }
                else
                {
                    _logger.LogError($"Failed to disconnect from Vocom device {device.Name}", "VocomDeviceDriver");
                }

                return disconnected;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error disconnecting from Vocom device {device?.Name}", "VocomDeviceDriver", ex);
                return false;
            }
        }

        /// <summary>
        /// Updates the firmware of a Vocom device
        /// </summary>
        /// <param name="device">The device to update</param>
        /// <param name="firmwareData">The firmware data</param>
        /// <returns>True if update is successful, false otherwise</returns>
        public async Task<bool> UpdateFirmwareAsync(VocomDevice device, byte[] firmwareData)
        {
            try
            {
                _logger.LogInformation($"Updating firmware for Vocom device {device?.Name}", "VocomDeviceDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom device driver not initialized", "VocomDeviceDriver");
                    return false;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot update firmware for null device", "VocomDeviceDriver");
                    return false;
                }

                if (firmwareData == null || firmwareData.Length == 0)
                {
                    _logger.LogError("Firmware data is null or empty", "VocomDeviceDriver");
                    return false;
                }

                // In a real implementation, this would update the firmware
                // For now, we'll just simulate this
                await Task.Delay(1000);

                _logger.LogInformation($"Updated firmware for Vocom device {device.Name}", "VocomDeviceDriver");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error updating firmware for Vocom device {device?.Name}", "VocomDeviceDriver", ex);
                return false;
            }
        }

        /// <summary>
        /// Sends a CAN frame to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendCANFrameAsync(VocomDevice device, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending CAN frame to Vocom device {device?.Name}", "VocomDeviceDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom device driver not initialized", "VocomDeviceDriver");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send CAN frame to null device", "VocomDeviceDriver");
                    return null;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("CAN frame data is null or empty", "VocomDeviceDriver");
                    return null;
                }

                // Use the native interop layer to send the CAN frame
                byte[] response = await _nativeInterop.SendCANFrameAsync(device, data, responseLength, timeout);

                if (response != null)
                {
                    _logger.LogInformation($"Sent CAN frame to Vocom device {device.Name}", "VocomDeviceDriver");
                }
                else
                {
                    _logger.LogError($"Failed to send CAN frame to Vocom device {device.Name}", "VocomDeviceDriver");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending CAN frame to Vocom device {device?.Name}", "VocomDeviceDriver", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends an SPI command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="command">The command byte</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendSPICommandAsync(VocomDevice device, byte command, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending SPI command to Vocom device {device?.Name}", "VocomDeviceDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom device driver not initialized", "VocomDeviceDriver");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send SPI command to null device", "VocomDeviceDriver");
                    return null;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("SPI command data is null or empty", "VocomDeviceDriver");
                    return null;
                }

                // Use the native interop layer to send the SPI command
                byte[] response = await _nativeInterop.SendSPICommandAsync(device, command, data, responseLength, timeout);

                if (response != null)
                {
                    _logger.LogInformation($"Sent SPI command to Vocom device {device.Name}", "VocomDeviceDriver");
                }
                else
                {
                    _logger.LogError($"Failed to send SPI command to Vocom device {device.Name}", "VocomDeviceDriver");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending SPI command to Vocom device {device?.Name}", "VocomDeviceDriver", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends an SCI command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="command">The command byte</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendSCICommandAsync(VocomDevice device, byte command, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending SCI command to Vocom device {device?.Name}", "VocomDeviceDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom device driver not initialized", "VocomDeviceDriver");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send SCI command to null device", "VocomDeviceDriver");
                    return null;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("SCI command data is null or empty", "VocomDeviceDriver");
                    return null;
                }

                // Use the native interop layer to send the SCI command
                byte[] response = await _nativeInterop.SendSCICommandAsync(device, command, data, responseLength, timeout);

                if (response != null)
                {
                    _logger.LogInformation($"Sent SCI command to Vocom device {device.Name}", "VocomDeviceDriver");
                }
                else
                {
                    _logger.LogError($"Failed to send SCI command to Vocom device {device.Name}", "VocomDeviceDriver");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending SCI command to Vocom device {device?.Name}", "VocomDeviceDriver", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends an IIC command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="address">The device address</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendIICCommandAsync(VocomDevice device, byte address, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending IIC command to Vocom device {device?.Name}", "VocomDeviceDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom device driver not initialized", "VocomDeviceDriver");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send IIC command to null device", "VocomDeviceDriver");
                    return null;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("IIC command data is null or empty", "VocomDeviceDriver");
                    return null;
                }

                // Use the native interop layer to send the IIC command
                byte[] response = await _nativeInterop.SendIICCommandAsync(device, address, data, responseLength, timeout);

                if (response != null)
                {
                    _logger.LogInformation($"Sent IIC command to Vocom device {device.Name}", "VocomDeviceDriver");
                }
                else
                {
                    _logger.LogError($"Failed to send IIC command to Vocom device {device.Name}", "VocomDeviceDriver");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending IIC command to Vocom device {device?.Name}", "VocomDeviceDriver", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends raw data to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendRawDataAsync(VocomDevice device, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending raw data to Vocom device {device?.Name}", "VocomDeviceDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom device driver not initialized", "VocomDeviceDriver");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send raw data to null device", "VocomDeviceDriver");
                    return null;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("Raw data is null or empty", "VocomDeviceDriver");
                    return null;
                }

                // Determine the protocol based on the first byte
                byte protocolByte = data.Length > 0 ? data[0] : (byte)0;
                byte[] response;

                // Check the protocol identifier in the first byte
                switch (protocolByte & 0xF0)
                {
                    case 0x10: // CAN protocol
                        _logger.LogInformation("Detected CAN protocol in raw data", "VocomDeviceDriver");
                        response = await SendCANFrameAsync(device, data, responseLength, timeout);
                        break;

                    case 0x20: // SPI protocol
                        _logger.LogInformation("Detected SPI protocol in raw data", "VocomDeviceDriver");
                        byte spiCommand = data.Length > 1 ? data[1] : (byte)0;
                        byte[] spiData = data.Length > 2 ? data.Skip(2).ToArray() : Array.Empty<byte>();
                        response = await SendSPICommandAsync(device, spiCommand, spiData, responseLength, timeout);
                        break;

                    case 0x30: // SCI protocol
                        _logger.LogInformation("Detected SCI protocol in raw data", "VocomDeviceDriver");
                        byte sciCommand = data.Length > 1 ? data[1] : (byte)0;
                        byte[] sciData = data.Length > 2 ? data.Skip(2).ToArray() : Array.Empty<byte>();
                        response = await SendSCICommandAsync(device, sciCommand, sciData, responseLength, timeout);
                        break;

                    case 0x40: // IIC protocol
                        _logger.LogInformation("Detected IIC protocol in raw data", "VocomDeviceDriver");
                        byte iicAddress = data.Length > 1 ? data[1] : (byte)0;
                        byte[] iicData = data.Length > 2 ? data.Skip(2).ToArray() : Array.Empty<byte>();
                        response = await SendIICCommandAsync(device, iicAddress, iicData, responseLength, timeout);
                        break;

                    default: // Unknown protocol, use CAN as default
                        _logger.LogWarning($"Unknown protocol identifier in raw data: 0x{protocolByte:X2}, using CAN as default", "VocomDeviceDriver");
                        response = await SendCANFrameAsync(device, data, responseLength, timeout);
                        break;
                }

                if (response != null)
                {
                    _logger.LogInformation($"Sent raw data to Vocom device {device.Name}", "VocomDeviceDriver");
                }
                else
                {
                    _logger.LogError($"Failed to send raw data to Vocom device {device.Name}", "VocomDeviceDriver");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending raw data to Vocom device {device?.Name}", "VocomDeviceDriver", ex);
                return null;
            }
        }

        /// <summary>
        /// Checks if PTT application is running
        /// </summary>
        /// <returns>True if PTT is running, false otherwise</returns>
        public async Task<bool> IsPTTRunningAsync()
        {
            try
            {
                _logger.LogInformation("Checking if PTT application is running", "VocomDeviceDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom device driver not initialized", "VocomDeviceDriver");
                    return false;
                }

                // Use the native interop layer to check if PTT is running
                bool isRunning = await _nativeInterop.IsPTTRunningAsync();

                _logger.LogInformation($"PTT application is {(isRunning ? "running" : "not running")}", "VocomDeviceDriver");
                return isRunning;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error checking if PTT application is running", "VocomDeviceDriver", ex);
                return false;
            }
        }

        /// <summary>
        /// Terminates the PTT application if it is running
        /// </summary>
        /// <returns>True if termination is successful, false otherwise</returns>
        public async Task<bool> TerminatePTTAsync()
        {
            try
            {
                _logger.LogInformation("Terminating PTT application", "VocomDeviceDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom device driver not initialized", "VocomDeviceDriver");
                    return false;
                }

                // Check if PTT is running
                bool isRunning = await IsPTTRunningAsync();
                if (!isRunning)
                {
                    _logger.LogInformation("PTT application is not running, no need to terminate", "VocomDeviceDriver");
                    return true;
                }

                // Use the native interop layer to disconnect PTT
                bool terminated = await _nativeInterop.DisconnectPTTAsync();

                if (terminated)
                {
                    _logger.LogInformation("PTT application terminated successfully", "VocomDeviceDriver");
                }
                else
                {
                    _logger.LogError("Failed to terminate PTT application", "VocomDeviceDriver");
                }

                return terminated;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error terminating PTT application", "VocomDeviceDriver", ex);
                return false;
            }
        }

        /// <summary>
        /// Disconnects the PTT application
        /// </summary>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public async Task<bool> DisconnectPTTAsync()
        {
            try
            {
                _logger.LogInformation("Disconnecting PTT application", "VocomDeviceDriver");

                if (!_isInitialized)
                {
                    _logger.LogError("Vocom device driver not initialized", "VocomDeviceDriver");
                    return false;
                }

                // Check if PTT is running
                bool isRunning = await IsPTTRunningAsync();
                if (!isRunning)
                {
                    _logger.LogInformation("PTT application is not running, no need to disconnect", "VocomDeviceDriver");
                    return true;
                }

                // Use the native interop layer to disconnect PTT
                bool disconnected = await _nativeInterop.DisconnectPTTAsync();

                if (disconnected)
                {
                    _logger.LogInformation("PTT application disconnected successfully", "VocomDeviceDriver");
                }
                else
                {
                    _logger.LogError("Failed to disconnect PTT application", "VocomDeviceDriver");
                }

                return disconnected;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error disconnecting PTT application", "VocomDeviceDriver", ex);
                return false;
            }
        }

        /// <summary>
        /// Disposes resources used by the driver
        /// </summary>
        /// <returns>True if disposal is successful, false otherwise</returns>
        public async Task<bool> DisposeAsync()
        {
            try
            {
                _logger.LogInformation("Disposing Vocom device driver resources", "VocomDeviceDriver");

                if (!_isInitialized)
                {
                    _logger.LogInformation("Vocom device driver not initialized, nothing to dispose", "VocomDeviceDriver");
                    return true;
                }

                // Shutdown the native interop layer
                bool shutdown = await _nativeInterop.ShutdownAsync();
                if (shutdown)
                {
                    _isInitialized = false;
                    _logger.LogInformation("Vocom device driver resources disposed successfully", "VocomDeviceDriver");
                }
                else
                {
                    _logger.LogError("Failed to dispose Vocom device driver resources", "VocomDeviceDriver");
                }

                return shutdown;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error disposing Vocom device driver resources", "VocomDeviceDriver", ex);
                return false;
            }
        }
    }
}
