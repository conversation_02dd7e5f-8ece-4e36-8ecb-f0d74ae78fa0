using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Services;

namespace VolvoFlashWR.Core.Tests.Services
{
    [TestFixture]
    public class LoggingServiceTests
    {
        private LoggingService _loggingService;
        private string _testLogDirectory;

        [SetUp]
        public void Setup()
        {
            // Create a test log directory
            _testLogDirectory = Path.Combine(Path.GetTempPath(), "VolvoFlashWR_Tests", "Logs", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testLogDirectory);

            // Create the logging service
            _loggingService = new LoggingService();
        }

        [TearDown]
        public void TearDown()
        {
            // Clean up the test directory
            if (Directory.Exists(_testLogDirectory))
            {
                Directory.Delete(_testLogDirectory, true);
            }
        }

        [Test]
        public async Task InitializeAsync_ValidParameters_ReturnsTrue()
        {
            // Act
            bool result = await _loggingService.InitializeAsync(_testLogDirectory, true);

            // Assert
            Assert.That(result, Is.True);
            Assert.That(_loggingService.LogFilePath, Is.EqualTo(_testLogDirectory));
            Assert.That(_loggingService.DetailedLoggingEnabled, Is.True);
        }

        [Test]
        public async Task LogInformation_ValidMessage_AddsLogEntry()
        {
            // Arrange
            await _loggingService.InitializeAsync(_testLogDirectory, true);
            string testMessage = "Test information message";
            string testSource = "TestSource";

            // Act
            _loggingService.LogInformation(testMessage, testSource);

            // Assert
            var logEntries = await _loggingService.GetAllLogEntriesAsync();
            Assert.That(logEntries, Has.Count.EqualTo(2)); // 1 for initialization + 1 for our test message
            Assert.That(logEntries[1].Message, Is.EqualTo(testMessage));
            Assert.That(logEntries[1].Source, Is.EqualTo(testSource));
            Assert.That(logEntries[1].Level, Is.EqualTo(LogLevel.Information));
        }

        [Test]
        public async Task LogWarning_ValidMessage_AddsLogEntry()
        {
            // Arrange
            await _loggingService.InitializeAsync(_testLogDirectory, true);
            string testMessage = "Test warning message";
            string testSource = "TestSource";

            // Act
            _loggingService.LogWarning(testMessage, testSource);

            // Assert
            var logEntries = await _loggingService.GetAllLogEntriesAsync();
            Assert.That(logEntries, Has.Count.EqualTo(2)); // 1 for initialization + 1 for our test message
            Assert.That(logEntries[1].Message, Is.EqualTo(testMessage));
            Assert.That(logEntries[1].Source, Is.EqualTo(testSource));
            Assert.That(logEntries[1].Level, Is.EqualTo(LogLevel.Warning));
        }

        [Test]
        public async Task LogError_ValidMessage_AddsLogEntry()
        {
            // Arrange
            await _loggingService.InitializeAsync(_testLogDirectory, true);
            string testMessage = "Test error message";
            string testSource = "TestSource";
            var testException = new Exception("Test exception");

            // Act
            _loggingService.LogError(testMessage, testSource, testException);

            // Assert
            var logEntries = await _loggingService.GetAllLogEntriesAsync();
            Assert.That(logEntries, Has.Count.EqualTo(2)); // 1 for initialization + 1 for our test message
            Assert.That(logEntries[1].Message, Does.Contain(testMessage));
            Assert.That(logEntries[1].Message, Does.Contain("Test exception"));
            Assert.That(logEntries[1].Source, Is.EqualTo(testSource));
            Assert.That(logEntries[1].Level, Is.EqualTo(LogLevel.Error));
        }

        [Test]
        public async Task LogDebug_DetailedLoggingEnabled_AddsLogEntry()
        {
            // Arrange
            await _loggingService.InitializeAsync(_testLogDirectory, true);
            string testMessage = "Test debug message";
            string testSource = "TestSource";

            // Act
            _loggingService.LogDebug(testMessage, testSource);

            // Assert
            var logEntries = await _loggingService.GetAllLogEntriesAsync();
            Assert.That(logEntries, Has.Count.EqualTo(2)); // 1 for initialization + 1 for our test message
            Assert.That(logEntries[1].Message, Is.EqualTo(testMessage));
            Assert.That(logEntries[1].Source, Is.EqualTo(testSource));
            Assert.That(logEntries[1].Level, Is.EqualTo(LogLevel.Debug));
        }

        [Test]
        public async Task LogDebug_DetailedLoggingDisabled_DoesNotAddLogEntry()
        {
            // Arrange
            await _loggingService.InitializeAsync(_testLogDirectory, false);
            string testMessage = "Test debug message";
            string testSource = "TestSource";

            // Act
            _loggingService.LogDebug(testMessage, testSource);

            // Assert
            var logEntries = await _loggingService.GetAllLogEntriesAsync();
            Assert.That(logEntries, Has.Count.EqualTo(1)); // Only the initialization message
        }

        [Test]
        public async Task GetLogEntriesByLevelAsync_ValidLevel_ReturnsFilteredEntries()
        {
            // Arrange
            await _loggingService.InitializeAsync(_testLogDirectory, true);
            _loggingService.LogInformation("Info message 1", "TestSource");
            _loggingService.LogInformation("Info message 2", "TestSource");
            _loggingService.LogWarning("Warning message", "TestSource");
            _loggingService.LogError("Error message", "TestSource");
            _loggingService.LogDebug("Debug message", "TestSource");

            // Act
            var infoEntries = await _loggingService.GetLogEntriesByLevelAsync(LogLevel.Information);
            var warningEntries = await _loggingService.GetLogEntriesByLevelAsync(LogLevel.Warning);
            var errorEntries = await _loggingService.GetLogEntriesByLevelAsync(LogLevel.Error);
            var debugEntries = await _loggingService.GetLogEntriesByLevelAsync(LogLevel.Debug);

            // Assert
            Assert.That(infoEntries, Has.Count.EqualTo(3)); // 1 for initialization + 2 for our test messages
            Assert.That(warningEntries, Has.Count.EqualTo(1));
            Assert.That(errorEntries, Has.Count.EqualTo(1));
            Assert.That(debugEntries, Has.Count.EqualTo(1));
        }

        [Test]
        public async Task ClearLogEntriesAsync_ValidCall_ClearsAllEntries()
        {
            // Arrange
            await _loggingService.InitializeAsync(_testLogDirectory, true);
            _loggingService.LogInformation("Info message", "TestSource");
            _loggingService.LogWarning("Warning message", "TestSource");

            var entriesBeforeClear = await _loggingService.GetAllLogEntriesAsync();
            Assert.That(entriesBeforeClear, Has.Count.EqualTo(3)); // 1 for initialization + 2 for our test messages

            // Act
            bool result = await _loggingService.ClearLogEntriesAsync();

            // Assert
            Assert.That(result, Is.True);
            var entriesAfterClear = await _loggingService.GetAllLogEntriesAsync();
            Assert.That(entriesAfterClear, Has.Count.EqualTo(0));
        }
    }
}

