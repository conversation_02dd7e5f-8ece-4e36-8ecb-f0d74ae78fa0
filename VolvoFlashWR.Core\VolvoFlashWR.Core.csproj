﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PlatformTarget>x64</PlatformTarget>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <SelfContained>false</SelfContained>
  </PropertyGroup>

  <ItemGroup>
    <!-- Core Logging -->
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />

    <!-- System Management -->
    <PackageReference Include="System.Management" Version="9.0.5" />

    <!-- Configuration -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />

    <!-- Dependency Injection -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />

    <!-- JSON Processing -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />

    <!-- Memory and Performance -->
    <PackageReference Include="System.Memory" Version="4.5.5" />
    <PackageReference Include="System.Buffers" Version="4.5.1" />

    <!-- Threading and Tasks -->
    <PackageReference Include="System.Threading.Tasks.Extensions" Version="4.5.4" />

    <!-- Collections -->
    <PackageReference Include="System.Collections.Immutable" Version="8.0.0" />
  </ItemGroup>

</Project>
