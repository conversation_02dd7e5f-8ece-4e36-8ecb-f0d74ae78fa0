@echo off
title VolvoFlashWR - Remove Unnecessary Libraries
color 0E

echo ===============================================================================
echo                    Remove Unnecessary Libraries
echo ===============================================================================
echo.
echo This script will remove unnecessary System libraries and other non-essential
echo libraries to streamline the application for Phoenix APCI real hardware mode.
echo.
echo IMPORTANT: This will move non-essential libraries to a backup folder.
echo           Essential libraries for real hardware communication will be kept.
echo.
echo ===============================================================================

pause

REM Create backup directory
if not exist "Libraries_Backup" mkdir Libraries_Backup
if not exist "Libraries_Backup\System" mkdir Libraries_Backup\System
if not exist "Libraries_Backup\NonEssential" mkdir Libraries_Backup\NonEssential

echo === Moving Non-Essential System Libraries to Backup ===

REM Keep only essential System libraries (these are required for .NET Core functionality)
set ESSENTIAL_SYSTEM_LIBS=System.Memory.dll System.Runtime.CompilerServices.Unsafe.dll System.Threading.Tasks.Extensions.dll System.ValueTuple.dll System.Numerics.Vectors.dll

REM Move all System libraries to backup first
move "Libraries\System.*.dll" "Libraries_Backup\System\" >nul 2>&1

REM Copy back only the essential ones
copy "Libraries_Backup\System\System.Memory.dll" "Libraries\" >nul 2>&1
copy "Libraries_Backup\System\System.Runtime.CompilerServices.Unsafe.dll" "Libraries\" >nul 2>&1
copy "Libraries_Backup\System\System.Threading.Tasks.Extensions.dll" "Libraries\" >nul 2>&1
copy "Libraries_Backup\System\System.ValueTuple.dll" "Libraries\" >nul 2>&1
copy "Libraries_Backup\System\System.Numerics.Vectors.dll" "Libraries\" >nul 2>&1

echo   ✓ Moved non-essential System libraries to backup
echo   ✓ Kept 5 essential System libraries

echo.
echo === Moving Non-Essential Libraries to Backup ===

REM Move non-essential libraries that are not needed for Phoenix APCI
move "Libraries\System.Collections.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.ComponentModel.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Console.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Data.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Diagnostics.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Drawing.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Dynamic.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Globalization.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.IO.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Linq.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Net.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.ObjectModel.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Reflection.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Resources.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Runtime.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Security.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Text.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Threading.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Xml.*.dll" "Libraries_Backup\NonEssential\" >nul 2>&1

echo   ✓ Moved detailed System libraries to backup

REM Move other non-essential libraries
move "Libraries\System.Memory.xml" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Numerics.Vectors.xml" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Runtime.CompilerServices.Unsafe.xml" "Libraries_Backup\NonEssential\" >nul 2>&1
move "Libraries\System.Threading.Tasks.Extensions.xml" "Libraries_Backup\NonEssential\" >nul 2>&1

echo   ✓ Moved XML documentation files to backup

echo.
echo === Verifying Essential Libraries Remain ===

set ESSENTIAL_COUNT=0

REM Check core APCI libraries
if exist "Libraries\apci.dll" (echo   ✓ apci.dll & set /a ESSENTIAL_COUNT+=1)
if exist "Libraries\apcidb.dll" (echo   ✓ apcidb.dll & set /a ESSENTIAL_COUNT+=1)
if exist "Libraries\Rpci.dll" (echo   ✓ Rpci.dll & set /a ESSENTIAL_COUNT+=1)
if exist "Libraries\Pc2.dll" (echo   ✓ Pc2.dll & set /a ESSENTIAL_COUNT+=1)

REM Check Vocom driver
if exist "Libraries\WUDFPuma.dll" (echo   ✓ WUDFPuma.dll & set /a ESSENTIAL_COUNT+=1)

REM Check Phoenix libraries
if exist "Libraries\PhoenixESW.dll" (echo   ✓ PhoenixESW.dll & set /a ESSENTIAL_COUNT+=1)
if exist "Libraries\PhoenixGeneral.dll" (echo   ✓ PhoenixGeneral.dll & set /a ESSENTIAL_COUNT+=1)

REM Check Volvo libraries
if exist "Libraries\Volvo.ApciPlus.dll" (echo   ✓ Volvo.ApciPlus.dll & set /a ESSENTIAL_COUNT+=1)
if exist "Libraries\Volvo.NVS.Core.dll" (echo   ✓ Volvo.NVS.Core.dll & set /a ESSENTIAL_COUNT+=1)

REM Check Vodia libraries
if exist "Libraries\Vodia.CommonDomain.Model.dll" (echo   ✓ Vodia.CommonDomain.Model.dll & set /a ESSENTIAL_COUNT+=1)

REM Check essential dependencies
if exist "Libraries\log4net.dll" (echo   ✓ log4net.dll & set /a ESSENTIAL_COUNT+=1)
if exist "Libraries\Newtonsoft.Json.dll" (echo   ✓ Newtonsoft.Json.dll & set /a ESSENTIAL_COUNT+=1)

echo.
echo ===============================================================================
echo                           Cleanup Summary
echo ===============================================================================
echo.
echo Essential libraries verified: %ESSENTIAL_COUNT%
echo.
echo ✓ Removed non-essential System libraries (moved to backup)
echo ✓ Kept 5 essential System libraries for .NET Core functionality
echo ✓ Preserved all Phoenix APCI and Vocom libraries
echo ✓ Preserved all Volvo-specific communication libraries
echo ✓ Preserved essential dependencies
echo.
echo The application now has a streamlined library structure optimized for
echo Phoenix APCI real hardware communication with Vocom 1 adapters.
echo.
echo Backup location: Libraries_Backup\
echo   - Libraries_Backup\System\ (non-essential System libraries)
echo   - Libraries_Backup\NonEssential\ (other non-essential libraries)
echo.
echo ===============================================================================
pause
