using System;
using System.Collections.Generic;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Tests.Models
{
    [TestFixture]
    public class ECUDeviceTests
    {
        [Test]
        public void ECUDevice_DefaultValues_AreCorrect()
        {
            // Arrange & Act
            var ecuDevice = new ECUDevice();

            // Assert
            // The ID is generated when needed, not in the constructor
            Assert.That(ecuDevice.ActiveFaults, Is.Not.Null);
            Assert.That(ecuDevice.InactiveFaults, Is.Not.Null);
            Assert.That(ecuDevice.Parameters, Is.Not.Null);
        }

        [Test]
        public void ECUDevice_CustomValues_AreCorrect()
        {
            // Arrange
            string id = "ECU123";
            string name = "TestECU";
            string serialNumber = "SN12345";
            string hardwareVersion = "HW1.0";
            string softwareVersion = "SW2.0";
            ECUConnectionStatus connectionStatus = ECUConnectionStatus.Connected;
            string microcontrollerType = "MC9S12XEP100";
            int eepromSize = 4096;
            int flashSize = 65536;
            int ramSize = 8192;
            bool supportsHighSpeedCommunication = true;
            bool supportsLowSpeedCommunication = false;
            ECUProtocolType protocolType = ECUProtocolType.CAN;
            var activeFaults = new List<ECUFault> { new ECUFault { Code = "P0123", Description = "Test Fault" } };
            var inactiveFaults = new List<ECUFault> { new ECUFault { Code = "P0456", Description = "Inactive Fault" } };
            var parameters = new Dictionary<string, object> { { "TestParam", "Value" } };
            DateTime lastCommunicationTime = DateTime.Now;

            // Act
            var ecuDevice = new ECUDevice
            {
                Id = id,
                Name = name,
                SerialNumber = serialNumber,
                HardwareVersion = hardwareVersion,
                SoftwareVersion = softwareVersion,
                ConnectionStatus = connectionStatus,
                MicrocontrollerType = microcontrollerType,
                EEPROMSize = eepromSize,
                FlashSize = flashSize,
                RAMSize = ramSize,
                SupportsHighSpeedCommunication = supportsHighSpeedCommunication,
                SupportsLowSpeedCommunication = supportsLowSpeedCommunication,
                ProtocolType = protocolType,
                ActiveFaults = activeFaults,
                InactiveFaults = inactiveFaults,
                Parameters = parameters,
                LastCommunicationTime = lastCommunicationTime
            };

            // Assert
            Assert.That(ecuDevice.Id, Is.EqualTo(id));
            Assert.That(ecuDevice.Name, Is.EqualTo(name));
            Assert.That(ecuDevice.SerialNumber, Is.EqualTo(serialNumber));
            Assert.That(ecuDevice.HardwareVersion, Is.EqualTo(hardwareVersion));
            Assert.That(ecuDevice.SoftwareVersion, Is.EqualTo(softwareVersion));
            Assert.That(ecuDevice.ConnectionStatus, Is.EqualTo(connectionStatus));
            Assert.That(ecuDevice.MicrocontrollerType, Is.EqualTo(microcontrollerType));
            Assert.That(ecuDevice.EEPROMSize, Is.EqualTo(eepromSize));
            Assert.That(ecuDevice.FlashSize, Is.EqualTo(flashSize));
            Assert.That(ecuDevice.RAMSize, Is.EqualTo(ramSize));
            Assert.That(ecuDevice.SupportsHighSpeedCommunication, Is.EqualTo(supportsHighSpeedCommunication));
            Assert.That(ecuDevice.SupportsLowSpeedCommunication, Is.EqualTo(supportsLowSpeedCommunication));
            Assert.That(ecuDevice.ProtocolType, Is.EqualTo(protocolType));
            Assert.That(ecuDevice.ActiveFaults, Is.EqualTo(activeFaults));
            Assert.That(ecuDevice.InactiveFaults, Is.EqualTo(inactiveFaults));
            Assert.That(ecuDevice.Parameters, Is.EqualTo(parameters));
            Assert.That(ecuDevice.LastCommunicationTime, Is.EqualTo(lastCommunicationTime));
        }
    }
}

