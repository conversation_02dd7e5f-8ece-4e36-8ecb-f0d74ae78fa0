@echo off
echo ========================================
echo VolvoFlashWR - Export for Real Hardware Testing
echo ========================================
echo.

REM Set variables
set EXPORT_DIR=VolvoFlashWR_RealHardware_Export
set PUBLISH_DIR=VolvoFlashWR.Launcher\bin\Release\net8.0-windows\publish\win-x64
set BUILD_DIR=VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64

echo [STEP 1] Cleaning previous exports...
if exist "%EXPORT_DIR%" (
    echo Removing existing export directory...
    rmdir /s /q "%EXPORT_DIR%"
)

echo [STEP 2] Building solution in Release mode...
dotnet build VolvoFlashWR.sln --configuration Release
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo [STEP 3] Publishing application...
dotnet publish VolvoFlashWR.Launcher\VolvoFlashWR.Launcher.csproj -c Release -r win-x64 --self-contained true -p:PublishSingleFile=false
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Publish failed!
    pause
    exit /b 1
)

echo [STEP 4] Creating export directory structure...
mkdir "%EXPORT_DIR%"
mkdir "%EXPORT_DIR%\Application"
mkdir "%EXPORT_DIR%\Documentation"
mkdir "%EXPORT_DIR%\Tools"

echo [STEP 5] Copying application files...
if exist "%PUBLISH_DIR%" (
    echo Copying from publish directory...
    xcopy "%PUBLISH_DIR%\*" "%EXPORT_DIR%\Application\" /E /I /Y
) else if exist "%BUILD_DIR%" (
    echo Copying from build directory...
    xcopy "%BUILD_DIR%\*" "%EXPORT_DIR%\Application\" /E /I /Y
) else (
    echo ERROR: No build output found!
    pause
    exit /b 1
)

echo [STEP 6] Copying additional tools...
if exist "VolvoFlashWR.KeyGenerator\bin\Release\net8.0\VolvoFlashWR.KeyGenerator.exe" (
    copy "VolvoFlashWR.KeyGenerator\bin\Release\net8.0\VolvoFlashWR.KeyGenerator.exe" "%EXPORT_DIR%\Tools\"
)

echo [STEP 7] Creating launcher scripts...
echo Creating Real Hardware Mode launcher...
(
echo @echo off
echo echo ========================================
echo echo VolvoFlashWR - Real Hardware Mode
echo echo ========================================
echo echo.
echo echo IMPORTANT: Make sure you have:
echo echo 1. Vocom 1 adapter connected via USB
echo echo 2. Vocom driver installed ^(CommunicationUnitInstaller-*******.msi^)
echo echo 3. PTT ^(Premium Tech Tool^) is NOT running
echo echo.
echo echo Press any key to continue or Ctrl+C to exit...
echo pause ^>nul
echo.
echo REM Set environment for real hardware
echo set USE_DUMMY_IMPLEMENTATIONS=false
echo set VERBOSE_LOGGING=true
echo set LOG_LEVEL=Debug
echo set PHOENIX_VOCOM_ENABLED=true
echo.
echo echo Starting VolvoFlashWR in Real Hardware Mode...
echo echo Environment: USE_DUMMY_IMPLEMENTATIONS=%%USE_DUMMY_IMPLEMENTATIONS%%
echo echo Environment: VERBOSE_LOGGING=%%VERBOSE_LOGGING%%
echo echo.
echo.
echo REM Verify critical libraries
echo echo Verifying critical libraries...
echo if exist "WUDFPuma.dll" ^(
echo     echo [OK] WUDFPuma.dll found
echo ^) else ^(
echo     echo [ERROR] WUDFPuma.dll not found - Install Vocom driver first!
echo     pause
echo     exit /b 1
echo ^)
echo.
echo if exist "Volvo.ApciPlus.dll" ^(
echo     echo [OK] Volvo.ApciPlus.dll found
echo ^) else ^(
echo     echo [ERROR] Volvo.ApciPlus.dll not found!
echo     pause
echo     exit /b 1
echo ^)
echo.
echo echo All libraries verified - Starting application...
echo "VolvoFlashWR.Launcher.exe" --mode=Normal
echo.
echo echo Application has exited.
echo pause
) > "%EXPORT_DIR%\Start_Real_Hardware_Mode.bat"

echo Creating Test Mode launcher...
(
echo @echo off
echo echo ========================================
echo echo VolvoFlashWR - Test Mode ^(No Hardware^)
echo echo ========================================
echo echo.
echo echo This mode runs without real hardware for testing.
echo echo.
echo echo Press any key to continue...
echo pause ^>nul
echo.
echo REM Set environment for test mode
echo set USE_DUMMY_IMPLEMENTATIONS=true
echo set VERBOSE_LOGGING=true
echo set LOG_LEVEL=Debug
echo set PHOENIX_VOCOM_ENABLED=false
echo.
echo echo Starting VolvoFlashWR in Test Mode...
echo "VolvoFlashWR.Launcher.exe" --mode=Dummy
echo.
echo echo Application has exited.
echo pause
) > "%EXPORT_DIR%\Start_Test_Mode.bat"

echo [STEP 8] Creating documentation...
(
echo # VolvoFlashWR - Real Hardware Testing Package
echo.
echo ## Package Contents
echo.
echo - **Application/**: Main application files with all required libraries
echo - **Tools/**: Additional tools ^(Key Generator^)
echo - **Documentation/**: This file and other documentation
echo.
echo ## Quick Start
echo.
echo ### For Real Hardware Testing:
echo 1. Install Vocom driver: CommunicationUnitInstaller-*******.msi
echo 2. Connect Vocom 1 adapter via USB
echo 3. Make sure PTT ^(Premium Tech Tool^) is NOT running
echo 4. Run: **Start_Real_Hardware_Mode.bat**
echo.
echo ### For Testing Without Hardware:
echo 1. Run: **Start_Test_Mode.bat**
echo.
echo ## System Requirements
echo.
echo - Windows 10/11 ^(x86 or x64^)
echo - .NET 8.0 Runtime ^(included in self-contained package^)
echo - Vocom 1 adapter ^(for real hardware mode^)
echo - Vocom driver installed ^(for real hardware mode^)
echo.
echo ## Critical Libraries Included
echo.
echo ✅ **WUDFPuma.dll** - Main Vocom 1 adapter driver
echo ✅ **Volvo.ApciPlus.dll** - APCI communication library
echo ✅ **apci.dll** - APCI core functionality
echo ✅ **All 31 required libraries** for real hardware communication
echo.
echo ## Troubleshooting
echo.
echo ### "WUDFPuma.dll not found" Error:
echo - Install Vocom driver: CommunicationUnitInstaller-*******.msi
echo - Make sure driver is properly installed
echo.
echo ### "Cannot connect to Vocom adapter" Error:
echo - Check USB connection
echo - Make sure PTT is not running
echo - Try different USB port
echo - Restart application
echo.
echo ### Application won't start:
echo - Make sure .NET 8.0 is installed
echo - Run as Administrator if needed
echo - Check Windows Event Viewer for errors
echo.
echo ## Support
echo.
echo For technical support, check the log files in the Logs/ directory.
echo.
echo ---
echo **Package created:** %DATE% %TIME%
echo **Version:** 1.0.0
echo **Target:** Real Hardware Testing
) > "%EXPORT_DIR%\Documentation\README.md"

echo [STEP 9] Verifying export package...
echo.
echo Verifying critical files in export package...

set MISSING_FILES=0

if not exist "%EXPORT_DIR%\Application\VolvoFlashWR.Launcher.exe" (
    echo ERROR: Main executable missing!
    set MISSING_FILES=1
)

if not exist "%EXPORT_DIR%\Application\WUDFPuma.dll" (
    echo ERROR: WUDFPuma.dll missing!
    set MISSING_FILES=1
)

if not exist "%EXPORT_DIR%\Application\Volvo.ApciPlus.dll" (
    echo ERROR: Volvo.ApciPlus.dll missing!
    set MISSING_FILES=1
)

if not exist "%EXPORT_DIR%\Application\apci.dll" (
    echo ERROR: apci.dll missing!
    set MISSING_FILES=1
)

if "%MISSING_FILES%"=="1" (
    echo.
    echo CRITICAL ERROR: Missing required files in export package!
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ EXPORT COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Export package created in: %EXPORT_DIR%
echo.
echo Package contents:
echo - Main application with ALL 31 required libraries
echo - Real Hardware Mode launcher
echo - Test Mode launcher
echo - Complete documentation
echo - Key Generator tool
echo.
echo Ready for real hardware testing!
echo.
echo To test:
echo 1. Copy the entire '%EXPORT_DIR%' folder to target laptop
echo 2. Install Vocom driver on target laptop
echo 3. Connect Vocom 1 adapter
echo 4. Run 'Start_Real_Hardware_Mode.bat'
echo.
echo Press any key to open export directory...
pause >nul
explorer "%EXPORT_DIR%"
