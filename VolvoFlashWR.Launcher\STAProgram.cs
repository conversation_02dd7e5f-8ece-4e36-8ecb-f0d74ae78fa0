using System;
using System.Threading;
using System.Windows;

namespace VolvoFlashWR.Launcher
{
    public static class STAProgram
    {
        [STAThread]
        public static void Main(string[] args)
        {
            try
            {
                // Ensure we're running in STA mode
                if (Thread.CurrentThread.GetApartmentState() != ApartmentState.STA)
                {
                    Console.WriteLine("Warning: Thread is not in STA mode. Attempting to set STA mode...");
                    Thread.CurrentThread.SetApartmentState(ApartmentState.STA);
                }

                // Verify STA mode
                if (Thread.CurrentThread.GetApartmentState() != ApartmentState.STA)
                {
                    throw new InvalidOperationException("The calling thread must be STA, because many UI components require this.");
                }

                Console.WriteLine("Starting VolvoFlashWR application in STA mode...");

                // Create and run the application
                var app = new VolvoFlashWR.UI.App();
                app.InitializeComponent();
                app.Run();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error starting application: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error starting application: {ex.Message}\n\nStack trace: {ex.StackTrace}", 
                    "Application Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
