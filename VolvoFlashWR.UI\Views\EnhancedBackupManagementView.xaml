<UserControl x:Class="VolvoFlashWR.UI.Views.EnhancedBackupManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:VolvoFlashWR.UI.Views"
             xmlns:viewmodels="clr-namespace:VolvoFlashWR.UI.ViewModels"
             xmlns:converters="clr-namespace:VolvoFlashWR.UI.Converters"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter" />
        <converters:BoolToSuccessConverter x:Key="BoolToSuccessConverter" />
        <converters:DateTimeToAgeConverter x:Key="DateTimeToAgeConverter" />

        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="120"/>
        </Style>

        <Style x:Key="InfoLabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,2"/>
        </Style>

        <Style x:Key="InfoValueStyle" TargetType="TextBlock">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,2"/>
        </Style>

        <Style x:Key="ProgressBarStyle" TargetType="ProgressBar">
            <Setter Property="Height" Value="15"/>
            <Setter Property="Margin" Value="5"/>
        </Style>

        <Style x:Key="CardBorderStyle" TargetType="Border">
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="5"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Background" Value="#F9F9F9"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Border Grid.Row="0" Style="{StaticResource CardBorderStyle}" Margin="5,5,5,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="Backup Management" FontSize="20" FontWeight="Bold" Margin="0,0,0,10"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="Selected ECU:" Style="{StaticResource InfoLabelStyle}"/>
                        <ComboBox Grid.Column="1" ItemsSource="{Binding ConnectedECUs}"
                                  SelectedItem="{Binding SelectedECUForBackup}"
                                  DisplayMemberPath="Name" Margin="5,2"/>

                        <TextBlock Grid.Column="2" Text="Status:" Style="{StaticResource InfoLabelStyle}" Margin="15,2,5,2"/>
                        <Border Grid.Column="3" CornerRadius="3" Padding="5,2" Margin="5,2"
                                Background="{Binding SelectedECUForBackup.ConnectionStatus, Converter={StaticResource ConnectionStatusToBrushConverter}}">
                            <TextBlock Text="{Binding SelectedECUForBackup.ConnectionStatus}" Foreground="White"/>
                        </Border>
                    </Grid>

                    <Grid Margin="0,5,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Backup Directory:" Style="{StaticResource InfoLabelStyle}"/>
                        <TextBlock Grid.Column="1" Text="{Binding BackupService.BackupDirectoryPath}" Style="{StaticResource InfoValueStyle}"/>
                    </Grid>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Vertical" Margin="10,0,0,0">
                    <Button Content="Refresh ECU List" Command="{Binding RefreshECUCommand}" Style="{StaticResource ActionButtonStyle}"/>
                    <Button Content="Connect" Command="{Binding ConnectToECUCommand}" Style="{StaticResource ActionButtonStyle}"/>
                    <Button Content="Disconnect" Command="{Binding DisconnectECUCommand}" Style="{StaticResource ActionButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="350"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Backup Creation -->
            <Border Grid.Column="0" Style="{StaticResource CardBorderStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="Create New Backup" Style="{StaticResource SectionHeaderStyle}"/>

                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Description:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBox Grid.Column="1" Text="{Binding BackupDescription}" Margin="5,2"/>
                            </Grid>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Category:" Style="{StaticResource InfoLabelStyle}"/>
                                <ComboBox Grid.Column="1" ItemsSource="{Binding BackupCategories}"
                                          SelectedItem="{Binding SelectedBackupCategory}"
                                          IsEditable="True" Margin="5,2"/>
                            </Grid>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Tags:" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBox Grid.Column="1" Text="{Binding BackupTags}"
                                         ToolTip="Enter tags separated by commas" Margin="5,2"/>
                            </Grid>

                            <GroupBox Header="Backup Content" Margin="0,10,0,0">
                                <StackPanel>
                                    <CheckBox Content="Include EEPROM" IsChecked="{Binding IncludeEEPROM}" Margin="5"/>
                                    <CheckBox Content="Include Microcontroller Code" IsChecked="{Binding IncludeMicrocontrollerCode}" Margin="5"/>
                                    <CheckBox Content="Include Parameters" IsChecked="{Binding IncludeParameters}" Margin="5"/>
                                </StackPanel>
                            </GroupBox>

                            <GroupBox Header="Backup Options" Margin="0,10,0,0">
                                <StackPanel>
                                    <CheckBox Content="Use Compression" IsChecked="{Binding UseCompression}" Margin="5"/>
                                    <CheckBox Content="Use Encryption" IsChecked="{Binding UseEncryption}" Margin="5"/>
                                    <CheckBox Content="Auto-Version" IsChecked="{Binding AutoVersion}" Margin="5"
                                              ToolTip="Automatically create a new version if a backup for this ECU already exists"/>
                                </StackPanel>
                            </GroupBox>
                        </StackPanel>
                    </ScrollViewer>

                    <StackPanel Grid.Row="2" Margin="0,10,0,0">
                        <Button Content="Create Backup" Command="{Binding CreateBackupCommand}"
                                Style="{StaticResource ActionButtonStyle}" HorizontalAlignment="Center"/>
                        <ProgressBar IsIndeterminate="{Binding IsCreatingBackup}"
                                     Visibility="{Binding IsCreatingBackup, Converter={StaticResource BoolToVisibilityConverter}}"
                                     Style="{StaticResource ProgressBarStyle}"/>
                        <TextBlock Text="{Binding StatusMessage}" TextWrapping="Wrap" Margin="5"
                                   HorizontalAlignment="Center" TextAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Right Panel - Backup Management -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TabControl Grid.Row="0">
                    <!-- Backup List Tab -->
                    <TabItem Header="Backup List">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- Filter Controls -->
                            <Grid Grid.Row="0" Margin="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="Filter:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <TextBox Grid.Column="1" Text="{Binding BackupFilter, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,5,0"/>
                                <Button Grid.Column="2" Content="Clear" Command="{Binding ClearBackupFilterCommand}" Width="60"/>
                            </Grid>

                            <!-- Backup List -->
                            <DataGrid Grid.Row="1" ItemsSource="{Binding Backups}" SelectedItem="{Binding SelectedBackup}"
                                      AutoGenerateColumns="False" IsReadOnly="True" Margin="5"
                                      AlternatingRowBackground="#F5F5F5">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="ECU" Binding="{Binding ECUName}" Width="100"/>
                                    <DataGridTextColumn Header="Date" Binding="{Binding CreationTime, StringFormat='{}{0:yyyy-MM-dd HH:mm}'}" Width="130"/>
                                    <DataGridTextColumn Header="Age" Binding="{Binding CreationTime, Converter={StaticResource DateTimeToAgeConverter}}" Width="80"/>
                                    <DataGridTextColumn Header="Version" Binding="{Binding Version}" Width="60"/>
                                    <DataGridTextColumn Header="Category" Binding="{Binding Category}" Width="100"/>
                                    <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="*"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </TabItem>

                    <!-- Backup Details Tab -->
                    <TabItem Header="Backup Details">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <StackPanel Margin="10" Visibility="{Binding IsBackupSelected, Converter={StaticResource BoolToVisibilityConverter}}">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Basic Information -->
                                    <StackPanel Grid.Column="0" Margin="0,0,5,0">
                                        <TextBlock Text="Basic Information" Style="{StaticResource SectionHeaderStyle}"/>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="ECU Name:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.ECUName}" Style="{StaticResource InfoValueStyle}"/>
                                        </Grid>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Serial Number:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.ECUSerialNumber}" Style="{StaticResource InfoValueStyle}"/>
                                        </Grid>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Hardware Version:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.ECUHardwareVersion}" Style="{StaticResource InfoValueStyle}"/>
                                        </Grid>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Software Version:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.ECUSoftwareVersion}" Style="{StaticResource InfoValueStyle}"/>
                                        </Grid>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Description:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.Description}" Style="{StaticResource InfoValueStyle}" TextWrapping="Wrap"/>
                                        </Grid>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Category:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.Category}" Style="{StaticResource InfoValueStyle}"/>
                                        </Grid>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Tags:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding SelectedBackupTags}" Style="{StaticResource InfoValueStyle}" TextWrapping="Wrap"/>
                                        </Grid>
                                    </StackPanel>

                                    <!-- Version Information -->
                                    <StackPanel Grid.Column="1" Margin="5,0,0,0">
                                        <TextBlock Text="Version Information" Style="{StaticResource SectionHeaderStyle}"/>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Version:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.Version}" Style="{StaticResource InfoValueStyle}"/>
                                        </Grid>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Creation Time:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.CreationTime}" Style="{StaticResource InfoValueStyle}"/>
                                        </Grid>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Created By:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.CreatedBy}" Style="{StaticResource InfoValueStyle}"/>
                                        </Grid>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Last Modified:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.LastModifiedTime}" Style="{StaticResource InfoValueStyle}"/>
                                        </Grid>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Modified By:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.LastModifiedBy}" Style="{StaticResource InfoValueStyle}"/>
                                        </Grid>

                                        <TextBlock Text="Content Information" Style="{StaticResource SectionHeaderStyle}" Margin="0,15,0,5"/>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Size:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding SelectedBackup.SizeInBytes, StringFormat='{}{0:N0} bytes'}" Style="{StaticResource InfoValueStyle}"/>
                                        </Grid>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Contains EEPROM:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding HasEEPROMData}" Style="{StaticResource InfoValueStyle}"/>
                                        </Grid>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Contains MCU Code:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding HasMicrocontrollerCode}" Style="{StaticResource InfoValueStyle}"/>
                                        </Grid>

                                        <Grid Margin="0,5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="Contains Parameters:" Style="{StaticResource InfoLabelStyle}"/>
                                            <TextBlock Grid.Column="1" Text="{Binding HasParameters}" Style="{StaticResource InfoValueStyle}"/>
                                        </Grid>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </ScrollViewer>
                    </TabItem>

                    <!-- Version History Tab -->
                    <TabItem Header="Version History">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Version History Controls -->
                            <Grid Grid.Row="0" Margin="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="Backup Version History" Style="{StaticResource SectionHeaderStyle}"/>
                                    <TextBlock Text="Track changes to your ECU backups over time. Compare versions to see what has changed."
                                               TextWrapping="Wrap" Margin="0,0,0,10"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0,0,0">
                                    <ComboBox ItemsSource="{Binding ViewModes}"
                                              SelectedItem="{Binding SelectedVersionViewMode}"
                                              Width="120" Margin="5,2"/>
                                </StackPanel>
                            </Grid>

                            <!-- Version Tree/Timeline View -->
                            <TabControl Grid.Row="1" Margin="5">
                                <TabItem Header="Tree View">
                                    <TreeView ItemsSource="{Binding VersionTree.VersionNodes}"
                                              SelectedItemChanged="VersionTree_SelectedItemChanged"
                                              Margin="5">
                                        <TreeView.ItemTemplate>
                                            <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="{Binding Backup.Version, StringFormat='v{0}'}" FontWeight="Bold"/>
                                                    <TextBlock Text=" - " />
                                                    <TextBlock Text="{Binding Backup.CreationTime, StringFormat='{}{0:yyyy-MM-dd HH:mm}'}" />
                                                    <TextBlock Text=" " />
                                                    <TextBlock Text="(Latest)" Foreground="Green"
                                                               Visibility="{Binding Backup.IsLatestVersion, Converter={StaticResource BoolToVisibilityConverter}}"/>
                                                    <TextBlock Text=" - " />
                                                    <TextBlock Text="{Binding Backup.Description}" Foreground="#666666" FontStyle="Italic" />
                                                </StackPanel>
                                            </HierarchicalDataTemplate>
                                        </TreeView.ItemTemplate>
                                    </TreeView>
                                </TabItem>

                                <TabItem Header="Timeline View">
                                    <Border BorderBrush="#DDDDDD" BorderThickness="1" Margin="5">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="*"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>

                                            <!-- Timeline visualization would be implemented here -->
                                            <Border Grid.Row="0" Background="#F9F9F9">
                                                <Grid>
                                                    <TextBlock Text="Version Timeline Visualization"
                                                               HorizontalAlignment="Center" VerticalAlignment="Center"
                                                               FontStyle="Italic" Foreground="#888888"/>
                                                </Grid>
                                            </Border>

                                            <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="5">
                                                <TextBlock Text="Zoom:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                                <Slider Width="150" Minimum="1" Maximum="10" Value="{Binding TimelineZoom}"
                                                        TickFrequency="1" IsSnapToTickEnabled="True" Margin="5,0"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </TabItem>

                                <TabItem Header="Comparison">
                                    <Grid Margin="5">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="*"/>
                                        </Grid.RowDefinitions>

                                        <Grid Grid.Row="0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <ComboBox Grid.Column="0" ItemsSource="{Binding VersionsForComparison}"
                                                      SelectedItem="{Binding SelectedVersionForComparisonA}"
                                                      DisplayMemberPath="DisplayName" Margin="5"/>

                                            <TextBlock Grid.Column="1" Text="vs" VerticalAlignment="Center"
                                                       HorizontalAlignment="Center" FontWeight="Bold" Margin="10,0"/>

                                            <ComboBox Grid.Column="2" ItemsSource="{Binding VersionsForComparison}"
                                                      SelectedItem="{Binding SelectedVersionForComparisonB}"
                                                      DisplayMemberPath="DisplayName" Margin="5"/>
                                        </Grid>

                                        <DataGrid Grid.Row="1" ItemsSource="{Binding ComparisonResults}"
                                                  AutoGenerateColumns="False" IsReadOnly="True" Margin="5"
                                                  AlternatingRowBackground="#F5F5F5">
                                            <DataGrid.Columns>
                                                <DataGridTextColumn Header="Parameter" Binding="{Binding ParameterName}" Width="200"/>
                                                <DataGridTextColumn Header="Version A" Binding="{Binding ValueA}" Width="*"/>
                                                <DataGridTextColumn Header="Version B" Binding="{Binding ValueB}" Width="*"/>
                                                <DataGridTextColumn Header="Difference" Binding="{Binding Difference}" Width="100"/>
                                                <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="100"/>
                                            </DataGrid.Columns>
                                        </DataGrid>
                                    </Grid>
                                </TabItem>
                            </TabControl>

                            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="5">
                                <Button Content="Compare Versions" Command="{Binding CompareVersionsCommand}"
                                        Style="{StaticResource ActionButtonStyle}"/>
                                <Button Content="Create New Version" Command="{Binding CreateNewVersionCommand}"
                                        Style="{StaticResource ActionButtonStyle}"/>
                                <Button Content="Export Comparison" Command="{Binding ExportComparisonCommand}"
                                        Style="{StaticResource ActionButtonStyle}"/>
                                <Button Content="Set as Baseline" Command="{Binding SetAsBaselineCommand}"
                                        Style="{StaticResource ActionButtonStyle}"/>
                            </StackPanel>
                        </Grid>
                    </TabItem>

                    <!-- Backup Schedule Tab -->
                    <TabItem Header="Backup Schedule">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- Schedule Controls -->
                            <Grid Grid.Row="0" Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="Scheduled Backups" Style="{StaticResource SectionHeaderStyle}"/>
                                    <TextBlock Text="Configure automatic backup schedules for your ECUs. Set frequency, retention policies, and notification options."
                                               TextWrapping="Wrap" Margin="0,0,0,10"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Orientation="Vertical" Margin="10,0,0,0">
                                    <Button Content="Add Schedule" Command="{Binding AddScheduleCommand}"
                                            Style="{StaticResource ActionButtonStyle}"/>
                                    <Button Content="Run All Now" Command="{Binding RunAllSchedulesCommand}"
                                            Style="{StaticResource ActionButtonStyle}"/>
                                </StackPanel>
                            </Grid>

                            <!-- Schedule Tabs -->
                            <TabControl Grid.Row="1" Margin="10">
                                <TabItem Header="Active Schedules">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="*"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <DataGrid Grid.Row="0" ItemsSource="{Binding ScheduledBackups}"
                                                  SelectedItem="{Binding SelectedSchedule}"
                                                  AutoGenerateColumns="False" IsReadOnly="True" Margin="0,10,0,10"
                                                  AlternatingRowBackground="#F5F5F5">
                                            <DataGrid.Columns>
                                                <DataGridTextColumn Header="ECU" Binding="{Binding ECUName}" Width="100"/>
                                                <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="150"/>
                                                <DataGridTextColumn Header="Frequency" Binding="{Binding Frequency}" Width="120"/>
                                                <DataGridTextColumn Header="Next Run" Binding="{Binding NextRunTime, StringFormat='{}{0:yyyy-MM-dd HH:mm}'}" Width="150"/>
                                                <DataGridTextColumn Header="Last Run" Binding="{Binding LastExecutionTime, StringFormat='{}{0:yyyy-MM-dd HH:mm}'}" Width="150"/>
                                                <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="*"/>
                                                <DataGridCheckBoxColumn Header="Active" Binding="{Binding IsActive}" Width="60"/>
                                            </DataGrid.Columns>
                                        </DataGrid>

                                        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="Edit Schedule" Command="{Binding EditScheduleCommand}"
                                                    Style="{StaticResource ActionButtonStyle}"/>
                                            <Button Content="Run Now" Command="{Binding RunScheduleNowCommand}"
                                                    Style="{StaticResource ActionButtonStyle}"/>
                                            <Button Content="Disable" Command="{Binding DisableScheduleCommand}"
                                                    Style="{StaticResource ActionButtonStyle}"/>
                                            <Button Content="Remove" Command="{Binding RemoveScheduleCommand}"
                                                    Style="{StaticResource ActionButtonStyle}"/>
                                        </StackPanel>
                                    </Grid>
                                </TabItem>

                                <TabItem Header="Retention Policies">
                                    <Grid Margin="10">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="*"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0" Text="Configure how long backups are kept and when they should be automatically removed."
                                                   TextWrapping="Wrap" Margin="0,0,0,10"/>

                                        <Grid Grid.Row="1">
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>

                                            <GroupBox Grid.Row="0" Header="Global Retention Settings" Margin="0,5,0,10">
                                                <Grid Margin="5">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                    </Grid.RowDefinitions>

                                                    <CheckBox Grid.Row="0" Grid.Column="0" Content="Keep backups for at least"
                                                              IsChecked="{Binding UseMinimumRetentionTime}" Margin="5"/>
                                                    <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Margin="5">
                                                        <TextBox Text="{Binding MinimumRetentionDays}" Width="50" IsEnabled="{Binding UseMinimumRetentionTime}"/>
                                                        <TextBlock Text=" days" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                                    </StackPanel>

                                                    <CheckBox Grid.Row="1" Grid.Column="0" Content="Limit maximum number of backups"
                                                              IsChecked="{Binding UseLimitBackupCount}" Margin="5"/>
                                                    <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="5">
                                                        <TextBox Text="{Binding MaximumBackupCount}" Width="50" IsEnabled="{Binding UseLimitBackupCount}"/>
                                                        <TextBlock Text=" backups per ECU" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                                    </StackPanel>

                                                    <CheckBox Grid.Row="2" Grid.Column="0" Content="Auto-delete backups older than"
                                                              IsChecked="{Binding UseMaximumRetentionTime}" Margin="5"/>
                                                    <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="5">
                                                        <TextBox Text="{Binding MaximumRetentionDays}" Width="50" IsEnabled="{Binding UseMaximumRetentionTime}"/>
                                                        <TextBlock Text=" days" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                                    </StackPanel>
                                                </Grid>
                                            </GroupBox>

                                            <GroupBox Grid.Row="1" Header="Version Retention" Margin="0,5,0,10">
                                                <Grid Margin="5">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                    </Grid.RowDefinitions>

                                                    <CheckBox Grid.Row="0" Grid.Column="0" Content="Keep all versions for"
                                                              IsChecked="{Binding KeepAllVersionsForPeriod}" Margin="5"/>
                                                    <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Margin="5">
                                                        <TextBox Text="{Binding KeepAllVersionsDays}" Width="50" IsEnabled="{Binding KeepAllVersionsForPeriod}"/>
                                                        <TextBlock Text=" days" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                                    </StackPanel>

                                                    <CheckBox Grid.Row="1" Grid.Column="0" Content="Limit versions per backup to"
                                                              IsChecked="{Binding LimitVersionCount}" Margin="5"/>
                                                    <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="5">
                                                        <TextBox Text="{Binding MaxVersionCount}" Width="50" IsEnabled="{Binding LimitVersionCount}"/>
                                                        <TextBlock Text=" versions" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                                    </StackPanel>
                                                </Grid>
                                            </GroupBox>

                                            <GroupBox Grid.Row="2" Header="Storage Management" Margin="0,5,0,10">
                                                <Grid Margin="5">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                    </Grid.RowDefinitions>

                                                    <CheckBox Grid.Row="0" Grid.Column="0" Content="Limit total backup storage to"
                                                              IsChecked="{Binding LimitTotalStorage}" Margin="5"/>
                                                    <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Margin="5">
                                                        <TextBox Text="{Binding MaxStorageMB}" Width="50" IsEnabled="{Binding LimitTotalStorage}"/>
                                                        <TextBlock Text=" MB" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                                    </StackPanel>

                                                    <CheckBox Grid.Row="1" Grid.Column="0" Content="Auto-compress backups older than"
                                                              IsChecked="{Binding AutoCompressOldBackups}" Margin="5"/>
                                                    <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="5">
                                                        <TextBox Text="{Binding CompressBackupsAfterDays}" Width="50" IsEnabled="{Binding AutoCompressOldBackups}"/>
                                                        <TextBlock Text=" days" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                                    </StackPanel>
                                                </Grid>
                                            </GroupBox>
                                        </Grid>

                                        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                                            <Button Content="Apply Policy" Command="{Binding ApplyRetentionPolicyCommand}"
                                                    Style="{StaticResource ActionButtonStyle}"/>
                                            <Button Content="Reset to Defaults" Command="{Binding ResetRetentionPolicyCommand}"
                                                    Style="{StaticResource ActionButtonStyle}"/>
                                        </StackPanel>
                                    </Grid>
                                </TabItem>

                                <TabItem Header="Schedule History">
                                    <DataGrid ItemsSource="{Binding ScheduleHistory}"
                                              AutoGenerateColumns="False" IsReadOnly="True" Margin="10"
                                              AlternatingRowBackground="#F5F5F5">
                                        <DataGrid.Columns>
                                            <DataGridTextColumn Header="Schedule" Binding="{Binding ScheduleName}" Width="150"/>
                                            <DataGridTextColumn Header="ECU" Binding="{Binding ECUName}" Width="100"/>
                                            <DataGridTextColumn Header="Execution Time" Binding="{Binding ExecutionTime, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" Width="150"/>
                                            <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="100"/>
                                            <DataGridTextColumn Header="Backup ID" Binding="{Binding BackupId}" Width="150"/>
                                            <DataGridTextColumn Header="Details" Binding="{Binding Details}" Width="*"/>
                                        </DataGrid.Columns>
                                    </DataGrid>
                                </TabItem>
                            </TabControl>
                        </Grid>
                    </TabItem>
                </TabControl>

                <!-- Action Buttons -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="5">
                    <Button Content="Restore Backup" Command="{Binding RestoreBackupCommand}"
                            Style="{StaticResource ActionButtonStyle}"/>
                    <Button Content="Export Backup" Command="{Binding ExportBackupCommand}"
                            Style="{StaticResource ActionButtonStyle}"/>
                    <Button Content="Import Backup" Command="{Binding ImportBackupCommand}"
                            Style="{StaticResource ActionButtonStyle}"/>
                    <Button Content="Delete Backup" Command="{Binding DeleteBackupCommand}"
                            Style="{StaticResource ActionButtonStyle}"/>
                    <Button Content="Refresh" Command="{Binding RefreshBackupsCommand}"
                            Style="{StaticResource ActionButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Style="{StaticResource CardBorderStyle}" Margin="5,0,5,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" VerticalAlignment="Center"/>

                <ProgressBar Grid.Column="1" IsIndeterminate="{Binding IsRestoringBackup}"
                             Visibility="{Binding IsRestoringBackup, Converter={StaticResource BoolToVisibilityConverter}}"
                             Width="100" Height="10"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>
