using System;
using System.Collections.Generic;
using VolvoFlashWR.Core.Enums;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents diagnostic data collected from an ECU
    /// </summary>
    public class DiagnosticData
    {
        /// <summary>
        /// Unique identifier for the diagnostic session
        /// </summary>
        public string SessionId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Timestamp when the diagnostic data was collected
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// ID of the ECU from which the data was collected
        /// </summary>
        public string ECUId { get; set; }

        /// <summary>
        /// Name of the ECU from which the data was collected
        /// </summary>
        public string ECUName { get; set; }

        /// <summary>
        /// List of active faults detected during diagnostics
        /// </summary>
        public List<ECUFault> ActiveFaults { get; set; } = new List<ECUFault>();

        /// <summary>
        /// List of inactive faults detected during diagnostics
        /// </summary>
        public List<ECUFault> InactiveFaults { get; set; } = new List<ECUFault>();

        /// <summary>
        /// Parameters read from the ECU during diagnostics
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Operating mode used during diagnostics
        /// </summary>
        public OperatingMode OperatingMode { get; set; }

        /// <summary>
        /// Connection type used during diagnostics
        /// </summary>
        public VocomConnectionType ConnectionType { get; set; }

        /// <summary>
        /// Indicates if the diagnostic session was successful
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// Error message if the diagnostic session was not successful
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Duration of the diagnostic session in milliseconds
        /// </summary>
        public long SessionDurationMs { get; set; }

        /// <summary>
        /// Status of the diagnostic session
        /// </summary>
        public DiagnosticStatus Status { get; set; } = DiagnosticStatus.InProgress;

        /// <summary>
        /// Memory usage information from the ECU
        /// </summary>
        public Dictionary<string, double> MemoryUsage { get; set; } = new Dictionary<string, double>();

        /// <summary>
        /// Performance metrics from the ECU
        /// </summary>
        public Dictionary<string, double> PerformanceMetrics { get; set; } = new Dictionary<string, double>();

        /// <summary>
        /// Indicates if the diagnostic data is valid
        /// </summary>
        public bool IsValid => IsSuccessful && Status == DiagnosticStatus.Success;

        /// <summary>
        /// Detailed diagnostic results
        /// </summary>
        public List<DiagnosticResult> DiagnosticResults { get; set; } = new List<DiagnosticResult>();
    }

    /// <summary>
    /// Represents a diagnostic result
    /// </summary>
    public class DiagnosticResult
    {
        /// <summary>
        /// Unique identifier for the diagnostic result
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Name of the diagnostic test
        /// </summary>
        public string TestName { get; set; }

        /// <summary>
        /// Description of the diagnostic test
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Result of the diagnostic test
        /// </summary>
        public string Result { get; set; }

        /// <summary>
        /// Indicates if the test passed
        /// </summary>
        public bool Passed { get; set; }

        /// <summary>
        /// Timestamp when the test was performed
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// Additional data related to the test
        /// </summary>
        public Dictionary<string, object> AdditionalData { get; set; } = new Dictionary<string, object>();
    }
}
