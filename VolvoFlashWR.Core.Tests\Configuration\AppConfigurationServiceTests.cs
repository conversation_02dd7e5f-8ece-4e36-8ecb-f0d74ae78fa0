using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using Moq;
using VolvoFlashWR.Core.Configuration;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Tests.Configuration
{
    [TestFixture]
    public class AppConfigurationServiceTests
    {
        private Mock<ILoggingService> _mockLogger;
        private AppConfigurationService _configService;
        private string _testConfigPath;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _configService = new AppConfigurationService(_mockLogger.Object);

            // Create a temporary test config path
            string tempDir = Path.Combine(Path.GetTempPath(), "VolvoFlashWRTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(tempDir);
            _testConfigPath = Path.Combine(tempDir, "test_config.json");

            // Ensure the directory exists
            string configDirectory = Path.GetDirectoryName(_testConfigPath);
            if (!string.IsNullOrEmpty(configDirectory))
            {
                Directory.CreateDirectory(configDirectory);
            }
        }

        [TearDown]
        public void TearDown()
        {
            // Clean up the test directory
            if (File.Exists(_testConfigPath))
            {
                File.Delete(_testConfigPath);
            }

            string directory = Path.GetDirectoryName(_testConfigPath);
            if (Directory.Exists(directory))
            {
                Directory.Delete(directory, true);
            }
        }

        [Test]
        public async Task InitializeAsync_CreatesDefaultConfiguration_WhenFileDoesNotExist()
        {
            // Arrange
            Assert.That(File.Exists(_testConfigPath), Is.False, "Test config file should not exist before test");

            // Act
            bool result = await _configService.InitializeAsync(_testConfigPath);

            // Explicitly save the configuration to ensure the file is created
            await _configService.SaveConfigurationAsync();

            // Assert
            Assert.That(result, Is.True, "Initialization should succeed");
            Assert.That(File.Exists(_testConfigPath), Is.True, "Config file should be created");

            // Verify default values
            Assert.That(_configService.GetValue<string>("Application.Name"), Is.EqualTo("VolvoFlashWR"));
            Assert.That(_configService.GetValue<bool>("Logging.Enabled"), Is.EqualTo(true));
            Assert.That(_configService.GetValue<bool>("Logging.DetailedLogging"), Is.EqualTo(false));
        }

        [Test]
        public async Task GetValue_ReturnsDefaultValue_WhenKeyDoesNotExist()
        {
            // Arrange
            await _configService.InitializeAsync(_testConfigPath);

            // Act
            string value = _configService.GetValue<string>("NonExistentKey", "DefaultValue");

            // Assert
            Assert.That(value, Is.EqualTo("DefaultValue"));
        }

        [Test]
        public async Task SetValueAsync_UpdatesExistingValue()
        {
            // Arrange
            await _configService.InitializeAsync(_testConfigPath);
            string testKey = "Application.Name";
            string originalValue = _configService.GetValue<string>(testKey);
            string newValue = "TestApplication";

            // Act
            bool result = await _configService.SetValueAsync(testKey, newValue);

            // Assert
            Assert.That(result, Is.True, "SetValue should succeed");
            Assert.That(_configService.GetValue<string>(testKey), Is.EqualTo(newValue));
            Assert.That(_configService.GetValue<string>(testKey), Is.Not.EqualTo(originalValue));
        }

        [Test]
        public async Task SetValueAsync_AddsNewValue_WhenKeyDoesNotExist()
        {
            // Arrange
            await _configService.InitializeAsync(_testConfigPath);
            string testKey = "Test.NewKey";
            string testValue = "NewValue";

            // Act
            bool result = await _configService.SetValueAsync(testKey, testValue);

            // Assert
            Assert.That(result, Is.True, "SetValue should succeed");
            Assert.That(_configService.GetValue<string>(testKey), Is.EqualTo(testValue));
        }

        [Test]
        public async Task SaveConfigurationAsync_PersistsChangesToFile()
        {
            // Arrange
            await _configService.InitializeAsync(_testConfigPath);
            string testKey = "Test.SaveTest";
            string testValue = "SavedValue";
            await _configService.SetValueAsync(testKey, testValue);

            // Act
            bool saveResult = await _configService.SaveConfigurationAsync();

            // Create a new instance to load from the saved file
            var newConfigService = new AppConfigurationService(_mockLogger.Object);
            bool loadResult = await newConfigService.InitializeAsync(_testConfigPath);

            // Assert
            Assert.That(saveResult, Is.True, "Save should succeed");
            Assert.That(loadResult, Is.True, "Load should succeed");
            Assert.That(newConfigService.GetValue<string>(testKey), Is.EqualTo(testValue));
        }

        [Test]
        public async Task ResetToDefaultsAsync_RestoresDefaultValues()
        {
            // Arrange
            await _configService.InitializeAsync(_testConfigPath);
            await _configService.SetValueAsync("Application.Name", "ModifiedName");
            await _configService.SetValueAsync("Test.CustomKey", "CustomValue");

            // Act
            bool result = await _configService.ResetToDefaultsAsync();

            // Assert
            Assert.That(result, Is.True, "Reset should succeed");
            Assert.That(_configService.GetValue<string>("Application.Name"), Is.EqualTo("VolvoFlashWR"));
            Assert.That(_configService.GetValue<string>("Test.CustomKey", "DefaultValue"), Is.EqualTo("DefaultValue"));
        }

        [Test]
        public async Task GetAllValues_ReturnsAllConfigurationValues()
        {
            // Arrange
            await _configService.InitializeAsync(_testConfigPath);

            // Act
            var allValues = _configService.GetAllValues();

            // Assert
            Assert.That(allValues, Is.Not.Null);
            Assert.That(allValues.Count, Is.GreaterThan(0));
            Assert.That(allValues.ContainsKey("Application.Name"), Is.True);
            Assert.That(allValues.ContainsKey("Logging.Enabled"), Is.True);
        }

        [Test]
        public void ConfigurationChanged_EventIsRaised_WhenValueIsSet()
        {
            // Arrange
            string changedKey = null;
            _configService.ConfigurationChanged += (sender, key) => changedKey = key;
            _configService.InitializeAsync(_testConfigPath).Wait();

            // Act
            _configService.SetValueAsync("Test.EventTest", "EventValue").Wait();

            // Assert
            Assert.That(changedKey, Is.EqualTo("Test.EventTest"));
        }
    }
}
