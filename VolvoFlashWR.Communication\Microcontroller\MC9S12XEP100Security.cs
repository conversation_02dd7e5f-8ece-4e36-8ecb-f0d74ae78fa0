using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Services;
using VolvoFlashWR.Core.Utilities;

namespace VolvoFlashWR.Communication.Microcontroller
{
    /// <summary>
    /// Provides security-related functionality for the MC9S12XEP100 microcontroller
    /// Based on the S12XE9SECV2 security module specifications
    /// </summary>
    public class MC9S12XEP100Security
    {
        private readonly ILoggingService? _logger;
        private readonly IRegisterAccess? _registerAccess;

        // Security module register addresses
        private static class SecurityRegisters
        {
            // Security status register
            public const uint SECSTAT = 0x306;

            // Security key registers (8 bytes)
            public const uint SECKEY0 = 0x300;
            public const uint SECKEY1 = 0x301;
            public const uint SECKEY2 = 0x302;
            public const uint SECKEY3 = 0x303;
            public const uint SECKEY4 = 0x304;
            public const uint SECKEY5 = 0x305;
            public const uint SECKEY6 = 0x306;
            public const uint SECKEY7 = 0x307;

            // Security control register
            public const uint SECCTL = 0x308;

            // Security test register
            public const uint SECTEST = 0x309;
        }

        // Security status register bit masks
        private static class SecurityStatusBits
        {
            public const byte KEYEN = 0x80;    // Key enabled
            public const byte UNSEC = 0x40;    // Unsecured state
            public const byte SEC = 0x20;      // Secured state
            public const byte SECSTAT = 0x10;  // Security status
            public const byte FNVIOL = 0x08;   // Flash non-volatile violation
            public const byte FVIOL = 0x04;    // Flash violation
            public const byte FCBEF = 0x02;    // Flash command buffer empty flag
            public const byte FACCERR = 0x01;  // Flash access error
        }

        // Security control register bit masks
        private static class SecurityControlBits
        {
            public const byte KEYACC = 0x80;   // Key access
            public const byte SECSTAT = 0x40;  // Security state
            public const byte FDIVEN = 0x20;   // Flash clock divider enable
            public const byte FDIV5 = 0x10;    // Flash clock divider bit 5
            public const byte FDIV4 = 0x08;    // Flash clock divider bit 4
            public const byte FDIV3 = 0x04;    // Flash clock divider bit 3
            public const byte FDIV2 = 0x02;    // Flash clock divider bit 2
            public const byte FDIV1 = 0x01;    // Flash clock divider bit 1
        }

        /// <summary>
        /// Initializes a new instance of the MC9S12XEP100Security class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="registerAccess">The register access interface</param>
        public MC9S12XEP100Security(ILoggingService? logger = null, IRegisterAccess? registerAccess = null)
        {
            _logger = logger;
            _registerAccess = registerAccess;
        }

        /// <summary>
        /// Checks if the ECU is secured
        /// </summary>
        /// <returns>True if the ECU is secured, false otherwise</returns>
        public async Task<bool> IsSecuredAsync()
        {
            try
            {
                if (_registerAccess == null)
                {
                    _logger?.LogError("Register access is null");
                    return false;
                }

                // Read the security status register
                byte secStatus = await _registerAccess.ReadRegisterByteAsync(SecurityRegisters.SECSTAT);

                // Check if the SEC bit is set (secured state)
                bool isSecured = (secStatus & SecurityStatusBits.SEC) != 0;

                _logger?.LogInformation($"ECU security status: {(isSecured ? "Secured" : "Unsecured")}");

                return isSecured;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error checking security status: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Performs security access using the backdoor key
        /// </summary>
        /// <param name="backdoorKey">The 8-byte backdoor key</param>
        /// <returns>True if access is granted, false otherwise</returns>
        public async Task<bool> PerformSecurityAccessAsync(byte[] backdoorKey)
        {
            try
            {
                if (_registerAccess == null)
                {
                    _logger?.LogError("Register access is null");
                    return false;
                }

                if (backdoorKey == null || backdoorKey.Length != 8)
                {
                    _logger?.LogError("Invalid backdoor key (must be 8 bytes)");
                    return false;
                }

                _logger?.LogInformation("Attempting security access with backdoor key");

                // Write the backdoor key to the security key registers
                await _registerAccess.WriteRegisterByteAsync(SecurityRegisters.SECKEY0, backdoorKey[0]);
                await _registerAccess.WriteRegisterByteAsync(SecurityRegisters.SECKEY1, backdoorKey[1]);
                await _registerAccess.WriteRegisterByteAsync(SecurityRegisters.SECKEY2, backdoorKey[2]);
                await _registerAccess.WriteRegisterByteAsync(SecurityRegisters.SECKEY3, backdoorKey[3]);
                await _registerAccess.WriteRegisterByteAsync(SecurityRegisters.SECKEY4, backdoorKey[4]);
                await _registerAccess.WriteRegisterByteAsync(SecurityRegisters.SECKEY5, backdoorKey[5]);
                await _registerAccess.WriteRegisterByteAsync(SecurityRegisters.SECKEY6, backdoorKey[6]);
                await _registerAccess.WriteRegisterByteAsync(SecurityRegisters.SECKEY7, backdoorKey[7]);

                // Set the KEYACC bit in the security control register to apply the key
                byte secCtl = await _registerAccess.ReadRegisterByteAsync(SecurityRegisters.SECCTL);
                secCtl |= SecurityControlBits.KEYACC;
                await _registerAccess.WriteRegisterByteAsync(SecurityRegisters.SECCTL, secCtl);

                // Wait for the key to be processed (typically takes a few milliseconds)
                await Task.Delay(10);

                // Check if access was granted by reading the security status register
                byte secStatus = await _registerAccess.ReadRegisterByteAsync(SecurityRegisters.SECSTAT);
                bool accessGranted = (secStatus & SecurityStatusBits.UNSEC) != 0;

                _logger?.LogInformation($"Security access {(accessGranted ? "granted" : "denied")}");

                return accessGranted;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error performing security access: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Generates a backdoor key for the ECU based on the ECU ID and a master key
        /// </summary>
        /// <param name="ecuId">The ECU ID</param>
        /// <param name="masterKey">The master key (16 bytes)</param>
        /// <returns>The 8-byte backdoor key</returns>
        public byte[] GenerateBackdoorKey(string ecuId, byte[] masterKey)
        {
            try
            {
                if (string.IsNullOrEmpty(ecuId))
                {
                    _logger?.LogError("ECU ID is null or empty");
                    return new byte[8];
                }

                if (masterKey == null || masterKey.Length != 16)
                {
                    _logger?.LogError("Invalid master key (must be 16 bytes)");
                    return new byte[8];
                }

                _logger?.LogInformation($"Generating backdoor key for ECU ID: {ecuId}");

                // Create a seed from the ECU ID
                byte[] seed = Encoding.ASCII.GetBytes(ecuId);

                // Use HMAC-SHA256 to generate a key based on the seed and master key
                using (var hmac = new HMACSHA256(masterKey))
                {
                    byte[] hash = hmac.ComputeHash(seed);

                    // Take the first 8 bytes of the hash as the backdoor key
                    byte[] backdoorKey = new byte[8];
                    Array.Copy(hash, backdoorKey, 8);

                    _logger?.LogInformation($"Generated backdoor key: {BitConverter.ToString(backdoorKey).Replace("-", "")}");

                    return backdoorKey;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error generating backdoor key: {ex.Message}");
                return new byte[8];
            }
        }

        /// <summary>
        /// Verifies the integrity of flash memory using CRC
        /// </summary>
        /// <param name="startAddress">The start address of the flash memory to verify</param>
        /// <param name="size">The size of the flash memory to verify</param>
        /// <returns>True if verification is successful, false otherwise</returns>
        public async Task<bool> VerifyFlashIntegrityAsync(uint startAddress, int size)
        {
            try
            {
                if (_registerAccess == null)
                {
                    _logger?.LogError("Register access is null");
                    return false;
                }

                _logger?.LogInformation($"Verifying flash integrity at address 0x{startAddress:X8}, size: {size} bytes");

                // Read the flash memory
                byte[] data = new byte[size];
                for (int i = 0; i < size; i++)
                {
                    data[i] = await _registerAccess.ReadRegisterByteAsync(startAddress + (uint)i);
                }

                // Calculate the CRC-16 checksum
                ushort calculatedCrc = CalculateCRC16(data);

                // Read the stored CRC from the flash memory (typically stored at the end of the flash block)
                byte crcHigh = await _registerAccess.ReadRegisterByteAsync(startAddress + (uint)size);
                byte crcLow = await _registerAccess.ReadRegisterByteAsync(startAddress + (uint)size + 1);
                ushort storedCrc = (ushort)((crcHigh << 8) | crcLow);

                bool isValid = calculatedCrc == storedCrc;
                _logger?.LogInformation($"Flash integrity verification {(isValid ? "passed" : "failed")}. Calculated CRC: 0x{calculatedCrc:X4}, Stored CRC: 0x{storedCrc:X4}");

                return isValid;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error verifying flash integrity: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Calculates the CRC-16 checksum for the given data
        /// </summary>
        /// <param name="data">The data to calculate the checksum for</param>
        /// <returns>The CRC-16 checksum</returns>
        private ushort CalculateCRC16(byte[] data)
        {
            const ushort polynomial = 0x1021; // CRC-16-CCITT polynomial
            ushort crc = 0xFFFF; // Initial value

            foreach (byte b in data)
            {
                crc ^= (ushort)(b << 8);
                for (int i = 0; i < 8; i++)
                {
                    if ((crc & 0x8000) != 0)
                    {
                        crc = (ushort)((crc << 1) ^ polynomial);
                    }
                    else
                    {
                        crc <<= 1;
                    }
                }
            }

            return crc;
        }

        /// <summary>
        /// Secures the ECU by setting the security bits
        /// </summary>
        /// <returns>True if the ECU is successfully secured, false otherwise</returns>
        public async Task<bool> SecureECUAsync()
        {
            try
            {
                if (_registerAccess == null)
                {
                    _logger?.LogError("Register access is null");
                    return false;
                }

                _logger?.LogInformation("Securing ECU");

                // Set the security control register to enable security
                byte secCtl = await _registerAccess.ReadRegisterByteAsync(SecurityRegisters.SECCTL);
                secCtl |= SecurityControlBits.SECSTAT;
                await _registerAccess.WriteRegisterByteAsync(SecurityRegisters.SECCTL, secCtl);

                // Wait for the security to be applied
                await Task.Delay(10);

                // Verify that the ECU is secured
                bool isSecured = await IsSecuredAsync();
                _logger?.LogInformation($"ECU security {(isSecured ? "enabled" : "failed to enable")}");

                return isSecured;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error securing ECU: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Performs a secure flash programming operation with verification
        /// </summary>
        /// <param name="address">The address to program</param>
        /// <param name="data">The data to program</param>
        /// <returns>True if programming is successful, false otherwise</returns>
        public async Task<bool> SecureFlashProgramAsync(uint address, byte[] data)
        {
            try
            {
                if (_registerAccess == null)
                {
                    _logger?.LogError("Register access is null");
                    return false;
                }

                if (data == null || data.Length == 0)
                {
                    _logger?.LogError("Invalid data (null or empty)");
                    return false;
                }

                _logger?.LogInformation($"Secure flash programming at address 0x{address:X8}, size: {data.Length} bytes");

                // Check if the ECU is secured
                bool isSecured = await IsSecuredAsync();
                if (isSecured)
                {
                    _logger?.LogWarning("ECU is secured, attempting security access");

                    // Get the backdoor key from the ECU properties
                    // This is just a placeholder - in a real implementation, you would get the key from a secure source
                    byte[] backdoorKey = new byte[8] { 0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF };

                    // Perform security access
                    bool accessGranted = await PerformSecurityAccessAsync(backdoorKey);
                    if (!accessGranted)
                    {
                        _logger?.LogError("Security access denied");
                        return false;
                    }
                }

                // Calculate the CRC-16 checksum for the data
                ushort crc = CalculateCRC16(data);

                // Append the CRC to the data
                byte[] dataWithCrc = new byte[data.Length + 2];
                Array.Copy(data, dataWithCrc, data.Length);
                dataWithCrc[data.Length] = (byte)((crc >> 8) & 0xFF); // CRC high byte
                dataWithCrc[data.Length + 1] = (byte)(crc & 0xFF); // CRC low byte

                // Program the flash memory with the data and CRC
                for (int i = 0; i < dataWithCrc.Length; i++)
                {
                    await _registerAccess.WriteRegisterByteAsync(address + (uint)i, dataWithCrc[i]);
                }

                // Verify the programmed data
                bool isVerified = await VerifyFlashIntegrityAsync(address, data.Length);
                _logger?.LogInformation($"Secure flash programming {(isVerified ? "successful" : "failed")}");

                return isVerified;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error in secure flash programming: {ex.Message}");
                return false;
            }
        }
    }
}
