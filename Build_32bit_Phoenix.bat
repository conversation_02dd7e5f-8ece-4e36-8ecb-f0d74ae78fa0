@echo off
title VolvoFlashWR - Build 32-bit for Phoenix APCI
color 0A

echo ===============================================================================
echo                    VolvoFlashWR - Build 32-bit for Phoenix APCI
echo ===============================================================================
echo.
echo Building the application as 32-bit to resolve Phoenix APCI architecture mismatch
echo.
echo ===============================================================================

echo === Building Solution as 32-bit ===
echo Building solution for 32-bit (x86) to match Phoenix APCI libraries...
dotnet build VolvoFlashWR.sln --configuration Release

REM Check if build was successful
if %ERRORLEVEL% NEQ 0 (
    echo Build failed! Please fix the build errors and run again.
    echo Press any key to exit...
    pause >nul
    exit /b %ERRORLEVEL%
)

echo Build completed successfully!

echo.
echo === Verifying Output Directory ===
if exist "VolvoFlashWR.Launcher\bin\Release\net8.0-windows" (
    echo ✓ Output directory found
    dir "VolvoFlashWR.Launcher\bin\Release\net8.0-windows\*.exe" /b
) else (
    echo ✗ Output directory not found
)

echo.
echo === Setting Environment for Phoenix APCI ===
set PHOENIX_VOCOM_ENABLED=true
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=true
set LOG_LEVEL=Debug

echo PHOENIX_VOCOM_ENABLED=%PHOENIX_VOCOM_ENABLED%
echo USE_DUMMY_IMPLEMENTATIONS=%USE_DUMMY_IMPLEMENTATIONS%

echo.
echo === Copying Essential Phoenix APCI Libraries ===

REM Change to the output directory
cd /d "VolvoFlashWR.Launcher\bin\Release\net8.0-windows"

REM Copy critical Phoenix APCI libraries
echo Copying essential libraries for 32-bit Phoenix APCI real hardware communication...

REM Core APCI libraries (CRITICAL)
copy "..\..\..\..\Libraries\apci.dll" "." >nul 2>&1
if exist "apci.dll" (echo   ✓ apci.dll) else (echo   ✗ apci.dll - MISSING!)
copy "..\..\..\..\Libraries\apcidb.dll" "." >nul 2>&1
if exist "apcidb.dll" (echo   ✓ apcidb.dll) else (echo   ✗ apcidb.dll - MISSING!)
copy "..\..\..\..\Libraries\Rpci.dll" "." >nul 2>&1
if exist "Rpci.dll" (echo   ✓ Rpci.dll) else (echo   ✗ Rpci.dll - MISSING!)
copy "..\..\..\..\Libraries\Pc2.dll" "." >nul 2>&1
if exist "Pc2.dll" (echo   ✓ Pc2.dll) else (echo   ✗ Pc2.dll - MISSING!)

REM Vocom driver (CRITICAL)
copy "..\..\..\..\Libraries\WUDFPuma.dll" "." >nul 2>&1
if exist "WUDFPuma.dll" (echo   ✓ WUDFPuma.dll) else (echo   ✗ WUDFPuma.dll - MISSING!)
copy "..\..\..\..\Libraries\WUDFUpdate_01009.dll" "." >nul 2>&1
if exist "WUDFUpdate_01009.dll" (echo   ✓ WUDFUpdate_01009.dll) else (echo   ✗ WUDFUpdate_01009.dll - MISSING!)

REM Phoenix libraries (ESSENTIAL)
copy "..\..\..\..\Libraries\PhoenixESW.dll" "." >nul 2>&1
if exist "PhoenixESW.dll" (echo   ✓ PhoenixESW.dll) else (echo   ✗ PhoenixESW.dll - MISSING!)
copy "..\..\..\..\Libraries\PhoenixGeneral.dll" "." >nul 2>&1
if exist "PhoenixGeneral.dll" (echo   ✓ PhoenixGeneral.dll) else (echo   ✗ PhoenixGeneral.dll - MISSING!)

REM Volvo APCI libraries (REQUIRED)
copy "..\..\..\..\Libraries\Volvo.ApciPlus.dll" "." >nul 2>&1
if exist "Volvo.ApciPlus.dll" (echo   ✓ Volvo.ApciPlus.dll) else (echo   ✗ Volvo.ApciPlus.dll - MISSING!)
copy "..\..\..\..\Libraries\Volvo.ApciPlusData.dll" "." >nul 2>&1
if exist "Volvo.ApciPlusData.dll" (echo   ✓ Volvo.ApciPlusData.dll) else (echo   ✗ Volvo.ApciPlusData.dll - MISSING!)

REM Volvo protocol libraries (REQUIRED)
copy "..\..\..\..\Libraries\Volvo.NVS.Core.dll" "." >nul 2>&1
if exist "Volvo.NVS.Core.dll" (echo   ✓ Volvo.NVS.Core.dll) else (echo   ✗ Volvo.NVS.Core.dll - MISSING!)
copy "..\..\..\..\Libraries\Volvo.NAMS.AC.Services.Interface.dll" "." >nul 2>&1
if exist "Volvo.NAMS.AC.Services.Interface.dll" (echo   ✓ Volvo.NAMS.AC.Services.Interface.dll) else (echo   ✗ Volvo.NAMS.AC.Services.Interface.dll - MISSING!)

REM Vodia libraries (REQUIRED)
copy "..\..\..\..\Libraries\Vodia.CommonDomain.Model.dll" "." >nul 2>&1
if exist "Vodia.CommonDomain.Model.dll" (echo   ✓ Vodia.CommonDomain.Model.dll) else (echo   ✗ Vodia.CommonDomain.Model.dll - MISSING!)
copy "..\..\..\..\Libraries\Vodia.Contracts.Common.dll" "." >nul 2>&1
if exist "Vodia.Contracts.Common.dll" (echo   ✓ Vodia.Contracts.Common.dll) else (echo   ✗ Vodia.Contracts.Common.dll - MISSING!)

REM Essential dependencies
copy "..\..\..\..\Libraries\log4net.dll" "." >nul 2>&1
if exist "log4net.dll" (echo   ✓ log4net.dll) else (echo   ✗ log4net.dll - MISSING!)
copy "..\..\..\..\Libraries\Newtonsoft.Json.dll" "." >nul 2>&1
if exist "Newtonsoft.Json.dll" (echo   ✓ Newtonsoft.Json.dll) else (echo   ✗ Newtonsoft.Json.dll - MISSING!)

echo.
echo ===============================================================================
echo                           32-bit Build Complete!
echo ===============================================================================
echo.
echo The application has been built as 32-bit to match the Phoenix APCI libraries.
echo This should resolve the architecture mismatch error (0x8007000B).
echo.
echo Next steps:
echo   1. Run the application to test Phoenix APCI initialization
echo   2. Check logs for any remaining architecture issues
echo   3. Verify real hardware detection (if Vocom adapter connected)
echo.
echo ===============================================================================

echo.
echo === Starting 32-bit Application with Phoenix APCI ===
echo.
echo Starting VolvoFlashWR Launcher (32-bit) with Phoenix APCI enabled...

REM Run the 32-bit application
"VolvoFlashWR.Launcher.exe" --mode=Normal

REM Return to original directory
cd /d "%~dp0"

echo.
echo Application has exited.
echo Check the logs for Phoenix APCI initialization results.
echo Press any key to close this window...
pause >nul
