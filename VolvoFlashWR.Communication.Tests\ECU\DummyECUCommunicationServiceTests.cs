using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.ECU;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.ECU
{
    [TestFixture]
    public class DummyECUCommunicationServiceTests
    {
        private DummyECUCommunicationService _dummyService;
        private Mock<ILoggingService> _loggerMock;
        private Mock<IVocomService> _vocomServiceMock;

        [SetUp]
        public void Setup()
        {
            _loggerMock = new Mock<ILoggingService>();
            _vocomServiceMock = new Mock<IVocomService>();
            _dummyService = new DummyECUCommunicationService(_loggerMock.Object);
        }

        [Test]
        public async Task InitializeAsync_ShouldReturnTrue()
        {
            // Act
            bool result = await _dummyService.InitializeAsync(_vocomServiceMock.Object);

            // Assert
            ClassicAssert.That(result, Is.True);
            ClassicAssert.That(_dummyService.IsInitialized, Is.True);
        }

        [Test]
        public async Task ScanForECUsAsync_ShouldReturnECUs()
        {
            // Arrange
            await _dummyService.InitializeAsync(_vocomServiceMock.Object);

            // Act
            List<ECUDevice> ecus = await _dummyService.ScanForECUsAsync();

            // Assert
            ClassicAssert.That(ecus, Is.Not.Null);
            ClassicAssert.That(ecus.Count > 0, Is.True);
            ClassicAssert.That(ecus[0].Manufacturer, Is.EqualTo("Volvo"));
        }

        [Test]
        public async Task ConnectToECUAsync_ShouldConnectECU()
        {
            // Arrange
            await _dummyService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _dummyService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];

            // Act
            bool result = await _dummyService.ConnectToECUAsync(ecu);

            // Assert
            ClassicAssert.That(result, Is.True);
            ClassicAssert.That(ecu.ConnectionStatus, Is.EqualTo(ECUConnectionStatus.Connected));
            ClassicAssert.That(_dummyService.ConnectedECUs.Contains(ecu), Is.True, "Connected ECUs list should contain the ECU");
        }

        [Test]
        public async Task SetCommunicationSpeedModeAsync_WithHighSpeed_ShouldSucceed()
        {
            // Arrange
            await _dummyService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _dummyService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _dummyService.ConnectToECUAsync(ecu);

            // Ensure the ECU supports high-speed communication
            ecu.SupportsHighSpeedCommunication = true;

            // Act
            bool result = await _dummyService.SetCommunicationSpeedModeAsync(ecu, CommunicationSpeedMode.High);

            // Assert
            ClassicAssert.That(result, Is.True);
            ClassicAssert.That(ecu.CurrentCommunicationSpeedMode, Is.EqualTo(CommunicationSpeedMode.High));
            ClassicAssert.That(ecu.ConnectionStatus, Is.EqualTo(ECUConnectionStatus.Connected));
        }

        [Test]
        public async Task SetCommunicationSpeedModeAsync_WithLowSpeed_ShouldSucceed()
        {
            // Arrange
            await _dummyService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _dummyService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _dummyService.ConnectToECUAsync(ecu);

            // Ensure the ECU supports low-speed communication
            ecu.SupportsLowSpeedCommunication = true;

            // Act
            bool result = await _dummyService.SetCommunicationSpeedModeAsync(ecu, CommunicationSpeedMode.Low);

            // Assert
            ClassicAssert.That(result, Is.True);
            ClassicAssert.That(ecu.CurrentCommunicationSpeedMode, Is.EqualTo(CommunicationSpeedMode.Low));
            ClassicAssert.That(ecu.ConnectionStatus, Is.EqualTo(ECUConnectionStatus.Connected));
        }

        [Test]
        public async Task SetCommunicationSpeedModeAsync_WithUnsupportedHighSpeed_ShouldFail()
        {
            // Arrange
            await _dummyService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _dummyService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _dummyService.ConnectToECUAsync(ecu);

            // Ensure the ECU does not support high-speed communication
            ecu.SupportsHighSpeedCommunication = false;

            // Act
            bool result = await _dummyService.SetCommunicationSpeedModeAsync(ecu, CommunicationSpeedMode.High);

            // Assert
            ClassicAssert.That(result, Is.False);
            ClassicAssert.That(ecu.CurrentCommunicationSpeedMode, Is.EqualTo(CommunicationSpeedMode.Normal));
        }

        [Test]
        public async Task SetCommunicationSpeedModeAsync_WithDisconnectedECU_ShouldFail()
        {
            // Arrange
            await _dummyService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _dummyService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];

            // Ensure the ECU is disconnected
            ecu.ConnectionStatus = ECUConnectionStatus.Disconnected;

            // Act
            bool result = await _dummyService.SetCommunicationSpeedModeAsync(ecu, CommunicationSpeedMode.High);

            // Assert
            ClassicAssert.That(result, Is.False);
        }

        [Test]
        public async Task ReadParametersAsync_ShouldReturnParameters()
        {
            // Arrange
            await _dummyService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _dummyService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _dummyService.ConnectToECUAsync(ecu);

            // Act
            Dictionary<string, object> parameters = await _dummyService.ReadParametersAsync(ecu);

            // Assert
            ClassicAssert.That(parameters, Is.Not.Null);
            ClassicAssert.That(parameters.Count > 0, Is.True);
        }

        [Test]
        public async Task PerformDiagnosticSessionAsync_ShouldReturnDiagnosticData()
        {
            // Arrange
            await _dummyService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _dummyService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _dummyService.ConnectToECUAsync(ecu);

            // Act
            DiagnosticData diagnosticData = await _dummyService.PerformDiagnosticSessionAsync(ecu);

            // Assert
            ClassicAssert.That(diagnosticData, Is.Not.Null);
            ClassicAssert.That(diagnosticData.ECUId, Is.EqualTo(ecu.Id));
            ClassicAssert.That(diagnosticData.DiagnosticResults, Is.Not.Null);
            ClassicAssert.That(diagnosticData.Timestamp, Is.Not.EqualTo(default(DateTime)));
            ClassicAssert.That(diagnosticData.IsValid, Is.True);
        }

        [Test]
        public async Task ReadEEPROMAsync_ShouldReturnData()
        {
            // Arrange
            await _dummyService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _dummyService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _dummyService.ConnectToECUAsync(ecu);

            // Act
            byte[] eepromData = await _dummyService.ReadEEPROMAsync(ecu);

            // Assert
            ClassicAssert.That(eepromData, Is.Not.Null);
            ClassicAssert.That(eepromData.Length, Is.GreaterThan(0));
        }

        [Test]
        public async Task ReadMicrocontrollerCodeAsync_ShouldReturnData()
        {
            // Arrange
            await _dummyService.InitializeAsync(_vocomServiceMock.Object);
            List<ECUDevice> ecus = await _dummyService.ScanForECUsAsync();
            ECUDevice ecu = ecus[0];
            await _dummyService.ConnectToECUAsync(ecu);

            // Act
            byte[] mcuCode = await _dummyService.ReadMicrocontrollerCodeAsync(ecu);

            // Assert
            ClassicAssert.That(mcuCode, Is.Not.Null);
            ClassicAssert.That(mcuCode.Length > 0, Is.True);
        }
    }
}

