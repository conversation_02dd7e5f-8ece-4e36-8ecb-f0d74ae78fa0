@echo off
title VolvoFlashWR - Phoenix APCI Library Verification
color 0B

echo ===============================================================================
echo                    Phoenix APCI Library Verification
echo ===============================================================================
echo.
echo This script verifies that all essential libraries for Phoenix APCI real
echo hardware communication are present and properly configured.
echo.
echo ===============================================================================

set MISSING_COUNT=0
set PRESENT_COUNT=0

echo === Core APCI Communication Libraries ===
call :CheckLibrary "apci.dll" "Core APCI communication library"
call :CheckLibrary "apcidb.dll" "APCI database library"
call :CheckLibrary "Rpci.dll" "Remote PCI communication library"
call :CheckLibrary "Pc2.dll" "PC2 communication library"

echo.
echo === Vocom 1 Adapter Driver Libraries ===
call :CheckLibrary "WUDFPuma.dll" "Main Vocom 1 adapter driver"
call :CheckLibrary "WUDFUpdate_01009.dll" "Vocom driver update component"
call :CheckLibrary "WdfCoInstaller01009.dll" "Windows driver framework co-installer"
call :CheckLibrary "winusbcoinstaller2.dll" "USB co-installer"

echo.
echo === Phoenix Integration Libraries ===
call :CheckLibrary "PhoenixESW.dll" "Phoenix ESW integration library"
call :CheckLibrary "PhoenixGeneral.dll" "Phoenix general utilities"
call :CheckLibrary "PhoenixProducInformation.dll" "Phoenix product information"

echo.
echo === Volvo APCI Communication Libraries ===
call :CheckLibrary "Volvo.ApciPlus.dll" "Enhanced Volvo APCI communication"
call :CheckLibrary "Volvo.ApciPlusData.dll" "Volvo APCI data handling"
call :CheckLibrary "Volvo.ApciPlusTea2Data.dll" "Volvo TEA2 data handling"

echo.
echo === Volvo Protocol Libraries ===
call :CheckLibrary "Volvo.NVS.Core.dll" "Volvo NVS core functionality"
call :CheckLibrary "Volvo.NVS.Logging.dll" "Volvo NVS logging"
call :CheckLibrary "Volvo.NAMS.AC.Services.Interface.dll" "Volvo NAMS AC services"
call :CheckLibrary "Volvo.NAMS.AC.Services.Interfaces.dll" "Volvo NAMS AC interfaces"

echo.
echo === Volvo IT Framework Libraries ===
call :CheckLibrary "VolvoIt.Baf.Utility.dll" "Volvo IT BAF utilities"
call :CheckLibrary "VolvoIt.Waf.ServiceContract.dll" "Volvo IT WAF service contracts"
call :CheckLibrary "VolvoIt.Waf.Utility.dll" "Volvo IT WAF utilities"

echo.
echo === Communication Protocol Libraries ===
call :CheckLibrary "Vodia.CommonDomain.Model.dll" "Common domain models"
call :CheckLibrary "Vodia.Contracts.Common.dll" "Common contracts"
call :CheckLibrary "Vodia.UtilityComponent.dll" "Utility components"

echo.
echo === Essential Dependencies ===
call :CheckLibrary "log4net.dll" "Logging framework"
call :CheckLibrary "Newtonsoft.Json.dll" "JSON serialization"
call :CheckLibrary "AutoMapper.dll" "Object mapping"
call :CheckLibrary "NHibernate.dll" "ORM framework"

echo.
echo === Configuration Files ===
call :CheckLibrary "Volvo.ApciPlus.dll.config" "Volvo APCI Plus configuration"
call :CheckLibrary "Volvo.ApciPlusData.dll.config" "Volvo APCI Plus Data configuration"

echo.
echo ===============================================================================
echo                           Verification Summary
echo ===============================================================================
echo.
echo Libraries Present: %PRESENT_COUNT%
echo Libraries Missing: %MISSING_COUNT%
echo.

if %MISSING_COUNT% EQU 0 (
    echo ✓ ALL ESSENTIAL LIBRARIES ARE PRESENT!
    echo   The application is ready for real hardware communication.
    echo.
    echo Environment Variables:
    echo   PHOENIX_VOCOM_ENABLED=true
    echo   USE_DUMMY_IMPLEMENTATIONS=false
    echo.
    echo The application will use Phoenix APCI for real Vocom 1 adapter communication.
) else (
    echo ✗ MISSING LIBRARIES DETECTED!
    echo   %MISSING_COUNT% essential libraries are missing.
    echo   Please run 'Enable_Real_Hardware_Mode.bat' to copy missing libraries.
)

echo.
echo ===============================================================================
pause
goto :eof

:CheckLibrary
set LIBRARY_NAME=%~1
set LIBRARY_DESC=%~2

if exist "%LIBRARY_NAME%" (
    echo   ✓ %LIBRARY_NAME% - %LIBRARY_DESC%
    set /a PRESENT_COUNT+=1
) else (
    if exist "Libraries\%LIBRARY_NAME%" (
        echo   ⚠ %LIBRARY_NAME% - Available in Libraries folder ^(not copied^)
        set /a MISSING_COUNT+=1
    ) else (
        echo   ✗ %LIBRARY_NAME% - MISSING - %LIBRARY_DESC%
        set /a MISSING_COUNT+=1
    )
)
goto :eof
