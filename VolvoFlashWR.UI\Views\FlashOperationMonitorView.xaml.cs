using System.Windows;
using VolvoFlashWR.Core.Diagnostics;

namespace VolvoFlashWR.UI.Views
{
    /// <summary>
    /// Interaction logic for FlashOperationMonitorView.xaml
    /// </summary>
    public partial class FlashOperationMonitorView : Window
    {
        /// <summary>
        /// Initializes a new instance of the FlashOperationMonitorView class
        /// </summary>
        /// <param name="monitor">The flash operation monitor</param>
        public FlashOperationMonitorView(FlashOperationMonitor monitor)
        {
            InitializeComponent();

            // Set the content to a new progress control with the monitor
            Content = new Controls.FlashOperationProgressControl(monitor);
        }
    }
}
