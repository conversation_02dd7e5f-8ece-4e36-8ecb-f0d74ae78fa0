using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Microsoft.Win32;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.UI.ViewModels
{
    /// <summary>
    /// ViewModel for backup operations
    /// </summary>
    public class BackupViewModel : INotifyPropertyChanged
    {
        #region Private Fields

        private readonly ILoggingService _loggingService;
        private readonly IBackupService _backupService;
        private readonly IECUCommunicationService _ecuCommunicationService;
        private bool _isInitialized;
        private bool _isCreatingBackup;
        private bool _isRestoringBackup;
        private string _statusMessage;
        private ECUDevice _selectedECU;
        private BackupData _selectedBackup;
        private ObservableCollection<BackupData> _backups;
        private bool _includeEEPROM;
        private bool _includeMicrocontrollerCode;
        private bool _includeParameters;
        private bool _restoreEEPROM;
        private bool _restoreMicrocontrollerCode;
        private bool _restoreParameters;
        private string _backupDescription;

        #endregion

        #region Properties

        /// <summary>
        /// Gets or sets whether the ViewModel is initialized
        /// </summary>
        public bool IsInitialized
        {
            get => _isInitialized;
            set
            {
                _isInitialized = value;
                OnPropertyChanged(nameof(IsInitialized));
            }
        }

        /// <summary>
        /// Gets or sets whether a backup is being created
        /// </summary>
        public bool IsCreatingBackup
        {
            get => _isCreatingBackup;
            set
            {
                _isCreatingBackup = value;
                OnPropertyChanged(nameof(IsCreatingBackup));
            }
        }

        /// <summary>
        /// Gets or sets whether a backup is being restored
        /// </summary>
        public bool IsRestoringBackup
        {
            get => _isRestoringBackup;
            set
            {
                _isRestoringBackup = value;
                OnPropertyChanged(nameof(IsRestoringBackup));
            }
        }

        /// <summary>
        /// Gets or sets the status message
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged(nameof(StatusMessage));
            }
        }

        /// <summary>
        /// Gets or sets the selected ECU
        /// </summary>
        public ECUDevice SelectedECU
        {
            get => _selectedECU;
            set
            {
                _selectedECU = value;
                OnPropertyChanged(nameof(SelectedECU));
                _ = LoadBackupsForSelectedECUAsync(); // Use discard to acknowledge we're intentionally not awaiting
            }
        }

        /// <summary>
        /// Gets or sets the selected backup
        /// </summary>
        public BackupData SelectedBackup
        {
            get => _selectedBackup;
            set
            {
                _selectedBackup = value;
                OnPropertyChanged(nameof(SelectedBackup));
            }
        }

        /// <summary>
        /// Gets or sets the collection of backups
        /// </summary>
        public ObservableCollection<BackupData> Backups
        {
            get => _backups;
            set
            {
                _backups = value;
                OnPropertyChanged(nameof(Backups));
            }
        }

        /// <summary>
        /// Gets or sets whether to include EEPROM data in the backup
        /// </summary>
        public bool IncludeEEPROM
        {
            get => _includeEEPROM;
            set
            {
                _includeEEPROM = value;
                OnPropertyChanged(nameof(IncludeEEPROM));
            }
        }

        /// <summary>
        /// Gets or sets whether to include microcontroller code in the backup
        /// </summary>
        public bool IncludeMicrocontrollerCode
        {
            get => _includeMicrocontrollerCode;
            set
            {
                _includeMicrocontrollerCode = value;
                OnPropertyChanged(nameof(IncludeMicrocontrollerCode));
            }
        }

        /// <summary>
        /// Gets or sets whether to include parameters in the backup
        /// </summary>
        public bool IncludeParameters
        {
            get => _includeParameters;
            set
            {
                _includeParameters = value;
                OnPropertyChanged(nameof(IncludeParameters));
            }
        }

        /// <summary>
        /// Gets or sets whether to restore EEPROM data
        /// </summary>
        public bool RestoreEEPROM
        {
            get => _restoreEEPROM;
            set
            {
                _restoreEEPROM = value;
                OnPropertyChanged(nameof(RestoreEEPROM));
            }
        }

        /// <summary>
        /// Gets or sets whether to restore microcontroller code
        /// </summary>
        public bool RestoreMicrocontrollerCode
        {
            get => _restoreMicrocontrollerCode;
            set
            {
                _restoreMicrocontrollerCode = value;
                OnPropertyChanged(nameof(RestoreMicrocontrollerCode));
            }
        }

        /// <summary>
        /// Gets or sets whether to restore parameters
        /// </summary>
        public bool RestoreParameters
        {
            get => _restoreParameters;
            set
            {
                _restoreParameters = value;
                OnPropertyChanged(nameof(RestoreParameters));
            }
        }

        /// <summary>
        /// Gets or sets the backup description
        /// </summary>
        public string BackupDescription
        {
            get => _backupDescription;
            set
            {
                _backupDescription = value;
                OnPropertyChanged(nameof(BackupDescription));
            }
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to create a backup
        /// </summary>
        public ICommand CreateBackupCommand { get; }

        /// <summary>
        /// Command to restore a backup
        /// </summary>
        public ICommand RestoreBackupCommand { get; }

        /// <summary>
        /// Command to delete a backup
        /// </summary>
        public ICommand DeleteBackupCommand { get; }

        /// <summary>
        /// Command to import a backup
        /// </summary>
        public ICommand ImportBackupCommand { get; }

        /// <summary>
        /// Command to export a backup
        /// </summary>
        public ICommand ExportBackupCommand { get; }

        /// <summary>
        /// Command to refresh the backup list
        /// </summary>
        public ICommand RefreshBackupsCommand { get; }

        /// <summary>
        /// Command to compare backups
        /// </summary>
        public ICommand CompareBackupsCommand { get; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the BackupViewModel class
        /// </summary>
        /// <param name="loggingService">The logging service</param>
        /// <param name="backupService">The backup service</param>
        /// <param name="ecuCommunicationService">The ECU communication service</param>
        public BackupViewModel(ILoggingService loggingService, IBackupService backupService, IECUCommunicationService ecuCommunicationService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));
            _ecuCommunicationService = ecuCommunicationService ?? throw new ArgumentNullException(nameof(ecuCommunicationService));

            // Initialize collections
            Backups = new ObservableCollection<BackupData>();

            // Initialize default values
            IncludeEEPROM = true;
            IncludeMicrocontrollerCode = true;
            IncludeParameters = true;
            RestoreEEPROM = true;
            RestoreMicrocontrollerCode = true;
            RestoreParameters = true;

            // Initialize commands
            CreateBackupCommand = new RelayCommand(async _ => await CreateBackupAsync(), _ => CanCreateBackup());
            RestoreBackupCommand = new RelayCommand(async _ => await RestoreBackupAsync(), _ => CanRestoreBackup());
            DeleteBackupCommand = new RelayCommand(async _ => await DeleteBackupAsync(), _ => CanDeleteBackup());
            ImportBackupCommand = new RelayCommand(async _ => await ImportBackupAsync(), _ => IsInitialized);
            ExportBackupCommand = new RelayCommand(async _ => await ExportBackupAsync(), _ => CanExportBackup());
            RefreshBackupsCommand = new RelayCommand(async _ => await RefreshBackupsAsync(), _ => IsInitialized);
            CompareBackupsCommand = new RelayCommand(async _ => await CompareBackupsAsync(), _ => CanCompareBackups());

            // Subscribe to events
            _backupService.BackupCreated += OnBackupCreated;
            _backupService.BackupRestored += OnBackupRestored;
            _backupService.BackupError += OnBackupError;

            // Initialize the ViewModel
            InitializeAsync();
        }

        #endregion

        #region Initialization

        private async void InitializeAsync()
        {
            try
            {
                StatusMessage = "Initializing backup operations...";

                // Initialize the backup service if not already initialized
                if (!_backupService.BackupDirectoryPath.Contains("Backups"))
                {
                    bool initialized = await _backupService.InitializeAsync(_ecuCommunicationService);
                    if (!initialized)
                    {
                        StatusMessage = "Failed to initialize backup service";
                        _loggingService.LogError("Failed to initialize backup service", "BackupViewModel");
                        return;
                    }
                }

                // Load all backups
                await RefreshBackupsAsync();

                IsInitialized = true;
                StatusMessage = "Backup operations initialized successfully";
                _loggingService.LogInformation("Backup operations initialized successfully", "BackupViewModel");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Initialization error: {ex.Message}";
                _loggingService.LogError("Initialization error", "BackupViewModel", ex);
            }
        }

        #endregion

        #region Backup Operations

        private async Task CreateBackupAsync()
        {
            try
            {
                if (SelectedECU == null)
                {
                    StatusMessage = "No ECU selected";
                    return;
                }

                IsCreatingBackup = true;
                StatusMessage = $"Creating backup for ECU {SelectedECU.Name}...";

                BackupData backup = await _backupService.CreateBackupAsync(
                    SelectedECU,
                    BackupDescription,
                    "", // category
                    null, // tags
                    IncludeEEPROM,
                    IncludeMicrocontrollerCode,
                    IncludeParameters);

                if (backup != null)
                {
                    StatusMessage = $"Backup created successfully for ECU {SelectedECU.Name}";
                    _loggingService.LogInformation($"Backup created successfully for ECU {SelectedECU.Name}", "BackupViewModel");

                    // Add the backup to the collection
                    Backups.Add(backup);
                    SelectedBackup = backup;
                }
                else
                {
                    StatusMessage = $"Failed to create backup for ECU {SelectedECU.Name}";
                    _loggingService.LogError($"Failed to create backup for ECU {SelectedECU.Name}", "BackupViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error creating backup: {ex.Message}";
                _loggingService.LogError("Error creating backup", "BackupViewModel", ex);
            }
            finally
            {
                IsCreatingBackup = false;
            }
        }

        private async Task RestoreBackupAsync()
        {
            try
            {
                if (SelectedECU == null)
                {
                    StatusMessage = "No ECU selected";
                    return;
                }

                if (SelectedBackup == null)
                {
                    StatusMessage = "No backup selected";
                    return;
                }

                // Confirm the restore operation
                MessageBoxResult result = MessageBox.Show(
                    $"Are you sure you want to restore the backup to ECU {SelectedECU.Name}?\n\nThis operation will overwrite data on the ECU and cannot be undone.",
                    "Confirm Restore",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                {
                    return;
                }

                IsRestoringBackup = true;
                StatusMessage = $"Restoring backup to ECU {SelectedECU.Name}...";

                bool success = await _backupService.RestoreBackupAsync(
                    SelectedBackup,
                    SelectedECU,
                    RestoreEEPROM,
                    RestoreMicrocontrollerCode,
                    RestoreParameters);

                if (success)
                {
                    StatusMessage = $"Backup restored successfully to ECU {SelectedECU.Name}";
                    _loggingService.LogInformation($"Backup restored successfully to ECU {SelectedECU.Name}", "BackupViewModel");
                }
                else
                {
                    StatusMessage = $"Failed to restore backup to ECU {SelectedECU.Name}";
                    _loggingService.LogError($"Failed to restore backup to ECU {SelectedECU.Name}", "BackupViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error restoring backup: {ex.Message}";
                _loggingService.LogError("Error restoring backup", "BackupViewModel", ex);
            }
            finally
            {
                IsRestoringBackup = false;
            }
        }

        private async Task DeleteBackupAsync()
        {
            try
            {
                if (SelectedBackup == null)
                {
                    StatusMessage = "No backup selected";
                    return;
                }

                // Confirm the delete operation
                MessageBoxResult result = MessageBox.Show(
                    $"Are you sure you want to delete the backup for ECU {SelectedBackup.ECUName}?\n\nThis operation cannot be undone.",
                    "Confirm Delete",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                {
                    return;
                }

                StatusMessage = $"Deleting backup for ECU {SelectedBackup.ECUName}...";

                bool success = await _backupService.DeleteBackupAsync(SelectedBackup);

                if (success)
                {
                    StatusMessage = $"Backup deleted successfully";
                    _loggingService.LogInformation($"Backup deleted successfully", "BackupViewModel");

                    // Remove the backup from the collection
                    Backups.Remove(SelectedBackup);
                    SelectedBackup = null;
                }
                else
                {
                    StatusMessage = $"Failed to delete backup";
                    _loggingService.LogError($"Failed to delete backup", "BackupViewModel");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error deleting backup: {ex.Message}";
                _loggingService.LogError("Error deleting backup", "BackupViewModel", ex);
            }
        }

        private async Task ImportBackupAsync()
        {
            try
            {
                // Create an open file dialog
                OpenFileDialog openFileDialog = new OpenFileDialog
                {
                    Filter = "Backup Files (*.backup)|*.backup|All Files (*.*)|*.*",
                    DefaultExt = ".backup"
                };

                // Show the dialog and get the result
                bool? result = openFileDialog.ShowDialog();

                if (result == true)
                {
                    StatusMessage = $"Importing backup from {openFileDialog.FileName}...";

                    BackupData backup = await _backupService.LoadBackupFromFileAsync(openFileDialog.FileName);

                    if (backup != null)
                    {
                        StatusMessage = $"Backup imported successfully";
                        _loggingService.LogInformation($"Backup imported successfully from {openFileDialog.FileName}", "BackupViewModel");

                        // Add the backup to the collection if it's not already there
                        if (!Backups.Any(b => b.Id == backup.Id))
                        {
                            Backups.Add(backup);
                        }
                        SelectedBackup = backup;
                    }
                    else
                    {
                        StatusMessage = $"Failed to import backup";
                        _loggingService.LogError($"Failed to import backup from {openFileDialog.FileName}", "BackupViewModel");
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error importing backup: {ex.Message}";
                _loggingService.LogError("Error importing backup", "BackupViewModel", ex);
            }
        }

        private async Task ExportBackupAsync()
        {
            try
            {
                if (SelectedBackup == null)
                {
                    StatusMessage = "No backup selected";
                    return;
                }

                // Create a save file dialog
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "Backup Files (*.backup)|*.backup|All Files (*.*)|*.*",
                    DefaultExt = ".backup",
                    FileName = Path.GetFileName(SelectedBackup.FilePath)
                };

                // Show the dialog and get the result
                bool? result = saveFileDialog.ShowDialog();

                if (result == true)
                {
                    StatusMessage = $"Exporting backup to {saveFileDialog.FileName}...";

                    bool success = await _backupService.SaveBackupToFileAsync(SelectedBackup, saveFileDialog.FileName);

                    if (success)
                    {
                        StatusMessage = $"Backup exported successfully to {saveFileDialog.FileName}";
                        _loggingService.LogInformation($"Backup exported successfully to {saveFileDialog.FileName}", "BackupViewModel");
                    }
                    else
                    {
                        StatusMessage = $"Failed to export backup";
                        _loggingService.LogError($"Failed to export backup to {saveFileDialog.FileName}", "BackupViewModel");
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error exporting backup: {ex.Message}";
                _loggingService.LogError("Error exporting backup", "BackupViewModel", ex);
            }
        }

        private async Task RefreshBackupsAsync()
        {
            try
            {
                StatusMessage = "Refreshing backups...";

                // Clear the current backups
                Backups.Clear();

                // Load all backups
                var allBackups = await _backupService.GetAllBackupsAsync();

                // Add the backups to the collection
                foreach (var backup in allBackups)
                {
                    Backups.Add(backup);
                }

                StatusMessage = $"Loaded {Backups.Count} backups";
                _loggingService.LogInformation($"Loaded {Backups.Count} backups", "BackupViewModel");

                // If an ECU is selected, filter the backups
                if (SelectedECU != null)
                {
                    await LoadBackupsForSelectedECUAsync();
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error refreshing backups: {ex.Message}";
                _loggingService.LogError("Error refreshing backups", "BackupViewModel", ex);
            }
        }

        private async Task LoadBackupsForSelectedECUAsync()
        {
            try
            {
                if (SelectedECU == null)
                {
                    return;
                }

                StatusMessage = $"Loading backups for ECU {SelectedECU.Name}...";

                // Clear the current backups
                Backups.Clear();

                // Load backups for the selected ECU
                var ecuBackups = await _backupService.GetBackupsForECUAsync(SelectedECU.Id);

                // Add the backups to the collection
                foreach (var backup in ecuBackups)
                {
                    Backups.Add(backup);
                }

                StatusMessage = $"Loaded {Backups.Count} backups for ECU {SelectedECU.Name}";
                _loggingService.LogInformation($"Loaded {Backups.Count} backups for ECU {SelectedECU.Name}", "BackupViewModel");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading backups for ECU: {ex.Message}";
                _loggingService.LogError("Error loading backups for ECU", "BackupViewModel", ex);
            }
        }

        private async Task CompareBackupsAsync()
        {
            try
            {
                // Create a dialog to select two backups to compare
                // For simplicity, we'll just use the first two backups in the collection
                if (Backups.Count < 2)
                {
                    StatusMessage = "Need at least two backups to compare";
                    return;
                }

                BackupData backup1 = Backups[0];
                BackupData backup2 = Backups[1];

                StatusMessage = $"Comparing backups...";

                var differences = await _backupService.CompareBackupsAsync(backup1, backup2);

                if (differences.Count > 0)
                {
                    // Display the differences
                    string differencesText = $"Found {differences.Count} differences between backups:\n\n";
                    foreach (var diff in differences)
                    {
                        differencesText += $"- {diff.Key}: {diff.Value}\n";
                    }

                    MessageBox.Show(
                        differencesText,
                        "Backup Comparison Results",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    StatusMessage = $"Comparison complete. Found {differences.Count} differences.";
                }
                else
                {
                    MessageBox.Show(
                        "The backups are identical.",
                        "Backup Comparison Results",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    StatusMessage = "Comparison complete. The backups are identical.";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error comparing backups: {ex.Message}";
                _loggingService.LogError("Error comparing backups", "BackupViewModel", ex);
            }
        }

        #endregion

        #region Event Handlers

        private void OnBackupCreated(object sender, BackupData backup)
        {
            // Update the UI on the UI thread
            if (Application.Current != null)
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    StatusMessage = $"Backup created for ECU {backup.ECUName}";
                });
            }
            else
            {
                // Direct call for test environment
                StatusMessage = $"Backup created for ECU {backup.ECUName}";
            }
        }

        private void OnBackupRestored(object sender, BackupData backup)
        {
            // Update the UI on the UI thread
            if (Application.Current != null)
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    StatusMessage = $"Backup restored to ECU {backup.ECUName}";
                });
            }
            else
            {
                // Direct call for test environment
                StatusMessage = $"Backup restored to ECU {backup.ECUName}";
            }
        }

        private void OnBackupError(object sender, string errorMessage)
        {
            // Update the UI on the UI thread
            if (Application.Current != null)
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    StatusMessage = $"Backup error: {errorMessage}";
                });
            }
            else
            {
                // Direct call for test environment
                StatusMessage = $"Backup error: {errorMessage}";
            }
        }

        #endregion

        #region Command Conditions

        private bool CanCreateBackup()
        {
            return IsInitialized && SelectedECU != null && !IsCreatingBackup && !IsRestoringBackup;
        }

        private bool CanRestoreBackup()
        {
            return IsInitialized && SelectedECU != null && SelectedBackup != null && !IsCreatingBackup && !IsRestoringBackup;
        }

        private bool CanDeleteBackup()
        {
            return IsInitialized && SelectedBackup != null && !IsCreatingBackup && !IsRestoringBackup;
        }

        private bool CanExportBackup()
        {
            return IsInitialized && SelectedBackup != null && !IsCreatingBackup && !IsRestoringBackup;
        }

        private bool CanCompareBackups()
        {
            return IsInitialized && Backups.Count >= 2 && !IsCreatingBackup && !IsRestoringBackup;
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// Simple implementation of ICommand for the ViewModel
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action<object> _execute;
        private readonly Func<object, bool> _canExecute;

        public RelayCommand(Action<object> execute, Func<object, bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object parameter)
        {
            return _canExecute == null || _canExecute(parameter);
        }

        public void Execute(object parameter)
        {
            _execute(parameter);
        }

        /// <summary>
        /// Raises the CanExecuteChanged event.
        /// </summary>
        public void RaiseCanExecuteChanged()
        {
            CommandManager.InvalidateRequerySuggested();
        }
    }
}
