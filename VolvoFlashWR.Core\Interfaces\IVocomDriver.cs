using System.Threading.Tasks;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Interfaces
{
    /// <summary>
    /// Interface for Vocom hardware driver
    /// </summary>
    public interface IVocomDriver
    {
        /// <summary>
        /// Gets a value indicating whether the driver is initialized
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Initializes the Vocom driver
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        Task<bool> InitializeAsync();

        /// <summary>
        /// Shuts down the Vocom driver
        /// </summary>
        /// <returns>True if shutdown is successful, false otherwise</returns>
        Task<bool> ShutdownAsync();

        /// <summary>
        /// Detects available Vocom devices
        /// </summary>
        /// <returns>Array of detected Vocom devices</returns>
        Task<VocomDevice[]> DetectDevicesAsync();

        /// <summary>
        /// Connects to a Vocom device
        /// </summary>
        /// <param name="device">The device to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        Task<bool> ConnectToDeviceAsync(VocomDevice device);

        /// <summary>
        /// Disconnects from a Vocom device
        /// </summary>
        /// <param name="device">The device to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        Task<bool> DisconnectFromDeviceAsync(VocomDevice device);

        /// <summary>
        /// Sends a CAN frame to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendCANFrameAsync(VocomDevice device, byte[] data, int responseLength, int timeout = 5000);

        /// <summary>
        /// Sends an SPI command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="command">The command byte</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendSPICommandAsync(VocomDevice device, byte command, byte[] data, int responseLength, int timeout = 5000);

        /// <summary>
        /// Sends an SCI command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="command">The command byte</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendSCICommandAsync(VocomDevice device, byte command, byte[] data, int responseLength, int timeout = 5000);

        /// <summary>
        /// Sends an IIC command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="address">The IIC device address</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendIICCommandAsync(VocomDevice device, byte address, byte[] data, int responseLength, int timeout = 5000);

        /// <summary>
        /// Sends raw data to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        Task<byte[]> SendRawDataAsync(VocomDevice device, byte[] data, int responseLength, int timeout = 5000);

        /// <summary>
        /// Updates the firmware of a Vocom device
        /// </summary>
        /// <param name="device">The device to update</param>
        /// <param name="firmwareData">The firmware data</param>
        /// <returns>True if update is successful, false otherwise</returns>
        Task<bool> UpdateFirmwareAsync(VocomDevice device, byte[] firmwareData);
    }
}
