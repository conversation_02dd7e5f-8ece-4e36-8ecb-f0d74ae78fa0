using System;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Configuration;
using VolvoFlashWR.Core.Services;

namespace VolvoFlashWR.KeyGenerator
{
    /// <summary>
    /// Utility class to generate activation keys
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("Volvo Flash WR - Activation Key Generator");
            Console.WriteLine("=========================================");
            Console.WriteLine();

            try
            {
                // Create required services
                var loggingService = new LoggingService();
                await loggingService.InitializeAsync("Logs", false);

                var configService = new AppConfigurationService(loggingService);
                await configService.InitializeAsync();

                var licensingService = new LicensingService(loggingService, configService);
                await licensingService.InitializeAsync();

                // Get expiration date
                DateTime expirationDate;
                if (args.Length > 0 && DateTime.TryParse(args[0], out DateTime parsedDate))
                {
                    expirationDate = parsedDate;
                }
                else
                {
                    // Default to one year from now
                    expirationDate = DateTime.Now.AddYears(1);
                }

                // Get number of keys to generate
                int keyCount = 5;
                if (args.Length > 1 && int.TryParse(args[1], out int parsedCount) && parsedCount > 0)
                {
                    keyCount = parsedCount;
                }

                Console.WriteLine($"Generating {keyCount} activation keys valid until {expirationDate:yyyy-MM-dd}");
                Console.WriteLine();

                // Generate keys
                for (int i = 0; i < keyCount; i++)
                {
                    string key = licensingService.GenerateActivationKey(expirationDate);
                    Console.WriteLine($"Key {i+1}: {key}");
                }

                Console.WriteLine();
                Console.WriteLine("Keys generated successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating keys: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
