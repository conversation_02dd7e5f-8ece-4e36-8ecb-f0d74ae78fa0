using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Utility class to analyze DLL exports
    /// </summary>
    public class DllAnalyzer
    {
        private readonly ILoggingService _logger;

        public DllAnalyzer(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Lists all exported functions from a DLL
        /// </summary>
        /// <param name="dllPath">Path to the DLL</param>
        /// <returns>List of exported function names</returns>
        public async Task<List<string>> ListExportedFunctionsAsync(string dllPath)
        {
            List<string> exportedFunctions = new List<string>();

            try
            {
                _logger.LogInformation($"Analyzing DLL exports for: {dllPath}", "DllAnalyzer");

                if (!File.Exists(dllPath))
                {
                    _logger.LogError($"DLL file not found: {dllPath}", "DllAnalyzer");
                    return exportedFunctions;
                }

                // Load the DLL
                IntPtr dllHandle = LoadLibrary(dllPath);
                if (dllHandle == IntPtr.Zero)
                {
                    int error = Marshal.GetLastWin32Error();
                    _logger.LogError($"Failed to load DLL. Error code: {error}", "DllAnalyzer");
                    return exportedFunctions;
                }

                try
                {
                    // Try to get exports using EnumProcessModules and GetProcAddress
                    await Task.Run(() =>
                    {
                        // Common function names to check for Vocom DLLs
                        string[] commonFunctionNames = new[]
                        {
                            "Vocom_Initialize",
                            "Initialize",
                            "Vocom_Init",
                            "VocomInitialize",
                            "Vocom1_Initialize",
                            "VOCOM_Initialize",
                            "Vocom_Shutdown",
                            "Shutdown",
                            "Vocom_DetectDevices",
                            "DetectDevices",
                            "Vocom_ConnectDevice",
                            "ConnectDevice",
                            "Vocom_DisconnectDevice",
                            "DisconnectDevice",
                            "Vocom_SendCANFrame",
                            "SendCANFrame",
                            "Vocom_SendSPICommand",
                            "SendSPICommand",
                            "Vocom_SendSCICommand",
                            "SendSCICommand",
                            "Vocom_SendIICCommand",
                            "SendIICCommand",
                            "Vocom_CheckPTTRunning",
                            "CheckPTTRunning",
                            "Vocom_DisconnectPTT",
                            "DisconnectPTT",
                            "Vocom_GetLastError",
                            "GetLastError"
                        };

                        foreach (string functionName in commonFunctionNames)
                        {
                            IntPtr procAddress = GetProcAddress(dllHandle, functionName);
                            if (procAddress != IntPtr.Zero)
                            {
                                exportedFunctions.Add(functionName);
                            }
                        }
                    });

                    _logger.LogInformation($"Found {exportedFunctions.Count} exported functions in DLL", "DllAnalyzer");
                }
                finally
                {
                    // Free the library
                    FreeLibrary(dllHandle);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error analyzing DLL exports: {ex.Message}", "DllAnalyzer");
            }

            return exportedFunctions;
        }

        /// <summary>
        /// Checks if a DLL is compatible with the current application
        /// </summary>
        /// <param name="dllPath">Path to the DLL</param>
        /// <returns>True if compatible, false otherwise</returns>
        public async Task<bool> IsDllCompatibleAsync(string dllPath)
        {
            try
            {
                _logger.LogInformation($"Checking DLL compatibility for: {dllPath}", "DllAnalyzer");

                if (!File.Exists(dllPath))
                {
                    _logger.LogError($"DLL file not found: {dllPath}", "DllAnalyzer");
                    return false;
                }

                // Get exported functions
                List<string> exportedFunctions = await ListExportedFunctionsAsync(dllPath);
                
                // Check if the DLL exports at least one of the required functions
                string[] requiredFunctions = new[]
                {
                    "Vocom_Initialize",
                    "Initialize",
                    "Vocom_Init",
                    "VocomInitialize"
                };

                foreach (string requiredFunction in requiredFunctions)
                {
                    if (exportedFunctions.Contains(requiredFunction))
                    {
                        _logger.LogInformation($"DLL is compatible, found required function: {requiredFunction}", "DllAnalyzer");
                        return true;
                    }
                }

                _logger.LogError("DLL is not compatible, no required functions found", "DllAnalyzer");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error checking DLL compatibility: {ex.Message}", "DllAnalyzer");
                return false;
            }
        }

        [DllImport("kernel32.dll")]
        private static extern IntPtr LoadLibrary(string dllToLoad);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string procedureName);

        [DllImport("kernel32.dll")]
        private static extern bool FreeLibrary(IntPtr hModule);
    }
}
