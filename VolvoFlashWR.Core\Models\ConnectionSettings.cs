using System;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents connection settings for the application
    /// </summary>
    public class ConnectionSettings
    {
        /// <summary>
        /// Preferred connection type for Vocom device
        /// </summary>
        public VocomConnectionType PreferredConnectionType { get; set; } = VocomConnectionType.USB;

        /// <summary>
        /// Timeout for connection attempts in milliseconds
        /// </summary>
        public int ConnectionTimeoutMs { get; set; } = 5000;

        /// <summary>
        /// Timeout for communication operations in milliseconds
        /// </summary>
        public int CommunicationTimeoutMs { get; set; } = 10000;

        /// <summary>
        /// Number of retry attempts for failed operations
        /// </summary>
        public int RetryAttempts { get; set; } = 3;

        /// <summary>
        /// Delay between retry attempts in milliseconds
        /// </summary>
        public int RetryDelayMs { get; set; } = 1000;

        /// <summary>
        /// Number of connection retry attempts
        /// </summary>
        public int ConnectionRetryAttempts { get; set; } = 3;

        /// <summary>
        /// Delay between connection retry attempts in milliseconds
        /// </summary>
        public int ConnectionRetryDelayMs { get; set; } = 1000;

        /// <summary>
        /// Whether to automatically reconnect on connection loss
        /// </summary>
        public bool AutoReconnect { get; set; } = true;

        /// <summary>
        /// Maximum number of auto-reconnect attempts
        /// </summary>
        public int MaxAutoReconnectAttempts { get; set; } = 5;

        /// <summary>
        /// Indicates if the application should automatically disconnect PTT application
        /// </summary>
        public bool AutoDisconnectPTT { get; set; } = true;

        /// <summary>
        /// Indicates if the application should automatically check for Bluetooth availability
        /// </summary>
        public bool AutoCheckBluetooth { get; set; } = true;

        /// <summary>
        /// Indicates if the application should use WiFi as a fallback connection method
        /// </summary>
        public bool UseWiFiFallback { get; set; } = true;

        /// <summary>
        /// Operating mode for the application
        /// </summary>
        public OperatingMode OperatingMode { get; set; } = OperatingMode.Bench;

        /// <summary>
        /// Indicates if detailed logging is enabled
        /// </summary>
        public bool EnableDetailedLogging { get; set; } = true;

        /// <summary>
        /// Path for log files
        /// </summary>
        public string LogFilePath { get; set; } = "Logs";

        /// <summary>
        /// Path for backup files
        /// </summary>
        public string BackupFilePath { get; set; } = "Backups";

        /// <summary>
        /// Baud rate for serial communication
        /// </summary>
        public int BaudRate { get; set; } = 115200;
    }

    /// <summary>
    /// Represents the operating mode of the application
    /// </summary>
    public enum OperatingMode
    {
        /// <summary>
        /// Open mode - alternative operating mode
        /// </summary>
        Open,

        /// <summary>
        /// Bench mode - primary operating mode for bench testing
        /// </summary>
        Bench
    }
}
