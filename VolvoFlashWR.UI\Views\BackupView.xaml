<Window x:Class="VolvoFlashWR.UI.Views.BackupView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:VolvoFlashWR.UI.Views"
        mc:Ignorable="d"
        Title="ECU Backup and Restore" Height="600" Width="800">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,10">
            <TextBlock Text="ECU Backup and Restore" FontSize="20" FontWeight="Bold" Margin="0,0,0,10"/>
            <TextBlock Text="{Binding StatusMessage}" Margin="0,0,0,5"/>
            <Separator/>
        </StackPanel>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Backup Creation -->
            <Border Grid.Column="0" BorderBrush="LightGray" BorderThickness="1" Margin="0,0,5,0" Padding="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="Create Backup" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>

                    <StackPanel Grid.Row="1" Margin="0,0,0,10">
                        <TextBlock Text="Selected ECU:" Margin="0,0,0,5"/>
                        <ComboBox ItemsSource="{Binding ConnectedECUs}" 
                                  SelectedItem="{Binding SelectedECU}"
                                  DisplayMemberPath="Name"
                                  Margin="0,0,0,5"/>
                    </StackPanel>

                    <StackPanel Grid.Row="2" Margin="0,0,0,10">
                        <TextBlock Text="Backup Description:" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding BackupDescription}" Margin="0,0,0,5"/>
                    </StackPanel>

                    <GroupBox Grid.Row="3" Header="Backup Options" Margin="0,0,0,10">
                        <StackPanel>
                            <CheckBox Content="Include EEPROM Data" IsChecked="{Binding IncludeEEPROM}" Margin="0,5,0,0"/>
                            <CheckBox Content="Include Microcontroller Code" IsChecked="{Binding IncludeMicrocontrollerCode}" Margin="0,5,0,0"/>
                            <CheckBox Content="Include Parameters" IsChecked="{Binding IncludeParameters}" Margin="0,5,0,0"/>
                        </StackPanel>
                    </GroupBox>

                    <Button Grid.Row="4" Content="Create Backup" Command="{Binding CreateBackupCommand}" Margin="0,0,0,10" Padding="5"/>

                    <ProgressBar Grid.Row="5" IsIndeterminate="{Binding IsCreatingBackup}" Visibility="{Binding IsCreatingBackup, Converter={StaticResource BooleanToVisibilityConverter}}" Height="10" VerticalAlignment="Top"/>
                </Grid>
            </Border>

            <!-- Right Panel - Backup Management -->
            <Border Grid.Column="1" BorderBrush="LightGray" BorderThickness="1" Margin="5,0,0,0" Padding="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="Manage Backups" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>

                    <ListView Grid.Row="1" ItemsSource="{Binding Backups}" SelectedItem="{Binding SelectedBackup}" Margin="0,0,0,10">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="ECU Name" DisplayMemberBinding="{Binding ECUName}" Width="100"/>
                                <GridViewColumn Header="Serial Number" DisplayMemberBinding="{Binding ECUSerialNumber}" Width="100"/>
                                <GridViewColumn Header="Creation Time" DisplayMemberBinding="{Binding CreationTime}" Width="150"/>
                                <GridViewColumn Header="Description" DisplayMemberBinding="{Binding Description}" Width="150"/>
                            </GridView>
                        </ListView.View>
                    </ListView>

                    <GroupBox Grid.Row="2" Header="Restore Options" Margin="0,0,0,10">
                        <StackPanel>
                            <CheckBox Content="Restore EEPROM Data" IsChecked="{Binding RestoreEEPROM}" Margin="0,5,0,0"/>
                            <CheckBox Content="Restore Microcontroller Code" IsChecked="{Binding RestoreMicrocontrollerCode}" Margin="0,5,0,0"/>
                            <CheckBox Content="Restore Parameters" IsChecked="{Binding RestoreParameters}" Margin="0,5,0,0"/>
                        </StackPanel>
                    </GroupBox>

                    <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Content="Restore Backup" Command="{Binding RestoreBackupCommand}" Margin="0,0,5,0" Padding="5"/>
                        <Button Content="Delete Backup" Command="{Binding DeleteBackupCommand}" Margin="5,0,5,0" Padding="5"/>
                        <Button Content="Import Backup" Command="{Binding ImportBackupCommand}" Margin="5,0,5,0" Padding="5"/>
                        <Button Content="Export Backup" Command="{Binding ExportBackupCommand}" Margin="5,0,5,0" Padding="5"/>
                        <Button Content="Refresh" Command="{Binding RefreshBackupsCommand}" Margin="5,0,5,0" Padding="5"/>
                        <Button Content="Compare" Command="{Binding CompareBackupsCommand}" Margin="5,0,0,0" Padding="5"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- Footer -->
        <StackPanel Grid.Row="2" Margin="0,10,0,0">
            <Separator Margin="0,0,0,5"/>
            <TextBlock Text="Backup Directory:" Margin="0,0,0,5"/>
            <TextBlock Text="{Binding BackupService.BackupDirectoryPath}" Margin="0,0,0,5"/>
            <ProgressBar IsIndeterminate="{Binding IsRestoringBackup}" Visibility="{Binding IsRestoringBackup, Converter={StaticResource BooleanToVisibilityConverter}}" Height="10"/>
        </StackPanel>
    </Grid>
</Window>
