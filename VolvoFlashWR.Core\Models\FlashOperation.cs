using System;
using System.Collections.Generic;

namespace VolvoFlashWR.Core.Models
{
    /// <summary>
    /// Represents a flash memory operation
    /// </summary>
    public class FlashOperation
    {
        private readonly List<PerformanceMetric> _performanceMetrics = new List<PerformanceMetric>();

        /// <summary>
        /// Gets the unique identifier for this operation
        /// </summary>
        public Guid Id { get; }

        /// <summary>
        /// Gets the type of flash operation
        /// </summary>
        public FlashOperationType OperationType { get; }

        /// <summary>
        /// Gets the starting address of the operation
        /// </summary>
        public uint Address { get; }

        /// <summary>
        /// Gets the size of the operation in bytes
        /// </summary>
        public int Size { get; }

        /// <summary>
        /// Gets or sets the number of bytes processed so far
        /// </summary>
        public int BytesProcessed { get; set; }

        /// <summary>
        /// Gets or sets the current status of the operation
        /// </summary>
        public FlashOperationStatus Status { get; set; }

        /// <summary>
        /// Gets the start time of the operation
        /// </summary>
        public DateTime StartTime { get; }

        /// <summary>
        /// Gets or sets the end time of the operation
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// Gets or sets the time of the last update to this operation
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// Gets or sets the error message if the operation failed
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets additional information about the operation
        /// </summary>
        public string AdditionalInfo { get; set; }

        /// <summary>
        /// Gets the number of retries that have been attempted for this operation
        /// </summary>
        public int RetryCount
        {
            get
            {
                if (string.IsNullOrEmpty(AdditionalInfo))
                {
                    return 0;
                }

                var parts = AdditionalInfo.Split(';');
                foreach (var part in parts)
                {
                    if (part.Trim().StartsWith("RetryCount:", StringComparison.OrdinalIgnoreCase))
                    {
                        if (int.TryParse(part.Trim().Substring("RetryCount:".Length), out int count))
                        {
                            return count;
                        }
                    }
                }

                return 0;
            }
        }

        /// <summary>
        /// Gets the performance metrics for this operation
        /// </summary>
        public IReadOnlyList<PerformanceMetric> PerformanceMetrics => _performanceMetrics.AsReadOnly();

        /// <summary>
        /// Gets the progress of the operation as a percentage (0-100)
        /// </summary>
        public double ProgressPercentage => Size > 0 ? (double)BytesProcessed / Size * 100 : 0;

        /// <summary>
        /// Gets the elapsed time of the operation
        /// </summary>
        public TimeSpan ElapsedTime => (EndTime ?? DateTime.Now) - StartTime;

        /// <summary>
        /// Gets the current throughput in bytes per second
        /// </summary>
        public double CurrentThroughput
        {
            get
            {
                if (BytesProcessed <= 0 || ElapsedTime.TotalSeconds <= 0)
                {
                    return 0;
                }

                return BytesProcessed / ElapsedTime.TotalSeconds;
            }
        }

        /// <summary>
        /// Gets the average throughput in bytes per second
        /// </summary>
        public double AverageThroughput
        {
            get
            {
                if (_performanceMetrics.Count == 0)
                {
                    return CurrentThroughput;
                }

                double totalThroughput = 0;
                foreach (var metric in _performanceMetrics)
                {
                    totalThroughput += metric.Throughput;
                }

                return totalThroughput / _performanceMetrics.Count;
            }
        }

        /// <summary>
        /// Gets the peak throughput in bytes per second
        /// </summary>
        public double PeakThroughput
        {
            get
            {
                if (_performanceMetrics.Count == 0)
                {
                    return CurrentThroughput;
                }

                double peak = 0;
                foreach (var metric in _performanceMetrics)
                {
                    if (metric.Throughput > peak)
                    {
                        peak = metric.Throughput;
                    }
                }

                return peak;
            }
        }

        /// <summary>
        /// Gets the estimated time remaining for the operation in seconds
        /// </summary>
        public double EstimatedTimeRemaining
        {
            get
            {
                if (BytesProcessed <= 0 || Size <= 0 || Status == FlashOperationStatus.Completed || Status == FlashOperationStatus.Failed)
                {
                    return 0;
                }

                // Calculate bytes per second using a weighted average of recent throughput
                double bytesPerSecond = CalculateWeightedThroughput();
                if (bytesPerSecond <= 0)
                {
                    return 0;
                }

                // Calculate remaining time
                return (Size - BytesProcessed) / bytesPerSecond;
            }
        }

        /// <summary>
        /// Initializes a new instance of the FlashOperation class
        /// </summary>
        /// <param name="operationType">The type of flash operation</param>
        /// <param name="address">The starting address of the operation</param>
        /// <param name="size">The size of the operation in bytes</param>
        public FlashOperation(FlashOperationType operationType, uint address, int size)
        {
            Id = Guid.NewGuid();
            OperationType = operationType;
            Address = address;
            Size = size;
            BytesProcessed = 0;
            Status = FlashOperationStatus.Pending;
            StartTime = DateTime.Now;
            LastUpdated = StartTime;
        }

        /// <summary>
        /// Adds a performance metric to the operation
        /// </summary>
        /// <param name="bytesProcessed">The number of bytes processed</param>
        /// <param name="elapsedTime">The elapsed time in milliseconds</param>
        public void AddPerformanceMetric(int bytesProcessed, double elapsedTime)
        {
            if (bytesProcessed <= 0 || elapsedTime <= 0)
            {
                return;
            }

            double throughput = bytesProcessed / (elapsedTime / 1000.0); // Convert to bytes per second
            _performanceMetrics.Add(new PerformanceMetric(bytesProcessed, elapsedTime, throughput, DateTime.Now));
        }

        /// <summary>
        /// Calculates a weighted average of recent throughput
        /// </summary>
        /// <returns>The weighted average throughput in bytes per second</returns>
        private double CalculateWeightedThroughput()
        {
            if (_performanceMetrics.Count == 0)
            {
                return CurrentThroughput;
            }

            // Use a weighted average of the last few metrics, with more recent metrics weighted more heavily
            double totalWeight = 0;
            double weightedThroughput = 0;

            // Get the last 5 metrics or all metrics if fewer than 5
            int count = Math.Min(5, _performanceMetrics.Count);
            var recentMetrics = _performanceMetrics.GetRange(_performanceMetrics.Count - count, count);

            for (int i = 0; i < recentMetrics.Count; i++)
            {
                // Weight is higher for more recent metrics
                double weight = i + 1;
                totalWeight += weight;
                weightedThroughput += recentMetrics[i].Throughput * weight;
            }

            return weightedThroughput / totalWeight;
        }
    }

    /// <summary>
    /// Represents a performance metric for a flash operation
    /// </summary>
    public class PerformanceMetric
    {
        /// <summary>
        /// Gets the number of bytes processed
        /// </summary>
        public int BytesProcessed { get; }

        /// <summary>
        /// Gets the elapsed time in milliseconds
        /// </summary>
        public double ElapsedTime { get; }

        /// <summary>
        /// Gets the throughput in bytes per second
        /// </summary>
        public double Throughput { get; }

        /// <summary>
        /// Gets the time when the metric was recorded
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// Initializes a new instance of the PerformanceMetric class
        /// </summary>
        /// <param name="bytesProcessed">The number of bytes processed</param>
        /// <param name="elapsedTime">The elapsed time in milliseconds</param>
        /// <param name="throughput">The throughput in bytes per second</param>
        /// <param name="timestamp">The time when the metric was recorded</param>
        public PerformanceMetric(int bytesProcessed, double elapsedTime, double throughput, DateTime timestamp)
        {
            BytesProcessed = bytesProcessed;
            ElapsedTime = elapsedTime;
            Throughput = throughput;
            Timestamp = timestamp;
        }
    }

    /// <summary>
    /// Represents the type of flash operation
    /// </summary>
    public enum FlashOperationType
    {
        /// <summary>
        /// Reading from flash memory
        /// </summary>
        Read,

        /// <summary>
        /// Writing to flash memory
        /// </summary>
        Write,

        /// <summary>
        /// Erasing flash memory
        /// </summary>
        Erase,

        /// <summary>
        /// Verifying flash memory
        /// </summary>
        Verify,

        /// <summary>
        /// Checking flash memory health
        /// </summary>
        HealthCheck
    }

    /// <summary>
    /// Represents the status of a flash operation
    /// </summary>
    public enum FlashOperationStatus
    {
        /// <summary>
        /// The operation is pending
        /// </summary>
        Pending,

        /// <summary>
        /// The operation is in progress
        /// </summary>
        InProgress,

        /// <summary>
        /// The operation is paused
        /// </summary>
        Paused,

        /// <summary>
        /// The operation appears to be stalled
        /// </summary>
        Stalled,

        /// <summary>
        /// The operation completed successfully
        /// </summary>
        Completed,

        /// <summary>
        /// The operation failed
        /// </summary>
        Failed
    }
}
