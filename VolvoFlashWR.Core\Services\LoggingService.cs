using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Utilities;
using CoreLogLevel = VolvoFlashWR.Core.Interfaces.LogLevel;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Implementation of the logging service
    /// </summary>
    public class LoggingService : ILoggingService
    {
        #region Private Fields

        private string _logFilePath;
        private bool _detailedLoggingEnabled;
        private List<LogEntry> _logEntries;
        private readonly object _logLock = new object();

        #endregion

        #region Events

        /// <summary>
        /// Event triggered when a log entry is added
        /// </summary>
        public event EventHandler<LogEntry> LogEntryAdded;

        #endregion

        #region Properties

        /// <summary>
        /// Gets the log file path
        /// </summary>
        public string LogFilePath => _logFilePath;

        /// <summary>
        /// Gets whether detailed logging is enabled
        /// </summary>
        public bool DetailedLoggingEnabled => _detailedLoggingEnabled;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the LoggingService class
        /// </summary>
        public LoggingService()
        {
            _logEntries = new List<LogEntry>();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Initializes the logging service
        /// </summary>
        /// <param name="logFilePath">Path to the log file</param>
        /// <param name="enableDetailedLogging">Whether to enable detailed logging</param>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public async Task<bool> InitializeAsync(string logFilePath, bool enableDetailedLogging)
        {
            try
            {
                _logFilePath = logFilePath;
                _detailedLoggingEnabled = enableDetailedLogging;

                // Create the log directory if it doesn't exist
                if (!Directory.Exists(_logFilePath))
                {
                    Directory.CreateDirectory(_logFilePath);
                }

                // Create a log file for this session
                string logFileName = $"Log_{DateTime.Now:yyyyMMdd_HHmmss}.log";
                string fullPath = Path.Combine(_logFilePath, logFileName);

                // Write a header to the log file
                await File.WriteAllTextAsync(fullPath, $"Log started at {DateTime.Now}\r\n");

                // Log the initialization
                LogInformation("Logging service initialized", "LoggingService");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing logging service: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Logs an information message
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        public void LogInformation(string message, string source = "")
        {
            LogMessage(message, source, CoreLogLevel.Information);
        }

        /// <summary>
        /// Logs a warning message
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        public void LogWarning(string message, string source = "")
        {
            LogMessage(message, source, CoreLogLevel.Warning);
        }

        /// <summary>
        /// Logs an error message
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        /// <param name="exception">The exception associated with the error</param>
        public void LogError(string message, string source = "", Exception exception = null)
        {
            string fullMessage = message;
            if (exception != null)
            {
                fullMessage += $" Exception: {exception.Message}";
                if (exception.StackTrace != null)
                {
                    fullMessage += $" StackTrace: {exception.StackTrace}";
                }
            }

            // Create a log entry with the exception
            var entry = new LogEntry
            {
                Id = Guid.NewGuid().ToString(),
                Timestamp = DateTime.Now,
                Level = CoreLogLevel.Error,
                Source = source,
                Message = fullMessage,
                Exception = exception
            };

            // Add the entry to the in-memory log
            lock (_logLock)
            {
                _logEntries.Add(entry);
            }

            // Write the entry to the log file
            WriteToLogFile(entry);

            // Raise the event
            LogEntryAdded?.Invoke(this, entry);
        }

        /// <summary>
        /// Logs an error message using the Core.Models.LogLevel enum
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        /// <param name="exception">The exception associated with the error</param>
        public void LogErrorWithModelLogLevel(string message, string source = "", Exception exception = null)
        {
            // Use the standard LogError method
            LogError(message, source, exception);
        }

        /// <summary>
        /// Logs a debug message (only if detailed logging is enabled)
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        public void LogDebug(string message, string source = "")
        {
            if (_detailedLoggingEnabled)
            {
                LogMessage(message, source, CoreLogLevel.Debug);
            }
        }

        /// <summary>
        /// Gets all log entries
        /// </summary>
        /// <returns>List of all log entries</returns>
        public async Task<List<LogEntry>> GetAllLogEntriesAsync()
        {
            return await Task.FromResult(_logEntries.ToList());
        }

        /// <summary>
        /// Gets log entries filtered by log level
        /// </summary>
        /// <param name="level">The log level to filter by</param>
        /// <returns>List of filtered log entries</returns>
        public async Task<List<LogEntry>> GetLogEntriesByLevelAsync(CoreLogLevel level)
        {
            return await Task.FromResult(_logEntries.Where(e => e.Level == level).ToList());
        }

        /// <summary>
        /// Gets log entries filtered by source
        /// </summary>
        /// <param name="source">The source to filter by</param>
        /// <returns>List of filtered log entries</returns>
        public async Task<List<LogEntry>> GetLogEntriesBySourceAsync(string source)
        {
            return await Task.FromResult(_logEntries.Where(e => e.Source == source).ToList());
        }

        /// <summary>
        /// Gets log entries filtered by date range
        /// </summary>
        /// <param name="startDate">The start date</param>
        /// <param name="endDate">The end date</param>
        /// <returns>List of filtered log entries</returns>
        public async Task<List<LogEntry>> GetLogEntriesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await Task.FromResult(_logEntries.Where(e => e.Timestamp >= startDate && e.Timestamp <= endDate).ToList());
        }

        /// <summary>
        /// Clears all log entries
        /// </summary>
        /// <returns>True if clearing is successful, false otherwise</returns>
        public async Task<bool> ClearLogEntriesAsync()
        {
            try
            {
                lock (_logLock)
                {
                    _logEntries.Clear();
                }
                return await Task.FromResult(true);
            }
            catch
            {
                return await Task.FromResult(false);
            }
        }

        /// <summary>
        /// Exports log entries to a file
        /// </summary>
        /// <param name="filePath">The path to export to</param>
        /// <returns>True if export is successful, false otherwise</returns>
        public async Task<bool> ExportLogEntriesAsync(string filePath)
        {
            try
            {
                // Create a string with all log entries
                string logText = string.Join(Environment.NewLine, _logEntries.Select(e => $"{e.Timestamp} [{e.Level}] {e.Source}: {e.Message}"));

                // Write the log text to the file
                await File.WriteAllTextAsync(filePath, logText);

                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Logs a message with the specified level
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        /// <param name="level">The log level</param>
        private void LogMessage(string message, string source, CoreLogLevel level)
        {
            // Create a log entry
            var entry = new LogEntry
            {
                Id = Guid.NewGuid().ToString(),
                Timestamp = DateTime.Now,
                Level = level,
                Source = source,
                Message = message,
                Exception = null
            };

            // Add the entry to the in-memory log
            lock (_logLock)
            {
                _logEntries.Add(entry);
            }

            // Write the entry to the log file
            WriteToLogFile(entry);

            // Raise the event
            LogEntryAdded?.Invoke(this, entry);
        }

        /// <summary>
        /// Logs a message with the specified level using the Core.Models.LogLevel enum
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="source">The source of the message</param>
        /// <param name="level">The log level from Core.Models.LogLevel</param>
        public void LogMessageWithModelLogLevel(string message, string source, Models.LogLevel level)
        {
            // Convert the model log level to core log level
            CoreLogLevel coreLogLevel = LogLevelConverter.ToCoreLogLevel(level);

            // Log the message with the converted log level
            LogMessage(message, source, coreLogLevel);
        }

        /// <summary>
        /// Writes a log entry to the log file
        /// </summary>
        /// <param name="entry">The log entry to write</param>
        private void WriteToLogFile(LogEntry entry)
        {
            try
            {
                if (string.IsNullOrEmpty(_logFilePath))
                {
                    return;
                }

                // Get the current log file
                string[] logFiles = Directory.GetFiles(_logFilePath, "Log_*.log");
                if (logFiles.Length == 0)
                {
                    return;
                }

                string logFile = logFiles.OrderByDescending(f => f).First();

                // Format the log entry
                string logLine = $"{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{entry.Level}] {entry.Source}: {entry.Message}{Environment.NewLine}";

                // Write to the log file
                File.AppendAllText(logFile, logLine);
            }
            catch
            {
                // Ignore errors when writing to the log file
            }
        }

        #endregion
    }


}
