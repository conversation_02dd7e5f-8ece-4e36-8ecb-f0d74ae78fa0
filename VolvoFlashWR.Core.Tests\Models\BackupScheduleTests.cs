using System;
using System.Collections.Generic;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Tests.Models
{
    [TestFixture]
    public class BackupScheduleTests
    {
        [Test]
        public void BackupSchedule_DefaultValues_AreCorrect()
        {
            // Arrange & Act
            var schedule = new BackupSchedule();
            var today = DateTime.Today;

            // Assert
            Assert.That(schedule.Id, Is.Not.Null.Or.Empty);
            Assert.That(schedule.IsEnabled, Is.True);
            Assert.That(schedule.FrequencyType, Is.EqualTo(BackupFrequencyType.Daily));
            Assert.That(schedule.Interval, Is.EqualTo(1));
            Assert.That(schedule.TimeOfDay, Is.EqualTo(new TimeSpan(3, 0, 0)));
            Assert.That(schedule.DaysOfWeek, Has.Count.EqualTo(1));
            Assert.That(schedule.DaysOfWeek[0], Is.EqualTo(DayOfWeek.Monday));
            Assert.That(schedule.DayOfMonth, Is.EqualTo(1));
            Assert.That(schedule.StartDate.Date, Is.EqualTo(today));
            Assert.That(schedule.EndDate, Is.Null);
            Assert.That(schedule.LastExecutionTime, Is.Null);
            Assert.That(schedule.Tags, Is.Not.Null);
            Assert.That(schedule.IncludeEEPROM, Is.True);
            Assert.That(schedule.IncludeMicrocontrollerCode, Is.True);
            Assert.That(schedule.IncludeParameters, Is.True);
            Assert.That(schedule.MaxBackupsToKeep, Is.EqualTo(0));
            Assert.That(schedule.CreatedBackupIds, Is.Not.Null);
        }

        [Test]
        public void BackupSchedule_CustomValues_AreCorrect()
        {
            // Arrange
            string id = "Schedule123";
            string name = "Daily Backup";
            string description = "Daily backup of ECU";
            string ecuId = "ECU123";
            string ecuName = "TestECU";
            bool isEnabled = false;
            BackupFrequencyType frequencyType = BackupFrequencyType.Weekly;
            int interval = 2;
            TimeSpan timeOfDay = new TimeSpan(14, 30, 0); // 2:30 PM
            var daysOfWeek = new List<DayOfWeek> { DayOfWeek.Monday, DayOfWeek.Wednesday, DayOfWeek.Friday };
            int dayOfMonth = 15;
            DateTime startDate = new DateTime(2023, 1, 1);
            DateTime? endDate = new DateTime(2023, 12, 31);
            DateTime? lastExecutionTime = new DateTime(2023, 1, 15);
            DateTime nextExecutionTime = new DateTime(2023, 1, 22);
            string category = "Production";
            var tags = new List<string> { "Tag1", "Tag2" };
            bool includeEEPROM = false;
            bool includeMicrocontrollerCode = true;
            bool includeParameters = false;
            int maxBackupsToKeep = 5;
            var createdBackupIds = new List<string> { "Backup1", "Backup2" };

            // Act
            var schedule = new BackupSchedule
            {
                Id = id,
                Name = name,
                Description = description,
                ECUId = ecuId,
                ECUName = ecuName,
                IsEnabled = isEnabled,
                FrequencyType = frequencyType,
                Interval = interval,
                TimeOfDay = timeOfDay,
                DaysOfWeek = daysOfWeek,
                DayOfMonth = dayOfMonth,
                StartDate = startDate,
                EndDate = endDate,
                LastExecutionTime = lastExecutionTime,
                NextExecutionTime = nextExecutionTime,
                Category = category,
                Tags = tags,
                IncludeEEPROM = includeEEPROM,
                IncludeMicrocontrollerCode = includeMicrocontrollerCode,
                IncludeParameters = includeParameters,
                MaxBackupsToKeep = maxBackupsToKeep,
                CreatedBackupIds = createdBackupIds
            };

            // Assert
            Assert.That(schedule.Id, Is.EqualTo(id));
            Assert.That(schedule.Name, Is.EqualTo(name));
            Assert.That(schedule.Description, Is.EqualTo(description));
            Assert.That(schedule.ECUId, Is.EqualTo(ecuId));
            Assert.That(schedule.ECUName, Is.EqualTo(ecuName));
            Assert.That(schedule.IsEnabled, Is.EqualTo(isEnabled));
            Assert.That(schedule.FrequencyType, Is.EqualTo(frequencyType));
            Assert.That(schedule.Interval, Is.EqualTo(interval));
            Assert.That(schedule.TimeOfDay, Is.EqualTo(timeOfDay));
            Assert.That(schedule.DaysOfWeek, Is.EqualTo(daysOfWeek));
            Assert.That(schedule.DayOfMonth, Is.EqualTo(dayOfMonth));
            Assert.That(schedule.StartDate, Is.EqualTo(startDate));
            Assert.That(schedule.EndDate, Is.EqualTo(endDate));
            Assert.That(schedule.LastExecutionTime, Is.EqualTo(lastExecutionTime));
            Assert.That(schedule.NextExecutionTime, Is.EqualTo(nextExecutionTime));
            Assert.That(schedule.Category, Is.EqualTo(category));
            Assert.That(schedule.Tags, Is.EqualTo(tags));
            Assert.That(schedule.IncludeEEPROM, Is.EqualTo(includeEEPROM));
            Assert.That(schedule.IncludeMicrocontrollerCode, Is.EqualTo(includeMicrocontrollerCode));
            Assert.That(schedule.IncludeParameters, Is.EqualTo(includeParameters));
            Assert.That(schedule.MaxBackupsToKeep, Is.EqualTo(maxBackupsToKeep));
            Assert.That(schedule.CreatedBackupIds, Is.EqualTo(createdBackupIds));
        }
    }
}

