using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.UI.Models;
using VolvoFlashWR.UI.ViewModels;

namespace VolvoFlashWR.UI.Tests.ViewModels
{
    [TestFixture]
    public class EnhancedDiagnosticsViewModelTests
    {
        private Mock<ILoggingService> _mockLoggingService;
        private Mock<IECUCommunicationService> _mockEcuCommunicationService;
        private EnhancedDiagnosticsViewModel _viewModel;

        [SetUp]
        public void Setup()
        {
            _mockLoggingService = new Mock<ILoggingService>();
            _mockEcuCommunicationService = new Mock<IECUCommunicationService>();
            
            // Setup mock ECU communication service
            _mockEcuCommunicationService.Setup(m => m.ConnectedECUs).Returns(new List<ECUDevice>());
            
            _viewModel = new EnhancedDiagnosticsViewModel(_mockLoggingService.Object, _mockEcuCommunicationService.Object);
        }

        [Test]
        public void Constructor_InitializesChartCollections()
        {
            // Assert
            Assert.That(_viewModel.ChartSeries, Is.Not.Null);
            Assert.That(_viewModel.ParameterSeries, Is.Not.Null);
            Assert.That(_viewModel.XAxes, Is.Not.Null);
            Assert.That(_viewModel.YAxes, Is.Not.Null);
            Assert.That(_viewModel.ThresholdConfigurations, Is.Not.Null);
        }

        [Test]
        public void AddParameterToChart_AddsToChartSeries()
        {
            // Arrange
            var ecuDevice = new ECUDevice
            {
                Name = "Test ECU",
                Parameters = new Dictionary<string, object> { { "TestParam", 100 } }
            };
            _mockEcuCommunicationService.Setup(m => m.ConnectedECUs).Returns(new List<ECUDevice> { ecuDevice });
            _viewModel.SelectedECUForDiagnostics = ecuDevice;
            _viewModel.MonitorableParameters.Add("TestParam");
            _viewModel.SelectedParameterForChart = "TestParam";

            // Act
            var command = _viewModel.AddParameterToChartCommand;
            command.Execute(null);

            // Assert
            Assert.That(_viewModel.SelectedParametersForMonitoring, Contains.Item("TestParam"));
            Assert.That(_viewModel.ChartSeries.Count, Is.EqualTo(1));
            Assert.That(_viewModel.ParameterSeries.Count, Is.EqualTo(1));
            Assert.That(_viewModel.ThresholdConfigurations.ContainsKey("TestParam"), Is.True);
        }

        [Test]
        public void RemoveParameterFromChart_RemovesFromChartSeries()
        {
            // Arrange
            var ecuDevice = new ECUDevice
            {
                Name = "Test ECU",
                Parameters = new Dictionary<string, object> { { "TestParam", 100 } }
            };
            _mockEcuCommunicationService.Setup(m => m.ConnectedECUs).Returns(new List<ECUDevice> { ecuDevice });
            _viewModel.SelectedECUForDiagnostics = ecuDevice;
            _viewModel.MonitorableParameters.Add("TestParam");
            _viewModel.SelectedParameterForChart = "TestParam";
            
            // Add parameter to chart
            _viewModel.AddParameterToChartCommand.Execute(null);
            
            // Act
            _viewModel.RemoveParameterFromChartCommand.Execute(null);

            // Assert
            Assert.That(_viewModel.SelectedParametersForMonitoring, Does.Not.Contain("TestParam"));
            Assert.That(_viewModel.ChartSeries.Count, Is.EqualTo(0));
            Assert.That(_viewModel.ParameterSeries.Count, Is.EqualTo(0));
        }

        [Test]
        public void ClearMonitoringData_ClearsAllCollections()
        {
            // Arrange
            var ecuDevice = new ECUDevice
            {
                Name = "Test ECU",
                Parameters = new Dictionary<string, object> { { "TestParam", 100 } }
            };
            _mockEcuCommunicationService.Setup(m => m.ConnectedECUs).Returns(new List<ECUDevice> { ecuDevice });
            _viewModel.SelectedECUForDiagnostics = ecuDevice;
            _viewModel.MonitorableParameters.Add("TestParam");
            _viewModel.SelectedParameterForChart = "TestParam";
            
            // Add parameter to chart
            _viewModel.AddParameterToChartCommand.Execute(null);
            
            // Add some monitoring data
            _viewModel.MonitoringData.Add(new MonitoringDataPoint("TestParam", "100", "unit"));
            
            // Act
            _viewModel.ClearMonitoringDataCommand.Execute(null);

            // Assert
            Assert.That(_viewModel.MonitoringData.Count, Is.EqualTo(0));
            
            // Note: We can't directly test the chart series values because they're updated on the UI thread
            // But we can verify the command was called
            _mockLoggingService.Verify(m => m.LogInformation(It.Is<string>(s => s.Contains("cleared")), It.IsAny<string>()));
        }

        [Test]
        public void ThresholdConfiguration_DeterminesCorrectStatus()
        {
            // Arrange
            var thresholdConfig = new ThresholdConfiguration("TestParam");
            thresholdConfig.SetNormalRange(0, 100);
            thresholdConfig.SetWarningRange(-10, 110);
            thresholdConfig.SetErrorRange(-20, 120);

            // Act & Assert
            Assert.That(thresholdConfig.GetStatus(50), Is.EqualTo("Normal"));
            Assert.That(thresholdConfig.GetStatus(105), Is.EqualTo("Warning"));
            Assert.That(thresholdConfig.GetStatus(115), Is.EqualTo("Error"));
        }
    }
}

