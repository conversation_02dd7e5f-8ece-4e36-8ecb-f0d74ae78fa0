using System;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Input;
using VolvoFlashWR.UI.ViewModels;

namespace VolvoFlashWR.UI.Views
{
    /// <summary>
    /// Interaction logic for BackupSchedulerView.xaml
    /// </summary>
    public partial class BackupSchedulerView : Window
    {
        public BackupSchedulerView(BackupSchedulerViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            
            // Subscribe to close request from view model
            if (viewModel != null)
            {
                viewModel.CloseRequested += (s, e) => Close();
            }
        }
        
        /// <summary>
        /// Validates that input is a number
        /// </summary>
        private void NumberValidationTextBox(object sender, TextCompositionEventArgs e)
        {
            Regex regex = new Regex("[^0-9]+");
            e.Handled = regex.IsMatch(e.Text);
        }
    }
}
