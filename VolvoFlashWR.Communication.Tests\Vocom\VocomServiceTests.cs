using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Vocom;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Utilities;

namespace VolvoFlashWR.Communication.Tests.Vocom
{
    [TestFixture]
    public class VocomServiceTests
    {
        private VocomService _vocomService;
        private Mock<ILoggingService> _mockLogger;
        private Mock<IVocomDeviceDriver> _mockVocomDriver;
        private Mock<IUSBCommunicationService> _mockUSBService;
        private Mock<IWiFiCommunicationService> _mockWiFiService;
        private Mock<IBluetoothCommunicationService> _mockBluetoothService;

        [SetUp]
        public void Setup()
        {
            // Create mocks
            _mockLogger = new Mock<ILoggingService>();
            _mockVocomDriver = new Mock<IVocomDeviceDriver>();
            _mockUSBService = new Mock<IUSBCommunicationService>();
            _mockWiFiService = new Mock<IWiFiCommunicationService>();
            _mockBluetoothService = new Mock<IBluetoothCommunicationService>();

            // Create the Vocom service
            _vocomService = new VocomService(_mockLogger.Object);

            // Initialize the service with mocked dependencies
            _vocomService.InitializeAsync(_mockVocomDriver.Object, _mockUSBService.Object, _mockWiFiService.Object, _mockBluetoothService.Object).Wait();
        }

        [Test]
        public async Task DetectVocomDevicesAsync_DevicesFound_ReturnsDevices()
        {
            // Arrange
            var testDevices = new List<VocomDevice>
            {
                new VocomDevice
                {
                    Id = "vocom1",
                    Name = "Vocom 1",
                    SerialNumber = "SN12345",
                    FirmwareVersion = "1.0",
                    ConnectionType = VocomConnectionType.USB
                },
                new VocomDevice
                {
                    Id = "vocom2",
                    Name = "Vocom 2",
                    SerialNumber = "SN67890",
                    FirmwareVersion = "1.1",
                    ConnectionType = VocomConnectionType.WiFi
                }
            };

            _mockVocomDriver.Setup(d => d.DetectDevicesAsync()).ReturnsAsync(testDevices);

            // Act
            var result = await _vocomService.DetectVocomDevicesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result[0].Id, Is.EqualTo("vocom1"));
            Assert.That(result[1].Id, Is.EqualTo("vocom2"));
        }

        [Test]
        public async Task ConnectToVocomDeviceAsync_ValidDevice_ConnectsSuccessfully()
        {
            // Arrange
            var device = new VocomDevice
            {
                Id = "vocom1",
                Name = "Vocom 1",
                SerialNumber = "SN12345",
                FirmwareVersion = "1.0",
                ConnectionType = VocomConnectionType.USB
            };

            _mockVocomDriver.Setup(d => d.ConnectToDeviceAsync(It.IsAny<VocomDevice>())).ReturnsAsync(true);

            // Act
            bool result = await _vocomService.ConnectToVocomDeviceAsync(device);

            // Assert
            Assert.That(result, Is.True);
            Assert.That(_vocomService.ConnectedDevices, Contains.Item(device));
        }

        [Test]
        public async Task ConnectToVocomDeviceAsync_InvalidDevice_ReturnsFalse()
        {
            // Arrange
            var device = new VocomDevice
            {
                Id = "vocom1",
                Name = "Vocom 1",
                SerialNumber = "SN12345",
                FirmwareVersion = "1.0",
                ConnectionType = VocomConnectionType.USB
            };

            _mockVocomDriver.Setup(d => d.ConnectToDeviceAsync(It.IsAny<VocomDevice>())).ReturnsAsync(false);

            // Act
            bool result = await _vocomService.ConnectToVocomDeviceAsync(device);

            // Assert
            Assert.That(result, Is.False);
            Assert.That(_vocomService.ConnectedDevices, Does.Not.Contain(device));
        }

        [Test]
        public async Task DisconnectFromVocomDeviceAsync_ConnectedDevice_DisconnectsSuccessfully()
        {
            // Arrange
            var device = new VocomDevice
            {
                Id = "vocom1",
                Name = "Vocom 1",
                SerialNumber = "SN12345",
                FirmwareVersion = "1.0",
                ConnectionType = VocomConnectionType.USB
            };

            _mockVocomDriver.Setup(d => d.ConnectToDeviceAsync(It.IsAny<VocomDevice>())).ReturnsAsync(true);
            _mockVocomDriver.Setup(d => d.DisconnectFromDeviceAsync(It.IsAny<VocomDevice>())).ReturnsAsync(true);

            // Connect first
            await _vocomService.ConnectToVocomDeviceAsync(device);

            // Act
            bool result = await _vocomService.DisconnectFromVocomDeviceAsync(device);

            // Assert
            Assert.That(result, Is.True);
            Assert.That(_vocomService.ConnectedDevices, Does.Not.Contain(device));
        }

        [Test]
        public async Task FallbackToWiFiAsync_WhenUSBFails_ConnectsViaWiFi()
        {
            // Arrange
            var device = new VocomDevice
            {
                Id = "vocom1",
                Name = "Vocom 1",
                SerialNumber = "SN12345",
                FirmwareVersion = "1.0",
                ConnectionType = VocomConnectionType.USB,
                IPAddress = "*************"
            };

            // USB connection fails
            _mockVocomDriver.Setup(d => d.ConnectToDeviceAsync(It.IsAny<VocomDevice>())).ReturnsAsync(false);

            // WiFi connection succeeds
            _mockWiFiService.Setup(w => w.ConnectToDeviceAsync(It.IsAny<string>(), It.IsAny<int>())).ReturnsAsync(true);

            // Act
            bool result = await _vocomService.ConnectToVocomDeviceWithFallbackAsync(device);

            // Assert
            Assert.That(result, Is.True);
            Assert.That(device.ConnectionType, Is.EqualTo(VocomConnectionType.WiFi));
            _mockWiFiService.Verify(w => w.ConnectToDeviceAsync(It.IsAny<string>(), It.IsAny<int>()), Times.Once);
        }

        [Test]
        public async Task FallbackToBluetoothAsync_WhenUSBAndWiFiFail_ConnectsViaBluetooth()
        {
            // Arrange
            var device = new VocomDevice
            {
                Id = "vocom1",
                Name = "Vocom 1",
                SerialNumber = "SN12345",
                FirmwareVersion = "1.0",
                ConnectionType = VocomConnectionType.USB,
                IPAddress = "*************",
                BluetoothAddress = "00:11:22:33:44:55"
            };

            // USB connection fails
            _mockVocomDriver.Setup(d => d.ConnectToDeviceAsync(It.IsAny<VocomDevice>())).ReturnsAsync(false);

            // WiFi connection fails
            _mockWiFiService.Setup(w => w.ConnectToDeviceAsync(It.IsAny<string>(), It.IsAny<int>())).ReturnsAsync(false);

            // Bluetooth connection succeeds
            _mockBluetoothService.Setup(b => b.ConnectToDeviceAsync(It.IsAny<string>())).ReturnsAsync(true);

            // Act
            bool result = await _vocomService.ConnectToVocomDeviceWithFallbackAsync(device);

            // Assert
            Assert.That(result, Is.True);
            Assert.That(device.ConnectionType, Is.EqualTo(VocomConnectionType.Bluetooth));
            _mockBluetoothService.Verify(b => b.ConnectToDeviceAsync(It.IsAny<string>()), Times.Once);
        }

        [Test]
        public async Task UpdateVocomFirmwareAsync_ValidDevice_UpdatesSuccessfully()
        {
            // Arrange
            var device = new VocomDevice
            {
                Id = "vocom1",
                Name = "Vocom 1",
                SerialNumber = "SN12345",
                FirmwareVersion = "1.0",
                ConnectionType = VocomConnectionType.USB
            };

            byte[] firmwareData = new byte[] { 1, 2, 3, 4 };
            _mockVocomDriver.Setup(d => d.UpdateFirmwareAsync(It.IsAny<VocomDevice>(), It.IsAny<byte[]>())).ReturnsAsync(true);

            // Act
            bool result = await _vocomService.UpdateVocomFirmwareAsync(device, firmwareData);

            // Assert
            Assert.That(result, Is.True);
            _mockVocomDriver.Verify(d => d.UpdateFirmwareAsync(device, firmwareData), Times.Once);
        }

        [Test]
        public async Task IsPTTRunningAsync_ChecksUsingConnectionHelper()
        {
            // Act
            bool result = await _vocomService.IsPTTRunningAsync();

            // Assert
            // Since we can't mock static methods in ConnectionHelper, we just verify the method doesn't throw
            Assert.That(() => _vocomService.IsPTTRunningAsync(), Throws.Nothing);
            _mockLogger.Verify(l => l.LogInformation(It.IsAny<string>(), It.IsAny<string>()), Times.AtLeastOnce);
        }

        [Test]
        public async Task DisconnectPTTAsync_WhenPTTNotRunning_ReturnsTrue()
        {
            // Arrange - Set up a test environment where PTT is not running
            // This is a bit tricky since we're using static methods in ConnectionHelper
            // We'll rely on the actual implementation for this test

            // Act
            bool result = await _vocomService.DisconnectPTTAsync();

            // Assert
            // Since PTT is likely not running in the test environment, this should return true
            Assert.That(result, Is.True);
            _mockLogger.Verify(l => l.LogInformation(It.IsAny<string>(), It.IsAny<string>()), Times.AtLeastOnce);
        }

        [Test]
        public async Task DisconnectPTTAsync_WhenDriverTerminateSucceeds_ReturnsTrue()
        {
            // Arrange
            // Mock that PTT is running and driver terminate succeeds
            _mockVocomDriver.Setup(d => d.TerminatePTTAsync()).ReturnsAsync(true);

            // We can't directly mock ConnectionHelper.IsPTTApplicationRunning since it's static
            // This test will rely on the actual implementation and may be skipped if PTT is not running

            // Act
            bool result = await _vocomService.DisconnectPTTAsync();

            // Assert
            // This should return true regardless of whether PTT is running or not
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task FindAndSavePTTInfoAsync_CreatesConfigDirectory()
        {
            // This is a private method, so we need to test it indirectly
            // We'll trigger it by calling IsPTTRunningAsync and checking if the config directory is created

            // Arrange
            string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
            string pttInfoPath = Path.Combine(configPath, "ptt_info.txt");

            // Delete the file if it exists to ensure a clean test
            if (File.Exists(pttInfoPath))
            {
                File.Delete(pttInfoPath);
            }

            // Act
            await _vocomService.IsPTTRunningAsync();

            // Assert
            // We can't guarantee the file will be created since PTT might not be running
            // But we can check that the directory exists
            Assert.That(Directory.Exists(configPath), Is.True);
        }
    }
}

