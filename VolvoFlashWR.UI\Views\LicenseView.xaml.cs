using System.Windows;
using VolvoFlashWR.UI.ViewModels;

namespace VolvoFlashWR.UI.Views
{
    /// <summary>
    /// Interaction logic for LicenseView.xaml
    /// </summary>
    public partial class LicenseView : Window
    {
        public LicenseView(LicenseViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            
            // Subscribe to close request from view model
            if (viewModel != null)
            {
                viewModel.CloseRequested += (s, e) => Close();
            }
        }
    }
}
