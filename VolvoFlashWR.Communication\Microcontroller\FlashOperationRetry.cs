using System;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Communication.Microcontroller
{
    /// <summary>
    /// Helper class for retrying flash operations
    /// </summary>
    public class FlashOperationRetry
    {
        private readonly ILoggingService _logger;
        private readonly int _maxRetries;
        private readonly int _retryDelayMs;

        /// <summary>
        /// Initializes a new instance of the FlashOperationRetry class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="maxRetries">The maximum number of retries</param>
        /// <param name="retryDelayMs">The delay between retries in milliseconds</param>
        public FlashOperationRetry(ILoggingService logger, int maxRetries = 3, int retryDelayMs = 100)
        {
            _logger = logger;
            _maxRetries = maxRetries;
            _retryDelayMs = retryDelayMs;
        }

        /// <summary>
        /// Executes a flash operation with retry logic
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <param name="operationName">The name of the operation for logging</param>
        /// <param name="address">The address for the operation</param>
        /// <returns>The result of the operation</returns>
        public async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, string operationName, uint address)
        {
            int retryCount = 0;
            Exception lastException = null;

            while (retryCount <= _maxRetries)
            {
                try
                {
                    if (retryCount > 0)
                    {
                        _logger?.LogInformation($"Retry {retryCount}/{_maxRetries} for {operationName} at address 0x{address:X8}");
                        await Task.Delay(_retryDelayMs);
                    }

                    T result = await operation();

                    if (retryCount > 0)
                    {
                        _logger?.LogInformation($"Successfully completed {operationName} at address 0x{address:X8} after {retryCount} retries");
                    }

                    return result;
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    _logger?.LogWarning($"Error during {operationName} at address 0x{address:X8}: {ex.Message}");
                    retryCount++;
                }
            }

            _logger?.LogError($"Failed to complete {operationName} at address 0x{address:X8} after {_maxRetries} retries");
            throw new Exception($"Failed to complete {operationName} at address 0x{address:X8} after {_maxRetries} retries", lastException);
        }

        /// <summary>
        /// Executes a flash operation with retry logic and validation
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <param name="validation">The validation function</param>
        /// <param name="operationName">The name of the operation for logging</param>
        /// <param name="address">The address for the operation</param>
        /// <returns>The result of the operation</returns>
        public async Task<T> ExecuteWithRetryAndValidationAsync<T>(Func<Task<T>> operation, Func<T, bool> validation, string operationName, uint address)
        {
            int retryCount = 0;
            Exception lastException = null;

            while (retryCount <= _maxRetries)
            {
                try
                {
                    if (retryCount > 0)
                    {
                        _logger?.LogInformation($"Retry {retryCount}/{_maxRetries} for {operationName} at address 0x{address:X8}");
                        await Task.Delay(_retryDelayMs);
                    }

                    T result = await operation();

                    // Validate the result
                    if (validation(result))
                    {
                        if (retryCount > 0)
                        {
                            _logger?.LogInformation($"Successfully completed {operationName} at address 0x{address:X8} after {retryCount} retries");
                        }

                        return result;
                    }
                    else
                    {
                        _logger?.LogWarning($"Validation failed for {operationName} at address 0x{address:X8}");
                        retryCount++;
                    }
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    _logger?.LogWarning($"Error during {operationName} at address 0x{address:X8}: {ex.Message}");
                    retryCount++;
                }
            }

            _logger?.LogError($"Failed to complete {operationName} at address 0x{address:X8} after {_maxRetries} retries");
            throw new Exception($"Failed to complete {operationName} at address 0x{address:X8} after {_maxRetries} retries", lastException);
        }
    }
}
