using System;
using System.Runtime.InteropServices;
using System.IO;

namespace VocomArchitectureTest
{
    class Program
    {
        // Test DLL imports to verify 32-bit compatibility
        [DllImport("apci.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int APCI_Initialize();

        [DllImport("WUDFPuma.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int Vocom_Initialize();

        static void Main(string[] args)
        {
            Console.WriteLine("=== Vocom Architecture Test ===");
            Console.WriteLine($"Process Architecture: {(Environment.Is64BitProcess ? "64-bit" : "32-bit")}");
            Console.WriteLine($"OS Architecture: {(Environment.Is64BitOperatingSystem ? "64-bit" : "32-bit")}");
            Console.WriteLine();

            // Test if DLLs exist
            string currentDir = Directory.GetCurrentDirectory();
            Console.WriteLine($"Current Directory: {currentDir}");
            
            string apciPath = Path.Combine(currentDir, "apci.dll");
            string wudfPath = Path.Combine(currentDir, "WUDFPuma.dll");
            
            Console.WriteLine($"apci.dll exists: {File.Exists(apciPath)}");
            Console.WriteLine($"WUDFPuma.dll exists: {File.Exists(wudfPath)}");
            Console.WriteLine();

            // Test DLL loading
            Console.WriteLine("Testing DLL loading...");
            
            try
            {
                Console.WriteLine("Attempting to call APCI_Initialize()...");
                int apciResult = APCI_Initialize();
                Console.WriteLine($"✅ APCI_Initialize() succeeded! Result: {apciResult}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ APCI_Initialize() failed: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"   Inner exception: {ex.InnerException.Message}");
                }
            }

            try
            {
                Console.WriteLine("Attempting to call Vocom_Initialize()...");
                int vocomResult = Vocom_Initialize();
                Console.WriteLine($"✅ Vocom_Initialize() succeeded! Result: {vocomResult}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Vocom_Initialize() failed: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"   Inner exception: {ex.InnerException.Message}");
                }
            }

            Console.WriteLine();
            Console.WriteLine("Test completed. Press any key to exit...");
            Console.ReadKey();
        }
    }
}
