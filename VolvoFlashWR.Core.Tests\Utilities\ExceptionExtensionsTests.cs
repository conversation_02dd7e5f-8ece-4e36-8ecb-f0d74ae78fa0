using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Utilities;

namespace VolvoFlashWR.Core.Tests.Utilities
{
    [TestFixture]
    public class ExceptionExtensionsTests
    {
        [Test]
        public void GetDetailedMessage_WithSimpleException_ReturnsDetailedMessage()
        {
            // Arrange
            var exception = new InvalidOperationException("Test exception");

            // Act
            string detailedMessage = exception.GetDetailedMessage();

            // Assert
            Assert.That(detailedMessage, Is.Not.Null);
            Assert.That(detailedMessage, Does.Contain("InvalidOperationException"));
            Assert.That(detailedMessage, Does.Contain("Test exception"));
        }

        [Test]
        public void GetDetailedMessage_WithInnerException_IncludesInnerExceptionDetails()
        {
            // Arrange
            var innerException = new ArgumentException("Inner exception");
            var outerException = new InvalidOperationException("Outer exception", innerException);

            // Act
            string detailedMessage = outerException.GetDetailedMessage();

            // Assert
            Assert.That(detailedMessage, Is.Not.Null);
            Assert.That(detailedMessage, Does.Contain("InvalidOperationException"));
            Assert.That(detailedMessage, Does.Contain("Outer exception"));
            Assert.That(detailedMessage, Does.Contain("Inner Exception"));
            Assert.That(detailedMessage, Does.Contain("ArgumentException"));
            Assert.That(detailedMessage, Does.Contain("Inner exception"));
        }

        [Test]
        public void GetDetailedMessage_WithoutStackTrace_DoesNotIncludeStackTrace()
        {
            // Arrange
            var exception = new InvalidOperationException("Test exception");

            // Act
            string detailedMessage = exception.GetDetailedMessage(includeStackTrace: false);

            // Assert
            Assert.That(detailedMessage, Is.Not.Null);
            Assert.That(detailedMessage, Does.Contain("InvalidOperationException"));
            Assert.That(detailedMessage, Does.Contain("Test exception"));
            Assert.That(detailedMessage, Does.Not.Contain("Stack Trace"));
        }

        [Test]
        public void GetExceptionProperties_ReturnsExpectedProperties()
        {
            // Arrange
            var exception = new FileNotFoundException("File not found", "test.txt");

            // Act
            var properties = exception.GetExceptionProperties();

            // Assert
            Assert.That(properties, Is.Not.Null);
            Assert.That(properties, Does.ContainKey("ExceptionType"));
            Assert.That(properties, Does.ContainKey("Message"));
            Assert.That(properties, Does.ContainKey("FileName"));
            Assert.That(properties["ExceptionType"], Is.EqualTo("System.IO.FileNotFoundException"));
            Assert.That(properties["Message"], Is.EqualTo("File not found"));
            Assert.That(properties["FileName"], Is.EqualTo("test.txt"));
        }

        [Test]
        public void GetAllInnerExceptions_WithAggregateException_ReturnsAllInnerExceptions()
        {
            // Arrange
            var innerExceptions = new List<Exception>
            {
                new ArgumentException("Argument exception"),
                new InvalidOperationException("Invalid operation"),
                new FileNotFoundException("File not found")
            };
            var aggregateException = new AggregateException("Aggregate exception", innerExceptions);

            // Act
            var allExceptions = aggregateException.GetAllInnerExceptions();

            // Assert
            Assert.That(allExceptions, Is.Not.Null);
            Assert.That(allExceptions.Count, Is.EqualTo(4)); // Aggregate + 3 inner exceptions
            Assert.That(allExceptions[0], Is.InstanceOf<AggregateException>());
            Assert.That(allExceptions[1], Is.InstanceOf<ArgumentException>());
            Assert.That(allExceptions[2], Is.InstanceOf<InvalidOperationException>());
            Assert.That(allExceptions[3], Is.InstanceOf<FileNotFoundException>());
        }

        [Test]
        public void IsCriticalException_WithCriticalException_ReturnsTrue()
        {
            // Arrange
            var exception = new OutOfMemoryException("Out of memory");

            // Act
            bool isCritical = exception.IsCriticalException();

            // Assert
            Assert.That(isCritical, Is.True);
        }

        [Test]
        public void IsCriticalException_WithNonCriticalException_ReturnsFalse()
        {
            // Arrange
            var exception = new ArgumentException("Argument exception");

            // Act
            bool isCritical = exception.IsCriticalException();

            // Assert
            Assert.That(isCritical, Is.False);
        }

        [Test]
        public void IsTransient_WithTransientException_ReturnsTrue()
        {
            // Arrange
            var exception = new TimeoutException("Timeout");

            // Act
            bool isTransient = exception.IsTransient();

            // Assert
            Assert.That(isTransient, Is.True);
        }

        [Test]
        public void IsTransient_WithNonTransientException_ReturnsFalse()
        {
            // Arrange
            var exception = new ArgumentException("Argument exception");

            // Act
            bool isTransient = exception.IsTransient();

            // Assert
            Assert.That(isTransient, Is.False);
        }

        [Test]
        public void GetUserFriendlyMessage_WithCommonException_ReturnsUserFriendlyMessage()
        {
            // Arrange
            var exception = new FileNotFoundException("File not found", "test.txt");

            // Act
            string message = exception.GetUserFriendlyMessage();

            // Assert
            Assert.That(message, Is.Not.Null);
            Assert.That(message, Does.Contain("file was not found").Or.Contain("file is accessible"));
            Assert.That(message, Does.Not.Contain("FileNotFoundException"));
        }

        [Test]
        public void GetUserFriendlyMessage_WithAggregateException_ReturnsFirstInnerExceptionMessage()
        {
            // Arrange
            var innerExceptions = new List<Exception>
            {
                new FileNotFoundException("File not found", "test.txt"),
                new InvalidOperationException("Invalid operation")
            };
            var aggregateException = new AggregateException("Aggregate exception", innerExceptions);

            // Act
            string message = aggregateException.GetUserFriendlyMessage();

            // Assert
            Assert.That(message, Is.Not.Null);
            Assert.That(message, Does.Contain("file was not found").Or.Contain("file is accessible"));
            Assert.That(message, Does.Not.Contain("AggregateException"));
        }
    }
}

