using System;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Core.Tests.Models
{
    [TestFixture]
    public class ECUFaultTests
    {
        [Test]
        public void ECUFault_DefaultValues_AreCorrect()
        {
            // Arrange & Act
            var fault = new ECUFault();

            // Assert
            Assert.That(fault.Code, Is.Null);
            Assert.That(fault.Description, Is.Null);
            Assert.That(fault.Severity, Is.EqualTo(FaultSeverity.Low));
            Assert.That(fault.IsActive, Is.False);
        }

        [Test]
        public void ECUFault_CustomValues_AreCorrect()
        {
            // Arrange
            string code = "P0123";
            string description = "Throttle Position Sensor Circuit High Input";
            FaultSeverity severity = FaultSeverity.Medium;
            DateTime timestamp = new DateTime(2023, 1, 1);
            bool isActive = true;

            // Act
            var fault = new ECUFault
            {
                Code = code,
                Description = description,
                Severity = severity,
                Timestamp = timestamp,
                IsActive = isActive
            };

            // Assert
            Assert.That(fault.Code, Is.EqualTo(code));
            Assert.That(fault.Description, Is.EqualTo(description));
            Assert.That(fault.Severity, Is.EqualTo(severity));
            Assert.That(fault.Timestamp, Is.EqualTo(timestamp));
            Assert.That(fault.IsActive, Is.EqualTo(isActive));
        }
    }
}

