using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.UI.Tests.Helpers;
using VolvoFlashWR.UI.ViewModels;

namespace VolvoFlashWR.UI.Tests.ViewModels
{
    [TestFixture]
    public class BackupViewModelTests
    {
        private Mock<ILoggingService> _mockLoggingService;
        private Mock<IBackupService> _mockBackupService;
        private Mock<IECUCommunicationService> _mockEcuCommunicationService;
        private BackupViewModel _viewModel;

        [SetUp]
        public void Setup()
        {
            // Set up the test environment
            TestHelper.SetupTestEnvironment();

            _mockLoggingService = new Mock<ILoggingService>();
            _mockBackupService = new Mock<IBackupService>();
            _mockEcuCommunicationService = new Mock<IECUCommunicationService>();

            // Setup mock services
            _mockBackupService.Setup(m => m.InitializeAsync(_mockEcuCommunicationService.Object)).ReturnsAsync(true);
            _mockBackupService.Setup(m => m.BackupDirectoryPath).Returns("C:\\Backups");

            // Create the view model
            _viewModel = new BackupViewModel(
                _mockLoggingService.Object,
                _mockBackupService.Object,
                _mockEcuCommunicationService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            // Clean up the test environment
            TestHelper.CleanupTestEnvironment();
        }

        [Test]
        public void Constructor_ValidParameters_InitializesProperties()
        {
            // Assert
            Assert.That(_viewModel, Is.Not.Null);
            Assert.That(_viewModel.Backups, Is.Not.Null);
            Assert.That(_viewModel.IncludeEEPROM, Is.True);
            Assert.That(_viewModel.IncludeMicrocontrollerCode, Is.True);
            Assert.That(_viewModel.IncludeParameters, Is.True);
            Assert.That(_viewModel.RestoreEEPROM, Is.True);
            Assert.That(_viewModel.RestoreMicrocontrollerCode, Is.True);
            Assert.That(_viewModel.RestoreParameters, Is.True);
        }

        [Test]
        public void Constructor_ValidParameters_InitializesCommands()
        {
            // Assert
            Assert.That(_viewModel.CreateBackupCommand, Is.Not.Null);
            Assert.That(_viewModel.RestoreBackupCommand, Is.Not.Null);
            Assert.That(_viewModel.DeleteBackupCommand, Is.Not.Null);
            Assert.That(_viewModel.ImportBackupCommand, Is.Not.Null);
            Assert.That(_viewModel.ExportBackupCommand, Is.Not.Null);
            Assert.That(_viewModel.RefreshBackupsCommand, Is.Not.Null);
        }

        [Test]
        public async Task RefreshBackupsAsync_ValidCall_PopulatesBackups()
        {
            // Arrange
            var mockBackups = new List<BackupData>
            {
                new BackupData { Id = "1", ECUName = "ECU1", CreationTime = DateTime.Now },
                new BackupData { Id = "2", ECUName = "ECU2", CreationTime = DateTime.Now.AddDays(-1) }
            };
            _mockBackupService.Setup(m => m.GetAllBackupsAsync()).ReturnsAsync(mockBackups);

            // Act - Use reflection to call the private method
            var refreshMethod = typeof(BackupViewModel).GetMethod("RefreshBackupsAsync",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            await (Task)refreshMethod.Invoke(_viewModel, null);

            // Assert
            Assert.That(_viewModel.Backups.Count, Is.EqualTo(2));
            Assert.That(_viewModel.Backups[0].ECUName, Is.EqualTo("ECU1"));
            Assert.That(_viewModel.Backups[1].ECUName, Is.EqualTo("ECU2"));
            _mockBackupService.Verify(m => m.GetAllBackupsAsync(), Times.AtLeastOnce);
        }

        [Test]
        public async Task CreateBackupAsync_ValidECU_CreatesBackup()
        {
            // Arrange
            var mockECU = new ECUDevice { Id = "ECU1", Name = "TestECU" };
            var mockBackup = new BackupData { Id = "Backup1", ECUId = "ECU1", ECUName = "TestECU" };

            _viewModel.SelectedECU = mockECU;
            _viewModel.BackupDescription = "Test Backup";

            _mockBackupService.Setup(m => m.CreateBackupAsync(
                mockECU,
                "Test Backup",
                "", // category
                null, // tags
                true,
                true,
                true)).ReturnsAsync(mockBackup);

            // Act - Use reflection to call the private method
            var createMethod = typeof(BackupViewModel).GetMethod("CreateBackupAsync",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            await (Task)createMethod.Invoke(_viewModel, null);

            // Assert
            _mockBackupService.Verify(m => m.CreateBackupAsync(
                mockECU,
                "Test Backup",
                "", // category
                null, // tags
                true,
                true,
                true), Times.Once);

            Assert.That(_viewModel.Backups.Count, Is.EqualTo(1));
            Assert.That(_viewModel.Backups[0].Id, Is.EqualTo("Backup1"));
            Assert.That(_viewModel.SelectedBackup, Is.EqualTo(mockBackup));
        }

        [Test]
        public async Task RestoreBackupAsync_ValidBackupAndECU_RestoresBackup()
        {
            // Arrange
            var mockECU = new ECUDevice { Id = "ECU1", Name = "TestECU" };
            var mockBackup = new BackupData { Id = "Backup1", ECUId = "ECU1", ECUName = "TestECU" };

            _viewModel.SelectedECU = mockECU;
            _viewModel.SelectedBackup = mockBackup;

            _mockBackupService.Setup(m => m.RestoreBackupAsync(
                mockBackup,
                mockECU,
                true,
                true,
                true)).ReturnsAsync(true);

            // Act - Use reflection to call the private method
            var restoreMethod = typeof(BackupViewModel).GetMethod("RestoreBackupAsync",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (restoreMethod != null)
            {
                await (Task)restoreMethod.Invoke(_viewModel, null);
            }

            // Assert
            _mockBackupService.Verify(m => m.RestoreBackupAsync(
                mockBackup,
                mockECU,
                true,
                true,
                true), Times.Once);
        }
    }
}

