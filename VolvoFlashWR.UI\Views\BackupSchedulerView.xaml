<Window x:Class="VolvoFlashWR.UI.Views.BackupSchedulerView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:VolvoFlashWR.UI.Views"
        mc:Ignorable="d"
        Title="Backup Scheduler" Height="500" Width="800"
        WindowStartupLocation="CenterOwner">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Backup Scheduler" FontSize="20" FontWeight="Bold" Margin="0,0,0,10"/>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Scheduled Backups List -->
            <GroupBox Grid.Column="0" Header="Scheduled Backups" Margin="0,0,5,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <ListView Grid.Row="0" ItemsSource="{Binding ScheduledBackups}"
                              SelectedItem="{Binding SelectedSchedule}">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="Name" Width="120" DisplayMemberBinding="{Binding Name}"/>
                                <GridViewColumn Header="ECU" Width="80" DisplayMemberBinding="{Binding ECUName}"/>
                                <GridViewColumn Header="Frequency" Width="100" DisplayMemberBinding="{Binding FrequencyDisplay}"/>
                                <GridViewColumn Header="Next Run" Width="150" DisplayMemberBinding="{Binding NextRunTime, StringFormat='{}{0:yyyy-MM-dd HH:mm}'}"/>
                            </GridView>
                        </ListView.View>
                    </ListView>

                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,5,0,0">
                        <Button Content="Add" Command="{Binding AddScheduleCommand}" Width="80" Margin="0,0,5,0"/>
                        <Button Content="Edit" Command="{Binding EditScheduleCommand}" Width="80" Margin="0,0,5,0"/>
                        <Button Content="Delete" Command="{Binding DeleteScheduleCommand}" Width="80"/>
                    </StackPanel>
                </Grid>
            </GroupBox>

            <!-- Schedule Details -->
            <GroupBox Grid.Column="1" Header="Schedule Details" Margin="5,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel IsEnabled="{Binding IsEditMode}">
                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Name:" VerticalAlignment="Center"/>
                            <TextBox Grid.Column="1" Text="{Binding CurrentSchedule.Name, UpdateSourceTrigger=PropertyChanged}"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="ECU:" VerticalAlignment="Center"/>
                            <ComboBox Grid.Column="1" ItemsSource="{Binding AvailableECUs}"
                                      SelectedItem="{Binding CurrentSchedule.SelectedECU}"
                                      DisplayMemberPath="Name"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Frequency:" VerticalAlignment="Center"/>
                            <ComboBox Grid.Column="1" ItemsSource="{Binding ScheduleFrequencies}"
                                      SelectedItem="{Binding CurrentSchedule.Frequency}"/>
                        </Grid>

                        <Grid Margin="0,5" Visibility="{Binding IsCustomFrequency, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Custom Interval:" VerticalAlignment="Center"/>
                            <Grid Grid.Column="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Grid.Column="0" Text="{Binding CurrentSchedule.CustomIntervalValue}"
                                         PreviewTextInput="NumberValidationTextBox"/>
                                <ComboBox Grid.Column="1" Width="100" Margin="5,0,0,0"
                                          ItemsSource="{Binding TimeUnits}"
                                          SelectedItem="{Binding CurrentSchedule.CustomIntervalUnit}"/>
                            </Grid>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Start Date:" VerticalAlignment="Center"/>
                            <DatePicker Grid.Column="1" SelectedDate="{Binding CurrentSchedule.StartDate}"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Start Time:" VerticalAlignment="Center"/>
                            <Grid Grid.Column="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <ComboBox Grid.Column="0" Width="60" ItemsSource="{Binding Hours}"
                                          SelectedItem="{Binding CurrentSchedule.StartHour}"/>
                                <TextBlock Grid.Column="1" Text=":" VerticalAlignment="Center" Margin="5,0"/>
                                <ComboBox Grid.Column="2" Width="60" ItemsSource="{Binding Minutes}"
                                          SelectedItem="{Binding CurrentSchedule.StartMinute}"/>
                            </Grid>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Category:" VerticalAlignment="Center"/>
                            <ComboBox Grid.Column="1" ItemsSource="{Binding BackupCategories}"
                                      SelectedItem="{Binding CurrentSchedule.Category}"
                                      IsEditable="True"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Tags:" VerticalAlignment="Center"/>
                            <TextBox Grid.Column="1" Text="{Binding CurrentSchedule.Tags}"
                                     ToolTip="Enter tags separated by commas"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Description:" VerticalAlignment="Center"/>
                            <TextBox Grid.Column="1" Text="{Binding CurrentSchedule.Description}"
                                     TextWrapping="Wrap" AcceptsReturn="True" Height="60"/>
                        </Grid>

                        <StackPanel Orientation="Horizontal" Margin="120,10,0,0">
                            <CheckBox Content="Include EEPROM" IsChecked="{Binding CurrentSchedule.IncludeEEPROM}" Margin="0,0,10,0"/>
                            <CheckBox Content="Include MCU Code" IsChecked="{Binding CurrentSchedule.IncludeMicrocontrollerCode}" Margin="0,0,10,0"/>
                            <CheckBox Content="Include Parameters" IsChecked="{Binding CurrentSchedule.IncludeParameters}"/>
                        </StackPanel>

                        <TextBlock Text="Backup Retention Policy" FontWeight="Bold" Margin="0,15,0,5"/>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Schedule Type:" VerticalAlignment="Center"/>
                            <ComboBox Grid.Column="1" ItemsSource="{Binding ScheduleTypes}"
                                      SelectedItem="{Binding CurrentSchedule.ScheduleType}"/>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Max Backups:" VerticalAlignment="Center"/>
                            <Grid Grid.Column="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Grid.Column="0" Text="{Binding CurrentSchedule.MaxBackupsToKeep}"
                                         PreviewTextInput="NumberValidationTextBox"/>
                                <TextBlock Grid.Column="1" Text="(0 = keep all)" VerticalAlignment="Center" Margin="5,0,0,0" Foreground="Gray"/>
                            </Grid>
                        </Grid>

                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Max Backup Age:" VerticalAlignment="Center"/>
                            <Grid Grid.Column="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Grid.Column="0" Text="{Binding CurrentSchedule.MaxBackupAge}"
                                         PreviewTextInput="NumberValidationTextBox"/>
                                <TextBlock Grid.Column="1" Text="days (0 = no limit)" VerticalAlignment="Center" Margin="5,0,0,0" Foreground="Gray"/>
                            </Grid>
                        </Grid>

                        <TextBlock Margin="120,5,0,0" TextWrapping="Wrap" Foreground="Gray">
                            <Run Text="Retention Policy:"/>
                            <LineBreak/>
                            <Run Text="• Daily: Keeps one backup per day for the last N days"/>
                            <LineBreak/>
                            <Run Text="• Weekly: Keeps one backup per week for the last N weeks"/>
                            <LineBreak/>
                            <Run Text="• Monthly: Keeps one backup per month for the last N months"/>
                            <LineBreak/>
                            <Run Text="• Custom: Keeps the newest N backups"/>
                            <LineBreak/>
                            <LineBreak/>
                            <Run Text="Max Backup Age: Creates a new full backup when the latest backup is older than the specified number of days"/>
                        </TextBlock>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
                            <Button Content="Save" Command="{Binding SaveScheduleCommand}" Width="80" Margin="0,0,5,0"/>
                            <Button Content="Cancel" Command="{Binding CancelEditCommand}" Width="80"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </GroupBox>
        </Grid>

        <!-- Footer -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
            <Button Content="Run Now" Command="{Binding RunScheduleNowCommand}" Width="100" Margin="0,0,5,0"/>
            <Button Content="Close" Command="{Binding CloseCommand}" Width="100"/>
        </StackPanel>
    </Grid>
</Window>
