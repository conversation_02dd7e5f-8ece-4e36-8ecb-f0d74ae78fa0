using System;
using System.Threading.Tasks;
using Moq;
using Moq.Language.Flow;

namespace VolvoFlashWR.Communication.Tests.Helpers
{
    /// <summary>
    /// Extension methods for <PERSON><PERSON> to help with async methods
    /// </summary>
    public static class MoqExtensions
    {
        /// <summary>
        /// Returns a Task of T from a method call
        /// </summary>
        /// <typeparam name="T">The type to return</typeparam>
        /// <param name="setup">The setup</param>
        /// <param name="value">The value to return</param>
        /// <returns>The setup object</returns>
        public static IReturnsResult<TMock> ReturnsAsync<TMock, T>(this ISetup<TMock, Task<T>> setup, T value)
            where TMock : class
        {
            return setup.Returns(Task.FromResult(value));
        }
    }
}
