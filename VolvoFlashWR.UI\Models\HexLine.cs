using System;
using System.Text;

namespace VolvoFlashWR.UI.Models
{
    /// <summary>
    /// Represents a line in a hex viewer
    /// </summary>
    public class HexLine
    {
        /// <summary>
        /// Gets or sets the address of the line
        /// </summary>
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the hex data of the line
        /// </summary>
        public string HexData { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the ASCII representation of the line
        /// </summary>
        public string AsciiData { get; set; } = string.Empty;

        /// <summary>
        /// Default constructor
        /// </summary>
        public HexLine()
        {
            // Properties already initialized with default values
        }

        /// <summary>
        /// Creates a hex line from a byte array
        /// </summary>
        /// <param name="data">The byte array</param>
        /// <param name="offset">The offset in the byte array</param>
        /// <param name="baseAddress">The base address to display</param>
        /// <param name="bytesPerLine">The number of bytes per line</param>
        /// <returns>A new hex line, or null if data is invalid</returns>
        public static HexLine? FromBytes(byte[] data, int offset, int baseAddress, int bytesPerLine = 16)
        {
            if (data == null || offset >= data.Length)
                return null;

            StringBuilder hexBuilder = new StringBuilder();
            StringBuilder asciiBuilder = new StringBuilder();

            int bytesToProcess = Math.Min(bytesPerLine, data.Length - offset);

            for (int i = 0; i < bytesPerLine; i++)
            {
                if (i < bytesToProcess)
                {
                    byte b = data[offset + i];
                    hexBuilder.Append(b.ToString("X2")).Append(" ");

                    // For ASCII representation, only show printable characters
                    if (b >= 32 && b <= 126)
                    {
                        asciiBuilder.Append((char)b);
                    }
                    else
                    {
                        asciiBuilder.Append(".");
                    }
                }
                else
                {
                    // Padding for incomplete lines
                    hexBuilder.Append("   ");
                    asciiBuilder.Append(" ");
                }
            }

            return new HexLine
            {
                Address = (baseAddress + offset).ToString("X8"),
                HexData = hexBuilder.ToString().TrimEnd(),
                AsciiData = asciiBuilder.ToString()
            };
        }

        /// <summary>
        /// Creates a collection of hex lines from a byte array
        /// </summary>
        /// <param name="data">The byte array</param>
        /// <param name="baseAddress">The base address to display</param>
        /// <param name="bytesPerLine">The number of bytes per line</param>
        /// <returns>An array of hex lines</returns>
        public static HexLine[] FromByteArray(byte[] data, int baseAddress = 0, int bytesPerLine = 16)
        {
            if (data == null || data.Length == 0)
                return new HexLine[0];

            int lineCount = (data.Length + bytesPerLine - 1) / bytesPerLine;
            HexLine[] lines = new HexLine[lineCount];

            for (int i = 0; i < lineCount; i++)
            {
                int offset = i * bytesPerLine;
                HexLine? line = FromBytes(data, offset, baseAddress + offset, bytesPerLine);
                lines[i] = line ?? new HexLine(); // Use empty HexLine if FromBytes returns null
            }

            return lines;
        }
    }
}
