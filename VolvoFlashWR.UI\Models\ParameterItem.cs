using System.ComponentModel;

namespace VolvoFlashWR.UI.Models
{
    /// <summary>
    /// Represents a parameter item in the UI
    /// </summary>
    public class ParameterItem : INotifyPropertyChanged
    {
        private string _key = string.Empty;
        private object _value = new();
        private string _unit = string.Empty;
        private string _description = string.Empty;

        /// <summary>
        /// Gets or sets the key of the parameter
        /// </summary>
        public string Key
        {
            get => _key;
            set
            {
                _key = value;
                OnPropertyChanged(nameof(Key));
            }
        }

        /// <summary>
        /// Gets or sets the value of the parameter
        /// </summary>
        public object Value
        {
            get => _value;
            set
            {
                _value = value;
                OnPropertyChanged(nameof(Value));
            }
        }

        /// <summary>
        /// Gets or sets the unit of the parameter
        /// </summary>
        public string Unit
        {
            get => _unit;
            set
            {
                _unit = value;
                OnPropertyChanged(nameof(Unit));
            }
        }

        /// <summary>
        /// Gets or sets the description of the parameter
        /// </summary>
        public string Description
        {
            get => _description;
            set
            {
                _description = value;
                OnPropertyChanged(nameof(Description));
            }
        }

        /// <summary>
        /// Event that is raised when a property value changes
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Default constructor
        /// </summary>
        public ParameterItem()
        {
            // Properties already initialized with default values
        }

        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">The name of the property that changed</param>
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Creates a new parameter item
        /// </summary>
        /// <param name="key">The key of the parameter</param>
        /// <param name="value">The value of the parameter</param>
        /// <param name="unit">The unit of the parameter</param>
        /// <param name="description">The description of the parameter</param>
        /// <returns>A new parameter item</returns>
        public static ParameterItem Create(string key, object value, string unit = "", string description = "")
        {
            return new ParameterItem
            {
                Key = key,
                Value = value,
                Unit = unit,
                Description = description
            };
        }
    }
}
