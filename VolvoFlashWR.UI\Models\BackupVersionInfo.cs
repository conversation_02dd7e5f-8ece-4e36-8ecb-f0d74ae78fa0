using System;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.UI.Models
{
    /// <summary>
    /// Represents information about a backup version for display and comparison
    /// </summary>
    public class BackupVersionInfo
    {
        /// <summary>
        /// The backup data
        /// </summary>
        public BackupData Backup { get; set; }
        
        /// <summary>
        /// Display name for the backup version
        /// </summary>
        public string DisplayName => $"v{Backup.Version} - {Backup.CreationTime:yyyy-MM-dd HH:mm}";
        
        /// <summary>
        /// Indicates if this is the latest version
        /// </summary>
        public bool IsLatest => Backup.IsLatestVersion;
        
        /// <summary>
        /// Indicates if this is the baseline version
        /// </summary>
        public bool IsBaseline { get; set; }
        
        /// <summary>
        /// Creates a new instance of BackupVersionInfo
        /// </summary>
        /// <param name="backup">The backup data</param>
        public BackupVersionInfo(BackupData backup)
        {
            Backup = backup ?? throw new ArgumentNullException(nameof(backup));
        }
    }
}
