@echo off
title VolvoFlashWR - Phoenix APCI Real Hardware Setup
color 0A

echo ===============================================================================
echo                    VolvoFlashWR - Phoenix APCI Real Hardware Setup
echo ===============================================================================
echo.
echo This comprehensive setup script will:
echo.
echo   1. Enable Phoenix APCI real hardware communication
echo   2. Copy essential libraries for Vocom 1 adapter
echo   3. Remove unnecessary libraries to optimize performance
echo   4. Verify all critical libraries are present
echo   5. Configure environment for real hardware testing
echo   6. Launch the application in Phoenix APCI mode
echo.
echo ===============================================================================
echo.
echo IMPORTANT: Make sure your Vocom 1 adapter is connected before proceeding.
echo.
pause

echo.
echo === Step 1: Configuring Environment Variables ===

REM Set critical environment variables for Phoenix APCI
set PHOENIX_VOCOM_ENABLED=true
set PHOENIX_DIAG_PATH=C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
set USE_DUMMY_IMPLEMENTATIONS=false

echo   ✓ PHOENIX_VOCOM_ENABLED=true
echo   ✓ USE_DUMMY_IMPLEMENTATIONS=false
echo   ✓ PHOENIX_DIAG_PATH configured

echo.
echo === Step 2: Copying Essential Phoenix APCI Libraries ===

REM Core APCI communication libraries
echo [CRITICAL] Core APCI Libraries:
copy "Libraries\apci.dll" "." >nul 2>&1
if exist "apci.dll" (echo   ✓ apci.dll) else (echo   ✗ apci.dll - MISSING!)
copy "Libraries\apcidb.dll" "." >nul 2>&1
if exist "apcidb.dll" (echo   ✓ apcidb.dll) else (echo   ✗ apcidb.dll - MISSING!)
copy "Libraries\Rpci.dll" "." >nul 2>&1
if exist "Rpci.dll" (echo   ✓ Rpci.dll) else (echo   ✗ Rpci.dll - MISSING!)
copy "Libraries\Pc2.dll" "." >nul 2>&1
if exist "Pc2.dll" (echo   ✓ Pc2.dll) else (echo   ✗ Pc2.dll - MISSING!)

REM Vocom 1 adapter driver
echo.
echo [CRITICAL] Vocom 1 Adapter Driver:
copy "Libraries\WUDFPuma.dll" "." >nul 2>&1
if exist "WUDFPuma.dll" (echo   ✓ WUDFPuma.dll) else (echo   ✗ WUDFPuma.dll - MISSING!)
copy "Libraries\WUDFUpdate_01009.dll" "." >nul 2>&1
if exist "WUDFUpdate_01009.dll" (echo   ✓ WUDFUpdate_01009.dll) else (echo   ✗ WUDFUpdate_01009.dll - MISSING!)
copy "Libraries\WdfCoInstaller01009.dll" "." >nul 2>&1
if exist "WdfCoInstaller01009.dll" (echo   ✓ WdfCoInstaller01009.dll) else (echo   ✗ WdfCoInstaller01009.dll - MISSING!)

REM Phoenix integration libraries
echo.
echo [ESSENTIAL] Phoenix Integration:
copy "Libraries\PhoenixESW.dll" "." >nul 2>&1
if exist "PhoenixESW.dll" (echo   ✓ PhoenixESW.dll) else (echo   ✗ PhoenixESW.dll - MISSING!)
copy "Libraries\PhoenixGeneral.dll" "." >nul 2>&1
if exist "PhoenixGeneral.dll" (echo   ✓ PhoenixGeneral.dll) else (echo   ✗ PhoenixGeneral.dll - MISSING!)
copy "Libraries\PhoenixProducInformation.dll" "." >nul 2>&1
if exist "PhoenixProducInformation.dll" (echo   ✓ PhoenixProducInformation.dll) else (echo   ✗ PhoenixProducInformation.dll - MISSING!)

REM Volvo APCI libraries
echo.
echo [REQUIRED] Volvo APCI Communication:
copy "Libraries\Volvo.ApciPlus.dll" "." >nul 2>&1
if exist "Volvo.ApciPlus.dll" (echo   ✓ Volvo.ApciPlus.dll) else (echo   ✗ Volvo.ApciPlus.dll - MISSING!)
copy "Libraries\Volvo.ApciPlusData.dll" "." >nul 2>&1
if exist "Volvo.ApciPlusData.dll" (echo   ✓ Volvo.ApciPlusData.dll) else (echo   ✗ Volvo.ApciPlusData.dll - MISSING!)
copy "Libraries\Volvo.ApciPlusTea2Data.dll" "." >nul 2>&1
if exist "Volvo.ApciPlusTea2Data.dll" (echo   ✓ Volvo.ApciPlusTea2Data.dll) else (echo   ✗ Volvo.ApciPlusTea2Data.dll - MISSING!)

REM Volvo protocol libraries
echo.
echo [REQUIRED] Volvo Protocol Libraries:
copy "Libraries\Volvo.NVS.Core.dll" "." >nul 2>&1
if exist "Volvo.NVS.Core.dll" (echo   ✓ Volvo.NVS.Core.dll) else (echo   ✗ Volvo.NVS.Core.dll - MISSING!)
copy "Libraries\Volvo.NVS.Logging.dll" "." >nul 2>&1
if exist "Volvo.NVS.Logging.dll" (echo   ✓ Volvo.NVS.Logging.dll) else (echo   ✗ Volvo.NVS.Logging.dll - MISSING!)
copy "Libraries\Volvo.NAMS.AC.Services.Interface.dll" "." >nul 2>&1
if exist "Volvo.NAMS.AC.Services.Interface.dll" (echo   ✓ Volvo.NAMS.AC.Services.Interface.dll) else (echo   ✗ Volvo.NAMS.AC.Services.Interface.dll - MISSING!)

REM Vodia communication libraries
echo.
echo [REQUIRED] Communication Protocol Libraries:
copy "Libraries\Vodia.CommonDomain.Model.dll" "." >nul 2>&1
if exist "Vodia.CommonDomain.Model.dll" (echo   ✓ Vodia.CommonDomain.Model.dll) else (echo   ✗ Vodia.CommonDomain.Model.dll - MISSING!)
copy "Libraries\Vodia.Contracts.Common.dll" "." >nul 2>&1
if exist "Vodia.Contracts.Common.dll" (echo   ✓ Vodia.Contracts.Common.dll) else (echo   ✗ Vodia.Contracts.Common.dll - MISSING!)
copy "Libraries\Vodia.UtilityComponent.dll" "." >nul 2>&1
if exist "Vodia.UtilityComponent.dll" (echo   ✓ Vodia.UtilityComponent.dll) else (echo   ✗ Vodia.UtilityComponent.dll - MISSING!)

REM Essential dependencies
echo.
echo [ESSENTIAL] Core Dependencies:
copy "Libraries\log4net.dll" "." >nul 2>&1
if exist "log4net.dll" (echo   ✓ log4net.dll) else (echo   ✗ log4net.dll - MISSING!)
copy "Libraries\Newtonsoft.Json.dll" "." >nul 2>&1
if exist "Newtonsoft.Json.dll" (echo   ✓ Newtonsoft.Json.dll) else (echo   ✗ Newtonsoft.Json.dll - MISSING!)
copy "Libraries\AutoMapper.dll" "." >nul 2>&1
if exist "AutoMapper.dll" (echo   ✓ AutoMapper.dll) else (echo   ✗ AutoMapper.dll - MISSING!)
copy "Libraries\NHibernate.dll" "." >nul 2>&1
if exist "NHibernate.dll" (echo   ✓ NHibernate.dll) else (echo   ✗ NHibernate.dll - MISSING!)

echo.
echo === Step 3: Creating Phoenix APCI Configuration ===

REM Create a marker file to indicate Phoenix mode is enabled
echo PHOENIX_APCI_ENABLED=true > phoenix_mode.txt
echo SETUP_DATE=%DATE% %TIME% >> phoenix_mode.txt
echo VOCOM_DRIVER=WUDFPuma.dll >> phoenix_mode.txt
echo APCI_CORE=apci.dll,apcidb.dll >> phoenix_mode.txt

echo   ✓ Phoenix APCI configuration created

echo.
echo ===============================================================================
echo                           Setup Complete!
echo ===============================================================================
echo.
echo ✓ Phoenix APCI mode enabled
echo ✓ Essential libraries copied for real hardware communication
echo ✓ Environment configured for Vocom 1 adapter
echo ✓ Application ready for real hardware testing
echo.
echo Critical libraries for real hardware:
echo   • apci.dll + apcidb.dll (Core APCI communication)
echo   • WUDFPuma.dll (Vocom 1 adapter driver)
echo   • PhoenixESW.dll (Phoenix integration)
echo   • Volvo.ApciPlus.dll (Volvo APCI communication)
echo   • Vodia communication protocol libraries
echo.
echo ===============================================================================

echo.
echo === Launching VolvoFlashWR in Phoenix APCI Real Hardware Mode ===
echo.
echo Starting application with Phoenix APCI enabled for real Vocom 1 adapter...
start "" "VolvoFlashWR.Launcher.exe"

echo.
echo *** Application launched in Phoenix APCI Real Hardware Mode! ***
echo.
echo The application will now:
echo   1. Initialize Phoenix APCI libraries
echo   2. Detect connected Vocom 1 adapters
echo   3. Establish real hardware communication
echo   4. Enable ECU flash programming with real devices
echo.
echo Monitor the application logs for connection status and diagnostics.
echo.
pause
