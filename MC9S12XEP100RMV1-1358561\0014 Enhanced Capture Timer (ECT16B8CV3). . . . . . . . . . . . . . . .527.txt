﻿Chapter 14
Enhanced Capture Timer (ECT16B8CV3)

Table 14-1. Revision History

Revision Sections
Revision Date Description of Changes

Number Affected

V03.06 05 Aug 2009 *********/14- update register PACTL bit4 PEDGE PT7 to IC7
549 update register PAFLG bit0 PAIF PT7 to IC7,update bit1 PAOVF PT3 to IC3

*********/14- update register ICSYS bit3 TFMOD PTx to ICx
551 update register PBFLG bit1 PBOVF PT1 to IC1

********4/14- update IC Queue Mode description.
557

*********/14-
562

********.2/14-
573

V03.07 26 Aug 2009 ********/14-536 - Add description, ?a counter overflow when TTOV[7] is set?, to be the
********/14-536 condition of channel 7 override event.
********/14-537 - Phrase the description of OC7M to make it more explicit

V03.08 04 May 2010 ********/14-540 - Add Table 14-11
*********/14- - TCRE description, add Note and Figure 14-17

543

14.1 Introduction
The HCS12 enhanced capture timer module has the features of the HCS12 standard timer module
enhanced by additional features in order to enlarge the field of applications, in particular for automotive
ABS applications.

This design specification describes the standard timer as well as the additional features.

The basic timer consists of a 16-bit, software-programmable counter driven by a prescaler. This timer can
be used for many purposes, including input waveform measurements while simultaneously generating an
output waveform. Pulse widths can vary from microseconds to many seconds.

A full access for the counter registers or the input capture/output compare registers will take place in one
clock cycle. Accessing high byte and low byte separately for all of these registers will not yield the same
result as accessing them in one word.

14.1.1 Features
• 16-bit buffer register for four input capture (IC) channels.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 527



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

• Four 8-bit pulse accumulators with 8-bit buffer registers associated with the four buffered IC
channels. Configurable also as two 16-bit pulse accumulators.

• 16-bit modulus down-counter with 8-bit prescaler.
• Four user-selectable delay counters for input noise immunity increase.

14.1.2 Modes of Operation
• Stop — Timer and modulus counter are off since clocks are stopped.
• Freeze — Timer and modulus counter keep on running, unless the TSFRZ bit in the TSCR1 register

is set to one.
• Wait — Counters keep on running, unless the TSWAI bit in the TSCR1 register is set to one.
• Normal — Timer and modulus counter keep on running, unless the TEN bit in the TSCR1 register

or the MCEN bit in the MCCTL register are cleared.

MC9S12XE-Family Reference Manual  Rev. 1.25

528 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

14.1.3 Block Diagram

Channel 0
Bus Clock Prescaler

Input Capture
IOC0

Output Compare
16-bit Counter

Channel 1
Input Capture

IOC1
Modulus Counter 16-Bit Modulus Counter Output Compare

Interrupt
Channel 2

Timer Overflow Input Capture
IOC2

Interrupt Output Compare

Timer Channel 0 Channel 3
Interrupt Input Capture

IOC3
Output Compare

Registers Channel 4
Input Capture

IOC4
Output Compare

Channel 5
Input Capture

IOC5
Output Compare

Timer Channel 7
Interrupt Channel 6

PA Overflow Input Capture
16-Bit IOC6

Interrupt Pulse Accumulator A Output Compare
PA Input Channel 7
Interrupt

16-Bit Input Capture
PB Overflow IOC7

Interrupt Pulse Accumulator B Output Compare

Figure 14-1. ECT Block Diagram

14.2 External Signal Description
The ECT module has a total of eight external pins.

14.2.1 IOC7 — Input Capture and Output Compare Channel 7
This pin serves as input capture or output compare for channel 7.

14.2.2 IOC6 — Input Capture and Output Compare Channel 6
This pin serves as input capture or output compare for channel 6.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 529



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

14.2.3 IOC5 — Input Capture and Output Compare Channel 5
This pin serves as input capture or output compare for channel 5.

14.2.4 IOC4 — Input Capture and Output Compare Channel 4
This pin serves as input capture or output compare for channel 4.

14.2.5 IOC3 — Input Capture and Output Compare Channel 3
This pin serves as input capture or output compare for channel 3.

14.2.6 IOC2 — Input Capture and Output Compare Channel 2
This pin serves as input capture or output compare for channel 2.

14.2.7 IOC1 — Input Capture and Output Compare Channel 1
This pin serves as input capture or output compare for channel 1.

14.2.8 IOC0 — Input Capture and Output Compare Channel 0
This pin serves as input capture or output compare for channel 0.

NOTE
 For the description of interrupts see Section 14.4.3, “Interrupts”.

14.3 Memory Map and Register Definition
This section provides a detailed description of all memory and registers.

14.3.1 Module Memory Map
The memory map for the ECT module is given below in the Table 14-2. The address listed for each register
is the address offset. The total address for each register is the sum of the base address for the ECT module
and the address offset for each register.

14.3.2 Register Descriptions
This section consists of register descriptions in address order. Each description includes a standard register
diagram with an associated figure number. Details of register bit and field function follow the register
diagrams, in bit order.

MC9S12XE-Family Reference Manual  Rev. 1.25

530 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x0000 R
TIOS IOS7 IOS6 IOS5 IOS4 IOS3 IOS2 IOS1 IOS0

W

0x0001 R 0 0 0 0 0 0 0 0
CFORC W FOC7 FOC6 FOC5 FOC4 FOC3 FOC2 FOC1 FOC0

0x0002 R
OC7M OC7M7 OC7M6 OC7M5 OC7M4 OC7M3 OC7M2 OC7M1 OC7M0

W

0x0003 R
OC7D OC7D7 OC7D6 OC7D5 OC7D4 OC7D3 OC7D2 OC7D1 OC7D0

W

0x0004 R
TCNT (High) TCNT15 TCNT14 TCNT13 TCNT12 TCNT11 TCNT10 TCNT9 TCNT8

W

0x0005 R
TCNT (Low) TCNT7 TCNT6 TCNT5 TCNT4 TCNT3 TCNT2 TCNT1 TCNT0

W

0x0006 R 0 0 0
TSCR1 TEN TSWAI TSFRZ TFFCA PRNT

W

0x0007 R
TTOF TOV7 TOV6 TOV5 TOV4 TOV3 TOV2 TOV1 TOV0

W

0x0008 R
TCTL1 OM7 OL7 OM6 OL6 OM5 OL5 OM4 OL4

W

0x0009 R
TCTL2 OM3 OL3 OM2 OL2 OM1 OL1 OM0 OL0

W

0x000A R
TCTL3 EDG7B EDG7A EDG6B EDG6A EDG5B EDG5A EDG4B EDG4A

W

0x000B R
TCTL4 EDG3B EDG3A EDG2B EDG2A EDG1B EDG1A EDG0B EDG0A

W

0x000C R
TIE C7I C6I C5I C4I C3I C2I C1I C0I

W

= Unimplemented or Reserved

Figure 14-2. ECT Register Summary (Sheet 1 of 5)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 531



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x000D R 0 0 0
TSCR2 TOI TCRE PR2 PR1 PR0

W

0x000E R
TFLG1 C7F C6F C5F C4F C3F C2F C1F C0F

W

0x000F R 0 0 0 0 0 0 0
TFLG2 TOF

W

0x0010 R
TC0 (High) Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8

W

0x0011 R
TC0 (Low) Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

W

0x0012 R
TC1 (High) Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8

W

0x0013 R
TC1 (Low) Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

W

0x0014 R
TC2 (High) Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8

W

0x0015 R
TC2 (Low) Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

W

0x0016 R
TC3 (High) Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8

W

0x0017 R
TC3 (Low) Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

W

0x0018 R
TC4 (High) Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8

W

0x0019 R
TC4 (Low) Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

W

0x001A R
TC5 (High) Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8

W

0x001B R
TC5 (Low) Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

W

= Unimplemented or Reserved

Figure 14-2. ECT Register Summary (Sheet 2 of 5)

MC9S12XE-Family Reference Manual  Rev. 1.25

532 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x001C R
TC6 (High) Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8

W

0x001D R
TC6 (Low) Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

W

0x001E R
TC7 (High) Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8

W

0x001F R
TC7 (Low) Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0

W

0x0020 R 0
PACTL PAEN PAMOD PEDGE CLK1 CLK0 PA0VI PAI

W

0x0021 R 0 0 0 0 0 0
PAFLG PA0VF PAIF

W

0x0022 R
PACN3 PACNT7(15) PACNT6(14) PACNT5(13) PACNT4(12) PACNT3(11) PACNT2(10) PACNT1(9) PACNT0(8)

W

0x0023 R
PACN2 PACNT7 PACNT6 PACNT5 PACNT4 PACNT3 PACNT2 PACNT1 PACNT0

W

0x0024 R
PACN1 PACNT7(15) PACNT6(14) PACNT5(13) PACNT4(12) PACNT3(11) PACNT2(10) PACNT1(9) PACNT0(8)

W

0x0025 R
PACN0 PACNT7 PACNT6 PACNT5 PACNT4 PACNT3 PACNT2 PACNT1 PACNT0

W

0x0026 R 0 0
MCCTL MCZI MODMC RDMCL MCEN MCPR1 MCPR0

W ICLAT FLMC

0x0027 R 0 0 0 POLF3 POLF2 POLF1 POLF0
MCFLG MCZF

W

0x0028 R 0 0 0 0
ICPAR PA3EN PA2EN PA1EN PA0EN

W

0x0029 R
DLYCT DLY7 DLY6 DLY5 DLY4 DLY3 DLY2 DLY1 DLY0

W

0x002A R
ICOVW NOVW7 NOVW6 NOVW5 NOVW4 NOVW3 NOVW2 NOVW1 NOVW0

W

= Unimplemented or Reserved

Figure 14-2. ECT Register Summary (Sheet 3 of 5)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 533



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x002B R
ICSYS SH37 SH26 SH15 SH04 TFMOD PACMX BUFEN LATQ

W

0x002C R
OCPD OCPD7 OCPD6 OCPD5 OCPD4 OCPD3 OCPD2 OCPD1 OCPD0

W

0x002D R
TIMTST Timer Test Register

W

0x002E R
PTPSR PTPS7 PTPS6 PTPS5 PTPS4 PTPS3 PTPS2 PTPS1 PTPS0

W

0x002F R
PTMCPSR PTMPS7 PTMPS6 PTMPS5 PTMPS4 PTMPS3 PTMPS2 PTMPS1 PTMPS0

W

0x0030 R 0 0 0 0 0 0
PBCTL PBEN PBOVI

W

0x0031 R 0 0 0 0 0 0 0
PBFLG PBOVF

W

0x0032 R PA3H7 PA3H6 PA3H5 PA3H4 PA3H3 PA3H2 PA3H1 PA3H0
PA3H W

0x0033 R PA2H7 PA2H6 PA2H5 PA2H4 PA2H3 PA2H2 PA2H1 PA2H0
PA2H W

0x0034 R PA1H7 PA1H6 PA1H5 PA1H4 PA1H3 PA1H2 PA1H1 PA1H0
PA1H W

0x0035 R PA0H7 PA0H6 PA0H5 PA0H4 PA0H3 PA0H2 PA0H1 PA0H0
PA0H W

0x0036 R
MCCNT W MCCNT15 MCCNT14 MCCNT13 MCCNT12 MCCNT11 MCCNT10 MCCNT9 MCCNT8
(High)

0x0037 R
MCCNT W MCCNT7 MCCNT6 MCCNT5 MCCNT4 MCCNT3 MCCNT2 MCCNT1 MCCNT0

(Low)

0x0038 R TC15 TC14 TC13 TC12 TC11 TC10 TC9 TC8
TC0H (High) W

0x0039 R TC7 TC6 TC5 TC4 TC3 TC2 TC1 TC0
TC0H (Low)

= Unimplemented or Reserved

Figure 14-2. ECT Register Summary (Sheet 4 of 5)

MC9S12XE-Family Reference Manual  Rev. 1.25

534 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Register
Bit 7 6 5 4 3 2 1 Bit 0

Name

0x003A R TC15 TC14 TC13 TC12 TC11 TC10 TC9 TC8
TC1H (High) W

0x003B R TC7 TC6 TC5 TC4 TC3 TC2 TC1 TC0
TC1H (Low) W

0x003C R TC15 TC14 TC13 TC12 TC11 TC10 TC9 TC8
TC2H (High) W

0x003D R TC7 TC6 TC5 TC4 TC3 TC2 TC1 TC0
TC2H (Low) W

0x003E R TC15 TC14 TC13 TC12 TC11 TC10 TC9 TC8
TC3H (High) W

0x003F R TC7 TC6 TC5 TC4 TC3 TC2 TC1 TC0
TC3H (Low) W

= Unimplemented or Reserved

Figure 14-2. ECT Register Summary (Sheet 5 of 5)

******** Timer Input Capture/Output Compare Select Register (TIOS)

Module Base + 0x0000

7 6 5 4 3 2 1 0
R

IOS7 IOS6 IOS5 IOS4 IOS3 IOS2 IOS1 IOS0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-3. Timer Input Capture/Output Compare Register (TIOS)

Read or write: Anytime

All bits reset to zero.

Table 14-2. TIOS Field Descriptions

Field Description

7:0 Input Capture or Output Compare Channel Configuration
IOS[7:0] 0 The corresponding channel acts as an input capture.

1 The corresponding channel acts as an output compare.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 535



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

******** Timer Compare Force Register (CFORC)

Module Base + 0x0001

7 6 5 4 3 2 1 0
R 0 0 0 0 0 0 0 0
W FOC7 FOC6 FOC5 FOC4 FOC3 FOC2 FOC1 FOC0

Reset 0 0 0 0 0 0 0 0

Figure 14-4. Timer Compare Force Register (CFORC)

Read or write: Anytime but reads will always return 0x0000 (1 state is transient).

All bits reset to zero.

Table 14-3. CFORC Field Descriptions

Field Description

7:0 Force Output Compare Action for Channel 7:0 — A write to this register with the corresponding data bit(s) set
FOC[7:0] causes the action which is programmed for output compare “x” to occur immediately. The action taken is the

same as if a successful comparison had just taken place with the TCx register except the interrupt flag does not
get set.
Note: A channel 7 event, which can be a counter overflow when TTOV[7] is set or A successful channel 7 output

compare overrides any channel 6:0 compares. If a forced output compare on any channel occurs at the
same time as the successful output compare, then the forced output compare action will take precedence
and the interrupt flag will not get set.

******** Output Compare 7 Mask Register (OC7M)

Module Base + 0x0002

7 6 5 4 3 2 1 0
R

OC7M7 OC7M6 OC7M5 OC7M4 OC7M3 OC7M2 OC7M1 OC7M0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-5. Output Compare 7 Mask Register (OC7M)

Read or write: Anytime

All bits reset to zero.

MC9S12XE-Family Reference Manual  Rev. 1.25

536 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Table 14-4. OC7M Field Descriptions

Field Description

7:0 Output Compare Mask Action for Channel 7:0
OC7M[7:0] A channel 7 event, which can be a counter overflow when TTOV[7] is set or a successful output compare

on channel 7, overrides any channel 6:0 compares. For each OC7M bit that is set,the output compare
action reflects the corresponding OC7D bit.
0 The corresponding OC7Dx bit in the output compare 7 data register will not be transferred to the timer port on

a channel 7 event, even if the corresponding pin is setup for output compare.
1 The corresponding OC7Dx bit in the output compare 7 data register will be transferred to the timer port on a

channel 7 event.
Note: The corresponding channel must also be setup for output compare (IOSx = 1 andOCPDx = 0) for data to

be transferred from the output compare 7 data register to the timer port.

******** Output Compare 7 Data Register (OC7D)

Module Base + 0x0003

7 6 5 4 3 2 1 0
R

OC7D7 OC7D6 OC7D5 OC7D4 OC7D3 OC7D2 OC7D1 OC7D0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-6. Output Compare 7 Data Register (OC7D)

Read or write: Anytime

All bits reset to zero.

Table 14-5. OC7D Field Descriptions

Field Description

7:0 Output Compare 7 Data Bits — A channel 7 event, which can be a counter overflow when TTOV[7] is set or A
OC7D[7:0] channel 7 output compare can cause bits in the output compare 7 data register to transfer to the timer port data

register depending on the output compare 7 mask register.

******** Timer Count Register (TCNT)

Module Base + 0x0004

15 14 13 12 11 10 9 8
R

TCNT15 TCNT14 TCNT13 TCNT12 TCNT11 TCNT10 TCNT9 TCNT8
W

Reset 0 0 0 0 0 0 0 0

Figure 14-7. Timer Count Register High (TCNT)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 537



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Module Base + 0x0005

7 6 5 4 3 2 1 0
R

TCNT7 TCNT6 TCNT5 TCNT4 TCNT3 TCNT2 TCNT1 TCNT0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-8. Timer Count Register Low (TCNT)

Read: Anytime

Write: Writable in special modes.

All bits reset to zero.

Table 14-6. TCNT Field Descriptions

Field Description

15:0 Timer Counter Bits — The 16-bit main timer is an up counter. A read to this register will return the current value
TCNT[15:0] of the counter.    Access to the counter register will take place in one clock cycle.

Note: A separate read/write for high byte and low byte in test mode will give a different result than accessing
them as a word. The period of the first count after a write to the TCNT registers may be a different size
because the write is not synchronized with the prescaler clock.

******** Timer System Control Register 1 (TSCR1)

Module Base + 0x0006

7 6 5 4 3 2 1 0
R 0 0 0

TEN TSWAI TSFRZ TFFCA PRNT
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-9. Timer System Control Register 1 (TSCR1)

Read or write: Anytime except PRNT bit is write once

All bits reset to zero.

Table 14-7. TSCR1 Field Descriptions

Field Description

7 Timer Enable
TEN 0 Disables the main timer, including the counter. Can be used for reducing power consumption.

1 Allows the timer to function normally.
Note: If for any reason the timer is not active, there is no ÷64 clock for the pulse accumulator since the ÷64 is

generated by the timer prescaler.

6 Timer Module Stops While in Wait
TSWAI 0 Allows the timer module to continue running during wait.

1 Disables the timer counter, pulse accumulators and modulus down counter when the MCU is in wait mode.
Timer interrupts cannot be used to get the MCU out of wait.

MC9S12XE-Family Reference Manual  Rev. 1.25

538 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Table 14-7. TSCR1 Field Descriptions (continued)

Field Description

5 Timer and Modulus Counter Stop While in Freeze Mode
TSFRZ 0 Allows the timer and modulus counter to continue running while in freeze mode.

1 Disables the timer and modulus counter whenever the MCU is in freeze mode. This is useful for emulation.
The pulse accumulators do not stop in freeze mode.

4 Timer Fast Flag Clear All
TFFCA 0 Allows the timer flag clearing to function normally.

1 A read from an input capture or a write to the output compare channel registers causes the corresponding
channel flag, CxF, to be cleared in the TFLG1 register. Any access to the TCNT register clears the TOF flag
in the TFLG2 register. Any access to the PACN3 and PACN2 registers clears the PAOVF and PAIF flags in the
PAFLG register. Any access to the PACN1 and PACN0 registers clears the PBOVF flag in the PBFLG register.
Any access to the MCCNT register clears the MCZF flag in the MCFLG register. This has the advantage of
eliminating software overhead in a separate clear sequence. Extra care is required to avoid accidental flag
clearing due to unintended accesses.

Note: The flags cannot be cleared via the normal flag clearing mechanism (writing a one to the flag) when
TFFCA = 1.

3 Precision Timer
PRNT 0 Enables legacy timer. Only bits DLY0 and DLY1 of the DLYCT register are used for the delay selection of the

delay counter. PR0, PR1, and PR2 bits of the TSCR2 register are used for timer counter prescaler selection.
MCPR0 and MCPR1 bits of the MCCTL register are used for modulus down counter prescaler selection.

1 Enables precision timer. All bits in the DLYCT register are used for the delay selection, all bits of the PTPSR
register are used for Precision Timer Prescaler Selection, and all bits of PTMCPSR register are used for the
prescaler Precision Timer Modulus Counter Prescaler selection.

******** Timer Toggle On Overflow Register 1 (TTOV)

Module Base + 0x0007

7 6 5 4 3 2 1 0
R

TOV7 TOV6 TOV5 TOV4 TOV3 TOV2 TOV1 TOV0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-10. Timer Toggle On Overflow Register 1 (TTOV)

Read or write: Anytime

All bits reset to zero.

Table 14-8. TTOV Field Descriptions

Field Description

7:0 Toggle On Overflow Bits — TOV97:0] toggles output compare pin on timer counter overflow. This feature only
TOV[7:0] takes effect when in output compare mode. When set, it takes precedence over forced output compare but not

channel 7 override events.
0 Toggle output compare pin on overflow feature disabled.
1 Toggle output compare pin on overflow feature enabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 539



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

******** Timer Control Register 1/Timer Control Register 2 (TCTL1/TCTL2)

Module Base + 0x0008

7 6 5 4 3 2 1 0
R

OM7 OL7 OM6 OL6 OM5 OL5 OM4 OL4
W

Reset 0 0 0 0 0 0 0 0

Figure 14-11. Timer Control Register 1 (TCTL1)

Module Base + 0x0009

7 6 5 4 3 2 1 0
R

OM3 OL3 OM2 OL2 OM1 OL1 OM0 OL0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-12. Timer Control Register 2 (TCTL2)

Read or write: Anytime

All bits reset to zero.

Table 14-9. TCTL1/TCTL2 Field Descriptions

Field Description

OM[7:0] OMx — Output Mode
7, 5, 3, 1 OLx — Output Level

These eight pairs of control bits are encoded to specify the output action to be taken as a result of a successful
OL[7:0] OCx compare. When either OMx or OLx is one, the pin associated with OCx becomes an output tied to OCx.

6, 4, 2, 0 See Table 14-10.

Table 14-10. Compare Result Output Action

OMx OLx Action

0 0 No output compare
action on the timer output signal

0 1 Toggle OCx output line
1 0 Clear OCx output line to zero
1 1 Set OCx output line to one

NOTE
To enable output action by OMx and OLx bits on timer port, the
corresponding bit in OC7M should be cleared. The settings for these bits can
be seen in Table 14-11

MC9S12XE-Family Reference Manual  Rev. 1.25

540 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Table 14-11. The OC7 and OCx event priority

OC7M7=0 OC7M7=1

OC7Mx=1 OC7Mx=0 OC7Mx=1 OC7Mx=0
TC7=TCx TC7>TCx TC7=TCx TC7>TCx TC7=TCx TC7>TCx TC7=TCx TC7>TCx

IOCx=OC7Dx IOCx=OC7Dx IOCx=OMx/OLx IOCx=OC7Dx IOCx=OC7Dx IOCx=OMx/OLx
IOC7=OM7/O +OMx/OLx IOC7=OM7/OL7 IOC7=OC7D7 +OMx/OLx IOC7=OC7D7

L7 IOC7=OM7/O IOC7=OC7D7
L7

Note: in Table 14-11,the IOS7 and IOSx should be set to 1

IOSx is the register TIOS bit x,

OC7Mx is the register OC7M bit x,

TCx is timer Input Capture/Output Compare register,

IOCx is channel x,

OMx/OLx is the register TCTL1/TCTL2,

OC7Dx is the register OC7D bit x.

IOCx = OC7Dx+ OMx/OLx, means that both OC7 event and OCx event will change channel x value.

14.3.2.9 Timer Control Register 3/Timer Control Register 4 (TCTL3/TCTL4)

Module Base + 0x000A

7 6 5 4 3 2 1 0
R

EDG7B EDG7A EDG6B EDG6A EDG5B EDG5A EDG4B EDG4A
W

Reset 0 0 0 0 0 0 0 0

Figure 14-13. Timer Control Register 3 (TCTL3)

Module Base + 0x000B

7 6 5 4 3 2 1 0
R

EDG3B EDG3A EDG2B EDG2A EDG1B EDG1A EDG0B EDG0A
W

Reset 0 0 0 0 0 0 0 0

Figure 14-14. Timer Control Register 4 (TCTL4)

Read or write: Anytime

All bits reset to zero.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 541



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Table 14-12. TCTL3/TCTL4 Field Descriptions

Field Description

EDG[7:0]B Input Capture Edge Control — These eight pairs of control bits configure the input capture edge detector
7, 5, 3, 1 circuits for each input capture channel. The four pairs of control bits in TCTL4 also configure the input capture

edge control for the four 8-bit pulse accumulators PAC0–PAC3.EDG0B and EDG0A in TCTL4 also determine the
EDG[7:0]A active edge for the 16-bit pulse accumulator PACB. See Table 14-13.
6, 4, 2, 0

Table 14-13. Edge Detector Circuit Configuration

EDGxB EDGxA Configuration

0 0 Capture disabled
0 1 Capture on rising edges only
1 0 Capture on falling edges only
1 1 Capture on any edge (rising or falling)

********* Timer Interrupt Enable Register (TIE)

Module Base + 0x000C

7 6 5 4 3 2 1 0
R

C7I C6I C5I C4I C3I C2I C1I C0I
W

Reset 0 0 0 0 0 0 0 0

Figure 14-15. Timer Interrupt Enable Register (TIE)

Read or write: Anytime

All bits reset to zero.

The bits C7I–C0I correspond bit-for-bit with the flags in the TFLG1 status register.

Table 14-14. TIE Field Descriptions

Field Description

7:0 Input Capture/Output Compare “x” Interrupt Enable
C[7:0]I 0 The corresponding flag is disabled from causing a hardware interrupt.

1 The corresponding flag is enabled to cause an interrupt.

MC9S12XE-Family Reference Manual  Rev. 1.25

542 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

********* Timer System Control Register 2 (TSCR2)

Module Base + 0x000D

7 6 5 4 3 2 1 0
R 0 0 0

TOI TCRE PR2 PR1 PR0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-16. Timer System Control Register 2 (TSCR2)

Read or write: Anytime

All bits reset to zero.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 543



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Table 14-15. TSCR2 Field Descriptions

Field Description

7 Timer Overflow Interrupt Enable
TOI 0 Timer overflow interrupt disabled.

1 Hardware interrupt requested when TOF flag set.

3 Timer Counter Reset Enable — This bit allows the timer counter to be reset by a successful channel 7 output
TCRE compare. This mode of operation is similar to an up-counting modulus counter.

0 Counter reset disabled and counter free runs.
1 Counter reset by a successful output compare on channel 7.

Note: If register TC7 = 0x0000 and TCRE = 1, then the TCNT register will stay at 0x0000 continuously. If register
TC7 = 0xFFFF and TCRE = 1, the TOF flag will never be set when TCNT is reset from 0xFFFF to 0x0000.

Note: TCRE=1 and TC7!=0, the TCNT cycle period will be TC7 x "prescaler counter width" + "1 Bus Clock".
When TCRE is set and TC7 is not equal to 0, TCNT will cycle from 0 to TC7. When TCNT reaches TC7
value, it will last only one bus cycle then reset to 0. for a more detail explanation please refer to Figure 14-
17.

Note: in Figure 14-17,if PR[2:0] is equal to 0, one prescaler counter equal to one bus clock

2:0 Timer Prescaler Select — These three bits specify the division rate of the main Timer prescaler when the PRNT
PR[2:0] bit of register TSCR1 is set to 0. The newly selected prescale factor will not take effect until the next synchronized

edge where all prescale counter stages equal zero. See Table 14-16.

Figure 14-17. The TCNT cycle diagram under TCRE=1 condition
prescaler

1 bus
counter

clock

TC7 0 1 ----- TC7-1 TC7 0

TC7 event TC7 event

Table 14-16. Prescaler Selection

PR2 PR1 PR0 Prescale Factor

0 0 0 1
0 0 1 2
0 1 0 4
0 1 1 8
1 0 0 16
1 0 1 32
1 1 0 64
1 1 1 128

MC9S12XE-Family Reference Manual  Rev. 1.25

544 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

********2 Main Timer Interrupt Flag 1 (TFLG1)

Module Base + 0x000E

7 6 5 4 3 2 1 0
R

C7F C6F C5F C4F C3F C2F C1F C0F
W

Reset 0 0 0 0 0 0 0 0

Figure 14-18. Main Timer Interrupt Flag 1 (TFLG1)

Read: Anytime

Write used in the flag clearing mechanism. Writing a one to the flag clears the flag. Writing a zero will not
affect the current status of the bit.

NOTE
When TFFCA = 1, the flags cannot be cleared via the normal flag clearing
mechanism (writing a one to the flag). Reference Section ********, “Timer
System Control Register 1 (TSCR1)”.

All bits reset to zero.

TFLG1 indicates when interrupt conditions have occurred. The flags can be cleared via the normal flag
clearing mechanism (writing a one to the flag) or via the fast flag clearing mechanism (reference TFFCA
bit in Section ********, “Timer System Control Register 1 (TSCR1)”).

Use of the TFMOD bit in the ICSYS register in conjunction with the use of the ICOVW register allows a
timer interrupt to be generated after capturing two values in the capture and holding registers, instead of
generating an interrupt for every capture.

Table 14-17. TFLG1 Field Descriptions

Field Description

7:0 Input Capture/Output Compare Channel “x” Flag — A CxF flag is set when a corresponding input capture or
C[7:0]F output compare is detected. C0F can also be set by 16-bit Pulse Accumulator B (PACB). C3F–C0F can also be

set by 8-bit pulse accumulators PAC3–PAC0.
If the delay counter is enabled, the CxF flag will not be set until after the delay.

********* Main Timer Interrupt Flag 2 (TFLG2)

Module Base + 0x000F

7 6 5 4 3 2 1 0
R 0 0 0 0 0 0 0

TOF
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-19. Main Timer Interrupt Flag 2 (TFLG2)

Read: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 545



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Write used in the flag clearing mechanism. Writing a one to the flag clears the flag. Writing a zero will not
affect the current status of the bit.

NOTE
When TFFCA = 1, the flag cannot be cleared via the normal flag clearing
mechanism (writing a one to the flag). Reference Section ********, “Timer
System Control Register 1 (TSCR1)”.

All bits reset to zero.

TFLG2 indicates when interrupt conditions have occurred. The flag can be cleared via the normal flag
clearing mechanism (writing a one to the flag) or via the fast flag clearing mechanism (Reference TFFCA
bit in Section ********, “Timer System Control Register 1 (TSCR1)”).

Table 14-18. TFLG2 Field Descriptions

Field Description

7 Timer Overflow Flag — Set when 16-bit free-running timer overflows from 0xFFFF to 0x0000.
TOF

********* Timer Input Capture/Output Compare Registers 0–7

Module Base + 0x0010

15 14 13 12 11 10 9 8
R

Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W

Reset 0 0 0 0 0 0 0 0

Figure 14-20. Timer Input Capture/Output Compare Register 0 High (TC0)

Module Base + 0x0011

7 6 5 4 3 2 1 0
R

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-21. Timer Input Capture/Output Compare Register 0 Low (TC0)

Module Base + 0x0012

15 14 13 12 11 10 9 8
R

Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W

Reset 0 0 0 0 0 0 0 0

Figure 14-22. Timer Input Capture/Output Compare Register 1 High (TC1)

MC9S12XE-Family Reference Manual  Rev. 1.25

546 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Module Base + 0x0013

7 6 5 4 3 2 1 0
R

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-23. Timer Input Capture/Output Compare Register 1 Low (TC1)

Module Base + 0x0014

15 14 13 12 11 10 9 8
R

Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W

Reset 0 0 0 0 0 0 0 0

Figure 14-24. Timer Input Capture/Output Compare Register 2 High (TC2)

Module Base + 0x0015

7 6 5 4 3 2 1 0
R

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-25. Timer Input Capture/Output Compare Register 2 Low (TC2)

Module Base + 0x0016

15 14 13 12 11 10 9 8
R

Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W

Reset 0 0 0 0 0 0 0 0

Figure 14-26. Timer Input Capture/Output Compare Register 3 High (TC3)

Module Base + 0x0017

7 6 5 4 3 2 1 0
R

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-27. Timer Input Capture/Output Compare Register 3 Low (TC3)

Module Base + 0x0018

15 14 13 12 11 10 9 8
R

Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W

Reset 0 0 0 0 0 0 0 0

Figure 14-28. Timer Input Capture/Output Compare Register 4 High (TC4)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 547



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Module Base + 0x0019

7 6 5 4 3 2 1 0
R

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-29. Timer Input Capture/Output Compare Register 4 Low (TC4)

Module Base + 0x001A

15 14 13 12 11 10 9 8
R

Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W

Reset 0 0 0 0 0 0 0 0

Figure 14-30. Timer Input Capture/Output Compare Register 5 High (TC5)

Module Base + 0x001B

7 6 5 4 3 2 1 0
R

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-31. Timer Input Capture/Output Compare Register 5 Low (TC5)

Module Base + 0x001C

15 14 13 12 11 10 9 8
R

Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W

Reset 0 0 0 0 0 0 0 0

Figure 14-32. Timer Input Capture/Output Compare Register 6 High (TC6)

Module Base + 0x001D

7 6 5 4 3 2 1 0
R

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-33. Timer Input Capture/Output Compare Register 6 Low (TC6)

Module Base + 0x001E

15 14 13 12 11 10 9 8
R

Bit 15 Bit 14 Bit 13 Bit 12 Bit 11 Bit 10 Bit 9 Bit 8
W

Reset 0 0 0 0 0 0 0 0

Figure 14-34. Timer Input Capture/Output Compare Register 7 High (TC7)

MC9S12XE-Family Reference Manual  Rev. 1.25

548 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Module Base + 0x001F

7 6 5 4 3 2 1 0
R

Bit 7 Bit 6 Bit 5 Bit 4 Bit 3 Bit 2 Bit 1 Bit 0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-35. Timer Input Capture/Output Compare Register 7 Low (TC7)

Read: Anytime

Write anytime for output compare function. Writes to these registers have no meaning or effect during
input capture.

All bits reset to zero.

Depending on the TIOS bit for the corresponding channel, these registers are used to latch the value of the
free-running counter when a defined transition is sensed by the corresponding input capture edge detector
or to trigger an output action for output compare.

********* 16-Bit Pulse Accumulator A Control Register (PACTL)

Module Base + 0x0020

7 6 5 4 3 2 1 0
R 0

PAEN PAMOD PEDGE CLK1 CLK0 PAOVI PAI
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-36. 16-Bit Pulse Accumulator Control Register (PACTL)

Read: Anytime

Write: Anytime

All bits reset to zero.

Table 14-19. PACTL Field Descriptions

Field Description

6 Pulse Accumulator A System Enable — PAEN is independent from TEN. With timer disabled, the pulse
PAEN accumulator can still function unless pulse accumulator is disabled.

0 16-Bit Pulse Accumulator A system disabled. 8-bit PAC3 and PAC2 can be enabled when their related enable
bits in ICPAR are set. Pulse Accumulator Input Edge Flag (PAIF) function is disabled.

1 16-Bit Pulse Accumulator A system enabled. The two 8-bit pulse accumulators PAC3 and PAC2 are cascaded
to form the PACA 16-bit pulse accumulator. When PACA in enabled, the PACN3 and PACN2 registers contents
are respectively the high and low byte of the PACA. PA3EN and PA2EN control bits in ICPAR have no effect.
Pulse Accumulator Input Edge Flag (PAIF) function is enabled. The PACA shares the input pin with IC7.

5 Pulse Accumulator Mode — This bit is active only when the Pulse Accumulator A is enabled (PAEN = 1).
PAMOD 0 Event counter mode

1 Gated time accumulation mode

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 549



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Table 14-19. PACTL Field Descriptions (continued)

Field Description

4 Pulse Accumulator Edge Control — This bit is active only when the Pulse Accumulator A is enabled
PEDGE (PAEN = 1). Refer to Table 14-20.

For PAMOD bit = 0 (event counter mode).
0 Falling edges on IC7 pin cause the count to be incremented
1 Rising edges on IC7 pin cause the count to be incremented
For PAMOD bit = 1 (gated time accumulation mode).
0 IC7 input pin high enables bus clock divided by 64 to Pulse Accumulator and the trailing falling edge on IC7

sets the PAIF flag.
1 IC7 input pin low enables bus clock divided by 64 to Pulse Accumulator and the trailing rising edge on IC7 sets

the PAIF flag.
If the timer is not active (TEN = 0 in TSCR1), there is no divide-by-64 since the ÷64 clock is generated by the
timer prescaler.

3:2 Clock Select Bits — For the description of PACLK please refer to Figure 14-72.
CLK[1:0] If the pulse accumulator is disabled (PAEN = 0), the prescaler clock from the timer is always used as an input

clock to the timer counter. The change from one selected clock to the other happens immediately after these bits
are written. Refer to Table 14-21.

2 Pulse Accumulator A Overflow Interrupt Enable
PAOVI 0 Interrupt inhibited

1 Interrupt requested if PAOVF is set

0 Pulse Accumulator Input Interrupt Enable
PAI 0 Interrupt inhibited

1 Interrupt requested if PAIF is set

.

Table 14-20. Pin Action

PAMOD PEDGE Pin Action

0 0 Falling edge
0 1 Rising edge
1 0 Divide by 64 clock enabled with pin high level
1 1 Divide by 64 clock enabled with pin low level

Table 14-21. Clock Selection

CLK1 CLK0 Clock Source

0 0 Use timer prescaler clock as timer counter clock
0 1 Use PACLK as input to timer counter clock
1 0 Use PACLK/256 as timer counter clock frequency
1 1 Use PACLK/65536 as timer counter clock frequency

MC9S12XE-Family Reference Manual  Rev. 1.25

550 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

********* Pulse Accumulator A Flag Register (PAFLG)

Module Base + 0x0021

7 6 5 4 3 2 1 0
R 0 0 0 0 0 0

PAOVF PAIF
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-37. Pulse Accumulator A Flag Register (PAFLG)

Read: Anytime

Write used in the flag clearing mechanism. Writing a one to the flag clears the flag. Writing a zero will not
affect the current status of the bit.

NOTE
When TFFCA = 1, the flags cannot be cleared via the normal flag clearing
mechanism (writing a one to the flag). Reference Section ********, “Timer
System Control Register 1 (TSCR1)”.

All bits reset to zero.

PAFLG indicates when interrupt conditions have occurred. The flags can be cleared via the normal flag
clearing mechanism (writing a one to the flag) or via the fast flag clearing mechanism (Reference TFFCA
bit in Section ********, “Timer System Control Register 1 (TSCR1)”).

Table 14-22. PAFLG Field Descriptions

Field Description

1 Pulse Accumulator A Overflow Flag — Set when the 16-bit pulse accumulator A overflows from 0xFFFF to
PAOVF 0x0000, or when 8-bit pulse accumulator 3 (PAC3) overflows from 0x00FF to 0x0000.

When PACMX = 1, PAOVF bit can also be set if 8-bit pulse accumulator 3 (PAC3) reaches 0x00FF followed by
an active edge on IC3.

0 Pulse Accumulator Input edge Flag — Set when the selected edge is detected at the IC7 input pin. In event
PAIF mode the event edge triggers PAIF and in gated time accumulation mode the trailing edge of the gate signal at

the IC7 input pin triggers PAIF.

********7 Pulse Accumulators Count Registers (PACN3 and PACN2)

Module Base + 0x0022

7 6 5 4 3 2 1 0
R

PACNT7(15) PACNT6(14) PACNT5(13) PACNT4(12) PACNT3(11) PACNT2(10) PACNT1(9) PACNT0(8)
W

Reset 0 0 0 0 0 0 0 0

Figure 14-38. Pulse Accumulators Count Register 3 (PACN3)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 551



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Module Base + 0x0023

7 6 5 4 3 2 1 0
R

PACNT7 PACNT6 PACNT5 PACNT4 PACNT3 PACNT2 PACNT1 PACNT0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-39. Pulse Accumulators Count Register 2 (PACN2)

Read: Anytime

Write: Anytime

All bits reset to zero.

The two 8-bit pulse accumulators PAC3 and PAC2 are cascaded to form the PACA 16-bit pulse
accumulator. When PACA in enabled (PAEN = 1 in PACTL), the PACN3 and PACN2 registers contents
are respectively the high and low byte of the PACA.

When PACN3 overflows from 0x00FF to 0x0000, the interrupt flag PAOVF in PAFLG is set.

Full count register access will take place in one clock cycle.

NOTE
A separate read/write for high byte and low byte will give a different result
than accessing them as a word.

When clocking pulse and write to the registers occurs simultaneously, write
takes priority and the register is not incremented.

********8 Pulse Accumulators Count Registers (PACN1 and PACN0)

Module Base + 0x0024

7 6 5 4 3 2 1 0
R

PACNT7(15) PACNT6(14) PACNT5(13) PACNT4(12) PACNT3(11) PACNT2(10) PACNT1(9) PACNT0(8)
W

Reset 0 0 0 0 0 0 0 0

Figure 14-40. Pulse Accumulators Count Register 1 (PACN1)

Module Base + 0x0025

7 6 5 4 3 2 1 0
R

PACNT7 PACNT6 PACNT5 PACNT4 PACNT3 PACNT2 PACNT1 PACNT0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-41. Pulse Accumulators Count Register 0 (PACN0)

Read: Anytime

Write: Anytime

MC9S12XE-Family Reference Manual  Rev. 1.25

552 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

All bits reset to zero.

The two 8-bit pulse accumulators PAC1 and PAC0 are cascaded to form the PACB 16-bit pulse
accumulator. When PACB in enabled, (PBEN = 1 in PBCTL) the PACN1 and PACN0 registers contents
are respectively the high and low byte of the PACB.

When PACN1 overflows from 0x00FF to 0x0000, the interrupt flag PBOVF in PBFLG is set.

Full count register access will take place in one clock cycle.

NOTE
A separate read/write for high byte and low byte will give a different result
than accessing them as a word.

When clocking pulse and write to the registers occurs simultaneously, write
takes priority and the register is not incremented.

********9 16-Bit Modulus Down-Counter Control Register (MCCTL)

Module Base + 0x0026

7 6 5 4 3 2 1 0
R 0 0

MCZI MODMC RDMCL MCEN MCPR1 MCPR0
W ICLAT FLMC

Reset 0 0 0 0 0 0 0 0

Figure 14-42. 16-Bit Modulus Down-Counter Control Register (MCCTL)

Read: Anytime

Write: Anytime

All bits reset to zero.

Table 14-23. MCCTL Field Descriptions

Field Description

7 Modulus Counter Underflow Interrupt Enable
MCZI 0 Modulus counter interrupt is disabled.

1 Modulus counter interrupt is enabled.

6 Modulus Mode Enable
MODMC 0 The modulus counter counts down from the value written to it and will stop at 0x0000.

1 Modulus mode is enabled. When the modulus counter reaches 0x0000, the counter is loaded with the latest
value written to the modulus count register.

Note: For proper operation, the MCEN bit should be cleared before modifying the MODMC bit in order to reset
the modulus counter to 0xFFFF.

5 Read Modulus Down-Counter Load
RDMCL 0 Reads of the modulus count register (MCCNT) will return the present value of the count register.

1 Reads of the modulus count register (MCCNT) will return the contents of the load register.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 553



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Table 14-23. MCCTL Field Descriptions (continued)

Field Description

4 Input Capture Force Latch Action — When input capture latch mode is enabled (LATQ and BUFEN bit in
ICLAT ICSYS are set), a write one to this bit immediately forces the contents of the input capture registers TC0 to TC3

and their corresponding 8-bit pulse accumulators to be latched into the associated holding registers. The pulse
accumulators will be automatically cleared when the latch action occurs.
Writing zero to this bit has no effect. Read of this bit will always return zero.

3 Force Load Register into the Modulus Counter Count Register — This bit is active only when the modulus
FLMC down-counter is enabled (MCEN = 1).

A write one into this bit loads the load register into the modulus counter count register (MCCNT). This also resets
the modulus counter prescaler.
Write zero to this bit has no effect. Read of this bit will return always zero.

2 Modulus Down-Counter Enable
MCEN 0 Modulus counter disabled. The modulus counter (MCCNT) is preset to 0xFFFF. This will prevent an early

interrupt flag when the modulus down-counter is enabled.
1 Modulus counter is enabled.

1:0 Modulus Counter Prescaler Select — These two bits specify the division rate of the modulus counter prescaler
MCPR[1:0] when PRNT of TSCR1 is set to 0. The newly selected prescaler division rate will not be effective until a load of

the load register into the modulus counter count register occurs.

Table 14-24. Modulus Counter Prescaler Select

MCPR1 MCPR0 Prescaler Division

0 0 1
0 1 4
1 0 8
1 1 16

********0 16-Bit Modulus Down-Counter FLAG Register (MCFLG)

Module Base + 0x0027

7 6 5 4 3 2 1 0
R 0 0 0 POLF3 POLF2 POLF1 POLF0

MCZF
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-43. 16-Bit Modulus Down-Counter FLAG Register (MCFLG)

Read: Anytime

Write only used in the flag clearing mechanism for bit 7. Writing a one to bit 7 clears the flag. Writing a
zero will not affect the current status of the bit.

MC9S12XE-Family Reference Manual  Rev. 1.25

554 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

NOTE
When TFFCA = 1, the flag cannot be cleared via the normal flag clearing
mechanism (writing a one to the flag). Reference Section ********, “Timer
System Control Register 1 (TSCR1)”.

All bits reset to zero.

Table 14-25. MCFLG Field Descriptions

Field Description

7 Modulus Counter Underflow Flag — The flag is set when the modulus down-counter reaches 0x0000.
MCZF The flag indicates when interrupt conditions have occurred. The flag can be cleared via the normal flag clearing

mechanism (writing a one to the flag) or via the fast flag clearing mechanism (Reference TFFCA bit in
Section ********, “Timer System Control Register 1 (TSCR1)”).

3:0 First Input Capture Polarity Status — These are read only bits. Writes to these bits have no effect.
POLF[3:0] Each status bit gives the polarity of the first edge which has caused an input capture to occur after capture latch

has been read.
Each POLFx corresponds to a timer PORTx input.
0 The first input capture has been caused by a falling edge.
1 The first input capture has been caused by a rising edge.

********1 ICPAR — Input Control Pulse Accumulators Register (ICPAR)

Module Base + 0x0028

7 6 5 4 3 2 1 0
R 0 0 0 0

PA3EN PA2EN PA1EN PA0EN
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-44. Input Control Pulse Accumulators Register (ICPAR)

Read: Anytime

Write: Anytime.

All bits reset to zero.

The 8-bit pulse accumulators PAC3 and PAC2 can be enabled only if PAEN in PACTL is cleared. If PAEN
is set, PA3EN and PA2EN have no effect.

The 8-bit pulse accumulators PAC1 and PAC0 can be enabled only if PBEN in PBCTL is cleared. If PBEN
is set, PA1EN and PA0EN have no effect.

Table 14-26. ICPAR Field Descriptions

Field Description

3:0 8-Bit Pulse Accumulator ‘x’ Enable
PA[3:0]EN 0 8-Bit Pulse Accumulator is disabled.

1 8-Bit Pulse Accumulator is enabled.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 555



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

********2 Delay Counter Control Register (DLYCT)

Module Base + 0x0029

7 6 5 4 3 2 1 0
R

DLY7 DLY6 DLY5 DLY4 DLY3 DLY2 DLY1 DLY0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-45. Delay Counter Control Register (DLYCT)

Read: Anytime

Write: Anytime

All bits reset to zero.

Table 14-27. DLYCT Field Descriptions

Field Description

7:0 Delay Counter Select — When the PRNT bit of TSCR1 register is set to 0, only bits DLY0, DLY1 are used to
DLY[7:0] calculate the delay.Table 14-28 shows the delay settings in this case.

When the PRNT bit of TSCR1 register is set to 1, all bits are used to set a more precise delay. Table 14-29 shows
the delay settings in this case. After detection of a valid edge on an input capture pin, the delay counter counts
the pre-selected number of [(dly_cnt + 1)*4]bus clock cycles, then it will generate a pulse on its output if the level
of input signal, after the preset delay, is the opposite of the level before the transition.This will avoid reaction to
narrow input pulses.
Delay between two active edges of the input signal period should be longer than the selected counter delay.
Note: It is recommended to not write to this register while the timer is enabled, that is when TEN is set in register

TSCR1.

Table 14-28. Delay Counter Select when PRNT = 0

DLY1 DLY0 Delay

0 0 Disabled
0 1 256 bus clock cycles
1 0 512 bus clock cycles
1 1 1024 bus clock cycles

Table 14-29. Delay Counter Select Examples when PRNT = 1

DLY7 DLY6 DLY5 DLY4 DLY3 DLY2 DLY1 DLY0 Delay

0 0 0 0 0 0 0 0 Disabled (bypassed)
0 0 0 0 0 0 0 1 8 bus clock cycles
0 0 0 0 0 0 1 0 12 bus clock cycles
0 0 0 0 0 0 1 1 16 bus clock cycles
0 0 0 0 0 1 0 0 20 bus clock cycles
0 0 0 0 0 1 0 1 24 bus clock cycles
0 0 0 0 0 1 1 0 28 bus clock cycles

MC9S12XE-Family Reference Manual  Rev. 1.25

556 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Table 14-29. Delay Counter Select Examples when PRNT = 1

DLY7 DLY6 DLY5 DLY4 DLY3 DLY2 DLY1 DLY0 Delay

0 0 0 0 0 1 1 1 32 bus clock cycles
0 0 0 0 1 1 1 1 64 bus clock cycles
0 0 0 1 1 1 1 1 128 bus clock cycles
0 0 1 1 1 1 1 1 256 bus clock cycles
0 1 1 1 1 1 1 1 512 bus clock cycles
1 1 1 1 1 1 1 1 1024 bus clock cycles

********3 Input Control Overwrite Register (ICOVW)

Module Base + 0x002A

7 6 5 4 3 2 1 0
R

NOVW7 NOVW6 NOVW5 NOVW4 NOVW3 NOVW2 NOVW1 NOVW0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-46. Input Control Overwrite Register (ICOVW)

Read: Anytime

Write: Anytime

All bits reset to zero.

Table 14-30. ICOVW Field Descriptions

Field Description

7:0 No Input Capture Overwrite
NOVW[7:0] 0 The contents of the related capture register or holding register can be overwritten when a new input capture

or latch occurs.
1 The related capture register or holding register cannot be written by an event unless they are empty (see

Section ********, “IC Channels”). This will prevent the captured value being overwritten until it is read or
latched in the holding register.

********4 Input Control System Control Register (ICSYS)

Module Base + 0x002B

7 6 5 4 3 2 1 0
R

SH37 SH26 SH15 SH04 TFMOD PACMX BUFEN LATQ
W

Reset 0 0 0 0 0 0 0 0

Figure 14-47. Input Control System Register (ICSYS)

Read: Anytime

Write: Once in normal modes

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 557



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

All bits reset to zero.

Table 14-31. ICSYS Field Descriptions

Field Description

7:4 Share Input action of Input Capture Channels x and y
SHxy 0 Normal operation

1 The channel input ‘x’ causes the same action on the channel ‘y’. The port pin ‘x’ and the corresponding edge
detector is used to be active on the channel ‘y’.

3 Timer Flag Setting Mode — Use of the TFMOD bit in conjunction with the use of the ICOVW register allows a
TFMOD timer interrupt to be generated after capturing two values in the capture and holding registers instead of

generating an interrupt for every capture.
By setting TFMOD in queue mode, when NOVWx bit is set and the corresponding capture and holding registers
are emptied, an input capture event will first update the related input capture register with the main timer
contents. At the next event, the TCx data is transferred to the TCxH register, the TCx is updated and the CxF
interrupt flag is set. In all other input capture cases the interrupt flag is set by a valid external event on ICx.
0 The timer flags C3F–C0F in TFLG1 are set when a valid input capture transition on the corresponding port pin

occurs.
1 If in queue mode (BUFEN = 1 and LATQ = 0), the timer flags C3F–C0F in TFLG1 are set only when a latch

on the corresponding holding register occurs. If the queue mode is not engaged, the timer flags C3F–C0F are
set the same way as for TFMOD = 0.

2 8-Bit Pulse Accumulators Maximum Count
PACMX 0 Normal operation. When the 8-bit pulse accumulator has reached the value 0x00FF, with the next active edge,

it will be incremented to 0x0000.
1 When the 8-bit pulse accumulator has reached the value 0x00FF, it will not be incremented further. The value

0x00FF indicates a count of 255 or more.

1 IC Buffer Enable
BUFFEN 0 Input capture and pulse accumulator holding registers are disabled.

1 Input capture and pulse accumulator holding registers are enabled. The latching mode is defined by LATQ
control bit.

0 Input Control Latch or Queue Mode Enable — The BUFEN control bit should be set in order to enable the IC
LATQ and pulse accumulators holding registers. Otherwise LATQ latching modes are disabled.

Write one into ICLAT bit in MCCTL, when LATQ and BUFEN are set will produce latching of input capture and
pulse accumulators registers into their holding registers.
0 Queue mode of Input Capture is enabled. The main timer value is memorized in the IC register by a valid input

pin transition. With a new occurrence of a capture, the value of the IC register will be transferred to its holding
register and the IC register memorizes the new timer value.

1 Latch mode is enabled. Latching function occurs when modulus down-counter reaches zero or a zero is
written into the count register MCCNT (see Section ********.2, “Buffered IC Channels”). With a latching event
the contents of IC registers and 8-bit pulse accumulators are transferred to their holding registers. 8-bit pulse
accumulators are cleared.

MC9S12XE-Family Reference Manual  Rev. 1.25

558 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

********* Output Compare Pin Disconnect Register (OCPD)
Module Base + 0x002C

7 6 5 4 3 2 1 0
R

OCPD7 OCPD6 OCPD5 OCPD4 OCPD3 OCPD2 OCPD1 OCPD0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-48. Output Compare Pin Disconnect Register (OCPD)

Read: Anytime

Write: Anytime

All bits reset to zero.

Table 14-32. OCPD Field Descriptions

Field Description

7:0 Output Compare Pin Disconnect Bits
OCPD[7:0] 0 Enables the timer channel IO port. Output Compare actions will occur on the channel pin. These bits do not

affect the input capture or pulse accumulator functions.
1 Disables the timer channel IO port. Output Compare actions will not affect on the channel pin; the output

compare flag will still be set on an Output Compare event.

********* Precision Timer Prescaler Select Register (PTPSR)

Module Base + 0x002E

7 6 5 4 3 2 1 0
R

PTPS7 PTPS6 PTPS5 PTPS4 PTPS3 PTPS2 PTPS1 PTPS0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-49. Precision Timer Prescaler Select Register (PTPSR)

Read: Anytime

Write: Anytime

All bits reset to zero.

Table 14-33. PTPSR Field Descriptions

Field Description

7:0 Precision Timer Prescaler Select Bits — These eight bits specify the division rate of the main Timer prescaler.
PTPS[7:0] These are effective only when the PRNT bit of TSCR1 is set to 1. Table 14-34 shows some selection examples

in this case.
The newly selected prescale factor will not take effect until the next synchronized edge where all prescale counter
stages equal zero.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 559



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Table 14-34. Precision Timer Prescaler Selection Examples when PRNT = 1

Prescale
PTPS7 PTPS6 PTPS5 PTPS4 PTPS3 PTPS2 PTPS1 PTPS0

Factor

0 0 0 0 0 0 0 0 1
0 0 0 0 0 0 0 1 2
0 0 0 0 0 0 1 0 3
0 0 0 0 0 0 1 1 4
0 0 0 0 0 1 0 0 5
0 0 0 0 0 1 0 1 6
0 0 0 0 0 1 1 0 7
0 0 0 0 0 1 1 1 8
0 0 0 0 1 1 1 1 16
0 0 0 1 1 1 1 1 32
0 0 1 1 1 1 1 1 64
0 1 1 1 1 1 1 1 128
1 1 1 1 1 1 1 1 256

********* Precision Timer Modulus Counter Prescaler Select Register (PTMCPSR)

Module Base + 0x002F

7 6 5 4 3 2 1 0
R

PTMPS7 PTMPS6 PTMPS5 PTMPS4 PTMPS3 PTMPS2 PTMPS1 PTMPS0
W

Reset 0 0 0 0 0 0 0 0

Figure 14-50. Precision Timer Modulus Counter Prescaler Select Register (PTMCPSR)

Read: Anytime

Write: Anytime

All bits reset to zero.

Table 14-35. PTMCPSR Field Descriptions

Field Description

7:0 Precision Timer Modulus Counter Prescaler Select Bits — These eight bits specify the division rate of the
PTMPS[7:0] modulus counter prescaler. These are effective only when the PRNT bit of TSCR1 is set to 1. Table 14-36 shows

some possible division rates.
The newly selected prescaler division rate will not be effective until a load of the load register into the modulus
counter count register occurs.

MC9S12XE-Family Reference Manual  Rev. 1.25

560 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Table 14-36. Precision Timer Modulus Counter Prescaler Select Examples when PRNT = 1

Prescaler
PTMPS7 PTMPS6 PTMPS5 PTMPS4 PTMPS3 PTMPS2 PTMPS1 PTMPS0 Division

Rate

0 0 0 0 0 0 0 0 1
0 0 0 0 0 0 0 1 2
0 0 0 0 0 0 1 0 3
0 0 0 0 0 0 1 1 4
0 0 0 0 0 1 0 0 5
0 0 0 0 0 1 0 1 6
0 0 0 0 0 1 1 0 7
0 0 0 0 0 1 1 1 8
0 0 0 0 1 1 1 1 16
0 0 0 1 1 1 1 1 32
0 0 1 1 1 1 1 1 64
0 1 1 1 1 1 1 1 128
1 1 1 1 1 1 1 1 256

********* 16-Bit Pulse Accumulator B Control Register (PBCTL)

Module Base + 0x0030

7 6 5 4 3 2 1 0
R 0 0 0 0 0 0

PBEN PBOVI
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-51. 16-Bit Pulse Accumulator B Control Register (PBCTL)

Read: Anytime

Write: Anytime

All bits reset to zero.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 561



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Table 14-37. PBCTL Field Descriptions

Field Description

6 Pulse Accumulator B System Enable — PBEN is independent from TEN. With timer disabled, the pulse
PBEN accumulator can still function unless pulse accumulator is disabled.

0 16-bit pulse accumulator system disabled. 8-bit PAC1 and PAC0 can be enabled when their related enable
bits in ICPAR are set.

1 Pulse accumulator B system enabled. The two 8-bit pulse accumulators PAC1 and PAC0 are cascaded to form
the PACB 16-bit pulse accumulator B. When PACB is enabled, the PACN1 and PACN0 registers contents are
respectively the high and low byte of the PACB.
PA1EN and PA0EN control bits in ICPAR have no effect.
The PACB shares the input pin with IC0.

1 Pulse Accumulator B Overflow Interrupt Enable
PBOVI 0 Interrupt inhibited

1 Interrupt requested if PBOVF is set

********* Pulse Accumulator B Flag Register (PBFLG)

Module Base + 0x0031

7 6 5 4 3 2 1 0
R 0 0 0 0 0 0 0

PBOVF
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-52. Pulse Accumulator B Flag Register (PBFLG)

Read: Anytime

Write used in the flag clearing mechanism. Writing a one to the flag clears the flag. Writing a zero will not
affect the current status of the bit.

NOTE
When TFFCA = 1, the flag cannot be cleared via the normal flag clearing
mechanism (writing a one to the flag). Reference Section ********, “Timer
System Control Register 1 (TSCR1)”.

All bits reset to zero.

PBFLG indicates when interrupt conditions have occurred. The flag can be cleared via the normal flag
clearing mechanism (writing a one to the flag) or via the fast flag clearing mechanism (Reference TFFCA
bit in Section ********, “Timer System Control Register 1 (TSCR1)”).

Table 14-38. PBFLG Field Descriptions

Field Description

1 Pulse Accumulator B Overflow Flag — This bit is set when the 16-bit pulse accumulator B overflows from
PBOVF 0xFFFF to 0x0000, or when 8-bit pulse accumulator 1 (PAC1) overflows from 0x00FF to 0x0000.

When PACMX = 1, PBOVF bit can also be set if 8-bit pulse accumulator 1 (PAC1) reaches 0x00FF and an active
edge follows on IC1.

MC9S12XE-Family Reference Manual  Rev. 1.25

562 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

********0 8-Bit Pulse Accumulators Holding Registers (PA3H–PA0H)

Module Base + 0x0032

7 6 5 4 3 2 1 0
R PA3H7 PA3H6 PA3H5 PA3H4 PA3H3 PA3H2 PA3H1 PA3H0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-53. 8-Bit Pulse Accumulators Holding Register 3 (PA3H)

Module Base + 0x0033

7 6 5 4 3 2 1 0
R PA2H7 PA2H6 PA2H5 PA2H4 PA2H3 PA2H2 PA2H1 PA2H0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-54. 8-Bit Pulse Accumulators Holding Register 2 (PA2H)

Module Base + 0x0034

7 6 5 4 3 2 1 0
R PA1H7 PA1H6 PA1H5 PA1H4 PA1H3 PA1H2 PA1H1 PA1H0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-55. 8-Bit Pulse Accumulators Holding Register 1 (PA1H)

Module Base + 0x0035

7 6 5 4 3 2 1 0
R PA0H7 PA0H6 PA0H5 PA0H4 PA0H3 PA0H2 PA0H1 PA0H0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-56. 8-Bit Pulse Accumulators Holding Register 0 (PA0H)

Read: Anytime.

Write: Has no effect.

All bits reset to zero.

These registers are used to latch the value of the corresponding pulse accumulator when the related bits in
register ICPAR are enabled (see Section 14.4.1.3, “Pulse Accumulators”).

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 563



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

********1 Modulus Down-Counter Count Register (MCCNT)

Module Base + 0x0036

15 14 13 12 11 10 9 8
R

MCCNT15 MCCNT14 MCCNT13 MCCNT12 MCCNT11 MCCNT10 MCCNT9 MCCNT8
W

Reset 1 1 1 1 1 1 1 1

Figure 14-57. Modulus Down-Counter Count Register High (MCCNT)

Module Base + 0x0037

7 6 5 4 3 2 1 0
R

MCCNT7 MCCNT6 MCCNT5 MCCNT4 MCCNT3 MCCNT2 MCCNT1 MCCNT0
W

Reset 1 1 1 1 1 1 1 1

Figure 14-58. Modulus Down-Counter Count Register Low (MCCNT)

Read: Anytime

Write: Anytime.

All bits reset to one.

A full access for the counter register will take place in one clock cycle.

NOTE
A separate read/write for high byte and low byte will give different results
than accessing them as a word.

If the RDMCL bit in MCCTL register is cleared, reads of the MCCNT register will return the present value
of the count register. If the RDMCL bit is set, reads of the MCCNT will return the contents of the load
register.

If a 0x0000 is written into MCCNT when LATQ and BUFEN in ICSYS register are set, the input capture
and pulse accumulator registers will be latched.

With a 0x0000 write to the MCCNT, the modulus counter will stay at zero and does not set the MCZF flag
in MCFLG register.

If the modulus down counter is enabled (MCEN = 1) and modulus mode is enabled (MODMC = 1), a write
to MCCNT will update the load register with the value written to it. The count register will not be updated
with the new value until the next counter underflow.

If modulus mode is not enabled (MODMC = 0), a write to MCCNT will clear the modulus prescaler and
will immediately update the counter register with the value written to it and down-counts to 0x0000 and
stops.

The FLMC bit in MCCTL can be used to immediately update the count register with the new value if an
immediate load is desired.

MC9S12XE-Family Reference Manual  Rev. 1.25

564 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

********2 Timer Input Capture Holding Registers 0–3 (TCxH)

Module Base + 0x0038

15 14 13 12 11 10 9 8
R TC15 TC14 TC13 TC12 TC11 TC10 TC9 TC8
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-59. Timer Input Capture Holding Register 0 High (TC0H)

Module Base + 0x0039

7 6 5 4 3 2 1 0
R TC7 TC6 TC5 TC4 TC3 TC2 TC1 TC0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-60. Timer Input Capture Holding Register 0 Low (TC0H)

Module Base + 0x003A

15 14 13 12 11 10 9 8
R TC15 TC14 TC13 TC12 TC11 TC10 TC9 TC8
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-61. Timer Input Capture Holding Register 1 High (TC1H)

Module Base + 0x003B

7 6 5 4 3 2 1 0
R TC7 TC6 TC5 TC4 TC3 TC2 TC1 TC0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-62. Timer Input Capture Holding Register 1 Low (TC1H)

Module Base + 0x003C

15 14 13 12 11 10 9 8
R TC15 TC14 TC13 TC12 TC11 TC10 TC9 TC8
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-63. Timer Input Capture Holding Register 2 High (TC2H)

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 565



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Module Base + 0x003D

7 6 5 4 3 2 1 0
R TC7 TC6 TC5 TC4 TC3 TC2 TC1 TC0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-64. Timer Input Capture Holding Register 2 Low (TC2H)

Module Base + 0x003E

15 14 13 12 11 10 9 8
R TC15 TC14 TC13 TC12 TC11 TC10 TC9 TC8
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-65. Timer Input Capture Holding Register 3 High (TC3H)

Module Base + 0x003F

7 6 5 4 3 2 1 0
R TC7 TC6 TC5 TC4 TC3 TC2 TC1 TC0
W

Reset 0 0 0 0 0 0 0 0
= Unimplemented or Reserved

Figure 14-66. Timer Input Capture Holding Register 3 Low (TC3H)

Read: Anytime

Write: Has no effect.

All bits reset to zero.

These registers are used to latch the value of the input capture registers TC0–TC3. The corresponding
IOSx bits in TIOS should be cleared (see Section ********, “IC Channels”).

14.4 Functional Description
This section provides a complete functional description of the ECT block, detailing the operation of the
design from the end user perspective in a number of subsections.

MC9S12XE-Family Reference Manual  Rev. 1.25

566 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

16-Bit Free-Running
÷ 1, 2, ..., 128 16 BIT MAIN TIMER 16-Bit Load Register

Main Timer ÷ 1, 4, 8, 16
16-Bit Modulus

Bus Clock Timer Prescaler Bus Clock Modulus Prescaler Down Counter

0 RESET
Comparator

P0 Pin Logic Delay TC0 Capture/Compare Reg.
Counter PAC0

EDG0

TC0H Hold Reg. PA0H Hold Reg.

0 RESET
Comparator

P1 Pin Logic Delay TC1 Capture/Compare Reg.
Counter PAC1

EDG1

TC1H Hold Reg. PA1H Hold Reg.

0 RESET
Comparator

P2 Pin Logic Delay TC2 Capture/Compare Reg.
Counter PAC2

EDG2

TC2H Hold Reg. PA2H Hold Reg.

0 RESET
Comparator

P3 Pin Logic Delay TC3 Capture/Compare Reg.
Counter PAC3

EDG3

TC3H Hold Reg. PA3H Hold Reg.

Comparator
P4 Pin Logic EDG4 TC4 Capture/Compare Reg.

MUX ICLAT, LATQ, BUFEN
EDG0

(Force Latch)
SH04

Comparator
P5 Pin Logic EDG5 TC5 Capture/Compare Reg.

MUX Write 0x0000
EDG1 to Modulus Counter

SH15

Comparator LATQ
P6 Pin Logic (MDC Latch Enable)

EDG6 TC6 Capture/Compare Reg.
MUX

EDG2

SH26

Comparator
P7 Pin Logic EDG7 TC7 Capture/Compare Reg.

MUX
EDG3

SH37

Figure 14-67. Detailed Timer Block Diagram in Latch Mode when PRNT = 0

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 567

LATCH

 Underflow



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

16-Bit Free-Running
÷ 1, 2,3, ..., 256 16 BIT MAIN TIMER 16-Bit Load Register

Main Timer ÷ 1, 2,3, ..., 256
16-Bit Modulus

Bus Clock Timer Prescaler Bus Clock Modulus Prescaler Down Counter

0 RESET
Comparator

P0 Pin Logic Delay TC0 Capture/Compare Reg.
Counter PAC0

EDG0

8, 12, 16, ..., 1024
TC0H Hold Reg. PA0H Hold Reg.

0 RESET
Comparator

P1 Pin Logic Delay TC1 Capture/Compare Reg.
Counter PAC1

EDG1

8, 12, 16, ..., 1024
TC1H Hold Reg. PA1H Hold Reg.

0 RESET
Comparator

P2 Pin Logic Delay TC2 Capture/Compare Reg.
Counter PAC2

EDG2

8, 12, 16, ..., 1024
TC2H Hold Reg. PA2H Hold Reg.

0 RESET
Comparator

P3 Pin Logic Delay TC3 Capture/Compare Reg.
Counter PAC3

EDG3
8, 12, 16, ..., 1024

TC3H Hold Reg. PA3H Hold Reg.

Comparator
P4 Pin Logic EDG4 TC4 Capture/Compare Reg.

MUX ICLAT, LATQ, BUFEN
EDG0

(Force Latch)
SH04

Comparator
P5 Pin Logic EDG5 TC5 Capture/Compare Reg.

MUX Write 0x0000
EDG1 to Modulus Counter

SH15

Comparator LATQ
P6 Pin Logic (MDC Latch Enable)

EDG6 TC6 Capture/Compare Reg.
MUX

EDG2

SH26

Comparator
P7 Pin Logic EDG7 TC7 Capture/Compare Reg.

MUX
EDG3

SH37

Figure 14-68. Detailed Timer Block Diagram in Latch Mode when PRNT = 1

MC9S12XE-Family Reference Manual  Rev. 1.25

568 Freescale Semiconductor

LATCH

 Underflow



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

÷1, 2, ..., 128

Bus Clock Timer 16-Bit Free-Running
Prescaler 16 BITM MaiAnI NTi mTIeMrER 16-Bit Load Register

÷ 1, 4, 8, 16

Bus Clock Modulus 16-Bit Modulus
Prescaler Down Counter

0 RESET
Comparator

P0 Pin Logic Delay TC0 Capture/Compare Reg. PAC0
Counter EDG0

TC0H Hold Reg. PA0H Hold Reg.

0 RESET
Comparator

P1 Pin Logic Delay TC1 Capture/Compare Reg. PAC1
Counter EDG1

TC1H Hold Reg. PA1H Hold Reg.

0 RESET
Comparator

P2 Pin Logic Delay TC2 Capture/Compare Reg. PAC2
Counter EDG2

TC2H Hold Reg. PA2H Hold Reg.

0 RESET
Comparator

P3 Pin Logic Delay TC3 Capture/Compare Reg. PAC3
Counter EDG3

TC3H Hold Reg. PA3H Hold Reg.

Comparator
P4 Pin Logic EDG4 LATQ, BUFEN

TC4 Capture/Compare Reg. (Queue Mode)
MUX

EDG0

SH04
Read TC3H

Comparator Hold Reg.
P5 Pin Logic EDG5 TC5 Capture/Compare Reg.

MUX
EDG1

Read TC2H
SH15 Hold Reg.

Comparator
P6 Pin Logic EDG6 TC6 Capture/Compare Reg.

MUX
EDG2 Read TC1H

Hold Reg.

SH26

Comparator
P7 Pin Logic EDG7 Read TC0H

TC7 Capture/Compare Reg. Hold Reg.
MUX

EDG3
SH37

Figure 14-69. Detailed Timer Block Diagram in Queue Mode when PRNT = 0

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 569

LATCH3
LATCH2

LATCH1
LATCH0



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

÷1, 2, 3, ... 256

Bus Clock Timer 16-Bit Free-Running
Prescaler 16 BITM MaiAnI NTi mTIeMrER ÷ 1, 2, 3, ... 256 16-Bit Load Register

Bus Clock Modulus 16-Bit Modulus
Prescaler Down Counter

0 RESET
Comparator

P0 Pin Logic Delay TC0 Capture/Compare Reg. PAC0
Counter EDG0

8, 12, 16, ... 1024
TC0H Hold Reg. PA0H Hold Reg.

0 RESET
Comparator

P1 Pin Logic Delay TC1 Capture/Compare Reg. PAC1
Counter EDG1

8, 12, 16, ... 1024
TC1H Hold Reg. PA1H Hold Reg.

0 RESET
Comparator

P2 Pin Logic Delay TC2 Capture/Compare Reg. PAC2
Counter EDG2

8, 12, 16, ... 1024
TC2H Hold Reg. PA2H Hold Reg.

0 RESET
Comparator

P3 Pin Logic Delay TC3 Capture/Compare Reg. PAC3
Counter EDG3

8, 12, 16, ... 1024
TC3H Hold Reg. PA3H Hold Reg.

Comparator
P4 Pin Logic EDG4 LATQ, BUFEN

TC4 Capture/Compare Reg. (Queue Mode)
MUX

EDG0

SH04
Read TC3H

Comparator Hold Reg.
P5 Pin Logic EDG5 TC5 Capture/Compare Reg.

MUX
EDG1

Read TC2H
SH15 Hold Reg.

Comparator
P6 Pin Logic EDG6 TC6 Capture/Compare Reg.

MUX
EDG2 Read TC1H

Hold Reg.

SH26

Comparator
P7 Pin Logic EDG7 Read TC0H

TC7 Capture/Compare Reg. Hold Reg.
MUX

EDG3
SH37

Figure 14-70. Detailed Timer Block Diagram in Queue Mode when PRNT = 1

MC9S12XE-Family Reference Manual  Rev. 1.25

570 Freescale Semiconductor

LATCH3
LATCH2

LATCH1
LATCH0



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

Load Holding Register and Reset Pulse Accumulator

8, 12,16, ..., 1024 0

EDG0 8-Bit PAC0 (PACN0)
P0 Edge Detector Delay Counter

PA0H Holding
Register

Interrupt

0
8, 12,16, ..., 1024

EDG1 8-Bit PAC1 (PACN1)
P1 Edge Detector Delay Counter

PA1H Holding
Register

0
8, 12,16, ..., 1024

EDG2 8-Bit PAC2 (PACN2)
P2 Edge Detector Delay Counter

PA2H Holding
Register

Interrupt

0
8, 12,16, ..., 1024

EDG3 8-Bit PAC3 (PACN3)
P3 Edge Detector Delay Counter

PA3H Holding
Register

Figure 14-71. 8-Bit Pulse Accumulators Block Diagram

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 571



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

TIMCLK (Timer Clock)

CLK1
CLK0 4:1 MUX

Prescaled Clock Clock Select
(PCLK) (PAMOD) Edge Detector P7

Interrupt

8-Bit PAC3 8-Bit PAC2
(PACN3) (PACN2) MUX

PACA

Divide by 64 Bus Clock

Interrupt

8-Bit PAC1 8-Bit PAC0
(PACN1) (PACN0) Delay Counter

PACB

Edge Detector P0

Figure 14-72. 16-Bit Pulse Accumulators Block Diagram

16-Bit Main Timer

Edge Delay
Px Detector Counter

TCx Input Set CxF
Capture Register Interrupt

TCxH I.C. BUFEN • LATQ • TFMOD
Holding Register

Figure 14-73. Block Diagram for Port 7 with Output Compare/Pulse Accumulator A

MC9S12XE-Family Reference Manual  Rev. 1.25

572 Freescale Semiconductor

PACLK / 65536

PACLK / 256

PACLK



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

14.4.1 Enhanced Capture Timer Modes of Operation
The enhanced capture timer has 8 input capture, output compare (IC/OC) channels, same as on the HC12
standard timer (timer channels TC0 to TC7). When channels are selected as input capture by selecting the
IOSx bit in TIOS register, they are called input capture (IC) channels.

Four IC channels (channels 7–4) are the same as on the standard timer with one capture register each that
memorizes the timer value captured by an action on the associated input pin.

Four other IC channels (channels 3–0), in addition to the capture register, also have one buffer each called
a holding register. This allows two different timer values to be saved without generating any interrupts.

Four 8-bit pulse accumulators are associated with the four buffered IC channels (channels 3–0). Each pulse
accumulator has a holding register to memorize their value by an action on its external input. Each pair of
pulse accumulators can be used as a 16-bit pulse accumulator.

The 16-bit modulus down-counter can control the transfer of the IC registers and the pulse accumulators
contents to the respective holding registers for a given period, every time the count reaches zero.

The modulus down-counter can also be used as a stand-alone time base with periodic interrupt capability.

******** IC Channels
The IC channels are composed of four standard IC registers and four buffered IC channels.

• An IC register is empty when it has been read or latched into the holding register.
• A holding register is empty when it has been read.

********.1 Non-Buffered IC Channels
The main timer value is memorized in the IC register by a valid input pin transition. If the corresponding
NOVWx bit of the ICOVW register is cleared, with a new occurrence of a capture, the contents of IC
register are overwritten by the new value. If the corresponding NOVWx bit of the ICOVW register is set,
the capture register cannot be written unless it is empty. This will prevent the captured value from being
overwritten until it is read.

********.2 Buffered IC Channels
There are two modes of operations for the buffered IC channels:

1. IC latch mode (LATQ = 1)
The main timer value is memorized in the IC register by a valid input pin transition (see Figure 14-
67 and Figure 14-68).
The value of the buffered IC register is latched to its holding register by the modulus counter for a
given period when the count reaches zero, by a write 0x0000 to the modulus counter or by a write
to ICLAT in the MCCTL register.
If the corresponding NOVWx bit of the ICOVW register is cleared, with a new occurrence of a
capture, the contents of IC register are overwritten by the new value. In case of latching, the
contents of its holding register are overwritten.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 573



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

If the corresponding NOVWx bit of the ICOVW register is set, the capture register or its holding
register cannot be written by an event unless they are empty (see Section ********, “IC Channels”).
This will prevent the captured value from being overwritten until it is read or latched in the holding
register.

2. IC Queue Mode (LATQ = 0)
The main timer value is memorized in the IC register by a valid input pin transition (see Figure 14-
69 and Figure 14-70).
If the corresponding NOVWx bit of the ICOVW register is cleared, with a new occurrence of a
capture, the value of the IC register will be transferred to its holding register and the IC register
memorizes the new timer value.
If the corresponding NOVWx bit of the ICOVW register is set, the capture register or its holding
register cannot be written by an event unless they are empty (see Section ********, “IC Channels”).
if the TFMOD bit of the ICSYS register is set,the timer flags C3F--C0F in TFLG register are set
only when a latch on the corresponding holding register occurs,after C3F--C0F are set,user should
clear flag C3F--C0F,then read TCx and TCxH to make TCx and TCxH be empty.
In queue mode, reads of the holding register will latch the corresponding pulse accumulator value
to its holding register.

********.3 Delayed IC Channels
There are four delay counters in this module associated with IC channels 0–3. The use of this feature is
explained in the diagram and notes below.

BUS CLOCK

DLY_CNT 0 1 2 3 253 254 255 256

INPUT ON Rejected
CH0–3 255 Cycles

INPUT ON Rejected
CH0–3 255.5 Cycles

INPUT ON
CH0–3 255.5 Cycles Accepted

INPUT ON
CH0–3 Accepted

256 Cycles

Figure 14-74. Channel Input Validity with Delay Counter Feature

In Figure 14-74 a delay counter value of 256 bus cycles is considered.
1. Input pulses with a duration of (DLY_CNT – 1) cycles or shorter are rejected.
2. Input pulses with a duration between (DLY_CNT – 1) and DLY_CNT cycles may be rejected or

accepted, depending on their relative alignment with the sample points.

MC9S12XE-Family Reference Manual  Rev. 1.25

574 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

3. Input pulses with a duration between (DLY_CNT – 1) and DLY_CNT cycles may be rejected or
accepted, depending on their relative alignment with the sample points.

4. Input pulses with a duration of DLY_CNT or longer are accepted.

14.4.1.2 OC Channel Initialization
An internal compare channel whose output drives OCx may be programmed before the timer drives the
output compare state (OCx). The required output of the compare logic can be disconnected from the pin,
leaving it driven by the GP IO port, by setting the appropriate OCPDx bit before enabling the output
compare channel (by default the OCPD bits are cleared which would enable the output compare logic to
drive the pin as soon as the timer output compare channel is enabled). The desired initial state can then be
configured in the internal output compare logic by forcing a compare action with the logic disconnected
from the IO (by writing a one to CFORCx bit with TIOSx, OCPDx and TEN bits set to one). Clearing the
output compare disconnect bit (OCPDx) will then allow the internal compare logic to drive the
programmed state to OCx. This allows a glitch free switching between general purpose I/O and timer
output functionality.

14.4.1.3 Pulse Accumulators
There are four 8-bit pulse accumulators with four 8-bit holding registers associated with the four IC
buffered channels 3–0. A pulse accumulator counts the number of active edges at the input of its channel.

The minimum pulse width for the PAI input is greater than two bus clocks.The maximum input frequency
on the pulse accumulator channel is one half the bus frequency or Eclk.

The user can prevent the 8-bit pulse accumulators from counting further than 0x00FF by utilizing the
PACMX control bit in the ICSYS register. In this case, a value of 0x00FF means that 255 counts or more
have occurred.

Each pair of pulse accumulators can be used as a 16-bit pulse accumulator (see Figure 14-72).

Pulse accumulator B operates only as an event counter, it does not feature gated time accumulation mode.
The edge control for pulse accumulator B as a 16-bit pulse accumulator is defined by TCTL4[1:0].

To operate the 16-bit pulse accumulators A and B (PACA and PACB) independently of input capture or
output compare 7 and 0 respectively, the user must set the corresponding bits: IOSx = 1, OMx = 0, and
OLx = 0. OC7M7 or OC7M0 in the OC7M register must also be cleared.

There are two modes of operation for the pulse accumulators:
• Pulse accumulator latch mode

The value of the pulse accumulator is transferred to its holding register when the modulus down-
counter reaches zero, a write 0x0000 to the modulus counter or when the force latch control bit
ICLAT is written.
At the same time the pulse accumulator is cleared.

• Pulse accumulator queue mode
When queue mode is enabled, reads of an input capture holding register will transfer the contents
of the associated pulse accumulator to its holding register.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 575



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

At the same time the pulse accumulator is cleared.

14.4.1.4 Modulus Down-Counter
The modulus down-counter can be used as a time base to generate a periodic interrupt. It can also be used
to latch the values of the IC registers and the pulse accumulators to their holding registers.

The action of latching can be programmed to be periodic or only once.

14.4.1.5 Precision Timer
By enabling the PRNT bit of the TSCR1 register, the performance of the timer can be enhanced. In this
case, it is possible to set additional prescaler settings for the main timer counter and modulus down counter
and enhance delay counter settings compared to the settings in the present ECT timer.

14.4.1.6 Flag Clearing Mechanisms
The flags in the ECT can be cleared one of two ways:

1. Normal flag clearing mechanism (TFFCA = 0)
Any of the ECT flags can be cleared by writing a one to the flag.

2. Fast flag clearing mechanism (TFFCA = 1)
With the timer fast flag clear all (TFFCA) enabled, the ECT flags can only be cleared by accessing
the various registers associated with the ECT modes of operation as described below. The flags
cannot be cleared via the normal flag clearing mechanism. This fast flag clearing mechanism has
the advantage of eliminating the software overhead required by a separate clear sequence. Extra
care must be taken to avoid accidental flag clearing due to unintended accesses.
— Input capture

A read from an input capture channel register causes the corresponding channel flag, CxF, to
be cleared in the TFLG1 register.

— Output compare
A write to the output compare channel register causes the corresponding channel flag, CxF, to
be cleared in the TFLG1 register.

— Timer counter
Any access to the TCNT register clears the TOF flag in the TFLG2 register.

— Pulse accumulator A
Any access to the PACN3 and PACN2 registers clears the PAOVF and PAIF flags in the
PAFLG register.

— Pulse accumulator B
Any access to the PACN1 and PACN0 registers clears the PBOVF flag in the PBFLG register.

— Modulus down counter
Any access to the MCCNT register clears the MCZF flag in the MCFLG register.

MC9S12XE-Family Reference Manual  Rev. 1.25

576 Freescale Semiconductor



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

14.4.2 Reset
The reset state of each individual bit is listed within the register description section (Section 14.3,
“Memory Map and Register Definition”) which details the registers and their bit-fields.

14.4.3 Interrupts
This section describes interrupts originated by the ECT block. The MCU must service the interrupt
requests. Table 14-39 lists the interrupts generated by the ECT to communicate with the MCU.

Table 14-39. ECT Interrupts

Interrupt Source Description

Timer channel 7–0 Active high timer channel interrupts 7–0
Modulus counter underflow Active high modulus counter interrupt
Pulse accumulator B overflow Active high pulse accumulator B interrupt
Pulse accumulator A input Active high pulse accumulator A input interrupt
Pulse accumulator A overflow Pulse accumulator overflow interrupt
Timer overflow Timer 0verflow interrupt

The ECT only originates interrupt requests. The following is a description of how the module makes a
request and how the MCU should acknowledge that request. The interrupt vector offset and interrupt
number are chip dependent.

******** Channel [7:0] Interrupt
This active high output will be asserted by the module to request a timer channel 7–0 interrupt to be
serviced by the system controller.

******** Modulus Counter Interrupt
This active high output will be asserted by the module to request a modulus counter underflow interrupt to
be serviced by the system controller.

******** Pulse Accumulator B Overflow Interrupt
This active high output will be asserted by the module to request a timer pulse accumulator B overflow
interrupt to be serviced by the system controller.

******** Pulse Accumulator A Input Interrupt
This active high output will be asserted by the module to request a timer pulse accumulator A input
interrupt to be serviced by the system controller.

MC9S12XE-Family Reference Manual  Rev. 1.25

Freescale Semiconductor 577



Chapter 14 Enhanced Capture Timer (ECT16B8CV3)

******** Pulse Accumulator A Overflow Interrupt
This active high output will be asserted by the module to request a timer pulse accumulator A overflow
interrupt to be serviced by the system controller.

14.4.3.6 Timer Overflow Interrupt
This active high output will be asserted by the module to request a timer overflow interrupt to be serviced
by the system controller.

MC9S12XE-Family Reference Manual  Rev. 1.25

578 Freescale Semiconductor