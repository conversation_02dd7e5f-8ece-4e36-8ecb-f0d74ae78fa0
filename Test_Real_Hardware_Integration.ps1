# Test Real Hardware Integration Script
# This script tests the real hardware integration capabilities

Write-Host "=== Testing Real Hardware Integration ===" -ForegroundColor Green

# Test 1: Verify all critical libraries are present
Write-Host "`n1. Testing Library Availability..." -ForegroundColor Cyan

$CriticalLibraries = @(
    "WUDFPuma.dll",
    "apci.dll", 
    "apcidb.dll",
    "Volvo.ApciPlus.dll",
    "Volvo.ApciPlusData.dll",
    "PhoenixESW.dll",
    "PhoenixGeneral.dll",
    "Rpci.dll",
    "Pc2.dll"
)

$LibrariesPath = ".\Libraries"
$AllPresent = $true

foreach ($lib in $CriticalLibraries) {
    $path = Join-Path $LibrariesPath $lib
    if (Test-Path $path) {
        Write-Host "   + $lib" -ForegroundColor Green
    } else {
        Write-Host "   X $lib" -ForegroundColor Red
        $AllPresent = $false
    }
}

if ($AllPresent) {
    Write-Host "   All critical libraries present!" -ForegroundColor Green
} else {
    Write-Host "   Some critical libraries missing!" -ForegroundColor Red
}

# Test 2: Check Vocom driver installation
Write-Host "`n2. Testing Vocom Driver Installation..." -ForegroundColor Cyan

$VocomDriverPaths = @(
    "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll",
    ".\Drivers\Vocom\WUDFPuma.dll",
    ".\Libraries\WUDFPuma.dll"
)

$DriverFound = $false
foreach ($path in $VocomDriverPaths) {
    if (Test-Path $path) {
        Write-Host "   + Vocom driver found at: $path" -ForegroundColor Green
        $DriverFound = $true
        break
    }
}

if (-not $DriverFound) {
    Write-Host "   X Vocom driver not found in any location" -ForegroundColor Red
}

# Test 3: Check Phoenix Diag integration
Write-Host "`n3. Testing Phoenix Diag Integration..." -ForegroundColor Cyan

$PhoenixPath = "C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021"
if (Test-Path $PhoenixPath) {
    Write-Host "   + Phoenix Diag installation found" -ForegroundColor Green
    
    $PhoenixLibs = @("apci.dll", "PhoenixESW.dll", "PhoenixGeneral.dll")
    foreach ($lib in $PhoenixLibs) {
        $phoenixLibPath = Join-Path $PhoenixPath $lib
        $localLibPath = Join-Path $LibrariesPath $lib
        if ((Test-Path $phoenixLibPath) -and (Test-Path $localLibPath)) {
            Write-Host "   + $lib integrated successfully" -ForegroundColor Green
        } else {
            Write-Host "   ! $lib integration incomplete" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "   ! Phoenix Diag not installed (optional)" -ForegroundColor Yellow
}

# Test 4: Test application configuration
Write-Host "`n4. Testing Application Configuration..." -ForegroundColor Cyan

$ConfigFiles = @(
    "VolvoFlashWR.UI.exe.config",
    "VolvoFlashWR.Launcher.exe.config"
)

foreach ($config in $ConfigFiles) {
    if (Test-Path $config) {
        Write-Host "   + $config present" -ForegroundColor Green
    } else {
        Write-Host "   X $config missing" -ForegroundColor Red
    }
}

# Test 5: Check executable files
Write-Host "`n5. Testing Application Executables..." -ForegroundColor Cyan

$Executables = @(
    "VolvoFlashWR.Launcher.exe",
    "VolvoFlashWR.UI.exe"
)

foreach ($exe in $Executables) {
    if (Test-Path $exe) {
        Write-Host "   + $exe present" -ForegroundColor Green
    } else {
        Write-Host "   X $exe missing" -ForegroundColor Red
    }
}

# Test 6: Check batch scripts
Write-Host "`n6. Testing Batch Scripts..." -ForegroundColor Cyan

$BatchFiles = @(
    "Run_Real_Hardware_Mode.bat",
    "Verify_Libraries.bat",
    "run_normal_mode.bat"
)

foreach ($batch in $BatchFiles) {
    if (Test-Path $batch) {
        Write-Host "   + $batch present" -ForegroundColor Green
    } else {
        Write-Host "   X $batch missing" -ForegroundColor Red
    }
}

# Summary
Write-Host "`n=== Integration Test Summary ===" -ForegroundColor Yellow

$TotalLibraries = (Get-ChildItem $LibrariesPath -Filter "*.dll" | Measure-Object).Count
Write-Host "Total libraries available: $TotalLibraries" -ForegroundColor Cyan

if ($AllPresent -and $DriverFound) {
    Write-Host "`n✓ READY FOR REAL HARDWARE COMMUNICATION!" -ForegroundColor Green
    Write-Host "The application is fully configured for Vocom hardware." -ForegroundColor Green
    Write-Host "`nNext steps:" -ForegroundColor Yellow
    Write-Host "1. Connect your Vocom 1 adapter" -ForegroundColor White
    Write-Host "2. Run 'Run_Real_Hardware_Mode.bat'" -ForegroundColor White
    Write-Host "3. Test ECU communication" -ForegroundColor White
} else {
    Write-Host "`n⚠ CONFIGURATION INCOMPLETE" -ForegroundColor Yellow
    Write-Host "Some components are missing. The application may fall back to dummy mode." -ForegroundColor Yellow
    Write-Host "`nTo fix:" -ForegroundColor Yellow
    Write-Host "1. Run 'Configure_Real_Hardware_Libraries.ps1'" -ForegroundColor White
    Write-Host "2. Install Vocom driver if needed" -ForegroundColor White
    Write-Host "3. Re-run this test" -ForegroundColor White
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
